/**
 * @return {Object}
 * 
 * @properties={typeid:24,uuid:"6C631EA0-13B3-4FBF-A219-799FD34161B9"}
 */
function ws_read() {
	
	scopes.globals.avBase_AccountingAPI = true;
	var sVersion = "";
	var sMethod = "";
	var sPathParams = new Array();
	var sQueryParams = new Array();
	var sToken = plugins.rest_ws.getRequest().getHeader('Authorization');

	for (var i = 0; i < arguments.length; i++) {
		if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
			switch (i) {
			case 0:
				sVersion = arguments[i];
				break;
			case 1:
				sMethod = arguments[i];
				break;
			case 2:
			default:
				sPathParams.push(arguments[i]);
				break;
			}
		} else {
			sQueryParams = arguments[i];
			
			if (sQueryParams.integration_type) {
			    scopes.globals.avBase_WorkatoIntegrationType = sQueryParams.integration_type[0];
			}
		}
	}
	
	var aParams = new Array();
	var oParams = new Object();
	oParams.version = new Array(sVersion);
	oParams.method = new Array(sMethod);
	if(sPathParams[0]) {
		oParams.id = new Array(sPathParams[0]);		
	}
	oParams.pathParams = new Array(sPathParams);
	oParams.queryParams = new Array(sQueryParams);
	if(sToken) {
		oParams.token = new Array(sToken.replace("Bearer ",""));	
	}
	oParams.requestType = "get";
	aParams.push(oParams);

	/** @type {JSON} */
	var parametersJSON = globals.parseArgumentsToJSON(aParams);
	return globals.requestProcessor(parametersJSON, false);
}

/**
 * @param {Object} requestBody
 * @return {Object}
 * @properties={typeid:24,uuid:"2E88DC36-5DD9-4752-8335-0871AB9574DB"}
 */
function ws_create(requestBody) {
	
	scopes.globals.avBase_AccountingAPI = true;
	var sVersion = "";
	var sMethod = "";
	var sQueryParams = [];
	var aPayload;
	var sToken = plugins.rest_ws.getRequest().getHeader('Authorization');

	// We are ignoring the first element as it seems to be the contents of the body.
	for (var i = 0; i < arguments.length; i++) {
		if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
			if (i == 1) {
				sVersion = arguments[i];
			}
			else if (i == 2) {
				sMethod = arguments[i];
			}
			else if (i >= 3) {
				sQueryParams.push(arguments[i]);
			}
		}
		else {
			if (i == 0) { //Response Body
				aPayload = arguments[i];
			}
			else if (i >= 3) {
                sQueryParams.push(arguments[i]);
            }
		}
	}
	
	//Integration Type is the last argument.
	var sLastQueryParameter = arguments[arguments.length - 1];
	if (sLastQueryParameter.integration_type) {
	    scopes.globals.avBase_WorkatoIntegrationType = sLastQueryParameter.integration_type[0];
	}
	
	var aParams = new Array();
	var oParams = new Object();
	oParams.version = new Array(sVersion);
	oParams.method = new Array(sMethod);
	oParams.payload = new Array(aPayload);
	if(sToken) {
		oParams.token = new Array(sToken.replace("Bearer ",""));	
	}
	if(sQueryParams[0]) {
		oParams.id = new Array(sQueryParams[0]);		
	}
	oParams.queryParams = new Array(sQueryParams);
	oParams.requestType = "post";
	aParams.push(oParams);
	
	/** @type {JSON} */
	var parametersJSON = globals.parseArgumentsToJSON(aParams);
	return globals.requestProcessor(parametersJSON, false);
}

/**
 * @param {Object} requestBody
 * @return {Object}
 *
 * @properties={typeid:24,uuid:"4DDA1AE0-983E-449A-B066-4504ECEF1C88"}
 */
function ws_update(requestBody) {
	
	scopes.globals.avBase_AccountingAPI = true;
	var sVersion = "";
	var sMethod = "";
	var sPathParams = new Array();
	var sQueryParams = new Array();
	var aPayload;
	var sToken = plugins.rest_ws.getRequest().getHeader('Authorization');

	// We are ignoring the first element as it seems to be the contents of the body.
	for (var i = 0; i < arguments.length; i++) {
		if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
			if (i == 1) {
				sVersion = arguments[i];
			}
			else if (i == 2) {
				sMethod = arguments[i];
			}
			else if (i >= 3) {
				sPathParams.push(arguments[i]);
			}
		}
		else {
			if (i == 0) { //Response Body
				aPayload = arguments[i];
			}
		}
	}
	
	   //Integration Type is the last argument.
    var sLastQueryParameter = arguments[arguments.length - 1];
    if (sLastQueryParameter.integration_type) {
        scopes.globals.avBase_WorkatoIntegrationType = sLastQueryParameter.integration_type[0];
    }
    
	var aParams = new Array();
	var oParams = new Object();
	oParams.version = new Array(sVersion);
	oParams.method = new Array(sMethod);
	oParams.payload = new Array(aPayload);
	
	if(sPathParams[0]) {
		oParams.id = new Array(sPathParams[0]);		
	}
	oParams.pathParams = new Array(sPathParams);
	oParams.queryParams = new Array(sQueryParams);
	
	if(sToken) {
		oParams.token = new Array(sToken.replace("Bearer ",""));	
	}
	oParams.requestType = "put";
	aParams.push(oParams);
	
	/** @type {JSON} */
	var parametersJSON = globals.parseArgumentsToJSON(aParams);
	return globals.requestProcessor(parametersJSON, false);
}

/**
* @return {Object}
 *
 * @properties={typeid:24,uuid:"C91CCEDA-8E9D-4AD2-A35D-E5790E1B9ECA"}
 */
function ws_delete() {
    scopes.globals.avBase_AccountingAPI = true;
    var sVersion = "";
    var sMethod = "";
    var sPathParams = new Array();
    var sQueryParams = new Array();
    var sToken = plugins.rest_ws.getRequest().getHeader('Authorization');

    for (var i = 0; i < arguments.length; i++) {
        if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
            switch (i) {
            case 0:
                sVersion = arguments[i];
                break;
            case 1:
                sMethod = arguments[i];
                break;
            case 2:
            default:
                sPathParams.push(arguments[i]);
                break;
            }
        } else {
            sQueryParams = arguments[i];
            
            if (sQueryParams.integration_type) {
                scopes.globals.avBase_WorkatoIntegrationType = sQueryParams.integration_type[0];
            }
        }
    }
    
    var aParams = new Array();
    var oParams = new Object();
    oParams.version = new Array(sVersion);
    oParams.method = new Array(sMethod);
    if(sPathParams[0]) {
        oParams.id = new Array(sPathParams[0]);     
    }
    oParams.pathParams = new Array(sPathParams);
    oParams.queryParams = new Array(sQueryParams);
    if(sToken) {
        oParams.token = new Array(sToken.replace("Bearer ",""));    
    }
    oParams.requestType = "delete";
    aParams.push(oParams);

    /** @type {JSON} */
    var parametersJSON = globals.parseArgumentsToJSON(aParams);
    return globals.requestProcessor(parametersJSON, false);
}