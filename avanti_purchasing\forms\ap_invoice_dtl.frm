customProperties:"useCssPosition:true",
dataSource:"db:/avanti/ap_invoice",
extendsID:"34F1998D-D56D-4CCB-8385-4C6F98E9790D",
items:[
{
cssPosition:"138,-1,-1,340,125,22",
formIndex:17,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"340",
right:"-1",
top:"138",
width:"125"
},
enabled:true,
formIndex:17,
labelFor:"created_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.transactionDate",
visible:true
},
name:"created_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"05B67362-9172-46E8-B60D-F8612957E5A5"
},
{
cssPosition:"61,-1,-1,470,180,22",
enabled:false,
formIndex:11,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"61",
width:"180"
},
dataProviderID:"curr_id",
editable:false,
enabled:false,
formIndex:11,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:6,
valuelistID:"9E25AAB4-5C24-4FBA-B0E4-FA1523FA6721",
visible:true
},
name:"curr_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"0B41685C-6285-4563-B7C7-A059A13DD65E"
},
{
cssPosition:"138,-1,-1,470,180,22",
formIndex:18,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"138",
width:"180"
},
dataProviderID:"created_date",
enabled:true,
formIndex:18,
onDataChangeMethodID:"F6E05625-E540-4DDC-85E7-DC48C4631629",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:9,
visible:true
},
name:"created_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"0FB6072F-A2D1-428F-8BE2-6FECCBC4113C"
},
{
cssPosition:"61,-1,-1,956,24,24",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"956",
right:"-1",
top:"61",
width:"24"
},
enabled:true,
formIndex:2,
styleClass:"label_bts text-center",
tabSeq:12,
text:"%%globals.icon_warning%%",
toolTipText:"i18n:avanti.lbl.unmatched_msg",
visible:true
},
name:"btn_outsideTolerance",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"156DC91C-C4A7-4F66-9BCE-1C913EF52DFD"
},
{
cssPosition:"36,-1,-1,670,125,22",
formIndex:19,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"670",
right:"-1",
top:"36",
width:"125"
},
enabled:true,
formIndex:19,
labelFor:"ap_invoice_doc_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentNumber",
visible:true
},
name:"ap_invoice_doc_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1E61DD01-01D9-4FBE-9E8B-C9EF8C9C8197"
},
{
cssPosition:"111,-1,-1,470,180,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"111",
width:"180"
},
dataProviderID:"ap_invoice_due_date",
enabled:true,
formIndex:9,
onDataChangeMethodID:"C1E2A15E-4998-4091-989F-D6E8B09559ED",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:8,
visible:true
},
name:"ap_invoice_due_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"4B61CDC7-6742-4002-A900-B3082AA44A23"
},
{
cssPosition:"86,-1,-1,140,180,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"86",
width:"180"
},
dataProviderID:"ap_invoice_date",
enabled:true,
formIndex:9,
onDataChangeMethodID:"C1E2A15E-4998-4091-989F-D6E8B09559ED",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:3,
visible:true
},
name:"ap_invoice_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"4CAAE59B-E15E-4709-8F78-6C4B84C08385"
},
{
cssPosition:"111,-1,-1,10,125,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"111",
width:"125"
},
enabled:true,
formIndex:3,
labelFor:"ap_invoice_amount",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceAmount",
visible:true
},
name:"ap_invoice_amount_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5964FD28-1E5A-485B-9B8E-535A5DEEC40C"
},
{
cssPosition:"61,-1,-1,800,151,22",
enabled:false,
formIndex:4,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"61",
width:"151"
},
dataProviderID:"ap_invoice_status",
editable:false,
enabled:false,
formIndex:4,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:10,
toolTipText:"i18n:avanti.lbl.unmatched_msg",
valuelistID:"56E87D42-CC85-42AF-91EC-7480FC004B86",
visible:true
},
name:"status_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6215A386-5D49-4D6B-8990-ED2DE17D027E"
},
{
cssPosition:"86,-1,-1,340,125,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"340",
right:"-1",
top:"86",
width:"125"
},
enabled:true,
formIndex:10,
labelFor:"terms_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.terms_id",
visible:true
},
name:"payment_terms_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"712142BF-4AF0-40EE-992A-D0D178B529E8"
},
{
cssPosition:"162,-1,-1,800,151,22",
enabled:false,
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"162",
width:"151"
},
enabled:false,
formIndex:1,
onActionMethodID:"56817800-FA4F-4F03-BCA2-C2161BBA72D6",
styleClass:"btn btn-default button_bts",
tabSeq:14,
text:"i18n:avanti.lbl.authorize",
visible:false
},
name:"btn_authorize",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"71DCC5F7-7169-465D-A9BD-EBEF78B599B7",
visible:false
},
{
cssPosition:"136,-1,-1,10,125,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"136",
width:"125"
},
enabled:true,
formIndex:12,
labelFor:"amount_left",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.varianceMoney",
visible:true
},
name:"amount_left_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7430343B-C235-43CF-886A-FE456ED44D26"
},
{
cssPosition:"2,-1,-1,800,151,22",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"2",
width:"151"
},
dataProviderID:"ap_invoice_to_ap_invoice_register.apinv_reg_number",
editable:false,
enabled:true,
formIndex:22,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"apinv_reg_number",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"74396BA6-C685-4452-8E8C-01BCDFB9CD2E"
},
{
cssPosition:"136,-1,-1,140,180,22",
enabled:false,
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"136",
width:"180"
},
dataProviderID:"ap_invoice_amount_left",
editable:false,
enabled:false,
formIndex:13,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:-2,
visible:true
},
name:"amount_left",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7D2F9757-369F-48DC-A7AF-F4107BE5CB1B"
},
{
cssPosition:"86,-1,-1,800,151,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"86",
width:"151"
},
enabled:true,
formIndex:6,
onActionMethodID:"84AF6B38-F4E3-416B-8EA3-BC470469390B",
styleClass:"btn btn-default button_bts",
tabSeq:11,
text:"i18n:avanti.lbl.activityStatus_onHold",
visible:true
},
name:"btn_onHold",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"83036665-89CB-4914-9DF7-CB3B399897A7"
},
{
cssPosition:"31,-1,-1,5,320,159",
json:{
cssPosition:{
bottom:"-1",
height:"159",
left:"5",
right:"-1",
top:"31",
width:"320"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_1A14696D",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"876F6952-9F28-44EA-A57E-E47BAD7D4C8F"
},
{
cssPosition:"36,-1,-1,470,180,22",
enabled:false,
formIndex:8,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"36",
width:"180"
},
dataProviderID:"ap_invoice_to_ap_supplier.supplier_matching_method",
editable:false,
enabled:false,
formIndex:8,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:5,
valuelistID:"AD202BA1-5849-418F-885F-4A2A2B917BCB",
visible:true
},
name:"matching_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8BF66C3B-B411-4D02-9B13-2AB53A0F51F9"
},
{
cssPosition:"61,-1,-1,670,125,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"670",
right:"-1",
top:"61",
width:"125"
},
enabled:true,
formIndex:3,
labelFor:"status_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"status_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8EE131FD-DBFE-48B9-9319-1C506412596F"
},
{
cssPosition:"36,-1,-1,140,180,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"36",
width:"180"
},
dataProviderID:"supplier_id",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"F0342C2A-8B8A-4FC5-A44F-B6FA6547E1BB",
styleClass:"typeahead_bts",
tabSeq:1,
valuelistID:"9848DA5F-A1CE-47B7-A62A-DA6DCF791A7D",
visible:true
},
name:"supplier_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"91218F96-FACE-4B56-9E34-E7E8CB62934D"
},
{
cssPosition:"86,-1,-1,470,180,22",
formIndex:11,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"86",
width:"180"
},
dataProviderID:"terms_id",
enabled:true,
formIndex:11,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:7,
valuelistID:"DD1D57F1-16E1-4469-8490-FB858195D026",
visible:true
},
name:"terms_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"97C8F7C7-2BE2-4CCD-A92E-4FFA25780127"
},
{
cssPosition:"111,-1,-1,298,22,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"298",
right:"-1",
top:"111",
width:"22"
},
enabled:true,
formIndex:5,
onActionMethodID:"5ADC19CC-66B3-49DB-BF74-E1E4E3FCFC8B",
styleClass:"label_bts text-center",
tabSeq:0,
text:"%%globals.icon_tableViewInfo%%",
toolTipText:"i18n:avanti.tooltip.showInvoiceDetails",
visible:true
},
name:"btnInvoiceDetails",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A2A6244A-C5F4-4220-86FA-2E8346788071"
},
{
cssPosition:"36,-1,-1,340,125,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"340",
right:"-1",
top:"36",
width:"125"
},
enabled:true,
formIndex:14,
labelFor:"matching_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.matchingMethod",
visible:true
},
name:"matching_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A876280B-6FCA-4A36-884E-7D47BDAA9AA5"
},
{
cssPosition:"136,-1,-1,800,151,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"136",
width:"151"
},
enabled:true,
formIndex:5,
onActionMethodID:"DEEA34C5-1189-4B84-86FB-99D820BE542B",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.readyToPost",
visible:true
},
name:"btnReadyToPost",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"AC044D53-89F4-4F05-97B9-56C4677084A5"
},
{
cssPosition:"111,-1,-1,800,151,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"111",
width:"151"
},
enabled:true,
formIndex:5,
onActionMethodID:"06BF416F-036F-4DFD-99A9-534DE0F23BA0",
styleClass:"btn btn-default button_bts",
tabSeq:13,
text:"i18n:avanti.lbl.open",
visible:true
},
name:"btn_open",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"ADBD295B-E19E-4EA3-AB13-D21B89A715AE"
},
{
cssPosition:"2,-1,-1,670,125,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"670",
right:"-1",
top:"2",
width:"125"
},
enabled:true,
formIndex:21,
labelFor:"apinv_reg_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.registerNumber",
visible:true
},
name:"apinv_reg_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AF211EFA-007C-45F7-A0BC-A6D6DECA9E32"
},
{
cssPosition:"36,-1,-1,10,125,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"36",
width:"125"
},
enabled:true,
formIndex:4,
labelFor:"supplier_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.supplier",
visible:true
},
name:"supplier_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AFC1AEB5-3EED-4100-BC73-2D86CE02460A"
},
{
height:450,
partType:5,
typeid:19,
uuid:"AFDF10E3-FB43-4874-AE06-5CE6985BC7B7"
},
{
cssPosition:"36,-1,-1,800,151,22",
formIndex:20,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"800",
right:"-1",
top:"36",
width:"151"
},
dataProviderID:"ap_invoice_doc_number",
editable:true,
enabled:true,
formIndex:20,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"ap_invoice_doc_number",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B81DAB63-D97D-40AD-9553-05FBF837CBB4"
},
{
cssPosition:"61,-1,-1,10,125,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"61",
width:"125"
},
enabled:true,
formIndex:7,
labelFor:"ap_invoice_num",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceNumber",
visible:true
},
name:"ap_invoice_num_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C19AF55A-8B2F-4188-B547-E64ECB20178E"
},
{
cssPosition:"195,-1,0,5,995,255",
formIndex:16,
json:{
cssPosition:{
bottom:"0",
height:"255",
left:"5",
right:"-1",
top:"195",
width:"995"
},
formIndex:16,
tabs:[
{
containedForm:"19756B76-EC78-4909-B406-EF05C5EB2E77",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"ap_invoice_to_ap_invoice_detail",
svyUUID:"BA457809-03ED-4067-82CA-FECC2FB0B1C7",
text:"i18n:avanti.lbl.invoiceDetail"
},
{
containedForm:"AC490549-4FEC-443D-A649-89CD3BCFA6F6",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:null,
svyUUID:"AE05B560-639D-4524-A565-7A2D4A4CC94E",
text:"i18n:avanti.lbl.notes"
},
{
containedForm:"D52E824F-1AC2-4EB4-B7DD-50828F5BA968",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"ap_invoice_to_ap_invoice_dist",
svyUUID:"164F260D-57D4-4491-886F-F65F07338413",
text:"i18n:avanti.lbl.distributions"
},
{
containedForm:"D8AECB5C-6F9A-4C63-A4B9-E6A2E17B6961",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabIntegrationDetails",
relationName:"_to_ap_invoice",
svyUUID:"9BAEA098-A812-497B-9FE7-0F1D09583B70",
text:"i18n:avanti.lbl.IntegrationDetails"
}
]
},
name:"tabs",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"C80922CB-5792-4DC5-9FBD-5A74713877D3"
},
{
cssPosition:"31,-1,-1,665,335,159",
json:{
cssPosition:{
bottom:"-1",
height:"159",
left:"665",
right:"-1",
top:"31",
width:"335"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_4E3229DE",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D2BC3A9F-CA32-47DF-96BE-C6BDA48E7EA9"
},
{
cssPosition:"31,-1,-1,335,320,159",
json:{
cssPosition:{
bottom:"-1",
height:"159",
left:"335",
right:"-1",
top:"31",
width:"320"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_ACB04CAC",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DB3CD17A-D491-4CE0-AB35-4607138B3983"
},
{
cssPosition:"86,-1,-1,10,125,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"86",
width:"125"
},
enabled:true,
formIndex:2,
labelFor:"ap_invoice_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceDate",
visible:true
},
name:"ap_invoice_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DDF282D2-B10C-434C-8201-D2B4D8D33F31"
},
{
cssPosition:"111,-1,-1,340,125,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"340",
right:"-1",
top:"111",
width:"125"
},
enabled:true,
formIndex:2,
labelFor:"ap_invoice_due_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceDueDate",
visible:true
},
name:"ap_invoice_due_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E7EE4A23-227F-4987-9193-D9BF7CA211F6"
},
{
cssPosition:"0,-1,-1,0,1000,26",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"26",
left:"0",
right:"-1",
top:"0",
width:"1000"
},
enabled:true,
formIndex:2,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.APInvoice_DetailView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E822C8CF-CFC4-4677-819A-8F3D5561AB82"
},
{
cssPosition:"61,-1,-1,140,180,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"61",
width:"180"
},
dataProviderID:"ap_invoice_num",
editable:true,
enabled:true,
formIndex:8,
onDataChangeMethodID:"97D237DC-790E-40F3-981E-C4AFEC4F0AD2",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:2,
visible:true
},
name:"ap_invoice_num",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"EAED798A-AA5A-489F-ADC1-D7BC0750D2AA"
},
{
cssPosition:"61,-1,-1,340,125,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"340",
right:"-1",
top:"61",
width:"125"
},
enabled:true,
formIndex:10,
labelFor:"curr_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.curr_id",
visible:true
},
name:"curr_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F3C41DE9-BA34-47DA-AED8-E402754C975D"
},
{
cssPosition:"111,-1,-1,140,153,22",
enabled:false,
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"111",
width:"153"
},
dataProviderID:"ap_invoice_amount",
editable:false,
enabled:false,
formIndex:1,
onActionMethodID:"5ADC19CC-66B3-49DB-BF74-E1E4E3FCFC8B",
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:4,
visible:true
},
name:"ap_invoice_amount",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F8B9FC90-00AD-47B4-8AE8-58C466B01D0A"
}
],
name:"ap_invoice_dtl",
onRecordSelectionMethodID:"1A99FE36-CD51-4A11-82DA-1ACBB25828E0",
onShowMethodID:"615F0B41-CFA7-4980-B21E-885691BB863C",
scrollbars:33,
size:"1000,304",
styleName:null,
typeid:3,
uuid:"8F835DE9-B398-4324-A74F-EEB107CE8E58"