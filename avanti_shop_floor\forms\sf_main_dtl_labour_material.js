/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B86FEA91-CC64-4A11-AB98-7AF18E8AD0B4",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"18081439-990C-466B-BB82-0470A737F8B7"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"290E44BA-358F-47C3-A8EB-C7454236820D"}
 * @AllowToRunInFind
 */
function onShowForm(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, event]);
			return null;
		}
	}

	databaseManager.setAutoSave(false)
	
	/**@type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec('in_item', ['item_track_rolls'], [1])
	
	elements.grid.getColumn(elements.grid.getColumnIndex("roll_dtl")).visible = false;
	elements.grid.getColumn(elements.grid.getColumnIndex("roll_dtl_mill")).visible = false;
	if(rItem){
		if(rItem.in_item_to_in_item_class.itemclass_show_roll_unique_id==1){
			elements.grid.getColumn(elements.grid.getColumnIndex("roll_dtl")).visible = true;
		}
		else if(rItem.in_item_to_in_item_class.itemclass_show_mill_roll_num==1){
			elements.grid.getColumn(elements.grid.getColumnIndex("roll_dtl_mill")).visible = true;
		}
	}
	
	refreshUI();
	
	var result =  _super.onShowForm(firstShow, event)
	controller.readOnly = false
	
	reloadMaterialExpectedQuantity();
	
	return result
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A0CE29B8-65AB-4B0B-9342-9FA23E817897"}
 */
var btnCopy_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.duplicateMaterialCostEntry');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4588FA2D-7F1A-4547-ABA7-E8669D67007B"}
 */
var btnLookupItems_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.lookup');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"795566BB-A055-411A-817B-B5A119D8F04A"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnLookupItems" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		btnLookupBins(event);
	}
	if (col.id == "btnCopy" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onAction_btnCopy(event);
	}
	if (col.id == "info_icon" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onActionOpenDetails(event);
	}
	if (col.id == "btnMoveDelete" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onActionDelete(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"B31CD3BA-81B3-4731-929B-7C39A842BA27"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "warehouse" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChangeWarehouse(oldValue, newValue, event);
	}
	if (col.id == "qty_used" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChange_qtyUsed(oldValue, newValue, event);
	}
	if (col.id == "bin_location" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChangeBinLocation(oldValue, newValue, event);
	}
	if (col.id == "roll_dtl_mill" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChange_roll(oldValue, newValue, event);
	}
	if (col.id == "roll_dtl" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		onDataChange_roll(oldValue, newValue, event);
	}
}

/**
 * @properties={typeid:35,uuid:"4F16777C-5C94-4E7D-B1C3-27ABECF8F421",variableType:-4}
 */
var _bypassRecordVarSet = false;

/**
 * @type {JSRecord<db:/avanti/prod_job_cost_material_entry>}
 * @properties={typeid:35,uuid:"B2AA3EFE-1CF7-4E66-B8E8-1E42A56A347E",variableType:-4}
 */
var _rEntry = null;

/**
 * @type {JSRecord<db:/avanti/prod_job_cost_material>}
 * @properties={typeid:35,uuid:"C6EE09F9-145D-4E8F-AFC6-3885F6454802",variableType:-4}
 */
var _rMat = null;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"48E84274-6C20-4A69-8A4F-588CDAD90479",variableType:-4}
 */
var associate_to_cost_entry = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5A2AD1C8-2F5B-4DBE-A0E2-005A4CA63477"}
 */
var comment = '';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"379C92C7-D556-4C7D-9928-BC0FB3F9CF31",variableType:8}
 */
var quantity_used = 0;

/**
 * @type {UUID}
 *
 * @properties={typeid:35,uuid:"1123329F-89C6-40E0-9B9F-F9C1994B2DC4",variableType:-4}
 */
var selected_item_id = null;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"369D4AAE-9860-448A-AB0B-A2F31348421C",variableType:-4}
 */
var show_all = false;

/**
 * @type {JSRecord<db:/avanti/sys_note>}
 * @properties={typeid:35,uuid:"86546432-ED54-43CA-8630-A49D08EC4D37",variableType:-4}
 */
var _rSavedNote = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"25B839D9-E8CD-44BB-8F36-9CE7F0E922D2",variableType:-4}
 */
var aFilters = new Array();

/**
 * @param {UUID|String} sItemId
 *
 * @properties={typeid:24,uuid:"A2BD7FDC-405D-456E-BE81-4F5AD5AF0B63"}
 */
function createEntryRec(sItemId){
	_rEntry = foundset.getRecord(foundset.newRecord())
	
	_rEntry.empl_id = globals.avShopFloor_employeeID
	_rEntry.item_id = sItemId
	_rEntry.job_id = forms.sf_main_dtl.selected_job_id
	_rEntry.ordrevds_id = forms.sf_main_dtl.selected_ordrevds_id

	var taskID = getTaskIDOrCCIDFromMSGRPID();
	/**@type {JSRecord<db:/avanti/prod_job_cost_material_entry>} */
	var rMaster = scopes.avDB.getRec('prod_job_cost_material_entry', ['item_id', 'ordrevdstask_id', 'ordrevds_id', 'jcme_is_master'], [sItemId, taskID, forms.sf_main_dtl.selected_ordrevds_id, 1])
	
	// see if there is already an entry rec somehow
	if(!rMaster){
		// create new master entry rec - so other employees can pick it up
		rMaster = scopes.avDB.duplicateRecord(_rEntry)
		rMaster.jcme_is_master = 1
		rMaster.empl_id = null
		rMaster.ordrevdstask_id = taskID
		
		databaseManager.saveData(rMaster)
	}
	
	_rEntry.jcme_master_recid = rMaster.jcme_id
	_rMat = null
	databaseManager.saveData(_rEntry)
}

/**
 * @AllowToRunInFind
 * 
 * @param {UUID} selected_item_id_param
 * @param {UUID} [jclID]
 * @param {JSRecord<db:/avanti/prod_job_cost_material_entry>} [rEntry]
 * @param {JSRecord<db:/avanti/prod_job_cost_material>} [rMat]
 *
 * @properties={typeid:24,uuid:"A9518D16-0C67-4963-8751-9FAD1C88D859"}
 */
function addMaterial(selected_item_id_param, jclID, rEntry, rMat) {
	_bypassRecordVarSet=true
	
	if(rEntry){
		_rEntry = rEntry
	}
	else{
		createEntryRec(selected_item_id_param)
	}
	
	if(rMat){
		_rMat = rMat
		_rMat.jcl_id = forms.sf_main_dtl_labour.foundset.jcl_id
		
		if(_rMat.prod_job_cost_material_to_prod_job_cost.prod_job_cost_to_prod_job_cost_material.getSize()==1){
			_rMat.prod_job_cost_material_to_prod_job_cost.msgrp_id = forms.sf_main_dtl_cost.selected_msgrp_id 
		}
		else{
			_rMat.jc_id = forms.sf_main_dtl_material_main.getJobCostIDFromMSGRPID()
		}
	} 
	
	if(globals.avBase_orgIsSingleWarehouse) {
		_rEntry.whse_id = _to_in_warehouse$globals_org_id.whse_id
	} 

	// sl-2463 - whse_id was missing - causing an error when saving rec
	if(!_rEntry.whse_id && utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_prod_job) && utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail)){
		_rEntry.whse_id = _rEntry.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.whse_id
	}

	_rEntry.ordrevdstask_id = getTaskIDOrCCIDFromMSGRPID();
	
	//SL-11912  if the material entry record has the ordrevdstask_id then we can create a material record which has the jcl_id as the added operation 
	if (!_rEntry.ordrevdstask_id) {
		createMatRec();
		_rMat.jcl_id= forms.sf_main_dtl_labour.foundset.jcl_id;
	}
	
	saveData()
	
	// Reset material entry form.
	selected_item_id = null

	comment = null
	quantity_used = 0

	////////////
	
	if(!rEntry){
		reloadMaterialFoundset()
	}
	
	_bypassRecordVarSet=false
}

/**
 * @properties={typeid:24,uuid:"468D3DBC-76E7-48E6-90E9-03F59312A3FF"}
 */
function createMatRec() {
    // create mat rec if we dont have one
    if (!_rMat) {
        /**@type {JSRecord<db:/avanti/prod_job_cost_material>} */
        _rMat = scopes.avDB.getNewRec('prod_job_cost_material');

        // sl-9927 - mat rec cant share jc rec with lab rec - must have its own - otherwise either lab or
        // mat rec will not be included in crm
        var rOpJC = forms.sf_main_dtl_labour.prod_job_cost_labour_to_prod_job_cost.getRecord(1);

        /**@type {JSRecord<db:/avanti/prod_job_cost>} */
        var rMatJC = rOpJC.foundset.getRecord(rOpJC.foundset.duplicateRecord(1, false, true));
        rMatJC.jc_cost_type = scopes.avUtils.JOB_COST_TYPE.Material;
        databaseManager.saveData(rMatJC);
        _rMat.jc_id = rMatJC.jc_id;

        _rEntry.jcm_id = _rMat.jcm_id;

        _rMat.created_by_id = _rEntry.empl_id;
        _rMat.created_date = application.getTimeStamp();
        _rMat.sys_employee_shift_id = forms.sf_main_dtl.current_shift_id;
        _rMat.job_id = _rEntry.job_id;
        _rMat.item_id = _rEntry.item_id;
        _rMat.whse_id = _rEntry.whse_id;
        _rMat.whseloc_id = _rEntry.whseloc_id;
        _rMat.initemroll_id = _rEntry.initemroll_id;
        if (_rMat.whse_id) {
            _rMat.itemwhse_id = getItemWarehouseID();
        }

        //Need to assign the material cost centre based on rules
        var rMaterialCostCentre = globals.getMaterialCostCentreFromLabourCostCentre(rOpJC.cc_id, _rMat.item_id);
        if (rMaterialCostCentre) {
            rMatJC.cc_id = rMaterialCostCentre.cc_id;
            rMatJC.dept_id = rMaterialCostCentre.dept_id;
            rMatJC.opcat_id = rMaterialCostCentre.opcat_id;
            rMatJC.jc_op_code = rMaterialCostCentre.cc_op_code;
        }
        else {
            rMatJC.cc_id = null;
            rMatJC.dept_id = null;
            rMatJC.opcat_id = null;
            rMatJC.jc_op_code = null;
        }
    }

    saveData();
}

/**
 * @AllowToRunInFind
 * @return
 * @properties={typeid:24,uuid:"FE50973B-733F-4D0B-AB8C-79542843853F"}
 */
function getItemWarehouseID(){
	/** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
	var fs_in_item_warehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
	if(fs_in_item_warehouse.find() || fs_in_item_warehouse.find()){
		fs_in_item_warehouse.item_id = foundset.item_id
		fs_in_item_warehouse.whse_id = foundset.whse_id
		if(fs_in_item_warehouse.search()){
			return fs_in_item_warehouse.getRecord(1).itemwhse_id
		}
	}
	
	return null
}

/**
 * @properties={typeid:24,uuid:"C65F9D0F-1143-44FE-A092-A26598DA7EC6"}
 */
function saveData(){
	if(_rEntry){
		databaseManager.saveData(_rEntry)
	}
	if(_rMat){
		databaseManager.saveData(_rMat)
	}
}

/**
 * Delete a material entry.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"85487C72-989F-4FC8-8143-5646343B93EB"}
 */
function onActionDelete(event) {
	if(!jcme_expected_rec &&  !areThereOtherEntries()){
		if (utils.hasRecords(prod_job_cost_material_entry_to_sa_pick_detail_bin)) {
			if (utils.hasRecords(prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll)) {
				prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed = null;
				databaseManager.saveData(prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll);
			}
			prod_job_cost_material_entry_to_sa_pick_detail_bin.deleteAllRecords();
		}
		
		if(utils.hasRecords(prod_job_cost_material_entry_to_prod_job_cost_material)){
			prod_job_cost_material_entry_to_prod_job_cost_material.deleteAllRecords()
		}

		if(utils.hasRecords(prod_job_cost_material_entry_to_prod_job_cost_material_entry$master)){
			prod_job_cost_material_entry_to_prod_job_cost_material_entry$master.deleteAllRecords()
		}

		foundset.deleteRecord()
		databaseManager.saveData(foundset)
		
		reloadMaterialFoundset();
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"51F1480C-4ECD-42FB-93A5-D10B8F324FC7"}
 */
function areThereOtherEntries(){
	return _rEntry.jcme_clc_total_qty_used > _rEntry.jcme_qty_used;
}

/**
 * @properties={typeid:24,uuid:"D7E10D6D-2902-46C6-9A43-C217E45C0B2A"}
 */
function refreshUI() {
	elements.btnReleaseToPick.visible = false;
	elements.pick_doc_number.visible = false;
	elements.picklist_info.visible = false;
	elements.btnReleaseToPick.enabled = false;
	
	if (!scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(forms.sf_main_dtl_labour.foundset)) {
		elements.btnReleaseToPick.visible = true;
	
		/**@type {JSRecord<db:/avanti/prod_job>} */
		var _rJob = scopes.avDB.getRec('prod_job', ['job_id'], [forms.sf_main_dtl.selected_job_id]);
		if (_rJob && 
				utils.hasRecords(_rJob.prod_job_to_sa_order_revision_detail) && 
				utils.hasRecords(_rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail) && 
				utils.hasRecords(_rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.sa_pick_detail_to_sa_pick)) {
			elements.pick_doc_number.visible = true;
			elements.picklist_info.visible = true;
			elements.btnReleaseToPick.enabled = false;
		}
		else {
			elements.pick_doc_number.visible = true;
			elements.btnReleaseToPick.enabled = true;
		}
	}
}

/**
 * @properties={typeid:24,uuid:"D2A3F1C2-D31B-44D8-A0D0-0149496DB340"}
 */
function reloadMaterialExpectedQuantity() {
	for (var nMaterialIndex = 1; nMaterialIndex <= foundset.getSize(); nMaterialIndex++) {
		var rMaterial = foundset.getRecord(nMaterialIndex);
		
		//If it's a roll and already has expected qty calculated then don't recalculate it
		if (rMaterial.initemroll_id && rMaterial.clc_expected_qty) {
			continue;
		}
		
		if(rMaterial.jcme_expected_rec) {
			rMaterial.clc_expected_qty = scopes.avShopFloor.calculateExpectedQuantity(rMaterial);
			databaseManager.saveData(rMaterial);
		}
	}
}

/**
 * @properties={typeid:24,uuid:"BDF9EC3D-9B7E-49C0-95CC-B2B5B566F3CE"}
 */
function reloadWarehouseBins() {
	if(globals.avBase_orgIsSingleWarehouse) {
		elements.grid.getColumn(elements.grid.getColumnIndex("warehouse")).visible = false;
	} else {
		elements.grid.getColumn(elements.grid.getColumnIndex("warehouse")).visible = true;
	}
	
//	if(utils.hasRecords(_rMat.prod_job_cost_material_to_sa_order_revds_task)  &&
//		utils.hasRecords(_rMat.prod_job_cost_material_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section)  &&
//		utils.hasRecords(_rMat.prod_job_cost_material_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail)  &&
//		utils.hasRecords(_rMat.prod_job_cost_material_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse)  && 
//		_rMat.prod_job_cost_material_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_in_warehouse.whse_enable_bin_locations) {
////		elements.bin_location.visible = true
//	} else {
////		elements.bin_location.visible = false
//	}
}

/**
 * Reload materials related to job.
 * 
 * @param {Boolean} [bRecreateJCMERollsRecs]
 * 
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"D6D6E4F1-5870-4334-8EC2-018571054791"}
 */
function reloadMaterialFoundset(bRecreateJCMERollsRecs) {
	if (forms.sf_main_dtl_cost.selected_msgrp_id) {
		scopes.globals.reloadMaterialFoundsetData(foundset, forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, 
			forms.sf_main_dtl_cost.selected_msgrp_id, forms.sf_main_dtl_labour.foundset.jcl_id, bRecreateJCMERollsRecs
		);	
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"31E23B91-BA2B-44C0-BDA6-D12608A20921"}
 */
function getTaskIDOrCCIDFromMSGRPID() {
	var uID = null;
	
	if (forms.sf_main_dtl_cost.selected_msgrp_id) {
		if (scopes.globals.isThisACostCenter(forms.sf_main_dtl_cost.selected_msgrp_id)) {
			uID = forms.sf_main_dtl_cost.selected_msgrp_id;
		}
		else {
			uID = scopes.avDB.getVal('sch_milestone_group', ['msgrp_id'], [forms.sf_main_dtl_cost.selected_msgrp_id], 'ordrevdstask_id'); 
		}
	}
	
	return uID;
}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"9435B6C4-EB9A-400A-8CC1-6578DCD4F8B5"}
 */
function onHide(event) {
	rollbackEditedRecords()
	return _super.onHide(event)
}

/**
 * Rollback any changes made in the material section for a time entry to prevent error: 
 		* "couldn't load dataset because foundset had editted records but couldn't save it"
 * @properties={typeid:24,uuid:"77313A2A-E8AE-4C06-9A71-582A14E0987C"}
 */
function rollbackEditedRecords() {
	databaseManager.revertEditedRecords(foundset)
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"54697558-24D2-4F1C-BD55-04D9935A886A"}
 */
function onDataChangeWarehouse(oldValue, newValue, event) {
	createMatRec()
	whseloc_id=null
	initemroll_id=null
	
	refreshRollFields()
	
	if(_rMat){
		_rMat.whse_id = _rEntry.whse_id
		_rMat.itemwhse_id = getItemWarehouseID()
		_rMat.whseloc_id=null
		_rMat.initemroll_id=null
	}
	
	// sl-4907
	scopes.globals.updateQtyAvail(foundset);
	
	if (jcme_qty_used > jcme_clc_qty_avail && jcme_qty_used && jcme_clc_qty_avail){
		// sl-4907 - warn - but allow them to enter the value
		elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.insufQtyInThatLoc') + ' '+ foundset.prod_job_cost_material_entry_to_in_item.item_code;
	} 
	else {
		elements.displayWarning.text = "";
	}
	
	saveData()
	
	globals["avUtilities_setVL_BinLocationsByItemWhse_ret_locid"](globals.UUIDtoStringNew(foundset.item_id), globals.UUIDtoStringNew(foundset.whse_id))
	
	return true
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"C85C8690-5366-4DBF-AB2D-956D1BF49B81"}
 */
function onDataChangeBinLocation(oldValue, newValue, event) {
	initemroll_id=null
	refreshRollFields()
	//SL-10192  if the user has selected a bin location that hasn't the selected item
	//we create  the corresponding records for both  in_warehouse_location
	//and in_item_warehouse in order to avoid bad transactions 
	if (_rMat) {
		_rMat.whseloc_id = newValue;
		if(_rMat.whse_id != foundset.getSelectedRecord().whse_id){
			  _rMat.whse_id = foundset.getSelectedRecord().whse_id;
		}
		if (!utils.hasRecords(_rMat.prod_job_cost_material_to_in_item_warehouse_location)) {
			if (!utils.hasRecords(_rMat.prod_job_cost_material_to_in_item_warehouse)) {
				/** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
				var fs_in_item_warehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
				fs_in_item_warehouse.newRecord();
				fs_in_item_warehouse.getSelectedRecord().item_id = _rMat.item_id;
				fs_in_item_warehouse.getSelectedRecord().whse_id = _rMat.whse_id;
				fs_in_item_warehouse.getSelectedRecord().itemwhse_unavailable_qty = 0;
				fs_in_item_warehouse.getSelectedRecord().itemwhse_unusable_qty = 0;
				databaseManager.saveData(fs_in_item_warehouse.getSelectedRecord());
			}
			/***@type {JSFoundSet<db:/avanti/in_item_warehouse_location>} ***/
			var inItemWarehouseFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_location');
			inItemWarehouseFoundset.newRecord();
			inItemWarehouseFoundset.getSelectedRecord().item_id = _rMat.item_id
			inItemWarehouseFoundset.getSelectedRecord().itemwhse_id = _rMat.prod_job_cost_material_to_in_item_warehouse.itemwhse_id
			inItemWarehouseFoundset.getSelectedRecord().itemwhseloc_active = 1;
			inItemWarehouseFoundset.getSelectedRecord().itemwhseloc_onhand_qty = 0;
			inItemWarehouseFoundset.getSelectedRecord().itemwhseloc_unavailible_qty = 0;
			inItemWarehouseFoundset.getSelectedRecord().itemwhseloc_unusable_qty = 0;
			inItemWarehouseFoundset.getSelectedRecord().whseloc_id = _rMat.whseloc_id;
			if (!_rMat.itemwhse_id) {
				_rMat.itemwhse_id = _rMat.prod_job_cost_material_to_in_item_warehouse.itemwhse_id;
			}
			databaseManager.saveData();

		}
		_rMat.initemroll_id = null;
	}

	// sl-4907
	scopes.globals.updateQtyAvail(foundset);
	if (jcme_qty_used > jcme_clc_qty_avail && jcme_qty_used && jcme_clc_qty_avail){
		// sl-4907 - warn - but allow them to enter the value
		elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.insufQtyInThatLoc') + ' '+ foundset.prod_job_cost_material_entry_to_in_item.item_code;
	} 
	else {
		elements.displayWarning.text = "";
	}
	saveData()

	return true
}

/**
 * @properties={typeid:24,uuid:"DB6C7BE2-26B9-4947-BA31-79A8694E4F77"}
 */
function refreshRollFields(){
	application.setValueListItems("vl_rollByItem", [], []);
	application.setValueListItems("vl_rollByItem_millnum", [], []);
	
	if(item_id && whse_id && scopes.avInv.isTrackedRoll(item_id)){
		scopes.avInv.loadRollByItemVL(item_id, whse_id, whseloc_id, true)
	}
}

/**
 * @private 
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"1FEA8FD3-3F6F-46DC-961E-09A0583633B1"}
 */
function itemCodeLookup(event) {
	forms.in_itemLookup._ItemDesc = null;
	forms.in_itemLookup._Supplier = null;
	forms.in_itemLookup._callbackForm = event.getFormName();
	forms.in_itemLookup._callbackMethod = "returnFromItemLookup"
	globals.DIALOGS.showFormInModalDialog(forms.in_itemLookup, -1, -1, 1250, 725, i18n.getI18NMessage("avanti.dialog.itemLookup_title"), true, false, "dlgItemLookup", true);
}

/**
 * Add an external item to the value list (an item that was not originally associated to the job). 
 * @param {JSEvent} event
 * @param {String} sItemId - The Item UUID
 * @param {String} sItemCode - the Item Code
 *
 * @return
 * @properties={typeid:24,uuid:"01394D1A-9409-4F50-AAA3-13BC8237009E"}
 */
function returnFromItemLookup(event, sItemId, sItemCode) {
	var aItemIdInPickList = [];
	if(sItemId){
		if (utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job) && 
				utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail) && 
				utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail)) {
			for(var i = 1; i <= foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.getSize(); i++) {
				var rPickDtl = foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail.getRecord(i);
				aItemIdInPickList.push(rPickDtl.item_id);
			}
			
			if (aItemIdInPickList.indexOf(sItemId) == -1) {
				var answer = globals.DIALOGS.showQuestionDialog('Confirmation', i18n.getI18NMessage('avanti.dialog.ShopFloorMatDoesNotMatchPickList_msg'),
					i18n.getI18NMessage('avanti.dialog.yes'),
					i18n.getI18NMessage('avanti.dialog.no'));
				if (answer != i18n.getI18NMessage('avanti.dialog.yes')) {
					return null;
				}
			}
		}
		selected_item_id = sItemId;
		associate_to_cost_entry = true;
		addMaterial(selected_item_id, forms.sf_main_dtl_labour.foundset.jcl_id);
		refreshRollFields();
		globals["avUtilities_setVL_BinLocationsByItemWhse_ret_locid"](globals.UUIDtoStringNew(foundset.item_id), globals.UUIDtoStringNew(foundset.whse_id));

	}
	return null;
}

/**
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"776D31F2-5278-4040-B41C-E59F225BC678"}
 */
function onActionOpenDetails(event) {
	if ( _rMat ) {
		
		if (globals.nav.mode === "browse"){
			globals.nav.mode = 'add';
		}
		
		var oParams = new Object();
		
		if ( foundset.getSelectedRecord() === null || foundset.getSelectedRecord().prod_job_cost_material_entry_to_prod_job_cost_material === null || 
			foundset.getSelectedRecord().prod_job_cost_material_entry_to_prod_job_cost_material.note_id === null ) {
			oParams.mode = "newcommit";
		} else {
			oParams.mode = "editCommit";
			oParams.pk = foundset.getSelectedRecord().prod_job_cost_material_entry_to_prod_job_cost_material.note_id;
		}
		oParams.fields = ['note_object_source_type', 'note_object_source_id', 'note_object_relation_type', 'note_object_relation_id', 'note_creation_empl_id', '_additionalRelation1'];
		
		/** @type{JSFoundSet<db:/avanti/prod_job>} */
		var fsJob = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job');
		fsJob.loadRecords(forms.sf_main_dtl.selected_job_id);
		
		var sCustomerID = fsJob.prod_job_to_sa_customer.cust_id;
		
		var sNoteSourceType = "Customer";
		var sNoteSourceID = sCustomerID;
		var sNoteRelationType = "Job";
		var sNoteRelationID = forms.sf_main_dtl.selected_job_id;
		var sAdditionalRelation1 = "";
		
		var sSelectedMaterialDesc = null; 
			
		if (_rMat && utils.hasRecords(_rMat.prod_job_cost_material_to_in_item) && _rMat.prod_job_cost_material_to_in_item.item_code && _rMat.prod_job_cost_material_to_in_item.item_desc1 ) {
			sSelectedMaterialDesc = _rMat.prod_job_cost_material_to_in_item.item_code + ": " + _rMat.prod_job_cost_material_to_in_item.item_desc1;
		} 
		 
		if ( sSelectedMaterialDesc ) {
			sAdditionalRelation1 = "Item: " + sSelectedMaterialDesc;
		}
		
		oParams.data = [sNoteSourceType, sNoteSourceID, sNoteRelationType, sNoteRelationID, globals.avShopFloor_employeeID, sAdditionalRelation1];
		
		_rSavedNote = null;
		
		globals.svy_nav_showLookupWindow(event, "note_id", "System_Notes", null, null, oParams);

		if ( _rSavedNote ) {

			foundset.getSelectedRecord().prod_job_cost_material_entry_to_prod_job_cost_material.jcm_comment = _rSavedNote.note_text;
			foundset.getSelectedRecord().prod_job_cost_material_entry_to_prod_job_cost_material.note_id = _rSavedNote.note_id;
			
			databaseManager.saveData(foundset.getSelectedRecord());
		}		
		
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D1A7AC4E-F1BF-4374-9CDF-DA8139C548AA"}
 */
function onDataChange_qtyUsed(oldValue, newValue, event) {
	try {
		var sClicksPref = globals.avBase_getSystemPreference_String(142);
		elements.displayWarning.text = '';
		
		var sDecimalPlaces = 0;
		if (utils.hasRecords(foundset.prod_job_cost_material_entry_to_in_item)) {
			sDecimalPlaces = foundset.prod_job_cost_material_entry_to_in_item.item_decimal_places;
		}
		
		//Check if rolls qty exceeds max qty set on pick list
		if (utils.hasRecords(foundset.prod_job_cost_material_entry_to_in_item) 
				&& foundset.prod_job_cost_material_entry_to_in_item.item_track_rolls == 1 
				&& utils.hasRecords(foundset.prod_job_cost_material_entry_to_sa_pick_detail_bin) 
				&& foundset.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty > 0 
				&& newValue > globals["avUtilities_roundNumber"](foundset.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty, sDecimalPlaces)) {
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.warning'), i18n.getI18NMessage('avanti.dialog.MaxQtyCanBeUsedForRolls') + ' ' + globals["avUtilities_roundNumber"](foundset.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty, sDecimalPlaces), i18n.getI18NMessage('avanti.dialog.ok'));
			jcme_qty_used = oldValue;
			return true;
		}

		if(newValue && newValue >= 0){
			createMatRec()
			_rMat.jcm_qty_used = _rEntry.jcme_qty_used;
			if (sClicksPref === 'Substrate Quantity Used') {
				globals.avShopFloor_qtyProducedChanged = true;
			}
	
			// sl-3692 - made a connection directly between lab rec and assoc mat rec
			_rMat.jcl_id = forms.sf_main_dtl_labour.foundset.jcl_id
			
			if(_rMat.prod_job_cost_material_to_prod_job_cost.prod_job_cost_to_prod_job_cost_material.getSize()==1){
				_rMat.prod_job_cost_material_to_prod_job_cost.msgrp_id = forms.sf_main_dtl_cost.selected_msgrp_id;
				
				// need to create a prod_job_cost_transactions rec too
				if(!utils.hasRecords(_rMat.prod_job_cost_material_to_prod_job_cost.prod_job_cost_to_prod_job_cost_transactions)){
					var new_cost_trans_rec = _rMat.prod_job_cost_material_to_prod_job_cost.prod_job_cost_to_prod_job_cost_transactions.getRecord(_rMat.prod_job_cost_material_to_prod_job_cost.prod_job_cost_to_prod_job_cost_transactions.newRecord())
					new_cost_trans_rec.jct_transaction_type = 'WIP'
					databaseManager.saveData(new_cost_trans_rec)
				}
			}
			else{
				_rMat.jc_id = forms.sf_main_dtl_material_main.getJobCostIDFromMSGRPID()
			}
			
			//validate
			scopes.globals.updateQtyAvail(foundset);
			if (_rMat && utils.hasRecords(_rMat.prod_job_cost_material_to_prod_job) &&
				utils.hasRecords(_rMat.prod_job_cost_material_to_prod_job.prod_job_to_sa_order_revision_detail)){
	    		validateQtyUsed(jcme_qty_used, _rMat.item_id, whse_id, whseloc_id, _rMat.prod_job_cost_material_to_prod_job.prod_job_to_sa_order_revision_detail.ordrevd_id);
			}
		}
		else if (!newValue){
			deleteMatRec()
		}
		else{
			elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.invalidQuantityUsed');
			
			jcme_qty_used = oldValue;
			return true;
				
		}
		updateTotQtyUsed();
		
		var $elements = elements.allnames;
		
		$elements.splice( $elements.indexOf('displayWarning'), 1 )[0];
		$elements.splice( $elements.indexOf('btn_item_lookup'), 1 )[0];       
		
//		if($elements.length > 0)
//		{
//		    $elements.sort(function(a,b){return elements[a].getLocationX() - elements[b].getLocationX()});
//		}
//		if(elements[$elements[$elements.indexOf('qty_used')+1]])
//			elements[$elements[$elements.indexOf('qty_used')+1]].requestFocus();
		
		saveData();
	} catch(ex) {
		application.output('Error changing quantity used ' + ex.message, LOGGINGLEVEL.ERROR);
	}
	return true;
}

/**
 * @properties={typeid:24,uuid:"7F61EAD5-CBA1-4D81-A9E6-CDB066FB3B71"}
 */
function deleteMatRec(){
	if(_rEntry.jcm_id){
		_rMat = null
		_rEntry.prod_job_cost_material_entry_to_prod_job_cost_material.deleteAllRecords()
		_rEntry.jcm_id = null
		databaseManager.saveData()
	}
}

/**
 * @properties={typeid:24,uuid:"44062224-846C-41B9-B406-387C658F7EF1"}
 */
function updateTotQtyUsed(){
	//Update jcme_clc_total_qty_used of all the rolls used in the job
	if (foundset.initemroll_id && utils.hasRecords(foundset.prod_job_cost_material_entry_to_in_item) && foundset.prod_job_cost_material_entry_to_in_item.item_track_rolls == 1) {
		for (var i = 1; i <= foundset.getSize(); i++) {
			var rRec = foundset.getRecord(i);
			if (rRec.item_id == item_id) {
				if (rRec.jcme_qty_used > 0) {
					rRec.jcme_clc_total_qty_used = scopes.avDB.getSum('prod_job_cost_material', 'jcm_qty_used', ['job_id','prod_job_cost_material_to_prod_job_cost.ordrevds_id','item_id'], [job_id, forms.sf_main_dtl.selected_ordrevds_id, item_id]);
				}
				else {
					rRec.jcme_clc_total_qty_used = 0;
				}
			}
		}
	}
	else {
		jcme_clc_total_qty_used = scopes.avDB.getSum('prod_job_cost_material', 'jcm_qty_used', ['job_id','prod_job_cost_material_to_prod_job_cost.ordrevds_id','item_id'], [job_id, forms.sf_main_dtl.selected_ordrevds_id, item_id])
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DCD7A45F-AFF4-4A57-A11E-6F3044292E4B"}
 */
function onRender_qty (event) {
	/** @type {JSRecord<db:/avanti/prod_job_cost_material>}*/
	var rRec = event.getRecord();
	var sDecimalPlaces = 0;
	
	if (rRec)
	{
		if (utils.hasRecords(rRec.prod_job_cost_material_to_in_item)) sDecimalPlaces = rRec.prod_job_cost_material_to_in_item.item_decimal_places;
	}
	
	var sFormat = globals.avUtilities_ItemQtyFormat(sDecimalPlaces);
	event.getRenderable()['format'] = sFormat;
	
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"6D3541F9-3C9A-4557-AB96-90E15B0D4B1D"}
 */
function onRecordSelection(_event, _form) {
	var ret = _super.onRecordSelection(_event, _form)

	if(!_bypassRecordVarSet){
		_rEntry = foundset.getSelectedRecord()
		_rMat = null
		if(_rEntry){
			if(utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_prod_job_cost_material)){
				_rMat = _rEntry.prod_job_cost_material_entry_to_prod_job_cost_material.getRecord(1)
			}
		}
		globals["avUtilities_setVL_BinLocationsByItemWhse_ret_locid"](globals.UUIDtoStringNew(foundset.item_id), globals.UUIDtoStringNew(foundset.whse_id))
		refreshRollFields()
		
		if (jcme_qty_used > jcme_clc_qty_avail && jcme_qty_used && jcme_clc_qty_avail){
			// sl-4907 - warn - but allow them to enter the value
			elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.insufQtyInThatLoc') + ' '+ foundset.prod_job_cost_material_entry_to_in_item.item_code;
		} 
		else {
			elements.displayWarning.text = "";
		}
		
		
	}
	
	return ret
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"77B54AF8-4364-4F2A-8F3F-0715BE8EA05B"}
 */
function btnLookupBins(event) {
	if(warehouseUsesBins()){
		globals.svy_nav_showLookupWindow(event, "whseloc_id", "Warehouse_Bin_Locations", "validateBinLocLookupSelection_rec", 'addLookupFSFilter', null);
	}
}

/**
 * @properties={typeid:24,uuid:"713D2754-667B-4EC6-8409-71329A0BF376"}
 */
function validateBinLocLookupSelection_rec(){
	onDataChangeBinLocation(null, globals.UUIDtoStringNew(whseloc_id), null)
}

/**
 * @param {JSFoundset<db:/avanti/in_warehouse_location>} fs
 * 
 * @return {JSFoundset<db:/avanti/in_warehouse_location>}
 *
 * @properties={typeid:24,uuid:"982E1856-0654-4C8B-B958-1CFB3D0C590E"}
 */
function addLookupFSFilter(fs) {
	var sSQL = "SELECT DISTINCT location.whseloc_id \
					FROM in_warehouse_location location \
					JOIN in_item_warehouse warehouse ON (location.whse_id = warehouse.whse_id) \
				WHERE warehouse.whse_id = '" + foundset.whse_id + "' \
					AND (location.whseloc_unusable = 0 OR location.whseloc_unusable IS NULL)\
					AND (location.whseloc_unavailable = 0 OR location.whseloc_unavailable IS NULL)\
					AND (location.whseloc_active = 1)";

	fs.addFoundSetFilterParam('whseloc_id', 'sql:in', sSQL, 'warehouse');

	return fs;
}

/**
 * @return {Boolean}
 * @properties={typeid:24,uuid:"9B7D2C20-09AC-45A3-9F58-25BCD10B4484"}
 */
function warehouseUsesBins(){
	if(whse_id){
		return scopes.avDB.getVal('in_warehouse', ['whse_id'], [whse_id], 'whse_enable_bin_locations') == 1
	}
	
	return false
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5C46F6CD-F636-4EEE-8D8A-928738288EF4"}
 */
function onDataChange_roll(oldValue, newValue, event) {

	var ret = true;
	
	if (_rEntry) {
		/** @type {JSFoundSet<db:/avanti/prod_job_cost_material_entry>} */
		var fsRollRecords = scopes.avDB.getFS('prod_job_cost_material_entry', ['job_id', 'org_id', 'empl_id', 'ordrevds_id', 'item_id', 'initemroll_id'], [_rEntry.job_id, globals.org_id, globals.avShopFloor_employeeID, _rEntry.ordrevds_id, _rEntry.item_id, newValue]);

		elements.displayWarning.text = '';

		if (fsRollRecords.getSize() > 0) {
			var msg = i18n.getI18NMessage('avanti.dialog.rollisAlreadySelected'); 
			elements.displayWarning.text = msg;
			ret = false;
		} 
		else {
			if (validateQtyAndRoll(_rEntry.jcme_qty_used, newValue)) {
				if (_rMat) {
					_rMat.initemroll_id = newValue;
				}
				
				if (_rEntry.pickdb_id && utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin)) {
					if (utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll)) {
						_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_qty_avail += _rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty;
						_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed -= _rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty;
						if (_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed < 0) {
							_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed = 0;
						}
						databaseManager.saveData(_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll);
					}
					_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.initemroll_id = newValue;
					databaseManager.saveData(_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin);
					_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_qty_avail -= _rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty;
					_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed += _rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.pickdb_qty;
					if (_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_qty_avail < 0) {
						_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_qty_avail = 0;
					}
					databaseManager.saveData(_rEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll);
				}
				saveData();
				ret = true;
			} 
			else {
				ret = false;
			}
		}
	}
	return ret;
}

/**
 * @param {Number} qty
 * @param {String} roll_ID
 *
 * @return
 * @properties={typeid:24,uuid:"2CE49624-207F-4F6A-B460-D82A5AE1F1A1"}
 */
function validateQtyAndRoll(qty, roll_ID){
	var ret = true
	elements.displayWarning.text = '';
	var qtyAvail = scopes.avInv.getQtyAvailInRoll(roll_ID) 
	if(qtyAvail< qty){
		var msg = i18n.getI18NMessage('avanti.dialog.InsufRollQty') + qtyAvail.toString() + ')'
		elements.displayWarning.text = msg;
		ret = false
	}
	
	return ret
}

/**
 * @param {Number} qtyUsed
 * @param {UUID} itemID
 * @param {UUID} whseID
 * @param {UUID} whselocID
 * @param {UUID} ordrevdID
 *
 * @return
 * @properties={typeid:24,uuid:"250C8875-2086-4EC5-9E9B-44EC530211B1"}
 */
function validateQtyUsed(qtyUsed,itemID, whseID, whselocID, ordrevdID){
	elements.displayWarning.text = '';
	//if(hasLocatonBeenSelected(whseID, whselocID) && jcme_qty_used > 0){
	if(jcme_qty_used > 0){
		var nQtyReservedAgainstSelectedJob = getQtyReservedAgainstSelectedJob(itemID,whseID,ordrevdID);
		var nQtyReservedAgainstOtherJobs = getQtyReservedAgainstOtherJobs(itemID,whseID,ordrevdID);
		
		if (nQtyReservedAgainstOtherJobs == 0 && nQtyReservedAgainstSelectedJob == 0){
			if (jcme_qty_used > jcme_clc_qty_avail && jcme_clc_qty_avail){
				// sl-4907 - warn - but allow them to enter the value
				elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.insufQtyInThatLoc') + ' '+ foundset.prod_job_cost_material_entry_to_in_item.item_code;
				return false;
			}
		}else if (jcme_qty_used > jcme_clc_qty_avail && jcme_clc_qty_avail){
			// sl-4907 - warn - but allow them to enter the value
			elements.displayWarning.text = i18n.getI18NMessage('avanti.dialog.insufQtyInThatLoc') + ' '+ foundset.prod_job_cost_material_entry_to_in_item.item_code;
			return false;
		} 
		
		
		if(jcme_qty_used > clc_expected_qty){
			
			var sAns = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage("avanti.dialog.notification"), 
				i18n.getI18NMessage("avanti.dialog.quantityNotReservedToJob_msg"), 
				i18n.getI18NMessage("avanti.lbl.proceed"), 
				i18n.getI18NMessage("avanti.dialog.cancel"));	
			
			if (sAns == i18n.getI18NMessage("avanti.lbl.proceed"))
			{
				return true;
			}
			else
			{
				jcme_qty_used = null;
				_rMat.jcm_qty_used = null;
				whseloc_id = null;
				updateTotQtyUsed();
				scopes.globals.updateQtyAvail(foundset);
				return false;
			}
		}
		
	}
	
	return true
}

/**
 * @param {UUID} whseID
 * @param {UUID} whselocID
 *
 * @return
 * @properties={typeid:24,uuid:"260E75D9-91FB-45D9-8581-43D97C756EAC"}
 */
function hasLocatonBeenSelected(whseID, whselocID){
	var bHasLocatonBeenSelected = false
	
	if(whse_id){
		if(scopes.avDB.getVal('in_warehouse', ['whse_id'], [whse_id], 'whse_enable_bin_locations')){
			if(whseloc_id){
				bHasLocatonBeenSelected = true
			}
		}
		else{
			bHasLocatonBeenSelected = true
		}
	}
	
	return bHasLocatonBeenSelected
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"ADB4FABB-EE94-4AB5-9575-4D841AAFEBEF"}
 */
function onAction_showStockAllocation (event) {
	if (foundset.getSize() > 0 && utils.hasRecords(foundset.prod_job_cost_material_entry_to_in_item)){
		forms["in_reserved_dlg"].showItemReserved(foundset.prod_job_cost_material_entry_to_in_item.getSelectedRecord(), whse_id);
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C2AD119F-791C-46C7-B03A-CC3B128E3173"}
 */
function onRender_binLocation (event) {
	/***@type {JSRecord<db:/avanti/prod_job_cost_material_entry>}*/
	var rRec = event.getRecord();
	
	if (rRec)
	{
		if (utils.hasRecords(rRec.prod_job_cost_material_entry_to_in_item) && rRec.prod_job_cost_material_entry_to_in_item.item_no_bin_location != 1)
		{
			event.getRenderable().enabled = true;
		}
		else 
		{
			event.getRenderable().enabled = false;
		}
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2C9FC693-4E95-4160-909C-8ECCA04D82EB"}
 */
function onRender_binLocationLookup (event) {
	/***@type {JSRecord<db:/avanti/prod_job_cost_material_entry>}*/
	var rRec = event.getRecord();
	
	if (rRec)
	{
		if (utils.hasRecords(rRec.prod_job_cost_material_entry_to_in_item) && rRec.prod_job_cost_material_entry_to_in_item.item_no_bin_location != 1)
		{
			event.getRenderable().enabled = true;
		}
		else 
		{
			event.getRenderable().enabled = false;
		}
	}
}

/**
 * Handle focus gained event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"89837DE9-F38C-412C-BD93-5B0C4C95103F"}
 */
function onFocusGained_qtyUsed(event) {
	// SL-20429 Prevent the user from clicking the buttons below when qty used is focused. This will fix the freezing issue.
	forms.sf_main_dtl_labour_buttons.elements.milestone_complete_btn.enabled = false;
	forms.sf_main_dtl_labour_buttons.elements.operation_complete_btn.enabled = false;
	forms.sf_main_dtl_labour_buttons.elements.resource_complete_btn.enabled = false;
	forms.sf_main_dtl_labour_buttons.elements.unfinished_btn.enabled = false;
	forms.sf_main_dtl_labour_buttons.elements.pause_btn.enabled = false;
}

/**
 * Handle focus lost event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"EE69BE34-E1C3-4EE2-8BD4-528EF94074DD"}
 */
function onFocusLost_qtyUsed(event) {
	// SL-20429 Enable the buttons once the focus is lost.
	forms.sf_main_dtl_labour_buttons.elements.milestone_complete_btn.enabled = true;
	forms.sf_main_dtl_labour_buttons.elements.operation_complete_btn.enabled = true;
	forms.sf_main_dtl_labour_buttons.elements.resource_complete_btn.enabled = true;
	forms.sf_main_dtl_labour_buttons.elements.unfinished_btn.enabled = true;
	forms.sf_main_dtl_labour_buttons.elements.pause_btn.enabled = true;
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7804DBE2-03B5-4042-9C04-AC97C6488371"}
 */
function onAction_btnCopy(event) {
	var answer = globals.DIALOGS.showQuestionDialog('Confirmation', i18n.getI18NMessage('avanti.dialog.copyRow_msg'),
		i18n.getI18NMessage('avanti.dialog.yes'),
		i18n.getI18NMessage('avanti.dialog.no'));
	if (answer == i18n.getI18NMessage('avanti.dialog.yes')) {
		/***@type {JSRecord<db:/avanti/prod_job_cost_material_entry>}*/
		var rProdJobCostMatEntry = foundset.getRecord(foundset.duplicateRecord(foundset.getSelectedIndex(), false, true));
		if (rProdJobCostMatEntry) {
			rProdJobCostMatEntry.initemroll_id = null;
			rProdJobCostMatEntry.jcme_qty_used = null;
			rProdJobCostMatEntry.jcme_expected_rec = 0;
			if (rProdJobCostMatEntry.pickdb_id && utils.hasRecords(rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin)) {
				var itemRollCommitted = 0;
				if (utils.hasRecords(rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll)) {
					itemRollCommitted = rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed;
				}
				
				/***@type {JSRecord<db:/avanti/sa_pick_detail_bin>}*/
				var rPickDtlBin = rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.getRecord(rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.duplicateRecord(rProdJobCostMatEntry.prod_job_cost_material_entry_to_sa_pick_detail_bin.getSelectedIndex(), false, true));
				if (rPickDtlBin) {
					if (utils.hasRecords(rPickDtlBin.sa_pick_detail_bin_to_in_item_roll)) {
						rPickDtlBin.sa_pick_detail_bin_to_in_item_roll.initemroll_committed = itemRollCommitted;
						databaseManager.saveData(rPickDtlBin.sa_pick_detail_bin_to_in_item_roll);
					}
					rPickDtlBin.initemroll_id = null;
					rProdJobCostMatEntry.pickdb_id = rPickDtlBin.pickdb_id;
					databaseManager.saveData(rPickDtlBin);
				}
			}
			databaseManager.saveData(rProdJobCostMatEntry);
		}
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"26885D8B-EFD4-4AE4-AFC6-C8B789A6C075"}
 */
function onAction_btnReleaseToPick(event) {
	var rRec = foundset.getSelectedRecord();

	if (rRec && utils.hasRecords(rRec, 'prod_job_cost_material_entry_to_prod_job')) {
		var rJob = rRec.prod_job_cost_material_entry_to_prod_job.getRecord(1);

		rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item.loadRecords();
		var bAllPicked = scopes.avSales.pickList_releaseJobMaterial(rJob);

		if (bAllPicked == true) {
			elements.btnReleaseToPick.enabled = false;
			elements.pick_doc_number.visible = true;
			elements.picklist_info.visible = true;
		} 
		else {
			elements.btnReleaseToPick.enabled = true;
			elements.pick_doc_number.visible = false;
			elements.picklist_info.visible = false;
		}
		reloadMaterialFoundset(true);
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"04100FA9-B0FA-41C3-A4A1-FEA80BB8909B"}
 */
function onActionOpenPickList(event) {
	forms.sa_pick_items.bEnablePrintPickAndAcceptPickInShopFloor = false;
	if (utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job) && 
			utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail) && 
			utils.hasRecords(foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail)) {
		forms.sa_pick_items.bEnablePrintPickAndAcceptPickInShopFloor = true;
		var oldNavMode = globals.nav.mode;
		globals.nav.mode = "browse";
		forms.sa_pick_items.controller.readOnly = true;
		forms.sa_pick_items.controller.loadRecords(foundset.prod_job_cost_material_entry_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_pick_detail);
		globals.DIALOGS.showFormInModalDialog(forms.sa_pick_items,-1,-1,1050,500,i18n.getI18NMessage('avanti.lbl.pickSlip'),true,false,"dlgSfPickList", true);
		if (globals.avShopFloor_employeeID && globals["avSecurity_checkForUserRight"]('Picking_Slip_View', 'btn_edit', globals.getUserFromEmp(globals.avShopFloor_employeeID)) == true) {
			databaseManager.saveData(forms.sa_pick_items.foundset);
			onShowForm(true, event);
		}
		globals.nav.mode = oldNavMode;
	}
}
