/**
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 * @private
 *
 * @properties={typeid:24,uuid:"2E85F0FB-8258-430A-8D2B-BC87A2B5BAF9"}
 */
function onRecordSelection (event, _form) {
	
	if (taskspeed_id) scopes.avTask.selectedTaskSpeedID = taskspeed_id.toString();
	
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @return
 * @properties={typeid:24,uuid:"983023F3-5F95-472E-BAFC-62E93EE55D37"}
 */
function onShow (firstShow, event) {
	
	if (taskspeed_id) scopes.avTask.selectedTaskSpeedID = taskspeed_id.toString();
	
	return _super.onShow(firstShow, event)
}

/**
 * @AllowToRunInFind
 * 
 * TODO generated, please specify type and doc for the params
 * @param foundsetindex
 * @param columnindex
 * @param record
 * @param event
 *
 * @properties={typeid:24,uuid:"B3847A84-DC7B-44A0-B6AF-3B12F9786C64"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "sort_icon" && col.styleClass.search(' disabled') == -1) {
		btnShuffle(event);
	}
	if (col.id == "btnDelete" && col.styleClass.search(' disabled') == -1) {
		scopes.globals.avUtilities_delete(event);
	}
}
/**
 * @properties={typeid:24,uuid:"28198427-9D21-41CC-87EA-5AF9309730C1"}
 */
function onReady() {
    _gridReady = 1;
}