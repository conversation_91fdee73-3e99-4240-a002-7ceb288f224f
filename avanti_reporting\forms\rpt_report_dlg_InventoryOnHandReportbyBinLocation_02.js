/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3CA60A48-226D-4457-A497-BCB4DAFBB534",variableType:4}
 */
var _includeAllItemClass = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"03EEE778-9959-4CFB-A56D-77F7A4CE2AE1"}
 */
var _toItemClass = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BC29325C-8E9F-4EEC-93C8-5C49D154837D"}
 */
var _fromItemClass = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"CACEB73B-CF47-40F1-9523-A35B2B37536D",variableType:4}
 */
var _includeAllItems = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"197E318E-7BAF-4BC0-829D-9B97BCC06BF8"}
 */
var _toItem = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7147E00E-BD1B-4286-9D59-517339BF67C9"}
 */
var _fromItem = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"EC666476-61D2-4DA3-AFBE-32E49CDD6042",variableType:4}
 */
var _includeAllItemGroups = 1;

/**
 *
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"4C4198D2-1C2B-47D2-AFF2-7D0252DA1385",variableType:4}
 */
var _fromItemGroup = null;

/**
 *
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"54F31280-38DE-40F7-9F6F-10B4271DA465",variableType:4}
 */
var _toItemGroup = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"6294624D-FBED-4EFB-B7B7-6A240698EC1B",variableType:-4}
 */
var _selectedItemGroupArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"FDF6232B-3A18-4339-AE1A-A6975106EEC2",variableType:4}
 */
var _includeSelectedItemClassCode = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A6E65C63-7920-46E7-99D0-49E7757DBF49",variableType:4}
 */
var _includeSelectedItem = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"07657797-642E-4C72-9FC1-DAD05FA66FD1",variableType:4}
 */
var _includeSelectedItemGroups = 0;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"833A0528-DBC2-44E3-AFF0-7678A85731F8",variableType:8}
 */
var includeSelectedItemClasses = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"DEC4C035-0ABA-410E-9644-C15031EEA4A8",variableType:-4}
 */
var _selectedItemClassArray = new Array();

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"5455F40A-E489-45E7-BADC-C31E77807C00",variableType:8}
 */
var includeSelectedItems = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"404A7C74-302D-4C13-A83C-E82A61459C41",variableType:-4}
 */
var _selectedItemArray = new Array();

/**
 * @properties={typeid:35,uuid:"B69CDCA7-F980-4B9B-97C1-6398F62EA006",variableType:-4}
 */
var _bProfileModified = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"20557BF6-A01C-4DD7-9F50-53CC08FBAD5F"}
 */
var fromPlant = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8C1194B8-8730-44CA-B2AC-E72A2325238C"}
 */
var fromWhse = "";

/**
 * @properties={typeid:35,uuid:"89294E4E-1DC8-472D-8516-15017272A3AC",variableType:-4}
 */
var _aPlantsReturn = [];

/**
 * @properties={typeid:35,uuid:"1145B050-73F8-4744-8B6C-BAF034BFDD6D",variableType:-4}
 */
var _aPlantsDisplay = [];

/**
 * @properties={typeid:35,uuid:"798D9D5D-169E-4EB8-8795-84FDEC4048AD",variableType:-4}
 */
var _aDivsReturn = [];

/**
 * @properties={typeid:35,uuid:"7E81DA0A-A8E9-400A-9496-494B86785ADB",variableType:-4}
 */
var _aDivsDisplay = [];

/**
 * @properties={typeid:35,uuid:"5E03E2FF-89D8-4890-AB13-981E615183BC",variableType:-4}
 */
var _aWhsesReturn = [];

/**
 * @properties={typeid:35,uuid:"1918EE49-A271-41B4-BEBE-EED3491EFFCD",variableType:-4}
 */
var _aWhsesDisplay = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"43F434CD-EC79-45E1-9138-648AEE9B3F00"}
 */
var fromDiv = "";

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"052297DA-EE6F-46FF-9666-616EBA771086",variableType:8}
 */
var includeAllParentCustomers = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"F6793840-E380-4AA5-ACB8-D3115D0842BF",variableType:8}
 */
var includeSelectedParentCustomers = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"FA579B32-807C-44C6-8B55-F53F2BB117A1"}
 */
var fromParentCustomer = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"06C6A138-83FA-4BAA-829D-A546D4B31445"}
 */
var toParentCustomer = null;

/**
*
* @type {Array}
*
*
* @properties={typeid:35,uuid:"F5BA6A37-90D2-44A1-9D5E-D78E502D0708",variableType:-4}
*/
var _selectedParentCustomerArray = new Array();

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"A5789666-5A8E-4940-BE75-D560D240EA99",variableType:8}
 */
var includeAllCustomerParts = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"862F783F-6D04-4E81-B239-D401E0FD895D",variableType:8}
 */
var includeSelectedCustomerParts = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"F115E6B9-A4C8-4BDD-A812-15CC11281C97"}
 */
var fromCustomerParts = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"76F3D15C-03C5-4175-97CB-B04A662D7865"}
 */
var toCustomerParts = null;

/**
*
* @type {Array}
*
*
 * @properties={typeid:35,uuid:"63345E09-F46B-476A-90AE-A20AFE9B5873",variableType:-4}
 */
var _selectedCustomerPartsArray = new Array();

/**
 * @type {Number}
 *
 *
 *
 * @properties={typeid:35,uuid:"802A40AB-CAD9-469D-83BB-BB9914D06A28",variableType:8}
 */
var _onSelectAllWarehouses = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"5CA893E8-A182-4BA2-A9C4-8EED63DC02B4",variableType:4}
 */
var _showInReportParentCustomer = 1;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C1DDCEA6-9686-4BA5-928D-0CAE415E78B2",variableType:4}
 */
var _showInReportCustomerParts = 1;

/**
 * @properties={typeid:35,uuid:"384D8221-BAD4-4990-A4FC-FF6CA0F17992",variableType:-4}
 */
var fromParentCustomerCode = null;

/**
 * @properties={typeid:35,uuid:"0EF1EB14-D2E1-4CF5-83D4-54D175C496A3",variableType:-4}
 */
var toParentCustomerCode = null;

/**
 * Standard Method for getting the Parameters from this filter form
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-08-14
 *
 * @returns {{aParamNames: Array, aParamValues: Array, whereClause: String}} Returns an object containing the param names you need and values
 *
 *
 * @properties={typeid:24,uuid:"DF0C9942-5FEE-44CB-8066-F0721F129DFE"}
 */
function getFilterParams()
{
	var oParams = new Object();
	var sPPlants = "";
	var sPWhse = "";
	
	var sWhere = " WHERE org_id = '" + globals.org_id + "' ";
	
	//preparing for query - Item Groups
	var sPSelectedItemGroupsSelection ="";
	for (var i = 0; i <= _selectedItemGroupArray.length - 1; i++) {
		if (i != 0) {
			sPSelectedItemGroupsSelection += ',';
		}
		sPSelectedItemGroupsSelection += "'" + _selectedItemGroupArray[i] + "' ";
	}
	
	//preparing for query - Item Class
	var sPSelectedItemClassSelection ="";
	for (var j = 0; j <= _selectedItemClassArray.length - 1; j++) {
		if (j != 0) {
			sPSelectedItemClassSelection += ',';
		}
		sPSelectedItemClassSelection += "'" + _selectedItemClassArray[j] + "' ";
	}
    
	//preparing for query - Item Class
	var sPSelectedItemSelection ="";
	for (var k = 0; k <= _selectedItemArray.length - 1; k++) {
		if (k != 0) {
			sPSelectedItemSelection += ',';
		}
		sPSelectedItemSelection += "'" + _selectedItemArray[k] + "'";
	}
	
	if (_selectedItemClassArray.length > 0) {
		sWhere += " AND itemclass_id IN (" + sPSelectedItemClassSelection + ") ";
	}
	else {
		if (_fromItemClass != null) sWhere += " AND itemclass_code >= '" + _fromItemClass + "' ";
		if (_toItemClass != null) sWhere += " AND itemclass_code <= '" + _toItemClass + "' ";
	}
	
	if (_selectedItemArray.length > 0) {
		sWhere += " AND item_id IN (" + sPSelectedItemSelection + ") ";
	} 
	else {
		if (_fromItem != null) sWhere += " AND item_code >= '" + _fromItem + "' ";
		if (_toItem != null) sWhere += " AND item_code <= '" + _toItem + "' ";
	}
	
	if (_selectedItemGroupArray.length > 0) {
		sWhere += " AND ingroup_id IN (" + sPSelectedItemGroupsSelection + ") ";
	} 
	else {
		if (_fromItemGroup != null) sWhere += " AND ingroup_code >= '" + _fromItemGroup + "' ";
		if (_toItemGroup != null) sWhere += " AND ingroup_code <= '" + _toItemGroup + "' ";
	}

	// Preparing for Parent Customer
	var sPSelectedParentCustomerSelection = "";
	for (var l = 0; l <= _selectedParentCustomerArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedParentCustomerSelection += ',';
		}
		sPSelectedParentCustomerSelection += "'" + _selectedParentCustomerArray[k] + "'";
	}
	
	if (_selectedParentCustomerArray.length > 0 && globals.avBase_selectedParentCustomersViewOption == 0) {
		sWhere += " AND parent_cust_id IN (" + sPSelectedParentCustomerSelection + ") ";
	}
	else {
		if (fromParentCustomer != null) sWhere += " AND parent_cust_id >= '" + fromParentCustomer + "' ";
		if (toParentCustomer != null) sWhere += " AND parent_cust_id <= '" + toParentCustomer + "' ";
	}

	//Preparing for Customer Part
	var sPSelectedCustomerPartsSelection = "";
	for (l = 0; l <= _selectedCustomerPartsArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedCustomerPartsSelection += ',';
		}
		sPSelectedCustomerPartsSelection += "'" + _selectedCustomerPartsArray[l] + "'";
	}
	
	if (_selectedCustomerPartsArray.length > 0 && globals.avBase_selectedCustomerPartsViewOption == 0) {
		sWhere += " AND item_cust_part_number IN (SELECT item_cust_part_number FROM in_item WHERE item_id IN( " + sPSelectedCustomerPartsSelection + ")) ";
	}
	else {
		if (fromCustomerParts != null) sWhere += " AND item_cust_part_number >= '" + fromCustomerParts + "' ";
		if (toCustomerParts != null)   sWhere += " AND item_cust_part_number <= '" + toCustomerParts + "' ";
	}
	
	if (fromPlant) {
		var aFromPlants = fromPlant.split('\n');
		for (i = 0; i <= aFromPlants.length - 1; i++) {
			if (i != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + aFromPlants[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aPlantsReturn.length - 1; j++) {
			if (j != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + _aPlantsReturn[j] + "'";
		}		
	}
	
	if (sPPlants) {
		sWhere += " AND plant_id IN (" + sPPlants + ") ";
	}
	
	if (fromWhse) {
		var aFromWhses = fromWhse.split('\n');
		for (i = 0; i <= aFromWhses.length - 1; i++) {
			if (i != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + aFromWhses[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aWhsesReturn.length - 1; j++) {
			if (j != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + _aWhsesReturn[j] + "'";
		}		
	}
	
	if (sPWhse) {
		sWhere += " AND (whse_id IN (" + sPWhse + ")) ";
	}
	
	
	oParams.whereClause = null;	
	oParams.aParamNames = ["pFromItem","pToItem", "pFromItemClass","pToItemClass", "pOrgId", "pWhere","pShowCustomerParts", "pShowParentCustomers"];
	oParams.aParamValues = [_fromItem, _toItem, _fromItemClass,_toItemClass, globals.org_id, sWhere, _showInReportCustomerParts, _showInReportParentCustomer]
	return oParams;
}

/** *
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"919A5D36-6F08-4993-A64C-CE516DE38F11"}
 */
function onShow(firstShow, event) {
	 _super.onShow(firstShow, event)
	 
	 globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	 
	 
	 _includeAllItems = 1;
	 includeSelectedItems = 0;
	 _fromItem = null;
	 _toItem = null;
	 _selectedItemArray = new Array();
	 
	 _includeAllItemClass = 1;
	 includeSelectedItemClasses = 0;
	 _fromItemClass = null;
	 _toItemClass = null;
	 _selectedItemClassArray = new Array();
	 
	 _includeAllItemGroups = 1;
	 _includeSelectedItemGroups = 0;
	 _fromItemGroup = null;
	 _toItemGroup = null;
	 _selectedItemGroupArray = new Array();
	 
	 globals.avBase_selectedParentCustomersViewOption = 0;
	 _showInReportParentCustomer = 1;
	 includeAllParentCustomers = 1;
	 includeSelectedParentCustomers = 0;
	 fromParentCustomer = null;
	 fromParentCustomerCode = null;
	 toParentCustomer = null;
	 toParentCustomerCode = null;
	 _selectedParentCustomerArray = new Array();
	 
	 
	 globals.avBase_selectedCustomerPartsViewOption = 0;
	 _showInReportCustomerParts = 1;
	 includeAllCustomerParts = 1;
	 includeSelectedCustomerParts = 0;
	 fromCustomerParts = null;
	 toCustomerParts = null;
	 _selectedCustomerPartsArray = new Array();

	 
	 _onSelectAllWarehouses = 0;
	 elements.fromWhse.enabled = true;
	 fromWhse = _to_in_warehouse$avbase_employeedefaultwarehouse.whse_code;
	 
	 refreshUI();
	 load_vl_DivisionshasWarehouses()
	 loadPlantsForDiv('From');
	 setDefaultDivisionFilter();
	 loadWarehousesForPlant('From');
}

/**
 * 
 * @properties={typeid:24,uuid:"F49AACA2-9853-4B0F-8901-9AF2C1197509"}
 */
function refreshUI()
{
	if (_includeAllItemClass == 1)
	{
		elements._fromItemClass.editable = false;
		elements._toItemClass.editable = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;
	}
	else
	{
		elements._fromItemClass.editable = true;
		elements._fromItemClass.enabled = true;
		elements._toItemClass.editable = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
	}
	
	
	if (_includeAllItems == 1)
	{
		elements._fromItem.editable = false;
		elements._toItem.editable = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;
	}
	else
	{
		elements._fromItem.editable = true;
		elements._fromItem.enabled = true;
		elements._toItem.editable = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
	}
	
	//Item Group
	if (_includeAllItemGroups == 1 || _includeSelectedItemGroups == 1) {
		elements._fromItemGroup.enabled = false;
		elements._toItemGroup.enabled = false;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;

		if (_includeSelectedItemGroups == 1) {
			elements._selectedItemGroupMessage_label.visible = true;
			_includeAllItemGroups = 0;
		}
	} 
	else {
		elements._fromItemGroup.enabled = true;
		elements._toItemGroup.enabled = true;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;
		elements._selectedItemGroupMessage_label.visible = true;
		_includeSelectedItemGroups = 0;
		_includeAllItemGroups = 0;
	}
    
	 elements._selectedItemGroupMessage_label.text = 
     (_selectedItemGroupArray && _includeSelectedItemGroups == 1 ?
     "<html><a href='#'>(" + _selectedItemGroupArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemGroupsSelected") +") </a></html>" : null);
     
	//Item Class
	if (_includeAllItemClass == 1 || includeSelectedItemClasses == 1) {
		elements._fromItemClass.enabled = false;
		elements._toItemClass.enabled = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;

		if (includeSelectedItemClasses == 1) {
			elements._selectedItemClassMessage_label.visible = true;
			_includeAllItemClass = 0;
		}
	} 
	else {
		elements._fromItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
		elements._selectedItemClassMessage_label.visible = false;
	}

	elements._selectedItemClassMessage_label.text = (_selectedItemClassArray && includeSelectedItemClasses == 1 ? "<html><a href='#'>(" + _selectedItemClassArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemClassesSelected") + ") </a></html>" : null); 
	
	//Item
	if (_includeAllItems == 1 || includeSelectedItems == 1) {
		elements._fromItem.enabled = false;
		elements._toItem.enabled = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;

		if (includeSelectedItems == 1) {
			elements._selectedItemMessage_label.visible = true;
			_includeAllItems = 0;
		}
	} 
	else {
		elements._fromItem.enabled = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
		elements._selectedItemMessage_label.visible = false;
	}

	elements._selectedItemMessage_label.text = (_selectedItemArray && includeSelectedItems == 1 ? "<html><a href='#'>(" + _selectedItemArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemsSelected") + ") </a></html>" : null);

	var bFlag = false;
	//Parent Customers
    bFlag = (includeAllParentCustomers == 1 || includeSelectedParentCustomers == 1 ? false : true);
    elements.fromParentCustomer.enabled = bFlag;
    elements.toParentCustomer.enabled = bFlag;
    elements.btnLookupFromParentCustomer.enabled = bFlag;
    elements.btnLookupToParentCustomer.enabled = bFlag;
    elements._selectedParentCustomerMessage_label.visible = !bFlag;
    
    elements._selectedParentCustomerMessage_label.text =
        (_selectedParentCustomerArray && includeSelectedParentCustomers == 1 ?
        "<html><a href='#'>(" + _selectedParentCustomerArray.length + " " +  i18n.getI18NMessage("avanti.lbl.parentCustomersSelected") +") </a></html>": null);
        
        
   // Customer Parts
    bFlag = (includeAllCustomerParts == 1 || includeSelectedCustomerParts == 1 ? false : true);
    elements.fromCustomerParts.enabled = bFlag;
    elements.toCustomerParts.enabled = bFlag;
    elements.btnLookupFromCustomerParts.enabled = bFlag;
    elements.btnLookupToCustomerParts.enabled = bFlag;
    elements._selectedCustomerPartsMessage_label.visible = !bFlag;
    
    elements._selectedCustomerPartsMessage_label.text =
        (_selectedCustomerPartsArray && includeSelectedCustomerParts == 1 ?
        "<html><a href='#'>(" + _selectedCustomerPartsArray.length + " " +  i18n.getI18NMessage("avanti.lbl.CustomerPartsSelected") +") </a></html>": null);     

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"AFB236CC-B25D-4411-842A-C283AC24B326"}
 */
function onDataChange_includeAllItemClasses(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItemClass = null;
		_toItemClass = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"444695A6-4A43-4ABB-8A5C-31915EFA36AA"}
 */
function onDataChangeIncludeAllItems(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItem = null;
		_toItem = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"E6E4B70D-9F3B-4D57-8864-90FF498C96BE"}
 */
function onDataChange_fromItemClass(oldValue, newValue, event) {
	_toItemClass = _fromItemClass
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"721556A8-A827-497C-8AB0-68F15AA4C07B"}
 */
function onDataChange_fromItem(oldValue, newValue, event) {
	_toItem = _fromItem
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"7925CC70-B0CF-42F5-9CC2-004D9DFE11AB"}
 */
function onDataChange_toItemClass(oldValue, newValue, event) {
	if(_fromItemClass>_toItemClass)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"DBE8F934-3BD0-434B-89FD-7148B8E5D46F"}
 */
function onDataChange_toItem(oldValue, newValue, event) {
	if(_fromItem>_toItem)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"AB2DC3D7-85C7-45C1-8C3A-B92A5FB0A0ED"}
 */
function afterFromItemClassLookup(record)
{
	if (record) _fromItemClass = record.itemclass_code;
	onDataChange_fromItemClass(null,_fromItemClass,null)
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"44D2FE6A-083B-4B1B-BF75-12E79ED3E5E1"}
 */
function afterToItemClassLookup(record)
{
	if (record) _toItemClass = record.itemclass_code;
}

/**
 * From Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"E75F9296-9385-482F-9A1D-4FFD510F5755"}
 */
function lookupFilter_ItemFrom(fs) 
{
	fs.addFoundSetFilterParam('item_code','>','^','PlannedPurchasing_FromItemLookupFilter');
	
	return fs;
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"417A3EE6-54DF-4C31-82EF-AEF7C12F6B41"}
 */
function afterFromItemLookup(record)
{
	if (record) _fromItem = record.item_code;
	onDataChange_fromItem(null,_fromItem,null);
	
}

/**
 * To Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"D20E81DC-92DC-4B50-8947-71067DD6F378"}
 */
function lookupFilter_ItemTo(fs) 
{
	fs.addFoundSetFilterParam('item_code','>=',_fromItem,'PlannedPurchasing_ToItemLookupFilter');
	
	return fs;
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"1F12371C-7F9F-417D-8A28-C9960233E27F"}
 */
function afterToItemLookup(record)
{
	if (record) _toItem = record.item_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"EEE3ED24-60F8-44F0-A4CF-34332E668E4A"}
 */
function onDataChange_fromItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;		
	}
	_toItemGroup = _fromItemGroup;
	refreshUI();
	elements._toItemGroup.requestFocus();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"B763D085-A59D-47DF-9912-A670690518CD"}
 */
function onDataChange_toItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;
		refreshUI();
	}
	return true;
}

/**
 * 
 * @param aSelectedItemGroups
 *
 *
 * @properties={typeid:24,uuid:"4471B821-E112-4F70-8609-E0B5AA711751"}
 */
function setSelectedItemGroups(aSelectedItemGroups) {
	_selectedItemGroupArray = aSelectedItemGroups;
	refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"E165763A-0753-4F23-A173-6275368E17C0"}
 */
function onAction_selectedItemGroup(event) {
	showSelectedItemGroupDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"249A0A7E-778E-4D10-81BD-D0AB25250B82"}
 */
function showSelectedItemGroupDialog() {
    forms.sysDialog_selected_item_groups._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_groups._sCallBackMethod = "setSelectedItemGroups";
    forms.sysDialog_selected_item_groups._aSelectedItemGroups = _selectedItemGroupArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_groups, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemGroups"), true, false, "sysDialogSelectedItemGroups", true);
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"90A5C68F-39CC-4AAE-A290-DC6B63EEF556"}
 */
function onIncludeAllItemGroupDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        _includeSelectedItemGroups = 0;
        elements._fromItemGroup.enabled = false;
        elements._toItemGroup.enabled = false;
        elements._selectedItemGroupMessage_label.text = null;
        elements._chkSelectedItemGroups
    }
    else {
    	 _includeSelectedItemGroups = 0;
         elements._fromItemGroup.enabled = true;
         elements._toItemGroup.enabled = true;
         elements._selectedItemGroupMessage_label.text = null;
    }
    
    _fromItemGroup = null;
    _toItemGroup = null;

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"352A884B-0002-4E92-A044-B7AB8AF8755F"}
 */
function onIncludeSelectedItemGroupToDataChange(oldValue, newValue, event) {
	if (newValue == 1) {
        _includeAllItemGroups = 0;
        _fromItemGroup = null;
        _toItemGroup = null;
        showSelectedItemGroupDialog();
        elements._selectedItemGroupMessage_label.enabled = true;
    }
    else {
        elements._selectedItemGroupMessage_label.text = null;
        _selectedItemGroupArray = [];
        _includeAllItemGroups = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"A1CB3E4B-025C-4DC8-8725-BECBDBA1FFD3"}
 */
function onIncludeSelectedItemClassDataChange(oldValue, newValue, event) {

    if (newValue == 1) {
        _includeAllItemClass = 0;
        _fromItemClass = null;
        _toItemClass = null;
        showSelectedItemClassDialog();
        elements._selectedItemClassMessage_label.enabled = true;
    }
    else {
        elements._selectedItemClassMessage_label.text = null;
        _selectedItemClassArray = [];
        _includeAllItemClass = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"BBABB390-CAE5-4722-9D1C-D1B3B986731E"}
 */
function onAction_selectedItemClasses(event) {
    showSelectedItemClassDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"CF61178E-C043-49E5-B302-DE79F611EBEE"}
 */
function showSelectedItemClassDialog() {
    forms.sysDialog_selected_item_classes._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_classes._sCallBackMethod = "setSelectedItemClasses";
    forms.sysDialog_selected_item_classes._aSelectedItemClasses = _selectedItemClassArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_classes, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemClasses"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItemClasses
 *
 *
 * @properties={typeid:24,uuid:"A18F724C-0AD2-4EF8-95FA-95A3D725E5B3"}
 */
function setSelectedItemClasses(aSelectedItemClasses) {
    _selectedItemClassArray = aSelectedItemClasses;
    refreshUI();
}

// Item

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 *
 * @return
 * @properties={typeid:24,uuid:"6050AC42-FB95-4CC5-990C-D734C55BFAF0"}
 */
function onIncludeSelectedItemsDataChange(oldValue, newValue, event) {
	
    if (newValue == 1) {
        _includeAllItems = 0;
        _fromItem = null;
        _toItem = null;
        showSelectedItemDialog();
    }
    else {
        elements._selectedItemMessage_label.text = null;
        _selectedItemArray = [];
        _includeAllItems = 1;
    }

    refreshUI();
    return true;
}

/**
 *
 * @properties={typeid:24,uuid:"E78D78AA-D13E-4605-824D-84CC1153CDFE"}
 */
function showSelectedItemDialog() {
    forms.sysDialog_selected_items._sCallBackForm = controller.getName();
    forms.sysDialog_selected_items._sCallBackMethod = "setSelectedItems";
    forms.sysDialog_selected_items._aSelectedItems = _selectedItemArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_items, -1, -1, 695, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItems"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItems
 *
 *
 * @properties={typeid:24,uuid:"BE59486C-63B0-4047-817F-4596B33AD812"}
 */
function setSelectedItems(aSelectedItems) {
    _selectedItemArray = aSelectedItems;
    refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"96DCD314-AD91-4851-8C4F-FD8F68237704"}
 */
function onAction_selectedItems(event) {
    showSelectedItemDialog();
}

/**
 * @properties={typeid:24,uuid:"6BEE0C3C-D941-46B7-BF65-E045D8BDE4F9"}
 */
function load_vl_DivisionshasWarehouses() {

	_aDivsReturn = [];
	_aDivsDisplay = [];
	
	fromDiv = "";

	var iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} ***/
		dsData;

	oSQL.sql = "SELECT DISTINCT CONCAT(div.div_code,': ',div.div_name) AS div , div.div_id \
			    FROM  sys_division AS div \
			    INNER JOIN in_warehouse AS whse ON whse.div_id = div.div_id \
			    WHERE div.org_id = ? \
			    AND div.div_id IN (SELECT div_id FROM sys_employee_div WHERE empl_id = ?) \
			    ORDER BY CONCAT(div.div_code,': ',div.div_name) ASC ";

	oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
	dsData = globals["avUtilities_sqlDataset"](oSQL);

	iMax = dsData.getMaxRowIndex();
	for (var i = 1; i <= iMax; i++) {
		_aDivsDisplay.push(dsData.getValue(i, 1));
		_aDivsReturn.push(dsData.getValue(i, 2));
	}

	application.setValueListItems("vl_DivisionshasWarehouses", _aDivsDisplay, _aDivsReturn);
}

/**
 *
 * @param sDivType
 *
 * @properties={typeid:24,uuid:"46A923DB-A38F-4EC0-96D7-17C9294336CD"}
 */
function loadPlantsForDiv(sDivType) {

	var aFromDiv = [];
	var sFromDiv = "";

	_aPlantsReturn = [];
	_aPlantsDisplay = [];

	fromPlant = "";
	fromWhse = "";
	
	if (!fromDiv) {
		setDefaultDivisionFilter();
	}
		aFromDiv = fromDiv.split('\n');

	for (var i = 0; i <= aFromDiv.length - 1; i++) {
		if (i != 0) {
			sFromDiv += ',';
		}
		sFromDiv += "'" + aFromDiv[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";

		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";
		
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	}
	iMax = dsData.getMaxRowIndex();
	
	if (iMax == 0) {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
			INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
			WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
			ORDER BY div.div_code ASC, plant.plant_code ASC";
	
			oSQL.args = [globals["org_id"]];
			dsData = globals["avUtilities_sqlDataset"](oSQL);
			iMax = dsData.getMaxRowIndex();
	}
	
	for (i = 1; i <= iMax; i++) {
		_aPlantsDisplay.push(dsData.getValue(i, 1));
		_aPlantsReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromPlant =  dsData.getValue(i, 2);
		}
	}
		
	application.setValueListItems("vl_Plants_rpt_dlg_base_on_div", _aPlantsDisplay, _aPlantsReturn);
}

/**
 * 
 * @param sPlantType
 *
 * @properties={typeid:24,uuid:"172C8189-0CE4-4D63-B085-42570526FDF5"}
 */
function loadWarehousesForPlant(sPlantType) {

	var aFromPlant = [];
	var sFromPlant = "";

	_aWhsesReturn = [];
	_aWhsesDisplay = [];

	
	aFromPlant = fromPlant.split('\n');

	for (var i = 0; i <= aFromPlant.length - 1; i++) {
		if (i != 0) {
			sFromPlant += ',';
		}
		sFromPlant += "'" + aFromPlant[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

	oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, div.div_id, plant.plant_id \
				 FROM in_warehouse AS whse \
					INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
					INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
				 WHERE div.org_id = ? \
					AND plant.plant_id IN ( " + sFromPlant + " ) \
				 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
				 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";	
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
			oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, plant.plant_id \
						 FROM in_warehouse AS whse \
							INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
							INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
						 WHERE div.org_id = ? \
							AND plant.plant_id IN ( " + sFromPlant + " ) \
						 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? ) \
						 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
	}
	iMax = dsData.getMaxRowIndex();
	for (i = 1; i <= iMax; i++) {
		_aWhsesDisplay.push(dsData.getValue(i, 1));
		_aWhsesReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromWhse =  dsData.getValue(i, 2);
		}
		else if (i>1 && _onSelectAllWarehouses == 1) {
			fromWhse += "\n";
			fromWhse +=  dsData.getValue(i, 2);
			 
		}
		
	}
		
	application.setValueListItems("vl_Warehouses_rpt_dlg_base_on_plant", _aWhsesDisplay, _aWhsesReturn);
	
}

/**
 * @properties={typeid:24,uuid:"FB6649D0-1C0B-4FB2-9649-11D493583796"}
 */
function setDefaultDivisionFilter() {
    
    elements.fromDiv.readOnly = false;
    
	if (utils.hasRecords(_to_sys_division$org)) {
		var rDivision = _to_sys_division$org.getRecord(1);
		fromDiv = rDivision.div_id;
	}
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"0F7D2D59-3AA7-4785-8A9C-2DEAD6E344C1"}
 */
function onDataChange_fromDiv (oldValue, newValue, event) {
	loadPlantsForDiv('From');
	onDataChange_fromPlant (oldValue, newValue, event);

}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"10D321B8-E097-4128-BE83-5D38F2E7D155"}
 */
function onDataChange_fromPlant (oldValue, newValue, event) {
	fromWhse = "";
	loadWarehousesForPlant('From');
}

/**
 * @properties={typeid:24,uuid:"2EBC7E16-987E-4B58-AFC0-94251385B6E6"}
 */
function showSelectedParentCustomerDialog() {
    forms.sysDialog_selected_parent_customers._sCallBackForm = controller.getName();
    forms.sysDialog_selected_parent_customers._sCallBackMethod = "setSelectedParentCustomers";
    forms.sysDialog_selected_parent_customers._aSelectedCustomers = _selectedParentCustomerArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_parent_customers, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedParentCustomers"), true, false, "sysDialogSelectedParentCustomer", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6A226495-1231-4E2A-B524-DC7D40E416CA"}
 */
function onShowFromParentCustomerLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'cust_code', 'Customers', 'afterFromParentCustomerLookup', 'lookupFilter_Customers', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} _fs
 * @return {JSFoundSet<db:/avanti/in_item_supplier>}
 *
 *
 *
 * @properties={typeid:24,uuid:"38431CD9-D930-451F-AC07-9A23D66D8641"}
 */
function lookupFilter_Customers(_fs)
{
	//_fs.addFoundSetFilterParam('cust_parent_cust_id','!=',null,'LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"DB4081BF-A893-467F-8370-6177951601D2"}
 */
function afterToParentCustomerLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"12305ADF-20B7-4407-B9E3-1555FBDF3EED"}
 */
function afterFromParentCustomerLookup(record) {
	if (record) fromParentCustomer = record.cust_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"918C32C8-7B6E-4AD1-B435-05495B070949"}
 */
function onFromParentCustomerDataChange(oldValue, newValue, event) {
	if (!toParentCustomer) {
		toParentCustomer = fromParentCustomer;
	} else if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.dateRangeException', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"CF57BB37-31FC-4EC9-8C68-************"}
 */
function onToParentCustomerDataChange(oldValue, newValue, event) {
	if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.dateRangeException', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"534FBA34-E367-4AEB-9374-351423C52DAA"}
 */
function onIncludeAllParentCustomerDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        elements._selectedParentCustomerMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C8D08E14-E525-437A-AEF0-69B1C4F551ED"}
 */
function onIncludeSelectedParentCustomerDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedParentCustomersViewOption = 0;
	
    if (newValue == 1) {
        includeAllParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        showSelectedParentCustomerDialog();
    }
    else if (newValue == 0) {
    	includeAllParentCustomers = 1;
        fromParentCustomer = null;
        toParentCustomer = null;
        _selectedParentCustomerArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedParentCustomers
 * public
 *
 * @properties={typeid:24,uuid:"81BE8F72-BDDC-43E0-ADC7-E41606016A85"}
 */
function setSelectedParentCustomers(aSelectedParentCustomers) {
    _selectedParentCustomerArray = aSelectedParentCustomers;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"83AA376D-4E3A-4216-9FD6-889EF03310B4"}
 */
function onAction_selectedParentCustomer(event) {
    showSelectedParentCustomerDialog();
}

/// FOR CUSTOMER PARTS ONLY

/**
 * 
 *  *
 * @properties={typeid:24,uuid:"302CB392-392A-4651-BC92-B22DB1EB43E6"}
 */
function showSelectedCustomerPartsDialog() {
    forms.sysDialog_selected_customers_parts._sCallBackForm = controller.getName();
    forms.sysDialog_selected_customers_parts._sCallBackMethod = "setSelectedCustomerParts";
    forms.sysDialog_selected_customers_parts._aSelectedCustomerParts = _selectedCustomerPartsArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_customers_parts, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.IncludeSelectedCustomerPartNo"), true, false, "sysDialogSelectedCustomerParts", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 * 
 *
 * @properties={typeid:24,uuid:"B2E83CA3-C881-42AC-828D-E809BC4248D4"}
 */
function onShowFromCustomerPartsLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'item_cust_part_number', 'Items', 'afterFromCustomerPartsLookup', 'lookupFilter_CustomerParts', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item>} _fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"D6989DA2-E22A-48C1-9F1E-7AEA385C312E"}
 */
function lookupFilter_CustomerParts(_fs)
{
	_fs.addFoundSetFilterParam('item_cust_part_number','!=',null,'LookupFilter')
	_fs.addFoundSetFilterParam('item_cust_part_number','!=','','LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"D3899B37-783F-4426-8683-7219E25DBC53"}
 */
function afterToCustomerPartsLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"E25E0E53-8FB1-4E6D-812A-6C924E2A111A"}
 */
function afterFromCustomerPartsLookup(record) {
	if (record) fromCustomerParts = record.item_cust_part_number;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"4661953A-E94D-48BD-B387-D0B55BA5D9AF"}
 */
function onFromCustomerPartsDataChange(oldValue, newValue, event) {
	if (!toCustomerParts) {
		toCustomerParts = fromCustomerParts;
	} else if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"82C70361-549E-49EF-8F26-198F87933095"}
 */
function onToCustomerPartsDataChange(oldValue, newValue, event) {
	if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"8F56BB6A-519C-4544-8229-370ED3E7A0FC"}
 */
function onIncludeAllCustomerPartsDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        elements._selectedCustomerPartsMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"A983C979-FBFC-4B4A-BFFE-EAEBBFC7E1AA"}
 */
function onIncludeSelectedCustomerPartsDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedCustomerPartsViewOption = 0;
	
    if (newValue == 1) {
        includeAllCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        showSelectedCustomerPartsDialog();
    }
    else if (newValue == 0) {
    	includeAllCustomerParts = 1;
        fromCustomerParts = null;
        toCustomerParts = null;
        _selectedCustomerPartsArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedCustomerParts
 * public
 
 *
 * @properties={typeid:24,uuid:"49321F00-7097-432E-8A3A-E92312ECA5C0"}
 */
function setSelectedCustomerParts(aSelectedCustomerParts) {
    _selectedCustomerPartsArray = aSelectedCustomerParts;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"87EEB52A-DAC5-4785-A7E8-A76071B4AE11"}
 */
function onAction_selectedCustomerParts(event) {
    showSelectedParentCustomerDialog();
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"8F8CB772-A56C-451F-B880-77602211B236"}
 */
function onAction_showFromSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	fromCustomerParts = rItem.item_cust_part_number;
	
	if (fromCustomerParts < toCustomerParts) {
		fromCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"9AE30288-FC83-48DE-A36B-C7FB89E6463D"}
 */
function onAction_showFromSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	fromParentCustomer = rCustomer.cust_id;
	fromParentCustomerCode = rCustomer.cust_code;
	
	if (fromParentCustomerCode < toParentCustomerCode) {
		fromParentCustomer = null;
		fromParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"16B86398-7AE1-49C7-A18F-CC76803C7166"}
 */
function onAction_showToSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	toCustomerParts = rItem.item_cust_part_number;
	
	if (toCustomerParts < fromCustomerParts) {
		toCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * 
 *
 * @properties={typeid:24,uuid:"390155F6-E8FB-4E52-8D93-8C1DB88798AE"}
 */
function onAction_showToSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	toParentCustomer = rCustomer.cust_id;
	toParentCustomerCode = rCustomer.cust_code;
	
	if (toParentCustomerCode < fromParentCustomerCode) {
		 toParentCustomer = null;
		 toParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"A79A5963-8F6F-4A56-8057-DE43FD338417"}
 */
function onAction_SelectAllWarehouses(event) {
	if (_onSelectAllWarehouses == 1) {
		elements.fromWhse.enabled = false;
  	    loadWarehousesForPlant('From');
	}
	else {
		 elements.fromWhse.enabled = true;
		 loadWarehousesForPlant('From');
	}
}
