/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"005679FE-08EC-4D3C-9967-5921D8FDC193"}
 */
var lang = '';

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"FC497E40-86F2-4E1A-B853-40B7D53C0431",variableType:93}
 */
var vFirstLoginAttempt = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7AACB683-BDAC-4E4B-B2A8-9FE195326646"}
 */
var vFramework_db = "svy_framework";

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"D267E971-099D-4824-9F4F-340BAB0E4AB0",variableType:93}
 */
var vLastLoginAttempt = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4E2E51EA-C982-457D-9E7B-F6751A09C17E"}
 */
var vOrganization = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2A3A8503-29E5-4ACC-9F0E-6F04414981F9"}
 */
var vOwner = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"64F0246C-7B6D-4456-9BDF-DE8493B342FA"}
 */
var vPassword = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0CFA1100-9A85-4326-A145-EBBB390E2804"}
 */
var vUser_db = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C4EA0620-B89F-42FC-915C-39B324F15594",variableType:4}
 */
var vUser_id = null;

/**
 * @properties={typeid:35,uuid:"792462FC-53A5-4149-A042-A0EBBDF9FC1D",variableType:-4}
 */
var vOwner_id = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F5DF8054-72FD-4007-A1B4-68932970B087"}
 */
var vUsername = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7429EA03-5753-453A-9DE1-2B3B3FCEA6D2"}
 */
var sShutdownJobName = '';

/**
 *	to exit the login screen
 *
 * <AUTHOR> Aleman
 * @since 2008-05-04
 * 
 * @return
 * @properties={typeid:24,uuid:"EF07B993-FC7C-4F87-9788-16E5BF9C5CCE"}
 */
function exit()
{
	plugins.clientmanager.shutDownClient(security.getClientID());
}

/**
 * @return
 * @properties={typeid:24,uuid:"A604FC5D-7985-4431-A990-A3D15EC63B2C"}
 */
function getOwnerID(){
	/*** @type {JSDataSet} */
	var dsData = databaseManager.getDataSetByQuery('svy_framework', 'select owner_id from sec_owner where name = ?', [vOwner], 1);
	if (dsData && dsData.getMaxRowIndex() > 0) {
/** @type {Array} */
		var taColVals = dsData.getColumnAsArray(1);
		return taColVals[0];
	}
	
	return null;
}

/**
 * @return
 * @properties={typeid:24,uuid:"5EBD4AFF-D241-4FAB-BB1D-F6F1723FC30D"}
 */
function getUserID(){
	/*** @type {JSDataSet} */
	var dsData = databaseManager.getDataSetByQuery('svy_framework', 'select user_id from sec_user where user_name = ?', [vUsername], 1);
	if (dsData && dsData.getMaxRowIndex() > 0) {
		/** @type {Array} */
		var taColVals = dsData.getColumnAsArray(1);
		return taColVals[0];
	}
	
	return null;
}

/**
 *	Method to let the user login, required is the group 'users' this method works with the sec_ tables
 * @param {{owner_id, user_id, username, owner_name}} [oUserOwner] Object that represents the login is from Single Sign-On/SAML 2.0
 * <AUTHOR> Aleman
 * @since 2008-05-04
 * 
 * @properties={typeid:24,uuid:"83BD1830-8ED5-43F7-8278-6EB165DE4211"}
 */
function login(oUserOwner)
{	
	//check if we should check the hash
	var _validated = security.authenticate('svy_sec_authenticate', 'svy_sec_validateHash',[{owner:vOwner, framework_db:vFramework_db}])

	if (!_validated) {
		plugins.dialogs.showWarningDialog("Can't login","Somebody messed with the security data. Logging in is not possible. Please contact the administrator.","OK");
		if (application.isInDeveloper()) {
			security.authenticate('svy_sec_authenticate', 'svy_sec_recalculateHash', [{owner:vOwner, framework_db:vFramework_db}]);
			plugins.dialogs.showWarningDialog("", "Developer: Hash recalculated, login again.", "OK");
		}
		return;
	}
	
	//check if user name and password are entered
	elements.btnLogin.requestFocus();
	if(globals.avLogin_isSSO === true) {	
		if( !oUserOwner && ((!vOrganization) || (!vOwner) || (!vUsername)) )
		{
			elements.error.text = i18n.getI18NMessage('svy.fr.dlg.username_password_entered');
			return;
		}	
	} else {
		if(!oUserOwner && ((!vUsername) || (!vPassword) || (!vOwner)))
		{
			elements.error.text = i18n.getI18NMessage('svy.fr.dlg.username_password_entered');
	
			return;
		}
	}
	
	if (!vFirstLoginAttempt) {
		vFirstLoginAttempt = new Date();
	}	
	
	/** @type {{owner_id:String,user_id:Number,error:String, success:Number}} */
	var _return = new Object();
	// Call authentication module/method, authentication is done on server not on the client.
	var _authObj = new Object();
	if (globals.avLogin_isSSO === true && vOrganization && vOwner_id && vOwner && vUsername){
		_return.success = 1;
		_return.owner_id = vOwner_id;
		_return.user_id = vUser_id;
		_authObj.username = vUsername;
	} else if(globals.avLogin_isSSO && oUserOwner.user_id && oUserOwner.owner_id && oUserOwner.username && oUserOwner.owner_name) {
		_return.owner_id = oUserOwner.owner_id;
		_return.user_id = oUserOwner.user_id;
		_return.success = 1;
		_authObj.username = oUserOwner.username;
		vUsername = oUserOwner.username;
		
		vOwner = oUserOwner.owner_name;
		vOwner_id = oUserOwner.owner_id;
		vUser_id = oUserOwner.user_id;
		if( globals.avLogin_isSSO === true && oUserOwner.org_id ) { 
			vOrganization = oUserOwner.org_id;
		}
	} else {
		_authObj.username = vUsername;
		_authObj.password = vPassword;
		_authObj.owner = vOwner;
		_authObj.firstLoginAttempt = vFirstLoginAttempt;
		_authObj.lastLoginAttempt = vLastLoginAttempt;
		_authObj.framework_db = vFramework_db;
		_return = security.authenticate('svy_sec_authenticate', 'svy_sec_checkUserPassword',[_authObj]);

		// sometimes when using a customer db in servoy authenticate returns null, even tho user/pwd is correct. dont know why
		// add this code so developer can set _return to success and get in.
		var bDebug = application.isInDeveloper() && false;
		
		if (bDebug) {
			_return = {};
			_return.success = true;
			_return.owner_id = 'C603971A-235B-40CC-A8BA-40A3FEF87B1E';
			_return.user_id = 2337;
		}
		
		
		// sl-19129 - SRF had 1 user that could login to Brooke Graphics LLC because it had recs in sec_user_org for both srf and brooke.
		// i couldnt find the source of the bad data, put this check in to make sure we dont allow user to login to owner it doesnt belong to.
		// also add code to delete bad recs in sec_user_org and sec_user_in_group.
		// only run this for web client, we get a security error when running sql in smart client. only needed for web cleint anyways.
		if (_return.success && application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {
			var sUserOwnerID = getUserOwnerID(_return.user_id);

			// user owner doesnt match owner used to login - dont allow login 
			if (sUserOwnerID != _return.owner_id) {
				_return.success = false;
			}
			
			// detectAndDeleteBadUserData regardless of whether we are logging into correct owner. even if we are in right owner, if there is a problem
			// we will see the wrong org in employee user details.
			detectAndDeleteBadUserData(_return.user_id, sUserOwnerID);	
		}
	}
	
    if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT && _return.user_id) {
        var employeeActiveStatus;
        var dsData = databaseManager.getDataSetByQuery('avanti', 'SELECT empl_active FROM sys_employee WHERE user_id = ?', [_return.user_id.toString()], 1);
        if (dsData && dsData.getMaxRowIndex() == 1 && dsData.getMaxColumnIndex() == 1) {
            employeeActiveStatus = dsData.getValue(1, 1);
        }
        if (employeeActiveStatus === 0) {
            _return.success = false;
        }
    }

	if(_return.success)
	{		
		// set user id
		globals.svy_sec_lgn_user_id = _return.user_id;
		
		//set owner id
		globals.svy_sec_lgn_owner_id = _return.owner_id;
		

		//get organizations, if there are multiple organizations for this user he has to choose his organization
		/** @type {JSDataSet} */
		var _dat_org =  security.authenticate('svy_sec_authenticate', 'svy_sec_getOrganizations', [_return.user_id, _return.owner_id, vFramework_db]);
		if(_dat_org.getMaxRowIndex() == 1) //only one organization
		{
			// login
			vUser_id =  _return.user_id;
			vOrganization = _dat_org.getValue(1,2);
			loginWithOrganization();
		}
		else
		{
			if(vUser_id)
			{	
				if(globals.avLogin_isSSO === true) {				
					if(vOrganization !== '' && vOrganization !== null ) {
						var user_allowed_by_organization = false;
						var org_id;
						for (var i = 1; i <= _dat_org.getMaxRowIndex(); i++)
						{
							org_id = _dat_org.getValue(i, 2);
							if(org_id === vOrganization) {
								user_allowed_by_organization = true;
							}
						}
						if(user_allowed_by_organization) {
							// login the organization
							loginWithOrganization();
						}
					} else {
						// using sso and connected to multiple org's and not passed in by url				
						vUser_id = _return.user_id
						application.setValueListItems('svy_sec_lgn_organizations',_dat_org,true);
						elements.lbl_organization.visible = true;
						elements.fld_organization.visible = true;
						
						//enter the organization id
						if(application.getUserProperty(application.getSolutionName() +'.organization'))
						{
							vOrganization = application.getUserProperty(application.getSolutionName() +'.organization')
							elements.btnLogin.requestFocus();
						}
						else
						{
							elements.fld_organization.requestFocus();
						}
					}
				} else {
					// login the organization
					loginWithOrganization();
				}
				return;
			}
			else
			{
				//set organization valuelist
				vUser_id = _return.user_id
				application.setValueListItems('svy_sec_lgn_organizations',_dat_org,true);
				elements.lbl_organization.visible = true;
				elements.fld_organization.visible = true;
			
				//enter the organization id
				if(application.getUserProperty(application.getSolutionName() +'.organization'))
				{
					vOrganization = application.getUserProperty(application.getSolutionName() +'.organization')
					elements.btnLogin.requestFocus();
				}
				else
				{
					elements.fld_organization.requestFocus();
				}
				return;
			}
		}
		application.setUserProperty(application.getSolutionName() +'.username',vUsername);
		application.setUserProperty(application.getSolutionName() +'.ownername',vOwner);
//		application.setUserProperty(application.getSolutionName() +'.password',vPassword)
//		vUsername = vPassword = elements.error.text = null;
		
		
		//for keeping track of logedin users per owner
		application.addClientInfo(_return.owner_id);
		
		//user is already choosing organization	
	}	
	else	
	{
		if(_return.error)
		{
			elements.error.text = i18n.getI18NMessage(_return.error);
		}
		else
		{
			elements.error.text = i18n.getI18NMessage('svy.fr.dlg.loginfailed');
		}
	}
	return;
}

/**
 * @private 
 * 
 * @param {Number} iUserID
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"7E1EC65D-6CC9-467D-A877-7A62E5A4ACA2"}
 */
function getUserOwnerID(iUserID) {
	var sOwnerID = null;
	
	if (iUserID != null) {
		/**@type {JSDataSet} ***/
        var dsData = databaseManager.getDataSetByQuery("svy_framework", "SELECT owner_id FROM sec_user WHERE user_id = ?", [iUserID.toString()], -1);
		
		if (dsData && dsData.getMaxRowIndex() == 1 && dsData.getMaxColumnIndex() == 1) {
			sOwnerID = dsData.getValue(1, 1);
		}
	}
	
	return sOwnerID;
}

/**
 * @private 
 * 
 * @param {Number} iUserID
 * @param {String} sOwnerID
 *
 * @properties={typeid:24,uuid:"F76CD416-1437-4A8B-91F9-5B0B8A0D4045"}
 */
function detectAndDeleteBadUserData(iUserID, sOwnerID) {
	if (iUserID != null && sOwnerID != null) {
		// BAD SEC_USER_ORG RECS - BELONG TO WRONG OWNER
		var sSQL = "SELECT uo.user_org_id, o.name \
					FROM sec_user_org uo \
					INNER JOIN sec_organization o ON uo.organization_id = o.organization_id \
					WHERE \
						uo.user_id = ? \
						AND o.owner_id != ?";
		var aArgs = [iUserID.toString(), sOwnerID];

		/**@type {JSDataSet} ***/
        var dsData = databaseManager.getDataSetByQuery("svy_framework", sSQL, aArgs, -1);
		
	    if (dsData) {
	        for (var i = 1; i <= dsData.getMaxRowIndex(); i++) {
	        	/**@type {Number} */
	            var iUserOrgID = dsData.getValue(i, 1);
	            var sOwnerName = dsData.getValue(i, 2);
	            
	    		plugins.rawSQL.executeSQL("svy_framework", "DELETE FROM sec_user_org WHERE user_org_id = ?", [iUserOrgID.toString()]);
	    		
	    		// have to delete sec_user_in_group recs connected to deleted sec_user_org too
	    		plugins.rawSQL.executeSQL("svy_framework", "DELETE FROM sec_user_in_group WHERE user_org_id = ?", [iUserOrgID.toString()]);
	            
	    	    // log it to dev log too
	        	var sLogInfo = "deleted bad sec_user_org rec for user_id: " + iUserID + ". it was linked to incorrect owner: " + sOwnerName;
	        	var sLogSQL = "insert into dev_log (dev_log_id, dev_log_ticket, dev_log_timestamp, dev_log_info) \
	        				   values ((select max(dev_log_id) + 1 from dev_log),  'SL-19129', GETDATE(), '" + sLogInfo + "')";
	        	
	       		plugins.rawSQL.executeSQL("avanti", sLogSQL, []);
	        }
	    }
	}
}

/**
 *	Gets the username, sets the right focus, set the progress bar to 0
 *
 * <AUTHOR> Aleman
 * @since 2008-05-04
 * 
 * 
 * @properties={typeid:24,uuid:"EF2338B1-2D97-463B-9C84-10360D923B27"}
 */
function onShow()
{
	//Hardcode language until we address number formatting and complete full translations
	i18n.setLocale('en', 'US');
	
	if (!vOwner) vOwner = application.getUserProperty(application.getSolutionName() +'.ownername'); 
	if (!vUsername) vUsername = application.getUserProperty(application.getSolutionName() +'.username');
	
	if (vUsername && vPassword && vOwner) {
		
		elements.btnLogin.requestFocus()
		
	} else if (vUsername && vPassword) {
		
		elements.fld_owner.requestFocus();
		
	} else if (vUsername) {
		
		elements.fld_passWord.requestFocus();
		
	} else {
		
		elements.fld_userName.requestFocus();
		
	}
	
	addShutdownByTimeoutJob();
	
}

/**
 * Creates a job which going to press exit if user is not logging in for 30 minutes
 * @properties={typeid:24,uuid:"3AD97D42-D768-4BC6-80C6-17FDF1ED0921"}
 */
function addShutdownByTimeoutJob() {
	var exitTimeoutInMinutes = 1; // 1 minute timeout

	// Set up idle detection with organization-specific timeout if needed
	//	 var nOrgSessionTimeout = globals.getSessionTimeoutIfThereIsOnlyOneOrganization(exitTimeoutInMinutes);
	//	    if (nOrgSessionTimeout) {
	//	        exitTimeoutInMinutes = nOrgSessionTimeout;
	//	    }


	plugins.svyIdle.onIdle(function() {
			application.output("Login screen idle timeout reached. Shutting down...");
			// Get the current client ID and shut it down
			var clientId = security.getClientID();
			if (clientId) {
				plugins.clientmanager.shutDownClient(clientId);
			}
		},
		null,
		null,
		null,
		null,
		exitTimeoutInMinutes * 60 * 1000, // Convert minutes to milliseconds
		false, // Don't keep tracking after shutdown
		false,
		false
	);

}

/**
 * On focus gained password, empty error message
 *
 * <AUTHOR> Aleman
 * @since 2008-05-04
 * 
 * @properties={typeid:24,uuid:"DB68A8D6-558C-4F71-A553-40938CA0C741"}
 */
function onFocusGainedPassword()
{
	elements.error.text = ''
}

/**
 * Set the progress bar not visible in webclient
 *
 * <AUTHOR> Aleman
 * @since 2008-05-04
 * @param {JSEvent} event
 * 
 * @properties={typeid:24,uuid:"FE19413E-0410-478B-9124-CFF36FAFDE90"}
 */
function onLoad(event)
{	
	globals.avQuery_param_org_id = '';
	
	//run onPreLogin-method when available
	if(globals['onPreLogin']) {
			globals['onPreLogin']();
	}
	
	var _solutionLoadedBefore = application.getUserProperty(application.getSolutionName() + '_loaded');
	application.setUserProperty(application.getSolutionName() + '_loaded','1');
	/** @type {String} */
	var _windowSize = security.authenticate('svy_sec_authenticate','svy_sec_getWindowSize', [vFramework_db]);
	var _forceWindowSize = security.authenticate('svy_sec_authenticate','svy_sec_getForcedWindowSize', [vFramework_db]);
	
	if((_forceWindowSize == 'true' || _solutionLoadedBefore != "1") && _windowSize) {
		/** @type {Array} */
		var _sizes = _windowSize.split(',');
		application.getWindow().setSize(_sizes[0]*1, _sizes[1]*1);
	}
	                                        
	//set autologin key in developer
	if(application.isInDeveloper()) {
		plugins.window.createShortcut('alt L',forms.svy_sec_login.autoLoginDeveloper, 'svy_sec_login');
	}
		
	//retrieve the owner_id and db-servernames from the deeplink - will not work in developer
	if(globals.svy_sec_l_startArg) {
		var _args = globals.svy_sec_l_startArg.split("|");
		
		var _owner_id = _args[0];
		vUser_db = _args[1];
		if (_args[2] != null) vFramework_db = _args[2];

		application.output('Owner Id' + _owner_id,LOGGINGLEVEL.INFO);
		
		var _owner = security.authenticate('svy_sec_authenticate','svy_sec_getOwnerName',[_owner_id, vFramework_db]);
		if(_owner) {
			vOwner = _owner;
		}
		application.output('Owner ' + _owner,LOGGINGLEVEL.INFO);
		if(vOwner) // if owner is known, don't allow selection
		{
			elements.fld_owner.enabled = false;
		}
	}
	if(globals.svy_sec_l_query_param) {
		var keys = Object.keys(globals.svy_sec_l_query_param);
		var index = keys.indexOf('org_id');
		if(index !== -1) {
			globals.avQuery_param_org_id = globals.svy_sec_l_query_param['org_id'];
		}
	}
	
	//hide organization fields
	elements.lbl_organization.visible = false;
	elements.fld_organization.visible = false;
	
	//remove toolbars
	application.setToolbarVisible('text',false);
	application.setToolbarVisible('edit',false);
	
	globals.avLogin_isSSO = false;
	if (application.getApplicationType() == APPLICATION_TYPES.NG_CLIENT) {

		var dsSSOEnabled = databaseManager.getDataSetByQuery('svy_framework',
			'select a.owner_id, a.name, a.saml_name_id from sec_owner as a where a.enable_saml_sso = 1', [], 100);
		if (dsSSOEnabled.getMaxRowIndex() > 0) {
			/** @type {Packages.org.apache.wicket.protocol.http.WebRequestCycle} */
			var oRequestCycle = Packages.org.apache.wicket.RequestCycle.get();

			var sNameID = dsSSOEnabled.getValue(1, 3);
			if (!sNameID) {
				application.output("Missing setup for Attribute Name to identify user in SAML payload. Check the System Setup - Organizations program.", LOGGINGLEVEL.ERROR);
			} else if (oRequestCycle) {
				var oHTTPRequest = oRequestCycle.getWebRequest().getHttpServletRequest();
				/** @type {String} */
				var sUsername = oHTTPRequest.getAttribute(sNameID);
				application.output('Payload User ID: ' + sUsername, LOGGINGLEVEL.INFO);
				if (sUsername) {
					if (sUsername.indexOf('@') > 0) {
						sUsername = sUsername.substring(0, sUsername.indexOf('@'));
					}
					var dsSSOUserExists = databaseManager.getDataSetByQuery('svy_framework',
						'select user_id, b.owner_id, a.name from sec_owner as a inner join sec_user as b on a.owner_id = b.owner_id and a.enable_saml_sso = 1 and user_name = ?',
						[sUsername], 1);

					if (dsSSOUserExists.getMaxRowIndex() == 1) {
						globals.avLogin_isSSO = true;
						var oUserOwner = new Object();
						oUserOwner.username = sUsername;
						oUserOwner.user_id = dsSSOUserExists.getValue(1, 1);
						oUserOwner.owner_id = dsSSOUserExists.getValue(1, 2);
						oUserOwner.owner_name = dsSSOUserExists.getValue(1, 3);
						if (globals.avQuery_param_org_id !== '') {
							oUserOwner.org_id = globals.avQuery_param_org_id;

							application.output('Org id' + oUserOwner.org_id, LOGGINGLEVEL.INFO);
						} else {

							elements.fld_owner.readOnly = true;

							elements.fld_userName.enabled = false;
							elements.fld_userName.readOnly = true;
							elements.fld_userName.bgcolor = "#D8D8D8"; // light_gray

							elements.fld_passWord.enabled = false;
							elements.fld_passWord.readOnly = true;
							elements.fld_passWord.fgcolor = "#D8D8D8"; // light_gray
							elements.fld_passWord.bgcolor = "#D8D8D8"; // light_gray

							elements.btnResetPassword.visible = false;
						}
						login(oUserOwner);
					} else if (dsSSOUserExists.getMaxRowIndex() > 1) {
						application.output("There are more than two users with that username!", LOGGINGLEVEL.ERROR);
					}
				} else {
					application.output("No username found in SAML Payload to match!", LOGGINGLEVEL.ERROR);
				}

			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"C1B4540E-C0B2-4D30-B0E9-F8391DE65200"}
 */
function autoLoginDeveloper() {
	if(application.isInDeveloper())
	{
//		vUsername = 'superuser'
//		vPassword = '12345'
		vUsername = 'sysadmin'
		vPassword = '12345'
		vOwner = 'Avanti Computer Systems'
		var oUserOwner = new Object();
		oUserOwner.username = vUsername;
		oUserOwner.user_id = null;
		oUserOwner.owner_id = null;
		oUserOwner.owner_name = vOwner;
		login(oUserOwner);
	}
	
}

/**
 * @param {Object} [_oldValue]
 * @param {Object} [_newValue]
 * @return
 * @properties={typeid:24,uuid:"24117749-D86F-46FC-9AFF-114073E6B954"}
 */
function loginWithOrganization(_oldValue, _newValue) {
			
	// save organization id
	application.setUserProperty(application.getSolutionName() +'.organization',vOrganization)
	
	// login
	var _user_org_id = security.authenticate('svy_sec_authenticate', 'svy_sec_login',[vUsername, vUser_id, vOrganization, vFramework_db])
	globals.svy_sec_lgn_organization_id = vOrganization
	if(_user_org_id && _user_org_id > 0) {
		globals.svy_sec_lgn_user_org_id = _user_org_id;
		return true;
	}
	
	return false;
}


/**
 * Handle focus lost event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C972404B-5445-4045-9A06-FEBD82140E9F"}
 */
function onFocusLostPassword (event) {
	
	if (vUsername && vPassword && vOwner) {
		
		login();
		
	}
}

/**
 * Open the avanti systems support site
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"8038AA3E-5331-4FE0-94D1-40B9637E5E3A"}
 */
function onAction_Logo(event) {
    application.showURL("https://support.avantisystems.com", "_blank");
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"A0C9C236-7E63-4E85-8946-FD56CDA32803"}
 */
function onAction_btnResetPassword (event) {
	globals.bResettingPassword = true;
	login();
}
