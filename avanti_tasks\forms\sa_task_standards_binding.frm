customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_task_standard",
extendsID:"DFD53413-00EA-4443-8EF6-2A4D18AEFBCD",
items:[
{
cssPosition:"273,-1,-1,341,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"341",
right:"-1",
top:"273",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.min",
visible:true
},
name:"lblSetupMins",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"04B29D36-C906-42AE-8F41-B2CBB7B57C39"
},
{
cssPosition:"223,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"223",
width:"140"
},
enabled:true,
labelFor:"taskstd_materials_needed",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.materialsNeeded",
visible:true
},
name:"component_28E9463E",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"072D273E-2920-4ECA-82C6-1537F915EDD6"
},
{
cssPosition:"472,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"472",
width:"75"
},
dataProviderID:"taskstd_mrkup",
editable:true,
enabled:true,
format:"0.00%",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.percent_tooltip",
visible:true
},
name:"fldMrkUp",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"0C2787AF-D67C-43E7-BDBC-DB76EEBD1140"
},
{
cssPosition:"248,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"248",
width:"60"
},
dataProviderID:"taskstd_trim_face",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTrimFace",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"10FECA7C-E9C6-4F68-AEEA-************"
},
{
cssPosition:"113,-1,-1,10,490,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"113",
width:"490"
},
enabled:true,
labelFor:"taskstd_machine_var_based_on",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.machineVariableBasedOn",
visible:true
},
name:"component_1E6276EB",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"124B1FA1-9D44-4AFB-B979-3B13F0578046"
},
{
cssPosition:"522,-1,-1,155,345,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"522",
width:"345"
},
dataProviderID:"jdftype_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EA854D03-A99A-46D5-B366-74F2E62A1987",
visible:true
},
name:"component_2CC3CD93",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"1394031D-CEE3-48C5-AFB1-932BB1DA4B89"
},
{
cssPosition:"422,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"422",
width:"140"
},
enabled:true,
labelFor:"taskstd_spine_glue",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.spineGlue",
visible:true
},
name:"component_5B31082D",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1A12E761-C1BA-440B-990E-6ED55555E8FD"
},
{
cssPosition:"248,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"248",
width:"140"
},
enabled:true,
labelFor:"taskstd_inline",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.hasInlineTasks",
visible:true
},
name:"component_DC562D1A",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1A9BE18E-AB0C-40FF-8168-B99A5AC10A80"
},
{
cssPosition:"39,-1,-1,500,145,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"500",
right:"-1",
top:"39",
width:"145"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.maxThickness",
visible:true
},
name:"lblMaxThickness",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1D9C224D-9588-4474-9F73-217B16A1F829"
},
{
cssPosition:"323,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"323",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.folio",
visible:true
},
name:"lblTrimFolio",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1E19E7D0-F24F-4FB6-91E9-055230F9E5B6"
},
{
cssPosition:"447,-1,-1,10,125,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"447",
width:"125"
},
enabled:true,
formIndex:14,
labelFor:"fldHelpers",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#helpers",
visible:true
},
name:"lblNrHelpers",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"27AB5165-3391-46E5-9D2B-FCBF54792CD2"
},
{
cssPosition:"138,-1,-1,550,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"550",
right:"-1",
top:"138",
width:"60"
},
dataProviderID:"taskstd_nr_pockets",
editable:true,
enabled:true,
onDataChangeMethodID:"5BABA1B2-6E2A-4C29-9B9F-D96DD95C18A7",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldPockets",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2816F321-5848-4C83-9BAB-0CEED1CDD36D"
},
{
cssPosition:"447,-1,-1,140,102,22",
formIndex:29,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"447",
width:"102"
},
dataProviderID:"taskstd_helpers",
editable:true,
enabled:true,
formIndex:29,
onDataChangeMethodID:null,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldNrHelpers",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2B4422F8-6454-4DEE-9D8E-6627EE060E8F"
},
{
cssPosition:"166,-1,-1,720,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"720",
right:"-1",
top:"166",
width:"161"
},
dataProviderID:"taskstd_flg_hand_feeder",
enabled:true,
onDataChangeMethodID:"5A117DBC-0B45-478D-A999-9C4F625A91B6",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.handFeeder",
visible:true
},
name:"fldAcc2",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"2D7A87B5-F163-42CC-A4D7-04B497A5AB1C"
},
{
cssPosition:"472,-1,-1,388,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"388",
right:"-1",
top:"472",
width:"116"
},
dataProviderID:"taskstd_custpricing",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"8A1143B3-056C-47EF-9DAC-CD94FEC115D4",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_A468E01C",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"2DFFF160-D318-4FE9-8F93-E465466A72F6"
},
{
cssPosition:"322,-1,-1,270,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"270",
right:"-1",
top:"322",
width:"140"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.knife3_setup",
visible:true
},
name:"lblKnife3",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2FE9E4EC-F468-4B08-8BE8-00FE1DF99E1F"
},
{
cssPosition:"373,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"373",
width:"140"
},
enabled:true,
labelFor:"taskstd_spine_thickness",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.spineThicknessCalc",
visible:true
},
name:"component_9D6A596C",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"32B1ADE6-9D55-43F5-95EF-35CABA740F35"
},
{
cssPosition:"38,-1,-1,155,345,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"38",
width:"345"
},
dataProviderID:"taskstd_bindery_type",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"BFD39CD7-A0F0-4386-97AF-A5356BB15989",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"5E651198-279D-4D67-B49D-8C2532B5AC44",
visible:true
},
name:"component_7514B57B",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"3E54F833-**************-E2D860BF04E3"
},
{
cssPosition:"373,-1,-1,155,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"373",
width:"116"
},
dataProviderID:"taskstd_spine_thickness_calc",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"432674BD-E588-45FC-807B-5EC59FDD2E80",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_spine_thickness",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"419CABC4-E302-4724-AF99-739F064B14FB"
},
{
cssPosition:"88,-1,-1,155,661,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"88",
width:"661"
},
dataProviderID:"taskstd_calc_type",
enabled:true,
inputType:"radio",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"E0BC5E00-F50B-4887-86B4-2A8BAFB85DEF",
visible:true
},
name:"taskstd_calc_type",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"41F182E3-D320-41D4-BA80-35A77D7A72A7"
},
{
cssPosition:"273,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"273",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.footTrim",
visible:true
},
name:"lblTrimFoot",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"48F1C1D4-72FB-48AC-9C82-1ECAEE4208CA"
},
{
cssPosition:"497,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"497",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.minimumChargeForTask",
visible:true
},
name:"lblMinCharge",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"50156B36-9C5D-421F-AA24-EFE64889504A"
},
{
cssPosition:"248,-1,-1,155,270,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"248",
width:"270"
},
dataProviderID:"taskstd_inline",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"4DD8F19B-EC3C-4032-9C4D-E2ECE31A6F77",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_inline",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"5689975A-B195-4524-918F-6F2B74E29C02"
},
{
cssPosition:"273,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"273",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.setupTimeStandard",
visible:true
},
name:"component_E461F30E",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5CDE7C01-808E-4536-9D93-A39B2E528143"
},
{
cssPosition:"298,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"298",
width:"60"
},
dataProviderID:"taskstd_trim_pull_lap",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTrimPullLap",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5EB2F7AC-5C54-4E1C-89B9-DC5FBFAAAB87"
},
{
cssPosition:"373,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"373",
width:"60"
},
dataProviderID:"taskstd_add_to_spine_thickness",
editable:true,
enabled:true,
format:"#.0000",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"taskstd_add_to_spine_thickness",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"737478D7-F4C0-45BA-BAA3-813C64C0F3D0"
},
{
cssPosition:"39,-1,-1,650,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"650",
right:"-1",
top:"39",
width:"60"
},
dataProviderID:"taskstd_max_thickness",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldMaxThickness",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"755F0B57-A722-441E-975F-ACA4765598CE"
},
{
cssPosition:"138,-1,-1,625,90,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"138",
width:"90"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.accessories",
visible:true
},
name:"lblAccessories",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7EFAE5F6-B185-40E7-BBAC-F3FEBAD84796"
},
{
cssPosition:"63,-1,-1,10,490,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"63",
width:"490"
},
enabled:true,
labelFor:"taskstd_calc_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.calculationQuantityVariable",
visible:true
},
name:"component_0581D5EE",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"81212F4B-869E-43FB-AF31-A081D4F01BD7"
},
{
cssPosition:"346,-1,-1,415,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"415",
right:"-1",
top:"346",
width:"60"
},
dataProviderID:"taskstd_5_knife_setup",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldKnife5Setup",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8333F6A0-30AF-4C3D-BA85-E8725F8603DB"
},
{
cssPosition:"422,-1,-1,155,106,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"422",
width:"106"
},
dataProviderID:"taskstd_spine_glue",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"78BD2C55-4347-4300-9CE5-DF6FE97BCF68",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_spine_glue",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"89556CE3-9CEB-49DC-B3D8-FAA358790A44"
},
{
cssPosition:"273,-1,-1,276,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"276",
right:"-1",
top:"273",
width:"60"
},
dataProviderID:"taskstd_setup_time",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTime",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"96C1F321-807F-4911-B05D-88CDF91AA269"
},
{
cssPosition:"198,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"198",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.grindOff",
visible:true
},
name:"lblGrindOff",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"982D79F4-42A4-4E80-AA85-D22DBB3FAB88"
},
{
height:555,
partType:5,
typeid:19,
uuid:"9C460E4A-2F04-4E3E-8372-EA2FE24E828D"
},
{
cssPosition:"273,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"273",
width:"60"
},
dataProviderID:"taskstd_trim_foot",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTrimFoot",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"9F0D8788-2D44-4198-A725-59F9A9F6EA78"
},
{
cssPosition:"38,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"38",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.binderyType",
visible:true
},
name:"component_790E50F1",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A37D5431-5D03-41D3-9DA9-D557A63FDF22"
},
{
cssPosition:"298,-1,-1,155,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"298",
width:"116"
},
dataProviderID:"taskstd_trimmer",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"36490467-8E0C-4D2B-AB7E-196FC2275A45",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_7C8B4566",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"A5FB6848-ADC7-4198-B86C-17C84044E591"
},
{
cssPosition:"198,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"198",
width:"60"
},
dataProviderID:"taskstd_grind_off",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldGrindOff",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A80B46A3-C4D4-4FC0-9F98-C19DDCAE7000"
},
{
cssPosition:"222,-1,-1,720,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"720",
right:"-1",
top:"222",
width:"161"
},
dataProviderID:"taskstd_flg_face_trim",
enabled:true,
onDataChangeMethodID:"960691E8-0A78-4CD9-A6A2-E5E0FA508ADB",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.faceTrim",
visible:true
},
name:"fldAcc4",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"AD138FB5-20B0-4A4A-8776-60C89A9CB982"
},
{
cssPosition:"138,-1,-1,155,161,85",
json:{
cssPosition:{
bottom:"-1",
height:"85",
left:"155",
right:"-1",
top:"138",
width:"161"
},
dataProviderID:"taskstd_machine_var_based_on",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"9D88D92A-43C6-4256-8B92-C2CEBD6EEF67",
styleClass:"choicegroup_bts vertical",
tabSeq:0,
valuelistID:"E7EB02C3-4A7A-424D-B4CA-E598E692D92E",
visible:true
},
name:"taskstd_machine_var_based_on",
styleClass:"choicegroup_bts vertical",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"AE877091-B1D0-4DB0-BE63-1E3CC47673C2"
},
{
cssPosition:"298,-1,-1,415,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"415",
right:"-1",
top:"298",
width:"60"
},
dataProviderID:"taskstd_1_knife_setup",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldKnife1Setup",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B6EA20F4-1E3B-4D40-BFE0-40E37132DD31"
},
{
cssPosition:"248,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"248",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.faceTrim",
visible:true
},
name:"lblTrimFace",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BC975469-4942-43D1-A60A-DA0D71060BB5"
},
{
cssPosition:"322,-1,-1,415,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"415",
right:"-1",
top:"322",
width:"60"
},
dataProviderID:"taskstd_3_knife_setup",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldKnife3Setup",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C4DFD50E-5715-42CB-AC45-1D0E13AD4418"
},
{
cssPosition:"10,-1,-1,155,345,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"10",
width:"345"
},
dataProviderID:"systaskfunc_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"0CFDFA91-30A8-45C3-8721-498AC8B09F74",
visible:true
},
name:"fldTaskFunctionalArea",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"********-56EB-4860-95B3-0A0B44F826FB"
},
{
cssPosition:"346,-1,-1,270,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"270",
right:"-1",
top:"346",
width:"140"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.knife5_setup",
visible:true
},
name:"lblKnife5",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C8B8A217-CE5D-426C-9BFF-B751EEE46BBE"
},
{
cssPosition:"373,-1,-1,270,261,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"270",
right:"-1",
top:"373",
width:"261"
},
enabled:true,
labelFor:"taskstd_std_spine_thickness",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.spineThicknessStandardAmount",
visible:true
},
name:"component_FB13D18E",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CA3F9A66-541F-4B99-8940-E0DDDD5E1E59"
},
{
cssPosition:"447,-1,-1,665,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"665",
right:"-1",
top:"447",
width:"116"
},
dataProviderID:"taskstd_cost_per_piece",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"3AE88BEF-70DA-4616-8E87-6C58DA3FE18B",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_516E2FC1",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"CEB6A9E1-3CEC-4232-8EF2-EA2D7735CD16"
},
{
cssPosition:"522,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"522",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType",
visible:true
},
name:"component_B595D3AA",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CF2F57F7-F54F-4002-AEAD-5E7C7AFD7403"
},
{
cssPosition:"138,-1,-1,317,173,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"317",
right:"-1",
top:"138",
width:"173"
},
enabled:true,
labelFor:"fldLift1",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.inches/Lift",
visible:true
},
name:"lblLift1",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D0864BD2-6BDD-4101-BAAA-AA3AA9A8F2A1"
},
{
cssPosition:"397,-1,-1,270,261,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"270",
right:"-1",
top:"397",
width:"261"
},
enabled:true,
labelFor:"taskstd_spine_rounding_factor",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.spineThicknessRoundingFactor",
visible:true
},
name:"component_FC9DCFA5",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D2F81BD4-**************-F83C436871EB"
},
{
cssPosition:"497,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"497",
width:"75"
},
dataProviderID:"taskstd_min_charge",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldMinCharge",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D4A8D394-03A9-4683-96C9-98474908AF90"
},
{
cssPosition:"323,-1,-1,625,60,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"323",
width:"60"
},
dataProviderID:"taskstd_trim_folio",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"3E667CAC-0761-4FA8-8F4E-6F1992BB45C2",
visible:true
},
name:"fldTrimFolio",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D50F1FB6-93C6-4BF6-A2F4-3045619ACE28"
},
{
cssPosition:"373,-1,-1,536,89,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"536",
right:"-1",
top:"373",
width:"89"
},
dataProviderID:"taskstd_std_spine_thickness",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"432674BD-E588-45FC-807B-5EC59FDD2E80",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_std_spine_thickness",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"D57FE57F-2FBC-4000-832D-0718036963AC"
},
{
cssPosition:"298,-1,-1,270,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"270",
right:"-1",
top:"298",
width:"140"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.knife1_setup",
visible:true
},
name:"lblKnife1",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DDF511AF-BB87-42D5-880D-C7500B0E88EE"
},
{
cssPosition:"163,-1,-1,350,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"350",
right:"-1",
top:"163",
width:"140"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.liftsHour",
visible:true
},
name:"lblLift2",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DE20F5FC-6DA3-425B-9FC3-8A988A4DDA2F"
},
{
cssPosition:"273,-1,-1,155,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"273",
width:"116"
},
dataProviderID:"taskstd_variable_data",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"D5FB202D-477D-49C6-A740-E3FF5E11939A",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"DC27E180-9E1F-4471-BC08-9BC82C747CD2",
visible:true
},
name:"component_7A2A1CF2",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"DE6180A8-FE63-4799-9175-AFCE697D7E1B"
},
{
cssPosition:"472,-1,-1,520,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"472",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.costPerPiecePricing",
visible:true
},
name:"component_81CDC0AD",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DF307FC6-CF70-45C4-994A-C267FDA00AA6"
},
{
cssPosition:"138,-1,-1,495,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"495",
right:"-1",
top:"138",
width:"60"
},
dataProviderID:"taskstd_inches_per_lift",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldLift1",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DF9183A3-9375-4AF8-9EB6-5F8148212DD2"
},
{
cssPosition:"223,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"223",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.headTrim",
visible:true
},
name:"lblTrimHead",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DFF40F21-B2D6-4FA9-981C-C64D35E1C34B"
},
{
cssPosition:"10,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.functionalArea",
visible:true
},
name:"component_AFB51962",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E131AEDE-CED1-4101-AE5F-8E426F823D93"
},
{
cssPosition:"163,-1,-1,495,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"495",
right:"-1",
top:"163",
width:"60"
},
dataProviderID:"taskstd_lifts_per_hr",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldLift2",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E49B78C5-3539-425A-972D-D042607F9077"
},
{
cssPosition:"138,-1,-1,317,228,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"317",
right:"-1",
top:"138",
width:"228"
},
enabled:true,
labelFor:"fldPockets",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.numberFeedersPockets",
visible:true
},
name:"lblPockets",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E4A2E2C0-E3C9-47FC-97F4-70149ED006D9"
},
{
cssPosition:"223,-1,-1,155,230,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"223",
width:"230"
},
dataProviderID:"taskstd_materials_needed",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"740EE70A-81EA-480E-8836-63B926B6C14D",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_materials_needed",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E5020BE2-C424-479A-A055-8E3DA024F576"
},
{
cssPosition:"223,-1,-1,625,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"625",
right:"-1",
top:"223",
width:"60"
},
dataProviderID:"taskstd_trim_head",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTrimHead",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E674AD33-DF1D-487C-BF3A-B03D682A5CC1"
},
{
cssPosition:"472,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"472",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.markup%",
visible:true
},
name:"lblMrkUp",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EA559E99-0A84-41C9-8763-28C2C90375DE"
},
{
cssPosition:"472,-1,-1,243,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"243",
right:"-1",
top:"472",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customerPricing",
visible:true
},
name:"component_252C961C",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EC384376-52BA-4897-B022-4C6E05895640"
},
{
cssPosition:"298,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"298",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.trimmer",
visible:true
},
name:"component_42451DE2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EC561C72-2232-4793-98F5-8314A455CFDC"
},
{
cssPosition:"194,-1,-1,720,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"720",
right:"-1",
top:"194",
width:"161"
},
dataProviderID:"taskstd_flg_card_feeder",
enabled:true,
onDataChangeMethodID:"92ED8BB4-5298-498D-B726-E6230A1BAC22",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.cardFeeder",
visible:true
},
name:"fldAcc3",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"F36919A0-5655-45A2-AEC3-4A33720406FB"
},
{
cssPosition:"298,-1,-1,480,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"480",
right:"-1",
top:"298",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.pullLap",
visible:true
},
name:"lblTrimPullLap",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FA8A7388-9FB3-48DD-86E4-BB7560B96F63"
},
{
cssPosition:"397,-1,-1,536,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"536",
right:"-1",
top:"397",
width:"60"
},
dataProviderID:"taskstd_spine_rounding_factor",
editable:true,
enabled:true,
format:"#.0000",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"taskstd_spine_rounding_factor",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"FE2ACBBE-DA98-451A-8360-28194F5C69FD"
},
{
cssPosition:"138,-1,-1,720,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"720",
right:"-1",
top:"138",
width:"161"
},
dataProviderID:"taskstd_flg_cover_deck",
enabled:true,
onDataChangeMethodID:"32DFF977-0BC5-4C77-A7F4-E9BEEACEAE04",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.coverDeck",
visible:true
},
name:"fldAcc1",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"FF52FC94-1C46-4E6C-A83B-C285A7D60D10"
}
],
name:"sa_task_standards_binding",
onShowMethodID:"4E2B6489-FF79-4377-848F-C8B74BDE664D",
paperPrintScale:100,
scrollbars:33,
size:"945,475",
styleName:null,
typeid:3,
uuid:"B8E7887F-A9EB-44A4-977E-DDDA5941D742"