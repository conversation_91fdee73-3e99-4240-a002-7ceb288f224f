/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"6D14C9A3-62DC-49DE-9355-619F28ED07FE",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"E9CAD275-B668-4D77-903E-4AAD996ABBF8"}
 */
function onReady() {
    _gridReady = 1;
}

/**
*
* @param {Boolean} _firstShow
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"74184E6C-98BE-44E0-B495-AD1EF02C2A39"}
*/
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	var retval = _super.onShowForm(_firstShow, _event);
	
	elements.grid.getColumn(elements.grid.getColumnIndex("fldPosition")).visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'; 
	elements.grid.getColumn(elements.grid.getColumnIndex("fldLength")).visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position';
	elements.btnClearPositions.visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position';
	
	if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
		foundset.sort('sequence_nr asc');
	}

	return retval;
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CB7178D9-D467-4D2E-A6E2-A291DA1D390F"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldUseUDV") {
		onAction_useUDV(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"26E07193-4C41-4603-9319-B395737BD795"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldPosition") {
		onDataChange_position(oldValue, newValue, event);
	}
	if (col.id == "fldLength") {
		onDataChange_length(oldValue, newValue, event);
	}
	if (col.id == "fldFieldName") {
		onDataChange_field(oldValue, newValue, event);
	}
	if (col.id == "fldSumType") {
		onDataChange_type(oldValue, newValue, event);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0041E3F0-79FA-41E5-B37A-97751734199D"}
 */
function btnMoveUp_onAction(event) {
	foundset.sort('sequence_nr asc')
	foundset.loadAllRecords()

	var rec = foundset.getSelectedRecord()
	if(rec && rec.sequence_nr > 1){
		var recBef = getRecFromSeqNum(rec.sequence_nr-1)
		if(recBef){
			recBef.sequence_nr++
			rec.sequence_nr--
			databaseManager.saveData()			
			foundset.sort('sequence_nr asc')
			foundset.loadAllRecords()
			foundset.setSelectedIndex(rec.sequence_nr)
			
			if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
				rec.bifsf_position = recBef.bifsf_position;
				setNextRecPosition(rec.sequence_nr, rec.bifsf_position, rec.bifsf_length);
			}
		}
	}
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"CDA7B955-84DF-4D6E-9E06-40349F493845"}
 */
function btnMoveDown_onAction(event) {
	foundset.sort('sequence_nr asc')
	foundset.loadAllRecords()

	var rec = foundset.getSelectedRecord()
	if(rec && rec.sequence_nr < foundset.getSize()){
		var recAft = getRecFromSeqNum(rec.sequence_nr+1)
		if(recAft){
			recAft.sequence_nr--
			rec.sequence_nr++
			databaseManager.saveData()			
			foundset.sort('sequence_nr asc')
			foundset.loadAllRecords()
			foundset.setSelectedIndex(rec.sequence_nr)
			
			if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
				recAft.bifsf_position = rec.bifsf_position;
				setNextRecPosition(recAft.sequence_nr, recAft.bifsf_position, recAft.bifsf_length);
			}
		}
	}
}

/**
 * @param {Number} iSeqNum
 * 
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_sum_field>}
 *
 * @properties={typeid:24,uuid:"982D70B1-BD53-4F2D-96DE-2BB7C7E6F6E4"}
 * @AllowToRunInFind
 */
function getRecFromSeqNum(iSeqNum){
	return foundset.getRecord(iSeqNum)
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"01E0796C-31A0-444D-9BC4-F27573CC0137"}
 */
function onAction_btnAdd(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		foundset.newRecord(false, true);
		foundset.sequence_nr = GetNextSeqNum();
		
		if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
			var rPrev = getPrevRec(foundset.sequence_nr);
			if(rPrev){
				if(rPrev.bifsf_position > 0 && rPrev.bifsf_length > 0){
					foundset.bifsf_position = rPrev.bifsf_position + rPrev.bifsf_length;
				}
			}
			else{
				foundset.bifsf_position = 1;
			}		
		}
		
		databaseManager.saveData(foundset);
	}
    
	return;
}

/**
 * @return
 * @properties={typeid:24,uuid:"71704247-D42B-408B-B082-0B9FC0B2DA9E"}
 */
function GetNextSeqNum(){
	var tiRetVal;

	tiRetVal = globals.Query("SELECT max(sequence_nr) from sys_batch_inv_file_sum_field where bif_id = '" + forms.sys_batch_inv_file_dtl.foundset.bif_id + "'", true);
	if(tiRetVal=='')
		tiRetVal=1;
	else
		tiRetVal+=1;
		
	return tiRetVal;
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"D7796229-799F-461C-A637-127E94B12655"}
 */
function onAction_btnDelete(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		var rPrev = getPrevRec(sequence_nr);
		
		globals.avUtilities_delete(event);
		
		if(rPrev){
			setNextRecPosition(rPrev.sequence_nr, rPrev.bifsf_position, rPrev.bifsf_length);
		}
		else if(utils.hasRecords(foundset)){
			var rFirst = foundset.getRecord(1);
			rFirst.bifsf_position = 1;
			setNextRecPosition(rFirst.sequence_nr, rFirst.bifsf_position, rFirst.bifsf_length);
		}
	}
    
	return;
}

/**
*
* @param {JSEvent} event
*
 * @return
* @properties={typeid:24,uuid:"1AFC643F-1CEE-4E05-A97F-0EDF919447B9"}
*/
function onLoad(event) {
	return _super.onLoad(event);
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"8E7B9D79-DC4F-4D45-9CB5-B5B647D16A99"}
 */
function onRecordSelection(_event, _form) {
	

	if(utils.hasRecords(sys_batch_inv_file_sum_field_to_app_avail_batch_inv_fields)){
		var rAppField = sys_batch_inv_file_sum_field_to_app_avail_batch_inv_fields.getRecord(1);
		
		if(!rAppField.abif_summary_field && rAppField.abif_field_name != 'Batch Invoice Post Date'){
		}
	}
	
	return _super.onRecordSelection(_event, _form);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"25EB47C5-8A53-43FF-BF9D-5422DA0FF0E0"}
 */
function onDataChange_field(oldValue, newValue, event) {
//	elements.grid.setReadOnly(true, ["fldSumType"]);

	if(newValue && utils.hasRecords(sys_batch_inv_file_sum_field_to_app_avail_batch_inv_fields)){
		var rAppField = sys_batch_inv_file_sum_field_to_app_avail_batch_inv_fields.getRecord(1);
		
		if(!rAppField.abif_summary_field && rAppField.abif_field_name != 'Batch Invoice Post Date'){
		}
	}
	
	updateFieldLabel();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7AB8A651-FB7F-43A0-8951-9AB6392468B0"}
 */
function onDataChange_type(oldValue, newValue, event) {
	updateFieldLabel();
	return true;
}

/**
 * @properties={typeid:24,uuid:"049DE738-8004-48B7-8EAB-21B7AFD2AF92"}
 */
function updateFieldLabel(){
	if(abif_id && bifsf_summary_type && !bifsf_col_header){
		bifsf_col_header = application.getValueListDisplayValue('vl_batch_inv_fields_numeric', abif_id) + ' - ' +
			application.getValueListDisplayValue('vl_biSummaryTypes', bifsf_summary_type);

	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7275B18F-70FE-44EC-9E30-DC742621AB28"}
 */
function onDataChange_position(oldValue, newValue, event) {
	if(newValue != null){
		if(newValue < 1){
			bifsf_position = oldValue;
			scopes.avText.showWarning('PositionTooSmall');
		}
		else if(newValue <= getPrevPos()){
			bifsf_position = oldValue;
			scopes.avText.showWarning('PosMustBe>PrevPos');
		}
		else{
			if(sequence_nr > 1){
				setPrevRecLength(sequence_nr, bifsf_position);
			}
			if(bifsf_length > 0){
				setNextRecPosition(sequence_nr, bifsf_position, bifsf_length);
			}
		}
	}
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DA9C5634-6B79-4ACE-99DF-E6181E192868"}
 */
function onDataChange_length(oldValue, newValue, event) {
	if(newValue != null){
		if(newValue >= 1){
			setNextRecPosition(sequence_nr, bifsf_position, bifsf_length);
		}
		else{
			scopes.avText.showWarning('LengthTooSmall');
			bifsf_length = oldValue;
		}
	}

	return true;
}

/**
 * @param {Number} nCurIdx
 * @param {Number} nCurPos
 * @param {Number} nCurLen
 *
 * @properties={typeid:24,uuid:"A05D7EBA-C882-4AEC-83F4-20D8D2ACFDE9"}
 */
function setNextRecPosition(nCurIdx, nCurPos, nCurLen){
	var rNext = getNextRec(nCurIdx);
	
	if(rNext){
		rNext.bifsf_position = nCurPos + nCurLen;
		
		if(rNext.bifsf_length > 0){
			setNextRecPosition(rNext.sequence_nr, rNext.bifsf_position, rNext.bifsf_length);
		}
	}
}

/**
 * @param {Number} nCurIdx
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_sum_field>}
 *
 * @properties={typeid:24,uuid:"8E63B220-94CE-4CA6-9EE1-D08ED029BBB0"}
 */
function getNextRec(nCurIdx){
	if(foundset.getSize() > nCurIdx){
		return foundset.getRecord(nCurIdx + 1);
	}
	else{
		return null;
	}
}

/**
 * @param {Number} nCurIdx
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_sum_field>}
 *
 * @properties={typeid:24,uuid:"8E4EF334-DCE0-4A1B-B348-14CD7DE97F3A"}
 */
function getPrevRec(nCurIdx){
	if(nCurIdx > 1){
		return foundset.getRecord(nCurIdx - 1);
	}
	else{
		return null;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"002353E1-02A2-4C10-AE55-08D05EE3AFEE"}
 */
function getPrevPos(){
	var rPrev = getPrevRec(sequence_nr);
	
	if(rPrev){
		return rPrev.bifsf_position;
	}
	else{
		return null;
	}
}

/**
 * @param {Number} nCurIdx
 * @param {Number} nCurPos
 *
 * @properties={typeid:24,uuid:"C9EB8CF1-58C9-43F7-B70D-89E2AB120D9B"}
 */
function setPrevRecLength(nCurIdx, nCurPos){
	var rPrev = getPrevRec(nCurIdx);
	
	if(rPrev){
		rPrev.bifsf_length = nCurPos - rPrev.bifsf_position;
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7DAABBAB-7FB0-48D7-A35D-C3FAA4BABB7B"}
 */
function onAction_useUDV(event) {

	if(bifsf_use_udv == 1){
		abif_id = null;
	}
	else{
		bifsf_user_defined_value = null;
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C5E39357-1304-42E5-89AC-0500EAC65C0C"}
 */
function onAction_clearPos(event) {
    
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
		for(var i=1; i<=foundset.getSize(); i++){
			var r = foundset.getRecord(i);
			
			if(i > 1){
				r.bifsf_position = null;
			}
			
			r.bifsf_length = null;
		}
	}
}

/**
 * Perform sort.
 *
 * @param {String} dataProviderID element data provider
 * @param {Boolean} asc sort ascending [true] or descending [false]
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"A0061674-5B27-4586-8726-15F739B9DECE"}
 */
function onSort(dataProviderID, asc, event) {
	if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format != 'Fixed Position'){
		foundset.sort(dataProviderID + (asc ? ' asc' : ' desc'), false);
	}
}
