customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sch_milestone_group",
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
cssPosition:"144,-1,15,738,80,22",
json:{
cssPosition:{
bottom:"15",
height:"22",
left:"738",
right:"-1",
top:"144",
width:"80"
},
enabled:true,
onActionMethodID:"70B6911D-31E2-4D2A-8D38-B5CA7A623F42",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_CE98E299",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"1F313435-AECC-4250-95F7-E382AA361CA6"
},
{
cssPosition:"15,-1,-1,185,189,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"185",
right:"-1",
top:"15",
width:"189"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_C598BC82",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"31771FFC-9F5A-43F5-B107-7BA36651E46E"
},
{
cssPosition:"15,-1,-1,602,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"602",
right:"-1",
top:"15",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
enabled:true,
styleClass:"border_list_text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_1",
styleClass:"border_list_text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"47074CD0-EECE-4802-A5BF-B99B1ACD1F3E"
},
{
cssPosition:"15,-1,-1,629,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"629",
right:"-1",
top:"15",
width:"200"
},
dataProviderID:"qa_check_pos_1",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"listview textbox_bts",
tabSeq:5,
visible:true
},
name:"qa_check_field_1",
styleClass:"listview textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6D1D27E9-59A6-4361-85E1-C06E594AEDEF"
},
{
cssPosition:"65,-1,-1,629,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"629",
right:"-1",
top:"65",
width:"200"
},
dataProviderID:"qa_check_pos_3",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"listview textbox_bts",
tabSeq:7,
visible:true
},
name:"qa_check_field_3",
styleClass:"listview textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"74EC794D-0DC8-4A93-8868-7E5D7E495F3C"
},
{
cssPosition:"15,244,-1,400,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"400",
right:"244",
top:"15",
width:"200"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_desc",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"listview textbox_bts",
tabSeq:0,
visible:true
},
name:"cost_centre",
styleClass:"listview textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"91F33B4E-12BD-4912-BF49-AA1F44B23684"
},
{
cssPosition:"40,-1,-1,629,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"629",
right:"-1",
top:"40",
width:"200"
},
dataProviderID:"qa_check_pos_2",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"listview textbox_bts",
tabSeq:6,
visible:true
},
name:"qa_check_field_2",
styleClass:"listview textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"95145F65-16BE-44C4-AFC1-BD28B4016B71"
},
{
height:181,
partType:8,
typeid:19,
uuid:"B613E9CA-0FE4-4235-8C66-55BF0C08661B"
},
{
height:99,
partType:5,
typeid:19,
uuid:"B94C121E-EF2C-4C28-8A6F-B2BF06758807"
},
{
cssPosition:"65,-1,-1,602,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"602",
right:"-1",
top:"65",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
enabled:true,
styleClass:"border_list_text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_3",
styleClass:"border_list_text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"BEE9F3DE-56A2-49F3-9653-C5449DEF8FDD"
},
{
partType:2,
typeid:19,
uuid:"CA8C7B64-6BBF-4CFD-A3FC-2DA6C43019C7"
},
{
cssPosition:"15,-1,-1,15,145,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"15",
right:"-1",
top:"15",
width:"145"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_EC28F0BE",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E0AFF926-2ADC-4A98-948B-505BE27CDB5F"
},
{
cssPosition:"40,-1,-1,602,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"602",
right:"-1",
top:"40",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
enabled:true,
styleClass:"border_list_text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_2",
styleClass:"border_list_text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"E9A211A0-5134-463B-BFDA-E370AA05C28B"
},
{
cssPosition:"144,-1,15,653,80,22",
json:{
cssPosition:{
bottom:"15",
height:"22",
left:"653",
right:"-1",
top:"144",
width:"80"
},
enabled:true,
onActionMethodID:"*************-4845-972C-966A40FFDFCC",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_1DA7D9E3",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"FC89216E-3389-47F9-96CF-38161ACFEA86"
}
],
name:"sf_main_dtl_labour_qa_checks_by_job_OLD",
onRenderMethodID:"-1",
onShowMethodID:"-1",
scrollbars:33,
size:"844,230",
styleName:"Avanti",
typeid:3,
uuid:"B31A497F-B61E-4770-ABF3-126E2D01FBA6",
view:1