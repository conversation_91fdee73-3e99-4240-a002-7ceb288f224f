customProperties:"methods:{\
onHideMethodID:{\
arguments:null,\
parameters:null\
},\
onRecordSelectionMethodID:{\
arguments:null,\
parameters:null\
},\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_price_rule",
extendsID:"38306E32-54A8-414C-BF88-9C4B659E6730",
items:[
{
cssPosition:"64,-1,-1,430,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"430",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
labelFor:"pricerule_end_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dateTo",
visible:true
},
name:"pricerule_end_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"47A1BF3E-7952-48D8-AD5C-2CF0EC193E8A"
},
{
cssPosition:"64,-1,-1,150,270,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"64",
width:"270"
},
dataProviderID:"pricetype_id",
editable:true,
enabled:true,
onDataChangeMethodID:"0EF230C3-C3E4-4D16-A54E-C5FFABC193B1",
styleClass:"typeahead_bts",
tabSeq:2,
valuelistID:"B3DC5B9A-1BF0-4767-B95B-2192F8328EF4",
visible:true
},
name:"pricetype_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"48140116-B496-4DD1-BC77-FD376E1583FB"
},
{
cssPosition:"37,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
labelFor:"pricerule_desc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.priceRuleDesc",
visible:true
},
name:"pricerule_desc_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"57FF40E7-4FCF-4BD9-975B-D508502861F5"
},
{
cssPosition:"37,-1,-1,430,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"430",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
labelFor:"pricerule_start_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dateFrom",
visible:true
},
name:"pricerule_start_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7ED5CAF5-0EBC-458B-A21C-3AB598722608"
},
{
cssPosition:"64,-1,-1,575,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"575",
right:"-1",
top:"64",
width:"140"
},
dataProviderID:"pricerule_end_date",
enabled:true,
format:"i18n:avanti.date.format",
onDataChangeMethodID:"B8A56232-136E-4D11-A4AD-B945D0645801",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:4,
visible:true
},
name:"pricerule_end_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"8F0237AF-241F-4808-BF98-02F0217418E1"
},
{
cssPosition:"96,4,6,1,995,500",
json:{
cssPosition:{
bottom:"6",
height:"500",
left:"1",
right:"4",
top:"96",
width:"995"
},
tabs:[
{
containedForm:"A5E88E7E-6048-421D-BECE-1B2BCA7D91A5",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabPriceDetails",
relationName:"sa_price_rule_to_sa_price_rule_detail",
svyUUID:"CD66277E-3A54-418E-B6D8-07EE3AAD361B",
text:"Pricing Details"
},
{
containedForm:"488BCAAB-75F4-48B2-9988-C401EDC73B97",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabNotes",
relationName:null,
svyUUID:"23ACB283-BF8B-4BFD-838D-A29758D5C2ED",
text:"i18n:avanti.lbl.notes"
}
]
},
name:"tab1",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"8FA799E5-CA03-4FD8-B66C-B2F2CBE97F4D"
},
{
cssPosition:"37,-1,-1,720,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"720",
right:"-1",
top:"37",
width:"100"
},
dataProviderID:"pricerule_active",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:5,
text:"i18n:avanti.lbl.active.flag",
visible:true
},
name:"pricerule_active",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"910BE687-FAC2-4788-A258-4D1DBC9D20C5"
},
{
cssPosition:"37,-1,-1,150,270,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"37",
width:"270"
},
dataProviderID:"pricerule_desc",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:1,
visible:true
},
name:"pricerule_desc",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A6106273-F2F6-4A44-A321-10AF01497328"
},
{
height:602,
partType:5,
typeid:19,
uuid:"B63F5142-**************-1586055196B0"
},
{
cssPosition:"37,-1,-1,575,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"575",
right:"-1",
top:"37",
width:"140"
},
dataProviderID:"pricerule_start_date",
enabled:true,
format:"i18n:avanti.date.format",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:3,
visible:true
},
name:"pricerule_start_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"BE32D952-4D7F-463C-8059-E86309888C47"
},
{
cssPosition:"5,5,-1,0,995,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"5",
top:"5",
width:"995"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.StandardPriceRules_DetailView",
visible:true
},
name:"lbl_group_order_type",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C6828019-8FF7-42B5-9B03-C48601D5DA04"
},
{
cssPosition:"64,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
labelFor:"pricetype_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.PriceCategory",
visible:true
},
name:"pricetype_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E960024C-8553-41AC-BB35-48BCC3CB11D9"
}
],
name:"sa_price_rule_dtl",
onHideMethodID:"F792A743-0D4B-4779-B126-2852015646EC",
onRecordSelectionMethodID:"8539E571-BFD0-42D3-9136-DBBB73AE9E0E",
onShowMethodID:"25EB5AD4-0671-45EE-AC1B-B905E179E771",
scrollbars:33,
size:"1000,602",
styleName:null,
typeid:3,
uuid:"9A545652-F926-4755-BA3A-014E9DDA6289",
view:5