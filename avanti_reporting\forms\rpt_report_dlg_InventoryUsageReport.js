/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D83CF444-757D-4EFE-9E02-31174B45EF0B",variableType:4}
 */
var _includeAllItemClass = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8B2781F4-4B88-4656-8570-D3FD14E617E9"}
 */
var _toItemClass = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D0B3AD90-15E7-48FA-B092-68A2C7C10E39"}
 */
var _fromItemClass = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"FA156830-AE78-435D-A3D4-EEAA4FA84EA4",variableType:4}
 */
var _includeAllItems = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C5783BC0-0F69-46DF-A9EB-86279C47FE3C"}
 */
var _toItem = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"91BD0E0D-56C4-45AA-B9EF-360F10916282"}
 */
var _fromItem = null;

/**
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"752DCD17-DC4A-4BC0-B091-93652A31A185",variableType:93}
 */
var _fromDate = null;

/**
 * 
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"D1A4155F-0EA4-4EA8-8966-059C4576AB14",variableType:93}
 */
var _toDate = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B6E7DD4C-6188-46A3-A5EE-51B4A498A8E6",variableType:4}
 */
var _includeAllItemGroups = 1;

/**
 *
 * @properties={typeid:35,uuid:"FF3EC13C-AF9A-46A2-9C1E-0DDCBBF66FBC",variableType:-4}
 */
var _fromItemGroup = null;

/**
 *
 * @properties={typeid:35,uuid:"882EC26C-0E45-4D2C-9DB1-0AE7316149C9",variableType:-4}
 */
var _toItemGroup = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"9D662C62-1693-4203-99DC-6192AA89BB74",variableType:-4}
 */
var _selectedItemGroupArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"44EB734E-9316-469B-9C17-0CCE5C135BFA",variableType:4}
 */
var _includeSelectedItemClassCode = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"EE0B07E8-4EB1-48B5-B6E6-F4A0281F6F66",variableType:4}
 */
var _includeSelectedItem = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"4070B2B7-1DE1-46D5-B25B-AFEE520FCFF0",variableType:4}
 */
var _includeSelectedItemGroups = 0;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"0A8FD434-7789-4806-B590-3E3DD51456F3",variableType:8}
 */
var includeSelectedItemClasses = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"6AC4F5A4-B2D9-466C-A685-5ED456051C65",variableType:-4}
 */
var _selectedItemClassArray = new Array();

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"90DFDE3E-40DD-4122-AF9F-EA774D94F64F",variableType:8}
 */
var includeSelectedItems = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"F07A9B4D-A373-48F8-864A-05A3F8942B1E",variableType:-4}
 */
var _selectedItemArray = new Array();

/**
 * @properties={typeid:35,uuid:"6DCA9CB7-98B6-491E-8937-BFF5E05B532C",variableType:-4}
 */
var _bProfileModified = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A0F0E440-D603-4D75-97CA-B4BB84B2FCE9"}
 */
var fromPlant = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F16818A1-A54B-426A-B2CA-97B773198EDA"}
 */
var fromWhse = "";

/**
 * @properties={typeid:35,uuid:"DEA6C503-42B5-4117-9334-CF0D9225BA80",variableType:-4}
 */
var _aPlantsReturn = [];

/**
 * @properties={typeid:35,uuid:"F62B1E4B-C7DC-4340-86DD-A00091B0BF4C",variableType:-4}
 */
var _aPlantsDisplay = [];

/**
 * @properties={typeid:35,uuid:"4ACAA677-781C-4E9D-93CB-D6C7C2AB1793",variableType:-4}
 */
var _aDivsReturn = [];

/**
 * @properties={typeid:35,uuid:"AC269748-1F9A-4301-B9A9-876A84A58C81",variableType:-4}
 */
var _aDivsDisplay = [];

/**
 * @properties={typeid:35,uuid:"66D6C85B-57FF-4465-A7C0-2DD57A9029A6",variableType:-4}
 */
var _aWhsesReturn = [];

/**
 * @properties={typeid:35,uuid:"CD1F044A-C5AB-409B-B316-F47785691677",variableType:-4}
 */
var _aWhsesDisplay = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5FDA3FE7-8354-476E-8CF1-9780A1E0EF27"}
 */
var fromDiv = "";

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"E8CD6E72-4454-4E4C-AB4E-08209F853044",variableType:8}
 */
var includeAllParentCustomers = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"574C61E5-6295-4AA7-BEDE-F807438787FD",variableType:8}
 */
var includeSelectedParentCustomers = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"62F9E7D8-3B43-450D-B61B-444C86037131"}
 */
var fromParentCustomer = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"116B7CAF-14B9-442E-B94C-289D092C98B2"}
 */
var toParentCustomer = null;

/**
*
* @type {Array}
*
*
* @properties={typeid:35,uuid:"A697CF49-FE83-4F04-B640-3F7EC3CEC422",variableType:-4}
*/
var _selectedParentCustomerArray = new Array();

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"F5367B57-7BBF-47B5-95C0-2AA68909D11F",variableType:8}
 */
var includeAllCustomerParts = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"C1043DA7-F8EF-48C4-B5DB-7CFB359C020D",variableType:8}
 */
var includeSelectedCustomerParts = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"725FE68C-87F1-4653-8DD6-0335228C2546"}
 */
var fromCustomerParts = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"D2B21710-3A23-48ED-8BB9-124FEB61584E"}
 */
var toCustomerParts = null;

/**
*
* @type {Array}
*
*
 * @properties={typeid:35,uuid:"86050C2E-26E2-466A-9A11-1EF7016A21F2",variableType:-4}
 */
var _selectedCustomerPartsArray = new Array();

/**
 * @type {Number}
 *
 *
 *
 * @properties={typeid:35,uuid:"ACE8F701-7201-4ADE-9199-0DEF65AC3034",variableType:8}
 */
var _onSelectAllWarehouses = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"8A30679F-354E-4E3C-A1FB-E6198BB86F59",variableType:4}
 */
var _showInReportParentCustomer = 1;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"9A4E0596-A64A-4A90-8D9B-F876D5EF91C9",variableType:4}
 */
var _showInReportCustomerParts = 1;

/**
 * @properties={typeid:35,uuid:"93D8094F-276B-4A09-9AC9-295E12EA9B96",variableType:-4}
 */
var fromParentCustomerCode = null;

/**
 * @properties={typeid:35,uuid:"C89A096D-6699-4DBF-BB51-8D8CBD67F1CF",variableType:-4}
 */
var toParentCustomerCode = null;

/**
 * Standard Method for getting the Parameters from this filter form
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-08-14
 *
 * @returns {{aParamNames: Array, aParamValues: Array, whereClause: String}} Returns an object containing the param names you need and values
 *
 *
 * @properties={typeid:24,uuid:"2DFD7D95-109D-432B-A7E9-A7E4475DCA7A"}
 */
function getFilterParams()
{
	var oParams = new Object();
	var sPPlants = "";
	var sPWhse = "";
	
	var sWhere = " WHERE i.org_id = '" + globals.org_id + "' ";
	
	sWhere += " AND ttype.intranstype_trans_code IN (\'SFMI\',\'SH\',\'CS\',\'IM\I',\'II\',\'TSMA\',\'FP\') ";
	
	//preparing for query - Item Groups
	var sPSelectedItemGroupsSelection ="";
	for (var i = 0; i <= _selectedItemGroupArray.length - 1; i++) {
		if (i != 0) {
			sPSelectedItemGroupsSelection += ',';
		}
		sPSelectedItemGroupsSelection += "'" + _selectedItemGroupArray[i] + "' ";
	}
	
	//preparing for query - Item Class
	var sPSelectedItemClassSelection ="";
	for (var j = 0; j <= _selectedItemClassArray.length - 1; j++) {
		if (j != 0) {
			sPSelectedItemClassSelection += ',';
		}
		sPSelectedItemClassSelection += "'" + _selectedItemClassArray[j] + "' ";
	}
    
	//preparing for query - Item Class
	var sPSelectedItemSelection ="";
	for (var k = 0; k <= _selectedItemArray.length - 1; k++) {
		if (k != 0) {
			sPSelectedItemSelection += ',';
		}
		sPSelectedItemSelection += "'" + _selectedItemArray[k] + "'";
	}
	
	if (_selectedItemClassArray.length > 0) {
		sWhere += " AND i.itemclass_id IN (" + sPSelectedItemClassSelection + ") ";
	}
	else {
		if (_fromItemClass != null) sWhere += " AND i.itemclass_code >= '" + _fromItemClass + "' ";
		if (_toItemClass != null) sWhere += " AND i.itemclass_code <= '" + _toItemClass + "' ";
	}
	
	if (_selectedItemArray.length > 0) {
		sWhere += " AND i.item_id IN (" + sPSelectedItemSelection + ") ";
	} 
	else {
		if (_fromItem != null) sWhere += " AND i.item_code >= '" + _fromItem + "' ";
		if (_toItem != null) sWhere += " AND i.item_code <= '" + _toItem + "' ";
	}
	
	if (_selectedItemGroupArray.length > 0) {
		sWhere += " AND grp.ingroup_id IN (" + sPSelectedItemGroupsSelection + ") ";
	} 
	else {
		if (_fromItemGroup != null) sWhere += " AND grp.ingroup_code >= '" + _fromItemGroup + "' ";
		if (_toItemGroup != null) sWhere += " AND grp.ingroup_code <= '" + _toItemGroup + "' ";
	}

	// Preparing for Parent Customer
	var sPSelectedParentCustomerSelection = "";
	for (var l = 0; l <= _selectedParentCustomerArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedParentCustomerSelection += ',';
		}
		sPSelectedParentCustomerSelection += "'" + _selectedParentCustomerArray[k] + "'";
	}
	
	if (_selectedParentCustomerArray.length > 0 && globals.avBase_selectedParentCustomersViewOption == 0) {
		sWhere += " AND i.parent_cust_id IN (" + sPSelectedParentCustomerSelection + ") ";
	}
	else {
		if (fromParentCustomer != null) sWhere += " AND i.parent_cust_id >= '" + fromParentCustomer + "' ";
		if (toParentCustomer != null) sWhere += " AND i.parent_cust_id <= '" + toParentCustomer + "' ";
	}

	//Preparing for Customer Part
	var sPSelectedCustomerPartsSelection = "";
	for (l = 0; l <= _selectedCustomerPartsArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedCustomerPartsSelection += ',';
		}
		sPSelectedCustomerPartsSelection += "'" + _selectedCustomerPartsArray[l] + "'";
	}
	
	if (_selectedCustomerPartsArray.length > 0 && globals.avBase_selectedCustomerPartsViewOption == 0) {
		sWhere += " AND i.item_cust_part_number IN (SELECT item_cust_part_number FROM in_item WHERE item_id IN( " + sPSelectedCustomerPartsSelection + ")) ";
	}
	else {
		if (fromCustomerParts != null) sWhere += " AND i.item_cust_part_number >= '" + fromCustomerParts + "' ";
		if (toCustomerParts != null)   sWhere += " AND i.item_cust_part_number <= '" + toCustomerParts + "' ";
	}
	
	var fromDateParam = null;
	var toDateParam = null;
	
	if (_fromDate) {
		fromDateParam = plugins.DateUtils.dateFormat(_fromDate, 'yyyy-MM-dd');
		sWhere += " AND CAST(ihead.itemtransh_date AS DATE) >= '" + fromDateParam + "' " ;
	}
	
	if (_toDate) {
		toDateParam = plugins.DateUtils.dateFormat(_toDate, 'yyyy-MM-dd');
		sWhere += " AND CAST(ihead.itemtransh_date AS DATE) <= '" + toDateParam + "' " ;
	}
	
	if (fromPlant) {
		var aFromPlants = fromPlant.split('\n');
		for (i = 0; i <= aFromPlants.length - 1; i++) {
			if (i != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + aFromPlants[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aPlantsReturn.length - 1; j++) {
			if (j != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + _aPlantsReturn[j] + "'";
		}		
	}
	
	if (sPPlants) {
		sWhere += " AND plant.plant_id IN (" + sPPlants + ") ";
	}
	
	if (fromWhse) {
		var aFromWhses = fromWhse.split('\n');
		for (i = 0; i <= aFromWhses.length - 1; i++) {
			if (i != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + aFromWhses[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aWhsesReturn.length - 1; j++) {
			if (j != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + _aWhsesReturn[j] + "'";
		}		
	}
	
	if (sPWhse) {
		sWhere += " AND (wa.whse_id IN (" + sPWhse + ")) ";
	}
	
	
	application.output ("sWhere")
	application.output (sWhere)
	
	oParams.whereClause = null;	
	oParams.aParamNames = ["pFromItem","pToItem", "pFromItemClass","pToItemClass","pFromDate","pToDate", "pOrgId", "pWhere","pShowCustomerParts", "pShowParentCustomers"];
	oParams.aParamValues = [_fromItem, _toItem, _fromItemClass,_toItemClass,_fromDate, _toDate, globals.org_id, sWhere, _showInReportCustomerParts, _showInReportParentCustomer]
	return oParams;
}

/** *
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"6615D99D-8B7F-4496-B05F-D1F2D446D2ED"}
 */
function onShow(firstShow, event) {
	 _super.onShow(firstShow, event)
	 
	 globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	 
	 
	 _includeAllItems = 1;
	 includeSelectedItems = 0;
	 _fromItem = null;
	 _toItem = null;
	 _selectedItemArray = new Array();
	 
	 _includeAllItemClass = 1;
	 includeSelectedItemClasses = 0;
	 _fromItemClass = null;
	 _toItemClass = null;
	 _selectedItemClassArray = new Array();
	 
	 _includeAllItemGroups = 1;
	 _includeSelectedItemGroups = 0;
	 _fromItemGroup = null;
	 _toItemGroup = null;
	 _selectedItemGroupArray = new Array();
	 
	 globals.avBase_selectedParentCustomersViewOption = 0;
	 _showInReportParentCustomer = 1;
	 includeAllParentCustomers = 1;
	 includeSelectedParentCustomers = 0;
	 fromParentCustomer = null;
	 fromParentCustomerCode = null;
	 toParentCustomer = null;
	 toParentCustomerCode = null;
	 _selectedParentCustomerArray = new Array();
	 
	 
	 globals.avBase_selectedCustomerPartsViewOption = 0;
	 _showInReportCustomerParts = 1;
	 includeAllCustomerParts = 1;
	 includeSelectedCustomerParts = 0;
	 fromCustomerParts = null;
	 toCustomerParts = null;
	 _selectedCustomerPartsArray = new Array();

	 
	 _toDate = application.getServerTimeStamp();
	 _fromDate = globals.avUtilities_dateGetFirstOfMonth(_toDate);
	 elements._fromDate.format = globals.avBase_dateFormat;
	 elements._toDate.format = globals.avBase_dateFormat;
	 
	 
	 _onSelectAllWarehouses = 0;
	 elements.fromWhse.enabled = true;
	 fromWhse = _to_in_warehouse$avbase_employeedefaultwarehouse.whse_code;
	 
	 refreshUI();
	 load_vl_DivisionshasWarehouses()
	 loadPlantsForDiv('From');
	 setDefaultDivisionFilter();
	 loadWarehousesForPlant('From');
}

/**
 * 
 * @properties={typeid:24,uuid:"202B4EE7-B8C5-4FAB-B409-DE96362AC687"}
 */
function refreshUI()
{
	if (_includeAllItemClass == 1)
	{
		elements._fromItemClass.enabled = false;
		elements._toItemClass.enabled = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;
	}
	else
	{
		elements._fromItemClass.enabled = true;
		elements._fromItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
	}
	
	
	if (_includeAllItems == 1)
	{
		elements._fromItem.enabled = false;
		elements._toItem.enabled = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;
	}
	else
	{
		elements._fromItem.enabled = true;
		elements._fromItem.enabled = true;
		elements._toItem.enabled = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
	}
	
	//Item Group
	if (_includeAllItemGroups == 1 || _includeSelectedItemGroups == 1) {
		elements._fromItemGroup.enabled = false;
		elements._toItemGroup.enabled = false;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;

		if (_includeSelectedItemGroups == 1) {
			elements._selectedItemGroupMessage_label.visible = true;
			_includeAllItemGroups = 0;
		}
	} 
	else {
		elements._fromItemGroup.enabled = true;
		elements._toItemGroup.enabled = true;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;
		elements._selectedItemGroupMessage_label.visible = true;
		_includeSelectedItemGroups = 0;
		_includeAllItemGroups = 0;
	}
    
	 elements._selectedItemGroupMessage_label.text = 
     (_selectedItemGroupArray && _includeSelectedItemGroups == 1 ?
     "<html><a href='#'>(" + _selectedItemGroupArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemGroupsSelected") +") </a></html>" : null);
     
	//Item Class
	if (_includeAllItemClass == 1 || includeSelectedItemClasses == 1) {
		elements._fromItemClass.enabled = false;
		elements._toItemClass.enabled = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;

		if (includeSelectedItemClasses == 1) {
			elements._selectedItemClassMessage_label.visible = true;
			_includeAllItemClass = 0;
		}
	} 
	else {
		elements._fromItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
		elements._selectedItemClassMessage_label.visible = false;
	}

	elements._selectedItemClassMessage_label.text = (_selectedItemClassArray && includeSelectedItemClasses == 1 ? "<html><a href='#'>(" + _selectedItemClassArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemClassesSelected") + ") </a></html>" : null); 
	
	//Item
	if (_includeAllItems == 1 || includeSelectedItems == 1) {
		elements._fromItem.enabled = false;
		elements._toItem.enabled = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;

		if (includeSelectedItems == 1) {
			elements._selectedItemMessage_label.visible = true;
			_includeAllItems = 0;
		}
	} 
	else {
		elements._fromItem.enabled = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
		elements._selectedItemMessage_label.visible = false;
	}

	elements._selectedItemMessage_label.text = (_selectedItemArray && includeSelectedItems == 1 ? "<html><a href='#'>(" + _selectedItemArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemsSelected") + ") </a></html>" : null);

	var bFlag = false;
	//Parent Customers
    bFlag = (includeAllParentCustomers == 1 || includeSelectedParentCustomers == 1 ? false : true);
    elements.fromParentCustomer.enabled = bFlag;
    elements.toParentCustomer.enabled = bFlag;
    elements.btnLookupFromParentCustomer.enabled = bFlag;
    elements.btnLookupToParentCustomer.enabled = bFlag;
    elements._selectedParentCustomerMessage_label.visible = !bFlag;
    
    elements._selectedParentCustomerMessage_label.text =
        (_selectedParentCustomerArray && includeSelectedParentCustomers == 1 ?
        "<html><a href='#'>(" + _selectedParentCustomerArray.length + " " +  i18n.getI18NMessage("avanti.lbl.parentCustomersSelected") +") </a></html>": null);
        
        
   // Customer Parts
    bFlag = (includeAllCustomerParts == 1 || includeSelectedCustomerParts == 1 ? false : true);
    elements.fromCustomerParts.enabled = bFlag;
    elements.toCustomerParts.enabled = bFlag;
    elements.btnLookupFromCustomerParts.enabled = bFlag;
    elements.btnLookupToCustomerParts.enabled = bFlag;
    elements._selectedCustomerPartsMessage_label.visible = !bFlag;
    
    elements._selectedCustomerPartsMessage_label.text =
        (_selectedCustomerPartsArray && includeSelectedCustomerParts == 1 ?
        "<html><a href='#'>(" + _selectedCustomerPartsArray.length + " " +  i18n.getI18NMessage("avanti.lbl.CustomerPartsSelected") +") </a></html>": null);     

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"837C52F1-31E7-4970-919C-F2CF9C10FAD3"}
 */
function onDataChange_includeAllItemClasses(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItemClass = null;
		_toItemClass = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"1E04A7A3-8C6D-49CC-90A2-F0C1E9D41556"}
 */
function onDataChangeIncludeAllItems(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItem = null;
		_toItem = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"4C426F54-11B0-401E-B61B-92C64826E318"}
 */
function onDataChange_fromItemClass(oldValue, newValue, event) {
	_toItemClass = _fromItemClass
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"539AEE91-991E-43AA-91DD-************"}
 */
function onDataChange_fromItem(oldValue, newValue, event) {
	_toItem = _fromItem
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"408E7DA9-5F0E-41A0-9E6A-A937EB8FDEF3"}
 */
function onDataChange_toItemClass(oldValue, newValue, event) {
	if(_fromItemClass>_toItemClass)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3D48B091-E60E-4E25-99CD-1E0049B41AEB"}
 */
function onDataChange_toItem(oldValue, newValue, event) {
	if(_fromItem>_toItem)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"98A73DFA-0C8B-45E5-819B-76CF7D47264D"}
 */
function afterFromItemClassLookup(record)
{
	if (record) _fromItemClass = record.itemclass_code;
	onDataChange_fromItemClass(null,_fromItemClass,null)
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"E97011C6-D812-49CA-97A2-AD4A725232FA"}
 */
function afterToItemClassLookup(record)
{
	if (record) _toItemClass = record.itemclass_code;
}

/**
 * From Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"C154269A-F2AA-49EB-A824-6A41B12B30F0"}
 */
function lookupFilter_ItemFrom(fs) 
{
	fs.addFoundSetFilterParam('item_code','>','^','PlannedPurchasing_FromItemLookupFilter');
	
	return fs;
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"C9827CB7-EEB4-47F7-B38B-88A9B8E03340"}
 */
function afterFromItemLookup(record)
{
	if (record) _fromItem = record.item_code;
	onDataChange_fromItem(null,_fromItem,null);
	
}

/**
 * To Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"D8B76875-A5F9-4522-A646-D35471C1BDF6"}
 */
function lookupFilter_ItemTo(fs) 
{
	fs.addFoundSetFilterParam('item_code','>=',_fromItem,'PlannedPurchasing_ToItemLookupFilter');
	
	return fs;
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"42746E1E-72A0-4A82-B18B-BE546E34BBDA"}
 */
function afterToItemLookup(record)
{
	if (record) _toItem = record.item_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"D39C34B5-E27E-4583-8770-574B0F52FABA"}
 */
function onDataChange_fromItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;		
	}
	_toItemGroup = _fromItemGroup;
	refreshUI();
// 	elements._toItemGroup.requestFocus();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"98F360B0-22A5-4BA7-84DB-9A6477FA90DD"}
 */
function onDataChange_toItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;
		refreshUI();
	}
	return true;
}

/**
 * 
 * @param aSelectedItemGroups
 *
 *
 * @properties={typeid:24,uuid:"6EDD7F98-9BD3-471E-A72C-6153E54D702F"}
 */
function setSelectedItemGroups(aSelectedItemGroups) {
	_selectedItemGroupArray = aSelectedItemGroups;
	refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"AF9B8C27-DF37-4D90-B590-021B743A65A2"}
 */
function onAction_selectedItemGroup(event) {
	showSelectedItemGroupDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"4D4DDCBA-976E-447C-BFCF-F6E28CFB8521"}
 */
function showSelectedItemGroupDialog() {
    forms.sysDialog_selected_item_groups._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_groups._sCallBackMethod = "setSelectedItemGroups";
    forms.sysDialog_selected_item_groups._aSelectedItemGroups = _selectedItemGroupArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_groups, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemGroups"), true, false, "sysDialogSelectedItemGroups", true);
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"887C66F3-23C6-4819-A5B5-E8F9DFEB5B47"}
 */
function onIncludeAllItemGroupDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        _includeSelectedItemGroups = 0;
        elements._fromItemGroup.enabled = false;
        elements._toItemGroup.enabled = false;
        elements._selectedItemGroupMessage_label.text = null;
        elements._chkSelectedItemGroups
    }
    else {
    	 _includeSelectedItemGroups = 0;
         elements._fromItemGroup.enabled = true;
         elements._toItemGroup.enabled = true;
         elements._selectedItemGroupMessage_label.text = null;
    }
    
    _fromItemGroup = null;
    _toItemGroup = null;

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"DDFC9DEC-B832-406A-97D8-763C349F589D"}
 */
function onIncludeSelectedItemGroupToDataChange(oldValue, newValue, event) {
	if (newValue == 1) {
        _includeAllItemGroups = 0;
        _fromItemGroup = null;
        _toItemGroup = null;
        showSelectedItemGroupDialog();
        elements._selectedItemGroupMessage_label.enabled = true;
    }
    else {
        elements._selectedItemGroupMessage_label.text = null;
        _selectedItemGroupArray = [];
        _includeAllItemGroups = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"9DEA9929-C3F8-4AE5-94F0-61739C0B77D1"}
 */
function onIncludeSelectedItemClassDataChange(oldValue, newValue, event) {

    if (newValue == 1) {
        _includeAllItemClass = 0;
        _fromItemClass = null;
        _toItemClass = null;
        showSelectedItemClassDialog();
        elements._selectedItemClassMessage_label.enabled = true;
    }
    else {
        elements._selectedItemClassMessage_label.text = null;
        _selectedItemClassArray = [];
        _includeAllItemClass = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"82B811EE-60AE-421A-8907-E32BDD86BFB6"}
 */
function onAction_selectedItemClasses(event) {
    showSelectedItemClassDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"804F504A-2261-43D6-A6CA-6782A129AB13"}
 */
function showSelectedItemClassDialog() {
    forms.sysDialog_selected_item_classes._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_classes._sCallBackMethod = "setSelectedItemClasses";
    forms.sysDialog_selected_item_classes._aSelectedItemClasses = _selectedItemClassArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_classes, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemClasses"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItemClasses
 *
 *
 * @properties={typeid:24,uuid:"51F1B517-9988-4148-A583-BD13A2C26039"}
 */
function setSelectedItemClasses(aSelectedItemClasses) {
    _selectedItemClassArray = aSelectedItemClasses;
    refreshUI();
}

// Item

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 *
 * @return
 * @properties={typeid:24,uuid:"ADCCFC27-D619-47E8-BE9A-9F2F3A66DBAC"}
 */
function onIncludeSelectedItemsDataChange(oldValue, newValue, event) {
	
    if (newValue == 1) {
        _includeAllItems = 0;
        _fromItem = null;
        _toItem = null;
        showSelectedItemDialog();
    }
    else {
        elements._selectedItemMessage_label.text = null;
        _selectedItemArray = [];
        _includeAllItems = 1;
    }

    refreshUI();
    return true;
}

/**
 *
 * @properties={typeid:24,uuid:"E5F657A6-9783-42A4-AC51-95B74A37E0F9"}
 */
function showSelectedItemDialog() {
    forms.sysDialog_selected_items._sCallBackForm = controller.getName();
    forms.sysDialog_selected_items._sCallBackMethod = "setSelectedItems";
    forms.sysDialog_selected_items._aSelectedItems = _selectedItemArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_items, -1, -1, 695, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItems"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItems
 *
 *
 * @properties={typeid:24,uuid:"E029873C-4436-441F-BD7F-847880C4C9EB"}
 */
function setSelectedItems(aSelectedItems) {
    _selectedItemArray = aSelectedItems;
    refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"F3AD2B41-977E-466E-82A1-4907E43D4BF5"}
 */
function onAction_selectedItems(event) {
    showSelectedItemDialog();
}

/**
 * @properties={typeid:24,uuid:"1B3DFD19-BBE6-411D-BBFB-B061C660D65A"}
 */
function load_vl_DivisionshasWarehouses() {

	_aDivsReturn = [];
	_aDivsDisplay = [];
	
	fromDiv = "";

	var iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} ***/
		dsData;

	oSQL.sql = "SELECT DISTINCT CONCAT(div.div_code,': ',div.div_name) AS div , div.div_id \
			    FROM  sys_division AS div \
			    INNER JOIN in_warehouse AS whse ON whse.div_id = div.div_id \
			    WHERE div.org_id = ? \
			    AND div.div_id IN (SELECT div_id FROM sys_employee_div WHERE empl_id = ?) \
			    ORDER BY CONCAT(div.div_code,': ',div.div_name) ASC ";

	oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
	dsData = globals["avUtilities_sqlDataset"](oSQL);

	iMax = dsData.getMaxRowIndex();
	for (var i = 1; i <= iMax; i++) {
		_aDivsDisplay.push(dsData.getValue(i, 1));
		_aDivsReturn.push(dsData.getValue(i, 2));
	}

	application.setValueListItems("vl_DivisionshasWarehouses", _aDivsDisplay, _aDivsReturn);
}

/**
 *
 * @param sDivType
 *
 * @properties={typeid:24,uuid:"7CDAD1C1-1084-49C5-B878-A165BA6ADCF5"}
 */
function loadPlantsForDiv(sDivType) {

	var aFromDiv = [];
	var sFromDiv = "";

	_aPlantsReturn = [];
	_aPlantsDisplay = [];

	fromPlant = "";
	fromWhse = "";
	
	if (!fromDiv) {
		setDefaultDivisionFilter();
	}
		aFromDiv = fromDiv.split('\n');

	for (var i = 0; i <= aFromDiv.length - 1; i++) {
		if (i != 0) {
			sFromDiv += ',';
		}
		sFromDiv += "'" + aFromDiv[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";

		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";
		
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	}
	iMax = dsData.getMaxRowIndex();
	
	if (iMax == 0) {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
			INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
			WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
			ORDER BY div.div_code ASC, plant.plant_code ASC";
	
			oSQL.args = [globals["org_id"]];
			dsData = globals["avUtilities_sqlDataset"](oSQL);
			iMax = dsData.getMaxRowIndex();
	}
	
	for (i = 1; i <= iMax; i++) {
		_aPlantsDisplay.push(dsData.getValue(i, 1));
		_aPlantsReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromPlant =  dsData.getValue(i, 2);
		}
	}
		
	application.setValueListItems("vl_Plants_rpt_dlg_base_on_div", _aPlantsDisplay, _aPlantsReturn);
}

/**
 * 
 * @param sPlantType
 *
 * @properties={typeid:24,uuid:"7514A19D-5F45-4CF7-A61A-EEF6A79E73DD"}
 */
function loadWarehousesForPlant(sPlantType) {

	var aFromPlant = [];
	var sFromPlant = "";

	_aWhsesReturn = [];
	_aWhsesDisplay = [];

	
	aFromPlant = fromPlant.split('\n');

	for (var i = 0; i <= aFromPlant.length - 1; i++) {
		if (i != 0) {
			sFromPlant += ',';
		}
		sFromPlant += "'" + aFromPlant[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

	oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, div.div_id, plant.plant_id \
				 FROM in_warehouse AS whse \
					INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
					INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
				 WHERE div.org_id = ? \
					AND plant.plant_id IN ( " + sFromPlant + " ) \
				 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
				 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";	
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
			oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, plant.plant_id \
						 FROM in_warehouse AS whse \
							INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
							INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
						 WHERE div.org_id = ? \
							AND plant.plant_id IN ( " + sFromPlant + " ) \
						 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? ) \
						 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
	}
	iMax = dsData.getMaxRowIndex();
	for (i = 1; i <= iMax; i++) {
		_aWhsesDisplay.push(dsData.getValue(i, 1));
		_aWhsesReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromWhse =  dsData.getValue(i, 2);
		}
		else if (i>1 && _onSelectAllWarehouses == 1) {
			fromWhse += "\n";
			fromWhse +=  dsData.getValue(i, 2);
			 
		}
		
	}
		
	application.setValueListItems("vl_Warehouses_rpt_dlg_base_on_plant", _aWhsesDisplay, _aWhsesReturn);
	
}

/**
 * @properties={typeid:24,uuid:"BEED71E8-0EDB-4D8A-BC40-B5D25F2B2F99"}
 */
function setDefaultDivisionFilter() {
    
    elements.fromDiv.enabled = true;
    
	if (utils.hasRecords(_to_sys_division$org)) {
		var rDivision = _to_sys_division$org.getRecord(1);
		fromDiv = rDivision.div_id;
	}
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"F5E211D7-33BF-4EFB-BE82-A257F8DFF89C"}
 */
function onDataChange_fromDiv (oldValue, newValue, event) {
	loadPlantsForDiv('From');
	onDataChange_fromPlant (oldValue, newValue, event);
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"631D7037-A85E-4E35-A0AF-CB6A4DEEA166"}
 */
function onDataChange_fromPlant (oldValue, newValue, event) {
	fromWhse = "";
	loadWarehousesForPlant('From');
}

/**
 * @properties={typeid:24,uuid:"288E6C9F-0327-4E39-A09D-E6FD7C090870"}
 */
function showSelectedParentCustomerDialog() {
    forms.sysDialog_selected_parent_customers._sCallBackForm = controller.getName();
    forms.sysDialog_selected_parent_customers._sCallBackMethod = "setSelectedParentCustomers";
    forms.sysDialog_selected_parent_customers._aSelectedCustomers = _selectedParentCustomerArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_parent_customers, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedParentCustomers"), true, false, "sysDialogSelectedParentCustomer", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"59AC37A2-3C1F-42DA-B81C-A5F9E2869EE1"}
 */
function onShowFromParentCustomerLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'cust_code', 'Customers', 'afterFromParentCustomerLookup', 'lookupFilter_Customers', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} _fs
 * @return {JSFoundSet<db:/avanti/in_item_supplier>}
 *
 *
 *
 * @properties={typeid:24,uuid:"2D08F3C7-5226-473F-873C-9115639228CF"}
 */
function lookupFilter_Customers(_fs)
{
	//_fs.addFoundSetFilterParam('cust_parent_cust_id','!=',null,'LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"15E5E9A7-7282-4DEB-AA6E-ECB984592A9D"}
 */
function afterToParentCustomerLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"6D3EF1C3-4DE2-4409-B6BD-CEBBBD330FCA"}
 */
function afterFromParentCustomerLookup(record) {
	if (record) fromParentCustomer = record.cust_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"687713C9-3891-41A7-B600-B486E62C10F3"}
 */
function onFromParentCustomerDataChange(oldValue, newValue, event) {
	if (!toParentCustomer) {
		toParentCustomer = fromParentCustomer;
	} else if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D65E76A3-DB8A-470D-B75E-1296C8822E76"}
 */
function onToParentCustomerDataChange(oldValue, newValue, event) {
	if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"96758648-8F46-454F-A775-A299AEDD62CE"}
 */
function onIncludeAllParentCustomerDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        elements._selectedParentCustomerMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"743A3292-6F66-4080-B039-9CA0C5623DFE"}
 */
function onIncludeSelectedParentCustomerDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedParentCustomersViewOption = 0;
	
    if (newValue == 1) {
        includeAllParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        showSelectedParentCustomerDialog();
    }
    else if (newValue == 0) {
    	includeAllParentCustomers = 1;
        fromParentCustomer = null;
        toParentCustomer = null;
        _selectedParentCustomerArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedParentCustomers
 * public
 *
 * @properties={typeid:24,uuid:"F17F6CDE-7EB1-4801-9B3A-45B1E340A5D1"}
 */
function setSelectedParentCustomers(aSelectedParentCustomers) {
    _selectedParentCustomerArray = aSelectedParentCustomers;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"18A9AED5-B880-483E-8525-AC9D4D69B532"}
 */
function onAction_selectedParentCustomer(event) {
    showSelectedParentCustomerDialog();
}

/// FOR CUSTOMER PARTS ONLY

/**
 * 
 *  *
 * @properties={typeid:24,uuid:"06DBDCDB-B051-4A3B-A94D-850D04C55E43"}
 */
function showSelectedCustomerPartsDialog() {
    forms.sysDialog_selected_customers_parts._sCallBackForm = controller.getName();
    forms.sysDialog_selected_customers_parts._sCallBackMethod = "setSelectedCustomerParts";
    forms.sysDialog_selected_customers_parts._aSelectedCustomerParts = _selectedCustomerPartsArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_customers_parts, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.IncludeSelectedCustomerPartNo"), true, false, "sysDialogSelectedCustomerParts", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 * 
 *
 * @properties={typeid:24,uuid:"DCB9EB49-ACA5-4532-8AE5-82A7604E4D01"}
 */
function onShowFromCustomerPartsLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'item_cust_part_number', 'Items', 'afterFromCustomerPartsLookup', 'lookupFilter_CustomerParts', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item>} _fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"429EA059-C051-4121-92DD-3AF707A47A64"}
 */
function lookupFilter_CustomerParts(_fs)
{
	_fs.addFoundSetFilterParam('item_cust_part_number','!=',null,'LookupFilter')
	_fs.addFoundSetFilterParam('item_cust_part_number','!=','','LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"75E9EF7E-9D2F-4779-A8D6-A5B86D67BCA3"}
 */
function afterToCustomerPartsLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"5478F616-671E-4212-9A72-AAA5F99B4E3C"}
 */
function afterFromCustomerPartsLookup(record) {
	if (record) fromCustomerParts = record.item_cust_part_number;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"51578DA7-9D4E-4678-ABE1-6B232555F0A9"}
 */
function onFromCustomerPartsDataChange(oldValue, newValue, event) {
	if (!toCustomerParts) {
		toCustomerParts = fromCustomerParts;
	} else if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"85E9459F-3B3A-4CEA-B26F-5AD8D8150047"}
 */
function onToCustomerPartsDataChange(oldValue, newValue, event) {
	if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"7771FBCB-C9D9-43F3-8444-D9079A17E344"}
 */
function onIncludeAllCustomerPartsDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        elements._selectedCustomerPartsMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"FBAED6AC-0AAE-46AF-AB2F-B8D34DF4F681"}
 */
function onIncludeSelectedCustomerPartsDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedCustomerPartsViewOption = 0;
	
    if (newValue == 1) {
        includeAllCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        showSelectedCustomerPartsDialog();
    }
    else if (newValue == 0) {
    	includeAllCustomerParts = 1;
        fromCustomerParts = null;
        toCustomerParts = null;
        _selectedCustomerPartsArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedCustomerParts
 * public
 
 *
 * @properties={typeid:24,uuid:"24F8B8AE-B6C2-4C0B-A5FE-9431CFF6AA03"}
 */
function setSelectedCustomerParts(aSelectedCustomerParts) {
    _selectedCustomerPartsArray = aSelectedCustomerParts;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"07B5797E-681F-4C27-8FAB-7749D16A75CC"}
 */
function onAction_selectedCustomerParts(event) {
    showSelectedParentCustomerDialog();
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3D69BB90-6F88-4518-B94D-D3F6BD284231"}
 */
function onAction_showFromSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	fromCustomerParts = rItem.item_cust_part_number;
	
	if (fromCustomerParts < toCustomerParts) {
		fromCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"538A385F-0706-4FE0-B102-F3D7DA3FCC4B"}
 */
function onAction_showFromSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	fromParentCustomer = rCustomer.cust_id;
	fromParentCustomerCode = rCustomer.cust_code;
	
	if (fromParentCustomerCode < toParentCustomerCode) {
		fromParentCustomer = null;
		fromParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"C798514C-6B25-41BB-9B7D-2ECA4044D00A"}
 */
function onAction_showToSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	toCustomerParts = rItem.item_cust_part_number;
	
	if (toCustomerParts < fromCustomerParts) {
		toCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * 
 *
 * @properties={typeid:24,uuid:"B95FD5BD-C396-4A0F-8A23-8C3658D3804B"}
 */
function onAction_showToSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	toParentCustomer = rCustomer.cust_id;
	toParentCustomerCode = rCustomer.cust_code;
	
	if (toParentCustomerCode < fromParentCustomerCode) {
		 toParentCustomer = null;
		 toParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7A78F5A4-C53E-4DAE-AAD0-3823F7BE96A5"}
 */
function onAction_SelectAllWarehouses(event) {
	if (_onSelectAllWarehouses == 1) {
		elements.fromWhse.enabled = false;
  	    loadWarehousesForPlant('From');
	}
	else {
		 elements.fromWhse.enabled = true;
		 loadWarehousesForPlant('From');
	}
}
