/**
 * @properties={typeid:35,uuid:"8919AD80-4A67-41FB-A2C1-9DD853E91AE3",variableType:-4}
 */
var _bHideDeleteShuffleButtons = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"62684234-E82A-4B1F-B933-B3368EFD3ACA"}
 */
var sSetupUnits = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FE08038B-1DFF-404C-A6E3-21222CB76664"}
 */
var sSetupUnitsRollMode = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"443A7E88-13C4-4285-B4BF-34544CA0282E"}
 */
var sDiameterUnits = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"CA179F5F-C881-4788-B3E5-AE637D3A7118"}
 */
var _selectedSupplierID = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"F8D1063C-C49E-48A6-8202-D3F1B037597E",variableType:4}
 */
var _duplicateRecord = 0;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"B166B7EE-CD91-45EE-9238-5F6D6069DAC9",variableType:-4}
 */
var _tabMap = [i18n.getI18NMessage("avanti.lbl.standards"),
	i18n.getI18NMessage("avanti.lbl.impressionsPerHour"),
	i18n.getI18NMessage("avanti.lbl.sheetsPerHour"),
	i18n.getI18NMessage("avanti.lbl.machineVariable"),
	i18n.getI18NMessage("avanti.lbl.Folds"),
	i18n.getI18NMessage("avanti.lbl.SheeterConditions"),
	i18n.getI18NMessage("avanti.lbl.variableData"),
	i18n.getI18NMessage("avanti.lbl.speed"),
	i18n.getI18NMessage("avanti.lbl.speedByColor"),
	i18n.getI18NMessage("avanti.lbl.setup"),
	i18n.getI18NMessage("avanti.lbl.spoilage"),
	i18n.getI18NMessage("avanti.lbl.runSpoils"),
	i18n.getI18NMessage("avanti.lbl.MRSpoils"),
	i18n.getI18NMessage("avanti.lbl.coverDeck"),
	i18n.getI18NMessage("avanti.lbl.handFeeder"),
	i18n.getI18NMessage("avanti.lbl.cardFeeder"),
	i18n.getI18NMessage("avanti.lbl.faceTrim"),
	i18n.getI18NMessage("avanti.lbl.perfScore"),
	i18n.getI18NMessage("avanti.lbl.cylinders"),
	i18n.getI18NMessage("avanti.lbl.dies"),
	i18n.getI18NMessage("avanti.lbl.plates"),
	i18n.getI18NMessage("avanti.lbl.inks"),
	i18n.getI18NMessage("avanti.lbl.inkCoverageAdjust"),
	i18n.getI18NMessage("avanti.lbl.substrateAdjust"),
	i18n.getI18NMessage("avanti.lbl.caliperAdjust"),
	i18n.getI18NMessage("avanti.lbl.coatedPaperAdjust"),
	i18n.getI18NMessage("avanti.lbl.windowCountAdjust"),
	i18n.getI18NMessage("avanti.lbl.windowSizeAdjust"),
	i18n.getI18NMessage("avanti.lbl.windowOpenAdjust"),
	i18n.getI18NMessage("avanti.lbl.flatSizeAdjustment"),
	i18n.getI18NMessage("avanti.lbl.multiSheetFactors"),
	i18n.getI18NMessage("avanti.lbl.coverage"),
	i18n.getI18NMessage("avanti.lbl.perf"),
	i18n.getI18NMessage("avanti.lbl.score"),
	i18n.getI18NMessage("avanti.lbl.inkjetting"),
	i18n.getI18NMessage("avanti.lbl.numbering"),
	i18n.getI18NMessage("avanti.lbl.other1"),
	i18n.getI18NMessage("avanti.lbl.other2"),
	i18n.getI18NMessage("avanti.lbl.other3"),
	i18n.getI18NMessage("avanti.lbl.suppliers"),
	i18n.getI18NMessage("avanti.lbl.markup"),
	i18n.getI18NMessage("avanti.lbl.UDF"),
	i18n.getI18NMessage("avanti.lbl.costLinks"),
	i18n.getI18NMessage("avanti.lbl.pricing"),
	i18n.getI18NMessage("avanti.lbl.cartons"),
	i18n.getI18NMessage("avanti.lbl.skids"),
	i18n.getI18NMessage("avanti.lbl.materials"),
	i18n.getI18NMessage("avanti.lbl.spineGlue"),
	i18n.getI18NMessage("avanti.lbl.blankets"),
	i18n.getI18NMessage("avanti.lbl.coverMaterials"),
	i18n.getI18NMessage("avanti.lbl.backerMaterials"),
	i18n.getI18NMessage("avanti.lbl.ratesColors"),
	i18n.getI18NMessage("avanti.lbl.colorClicksCost"),
	i18n.getI18NMessage("avanti.lbl.blackWhiteClicksCost"),
	i18n.getI18NMessage("avanti.lbl.colorClicksPrice"),
	i18n.getI18NMessage("avanti.lbl.blackWhiteClicksPrice"),
	i18n.getI18NMessage("avanti.lbl.clicksCost"),
	i18n.getI18NMessage("avanti.lbl.clicksPrice"),
	i18n.getI18NMessage("avanti.lbl.costRate"),
	i18n.getI18NMessage("avanti.lbl.addToSpineThickness"),
	i18n.getI18NMessage("avanti.lbl.associatedTask"),
	i18n.getI18NMessage("avanti.lbl.runType"),
	i18n.getI18NMessage("avanti.lbl.email"),
	i18n.getI18NMessage("avanti.lbl.Divisions&Plants")];

/**
 * @author: Gary Dotzlaw, 2011-05-05
 * @description: Will add new speed records
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3077163E-7F30-4AE1-BED2-1B34E96D8A2D"}
 * @AllowToRunInFind
 */
function btnAdd (event) {
	// Only add a new record if in edit mode
	var sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_adj>} */ 
		rAdj,
		/** @type {JSFoundSet<db:/avanti/sa_task>}*/
		jsForm = forms[sForm].foundset,
		rRecRel1,
		rRecRel2,
		/*** @type {JSFoundSet<db:/avanti/sa_task_setup>} */
		fsSetup,
		/*** @type {JSFoundSet<db:/avanti/sa_task_spoil>} */
		fsSpoil,
		/*** @type {JSRecord<db:/avanti/sa_task_machine>} */
		rMachine,
		/** @type {JSRecord<db:/avanti/sa_task_paper>}*/
		rPaper,
		/** @type {JSRecord<db:/avanti/sa_task_speed>}*/
		rSpeed,
		/** @type {JSRecord<db:/avanti/sa_task_speed_sheetsperhour>}*/
		rSpeedSheets,
		/** @type {JSRecord<db:/avanti/sa_task_wash_ink>}*/
		rWash,
		iTaskTypeID = forms.sa_task_dtl.tasktype_id,
		i = 0,
		/** @type {JSFoundset<db:/avanti/sa_task_click_bw>}*/
		fsClickBw = datasources.db.avanti.sa_task_click_bw.getFoundSet(),
		/** @type {JSRecord<db:/avanti/sa_task_click_bw>}*/
		rClickBw = null,
		/** @type {JSFoundset<db:/avanti/sa_task_color>}*/
		fsColor = null,
		/** @type {JSRecord<db:/avanti/sa_task_run_types>}*/
		rRunType = null,
		/** @type {JSRecord<db:/avanti/sa_task_color>}*/
		rColor = null;

	if ( !scopes.avUtils.isNavModeReadOnly() ) {
		// Speed and spoils use a dialog to create new records
		if ( sForm.search("speed_01") > -1 
				|| sForm.search("adj_caliper") > -1 
				|| sForm.search("adj_ink") > -1
				|| sForm.search("cost_rate_01") > -1 
				|| sForm.search("cost_mrkup_01") > -1 
				|| sForm.search("speed_04") > -1 
				|| sForm.search("sa_task_spoilage") > -1 
				|| sForm.search("speed_05") > -1 
				|| sForm.search("spoils_01") > -1 
				|| sForm.search("spoils_02") > -1 
				|| sForm.search("spoils_05") > -1 
				|| sForm.search("spoils_14") > -1
				|| sForm.search("setup_06") > -1 
				|| sForm.search("spoils_16") > -1 
				|| sForm.search("task_machine_04") > -1 ) {
			forms.sa_task._taskForm = sForm;

			// New show window method
			//			var win = application.createWindow("taskCreateRecords", JSWindow.MODAL_DIALOG);
			//			win.title = i18n.getI18NMessage("avanti.dialog.taskCreateRecords_title");
			//			win.show("sa_task_dlg_createRecords");

			globals.DIALOGS.showFormInModalDialog(forms.sa_task_dlg_createRecords, -1, -1, 450, 350, " ", true, false, "taskCreateRecords", true);
		} 
		else if (sForm.search("sa_task_run_types") > -1) {
		    rRunType = jsForm.getRecord(jsForm.newRecord(false, true));
		    rRunType.sequence_nr = jsForm.getSize();
		}
		else if (sForm.search("adj_flat_size") > -1) {
            rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
            rAdj.sequence_nr = jsForm.getSize();
            rAdj.taskadj_type = scopes.globals.avTask_adjFlatSize;
        } 
		else if (sForm.search("adj_substrate") > -1) {
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjSubstrate;
		} 
		else if (sForm.search("adj_multi_sheet") > -1) {
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjMultiSheet;
		} 
		else if (sForm.search("adj_coated_paper") > -1) {
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjCoatedPaper;
		} 
		else if (sForm.search("adj_win_open") > -1) {
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjWindowOpen;
		} 
		else if (sForm.search("adj_win_count") > -1 ){
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjWindowCount;
		} 
		else if (sForm.search("adj_win_size") > -1) {
			rAdj = jsForm.getRecord(jsForm.newRecord(false, true));
			rAdj.sequence_nr = jsForm.getSize();
			rAdj.taskadj_type = scopes.globals.avTask_adjWindowSize;
		}
		// GD - 2013-04-19: Need to create spoils and setup records for this machine variable for handwork
		else if ( ( sForm.search("machine_03") > -1 
				|| sForm.search("machine_15") > -1 
				|| sForm.search("machine_16") > -1 
				|| sForm.search("machine_11") > -1 
				|| sForm.search("machine_17") > -1 
				|| sForm.search("machine_18") > -1 
				|| sForm.search("machine_19") > -1
				|| sForm.search("machine_22") > -1
				|| sForm.search("machine_23") > -1
				|| sForm.search("machine_24") > -1
				|| sForm.search("machine_25") > -1
		) && ( iTaskTypeID == 25 
				|| iTaskTypeID == 21 
				|| iTaskTypeID == scopes.avTask.TASKTYPEID.EdgeBinding 
				|| iTaskTypeID == 27 
				|| iTaskTypeID == 30 
				|| iTaskTypeID == 31 
				|| iTaskTypeID == 32 
				|| iTaskTypeID == 33 
				|| iTaskTypeID == 35
				|| iTaskTypeID === scopes.avTask.TASKTYPEID.OfflineCoating
				|| iTaskTypeID === scopes.avTask.TASKTYPEID.HighDieCutter
				|| iTaskTypeID === scopes.avTask.TASKTYPEID.PaddingFanningCarbonless
				|| iTaskTypeID === scopes.avTask.TASKTYPEID.Rewinder
		) ) {
			rMachine = jsForm.getRecord(jsForm.newRecord(false, true));
			rMachine.sequence_nr = jsForm.getSize();
			rMachine.task_id = forms.sa_task_dtl.task_id;

			fsSpoil = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task_spoil');
			rRecRel1 = fsSpoil.getRecord(fsSpoil.newRecord(false, true));
			rRecRel1.taskmachine_id = rMachine.taskmachine_id;
			rRecRel1.task_id = rMachine.task_id;
			rRecRel1.sequence_nr = forms[sForm].foundset.getSize();

			if (iTaskTypeID != scopes.avTask.TASKTYPEID.OfflineCoating) { // GD - Aug 12, 2015: coating needs no setup
				
				fsSetup = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task_setup');
				rRecRel2 = fsSetup.getRecord(fsSetup.newRecord(false, true));
				rRecRel2.taskmachine_id = rMachine.taskmachine_id;
				rRecRel2.task_id = rMachine.task_id;
				rRecRel2.sequence_nr = forms[sForm].foundset.getSize();
			}

			databaseManager.saveData();
		
		} else if (sForm.search("machine_07") > -1 
				&& iTaskTypeID === scopes.avTask.TASKTYPEID.GrandFormatPress) {
			
			rMachine = jsForm.getRecord(jsForm.newRecord(false, true));
			rMachine.sequence_nr = jsForm.getSize();
			rMachine.task_id = forms.sa_task_dtl.task_id;
			
			fsSetup = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task_setup');
			rRecRel2 = fsSetup.getRecord(fsSetup.newRecord(false, true));
			rRecRel2.taskmachine_id = rMachine.taskmachine_id;
			rRecRel2.task_id = rMachine.task_id;
			rRecRel2.sequence_nr = forms[sForm].foundset.getSize();
			
			databaseManager.saveData(rMachine);
			databaseManager.saveData(rRecRel2);
			
		} else if ( sForm.search("suppliers_01") > -1 ) {
			
			// GD - 2012-09-12: Need to add default suppliers using lookup window for outsourced task
			globals.svy_nav_showLookupWindow(event, "_selectedSupplierID", "Suppliers", "btnReturnSupplierID", 'addSupplierLookupFilter');
			
		} else if (sForm.search("sa_task_speed_13") > -1 ) { 
			
			// Create the record
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskspeed_for_click_cost = 1;
			
			fsColor = jsForm["sa_task_speed_to_sa_task.sa_task_to_sa_task_color"];
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}
			
			// For the click cost speed records, we also want to create the same record for price
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskspeed_for_click_cost = null;
			rSpeed.taskspeed_for_click_price = 1;
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}

			databaseManager.saveData();
			
		} else if (sForm.search("sa_task_speed_15") > -1 ) { 
			
			// Create the record
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskspeed_for_click_price = 1;
			
			fsColor = jsForm["sa_task_speed_to_sa_task.sa_task_to_sa_task_color"];
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}
			
			databaseManager.saveData();
			
		} else if (sForm.search("sa_task_speed_14") > -1 ) { 
			
			// Create the record
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskcutoff_id = scopes.avTask.selectedTaskCutoffID;
			rSpeed.taskspeed_for_click_cost = 1;
			
			fsColor = jsForm["sa_task_speed_to_sa_task.sa_task_to_sa_task_color"];
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}
			
			// For the click cost speed records, we also want to create the same record for price
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskcutoff_id = scopes.avTask.selectedTaskCutoffID;
			rSpeed.taskspeed_for_click_cost = null;
			rSpeed.taskspeed_for_click_price = 1;
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}
			
			databaseManager.saveData();
			
		} else if (sForm.search("sa_task_speed_16") > -1 ) { 
			
			// Create the record
			rSpeed = jsForm.getRecord(jsForm.newRecord(false, true));
			rSpeed.sequence_nr = jsForm.getSize();
			rSpeed.taskcutoff_id = scopes.avTask.selectedTaskCutoffID;
			rSpeed.taskspeed_for_click_price = 1;
			
			fsColor = jsForm["sa_task_speed_to_sa_task.sa_task_to_sa_task_color"];
			
			if (utils.hasRecords(fsColor)) {
				
				// Create the necessary click_bw records for all colors
				for ( i = 1; i <= fsColor.getSize(); i++ ) {
					
					rColor = fsColor.getRecord(i);
					
					rClickBw = fsClickBw.getRecord(fsClickBw.newRecord(false, true));
					
					rClickBw.taskcolor_id = rColor.taskcolor_id;
					rClickBw.taskspeed_id = rSpeed.taskspeed_id;
				}
			}
			databaseManager.saveData();
			
		} else if ( sForm.search("paper_01") > -1 ) {
			
			if ( forms[sForm].foundset.getSize() == 20 ) {
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.sheetSize_title"),
					i18n.getI18NMessage("avanti.dialog.sheetSize_msg"),
					i18n.getI18NMessage("avanti.dialog.ok"));

				return;
			}

			// Create the record
			rPaper = jsForm.getRecord(jsForm.newRecord(false, true));
			rPaper.sequence_nr = jsForm.getSize();

			databaseManager.saveData(rPaper);

			// Load the foundset
			jsForm.loadAllRecords();
			jsForm.sort("sequence_nr asc");

			// Load the form
			forms[sForm].controller.loadRecords(jsForm)

			// Make sure the tab is showing
			scopes.globals.avUtilities_tabSetSelectedIndex("sa_task_dtl", "tab1", 2);

		} else if ( sForm.search("paper_03") > -1 ) {
			
			// Create the record
			rPaper = jsForm.getRecord(jsForm.newRecord(false, true));
			rPaper.sequence_nr = jsForm.getSize();
			rSpeedSheets = rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.getRecord(rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.newRecord(false, true));

			databaseManager.saveData(rPaper);
			databaseManager.saveData(rSpeedSheets);
			
			
		} else if(sForm=='sa_task_machine_web_folds' || sForm=='sa_task_machine_sheeter_conditions'){
			rMachine = jsForm.getRecord(jsForm.newRecord(false, true));
			rMachine.sequence_nr = jsForm.getSize();
			rMachine.task_id = forms.sa_task_dtl.task_id;
			
			if(sForm=='sa_task_machine_web_folds'){
				rMachine.taskmachine_type = 'WF'
			}
			else if(sForm=='sa_task_machine_sheeter_conditions'){
				rMachine.taskmachine_type = 'SC'
			}
				
			databaseManager.saveData(rMachine);

			// Load the foundset
			jsForm.loadAllRecords();
			jsForm.sort("sequence_nr asc");

			// Load the form
			forms[sForm].controller.loadRecords(jsForm);
		}
		else {
			rWash = jsForm.getRecord(jsForm.newRecord(false, true));
			rWash.sequence_nr = jsForm.getSize();
			databaseManager.saveData(rWash);

			// Load the foundset
			jsForm.loadAllRecords();
			jsForm.sort("sequence_nr asc");

			// Load the form
			forms[sForm].controller.loadRecords(jsForm);
		}
	}
}

/**
 * @param {JSFoundSet<db:/avanti/ap_supplier>} fsSuppliers
 *
 * @return
 * @properties={typeid:24,uuid:"B1580765-AF47-4BFD-905A-5A632661724A"}
 */
function addSupplierLookupFilter(fsSuppliers) {
    fsSuppliers.addFoundSetFilterParam('supplier_active','=','1','SupplierLookupFilter');
    
    return fsSuppliers;
}

/**
 * Return from Supplier lookup window
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-08-21
 *
 *
 * @properties={typeid:24,uuid:"5EE4D12D-72FE-4C21-98EF-9D4FAD80DE7D"}
 */
function btnReturnSupplierID () {
	if ( _selectedSupplierID ) {
		var fs = forms.sa_task_dtl.sa_task_to_sa_task_supplier;
		var iMax = fs.getSize();

		fs.newRecord(false, true);

		var rSuppl = fs.getSelectedRecord();
		rSuppl.supplier_id = _selectedSupplierID;
		rSuppl.sequence_nr = iMax + 1;

		_selectedSupplierID = null;
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A94AE2EF-4BFA-48DD-9075-6FEC74BA5A76"}
 */
function btnShuffle (event) {
	// Only shuffle if in edit mode
	if ( scopes.avUtils.isNavModeReadOnly() ) {
		globals.avUtilities_shuffle(event);
	}
}

/**
 * @param {JSFoundset<db:/avanti/sa_task_worktype_press>} [fs] - optional foundset to use
 * 
 * @properties={typeid:24,uuid:"EFF0E599-2137-4B40-90B7-8C5468FA9E6A"}
 */
function setVL_taskPressPool(fs){
	
	var aReturn = [],
		aDisplay = [],
		i = 0,
		iMax = 0,
		rRec;
	
	if (!fs) fs = sa_task_worktype_section_to_sa_task_worktype_press;
	
	if ( utils.hasRecords(fs) ) {
		databaseManager.saveData(fs);
		fs.sort("sequence_nr asc");
		iMax = fs.getSize();
		for ( i = 1; i <= iMax; i++ ) {
			rRec = fs.getRecord(i);
	
			if (rRec && utils.hasRecords(rRec.sa_task_worktype_press_to_sa_task)){
				aDisplay.push(rRec.sa_task_worktype_press_to_sa_task.task_description);
				aReturn.push(rRec.worktypepres_id);
			}
		}
	}
	
	application.setValueListItems("avTaskWorkTypePressPool", aDisplay, aReturn);
}

/**
 * Shows the inline task tab
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-07
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"4DD8F19B-EC3C-4032-9C4D-E2ECE31A6F77"}
 */
function onDataChange_inline (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_inline = newValue;

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.associatedTask");
			
			// GD - Oct 27, 2014: Delete all associated task records
			if (utils.hasRecords(jsForm.sa_task_standard_to_sa_task) && utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_associated_tasks)){
				jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_associated_tasks.deleteAllRecords();
			}
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true

	//	var sForm = event.getFormName();
	//	var i;
	//
	//	if (oldValue != newValue)
	//	{
	//		if (newValue == 1)
	//		{
	//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_associated_tasks_01, i18n.getI18NMessage("avanti.lbl.associatedTask"), "sa_task_to_sa_task_associated_task", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
	//		}
	//		else
	//		{
	//			// Need to find which tab folder is
	//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
	//			{
	//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_associated_tasks_01")
	//				{
	//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
	//				}
	//			}
	//		}
	//	}
	//	return true;
}

/**

 * @protected
 * 
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that
 *
 * @return
 * @properties={typeid:24,uuid:"8519862C-2DA1-47B3-9A93-CC3FDE3A274C"}
 */
function onDataChange_custom_run_types(oldValue, newValue, event) {
    
    if ( oldValue != newValue ) {
        
        var sForm = event.getFormName();
        /** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
        var jsForm = forms[sForm].foundset;

        jsForm.taskstd_custom_run_types = newValue;

        if ( newValue == 0 ) {
            // Remove the tab
            globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.runType");
            
            // GD - Oct 27, 2014: Delete all associated run type records
            if (utils.hasRecords(jsForm.sa_task_standard_to_sa_task) && utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_run_types)){
                jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_run_types.deleteAllRecords();
            }
        } else {
            // Add the tab
            forms.sa_task_dtl.buildDetailView();
        }

        forms[sForm].refreshUI();
    }

    return true
}

/**
 * <AUTHOR> Dotzlaw
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"60B96805-2931-4282-9C75-182F64F92A6D"}
 */
function onDataChange_clickPricing (oldValue, newValue, event) {
	
	if ( oldValue != newValue ) {
		
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_inline = newValue;
		
		if ( newValue == 0 ) {
			
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.associatedTask");
			
			// GD - Oct 27, 2014: Delete all associated task records
			if (utils.hasRecords(jsForm.sa_task_standard_to_sa_task) && utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_associated_tasks)){
				jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_associated_tasks.deleteAllRecords();
			}
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
		
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"C56782C1-63D5-4346-B230-F2C575678C46"}
 */
function onDataChange_perfScore (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_perf_score = newValue;

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.perfScore");
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true

	//	var i;
	//	if (oldValue != newValue)
	//	{
	//		if (newValue == 1)
	//		{
	//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_perfScore_01, i18n.getI18NMessage("avanti.lbl.perfScore"), "sa_task_to_sa_task_perf", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
	//		}
	//		else
	//		{
	//			// Need to find which tab folder is
	//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
	//			{
	//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_perfScore_01")
	//				{
	//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
	//				}
	//			}
	//		}
	//	}
	//
	//	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"740EE70A-81EA-480E-8836-63B926B6C14D"}
 */
function onDataChange_materialsNeeded(oldValue, newValue, event) {
    
    if (oldValue != newValue) {
        
        var sForm = event.getFormName();
        /** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
        var jsForm = forms[sForm].foundset;

        jsForm.taskstd_materials_needed = newValue;
        jsForm.taskstd_spine_glue = 0;
        /// sl-3106 - delete material, otherwise it causes problems downstream, even tho 'mat needed' is off
        if (utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_material)) {
            jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_material.deleteAllRecords()
        }

        if (newValue == 0) {
            // Remove the tab
            globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.materials");

        }
        else {
            // Add the tab
            forms.sa_task_dtl.buildDetailView();
        }

        forms[sForm].refreshUI();
    }

    return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"A787BD8D-1710-4A98-9749-05D301CC6AAF"}
 */
function onDataChange_speedUnit (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		// Add the tab
		forms.sa_task_dtl.buildDetailView();
	}

	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D5FB202D-477D-49C6-A740-E3FF5E11939A"}
 */
function onDataChange_variableData (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		/*** @type {Form} */
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var fsForm = forms[sForm].foundset;

		fsForm.taskstd_variable_data = newValue;

		databaseManager.saveData(fsForm);

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.setup");
			forms[sForm].elements['fldTime'].visible = true;
			forms[sForm].elements['lblSetupMins'].visible = true;
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
			forms[sForm].elements['fldTime'].visible = false;
			forms[sForm].elements['lblSetupMins'].visible = false;
		}

		forms[sForm].refreshUI();
	}

	return true
}

/**
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"C810F6F2-6B05-464E-ABAB-950EA05D9313"}
 */
function onDataChange_standardAmountAddedToSpineThickness (oldValue, newValue, event) {
	
	/*** @type {Form} */
	var sForm = event.getFormName();
	/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
	var fsForm = forms[sForm].foundset;

	fsForm.taskstd_std_spine_thickness = newValue;
	
	if ( newValue == 1 ) {
		
		// Remove the tab
		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.addToSpineThickness");

	} else {
		
		// Add the tab
		forms.sa_task_dtl.buildDetailView();
	}
	
	forms[sForm].refreshUI();
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"39C7F060-57A3-4A99-8672-45A9B0335878"}
 */
function onDataChange_pricingMethod (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		/*** @type {Form} */
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var fsForm = forms[sForm].foundset;

		fsForm.taskstd_click_pricing_method = newValue;

		databaseManager.saveData(fsForm);

		if ( newValue == "M" ) {

			//			// Remove the tab
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.clicksPrice");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.colorClicksPrice");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.blackWhiteClicksPrice");

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		} else {

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"9BB2EABC-3C47-459A-9D25-D82925384347"}
 */
function onDataChange_clickCalcBasedOn (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		/*** @type {Form} */
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var fsForm = forms[sForm].foundset;

		fsForm.taskstd_click_calc_based_on = newValue;

		databaseManager.saveData(fsForm);

		if (fsForm.taskstd_click_calc_based_on == scopes.avTask.TASK_CLICK_CALC_BASED_ON.Impressions) {

			forms[sForm].elements.taskstd_pricing_model.visible = true;
			forms[sForm].elements.taskstd_click_calc_total_imp.visible = true;
		} 
		else if ((fsForm.taskstd_click_calc_based_on == scopes.avTask.TASK_CLICK_CALC_BASED_ON.LinearFeet
				|| fsForm.taskstd_click_calc_based_on == scopes.avTask.TASK_CLICK_CALC_BASED_ON.Meters)) {

			forms[sForm].elements.taskstd_pricing_model.visible = false;
			forms[sForm].elements.taskstd_click_calc_total_imp.visible = false;
		}

		forms.sa_task_dtl.foundset.sa_task_to_sa_task_impressions.omitRecord(1);
		forms.sa_task_dtl.foundset.sa_task_to_sa_task_impressions.deleteAllRecords();
		forms.sa_task_dtl.foundset.sa_task_to_sa_task_impressions.loadOmittedRecords();	
		
		forms.sa_task_dtl.refreshUI(null);
	}

	return true;
}

/**
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"175AE9F6-B429-4E9F-9B05-ADFB55B83B45"}
 */
function onDataChange_numberOfColors (oldValue, newValue, event) {
	
	var 
		sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_standard>} */ 
		rStd = forms[sForm].foundset.getSelectedRecord(),
		rTask = rStd.sa_task_standard_to_sa_task.getRecord(1),
		fsColor = rTask.sa_task_to_sa_task_color,
		rColor = null,
		i = 0,
		iStart = 0;
	
	if (newValue <= 10) {
		
		// GD - Oct 24, 2016: If we have black & white selected, then we should only have one color
		if (rStd.taskstd_color_type == "B") {
			
			if (newValue != 1) {
				
				newValue = 1;
			}
		}
		
		rStd.taskstd_color_diff_rate = newValue;
		
		// Adjust the colors table	
		if (utils.hasRecords(fsColor) 
				&& fsColor.getSize() > newValue) {
			
			// we already have too many records; delete some
			for ( i = fsColor.getSize(); i > newValue; i-- ) {
				
				fsColor.deleteRecord(i);
			}
		} else if (utils.hasRecords(fsColor) 
				&& fsColor.getSize() > 0) {
			
			// we do not have enough records, add some
			iStart = fsColor.getSize() + 1;
			
			for ( i = iStart; i <= newValue; i++ ) {
				
				rColor = fsColor.getRecord(fsColor.newRecord(false, true));
				
				rColor.taskcolor_nr = i;
				
				addClickRec(rColor.taskcolor_id);			
			}
		} else {
			
			// we have no color records yet, add them all
			for ( i = 1; i <= newValue; i++ ) {
				
				rColor = fsColor.getRecord(fsColor.newRecord(false, true));
				
				rColor.taskcolor_nr = i;
				
				addClickRec(rColor.taskcolor_id);			
			}
		}

		databaseManager.saveData();
		
	} else {
		
		rStd.taskstd_color_diff_rate = oldValue;
		
	}
	
	// For each color record, we now need to make sure we create a click_bw record for every speed record
	/*
	 * @param {UUID} sColorID
	 */
	function addClickRec(sColorID) {
		
		var j = 0,
			k = 0,
			fsSpeed = rStd.sa_task_standard_to_sa_task.sa_task_to_sa_task_speed,
			rSpeed = null,
			fsCutoff = rTask.sa_task_to_sa_task_cutoff,
			rCutoff = null,
			rClick = null;
		
		if (sForm == "sa_task_standards_digitalSheet") {
			
			for ( j = 1; j <= fsSpeed.getSize(); j++ ) {
				
				rSpeed = fsSpeed.getRecord(j);
				
				rClick = rSpeed.sa_task_speed_to_sa_task_click_bw.getRecord(rSpeed.sa_task_speed_to_sa_task_click_bw.newRecord(false, true));
				rClick.taskcolor_id = sColorID;
			}
		} else {
			
			// Digital web
			for ( j = 1; j <= fsCutoff.getSize(); j++ ) {
				
				rCutoff = fsCutoff.getRecord(j);
				
				if (utils.hasRecords(rCutoff.sa_task_cutoff_to_sa_task_speed)) {
					
					fsSpeed = rCutoff.sa_task_cutoff_to_sa_task_speed;
					
					for ( k = 1; k <= fsSpeed.getSize(); k++ ) {
						
						rSpeed = fsSpeed.getRecord(k);
						
						rClick = rSpeed.sa_task_speed_to_sa_task_click_bw.getRecord(rSpeed.sa_task_speed_to_sa_task_click_bw.newRecord(false,true));
						rClick.taskcolor_id = sColorID;
					}
				}
			}
		}
		
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"79ABD78A-BFEA-4116-AC0E-3B3AEFC80525"}
 */
function onDataChange_colorType (oldValue, newValue, event) {
	
	/*** @type {Form} */
	var sForm = event.getFormName();
	/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
	var jsForm = forms[sForm].foundset;
	
	// If we are running sheets per hour, then we are using color model so this value cannot be changed
	if (jsForm.taskstd_speed_unit === 1 ) newValue = oldValue;
	
	if ( oldValue != newValue ) {
		

		if ( newValue == "B" ) {
			
			jsForm.taskstd_color_diff_rate = 0;
			
			// GD - Oct 24, 2016: SL-8295: If we are using sheets per hour, then set the number or color rates = 1 and regenerate the color records
			if (jsForm.taskstd_color_diff_rate != 1 && jsForm.taskstd_speed_unit == 1) {
				
				jsForm.taskstd_color_diff_rate = 1;
				
				forms[sForm].onDataChange_numberOfColors (null, 1, event);
			}

			//			// Remove the tab
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.colorClicksPrice");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.colorClicksPrice");

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
			
		} else {
			
			// GD - Oct 26, 2016: If we are using custom colors, then make sure we have a speed_sheetsPerHour record
			if ( newValue == "S") {
				
				if (!utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_speed_sheetsperhour)) {
					
					jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_speed_sheetsperhour.newRecord();
				}
				jsForm.taskstd_color_diff_rate = 2;
				
			} else {
			
				jsForm.taskstd_color_diff_rate = 0;
				
			}

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true;
}

/**
 * Adds a Variable Data tab
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-23
 *
 * @param {String} sForm - paramDescription

 *
 *
 * @properties={typeid:24,uuid:"A631DAEC-AFB6-45AB-8949-612A25E339C0"}
 */
function addVariableTab (sForm) {
	if ( !sForm ) return;

	/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
	var jsForm = forms[sForm].foundset;
	var sTabForm;
//	var i;
	if ( sForm == "sa_task_standards_digitalSheet" ) {
		sTabForm = "sa_task_setup_03";
	} else {
		sTabForm = "sa_task_setup_02";
	}

//	var bFoundTab = false;

	if ( jsForm.taskstd_variable_data == "1" ) {
		// Add the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", sTabForm, "avanti.lbl.setup", "sa_task_to_sa_task_setup", _tabMap);
	} else {
		// Remove the tab
		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", sTabForm);
	}

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"B53D98F9-AEAD-4105-ABAC-905C5091CB4F"}
 */
function onDataChange_cutoffNr (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		iCutoffs = newValue,
		/**@type{JSFoundSet<db:/avanti/sa_task_cutoff>} ***/
		fs,
		rRec,
		iExist = 0,
		i = 0;

	fs = forms[sForm].sa_task_standard_to_sa_task.sa_task_to_sa_task_cutoff;
	
	if ( iCutoffs > 0 ) {
		
		if ( utils.hasRecords(fs) ) {
			
			iExist = fs.getSize();
	
			// Do we need to delete some records?
			if ( iCutoffs < iExist ) {
				for ( i = iCutoffs + 1; i <= iExist; i++ ) {
					fs.deleteRecord(iCutoffs + 1);
				}
			}
	
			// Do we need to add some records?
			if ( iCutoffs > iExist ) {
				
				for ( i = iExist + 1; i <= iCutoffs; i++ ) {
					
					rRec = fs.getRecord(fs.newRecord(false, true));
					rRec.sequence_nr = i;
					rRec.taskcutoff_nr = i;
				
				}
				fs.setSelectedIndex(iExist + 1);
			}
		} else {
			
			for ( i = 1; i <= iCutoffs; i++ ) {
				
				rRec = fs.getRecord(fs.newRecord(false, true));
				rRec.sequence_nr = i;
				rRec.taskcutoff_nr = i;
			}
		}
	}
	
	databaseManager.saveData(fs);
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"EB141212-A22C-41D4-84A4-F646A6A09AD3"}
 */
function onDataChange_turningBars (oldValue, newValue, event) {
	var sForm = "";
	
	sForm = event.getFormName();
	forms[sForm].refreshUI();

	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 * 
 * @properties={typeid:24,uuid:"A52A78E5-59A1-4F99-A02C-BD680508FC76"}
 */
function onDataChange_nrTurningBars (oldValue, newValue, event) {

	var sForm = "",
		iTurningBars = newValue,
		/**@type{JSFoundSet<db:/avanti/sa_task_turning_bar>} ***/
		fs,
		rRec,
		iExist = 0,
		i = 0;

	sForm = event.getFormName();

	fs = forms[sForm].sa_task_standard_to_sa_task.sa_task_to_sa_task_turning_bar;

	if ( iTurningBars > 0 ) {
		if ( utils.hasRecords(fs) ) {
			iExist = fs.getSize();

			// Do we need to delete some records?
			if ( iTurningBars < iExist ) {
				for ( i = iTurningBars + 1; i <= iExist; i++ ) {
					fs.deleteRecord(iTurningBars + 1);
				}
			}

			// Do we need to add some records?
			if ( iTurningBars > iExist ) {
				for ( i = iExist + 1; i <= iTurningBars; i++ ) {
					rRec = fs.getRecord(fs.newRecord(false, true));
					rRec.sequence_nr = i;
					rRec.taskturn_nr = i;
				}
				fs.setSelectedIndex(iExist + 1);
			}
		} else {
			for ( i = 1; i <= iTurningBars; i++ ) {
				rRec = fs.getRecord(fs.newRecord(false, true));
				rRec.sequence_nr = i;
				rRec.taskturn_nr = i;
			}
		}
	}

	databaseManager.saveData(fs);

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"CE111EA7-06C2-4CC6-8756-D4D05BC5CA9E"}
 */
function onDataChange_numberOfCoaters (oldValue, newValue, event) {
	var sForm = "",
		iCoatingUnits = newValue,
		/**@type{JSFoundSet<db:/avanti/sa_task_standard>} ***/
		fs,
		rRec,
		iExist = 0,
		i = 0;

	sForm = event.getFormName();
	fs = forms[sForm].sa_task_standard_to_sa_task.sa_task_to_sa_task_coater_unit;

	if ( iCoatingUnits > 0 ) {
		if ( utils.hasRecords(fs) ) {
			iExist = fs.getSize();
			databaseManager.saveData(fs);
			fs.sort('taskcoatunit_nr asc');

			// Do we need to delete some records?
			if ( iCoatingUnits < iExist ) {
				for ( i = iCoatingUnits + 1; i <= iExist; i++ ) {
					fs.deleteRecord(iCoatingUnits + 1);
				}
			}

			// Do we need to add some records?
			if ( iCoatingUnits > iExist ) {
				for ( i = iExist + 1; i <= iCoatingUnits; i++ ) {
					rRec = fs.getRecord(fs.newRecord(false, true));
					rRec.sequence_nr = i;
					rRec.taskcoatunit_nr = i;
				}
				fs.setSelectedIndex(iExist + 1);
			}
			
			// fix any incorrect sequence_nrs
			for ( i = 1; i <= fs.getSize(); i++ ) {
				rRec = fs.getRecord(i);
				rRec.sequence_nr = i;
				rRec.taskcoatunit_nr = i;
			}
		} 
		else {
			for ( i = 1; i <= iCoatingUnits; i++ ) {
				rRec = fs.getRecord(fs.newRecord(false, true));
				rRec.sequence_nr = i;
				rRec.taskcoatunit_nr = i;
			}
		}
	}

	databaseManager.saveData(fs);

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"AD4C23DB-C710-4739-A257-15002A2C96DC"}
 */
function onDataChange_variableCutoff (oldValue, newValue, event) {

	var sForm;

	if ( !event ) return true;
	sForm = event.getFormName();
	
	// GD - Oct 26, 2016: Remove any excess cutoff records
	if (newValue == 0) {
		
		var 
			/** @type {JSRecord<db:/avanti/sa_task_standard>} */
			rStd = forms[sForm].foundset.getSelectedRecord(),
			fsCutoff = rStd.sa_task_standard_to_sa_task.sa_task_to_sa_task_cutoff,
			i = 0;
	
		if (utils.hasRecords(fsCutoff)) {
				
			for ( i = fsCutoff.getSize(); i > 1; i-- ) {
				
				fsCutoff.deleteRecord(i);
			}
		}
		
		rStd.taskstd_cutoff_nr = 1;
	}
	
	forms[sForm].refreshUI();

	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}

 *
 * @properties={typeid:24,uuid:"D679CC42-469D-4E40-9DC7-56EDD3E04F37"}
 * @AllowToRunInFind
 */
function onDataChange_calcType (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_calc_type = newValue;

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.speed");
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true
	//
	//
	//	var i;
	//	if (oldValue != newValue)
	//	{
	//		forms.sa_task._calcType = newValue;
	//
	//		// Find the current speed tab
	//		var iIndex;
	//		for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
	//		{
	//			if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i).search("speed") > -1)
	//			{
	//				iIndex = i;
	//				break;
	//			}
	//		}
	//
	//		// Add the appropriate speed tab
	//		if (iIndex > 0)
	//		{
	//			scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", iIndex);
	//			if (newValue == "I")
	//			{
	//				scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_speed_01, i18n.getI18NMessage("avanti.lbl.speed"), "sa_task_to_sa_task_speed", null, null, iIndex - 1, null);
	//			}
	//			else
	//			{
	//				scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_speed_04, i18n.getI18NMessage("avanti.lbl.speed"), "sa_task_to_sa_task_speed", null, null, iIndex - 1, null);
	//			}
	//		}
	//	}
	//	return true
}

/**
 * Handles the change to the number of inks used
 *
 * @param {Number} iNewInks - number of inks records to create
 * @param {String} sForm -  the form to use for the # of inks
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"17A8E12C-B156-4EB1-A040-8092A281C10E"}
 */
function onDataChange_numberOfInks (iNewInks, sForm) {
	var i;
	if ( !iNewInks || !sForm ) return false

	// Check to see if there are any records
	var fsInks = forms.sa_task_dtl.sa_task_to_sa_task_ink;

	if ( utils.hasRecords(fsInks) ) {
		var iInks = fsInks.getSize();

		// Do we need to delete some records?
		if ( iNewInks < iInks ) {
			for ( i = iNewInks + 1; i <= iInks; i++ ) {
				fsInks.deleteRecord(iNewInks + 1);
			}
		}

		// Do we need to add some records?
		if ( iNewInks > iInks ) {
			for ( i = iInks + 1; i <= iNewInks; i++ ) {
				fsInks.newRecord(false, true);
				//				fsInks.task_id = globals.avBase_selectedTaskID;
				databaseManager.saveData(fsInks.getSelectedRecord());
			}
			fsInks.setSelectedIndex(iInks + 1);
		}
	} else if ( globals.avBase_selectedTaskID ) {
		// Add the records
		for ( i = 1; i <= iNewInks; i++ ) {
			fsInks.newRecord(false, true);
			//			fsInks.task_id = globals.avBase_selectedTaskID;
			databaseManager.saveData(fsInks.getSelectedRecord());
		}
	}

	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"23A4F000-F9CD-40F1-A246-EDB725361F60"}
 */
function onDataChange_specialInks (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_special_inks = newValue;

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.inks");

			jsForm.taskstd_nr_inks = 0;
			if ( utils.hasRecords(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink) ) {
				//				jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink.deleteAllRecords();

				jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink.loadAllRecords();
				if ( jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink.getSize() > 199 ) jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink.getRecord(databaseManager.getFoundSetCount(jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink));
				jsForm.sa_task_standard_to_sa_task.sa_task_to_sa_task_ink.deleteAllRecords();
			}
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true
}

/**
 * Handles changed data
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-03-12
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"8A1143B3-056C-47EF-9DAC-CD94FEC115D4"}
 */
function onDataChange_customerPricing (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.pricing");
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3AE88BEF-70DA-4616-8E87-6C58DA3FE18B"}
 */
function onDataChange_costPerPiecePricing (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.costRate");
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"EAAD351C-03AD-46E0-A6BB-64DDA4BC7F77"}
 */
function onDataChange_differentRate (oldValue, newValue, event) {

	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		if (jsForm.taskstd_color_type == "B" && oldValue == 0) {
			
			jsForm.taskstd_color_diff_rate = oldValue;
			return true;
		}
		
		jsForm.taskstd_color_diff_rate = newValue;

		if ( newValue == 0 ) {
			//			// Remove the tab
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.clicksCost");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.clicksPrice");

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		} else {
			//			// Remove the tab
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.colorClicksCost");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.blackWhiteClicksCost");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.colorClicksPrice");
			//			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.blackWhiteClicksPrice");

			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}

		forms[sForm].refreshUI();
	}

	return true;
}

/**
 * Creates a Data Grid for task clicks, speeds, or spoilage based on the specified parameters
 *
 * <AUTHOR> Dotzlaw
 * @since 2023-10-20
 * @param {String} sForm - The form the click happened on
 * @param {String} sType - Type of grid to create ("singleRateCost", "multipleRateCost", "spoilage_digital", etc.)
 *
 * @properties={typeid:24,uuid:"5DD8D703-836F-47E2-9785-FF2592C5739A"}
 * @AllowToRunInFind
 */
function cloneTaskForm (sForm, sType) {
	
	/*** @type {JSForm} */
	var jsForm;
	/*** @type {JSForm} */
	var jsForm1;
	/*** @type {JSForm} */
	var jsForm2;
	/*** @type {JSForm} */
	var jsForm3;
	/*** @type {JSWebComponent} */
	var jsLabel;
	var bCheck = false;
	var baseForm = "sa_task_clicks";
	
	if (sForm == "sa_task_standards_digitalWeb"
			&& forms[sForm].foundset.taskstd_click_calc_based_on
			&& forms[sForm].foundset.taskstd_click_calc_based_on != scopes.avTask.TASK_CLICK_CALC_BASED_ON.Impressions) {
		baseForm = "sa_task_clicks_linear";
		_bHideDeleteShuffleButtons = true;
	}
	else {
		_bHideDeleteShuffleButtons = false;
	}

	//	application.output("Entering the cloneTaskForm method...");

	if ( !sForm || !sType ) return;

	if ( sType == "multipleRateCost" ) {
		
		jsForm = solutionModel.getForm(baseForm);

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_single_clicks_cost");
		cloneTaskForm_removeClone("sa_task_single_clicks_price");
		cloneTaskForm_removeClone("sa_task_clicks_bw_cost");
		cloneTaskForm_removeClone("sa_task_clicks_color_cost");

		// Check to see if the clone exits, if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_clicks_bw_cost");
		if ( !bCheck ) {
			jsForm2 = solutionModel.cloneForm("sa_task_clicks_bw_cost", jsForm);
			cloneTaskForm_addLabelsFields(jsForm2, "bw", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_clicks_bw' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_clicks_bw_cost", "avanti.lbl.blackWhiteClicksCost", "sa_task_to_sa_task_impressions", _tabMap);

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_clicks_color_cost");
		if ( !bCheck ) {
			jsForm1 = solutionModel.cloneForm("sa_task_clicks_color_cost", jsForm);
			cloneTaskForm_addLabelsFields(jsForm1, "color", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_clicks_color' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_clicks_color_cost", "avanti.lbl.colorClicksCost", "sa_task_to_sa_task_impressions", _tabMap);

		//		bCheck = cloneTaskForm_checkForCloneTab("sa_task_clicks_bw");
		//		if (!bCheck)
		//		{
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_clicks_bw", i18n.getI18NMessage("avanti.lbl.blackWhiteClicks"), "sa_task_to_sa_task_impressions", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//			application.output("Running cloneTaskForm()...'sa_task_clicks_bw' did not exist as a tab and has been added");
		//		}
	} else if ( sType == "multipleRatePrice" ) {
		
		jsForm = solutionModel.getForm(baseForm);

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_single_clicks");
		cloneTaskForm_removeClone("sa_task_clicks_bw_price");
		cloneTaskForm_removeClone("sa_task_clicks_color_price");

		// Check to see if the clone exits, if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_clicks_bw_price");
		if ( !bCheck ) {
			jsForm2 = solutionModel.cloneForm("sa_task_clicks_bw_price", jsForm);
			cloneTaskForm_addLabelsFields(jsForm2, "bwPrice", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_clicks_bw' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_clicks_bw_price", "avanti.lbl.blackWhiteClicksPrice", "sa_task_to_sa_task_impressions", _tabMap);

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_clicks_color_price");
		if ( !bCheck ) {
			jsForm1 = solutionModel.cloneForm("sa_task_clicks_color_price", jsForm);
			cloneTaskForm_addLabelsFields(jsForm1, "colorPrice", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_clicks_color' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_clicks_color_price", "avanti.lbl.colorClicksPrice", "sa_task_to_sa_task_impressions", _tabMap);

	} else if (sType == "singleRateCostSheetsPerHour") {
		
		jsForm = solutionModel.getForm('sa_task_clicks_02');
		
		cloneTaskForm_removeClone("sa_task_single_clicks_cost");
		
		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_cost");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_cost", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "bwSheetsPerHour", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
		}
		
		scopes.globals.avUtilities_tabRemoveAll("sa_task_clicks_cost_sheetsPerHour_dtl", "tabClicks");
		
		scopes.globals.avUtilities_tabAdd("sa_task_clicks_cost_sheetsPerHour_dtl", "tabClicks", "sa_task_single_clicks_cost", null, "_to_sa_task_click_bw$selectedtaskspeedid", null, null, null, null);	
		
	} else if (sType == "singleRatePriceSheetsPerHour" ) {
		
		jsForm = solutionModel.getForm('sa_task_clicks_02');
		
		cloneTaskForm_removeClone("sa_task_single_clicks_price");
		
		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_price");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_price", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "bwPriceSheetsPerHour", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
		}
		
		scopes.globals.avUtilities_tabRemoveAll("sa_task_clicks_price_sheetsPerHour_dtl", "tabClicks");
		
		scopes.globals.avUtilities_tabAdd("sa_task_clicks_price_sheetsPerHour_dtl", "tabClicks", "sa_task_single_clicks_price", null, "_to_sa_task_click_bw$selectedtaskspeedid", null, null, null, null);	
				
	} else if (sType == "singleRateCostDigitalWebCustom") {
		
		jsForm = solutionModel.getForm('sa_task_clicks_03');
		
		cloneTaskForm_removeClone("sa_task_single_clicks_cost");
		
		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_cost");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_cost", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "bwDigitalSheetCustom", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
		}
		
		scopes.globals.avUtilities_tabRemoveAll("sa_task_clicks_cost_digitalWebCustom_dtl", "tabClicks");
		
		scopes.globals.avUtilities_tabAdd("sa_task_clicks_cost_digitalWebCustom_dtl", "tabClicks", "sa_task_single_clicks_cost", null, "_to_sa_task_click_bw$selectedtaskspeedid", null, null, null, null);	
		
	} else if (sType == "singleRatePriceDigitalWebCustom" ) {
			
			jsForm = solutionModel.getForm('sa_task_clicks_03');
			
			cloneTaskForm_removeClone("sa_task_single_clicks_price");
			
			// Check to see if the clones exits, and if not, create it
			bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_price");
			if ( !bCheck ) {
				jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_price", jsForm);
				cloneTaskForm_addLabelsFields(jsForm3, "bwPriceDigitalSheetCustom", sForm);
				//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
			}
			
			scopes.globals.avUtilities_tabRemoveAll("sa_task_clicks_price_digitalWebCustom_dtl", "tabClicks");
			
			scopes.globals.avUtilities_tabAdd("sa_task_clicks_price_digitalWebCustom_dtl", "tabClicks", "sa_task_single_clicks_price", null, "_to_sa_task_click_bw$selectedtaskspeedid", null, null, null, null);	
					
		} else if ( sType == "singleRateCost" ) {
		
		jsForm = solutionModel.getForm('sa_task_clicks');

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_single_clicks_cost");
		cloneTaskForm_removeClone("sa_task_clicks_bw_cost");
		cloneTaskForm_removeClone("sa_task_clicks_color_cost");
		cloneTaskForm_removeClone("sa_task_clicks_bw_price");
		cloneTaskForm_removeClone("sa_task_clicks_color_price");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_cost");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_cost", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "bw", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_single_clicks_cost", "avanti.lbl.clicksCost", "sa_task_to_sa_task_impressions", _tabMap);

	} else if ( sType == "singleRatePrice" ) {
		
		jsForm = solutionModel.getForm('sa_task_clicks');

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_single_clicks_price");
		cloneTaskForm_removeClone("sa_task_clicks_bw_cost");
		cloneTaskForm_removeClone("sa_task_clicks_color_cost");
		cloneTaskForm_removeClone("sa_task_clicks_bw_price");
		cloneTaskForm_removeClone("sa_task_clicks_color_price");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_single_clicks_price");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_single_clicks_price", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "bwPrice", sForm);
			//			application.output("Running cloneTaskForm()...'sa_task_single_clicks' did not exist and has been added");
		}

		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_single_clicks_price", "avanti.lbl.clicksPrice", "sa_task_to_sa_task_impressions", _tabMap);

	} else if ( sType == "spoilage_digital" ) {
		
		jsForm = solutionModel.getForm('sa_task_speed_06');
		
		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_spoilage");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_spoilage");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_spoilage", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "spoils", sForm);
			
			//			application.output("Running cloneTaskForm()...'sa_task_spoilage' did not exist and has been added");
		}
		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_spoilage", "avanti.lbl.spoilage", "sa_task_to_sa_task_speed", _tabMap);
		
		// sl-5821 - label explaining spoils vals are pcts
		forms["sa_task_spoilage"].elements.lblSpoilsArePercent.visible = true

	} else if ( sType == "spoilage_digital_noclicks" ) {
		
		jsForm = solutionModel.getForm('sa_task_speed_06');
		
		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_spoilage");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_spoilage");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_spoilage", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "spoils", sForm);
			
			//			application.output("Running cloneTaskForm()...'sa_task_spoilage' did not exist and has been added");
		}
		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_spoilage", "avanti.lbl.spoilage", "sa_task_to_sa_task_speed_noclicks", _tabMap);
		
		// sl-5821 - label explaining spoils vals are pcts
		forms["sa_task_spoilage"].elements.lblSpoilsArePercent.visible = true

	} else if ( sType == "wideFormatSpeed" ) {
		
		jsForm = solutionModel.getForm('sa_task_speed_07');

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_wideFormat_speed");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_wideFormat_speed");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_wideFormat_speed", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "wideFormatSpeed", sForm);
		}
		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_wideFormat_speed", "avanti.lbl.speed", "sa_task_to_sa_task_setup", _tabMap);
		
	} else if ( sType == "grandFormatSpeed" ) {
		
		jsForm = solutionModel.getForm('sa_task_speed_11');

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_grandFormat_speed");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_grandFormat_speed");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_grandFormat_speed", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "grandFormatSpeed", sForm);
		}
		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_grandFormat_speed", "avanti.lbl.speed", "sa_task_to_sa_task_speed", _tabMap);
		
	} else if ( sType == "proofingSpeed" ) {
		
		jsForm = solutionModel.getForm('sa_task_speed_08');

		// Remove any old forms
		cloneTaskForm_removeClone("sa_task_proofing_speed");

		// Check to see if the clones exits, and if not, create it
		bCheck = cloneTaskForm_checkForClone("sa_task_proofing_speed");
		if ( !bCheck ) {
			jsForm3 = solutionModel.cloneForm("sa_task_proofing_speed", jsForm);
			cloneTaskForm_addLabelsFields(jsForm3, "proofingSpeed", sForm);
		}
		// We need add the clone to the tab
		globals.avUtilities_tabAdd("sa_task_dtl", "tab1", "sa_task_proofing_speed", "avanti.lbl.speed", "sa_task_to_sa_task_setup", _tabMap);
	}

	/**
	 * Add the labels and fields to the form being cloned
	 * @param {JSForm} jsFormClone - the form to work with
	 * @param {String} sCloneType - "color" or "black & white"
	 * @param {String} sTaskForm - form to work with
	 * @returns {JSForm} returns the modified form
	 */
	function cloneTaskForm_addLabelsFields (jsFormClone, sCloneType, sTaskForm) {

		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var fsImp = forms[sTaskForm].foundset;
		var sField;
		var i;
		var sRel;
		if ( sTaskForm.search("paper") > -1 ) {
			fsImp = forms[sTaskForm].foundset;
			sField = "taskpaper_sheet_size";
		} else if ( sCloneType.search("SheetsPerHour") > - 1 ) {
			fsImp = forms.sa_task_dtl.sa_task_to_sa_task_paper;
			sField = "taskpaper_sheet_size";
		} else if ( sTaskForm.search("sa_task_standards_wide") > -1 || sTaskForm.search("sa_task_standards_grandFormat") > -1) {
			fsImp = forms.sa_task_dtl.sa_task_to_sa_task_machine;
			sField = "taskmachine_description";
		} else if ( sTaskForm.search("sa_task_standards_proofing") > -1 ) {
			fsImp = forms.sa_task_dtl.sa_task_to_sa_task_machine;
			sField = "taskmachine_description";
		} else if ( sTaskForm.search("standard") > -1 ) {
			fsImp = forms.sa_task_dtl.sa_task_to_sa_task_paper;
			sField = "taskpaper_sheet_size";
		} else {
			fsImp = forms[sTaskForm].sa_task_to_sa_task_paper;
			sField = "taskpaper_sheet_size";
		}
		if ( sCloneType == "color" ) {
			sRel = 'sa_task_impressions_to_sa_task_click_color.nr_';
		} else if ( sCloneType == "bw") {
			sRel = 'sa_task_impressions_to_sa_task_click_bw.nr_';
		} else if ( sCloneType == "colorPrice" ) {
			sRel = 'sa_task_impressions_to_sa_task_click_color.price_nr_';
		} else if ( sCloneType == "bwPrice") {
			sRel = 'sa_task_impressions_to_sa_task_click_bw.price_nr_';
		} else if ( sCloneType == "bwSheetsPerHour" || sCloneType == "bwDigitalSheetCustom") {
			sRel = 'nr_';
		} else if ( sCloneType == "bwPriceSheetsPerHour" || sCloneType == "bwPriceDigitalSheetCustom") {
			sRel = 'price_nr_';
		} else if ( sCloneType == "spoils" ) {
			sRel = "sa_task_speed_to_sa_task_spoil.nr_";
		} else if ( sCloneType == "wideFormatSpeed" ) {
			sRel = "sa_task_setup_to_sa_task_click_bw.nr_";
		} else if ( sCloneType == "grandFormatSpeed" ) {
			sRel = "sa_task_speed_to_sa_task_click_bw.nr_";
		} else if ( sCloneType == "proofingSpeed" ) {
			sRel = "sa_task_setup_to_sa_task_click_bw.nr_";
		} else {
			return null; // Not supported
		}

		/** @type {JSRecord<db:/avanti/sa_task_paper>}*/
		var rRec;

		if (utils.hasRecords(fsImp)) {
			
			// Get the grid component from the cloned form
		    /** @type {JSWebComponent} */
		    var jsGrid = jsFormClone.getWebComponent('grid');
		    /** @type {Array<CustomType<aggrid-groupingtable.column>>} */
		    var aColumns = jsGrid.getJSONProperty('columns');
						
	        fsImp.sort("sequence_nr asc");

	        for (i = 1; i <= fsImp.getSize(); i++) {
	        	
	            rRec = fsImp.getRecord(i);
	            
	            /** @type {CustomType<aggrid-groupingtable.column>} */
	            var column = {
	                rowGroupIndex: -1,
	                enableRowGroup: false,
	                enableSort: false,
	                enableToolPanel: false,
	                autoResize: false,
	                maxWidth: 100,
	                minWidth: 100,
	                width: 100,
	                headerStyleClass: 'text-center',
	                styleClass: 'text-center',
	                editType: 'TEXTFIELD',
	                headerTitle: rRec[sField],
	                id: 'col_' + i,
	                dataprovider: sRel + i
	            };

	            if (sType.indexOf("spoilage") === -1 && sType.indexOf("Speed") === -1) {
	                column.format = '0.000000|#.######';
	            }

	            aColumns.push(column);
	        }
	        
	        // Add shuffle and delete columns only if needed
			if (sCloneType.search("SheetsPerHour") == -1 
					&& sCloneType.search("DigitalSheetCustom") == -1
					&& !_bHideDeleteShuffleButtons) {
				
				aColumns.push(cloneTaskForm_addShuffleIcon());
				aColumns.push(cloneTaskForm_addDeleteIcon());
			}
		
			// Set the grid properties
		    scopes['ng_utils'].reorderArrayByStyleClass(aColumns);
		    jsGrid.setJSONProperty('columns', aColumns);
		    
	        
	        if ( sCloneType == 'wideFormatSpeed' || sCloneType == "grandFormatSpeed") {
				// Set the uom label
				jsLabel = jsForm.getComponent('lblUOM');
				jsLabel.setJSONProperty('text', application.getValueListDisplayValue('avTaskWideCalcTypes', forms.sa_task_dtl.foundset.sa_task_to_sa_task_standard.taskstd_calc_type) + " " + i18n.getI18NMessage("avanti.lbl.perMin"));
			} 
	        else if ( sCloneType == 'proofingSpeed' ) {
				// Set the uom label
	        	jsLabel = jsForm.getComponent('lblUOM');
	        	jsLabel.setJSONProperty('text', application.getValueListDisplayValue('avTaskWideCalcTypes', forms.sa_task_dtl.foundset.sa_task_to_sa_task_standard.taskstd_calc_type_2) + " " + i18n.getI18NMessage("avanti.lbl.perMin"));
			}
	    }		

		return jsFormClone;
	}

	/**
	 * Checks to see if a clone exists
	 * @param {String} sClone - The name of the clone to look for
	 * @returns {Boolean} TRUE or FALSE if the form exits
	 */
	function cloneTaskForm_checkForClone (sClone) {
		if ( solutionModel.getForm(sClone) ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Checks to see if a clone is on the tab
	 * @param {String} sCloneTab - The name of the clone to look for
	 * @returns {Boolean} TRUE or FALSE if the clone exists as a tab
	 */
	function cloneTaskForm_checkForCloneTab (sCloneTab) {
		var i;
		for ( i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++ ) {
			if ( scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == sCloneTab ) {
				return true;
			}
		}
		return false;
	}

	/**
	 * Creates a column for the shuffle button
	 * @returns {CustomType<aggrid-groupingtable.column>} Shuffle column definition
	 */
	function cloneTaskForm_addShuffleIcon () {
	    /** @type {CustomType<aggrid-groupingtable.column>} */
	    var column = {
	        rowGroupIndex: -1,
	        enableRowGroup: false,
	        enableSort: false,
	        enableToolPanel: false,
	        autoResize: false,
	        headerTitle: ' ',
	        id: 'btnShuffle',
	        styleClass: 'material-symbols-outlined swap_vert',
	        minWidth: 25,
			maxWidth: 25,
	        width: 25
	    };
	    return column;
	}

	/**
	* Creates a column for the delete button
	* @returns {CustomType<aggrid-groupingtable.column>} Delete column definition
	*/
	function cloneTaskForm_addDeleteIcon() {

	    /** @type {CustomType<aggrid-groupingtable.column>} */
	    var column = {
	        rowGroupIndex: -1,
	        enableRowGroup: false,
	        enableSort: false,
	        enableToolPanel: false,
	        autoResize: false,
	        headerTitle: ' ',
	        id: 'btnDelete',
	        styleClass: 'material-symbols-outlined delete',
			minWidth: 25,
			maxWidth: 25,
	        width: 25
	    };
	    return column;
	}

	return;
}

/**
 * Remove the form from the tab and the solutionModel
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-06-03
 *
 * @param {String} sClone - The form to be removed
 *
 * @returns {Boolean} TRUE or FALSE as to whether or not it has been removed
 *
 *
 * @properties={typeid:24,uuid:"01993293-0CE8-412B-98EA-9C59B26F0D53"}
 * @AllowToRunInFind
 */
function cloneTaskForm_removeClone (sClone) {	
	var bSuccess = false;
	var i;
	for ( i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++ ) {		
		if ( scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == sClone ) {		
			scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
			bSuccess = true;
		}
	}
	try {	
		bSuccess = history.removeForm(sClone);		
	} catch ( err ) {

	}
	solutionModel.removeForm(sClone);	
	return bSuccess;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"32B85E52-3D28-4885-B874-70842058FB87"}
 */
function onDataChange_colors (oldValue, newValue, event) {
	// Restrict color to two characters
	if ( oldValue != newValue ) {
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var fsForm = forms[event.getFormName()].foundset;
		if ( newValue.toString().length > 2 ) {
			var sMsg = i18n.getI18NMessage("avanti.dialog.dataVerification_msg").replace("<data>", "2");
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.dataVerification_title"),
				sMsg,
				i18n.getI18NMessage("avanti.dialog.ok"));
			fsForm.taskstd_colours = oldValue;
		}
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"6D3296D5-7BEE-4939-B5DF-538377641DF2"}
 */
function onDataChange_perfecting (oldValue, newValue, event) {
	var sForm = event.getFormName();

	showPerfectingOptions(newValue, sForm);

	return true
}

/**
 * Shows/Hides the perfecting options
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-01-16
 *
 * @param {String} sOption - Either "S" to hide the perfecting options, or they will be shown (P is default)
 * @param {String} sForm - form name to use
 *
 *
 * @properties={typeid:24,uuid:"45065B23-8D5A-4F6B-BA04-7B628B1CC2F4"}
 */
function showPerfectingOptions (sOption, sForm) {
	var aElements = ["taskstd_perfector_after_unit", "taskstd_perfector2_after_unit", "lblPefectorUnit", "lblPefectorUnit2"];
	var bVisible = true;

	if ( sOption == "S" ) {
		bVisible = false;
	}

	for ( var i = 0; i < aElements.length; i++ ) {
		forms[sForm].elements[aElements[i]].visible = bVisible;
	}
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"55474315-A4BB-450F-B92E-4909D4CFAB37"}
 */
function onDataChange_machineVariables (oldValue, newValue, event) {
//	var i;
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;
		jsForm.taskstd_machine_var_based_on = newValue;

		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.machineVariable");
		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.spoilage");

		//		if (newValue == "F")
		//		{
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_machine_02, i18n.getI18NMessage("avanti.lbl.machineVariable"), "sa_task_to_sa_task_machine", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_spoils_03, i18n.getI18NMessage("avanti.lbl.spoilage"), "sa_task_to_sa_task_spoil", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_machine_03")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_spoils_04")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//		}
		//		else if (newValue == "O")
		//		{
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_machine_03, i18n.getI18NMessage("avanti.lbl.machineVariable"), "sa_task_to_sa_task_machine", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_spoils_04, i18n.getI18NMessage("avanti.lbl.spoilage"), "sa_task_to_sa_task_spoil", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_machine_02")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_spoils_03")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//		}
		//		else
		//		{
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_machine_02")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_spoils_03")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_machine_03")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_spoils_04")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//		}

		forms.sa_task_dtl.buildDetailView();
		forms[sForm].refreshUI(newValue);
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"********-9D7D-4B1C-9513-9EF4C039CFAC"}
 */
function onDataChange_packagingOption (oldValue, newValue, event) {
//	var i;
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;
		jsForm.taskstd_packaging_option = newValue;

		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.cartons");
		globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.materials");

		forms.sa_task_dtl.buildDetailView();
		forms[sForm].refreshUI(newValue);

		//		if (newValue == "C")
		//		{
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_materials_02, i18n.getI18NMessage("avanti.lbl.cartons"), "sa_task_to_sa_task_material", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_materials_03")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//		}
		//		else if (newValue == "S")
		//		{
		//			scopes.globals.avUtilities_tabAdd("sa_task_dtl", "tab1", forms.sa_task_materials_03, i18n.getI18NMessage("avanti.lbl.materials"), "sa_task_to_sa_task_material", null, null, forms.sa_task_dtl.elements.tab1.getMaxTabIndex() + 1, null);
		//
		//			for (i = 1; i <= scopes.globals.avUtilities_tabGetMaxTabIndex("sa_task_dtl", "tab1"); i++)
		//			{
		//				if (scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", i) == "sa_task_materials_02")
		//				{
		//					scopes.globals.avUtilities_tabRemoveAt("sa_task_dtl", "tab1", i);
		//				}
		//			}
		//		}
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"32DFF977-0BC5-4C77-A7F4-E9BEEACEAE04"}
 */
function onDataChange_coverDeck (oldValue, newValue, event) {

	var sForm = event.getFormName();

	forms.sa_task_dtl.buildDetailView();
	forms[sForm].refreshUI(newValue);

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"5A117DBC-0B45-478D-A999-9C4F625A91B6"}
 */
function onDataChange_handFeeder (oldValue, newValue, event) {

	var sForm = event.getFormName();

	forms.sa_task_dtl.buildDetailView();
	forms[sForm].refreshUI(newValue);

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"92ED8BB4-5298-498D-B726-E6230A1BAC22"}
 */
function onDataChange_cardFeeder (oldValue, newValue, event) {

	var sForm = event.getFormName();

	forms.sa_task_dtl.buildDetailView();
	forms[sForm].refreshUI(newValue);

	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"960691E8-0A78-4CD9-A6A2-E5E0FA508ADB"}
 */
function onDataChange_faceTrim (oldValue, newValue, event) {

	var sForm = event.getFormName();

	forms.sa_task_dtl.buildDetailView();
	forms[sForm].refreshUI(newValue);

	return true;
}

/**
 * @properties={typeid:24,uuid:"6D791DD0-853C-4681-9F03-7E14940E6F18"}
 */
function setTaskOperationsVL () {
	var aDisplay = new Array();
	var aReturn = new Array();
	var bClearVL = true;

	// Prepare the Task Operations VL
	/*** @type {JSFoundSet<db:/avanti/app_task_operation>} */
	var fsOper = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'app_task_operation');
	/*** @type {JSRecord<db:/avanti/app_task_operation>} */
	var rOper;

	fsOper.loadAllRecords();
	fsOper.sort("sequence_nr asc");

	for ( var i = 1; i <= fsOper.getSize(); i++ ) {
		rOper = fsOper.getRecord(i);
		aDisplay.push(i18n.getI18NMessage(rOper.taskoper_key));
		aReturn.push(rOper.taskoper_id);
	}

	if ( aDisplay.length > 0 && aReturn.length > 0 ) {
		bClearVL = false;
	}

	if ( !bClearVL ) {
		application.setValueListItems("vl_TaskOperations_i18n", aDisplay, aReturn);
	} else {
		application.setValueListItems("vl_TaskOperations_i18n", [], []);
	}
	return;

}

/**
 * @param {Number} [iTaskTypeID] - Optional tasktypeid restriction
 * @param {Boolean} [bPerfScore] - Optional restriction for perf/score only
 * @param {Number} [nWebs] - Optional number of webs required
 * @param {Array} [aDie] - Optional dies the press should be capable of running (itemclass_id)
 * @param {Array} [aCyl] - Optional cylinders the press should be capable of running (itemclass_id)
 * @param {String} [sType] - Optional type of press (like "B" for blank)
 * @param {Boolean} [bJustReturnArrays]
 * 
 * @return {{aDisplay:Array<String>, aReturn:Array<String>}}
 *
 * @properties={typeid:24,uuid:"B82ACA69-AC79-48D3-B04A-B28A3C14E5CA"}
 * @AllowToRunInFind
 */
function setTaskWorkType_pressTasksVL (iTaskTypeID, bPerfScore, nWebs, aDie, aCyl, sType, bJustReturnArrays) {
	var aDisplay = [],
		aReturn = [],
		aDisplayByPlant = [],
		aReturnByPlant = [],
		bClearVL = true,
		fsTask,
		rTask,
		i = 0, j = 0, 
		rRec, 
		bCyl = true, 
		bDie = true, 
		bType = false,
		aDieOrig = aDie, 
		aCylOrig = aCyl;

	if (!bPerfScore) bPerfScore = false;

	var bFilterByPlant = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter) === 1 
		&& globals.avBase_plantIDTemp && globals.avBase_plantIDTemp != 'ALL';
	
	// GD - 2013-11-05 - PERFTUNE: Trying to get the tasks by relation
	if ( utils.hasRecords(_to_sa_task$active_nonsysstandard) ) {

		fsTask = _to_sa_task$active_nonsysstandard;

		//	/*** @type {JSFoundSet<db:/avanti/sa_task>} */
		//	var fsTask = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task');
		//	fsTask.loadRecords(application.getUUID("pk"));
		//
		//	if (fsTask.find() || fsTask.find())
		//	{
		//		fsTask.task_active = 1;
		//		fsTask.sa_task_to_app_task_type.tasktype_is_press = 1;
		//		fsTask.search();
		//
		//		if (fsTask.getSize() > 0)
		//		{
		if ( utils.hasRecords(fsTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas) ) {
			fsTask.sort("sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.sequence_nr asc, sequence_nr asc");
		} else {
			fsTask.sort("sequence_nr asc");
		}

		for (i = 1; i <= fsTask.getSize(); i++ ) {
			rTask = fsTask.getRecord(i);

			if ( rTask.tasktype_id 
			        && utils.hasRecords(rTask.sa_task_to_app_task_type) 
			        && rTask.sa_task_to_app_task_type.tasktype_is_press === 1 ) {

				// include only press types

				if ((iTaskTypeID && rTask.tasktype_id === iTaskTypeID) // specific tasktype like offset
						|| !iTaskTypeID){ 
					
				    if (rTask.task_description){
				    	
				    	if (rTask.tasktype_id === 7){
				    		
				    		aDie = aDieOrig;
				    		aCyl = aCylOrig;
				    		
				    		bType = false;
				    		if (!sType){
				    			bType = true;
				    		} else if (rTask.sa_task_to_sa_task_standard.taskstd_press_type === sType) {
				    			bType = true;
				    		}
				    		
				    		if (bType && aCyl && aCyl.length > 0 ){
				    			
				    			bCyl = false;
				    			
				    			if (rTask.sa_task_to_sa_task_standard.taskstd_cylinder_type === "V"
				    				&& utils.hasRecords(rTask.sa_task_to_sa_task_class_cylinder)){
				    			
						    		for ( j = 1; j <= rTask.sa_task_to_sa_task_class_cylinder.getSize(); j++ ) {
						    			rRec = rTask.sa_task_to_sa_task_class_cylinder.getRecord(j);
						    			
						    			if (aCyl.lastIndexOf(rRec.itemclass_id) > -1){
						    				aCyl = scopes.globals.avUtilities_arrayRemoveElementByIndex(aCyl, aCyl.lastIndexOf(rRec.itemclass_id));
						    				if (aCyl.length === 0) {
						    					bCyl = true;
						    					break; // finished meeting all cylinder requirements
						    				}
						    			}
						    		}
					    		}
				    		}
			    			if (bType && aDie && aDie.length > 0 ){
				    			
				    			bDie = false;
				    			
			    		        if (rTask.sa_task_to_sa_task_standard.taskstd_access_all_dies == 1) {
			    		            
			    		            bDie = true;
			    		        }
			    		        else {
    				    			if (utils.hasRecords(rTask.sa_task_to_sa_task_class_die)){
    						    		for ( j = 1; j <= rTask.sa_task_to_sa_task_class_die.getSize(); j++ ) {
    						    			rRec = rTask.sa_task_to_sa_task_class_die.getRecord(j);
    						    			
    						    			if (aDie.lastIndexOf(rRec.itemclass_id) > -1){
    						    				aDie = scopes.globals.avUtilities_arrayRemoveElementByIndex(aDie, aDie.lastIndexOf(rRec.itemclass_id));
    						    				if (aDie.length === 0) {
    						    					bDie = true;
    						    					break; // finished meeting all die requirements
    						    				}
    						    			}
    						    		}
    				    			}
			    		        }
				    		}
			    			if (bDie && bCyl && bType) addValues();
			    			
				    	} else if (bPerfScore){
							if (rTask.sa_task_to_sa_task_standard.taskstd_perf_score === 1){
								addValues();
							}
						} else if (nWebs) {
							// GD - Apr 12, 2014: Added the restriction for number of webs
							if (rTask.sa_task_to_sa_task_standard.taskstd_web_nr >= nWebs){
								addValues();
							}
						} else {	
								addValues();
						}
				    }
				}
			}
		}

		if ( aDisplay.length > 0 && aReturn.length > 0 ) {
			bClearVL = false;
		}
		//		}
	}
	
	function addValues(){
		var sDisplay;
		
		if ( utils.hasRecords(rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas) && rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc ) {
			sDisplay = rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc + ": " + rTask.task_description;
		} 
		else {
			sDisplay = "N/A: " + rTask.task_description;
		}

		aDisplay.push(sDisplay);
		aReturn.push(rTask.task_id);

		// have to filter by ord plant
		if(bFilterByPlant && scopes.avSystem.taskUsesPlant(rTask.task_id, globals.avBase_plantIDTemp)){
			aDisplayByPlant.push(sDisplay);
			aReturnByPlant.push(rTask.task_id);
		}
	}

	if (bClearVL) {
		if(!bJustReturnArrays){
			application.setValueListItems("avSales_taskWorkTypes_pressTasks", [], []);
		}

		return {aDisplay: [], aReturn: []};
	} 
	else {
		var oArr = globals.avUtilities_arraySort(aDisplay, aReturn);

		if(bJustReturnArrays){
			return {aDisplay: oArr.a1, aReturn: oArr.a2};
		}
		else{
			application.setValueListItems("avSales_taskWorkTypes_pressTasks", oArr.a1, oArr.a2);

			if(bFilterByPlant){
				oArr = globals.avUtilities_arraySort(aDisplayByPlant, aReturnByPlant);
			}
			
			application.setValueListItems("vl_pressesByOrdPlant", oArr.a1, oArr.a2);
			return {aDisplay: oArr.a1, aReturn: oArr.a2};
		}
	}
}

/**
 *
 * @param {Boolean} [bGeneric]
 * @param {Boolean} [bJustReturnArrays]
 * @param {String} [displayValue] - What the user typed into the field so we can implement type ahead
 * 
 * @return {{aDisplay:Array<String>, aReturn:Array<String>}}
 *
 * @properties={typeid:24,uuid:"97CC1779-9F0A-427E-BB08-B4C9B9A72546"}
 * @AllowToRunInFind
 */
function setTaskWorkType_otherTasksVL (bGeneric, bJustReturnArrays, displayValue) {
	var aDisplay = new Array();
	var aReturn = new Array();
	var aGenericSectionTaskTypes = scopes.avSection.getGenericSectionSupportedTaskTypes();

	/*** @type {JSFoundSet<db:/avanti/sa_task>} */
	var fsTask = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task');

	if ( fsTask.find() || fsTask.find() ) {
		fsTask.task_active = 1;
		fsTask.task_is_sys_standard = "^||0"
		fsTask.sa_task_to_app_task_type.tasktype_is_press = "^";
		if (displayValue) {
		    fsTask.task_description = displayValue + "%";
		}
		fsTask.search();
		
		if (!fsTask || fsTask.getSize() == 0) {
		    return {aDisplay: aDisplay, aReturn: aReturn}; 
		}

		if ( globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ShowFunctionalGroupOnTaskVl) == 1
		        && utils.hasRecords(fsTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas) ) {
		            
			fsTask.sort("sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.sequence_nr asc, sequence_nr asc");
			
		} else {
		    
			fsTask.sort("task_description asc");
		}

		if ( fsTask.getSize() > 0 ) {
			/*** @type {JSRecord<db:/avanti/sa_task>} */
			var rTask;

			for ( var i = 1; i <= fsTask.getSize(); i++ ) {
				rTask = fsTask.getRecord(i);
				
				// sl-3478 - generic section only allows certain task types
				if(bGeneric && aGenericSectionTaskTypes.indexOf(rTask.tasktype_id) == -1){
					continue
				}
				
				// Exclude the ink, paper, press, folder and cutter tasks
				if ( rTask.tasktype_id != 5 && rTask.tasktype_id != 99 && rTask.tasktype_id != 98 ) {
					if ( rTask.task_description ) {
					    
					    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ShowFunctionalGroupOnTaskVl) == 1) {
					        
    						if (utils.hasRecords(rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas)
    						        && rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc ) {
    						            
    							aDisplay.push(rTask.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc + ": " + rTask.task_description);
    							
    						} else {
    						    
    							aDisplay.push("N/A: " + rTask.task_description);
    						}
					    }
					    else {
					        
					        aDisplay.push(rTask.task_description);
					    }
						aReturn.push(rTask.task_id);
					}
				}
			}

			if (!displayValue) {
			    aDisplay.push(i18n.getI18NMessage("avanti.lbl.materials"));
			    aReturn.push(globals.avSales_materialTaskUUID);
			}
		}
	}

	if(!bJustReturnArrays){
		application.setValueListItems("avSales_taskWorkTypes_otherTasks", aDisplay, aReturn);
	}

	return {aDisplay: aDisplay, aReturn: aReturn};
}

/**
 * Set the task sheet size value list for a press
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-11-07
 *
 * @param {JSRecord<db:/avanti/sa_task>} rPress - The press task
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"E85640BD-48D8-4897-A10C-76DA6E6412F7"}
 */
function setTaskSheetSizeVL (rPress) {
	var aDisplay = new Array();
	var aReturn = new Array();

	// Get the min and max sizes
	var iMin1;
	var iMin2;
	var iMax1;
	var iMax2;
	var bRoll = false;

	/*** @type {JSFoundSet<db:/avanti/in_item_paper>} */
	var fsPaper = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_paper');
	if ( fsPaper.find() || fsPaper.find() ) {
		if ( rPress.tasktype_id == 2 || rPress.tasktype_id == 10 || rPress.tasktype_id == 7 ) {
			// Rolls of paper
			iMin1 = rPress.sa_task_to_sa_task_standard.taskstd_min_roll_size;
			iMax1 = rPress.sa_task_to_sa_task_standard.taskstd_max_roll_size;

			fsPaper.paper_first_dim = ">=" + iMin1;
			fsPaper.paper_first_dim = "<=" + iMax1;
			fsPaper.in_item_paper_to_in_paper_brand.in_paper_brand_to_in_paper_grade.papergrade_is_roll = 1;

			bRoll = true;
		} else {
			// Sheets of paper
			iMin1 = rPress.sa_task_to_sa_task_standard.taskstd_min_paper_width
			iMin2 = rPress.sa_task_to_sa_task_standard.taskstd_min_paper_heigth;
			iMax1 = rPress.sa_task_to_sa_task_standard.taskstd_max_paper_width;
			iMax2 = rPress.sa_task_to_sa_task_standard.taskstd_max_paper_height;

			fsPaper.paper_first_dim = ">=" + iMin1;
			fsPaper.paper_first_dim = "<=" + iMax1;
			fsPaper.paper_second_dim = ">=" + iMin2;
			fsPaper.paper_second_dim = "<=" + iMax2;

			// Search in the reverse as well
			fsPaper.newRecord();
			fsPaper.paper_second_dim = ">=" + iMin1;
			fsPaper.paper_second_dim = "<=" + iMax1;
			fsPaper.paper_first_dim = ">=" + iMin2;
			fsPaper.paper_first_dim = "<=" + iMax2;
		}

		fsPaper.search();
	}

	var rPaper;
	var sSheetSize;
	fsPaper.sort("paper_first_dim asc, paper_second_dim asc");
	for ( var i = 1; i <= fsPaper.getSize(); i++ ) {
		rPaper = fsPaper.getRecord(i);

		if ( bRoll ) {
			sSheetSize = rPaper.paper_first_dim + '" ' + i18n.getI18NMessage("avanti.lbl.roll");
		} else {
			sSheetSize = rPaper.paper_first_dim + " x " + rPaper.paper_second_dim;
		}

		if ( aDisplay.indexOf(sSheetSize, 0) == -1 ) {
			aDisplay.push(sSheetSize);
			aReturn.push(sSheetSize);
		}
	}

	application.setValueListItems("avTaskSheetSizes", aDisplay, aReturn);
}

/**
 * Will get the system runPress task UUID or create it
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-08-30
 *
 * @properties={typeid:24,uuid:"FFBFA953-8B00-497C-B1C7-A456D706D400"}
 * @AllowToRunInFind
 */
function getRunPressTask () {
	/*** @type {JSFoundSet<db:/avanti/sa_task>} */
	var fsTask = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task');

	// GD - 2012-05-11: If we are logging in and out of a new org, do not create anything until data is loaded
	fsTask.loadAllRecords();
	if ( fsTask.getSize() == 0 ) return;

	if ( fsTask.find() || fsTask.find() ) {
		fsTask.tasktype_id = globals.avSales_runPressTaskTypeID;
		fsTask.search();
		
		// sl-13235 = changed from '== 1' to '>= 1', otherwise if we have > 1 rec it creates another rec every time you login
		if ( fsTask.getSize() >= 1 && fsTask.tasktype_id == globals.avSales_runPressTaskTypeID ) {
			globals.avSales_runPressTaskUUID = fsTask.task_id
		} else {
			var rTask = fsTask.getRecord(fsTask.newRecord(false));
			rTask.tasktype_id = globals.avSales_runPressTaskTypeID;
			rTask.task_description = i18n.getI18NMessage("avanti.lbl.pressRun");
			rTask.sequence_nr = globals.avUtilities_getSequenceNr(fsTask);
			globals.avSales_runPressTaskUUID = rTask.task_id;

			databaseManager.saveData(rTask);
		}
	}
}

/**
 * Will get the system Paper task UUID or create it
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-08-30
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"06035E34-9312-495A-8B95-98DE68528BC1"}
 */
function getPaperTask () {
	/*** @type {JSFoundSet<db:/avanti/sa_task>} */
	var fsTask = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task');

	// GD - 2012-05-11: If we are logging in and out of a new org, do not create anything until data is loaded
	fsTask.loadAllRecords();
	if ( fsTask.getSize() == 0 ) return;

	if ( fsTask.find() || fsTask.find() ) {
		fsTask.tasktype_id = globals.avSales_paperTaskTypeID;
		fsTask.search();
		
		// sl-13235 = changed from '== 1' to '>= 1', otherwise if we have > 1 rec it creates another rec every time you login
		if ( fsTask.getSize() >= 1 && fsTask.tasktype_id == globals.avSales_paperTaskTypeID ) {
			globals.avSales_paperTaskUUID = fsTask.task_id
		} else {
			var rTask = fsTask.getRecord(fsTask.newRecord(false));
			rTask.tasktype_id = globals.avSales_paperTaskTypeID;
			rTask.task_description = i18n.getI18NMessage("avanti.lbl.paper");
			rTask.sequence_nr = globals.avUtilities_getSequenceNr(fsTask);
			globals.avSales_paperTaskUUID = rTask.task_id;

			databaseManager.saveData(rTask);
		}
	}
}

/**
 * Will get the system Paper task UUID or create it
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-02-16
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"94542B21-5DA7-4ABF-B386-804393DC2EF2"}
 */
function getMaterialTask () {
	/*** @type {JSFoundSet<db:/avanti/sa_task>} */
	var fsTask = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task');

	// GD - 2012-05-11: If we are logging in and out of a new org, do not create anything until data is loaded
	fsTask.loadAllRecords();
	if ( fsTask.getSize() == 0 ) return;

	fsTask.loadAllRecords();
	if ( fsTask.getSize() == 0 ) return;

	if ( fsTask.find() || fsTask.find() ) {
		fsTask.tasktype_id = globals.avSales_materialTaskTypeID;
		fsTask.search();
		
		// sl-13235 = changed from '== 1' to '>= 1', otherwise if we have > 1 rec it creates another rec every time you login
		if ( fsTask.getSize() >= 1 && fsTask.tasktype_id == globals.avSales_materialTaskTypeID ) {
			globals.avSales_materialTaskUUID = fsTask.task_id
		} else {
			var rTask = fsTask.getRecord(fsTask.newRecord(false));
			rTask.tasktype_id = globals.avSales_materialTaskTypeID;
			rTask.task_description = i18n.getI18NMessage("avanti.lbl.material");
			rTask.sequence_nr = globals.avUtilities_getSequenceNr(fsTask);
			globals.avSales_materialTaskUUID = rTask.task_id;

			databaseManager.saveData(rTask);
		}
	}
}

/**
 * Data change
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-05-14
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"7D7F7EF6-3AF1-4F37-BEAD-CBDFA93334BE"}
 */
function onDataChange_colorBarRequired (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_colorbar = newValue;

		databaseManager.saveData(jsForm);

	}

	return true;
}

/**
 * Data change
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-05-14
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"4337ED78-E27A-42E1-BA79-67398DD57048"}
 */
function onDataChange_paperType (oldValue, newValue, event) {
	if ( oldValue != newValue ) {
		var sForm = event.getFormName(),
			/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
			jsForm = forms[sForm].foundset,
			iTaskTypeID = 0;

		jsForm.taskstd_paper_type = newValue;
		iTaskTypeID = jsForm.sa_task_standard_to_sa_task.tasktype_id;

		databaseManager.saveData(jsForm);
		
		// GD - Sep 25, 2014: Sl-3056: Add sheets size limits for Wide Format
		if (iTaskTypeID === scopes.avTask.TASKTYPEID.WideFormatPress || iTaskTypeID === scopes.avTask.TASKTYPEID.GrandFormatPress){
			forms[sForm].refreshUI();
		}
	}

	return true;
}

/**
 * Duplicates the current task
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-05-10
 *
 * @param {String} sForm - The name of the form
 * @param {String} [sStandardTaskUUID] - Optional standard task uuid to clone
 *
 * @returns {String} ID of new Task
 *
 * @properties={typeid:24,uuid:"2F46F2B1-E88F-4A46-91E4-8C669C1BCE4F"}
 */
function duplicateTask (sForm, sStandardTaskUUID) {
	/***@type {JSFoundSet<db:/avanti/sa_task>}*/
	var fsTask = forms[sForm].foundset,
		/** @type {JSRecord<db:/avanti/sa_task_setup>}*/
		rSetup,
		/** @type {JSRecord<db:/avanti/sa_task_machine>}*/
		rMachine,
		/** @type {JSRecord<db:/avanti/sa_task_spoil>}*/
		rSpoil,
		aMachine = [],
		aImpressions = [],
		aSpeed = [],
		/** @type {JSRecord<db:/avanti/sa_task_cutoff>}*/
		rCutoff,
		aCutOff = [];

	var bCloneStd = false;
	if ( sStandardTaskUUID ) {
		globals.avBase_selectedTaskID = sStandardTaskUUID;

		if ( utils.hasRecords(_to_sa_task$avbase_selectedtaskid) ) {
			bCloneStd = true;
		} else {
			return null;
		}
	}

	/***@type{JSRecord<db:/avanti/sa_task>}*/
	var rRec = fsTask.getSelectedRecord();

	if ( rRec.task_is_sys_standard == 1 && globals.avBase_developmentMode != 1 ) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.taskStandard_title"),
			i18n.getI18NMessage("avanti.dialog.taskStandard_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"));

		return null;
	}

	if ( fsTask.task_is_system == 1 ) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.duplicateSystemTaskType_title"),
			i18n.getI18NMessage("avanti.dialog.duplicateSystemTaskType_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"));

		return null;
	}

	var i;

	var rCur;
//	var sCurPK;
	var rNew;
	var sNewPK;
	if ( bCloneStd ) // We are duplicating a task from the standards library
	{
		rCur = _to_sa_task$avbase_selectedtaskid.getRecord(1);
//		sCurPK = rCur.task_id;

		rNew = _to_sa_task$avbase_selectedtaskid.getRecord(_to_sa_task$avbase_selectedtaskid.duplicateRecord(1, false, true));
		rNew.task_is_sys_standard = null;
		rNew.org_id = globals.org_id
		sNewPK = rNew.task_id;
	} else {
		// Show dialog to verify duplicate method
		var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.taskDuplicateRecord_title"),
			i18n.getI18NMessage("avanti.dialog.taskDuplicateRecord_msg"),
			i18n.getI18NMessage("avanti.dialog.ok"),
			i18n.getI18NMessage("avanti.dialog.cancel"));
		if ( sAns != i18n.getI18NMessage("avanti.dialog.ok") ) {
			return null;
		}

		rCur = fsTask.getSelectedRecord();
//		sCurPK = rCur.task_id;

		rNew = fsTask.getRecord(fsTask.duplicateRecord(fsTask.getSelectedIndex(), false, true));
		sNewPK = rNew.task_id;
	}
	
	// sl-21794 - set globals.avBase_selectedTaskID to sNewPK - avBase_selectedTaskID is used as an auto-enter for task_id on several task related tables. sometimes avBase_selectedTaskID 
	// was null here, and sometimes set to a different task. im not sure it was actually causing a problem as we seem to always manually set task_ids in this function. but it could 
	// potentially cause a problem in the future. regardless, it should be set to the task_id of the new task we are creating, just in case.    
	globals.avBase_selectedTaskID = sNewPK;

	forms.sa_task_dtl._duplicateRecord = 1;
	if ( sForm == "sa_task_dtl" ) scopes.globals.avUtilities_tabSetSelectedIndex("sa_task_dtl", "tab1", 1);
	var iSeqNr;

	iSeqNr = fsTask.getSize() + 1;

	// Set some data
	rNew.task_description += i18n.getI18NMessage("avanti.lbl.duplicated");
	rNew.sequence_nr = iSeqNr;
	rNew.task_outside_purchase_item_id = null;

	var rNewStd = rCur.sa_task_to_sa_task_standard.getRecord(rCur.sa_task_to_sa_task_standard.duplicateRecord(1, false, true)); // rCur.sa_task_to_sa_task_standard.getSelectedIndex()
	// GD - May 10, 2014: Make sure there are no other standard records before joining the new one
	rNew.sa_task_to_sa_task_standard.deleteAllRecords();
	rNewStd.task_id = sNewPK;
	if ( bCloneStd ) rNewStd.org_id = globals.org_id;
	//	if (bCloneStd && utils.hasRecords(_to_sys_task_functional_areas$active)) rNewStd.systaskfunc_id = _to_sys_task_functional_areas$active.getRecord(1).systaskfunc_id; //Need to pick the first one that the user has as the default
	if ( bCloneStd ) // Inject system functional area if not already there
	{
		var rUnAssigned;
		if ( !utils.hasRecords(_to_sys_task_functional_areas$unassigned) ) {
			rUnAssigned = _to_sys_task_functional_areas$unassigned.getRecord(_to_sys_task_functional_areas$unassigned.newRecord(false, true));
			databaseManager.saveData(rUnAssigned);
		} else {
			rUnAssigned = _to_sys_task_functional_areas$unassigned.getRecord(1);
		}

		rNewStd.systaskfunc_id = rUnAssigned.systaskfunc_id;
	}
	databaseManager.saveData(rNewStd);

	// Clone the data for the original record, setting the task_id to the new UUID
	var iMax;
	var kMax;
	var l;
	var relClick,
		/** @type {JSRecord<db:/avanti/sa_task_color>} */ 
		rColor = null,
		aColor = [],
		/** @type {JSRecord<db:/avanti/sa_task_paper>} */ 
		rPaper = null,
		aPaper = [],
		iMaxPaper = 0,
		m = 0;
	var aClone = [
				"sa_task_to_sa_task_accessory$cardfeeder",
				"sa_task_to_sa_task_accessory$coverdeck",
				"sa_task_to_sa_task_accessory$facetrim",
				"sa_task_to_sa_task_accessory$handfeeder",
				"sa_task_to_sa_task_adj$caliper",
				"sa_task_to_sa_task_adj$ink",
				"sa_task_to_sa_task_associated_tasks",
				"sa_task_to_sa_task_coater_unit",
				"sa_task_to_sa_task_cost_link",
				"sa_task_to_sa_task_cost_rate",
				"sa_task_to_sa_task_coverage",
				"sa_task_to_sa_task_cutoff",
				"sa_task_to_sa_task_class_cylinder",
				"sa_task_to_sa_task_cylinder",
				"sa_task_to_sa_task_class_die",
				"sa_task_to_sa_task_die",
				"sa_task_to_sa_task_email",
				"sa_task_to_sa_task_folder",
				"sa_task_to_sa_task_ink",
				"sa_task_to_sa_task_machine",
				"sa_task_to_sa_task_material",
				"sa_task_to_sa_task_mrspoil",
				"sa_task_to_sa_task_paper",
				"sa_task_to_sa_task_perf", 
				"sa_task_to_sa_task_plate",
				"sa_task_to_sa_task_price",
				"sa_task_to_sa_task_setup",
				"sa_task_to_sa_task_standard", 
				"sa_task_to_sa_task_supplier",
				"sa_task_to_sa_task_spoil",
				"sa_task_to_sa_task_turning_bar",
				"sa_task_to_sa_task_udf",
				"sa_task_to_sa_task_wash_ink",
				"sa_task_to_sa_task_color",
				"sa_task_to_sa_task_speed_sheetsperhour"				
		     ];

	for ( i = 0; i <= aClone.length - 1; i++ ) {
		
		//		application.output("Cloning: " + aClone[i]);
		
//		if (aClone[i] == "sa_task_to_sa_task_coater_unit"){
//			application.output("debug")
//		}

		if ( utils.hasRecords(rCur[aClone[i]]) ) {
			
			iMax = rCur[aClone[i]].getSize();
			
			for ( var k = 1; k <= iMax; k++ ) {
				
				if (aClone[i] == 'sa_task_to_sa_task_setup') {

					rSetup = rCur[aClone[i]].getRecord(k);

				}
				else if (aClone[i] == 'sa_task_to_sa_task_machine') {

					rMachine = rCur[aClone[i]].getRecord(k);
					aMachine["'" + rMachine.taskmachine_id + "'"] = null;
				}
				else if (aClone[i] == 'sa_task_to_sa_task_paper') {

					rPaper = rCur[aClone[i]].getRecord(k);
					aPaper["'" + rPaper.taskpaper_id + "'"] = null;

				}
				else if (aClone[i] == 'sa_task_to_sa_task_color') {

					rColor = rCur[aClone[i]].getRecord(k);
					aColor["'" + rColor.taskcolor_id + "'"] = null;
				}
				else if (aClone[i] == 'sa_task_to_sa_task_cutoff') {
					rCutoff = rCur[aClone[i]].getRecord(k);
					aCutOff["'" + rCutoff.taskcutoff_id + "'"] = null;
				}

				rCur[aClone[i]].duplicateRecord(k, false, true);
				
				if ( bCloneStd ) rCur[aClone[i]].org_id = globals.org_id;
				
				rCur[aClone[i]].task_id = sNewPK;
				rCur[aClone[i]].sequence_nr = k;
				
				if (aClone[i] == 'sa_task_to_sa_task_machine') {

					aMachine["'" + rMachine.taskmachine_id + "'"] = rCur[aClone[i]].taskmachine_id;
					
					if (utils.hasRecords(rMachine.sa_task_machine_to_sa_task_machine_patch)) {

    					var iMaxPatch = rMachine.sa_task_machine_to_sa_task_machine_patch.getSize();
    					for (m = 1; m <= iMaxPatch; m++) {
    					    
    					    application.output("Duplicating patch: " + m + " Max Patches: " + iMaxPatch + "newTaskID: " + sNewPK + " machineID: " + rMachine.taskmachine_id + " new MachineID: " + aMachine["'" + rMachine.taskmachine_id + "'"]);

    					    rMachine.sa_task_machine_to_sa_task_machine_patch.duplicateRecord(m, false, true);
    					    
    					    rMachine.sa_task_machine_to_sa_task_machine_patch.taskmachine_id = aMachine["'" + rMachine.taskmachine_id + "'"];
    					}
					}
				}
				else if (aClone[i] == 'sa_task_to_sa_task_paper') {

					aPaper["'" + rPaper.taskpaper_id + "'"] = rCur[aClone[i]].taskpaper_id;

					// process related table sa_task_speed_sheetsperhour
					iMaxPaper = rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.getSize();
					for (m = 1; m <= iMaxPaper; m++) {

						rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.duplicateRecord(m, false, true);

						rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.taskpaper_id = aPaper["'" + rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.taskpaper_id + "'"];

						if (bCloneStd) rPaper.sa_task_paper_to_sa_task_speed_sheetsperhour.org_id = globals.org_id;
					}

				}
				else if (aClone[i] == 'sa_task_to_sa_task_color') {

					aColor["'" + rColor.taskcolor_id + "'"] = rCur[aClone[i]].taskcolor_id;

				}
				else if (aClone[i] == 'sa_task_to_sa_task_cutoff') {
					aCutOff["'" + rCutoff.taskcutoff_id + "'"] = rCur[aClone[i]].taskcutoff_id;
				}
				
				// Process Related table task_click_bw  and task_click_color
				if ( aClone[i] == 'sa_task_to_sa_task_setup' ) {

					var newTaskSetupId = rCur[aClone[i]].tasksetup_id;
					rCur[aClone[i]].selectRecord(rSetup.tasksetup_id);
					relClick = 'sa_task_to_sa_task_setup.sa_task_setup_to_sa_task_click_bw'

					if ( utils.hasRecords(rCur[relClick]) ) {
						kMax = rCur[relClick].getSize();

						for ( l = 1; l <= kMax; l++ ) {
							rCur[relClick].duplicateRecord(l, false, true);
							if ( bCloneStd ) rCur[relClick].org_id = globals.org_id;
							rCur[relClick].tasksetup_id = newTaskSetupId;
						}

					}

					relClick = 'sa_task_to_sa_task_setup.sa_task_setup_to_sa_task_click_color'

					if ( utils.hasRecords(rCur[relClick]) ) {
						kMax = rCur[relClick].getSize();

						for ( l = 1; l <= kMax; l++ ) {
							rCur[relClick].duplicateRecord(l, false, true);
							if ( bCloneStd ) rCur[relClick].org_id = globals.org_id;
							rCur[relClick].tasksetup_id = newTaskSetupId;
						}

					}
				}
			}
		}
	}
	
	databaseManager.saveData();
	
	// Now clone the impressions and clicks
	if ( utils.hasRecords(rCur.sa_task_to_sa_task_impressions) ) {
		
		var rOldImp;
		var sNewImp;
		var iClick;
		
		iMax = rCur.sa_task_to_sa_task_impressions.getSize();
		
		for ( i = 1; i <= iMax; i++ ) {
			
			rOldImp = rCur.sa_task_to_sa_task_impressions.getRecord(i);
			
			rCur.sa_task_to_sa_task_impressions.duplicateRecord(i, false, true)

			if ( bCloneStd ) rCur.sa_task_to_sa_task_impressions.org_id = globals.org_id;
			rCur.sa_task_to_sa_task_impressions.task_id = sNewPK;
			sNewImp = rCur.sa_task_to_sa_task_impressions.taskimpression_id;

			// GD - Mar 11, 2016: SL-8131
			aImpressions["'" + rOldImp.taskimpression_id + "'"] = rCur.sa_task_to_sa_task_impressions.taskimpression_id;

			// Duplicate the bw clicks
			if ( utils.hasRecords(rOldImp.sa_task_impressions_to_sa_task_click_bw) ) {
				
				iClick = rOldImp.sa_task_impressions_to_sa_task_click_bw.getSize();
				
				for ( k = 1; k <= iClick; k++ ) {
					
					rOldImp.sa_task_impressions_to_sa_task_click_bw.duplicateRecord(k, false, true);
					if ( bCloneStd ) rOldImp.sa_task_impressions_to_sa_task_click_bw.org_id = globals.org_id;
					rOldImp.sa_task_impressions_to_sa_task_click_bw.taskimpression_id = sNewImp;
				}
			}

			// Duplicate the color clicks
			if ( utils.hasRecords(rOldImp.sa_task_impressions_to_sa_task_click_color) ) {
				
				iClick = rOldImp.sa_task_impressions_to_sa_task_click_color.getSize();
				
				for ( k = 1; k <= iClick; k++ ) {
					
					rOldImp.sa_task_impressions_to_sa_task_click_color.duplicateRecord(k, false, true);
					if ( bCloneStd ) rOldImp.sa_task_impressions_to_sa_task_click_color.org_id = globals.org_id;
					rOldImp.sa_task_impressions_to_sa_task_click_color.taskimpression_id = sNewImp;
				}
			}
		}
	}
	
	
	// Now clone the speed to spoils and clicks_bw
	var bSheets = false;
	if (rCur.sa_task_to_sa_task_standard.taskstd_speed_unit === 1) {
	    bSheets = true;
	}
	
	if ( utils.hasRecords(rCur.sa_task_to_sa_task_speed) ) {
		
		var rOldSpeed;
		var sSpeed,
			iMaxSpeed = 0;

		iMaxSpeed = rCur.sa_task_to_sa_task_speed.getSize();
		for ( i = 1; i <= iMaxSpeed; i++ ) {
			
			rOldSpeed = rCur.sa_task_to_sa_task_speed.getRecord(i);
			rCur.sa_task_to_sa_task_speed.duplicateRecord(i, false, true);
			
			if ( bCloneStd ) rCur.sa_task_to_sa_task_speed.org_id = globals.org_id;
			rCur.sa_task_to_sa_task_speed.task_id = sNewPK;
			sSpeed = rCur.sa_task_to_sa_task_speed.taskspeed_id;
			aSpeed["'" + rOldSpeed.taskspeed_id + "'"] = rCur.sa_task_to_sa_task_speed.taskspeed_id;

			if ( utils.hasRecords(rOldSpeed.sa_task_speed_to_sa_task_spoil) ) {
			    
				var nMaxSpoils = databaseManager.getFoundSetCount(rOldSpeed.sa_task_speed_to_sa_task_spoil);
				
				if (bSheets) {
				    
			        rOldSpeed.sa_task_speed_to_sa_task_spoil.sort("sequence_nr asc");
				    for ( k = nMaxSpoils; k >= 2; k-- ) {
				        rOldSpeed.sa_task_speed_to_sa_task_spoil.deleteRecord(k);
				    }
				    nMaxSpoils = 1;
				}
				
				for ( k = 1; k <= nMaxSpoils; k++ ) {
					
					rOldSpeed.sa_task_speed_to_sa_task_spoil.duplicateRecord(k, false, true);
					if ( bCloneStd ) rOldSpeed.sa_task_speed_to_sa_task_spoil.org_id = globals.org_id;
					rOldSpeed.sa_task_speed_to_sa_task_spoil.taskspeed_id = sSpeed;
				}
			}
			// GD - Mar 11, 2016: Now clone the speed to click bw
			if ( utils.hasRecords(rOldSpeed.sa_task_speed_to_sa_task_click_bw) ) {

				iMax = rOldSpeed.sa_task_speed_to_sa_task_click_bw.getSize();
				for ( k = 1; k <= iMax; k++ ) {
					
					rOldSpeed.sa_task_speed_to_sa_task_click_bw.duplicateRecord(k, false, true);
					if ( bCloneStd ) rOldSpeed.sa_task_speed_to_sa_task_click_bw.org_id = globals.org_id;
					rOldSpeed.sa_task_speed_to_sa_task_click_bw.taskspeed_id = sSpeed;
					
					if (rOldSpeed.sa_task_speed_to_sa_task_click_bw.taskcolor_id) {
						
						rOldSpeed.sa_task_speed_to_sa_task_click_bw.taskcolor_id = aColor["'" + rOldSpeed.sa_task_speed_to_sa_task_click_bw.taskcolor_id + "'"]
					}
				}
			}
		}
	}

	databaseManager.saveData();
	
	// Moved all the 'hookups' to one place down here - hookup new 3rd level task tables (eg sa_task_setup) to their new 2nd level task tables (eg. sa_task_machine) 

	// GD - Mar 11, 2016: SL-8131: Fix up the Setup tab if using a machine var
	for ( i = 1; i <= rNew.sa_task_to_sa_task_setup.getSize(); i++ ) {
		rSetup = rNew.sa_task_to_sa_task_setup.getRecord(i);
		
		if (rSetup.taskmachine_id && aMachine && aMachine["'" + rSetup.taskmachine_id + "'"]) {
			rSetup.taskmachine_id = aMachine["'" + rSetup.taskmachine_id + "'"];
		}
	}
	
	// GD - Apr 8, 2014: Fix up the Variable and Spoilage tabs
	for ( i = 1; i <= rNew.sa_task_to_sa_task_spoil.getSize(); i++ ) {		
		rSpoil = rNew.sa_task_to_sa_task_spoil.getRecord(i);
		
		if (rSpoil.taskmachine_id && aMachine && aMachine["'" + rSpoil.taskmachine_id + "'"]) {
			rSpoil.taskmachine_id = aMachine["'" + rSpoil.taskmachine_id + "'"];
		}

		// sl-21794 - new spoils recs werent being hooked up to the new speed recs - still refs the old speed rec
		if (rSpoil.taskspeed_id && aSpeed && aSpeed["'" + rSpoil.taskspeed_id + "'"]) {
			rSpoil.taskspeed_id = aSpeed["'" + rSpoil.taskspeed_id + "'"];
		}
	}
	
	// sl-21794 - new speed recs werent being hooked up to the new cutoff recs - still refs the old cutoff rec
	for ( i = 1; i <= rNew.sa_task_to_sa_task_speed.getSize(); i++ ) {
		var rSpeed = rNew.sa_task_to_sa_task_speed.getRecord(i);
		
		if (rSpeed.taskcutoff_id && aCutOff && aCutOff["'" + rSpeed.taskcutoff_id + "'"]) {
			rSpeed.taskcutoff_id = aCutOff["'" + rSpeed.taskcutoff_id + "'"];
		}
	}

	_duplicateRecord = 0;

	fsTask.selectRecord(sNewPK);

	if ( sForm == "sa_task_dtl" ) {
		forms.sa_task_dtl.buildDetailView(null);
		forms.sa_task_dtl.refreshUI(null);
	}

	return sNewPK;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"44CEA592-010C-4E29-8C55-89F190A8B2F0"}
 */
function onDataChange_perfectingOnly (oldValue, newValue, event) {
	forms.sa_task_dtl.buildDetailView(null);

	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"AE8AE629-2507-4E3B-90A6-1D21950B599E"}
 */
function onDataChange_isStandard (oldValue, newValue, event) {
	var aClone = ["sa_task_to_sa_task_associated_tasks",
		"sa_task_to_sa_task_standard", "sa_task_to_sa_task_speed", "sa_task_to_sa_task_impressions",
		"sa_task_to_sa_task_cost_link", "sa_task_to_sa_task_coverage",
		"sa_task_to_sa_task_cylinder", "sa_task_to_sa_task_die",
		"sa_task_to_sa_task_folder", "sa_task_to_sa_task_ink", "sa_task_to_sa_task_machine",
		"sa_task_to_sa_task_material", "sa_task_to_sa_task_mrspoil", "sa_task_to_sa_task_paper",
		"sa_task_to_sa_task_perf", "sa_task_to_sa_task_plate", "sa_task_to_sa_task_setup",
		"sa_task_to_sa_task_spoil", "sa_task_to_sa_task_price", "sa_task_to_sa_task_udf", "sa_task_to_sa_task_cost_rate"];

	var sForm = event.getFormName();
	/*** @type {JSRecord<db:/avanti/sa_task>} */
	var rRec = forms[sForm].foundset.getSelectedRecord();
	/*** @type {JSRecord<db:/avanti/sa_task>} */
	var rRelRec, rRelRec2;
	var i = 0, k = 0, z = 0;

	var sTab1Form = scopes.globals.avUtilities_tabGetFormName("sa_task_dtl", "tab1", 1);

	// Remove the org ID from all related records
	if ( newValue == 1 ) {
		// Hide the Workflow Grouping (task functional area)
		forms[sTab1Form].elements["fldTaskFunctionalArea"].visible = false;

		// Process main task
		rRec.org_id = globals.avBase_taskStandardUUID;

		// Process related records
		for ( i = 0; i <= aClone.length - 1; i += 1 ) {
			rRec[aClone[i]].loadAllRecords();

			// Loop through all the records and set the org_id
			for ( k = 1; k <= rRec[aClone[i]].getSize(); k += 1 ) {
				rRelRec2 = rRec[aClone[i]].getRecord(k);

				processRelRecs();

				rRelRec2.org_id = globals.avBase_taskStandardUUID;
			}
		}
	} else {
		// Show the Workflow Grouping (task functional area)
		forms[sTab1Form].elements["fldTaskFunctionalArea"].visible = true;

		// Process main task
		rRec.org_id = globals.org_id;

		for ( i = 0; i <= aClone.length - 1; i += 1 ) {
			rRec[aClone[i]].loadAllRecords();

			// Loop through all the records and set the org_id
			for ( k = 1; k <= rRec[aClone[i]].getSize(); k += 1 ) {
				rRelRec2 = rRec[aClone[i]].getRecord(k);

				processRelRecs();

				rRelRec2.org_id = globals.org_id;
			}
		}
	}

	//	// Process other relations
	//	if (utils.hasRecords(rRec.sa_task_to_sa_task_setup) && utils.hasRecords(rRec.sa_task_to_sa_task_setup.sa_task_setup_to_sa_task_click_bw))
	//	{
	//		setRelRecs("sa_task_to_sa_task_setup.sa_task_setup_to_sa_task_click_bw");
	//	}
	//	if (utils.hasRecords(rRec.sa_task_to_sa_task_impressions) && utils.hasRecords(rRec.sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_bw))
	//	{
	//		setRelRecs("sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_bw");
	//	}
	//	if (utils.hasRecords(rRec.sa_task_to_sa_task_impressions) && utils.hasRecords(rRec.sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_color))
	//	{
	//		setRelRecs("sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_color");
	//	}
	//	if (utils.hasRecords(rRec.sa_task_to_sa_task_folder) && utils.hasRecords(rRec.sa_task_to_sa_task_folder.sa_task_folder_to_sa_task_folder_options))
	//	{
	//		setRelRecs("sa_task_to_sa_task_folder.sa_task_folder_to_sa_task_folder_options");
	//	}
	//	if (utils.hasRecords(rRec.sa_task_to_sa_task_speed) && utils.hasRecords(rRec.sa_task_to_sa_task_speed.sa_task_speed_to_sa_task_spoil))
	//	{
	//		setRelRecs("sa_task_to_sa_task_speed.sa_task_speed_to_sa_task_spoil");
	//	}

	/**
	 * Process related records
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2013-10-29
	 *
	 */
	function processRelRecs () {

		// Process other relations
		if ( aClone[i] === "sa_task_to_sa_task_setup" && utils.hasRecords(rRelRec2["sa_task_setup_to_sa_task_click_bw"]) ) {
			setRelRecs("sa_task_to_sa_task_setup.sa_task_setup_to_sa_task_click_bw");
		}
		if ( aClone[i] === "sa_task_to_sa_task_impressions" && utils.hasRecords(rRelRec2["sa_task_impressions_to_sa_task_click_bw"]) ) {
			setRelRecs("sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_bw");
		}
		if ( aClone[i] === "sa_task_to_sa_task_impressions" && utils.hasRecords(rRelRec2["sa_task_impressions_to_sa_task_click_color"]) ) {
			setRelRecs("sa_task_to_sa_task_impressions.sa_task_impressions_to_sa_task_click_color");
		}
		if ( aClone[i] === "sa_task_to_sa_task_folder" && utils.hasRecords(rRelRec2["sa_task_folder_to_sa_task_folder_options"]) ) {
			setRelRecs("sa_task_to_sa_task_folder.sa_task_folder_to_sa_task_folder_options");
		}
		if ( aClone[i] === "sa_task_to_sa_task_speed" && utils.hasRecords(rRelRec2["sa_task_speed_to_sa_task_spoil"]) ) {
			setRelRecs("sa_task_to_sa_task_speed.sa_task_speed_to_sa_task_spoil");
		}
	}

	/**
	 * Sets the related records
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2013-04-15
	 *
	 * @param {String} sRel - The relation to use
	 *
	 */
	function setRelRecs (sRel) {
		for ( z = 1; z <= rRec[sRel].getSize(); z += 1 ) {
			rRelRec = rRec[sRel].getRecord(z);
			if ( newValue == 1 ) {
				rRelRec.org_id = globals.avBase_taskStandardUUID;
			} else {
				rRelRec.org_id = globals.org_id;
			}
		}
	}

	return true
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A884F54D-FB7B-4D14-833F-86FA7AA4A6D7"}
 */
function onShow (firstShow, event) {
	globals.avUtilities_setFormEditMode(controller.getName(), globals.nav.mode);

	if(globals.avPref_dimension_unit == 'M') {
		sSetupUnits = i18n.getI18NMessage('avanti.lbl.setupMeters')
		sSetupUnitsRollMode = i18n.getI18NMessage('avanti.lbl.setupMetersRollMode')
		sDiameterUnits = i18n.getI18NMessage('avanti.lbl.diameterMetric')
	} else {
		sSetupUnits = i18n.getI18NMessage('avanti.lbl.setupFeet')
		sSetupUnitsRollMode = i18n.getI18NMessage('avanti.lbl.setupFeetRollMode')
		sDiameterUnits = i18n.getI18NMessage('avanti.lbl.diameter')
	}
	
	_super.onShowForm(firstShow, event);
}

/**
 * Handles onDataChange for ink color
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-06-11
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} [event] the event that triggered the action
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} [rSection] Optional section to use
 * @param {Boolean} [bSkipUpdateForPress]
 *
 * @returns {Boolean}
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"110D451E-B75E-436B-825B-6871BFF7C26D"}
 */
function onDataChange_inkColor (oldValue, newValue, event, rSection, bSkipUpdateForPress) {
	if ( !rSection && !event ) return true;

	var i = 0, k = 0, iMax = 0, iNr = 0;
	var rInk;
	var sForm;

	if ( event ) {
		sForm = event.getFormName();
	} else {
		sForm = "sa_task_worktype_section_table";
	}

	if ( !rSection ) {
		if ( utils.hasRecords(forms[sForm].foundset) ) {
			rSection = forms[sForm].foundset.getSelectedRecord();
		}
	}

	if ( !rSection ) return true;

	var sFormattedColors = scopes.avSection.formatColours(newValue);
	
	if(sFormattedColors == null || sFormattedColors == oldValue){
		rSection.worktypesection_colours = oldValue;
		return true;
	}
	else{
		newValue = sFormattedColors;
		rSection.worktypesection_colours = sFormattedColors;
	}	
	
	if ( oldValue != newValue ) {
		// Parse the entry and get the front and back colors
		var nFront = 0;
		var nBack = 0;
		var aColours = new Array();

		// Check to make certain the entry was correct
		if ( newValue.search("/") == -1 ) {
			rSection.worktypesection_colours = oldValue;
			return true;
		}
		aColours = newValue.split("/");
		nFront = parseInt(aColours[0]);
		nBack = parseInt(aColours[1]);

		// If we don't have a front and back colour, then exit
		if ( nFront === null || nBack === null ) {
			rSection.worktypesection_colours = oldValue;
			return true;
		}

//		var nTotal = nFront + nBack;
		var iFrontCount = 0;
		var iBackCount = 0;
		/**@type {JSFoundSet<db:/avanti/sa_task_worktype_ink>} */
		var fs = rSection.sa_task_worktype_section_to_sa_task_worktype_ink;
		
		/**@type {JSFoundSet<db:/avanti/sa_task_worktype_ink>} */
		var fsFront;
		/**@type {JSFoundSet<db:/avanti/sa_task_worktype_ink>} */
		var fsBack;
		/**@type {JSFoundSet<db:/avanti/sa_task_worktype_ink>} */
		var fsCoaters;

		// GD - 2013-09-20: SL-1069 - Need to keep original settings, so only remove records that need to be
		if ( utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_ink) ) {

			fs.loadAllRecords();
			fsFront = fs.duplicateFoundSet();
			fsBack = fs.duplicateFoundSet();
			fsCoaters = fs.duplicateFoundSet();

			fsFront.addFoundSetFilterParam('worktypeink_side', '=', "F");
			if ( fsFront.find() || fsFront.find() ) {
				fsFront.worktypesection_id = rSection.worktypesection_id;
				fsFront.search();
			} else {
				globals.logFailedFindFoundset(fsFront);
			}
			fsFront.sort("sequence_nr asc");

			fsBack.addFoundSetFilterParam('worktypeink_side', '=', "B");
			if ( fsBack.find() || fsBack.find() ) {
				fsFront.worktypesection_id = rSection.worktypesection_id;
				fsBack.search();
			} else {
				globals.logFailedFindFoundset(fsBack);
			}
			fsBack.sort("sequence_nr asc");

			// Build an array of Coater settings
			var aCoaters = [];
			var aCoaterNames = [];
			fsCoaters.addFoundSetFilterParam('worktypeink_side', 'LIKE', "%C%");
			fsCoaters.loadAllRecords();
			fsCoaters.sort("sequence_nr desc");

			if ( fsCoaters && fsCoaters.getSize() > 0 ) {
				fsCoaters.sort("sequence_nr desc");
				for ( i = fsCoaters.getSize(); i >= 1; i-- ) {
					rInk = fsCoaters.getRecord(i);
					if ( aCoaterNames.lastIndexOf(rInk.worktypeink_side) == -1 ) {
						aCoaterNames.push(rInk.worktypeink_side)
						aCoaters["'" + rInk.worktypeink_side + "'"] = [];
					}

					aCoaters["'" + rInk.worktypeink_side + "'"].push([rInk.inktype_id, rInk.worktypeink_color, rInk.worktypeink_washups, rInk.worktypeink_coverage]);
				}
				fsCoaters.deleteAllRecords();
			}

			for ( i = fsFront.getSize(); i >= 1; i-- ) {
				if ( i > nFront ) {
					fsFront.deleteRecord(i);
				}
			}

			for ( i = fsBack.getSize(); i >= 1; i-- ) {
				if ( i > nBack ) {
					fsBack.deleteRecord(i);
				}
			}


			iFrontCount = fsFront.getSize();
			iBackCount = fsBack.getSize();

			// GD - 2014-02-25: Renumber the back records
			if ( fsBack && fsBack.getSize() > 0 ) {
				iMax = fsBack.getSize();
				iNr = nFront + 1;
				for ( k = 1; k <= iMax; k++ ) {
					rInk = fsBack.getRecord(k);
					rInk.sequence_nr = iNr;
					iNr += 1;
				}

			}

		}


		//		// Delete all records
		//		if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_ink))
		//		{
		//			rSection.sa_task_worktype_section_to_sa_task_worktype_ink.loadAllRecords();
		//	//		rSection.sa_order_revision_detail_section_to_sa_order_revds_ink.deleteAllRecords();
		//
		//			if (rSection.sa_task_worktype_section_to_sa_task_worktype_ink.getSize() > 199) rSection.sa_task_worktype_section_to_sa_task_worktype_ink.getRecord(databaseManager.getFoundSetCount(rSection.sa_task_worktype_section_to_sa_task_worktype_ink));
		//			rSection.sa_task_worktype_section_to_sa_task_worktype_ink.deleteAllRecords();
		//		}

		// Create records for the front and back

		var _standardInkType;

		// Front side records
		rSection.worktypesection_ink_coverage = globals.avBase_getSystemPreference_Number(15); // Default 20% coverage
		_standardInkType = globals.avBase_getSystemPreference_String(16);
		if ( !_standardInkType ) {
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.itemInkProcessError_title"),
				i18n.getI18NMessage("avanti.dialog.itemInkProcessError_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"));
		}
		for ( i = iFrontCount + 1; i <= nFront; i++ ) {
			rInk = fs.getRecord(fs.newRecord(false, true));
			setInk("avanti.lbl.frontShort", i);
		}
		// Back side records
		for ( i = iBackCount + 1; i <= nBack; i++ ) {
			rInk = fs.getRecord(fs.newRecord(false, true));
			setInk("avanti.lbl.backShort", i + Number(nFront));
		}

		renumberInks();

		// GD Add coaters if necessary
		databaseManager.saveData(fs);
		globals.avCalcs_section_insertCoating_workTemplate(rSection, aCoaters);

		forms.sa_task_worktype_ink_tbl.foundset.sort("sequence_nr asc");
		
		// GD - Feb 5, 2016: SL-6920 - Override the inks for the first press in the press pool
		if (!bSkipUpdateForPress) updateInkTypeForPress(rSection, null, true);
		
		if (rSection.worktypesection_is_offsetweb == 1) {
		            
		    if (rSection.parent_id 
		            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent)
		            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs)) {
		        
                var fsSetupWebs = rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent.sa_task_worktype_section_to_sa_order_revds_web_setup;
		        
		        for (i = 1; i <= fsSetupWebs.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.getSize(); i++) {
		            
		            var rSetupWeb = fsSetupWebs.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.getRecord(i);
		            
		            if(rSetupWeb.webwiz_webnum == rSection.worktypesection_web_number) {
		                
    		            rSetupWeb.webwiz_colors = newValue;
		                break;
		            }
		        }
		    }
		    else if (!rSection.parent_id
		            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_order_revds_web_setup)) {
		                
		        rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.ordrevdswebset_colors = newValue;
		        
		        if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs)) {
		            rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.webwiz_colors = newValue;
		        }
		    }
		}
	}

	/**
	 * Renumber the ink records
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2014-02-25
	 */
	function renumberInks () {

		// GD - 2014-02-25: Need to sort the records before renumbering
		databaseManager.saveData(fs);
		fs.sort("worktypeink_side desc, sequence_nr asc");

		if ( fs && fs.getSize() > 0 ) {
			iMax = fs.getSize();
			for ( k = 1; k <= iMax; k++ ) {
				rInk = fs.getRecord(k);
				rInk.sequence_nr = k;
			}
		}
	}

	/**
	 * Sets the ink record
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2014-02-25
	 *
	 * @param {String} sLabel - the i18n key
	 * @param {Number} iSeqNr - the sequence nr to use
	 */
	function setInk (sLabel, iSeqNr) {

		rInk.worktypeink_side = i18n.getI18NMessage(sLabel);
		rInk.sequence_nr = iSeqNr;
		rInk.worktypeink_washups = 1;
		if ( _standardInkType ) rInk.inktype_id = _standardInkType;

		// GD - 2014-02-25: SL-1216 Default ink coverage
		if ( utils.hasRecords(rInk.sa_task_worktype_ink_to_in_ink_type) && rInk.sa_task_worktype_ink_to_in_ink_type.inktype_default_coverage != null ) {

			rInk.worktypeink_coverage = rInk.sa_task_worktype_ink_to_in_ink_type.inktype_default_coverage * 100;
		} else {
			rInk.worktypeink_coverage = rSection.worktypesection_ink_coverage;
		}

		// GD - 2012-11-26: Are we adding a process ink?
		if ( rInk.inktype_id ) checkForProcessInk();
	}

	/**
	 * Checks for process ink type and adds default colors
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2012-11-26
	 *
	 */
	function checkForProcessInk () {
		if ( rInk.sa_task_worktype_ink_to_in_ink_type.inktype_is_process == 1 ) {
			// Add Black first
			if ( i == 1 ) {
				rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
				rInk.itemink_id = scopes.avInv.processBlack;
			} else if ( i == 2 ) {
				rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkMagenta");
				rInk.itemink_id = scopes.avInv.processMagenta;
			} else if ( i == 3 ) {
				rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkYellow");
				rInk.itemink_id = scopes.avInv.processYellow;
			} else if ( i == 4 ) {
				rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkCyan");
				rInk.itemink_id = scopes.avInv.processCyan;
			} else {
				rInk.worktypeink_color = i18n.getI18NMessage("avanti.lbl.processInkBlack");
				rInk.itemink_id = scopes.avInv.processBlack;
			}
		}
	}

	return true;
}

/**
 * Checks to see if the inks should be updated for the press
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 5, 2016
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @param {String|UUID} [sTaskId] optional task id for the press we are switching to
 * @param {Boolean} [bFromOnDataChangeInkColor] option to avoid calling onDataChangeInkColor again
 * @public
 *
 * @properties={typeid:24,uuid:"D841C7E1-3BB7-4F38-A1D9-5ED40A08DC3C"}
 */
function updateInkTypeForPress(rSection, sTaskId, bFromOnDataChangeInkColor) {
	
	var fsPressPool = null,
		rFirstPress = null,
		iTaskTypeId = null,
		rTask = null;
	
	if (!utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_press)) return;
	
	if (sTaskId) {
		
		globals.avBase_selectedTaskID = sTaskId;
		
		if (utils.hasRecords(_to_sa_task$avbase_selectedtaskid)) {
			
			rTask = _to_sa_task$avbase_selectedtaskid.getRecord(1);
			iTaskTypeId = rTask.tasktype_id;
			
		}
	} else {
		
		fsPressPool = rSection.sa_task_worktype_section_to_sa_task_worktype_press;
		fsPressPool.sort("sequence_nr asc");
		
		rFirstPress = fsPressPool.getRecord(1);
		
		if (!utils.hasRecords(rFirstPress.sa_task_worktype_press_to_sa_task)) return;
		rTask = rFirstPress.sa_task_worktype_press_to_sa_task.getRecord(1);
		iTaskTypeId = rTask.tasktype_id;
		
	}
	
	if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_ink)
			&& scopes.avInv.updateInkRecords_pressTypes (iTaskTypeId)) {
		
		onDataChange_inkColor(null,rSection.worktypesection_colours, null, rSection, true);
		
		scopes.avInv.updateInkRecords_worktemplate (rSection, rTask, rSection.sa_task_worktype_section_to_sa_task_worktype_ink);
		
	} else if (!bFromOnDataChangeInkColor && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_ink)) {
		
		// regenerate normal inks
		onDataChange_inkColor(null,rSection.worktypesection_colours, null, rSection, true);
		
		scopes.avInv.updateInkRecords_worktemplate (rSection, rTask, rSection.sa_task_worktype_section_to_sa_task_worktype_ink);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {JSRecord<db:/avanti/sa_task>} [rTask] optional foundset to use
 *
 * @properties={typeid:24,uuid:"7EDA94E7-98FB-48F0-BC45-6B90FCC8C313"}
 */
function btnRefreshSpoilsVariables (event, rTask) {

	if ( !rTask && scopes.avUtils.isNavModeReadOnly()) return;

	var sForm;
	if ( !rTask ) {
		sForm = event.getFormName();
		if ( !utils.hasRecords(forms[sForm].foundset.sa_task_spoil_to_sa_task) ) {
			rTask = forms.sa_task_dtl.foundset.getSelectedRecord();
		} else {
			rTask = forms[sForm].foundset.sa_task_spoil_to_sa_task.getRecord(1);
		}

	}

	// Only run this method if the task machine variable is based on type other, or none
	// sl-4835 - these conditions are specific to spoils - we still need to run setup
	var bDoSpoils = true
	if ( rTask.sa_task_to_sa_task_standard.taskstd_machine_var_based_on != "O" 
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.FolderGluer
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.DieCutting
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.GrandFormatPress
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.EdgeBinding
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.EdgeFinishing
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.HighDieCutter
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.Handwork
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.RollToSheetConversion
		&& rTask.tasktype_id != scopes.avTask.TASKTYPEID.InkJetting){
//		return;
		bDoSpoils = false
	}
	
	// GF always uses variable setup - no flag needed
	var bDoSetup = rTask.sa_task_to_sa_task_standard.taskstd_variable_data == 1 || rTask.tasktype_id == scopes.avTask.TASKTYPEID.GrandFormatPress;
	
	if(!bDoSpoils && !bDoSetup){
		return
	}

	var fsTaskMachine = rTask.sa_task_to_sa_task_machine,
		iTaskMachineSize = fsTaskMachine.getSize(),
		rTaskMachine,
		aTaskMachine = [],
		fsTaskSpoil = rTask.sa_task_to_sa_task_spoil,
		iTaskSpoilsSize = fsTaskSpoil.getSize(),
		rTaskSpoil,
		fsTaskSetup = rTask.sa_task_to_sa_task_setup,
		iTaskSetupSize = fsTaskSetup.getSize(),
		rTaskSetup,
		i = 0,
		iSeqNr = 0,
		iDeleteCount = 0,
		aTaskMachine2 = [];

	// Sort the machine records and loop through them, put the machineid into an array
	fsTaskMachine.sort("sequence_nr asc");
	for ( i = iTaskMachineSize; i > 0; i-- ) {
		rTaskMachine = fsTaskMachine.getRecord(i);
		aTaskMachine.push(rTaskMachine.taskmachine_id);
		aTaskMachine2.push(rTaskMachine.taskmachine_id);
	}

	///////// SPOILS
	// Loop through the sorted spoils and update the spoil record with the taskmachine_id; create new records if spoil is missing
	if(bDoSpoils){
		fsTaskSpoil.sort("sequence_nr asc");
		// Remove any excess records
		if ( iTaskSpoilsSize > iTaskMachineSize ) {
			for ( i = iTaskSpoilsSize; i > iTaskMachineSize; i-- ) {
				fsTaskSpoil.deleteRecord(i);
				iDeleteCount++;
			}
			databaseManager.saveData(fsTaskSpoil);

			iTaskSpoilsSize = iTaskSpoilsSize - iDeleteCount;
		}
		for ( i = iTaskSpoilsSize; i > 0; i-- ) {
			rTaskSpoil = fsTaskSpoil.getRecord(i);
			rTaskSpoil.taskmachine_id = aTaskMachine.shift();
			iSeqNr++;
		}

		// If there are taskmachine_ids left in the taskmachine array, then spoil records need to be added
		for ( i = 0; i < aTaskMachine.length; i++ ) {
			rTaskSpoil = fsTaskSpoil.getRecord(fsTaskSpoil.newRecord(false, true));
			rTaskSpoil.taskmachine_id = aTaskMachine[i];
			iSeqNr++;
			rTaskSpoil.sequence_nr = iSeqNr;
		}
	}
	////////
	
	/////// SETUP
	// Loop through the sorted setup and update the setup record with the taskmachine_id; create new records if setup is missing
	if(bDoSetup){
		fsTaskSetup.sort("sequence_nr asc");
		// Remove any excess records
		if ( iTaskSetupSize > iTaskMachineSize ) {
			for ( i = iTaskSetupSize; i > iTaskMachineSize; i-- ) {
				fsTaskSetup.deleteRecord(i);
				iDeleteCount++;
			}
			databaseManager.saveData(fsTaskSetup);

			iTaskSetupSize = iTaskSetupSize - iDeleteCount;
		}

		for ( i = iTaskSetupSize; i > 0; i-- ) {
			rTaskSetup = fsTaskSetup.getRecord(i);
			rTaskSetup.taskmachine_id = aTaskMachine2.shift();
			iSeqNr++;
		}

		// If there are taskmachine_ids left in the taskmachine array, then spoil records need to be added
		for ( i = 0; i < aTaskMachine2.length; i++ ) {
			rTaskSetup = fsTaskSetup.getRecord(fsTaskSetup.newRecord(false, true));
			rTaskSetup.taskmachine_id = aTaskMachine2[i];
			iSeqNr++;
			rTaskSetup.sequence_nr = iSeqNr;
		}
	}
	///////
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public 
 *
 * @properties={typeid:24,uuid:"5C5948CA-B646-42E9-9CF1-57CB010C8590"}
 */
function onDataChange_pressSlowDown (oldValue, newValue, event) {
	var sForm, 
		sElement,
		/** @type {JSFoundset<db:/avanti/sa_task_standard>} */ 
		fs,
		fsTaskAdj,
		rTaskAdj;
	
	if (event){
		sForm = event.getFormName();
		sElement = event.getElementName();
		
		if (sElement && sForm && sElement === "fldWindowOpenAdj"){
			
			fs = forms[sForm].foundset;
			
			if (newValue === 0 
					&& utils.hasRecords(fs.sa_task_standard_to_sa_task.sa_task_to_sa_task_adj$windowopen)){
				// delete assoc records
				fs.sa_task_standard_to_sa_task.sa_task_to_sa_task_adj$windowopen.deleteAllRecords();
				
			} else if (newValue === 1){
				
				// add assoc records
				fsTaskAdj = fs.sa_task_standard_to_sa_task.sa_task_to_sa_task_adj$windowopen;
				
				rTaskAdj = fsTaskAdj.getRecord(fsTaskAdj.newRecord(false,true));
				rTaskAdj.sequence_nr = 1;
				rTaskAdj.taskadj_label = i18n.getI18NMessage("avanti.lbl.front");
				rTaskAdj = fsTaskAdj.getRecord(fsTaskAdj.newRecord(false,true));
				rTaskAdj.sequence_nr = 1;
				rTaskAdj.taskadj_label = i18n.getI18NMessage("avanti.lbl.back");
				rTaskAdj = fsTaskAdj.getRecord(fsTaskAdj.newRecord(false,true));
				rTaskAdj.sequence_nr = 1;
				rTaskAdj.taskadj_label = i18n.getI18NMessage("avanti.lbl.frontAndBack");
			}
		}
	}
	
	forms.sa_task_dtl.buildDetailView(null);
	
	return true;
}

/**
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public 
 *
 * @properties={typeid:24,uuid:"659CAC67-1217-4FCA-9F1B-DA31D75B4B5C"}
 */
function onDataChange_flatSizeAdjustment (oldValue, newValue, event) {
    
    var sForm, 
        /** @type {JSFoundset<db:/avanti/sa_task_standard>} */ 
        fs;
    
    if (newValue === 0 
            && event) {

        sForm = event.getFormName();
                        
        fs = forms[sForm].foundset;
        
        if (utils.hasRecords(fs.sa_task_standard_to_sa_task.sa_task_to_sa_task_adj$flatsize)) {
            
            fs.sa_task_standard_to_sa_task.sa_task_to_sa_task_adj$flatsize.deleteAllRecords();
        }
    }

    forms.sa_task_dtl.buildDetailView(null);

    return true;
}

/**
 * Associated Task VL
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-15
 * 
 * @public 
 *
 * @properties={typeid:24,uuid:"B31DE538-272A-46DD-B7A3-602B6BCB537F"}
 */
function setTaskAssociatedVL () {
	
	var aReturn = [],
		aDisplay = [],
		i = 0,
		iSize = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = {},
		/***@type {JSDataSet} */
		dsData;
		
		oSQL.sql = "SELECT \
                        task_description, \
                        task_id \
                    FROM sa_task t \
                    WHERE t.tasktype_id NOT IN(1, 2, 3, 5, 7, 8, 9, 10, 15, 98, 99, 100, 34) \
                        AND t.task_is_sys_standard IS NULL \
                        AND t.org_id = ? \
                        AND t.task_active = 1 \
                    ORDER BY task_description ASC";
		oSQL.args = [globals.org_id];
		dsData = globals.avUtilities_sqlDataset(oSQL);
	
	
	iSize = dsData.getMaxRowIndex();
	for ( i = 1; i <= iSize; i++ ) {
	
		aDisplay.push(dsData.getValue(i,1));
		aReturn.push(dsData.getValue(i,2));
	}
	
	application.setValueListItems("avTaskAssoc", aDisplay, aReturn);
}

/**
 * @properties={typeid:24,uuid:"A9C448E5-A186-4AE9-9C8E-2167C4619306"}
 */
function buildPaperVL () {
	
	var q = datasources.db.avanti.in_item_paper.createSelect(),
		item = q.joins.in_item_paper_to_in_item,
		brand = q.joins.in_item_paper_to_in_paper_brand,
		grade = q.joins.in_item_paper_to_in_paper_grade,
		rWTSection = forms.sa_task_worktype_section_tbl.foundset.getSelectedRecord(),
		/*** @type {JSFoundSet<db:/avanti/in_item_paper>} */
		fs;
	
	if (!rWTSection) {
	    return;
	}
	
	// results
	q.result.add(item.columns.item_desc1);
	q.result.add(item.columns.item_id);
	
	// where
	if (rWTSection.worktypesection_is_offsetweb === 1) q.where.add(grade.columns.papergrade_is_roll.eq(1));
	if ( rWTSection.paper_brand_name ) q.where.add(q.columns.paper_brand_name.eq(rWTSection.paper_brand_name));
	if ( rWTSection.papergrade_name ) q.where.add(grade.columns.papergrade_name.eq(rWTSection.papergrade_name));
	if ( rWTSection.paperbrand_name ) q.where.add(brand.columns.paperbrand_name.eq(rWTSection.paperbrand_name));
	if ( rWTSection.paper_color ) q.where.add(item.columns.item_color.eq(rWTSection.paper_color));
	if ( rWTSection.paper_weight ) q.where.add(q.columns.paper_weight.eq(rWTSection.paper_weight));
	
	// sort
	q.sort.add(q.columns.paper_weight);
	q.sort.add(item.columns.item_desc1);
	
	// execute
	fs = databaseManager.getFoundSet(q);
	globals.avCalcs_paper_set_avSales_paperInventoryVL(fs, rWTSection.item_id, true);
}

/**
 * Clears the pinfeeds
 *
 * <AUTHOR> Dotzlaw
 * @since Sep 9, 2014
 *
 * @param {JSEvent} event	
 * 
 * @return {JSRecord<db:/avanti/sa_task_worktype_section>}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"D5BAC8B3-0BF3-4420-9EE2-F3389FF3E330"}
 */
function clearPinFeeds (event) {
	
	if (!event) return null;
	
	var sForm = event.getFormName(),
		/** @type {JSFoundset<db:/avanti/sa_task_worktype_section>} */ 
		fs = forms[sForm].foundset,
		rRec = fs.getSelectedRecord();
	
	rRec.worktypesection_pin_left = null;
	rRec.worktypesection_pin_right = null;
	
	return rRec;
}

/**
 * Override the cutoff size
 *
 * <AUTHOR> Dotzlaw
 * @since Sep 9, 2014
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {JSRecord<db:/avanti/sa_task_worktype_section>}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"D6C3905B-C923-4855-B859-B3A07703D8CD"}
 */
function onDataChange_cutOffStd (oldValue, newValue, event) {
	
	if (!event) return null;
	
	var sForm = event.getFormName(),
		/** @type {JSFoundset<db:/avanti/sa_task_worktype_section>} */ 
		fs = forms[sForm].foundset,
		rRec = fs.getSelectedRecord();
	
	if (newValue == oldValue) return rRec;
	
	rRec.worktypesection_cutoff_std_ov = newValue;
	rRec.worktypesection_cutoff_std = newValue;
	
	if (rRec.worktypesection_is_flexo && rRec.worktypesection_nr_webs > 0) {
        forms.sa_task_worktype_section_dlg_flexoWeb.onDataChange_webFlexoFields(oldValue, newValue, event);
    }
	
	forms[sForm].refreshUI();
	
	return rRec;
}

/**
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"CED634B5-9FC3-4C50-94F3-37FCD0A2A68B"}
 */
function onDataChange_cutOffVar (oldValue, newValue, event) {
    
	var sForm = event.getFormName(),
		/** @type {JSFoundset<db:/avanti/sa_task_worktype_section>} */ 
		fs = forms[sForm].foundset,
		rRec = fs.getSelectedRecord();
	
	if (newValue == oldValue) return true;	

	rRec.worktypesection_cutoff_var_ov = newValue;
	
	if (rRec.worktypesection_is_flexo && rRec.worktypesection_nr_webs > 0) {
	    forms.sa_task_worktype_section_dlg_flexoWeb.onDataChange_webFlexoFields(oldValue, newValue, event);
	}
	
	return true;
}

/**
* <AUTHOR> Dotzlaw
* @since April 16, 2015
*
* @param oldValue old value
* @param newValue new value
* @param {JSEvent} event the event that triggered the action
*
 * @properties={typeid:24,uuid:"E147804C-9D47-473F-95E6-1D6650FA3AE7"}
 */
function onDataChange_requiresPlates(oldValue, newValue, event) {
	
	forms.sa_task_dtl.buildDetailView(null);
}

/**
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @param {Number} iTaskTypeID - The ID of the task type to look for
 *
 * @return
 * @properties={typeid:24,uuid:"5946A122-CE11-4491-9B8A-7DC6A2DAF02B"}
 */
function isThereTaskOfTypeID(rSection, iTaskTypeID) {
    
    if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_tasks)) {
        
        var mx = rSection.sa_task_worktype_section_to_sa_task_worktype_tasks.getSize();
        if ( mx > 20) {
            mx = databaseManager.getFoundSetCount(rSection.sa_task_worktype_section_to_sa_task_worktype_tasks);
        }

        for (var i = 1; i <= mx; i++) {
            
            var r = rSection.sa_task_worktype_section_to_sa_task_worktype_tasks.getRecord(i);

            if (utils.hasRecords(r.sa_task_worktype_tasks_to_sa_task) && r.sa_task_worktype_tasks_to_sa_task.tasktype_id == iTaskTypeID) {
                return true;
            }
        }
    }

    return false;
}

/**
 * <AUTHOR> Dotzlaw
 * @since Aug 12, 2015
 * @public 
 *
 * @properties={typeid:24,uuid:"3C267FC9-16CC-4176-B24F-074CB22D2183"}
 */
function setVL_avSales_inkTypeCoatings () {
	
	var aReturn = [],
		aDisplay = [],
		i = 0,
		iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = {},
		/***@type {JSDataSet} */
		dsData;
	
	//oSQL.sql = globals.avUtilities_appSqlQueryGet('SELECT', null, '', globals.avBase_dbase_version).query;
	oSQL.sql = "SELECT inktype_code, inktype_description, inktype_id FROM in_ink_type WHERE org_id = ? AND inktype_is_coating = ? AND inktype_active = ?";
	oSQL.args = [globals.org_id, 1, 1];
	dsData = globals.avUtilities_sqlDataset(oSQL);
	
	if ( dsData ) {
		iMax = dsData.getMaxRowIndex();
		for ( i = 1; i <= iMax; i++ ) {
	
			aDisplay.push(dsData.getValue(i, 1) + ": " + dsData.getValue(i, 2));
			aReturn.push(dsData.getValue(i, 3));
		}
	}
	
	application.setValueListItems("avSales_inkCoatingTypes", aDisplay, aReturn);
}

/**
 * <AUTHOR> Dotzlaw
 * @since Aug 12, 2015
 * @public
 * @param {String} sTaskID - the taskID for which to get the available materials
 *
 * @properties={typeid:24,uuid:"45CCAB76-F231-4A3B-A251-B92EC630DAC3"}
 */
function setVL_avSales_getTaskMaterials(sTaskID) {
	
	var aReturn = [],
		aDisplay = [],
		i = 0,
		rRec,
		fs = null,
		oSort = {};
	
	scopes.globals.avBase_selectedTaskID = sTaskID;
	
	if (utils.hasRecords(_to_sa_task$avbase_selectedtaskid)) {
	
		if ( utils.hasRecords(_to_sa_task$avbase_selectedtaskid.sa_task_to_sa_task_material) ) {
		
			fs = _to_sa_task$avbase_selectedtaskid.sa_task_to_sa_task_material;
		
			for ( i = 1; i <= fs.getSize(); i++ ) {
				
				rRec = fs.getRecord(i);
		
				(utils.hasRecords(rRec.sa_task_material_to_in_item)) ? aDisplay.push(rRec.sa_task_material_to_in_item.item_code + ": " + rRec.sa_task_material_to_in_item.item_desc1) : aDisplay.push("N/A");
				aReturn.push(rRec.item_id);
			}
			
			oSort = scopes.globals.avUtilities_arraySort(aDisplay,aReturn);
			aDisplay = oSort.a1;
			aReturn = oSort.a2;
		}
	}
	
	application.setValueListItems("avSales_getTaskMaterials", aDisplay, aReturn);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 * @returns {Boolean}
 * @public
 *
 * @properties={typeid:24,uuid:"9F70885A-121F-40D1-892F-DA2EF4BC9E85"}
 */
function onDataChange_markupType (oldValue, newValue, event) {
	
	var sForm = event.getFormName();
	
	if ( oldValue != newValue ) {

		if ( newValue == 0 ) {
			// Remove the tab
			globals.avUtilities_tabRemove("sa_task_dtl", "tab1", "avanti.lbl.markup");
		} else {
			// Add the tab
			forms.sa_task_dtl.buildDetailView();
		}
	}
	
	forms[sForm].taskstd_mrkup_type = newValue;
		
	forms[sForm].refreshUI();
	
	return true;
}

/**
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"89318CC6-A71C-4D58-92E0-B2F6D0A67727"}
 */
function onDataChange_usePreviousSection (oldValue, newValue, event) {
	
	var fs = forms.sa_task_worktype_section_tbl.foundset,
		iMax = fs.getSize(),
		iIndex = fs.getSelectedIndex(),
		rSection = fs.getSelectedRecord(),
		rPrevSection = null,
		sForm = event.getFormName();
	
	if (newValue == 1 && iIndex > 1 && iMax > 1) {
		
		rPrevSection = fs.getRecord(iIndex - 1);
		
		rSection.worktypesection_id_prev_sec = rPrevSection.worktypesection_id;
		
		forms[sForm].elements.fldPreviousSection.visible = true;
		
		setEnabledOnPaperFields(sForm, false);
		
	} else {
		
		rSection.worktypesection_use_prev_sec = null;
		
		rSection.worktypesection_id_prev_sec = null;
		
		forms[sForm].elements.fldPreviousSection.visible = false;
		
		setEnabledOnPaperFields(sForm, true);
	}
	return true;
}

/**
 * @param {String} sForm
 * @param {Boolean} bOnOff
 *
 * @properties={typeid:24,uuid:"AE73FE00-7A48-441C-A51F-5824C5B3175C"}
 */
function setEnabledOnPaperFields(sForm, bOnOff){
	forms[sForm].elements.btnLookupPaper.enabled = bOnOff;
	forms[sForm].elements.btnClear_paper_weight.enabled = bOnOff;
	forms[sForm].elements.btnClear_paper_brand_name.enabled = bOnOff;
	forms[sForm].elements.btnClear_papergrade_name.enabled = bOnOff;
	forms[sForm].elements.btnClear_paperbrand_name.enabled = bOnOff;
	forms[sForm].elements.btnClear_paper_color.enabled = bOnOff;
	forms[sForm].elements.fldSelectedPaper.enabled = bOnOff;
	forms[sForm].elements.paper_brand_name.enabled = bOnOff;
	forms[sForm].elements.papergrade_name.enabled = bOnOff;
	forms[sForm].elements.paper_weight.enabled = bOnOff;
	forms[sForm].elements.paperbrand_name.enabled = bOnOff;
	forms[sForm].elements.paper_color.enabled = bOnOff;
}

/**
 * @public
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"5A4C0AF8-30D0-456C-B9B1-CCE4D47F9D8D"}
 */
function checkForRollToSheetConverter (rSection) {

	var i = 0,
		rTask = null,
		fs = rSection.sa_task_worktype_section_to_sa_task_worktype_tasks,
		bReturn = false;
	
	for ( i = 1; i <= fs.getSize(); i++ ) {
		
		rTask = fs.getRecord(i);
		
		if (utils.hasRecords(rTask.sa_task_worktype_tasks_to_sa_task) 
				&& rTask.sa_task_worktype_tasks_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.RollToSheetConversion) {
			
			bReturn = true;
			
			if (rSection.worktypesection_is_generic === 1 ) {
				
				rSection.worktypesection_roll_to_sheet = "offline";
				
			} else {
				
				rSection.worktypesection_roll_to_sheet = "inline";
			}
			break;
		}
	}
	
	if (!bReturn) {
		
		rSection.worktypesection_roll_to_sheet = null;

	}
	
	return bReturn;
}

/**
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 * @public
 *
 * @properties={typeid:24,uuid:"07F00027-A335-4F13-99F9-0655DEC17BE4"}
 */
function onDataChange_rollToSheetConversion (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		/** @type {JSFoundset<db:/avanti/sa_task_worktype_tasks>} */ 
		fs = forms[sForm].foundset.sa_task_worktype_section_to_sa_task_worktype_tasks,
		rTask = null,
		i = 0;
	
	if (newValue === "inline" ) {
		
		// Clear the tasks material if any
		for ( i = 1; i <= fs.getSize(); i++ ) {
			
			rTask = fs.getRecord(i);
			
			if (rTask.sa_task_worktype_tasks_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.RollToSheetConversion) {
				
				rTask.item_id = null;

				break;
			}
		}
	}
}

/**
 * Change to bleed size should be copied to all fields
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2016
 * @param {Number} oldValue
 * @param {Number} newValue 
 * @param {JSEvent} event
 * @public
 *
 * @properties={typeid:24,uuid:"61EC999D-6259-4DF7-BD91-5EB569B80BDF"}
 */
function onDataChange_bleedSize (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_worktype_section>} */ 
		rSection = forms[sForm].foundset.getSelectedRecord();	
	
	if (rSection.worktypesection_is_grandformat === 1 && globals.avBase_getSystemPreference_Number(139) === 1) {
		
		if (rSection.worktypesection_bleed_w === 0) {
			
			rSection.worktypesection_bleed_top = 0;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else if (rSection.worktypesection_bleed_w === 1) {
			
			rSection.worktypesection_bleed_top = newValue;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else {
			
			rSection.worktypesection_bleed_top = newValue;
			rSection.worktypesection_bleed_bottom = newValue;
			
		}
		
		if (rSection.worktypesection_bleed_l === 0) {
			
			rSection.worktypesection_bleed_left = 0;
			rSection.worktypesection_bleed_right = 0;
			
		} else if (rSection.worktypesection_bleed_l === 1) {
			
			rSection.worktypesection_bleed_left = newValue;
			rSection.worktypesection_bleed_right = 0;
			
		} else {
			
			rSection.worktypesection_bleed_left = newValue;
			rSection.worktypesection_bleed_right = newValue;
			
		}
		
	} else {
		
		if (rSection.worktypesection_bleed_w === 0) {
			
			rSection.worktypesection_bleed_left = 0;
			rSection.worktypesection_bleed_right = 0;
			
		} else if (rSection.worktypesection_bleed_w === 1) {
			
			rSection.worktypesection_bleed_left = newValue;
			rSection.worktypesection_bleed_right = 0;
			
		} else {
			
			rSection.worktypesection_bleed_left = newValue;
			rSection.worktypesection_bleed_right = newValue;
			
		}
		if (rSection.worktypesection_bleed_l === 0) {
			
			rSection.worktypesection_bleed_top = 0;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else if (rSection.worktypesection_bleed_l === 1) {
			
			rSection.worktypesection_bleed_top = newValue;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else {
			
			rSection.worktypesection_bleed_top = newValue;
			rSection.worktypesection_bleed_bottom = newValue;
			
		}
	}
}

/**
 * Change to bleed size should be copied to all fields
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2016
 * @param {Number} oldValue
 * @param {Number} newValue 
 * @param {JSEvent} event
 * @public
 *
 * @return
 * @properties={typeid:24,uuid:"0C71965A-4B76-4673-9381-87E09DD7B59F"}
 */
function onDataChange_bleedDim1 (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_worktype_section>} */ 
		rSection = forms[sForm].foundset.getSelectedRecord(),
		sWorkType = rSection.worktypesection_work_type,
		nBleedSize = 0;
	
	if (sWorkType != "F") return true;
	
	rSection.worktypesection_bleed_size = rSection.worktypesection_bleed_size ? rSection.worktypesection_bleed_size : globals.avBase_getSystemPreference_Number(10);
	nBleedSize = rSection.worktypesection_bleed_size;
	
	if (rSection.worktypesection_is_grandformat === 1 && globals.avBase_getSystemPreference_Number(139) === 1) {
		
		if (newValue === 0) {
			
			rSection.worktypesection_bleed_top = 0;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else if (newValue === 1) {
			
			rSection.worktypesection_bleed_top = nBleedSize;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else {
			
			rSection.worktypesection_bleed_top = nBleedSize;
			rSection.worktypesection_bleed_bottom = nBleedSize;
			
		}
		
	} else {
		
		if (newValue === 0) {
			
			rSection.worktypesection_bleed_left = 0;
			rSection.worktypesection_bleed_right = 0;
			
		} else if (newValue === 1) {
			
			rSection.worktypesection_bleed_left = nBleedSize;
			rSection.worktypesection_bleed_right = 0;
			
		} else {
			
			rSection.worktypesection_bleed_left = nBleedSize;
			rSection.worktypesection_bleed_right = nBleedSize;
			
		}
	}
	return true;
}

/**
 * Change to bleed size should be copied to all fields
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2016
 * @param {Number} oldValue
 * @param {Number} newValue 
 * @param {JSEvent} event
 * @public
 *
 * @return
 * @properties={typeid:24,uuid:"DFFFA353-08FA-4796-B3B5-4E8CABDF2E1E"}
 */
function onDataChange_bleedDim2 (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_worktype_section>} */ 
		rSection = forms[sForm].foundset.getSelectedRecord(),
		sWorkType = rSection.worktypesection_work_type,
		nBleedSize = 0;
	
	if (sWorkType != "F") return true;
	
	rSection.worktypesection_bleed_size = rSection.worktypesection_bleed_size ? rSection.worktypesection_bleed_size : globals.avBase_getSystemPreference_Number(10);
	nBleedSize = rSection.worktypesection_bleed_size;
	
	if (rSection.worktypesection_is_grandformat === 1 && globals.avBase_getSystemPreference_Number(139) === 1) {
		
		if (newValue === 0) {
			
			rSection.worktypesection_bleed_left = 0;
			rSection.worktypesection_bleed_right = 0;
			
		} else if (newValue === 1) {
			
			rSection.worktypesection_bleed_left = nBleedSize;
			rSection.worktypesection_bleed_right = 0;
			
			
		} else {
			
			rSection.worktypesection_bleed_left = nBleedSize;
			rSection.worktypesection_bleed_right = nBleedSize;
			
		}
		
	} else {
		
		if (newValue === 0) {
			
			rSection.worktypesection_bleed_top = 0;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else if (newValue === 1) {
			
			rSection.worktypesection_bleed_top = nBleedSize;
			rSection.worktypesection_bleed_bottom = 0;
			
		} else {
			
			rSection.worktypesection_bleed_top = nBleedSize;
			rSection.worktypesection_bleed_bottom = nBleedSize;
			
		}
	}
	return true;
}

/**
 * Updates some UI forms with the optional bleed fields
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2016
 * @param {String} sForm - the form name
 * @param {String} sWorkTypeCode
 * @public
 *
 * @properties={typeid:24,uuid:"B253BB39-1D18-48B9-BADE-71990E0D95FD"}
 */
function updateBleedFields (sForm, sWorkTypeCode) {
	
	/** @type {JSRecord<db:/avanti/sa_task_worktype_section>} **/
	var rSection = null;
	
	if (sWorkTypeCode === "F" ) {
		
		rSection = forms[sForm].foundset.getSelectedRecord();
		
		forms[sForm].elements.fldBleedSize.visible = true;
		
		// Show the fields
		if (globals.avBase_getSystemPreference_Number(139) === 1) {
			
			forms[sForm].elements.lblBleedsForWidth.visible = false;
			forms[sForm].elements.lblBleedsForHeight.visible = false;
						
			forms[sForm].elements.fldBleedLeft.visible = false;
			forms[sForm].elements.fldBleedRight.visible = false;
			forms[sForm].elements.fldBleedTop.visible = false;
			forms[sForm].elements.fldBleedBottom.visible = false;
			
			if (rSection.worktypesection_is_grandformat === 1 ) {
				forms[sForm].elements.lblPanelBleedsForWidth.visible = false;
				forms[sForm].elements.lblPanelBleedsForHeight.visible = false;
				if (sForm == "sa_task_worktype_section_dlg_grandFormat") {
	    			if (rSection.worktypesection_allow_tiling === 1 ){
	    				forms[sForm].elements.lblPanelBleedsForHeight_139.visible = true;
	    				forms[sForm].elements.lblPanelBleedsForWidth_139.visible = true;
	    				forms[sForm].elements.lblBleedsForHeight_139.visible = false;
	    				forms[sForm].elements.lblBleedsForWidth_139.visible = false;
	    			} else {
	    				forms[sForm].elements.lblPanelBleedsForHeight_139.visible = false;
	    				forms[sForm].elements.lblPanelBleedsForWidth_139.visible = false;
	    				forms[sForm].elements.lblBleedsForHeight_139.visible = true;
	    				forms[sForm].elements.lblBleedsForWidth_139.visible = true;
	    			}
				}
			}
			else {
				forms[sForm].elements.lblBleedsForHeight_139.visible = true;
				forms[sForm].elements.lblBleedsForWidth_139.visible = true;			
			}
			
			forms[sForm].elements.fldBleedLeft_139.visible = true;
			forms[sForm].elements.fldBleedLeft_139.visible = true;
			forms[sForm].elements.fldBleedRight_139.visible = true;
			forms[sForm].elements.fldBleedTop_139.visible = true;
			forms[sForm].elements.fldBleedBottom_139.visible = true;
			
		} else {
			
			if (rSection.worktypesection_is_grandformat === 1 ) {
			    if (sForm == "sa_task_worktype_section_dlg_grandFormat") {
    				if (rSection.worktypesection_allow_tiling === 1) {
    					forms[sForm].elements.lblPanelBleedsForWidth.visible = true;
    					forms[sForm].elements.lblPanelBleedsForHeight.visible = true;
    				} else {
    					forms[sForm].elements.lblPanelBleedsForWidth.visible = false;
    					forms[sForm].elements.lblPanelBleedsForHeight.visible = false;	
    				}
    				forms[sForm].elements.lblPanelBleedsForHeight_139.visible = false;
    				forms[sForm].elements.lblPanelBleedsForWidth_139.visible = false;
			    }
			}
			
			if (rSection.worktypesection_allow_tiling === 1) {
				forms[sForm].elements.lblBleedsForWidth.visible = false;
				forms[sForm].elements.lblBleedsForHeight.visible = false;
			} else {
				forms[sForm].elements.lblBleedsForWidth.visible = true;
				forms[sForm].elements.lblBleedsForHeight.visible = true;		
			}
			
			forms[sForm].elements.fldBleedLeft.visible = true;
			forms[sForm].elements.fldBleedRight.visible = true;
			forms[sForm].elements.fldBleedTop.visible = true;
			forms[sForm].elements.fldBleedBottom.visible = true;
			
			
			forms[sForm].elements.lblBleedsForHeight_139.visible = false;
			forms[sForm].elements.lblBleedsForWidth_139.visible = false;
			
			forms[sForm].elements.fldBleedLeft_139.visible = false;
			forms[sForm].elements.fldBleedRight_139.visible = false;
			forms[sForm].elements.fldBleedTop_139.visible = false;
			forms[sForm].elements.fldBleedBottom_139.visible = false;
	
		}
		
	} else {
		
		// Hide the fields
		forms[sForm].elements.lblBleedsForWidth.visible = false;
		forms[sForm].elements.lblBleedsForHeight.visible = false;
		
		forms[sForm].elements.fldBleedSize.visible = false;
		forms[sForm].elements.fldBleedLeft.visible = false;
		forms[sForm].elements.fldBleedRight.visible = false;
		forms[sForm].elements.fldBleedTop.visible = false;
		forms[sForm].elements.fldBleedBottom.visible = false;
		
		forms[sForm].elements.lblBleedsForHeight_139.visible = false;
		forms[sForm].elements.lblBleedsForWidth_139.visible = false;
		
		forms[sForm].elements.fldBleedLeft_139.visible = false;
		forms[sForm].elements.fldBleedRight_139.visible = false;
		forms[sForm].elements.fldBleedTop_139.visible = false;
		forms[sForm].elements.fldBleedBottom_139.visible = false;
	}
}

/**
 * Description
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 8, 2016
 * @param {Number} oldValue
 * @param {Number} newValue 
 * @param {JSEvent} event
 * @public
 *
 * @return
 * @properties={typeid:24,uuid:"FA77AD65-7D64-49EA-91C4-015723312DB5"}
 */
function onDataChange_bleedsForFlatwork (oldValue, newValue, event) {
	
	var sForm = event.getFormName(),
		/** @type {JSRecord<db:/avanti/sa_task_worktype_section>} */ 
		rSection = forms[sForm].foundset.getSelectedRecord(),
		sWorkType = rSection.worktypesection_work_type,
		sElementName = event.getElementName(),
		sDataprovider = forms[sForm].elements[sElementName].getDataProviderID();
	
	if (sWorkType != "F") return true;
	
	// Save the new value so the checks below are easier
	rSection[sDataprovider] = newValue;
	
	databaseManager.saveData(rSection);
	
	if (rSection.worktypesection_is_grandformat === 1 && globals.avBase_getSystemPreference_Number(139) === 1) {
					
		if (rSection.worktypesection_bleed_top > 0 && rSection.worktypesection_bleed_bottom > 0) {

			rSection.worktypesection_bleed_w = 2;
			
		} else if (rSection.worktypesection_bleed_top > 0 || rSection.worktypesection_bleed_bottom > 0) {

			rSection.worktypesection_bleed_w = 1;
			
		} else {

			rSection.worktypesection_bleed_w = 0;
		}
		
		if (rSection.worktypesection_bleed_left > 0 && rSection.worktypesection_bleed_right > 0) {

			rSection.worktypesection_bleed_l = 2;
			
		} else if (rSection.worktypesection_bleed_left > 0 || rSection.worktypesection_bleed_right > 0) {

			rSection.worktypesection_bleed_l = 1;
			
		} else {

			rSection.worktypesection_bleed_l = 0;
		}
		
	} else {
		
		if (rSection.worktypesection_bleed_left > 0 && rSection.worktypesection_bleed_right > 0) {

			rSection.worktypesection_bleed_w = 2;
			
		} else if (rSection.worktypesection_bleed_left > 0 || rSection.worktypesection_bleed_right > 0) {

			rSection.worktypesection_bleed_w = 1;
			
		} else {

			rSection.worktypesection_bleed_w = 0;
		}
		
		if (rSection.worktypesection_bleed_top > 0 && rSection.worktypesection_bleed_bottom > 0) {

			rSection.worktypesection_bleed_l = 2;
			
		} else if (rSection.worktypesection_bleed_top > 0 || rSection.worktypesection_bleed_bottom > 0) {

			rSection.worktypesection_bleed_l = 1;
			
		} else {

			rSection.worktypesection_bleed_l = 0;
		}
	}
	return true;
}

/**
 * Updates some UI forms with the optional edge fields
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2016
 * @param {String} sForm
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @public
 *
 * @properties={typeid:24,uuid:"D15E184D-DD6C-40FA-A657-BE6B83C8FEE8"}
 */
function updateEdgeFields (sForm, rSection) {

	
	// Show the fields
	if (rSection.worktypesection_is_grandformat === 1  
			&& rSection.worktypesection_allow_tiling === 1 
			&& globals.avBase_getSystemPreference_Number(139) === 1) {
			
		forms[sForm].elements.lblEdgesForWidth.visible = false;
		forms[sForm].elements.lblEdgesForHeight.visible = false;
					
		forms[sForm].elements.fldEdgeLeft.visible = false;
		forms[sForm].elements.fldEdgeRight.visible = false;
		forms[sForm].elements.fldEdgeTop.visible = false;
		forms[sForm].elements.fldEdgeBottom.visible = false;
		
		forms[sForm].elements.lblEdgesForHeight_139.visible = true;
		forms[sForm].elements.lblEdgesForWidth_139.visible = true;
		
		forms[sForm].elements.fldEdgeLeft_139.visible = true;
		forms[sForm].elements.fldEdgeLeft_139.visible = true;
		forms[sForm].elements.fldEdgeRight_139.visible = true;
		forms[sForm].elements.fldEdgeTop_139.visible = true;
		forms[sForm].elements.fldEdgeBottom_139.visible = true;
		
	} else if (rSection.worktypesection_is_grandformat === 1 
			&& rSection.worktypesection_allow_tiling === 1) {
		
		forms[sForm].elements.lblEdgesForWidth.visible = true;
		forms[sForm].elements.lblEdgesForHeight.visible = true;
		
		forms[sForm].elements.fldEdgeLeft.visible = true;
		forms[sForm].elements.fldEdgeRight.visible = true;
		forms[sForm].elements.fldEdgeTop.visible = true;
		forms[sForm].elements.fldEdgeBottom.visible = true;
		
		forms[sForm].elements.lblEdgesForHeight_139.visible = false;
		forms[sForm].elements.lblEdgesForWidth_139.visible = false;
		
		forms[sForm].elements.fldEdgeLeft_139.visible = false;
		forms[sForm].elements.fldEdgeRight_139.visible = false;
		forms[sForm].elements.fldEdgeTop_139.visible = false;
		forms[sForm].elements.fldEdgeBottom_139.visible = false;

	} else {
		
		// Hide the fields
		forms[sForm].elements.lblEdgesForWidth.visible = false;
		forms[sForm].elements.lblEdgesForHeight.visible = false;
		
		forms[sForm].elements.fldEdgeLeft.visible = false;
		forms[sForm].elements.fldEdgeRight.visible = false;
		forms[sForm].elements.fldEdgeTop.visible = false;
		forms[sForm].elements.fldEdgeBottom.visible = false;
		
		forms[sForm].elements.lblEdgesForHeight_139.visible = false;
		forms[sForm].elements.lblEdgesForWidth_139.visible = false;
		
		forms[sForm].elements.fldEdgeLeft_139.visible = false;
		forms[sForm].elements.fldEdgeRight_139.visible = false;
		forms[sForm].elements.fldEdgeTop_139.visible = false;
		forms[sForm].elements.fldEdgeBottom_139.visible = false;
	}
}

/**
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"9367EC21-2A99-42B5-8384-7AFEDA8D6D15"}
 */
function onDataChange_folder (oldValue, newValue, event) {
	
	var sForm = event.getFormName();
	
	forms[sForm].worktypesection_flg_folder_ov = newValue;
	
	forms[sForm].onShow();
		
	return true;
		
}

/**
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"B5575ABF-CA66-4FCC-BDEB-5063E5A9F0F7"}
 */
function onDataChange_postPressTrim (oldValue, newValue, event) {
    
    var sForm = event.getFormName();
    
    forms[sForm].worktypesection_flg_pptrim_ov = newValue;
    
    forms[sForm].onShow();
        
    return true;
        
}

/**
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F1127C0F-B2D6-428E-83E5-A72912E36BD4"}
 */
function onDataChange_finalTrim (oldValue, newValue, event) {
    
    var sForm = event.getFormName();
    
    forms[sForm].worktypesection_flg_ftrim_ov = newValue;
    
    forms[sForm].onShow();
        
    return true;
        
}

/**
 * Gets the fold patterns for the section
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSec
 * @param {Boolean} bClear - clear the VL
 * <AUTHOR> Dotzlaw
 * @since Oct 4, 2016
 * @public
 *
 * @properties={typeid:24,uuid:"D277622A-5A3A-41B7-B4EC-0B0B3C576985"}
 */
function setVL_avSales_sectionTaskMachineDesc (rSec, bClear) {
	
	var 
		aReturnOptimal = [null],
		aDisplayOptimal = ["Optimal"],
		aReturn = [],
		aDisplay = [],
		i = 0,
		k = 0,
		/** @type {JSRecord<db:/avanti/sa_task_machine>} */ 
		rMachine = null,
		/** @type {JSRecord<db:/avanti/sa_task>} */ 
		rFolder = null;
	
	if (!rSec || bClear) {
		
		// Cannot set the vl, so clear it and return
		application.setValueListItems("avSales_foldPatterns", aDisplayOptimal, aReturnOptimal);
		
		return;
		
	}

	
	// If we already have a folder task for this section, then only the fold patterns for that folder should be shown
	rFolder = scopes.avTask.getTaskFolder(rSec)
	
	if (rFolder) {
		
		getFoldPatterns();
		
	} else {
		
		// If we have no folder task, then we need to get the folders in the standards
		
		scopes.globals.avSales_selectedRevisionSectionTaskTypeID = scopes.avTask.TASKTYPEID.Folder;
		
		if (utils.hasRecords(_to_sa_task$avsales_selectedrevisionsectiontasktypeid)) {
			
			for ( i = 1; i <= _to_sa_task$avsales_selectedrevisionsectiontasktypeid.getSize(); i++ ) {
				
				rFolder = _to_sa_task$avsales_selectedrevisionsectiontasktypeid.getRecord(i);
				
				getFoldPatterns();
			}
		}	
	}
	
	function getFoldPatterns() {
		
		if (utils.hasRecords(rFolder.sa_task_to_sa_task_machine)) {
			
			for ( k = 1; k <= rFolder.sa_task_to_sa_task_machine.getSize(); k++ ) {
				
				rMachine = rFolder.sa_task_to_sa_task_machine.getRecord(k);
				
				if (utils.hasRecords(rMachine.sa_task_machine_to_app_jdf_fold) && aReturn.lastIndexOf(rMachine.sa_task_machine_to_app_jdf_fold.jdffold_id) == -1)
				{							
					aReturn.push(rMachine.sa_task_machine_to_app_jdf_fold.jdffold_id);

					aDisplay.push(rMachine.sa_task_machine_to_app_jdf_fold.clc_owner_jdf_desc);
						
				}
			}
		}
	}
	
	var oObj = globals.avUtilities_arraySort(aDisplay, aReturn);
	
	if (oObj && oObj.a1.length > 0 && oObj.a2.length > 0)
	{
		application.setValueListItems("avSales_foldPatterns", aDisplayOptimal.concat(oObj.a1), aReturnOptimal.concat(oObj.a2));
	}
	else
	{
		application.setValueListItems("avSales_foldPatterns", aDisplayOptimal, aReturnOptimal);
	}
}

/**
 * @param {JSEvent} event the event that triggered the action
 * @param {String} sType - either cost or price
 * @public
 * @properties={typeid:24,uuid:"0E60F2AF-F2D4-4FEA-807B-0CF4CE7CEE56"}
 */
function btnCopyDown_digitalWebCustom (event, sType) {
	
	var fsClicks = _to_sa_task_click_bw$selectedtaskspeedid,
		iStart =0,
		rClickOrig = null,
		rClick = null,
		i = 0,
		j = 0,
		sColumn = (sType == "price") ? "price_nr_" : "nr_";
	
	if (utils.hasRecords(fsClicks)){
		
		iStart = fsClicks.getSelectedIndex() + 1;
		rClickOrig = fsClicks.getSelectedRecord();
		
		for ( i = iStart; i <= fsClicks.getSize(); i++ ) {
			
			rClick = fsClicks.getRecord(i);
			
			for ( j= 1; j <= 20; j++ ) {
				
				rClick[sColumn + j] = rClickOrig[sColumn + j];
			}
		}
		databaseManager.saveData(fsClicks);
	}
}

/**
 * @param {JSEvent} event the event that triggered the action
 * @public
 * @properties={typeid:24,uuid:"81B7BE6A-DEC5-45B3-8174-AC945E9557AB"}
 */
function btnCopyClickCostAddMarkup_digitalWebCustom (event) {
	
	scopes.avTask.selectedTaskSpeedID = forms.sa_task_speed_16.foundset.getSelectedRecord().taskspeed_id;
	
	var 
		fsSpeedCost = _to_sa_task_speed$selectedtaskcutoffid_clickcost,
		rSpeedCost = null,
		sForm = event.getFormName(),
		nMrkUp = (sForm && forms[sForm]._nMarkup) ? forms[sForm]._nMarkup : 0,
		nValue = (sForm && forms[sForm]._nValue) ? forms[sForm]._nValue : 0,
		rClickCost = null,
		rSpeedPrice = _to_sa_task_speed$selectedtaskcutoffid_clickprice.getSelectedRecord(),
		rClickPrice = null,
		i = 0,
		j = 0,
		k = 0,
		sColumnCost = "nr_",
		sColumnPrice = "price_nr_";
		
	if (nMrkUp > 0 || nValue > 0) {
		
		// Find the matching click cost record for the impression range
		for ( i = 1; i <= fsSpeedCost.getSize(); i++ ) {
			
			rSpeedCost = fsSpeedCost.getRecord(i);
			
			if (rSpeedCost.taskspeed_from == rSpeedPrice.taskspeed_from && rSpeedCost.taskspeed_to == rSpeedPrice.taskspeed_to) {
				
				// Matching cost found
				
				rSpeedCost.sa_task_speed_to_sa_task_click_bw.sort("sa_task_click_bw_to_sa_task_color.taskcolor_nr");
				rSpeedPrice.sa_task_speed_to_sa_task_click_bw.sort("sa_task_click_bw_to_sa_task_color.taskcolor_nr");
				
				for ( k = 1; k <= rSpeedCost.sa_task_speed_to_sa_task_click_bw.getSize(); k++ ) {
					
					rClickCost = rSpeedCost.sa_task_speed_to_sa_task_click_bw.getRecord(k);
					rClickPrice = rSpeedPrice.sa_task_speed_to_sa_task_click_bw.getRecord(k);
					
					for ( j= 1; j <= 20; j++ ) {
						
						if (nMrkUp > 0) rClickPrice[sColumnPrice + j] = rClickCost[sColumnCost + j] * (1 + nMrkUp / 100);
						if (nValue > 0) rClickPrice[sColumnPrice + j] = rClickCost[sColumnCost + j] + nValue;
					}
				}
				databaseManager.saveData(rSpeedPrice.sa_task_speed_to_sa_task_click_bw);
			}
		}
	}
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"97BBEC01-EA49-4E6B-AB85-BAEC019F47A4"}
 */
function onDataChange_differentRateFrontandBack(oldValue, newValue, event)
{
	if (oldValue != newValue)
	{
		var sForm = event.getFormName();
		/** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
		var jsForm = forms[sForm].foundset;

		jsForm.taskstd_diff_rates_fb = newValue;

		databaseManager.saveData();

		forms[sForm].refreshUI();
	}

	return true;
}

/**
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"65E3C28A-DF7F-4C08-AB7E-43B60936613D"}
 */
function onDataChange_clicksUseColor(oldValue, newValue, event) {
    if (oldValue != newValue) {
        var sForm = event.getFormName();
        /** @type {JSFoundSet<db:/avanti/sa_task_standard>}*/
        var jsForm = forms[sForm].foundset;

        jsForm.taskstd_clicks_x_colors = newValue;

        databaseManager.saveData(jsForm);

    }

    return true;
}

/**
 * Sets the paper details in the different section dialogs
 *
 * <AUTHOR> Dotzlaw
 * @since May 9, 2018
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @return {JSRecord<db:/avanti/in_item_paper>}
 * @public
 *
 * @properties={typeid:24,uuid:"90E0D07E-F5B1-4FFB-A022-48C9287DE35C"}
 */
function setPaperDetails(rSection) {
    
    if (!rSection) {
        return null;
    }
    
    if (rSection && rSection.item_id) {
        
        globals.avSales_selectedItemUUID = rSection.item_id;
        
        if (utils.hasRecords(_to_in_item$avsales_selecteditemuuid)) {
            
            var rItem = _to_in_item$avsales_selecteditemuuid.getRecord(1);
            
            if (utils.hasRecords(rItem.in_item_to_in_item_paper)) {
                
                var rItemPaper = rItem.in_item_to_in_item_paper.getRecord(1);

                rSection.paper_brand_name = rItemPaper.paper_brand_name;
                rSection.paper_color = rItem.item_color;
                rSection.paper_weight = rItemPaper.paper_weight;
                rSection.paperbrand_id = rItemPaper.paperbrand_id;
                rSection.papergrade_id = rItemPaper.papergrade_id;
                
                if (utils.hasRecords(rItemPaper.in_item_paper_to_in_paper_brand)) {
                    
                    rSection.paperbrand_name = rItemPaper.in_item_paper_to_in_paper_brand.paperbrand_name;
                }
                if (utils.hasRecords(rItemPaper.in_item_paper_to_in_paper_grade)) {
                    
                    rSection.papergrade_name = rItemPaper.in_item_paper_to_in_paper_grade.papergrade_name;
                }
                
                if (rSection.worktypesection_is_offsetweb == 1
                        || (rSection.worktypesection_is_flexo == 1 && rSection.worktypesection_flexo_worktype == scopes.avPress.FLEXO_WORKTYPE.WebCutoff)) {
                    
                    if (rSection.parent_id 
                            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent)
                            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs)) {
                        
                        var fsSetupWebs = rSection.sa_task_worktype_section_to_sa_task_worktype_section$parent.sa_task_worktype_section_to_sa_order_revds_web_setup;
                        
                        for (var i = 1; i <= fsSetupWebs.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.getSize(); i++) {
                            
                            var rSetupWeb = fsSetupWebs.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.getRecord(i);
                            
                            if(rSetupWeb.webwiz_webnum == rSection.worktypesection_web_number) {
                                
                                rSetupWeb.item_id = rSection.item_id;
                                break;
                            }
                        }
                    }
                    else if (!rSection.parent_id
                            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_order_revds_web_setup)) {
                                
                        rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.item_id = rSection.item_id;
                        
                        if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs)) {
                            rSection.sa_task_worktype_section_to_sa_order_revds_web_setup.sa_order_revds_web_setup_to_sa_order_revds_web_setup_webs.item_id = rSection.item_id;
                        }
                    }
                }
                
                return rItemPaper;
            }
        } 
    }
    
    return null;
}

/**
 * @param {UUID|String} sDivID
 * @param {UUID|String} sPlantID
 *
 * @properties={typeid:24,uuid:"5E10BCC0-0AD1-4382-A98A-DB2ACA7EC51C"}
 */
function setTemplateTempDivAndPlant(sDivID, sPlantID) {
    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
        globals.avBase_divIDTemp = sDivID;
        globals.avBase_plantIDTemp = sPlantID;
        globals.avBase_setTableFilter();
        
        if (sDivID) {
            scopes.avVL.loadVlFromSQL('avSales_taskWorktypePlants', 
                "SELECT plant_id, plant_name FROM sys_plant WHERE org_id = ? AND div_id = ? ORDER BY 2 ASC",
                [globals.org_id, sDivID.toString()]);
        }
        else {
            scopes.avVL.clearVL('avSales_taskWorktypePlants');
        }
    }
}

/**
 * @properties={typeid:24,uuid:"A7095F93-B6DB-41B9-A6C8-BEF0AC4FDDBE"}
 */
function revertTemplateTempDivAndPlant() {
    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) {
        globals.avBase_divIDTemp = null;
        globals.avBase_plantIDTemp = null;
        globals.avBase_plantIDTemps = null;
        globals.avBase_setTableFilter();
    }
}

/**
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"E5B5F94F-5746-485E-8368-77141F53F34A"}
 */
function onDataChange_finishedSizeAreaCalculation(oldValue, newValue, event) {
    
    if (oldValue != newValue) {
        var sForm = event.getFormName();
        /** @type {JSRecord<db:/avanti/sa_task_worktype_section>}*/
        var rWorkTypeSection = forms[sForm].foundset.getSelectedRecord();

        rWorkTypeSection.worktypesection_fin_area_over = newValue;
        
        rWorkTypeSection = scopes.avSection.calculateFinishedSizeArea_workTemplate(rWorkTypeSection);

        databaseManager.saveData(rWorkTypeSection);

        forms[sForm].refreshUI();
    }

    return true;
}

/**
 * <AUTHOR> Dotzlaw
 * @since Aug 19, 2019
 * @param {JSEvent} event the event that triggered the action
 * @param {String} [sForm] - optional form name to use
 * @public
 *
 * @properties={typeid:24,uuid:"2923FD96-54E3-4BA3-AB8D-7B8CB11346D8"}
 */
function showHideFinishedSizeArea(event, sForm) {
    
    if (!event && !sForm) return;
    
    sForm = (sForm) ? sForm : event.getFormName();
    
    if (sForm) {
        
        forms[sForm].elements.fldFinishedSizeAreaCalc.visible = false;
        forms[sForm].elements.fldFinishedSizeAreaCalc.enabled = false;
        scopes.globals.avUtilities_addBkgndColorStyle(forms[sForm].elements.fldFinishedSizeAreaCalc, scopes.avUtils.BACKGROUND_COLORS.DrkGray);
        if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.FinishedSizeAreaCalculationOn)) {
            
            forms[sForm].elements.fldFinishedSizeAreaCalc.visible = true;
            
            if (scopes.globals.avSecurity_checkForUserRight('Order Entry', 'edit_finished_size_area_calculation', globals.avBase_employeeUserID) 
                    || scopes.globals.avSecurity_checkForUserRight('Estimate Entry', 'edit_finished_size_area_calculation', globals.avBase_employeeUserID)) {
                
                forms[sForm].elements.fldFinishedSizeAreaCalc.enabled = true;
                scopes.globals.avUtilities_addBkgndColorStyle(forms[sForm].elements.fldFinishedSizeAreaCalc, scopes.avUtils.BACKGROUND_COLORS.Yellow);
            }
            
        }
        /** @type {JSRecord<db:/avanti/sa_task_worktype_section>}*/
        var rWorkTypeSection = forms[sForm].foundset.getSelectedRecord();

        scopes.avSection.calculateFinishedSizeArea_workTemplate(rWorkTypeSection); 
    }
    return;
}

/**
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"C9D5DE6F-E64C-4594-94AB-A96E518EC8FC"}
 */
function onDataChange_sizesForFinishedSizeAreaCalculation(oldValue, newValue, event) {

    showHideFinishedSizeArea(event,controller.getName());
    return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"616D6ED9-645D-4B9C-80D3-AEB1B4C8051D"}
 */
function onDataChange_digitalInkType(oldValue, newValue, event) {
    
    var sForm = event.getFormName(),
        /** @type {JSRecord<db:/avanti/sa_task_standard>} */ 
        rTaskStd = forms[sForm].foundset.getSelectedRecord();
        
    rTaskStd.inktype_id = newValue;
    scopes.avPress.checkDigitalInkType(rTaskStd); 
    
    return true;
}
