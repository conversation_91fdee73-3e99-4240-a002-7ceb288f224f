customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sys_sender",
extendsID:"D4AA970D-B61A-4C52-BB9E-7F7B07E8CABC",
items:[
{
cssPosition:"167,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"167",
width:"140"
},
enabled:true,
labelFor:"attachment_prefix",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_attachmentPrefix",
visible:true
},
name:"component_D2FFCAA2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"062D5349-40C0-407C-BAA8-7D4C9CBE211A"
},
{
cssPosition:"32,-1,-1,906,341,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"906",
right:"-1",
top:"32",
width:"341"
},
dataProviderID:"no_api_response",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.noApiResponseText",
toolTipText:"i18n:avanti.lbl.noApiResponseText",
visible:true
},
name:"no_api_response",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"0AF78EAC-02CE-4D71-9417-D529481EFAC1"
},
{
cssPosition:"59,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"59",
width:"385"
},
dataProviderID:"create_contact_as_one_time",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.createContactAsOneTime",
visible:true
},
name:"create_one_time_contact",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"0C3B2DE6-1BC7-45BF-BD3A-93815A65EBA2"
},
{
cssPosition:"32,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"32",
width:"385"
},
dataProviderID:"create_address_as_one_time",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.createAddressAsOneTime",
visible:true
},
name:"create_one_time_address",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"119E5CE1-2FED-40F5-AE6E-D5BE3A546C8B"
},
{
cssPosition:"140,-1,-1,150,220,24",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"24",
left:"150",
right:"-1",
top:"140",
width:"220"
},
dataProviderID:"default_line_item_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"9117880E-E58E-4B68-BD54-185E2111E1FD",
visible:true
},
name:"generic_line_item",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"14D4C5C4-1A48-43B3-A719-E3A03C419D58"
},
{
cssPosition:"140,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"140",
width:"385"
},
dataProviderID:"print_comment_on_pick",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_pickingslip",
visible:true
},
name:"print_comment_on_pick",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"20204275-CDBA-47DE-8E85-02DE1361DA81"
},
{
cssPosition:"86,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"86",
width:"385"
},
dataProviderID:"print_comment_on_ack",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_orderack",
visible:true
},
name:"print_comment_on_ack",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"226CE91A-C582-4EAF-859B-8ED464674D20"
},
{
cssPosition:"356,10,0,5,1235,466",
json:{
cssPosition:{
bottom:"0",
height:"466",
left:"5",
right:"10",
top:"356",
width:"1235"
},
styleClass:"scrollable-tabpanel",
tabs:[
{
containedForm:"6C9F7E62-AD75-47DD-A67C-B44278164081",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"sys_sender_to_sys_sender",
svyUUID:"9C7EFEE0-F46C-4CB4-93CF-966D94E79725",
text:"i18n:avanti.lbl.sender_xmlSchemaMapping"
},
{
containedForm:"2BBC84E3-E4A6-42AF-B77B-69523FF284E2",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"sys_sender_to_sys_sender_mapping",
svyUUID:"A043C48F-CFC3-4945-B2E3-5850B8406E86",
text:"i18n:avanti.lbl.sender_xmlValueMapping"
},
{
containedForm:"D361EE62-3C4C-4B69-AE63-0CF43929331E",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"sys_sender_to_sys_sender_task_mapping",
svyUUID:"521B7C1B-8994-4F43-AD04-50BC502B3812",
text:"i18n:avanti.lbl.sender_taskMapping"
},
{
containedForm:"362FC270-6FF9-486D-9D1C-FC7DF46FB44D",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"sys_sender_to_sys_sender",
svyUUID:"756553DD-38BD-48DB-B27E-35CE973AA5DC",
text:"i18n:avanti.lbl.jdfType_IntegrationOptions"
}
]
},
name:"tabs_135",
styleClass:"scrollable-tabpanel",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"2348A504-A73D-45AC-8ABC-EAFBA6E245F9"
},
{
cssPosition:"194,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"194",
width:"220"
},
dataProviderID:"attachment_username",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"attachment_username",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"23AC952C-7A58-4B0A-BF16-E1CD39E3BAC3"
},
{
cssPosition:"248,-1,-1,215,299,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"215",
right:"-1",
top:"248",
width:"299"
},
dataProviderID:"attachment_ftp_hostkey",
editable:true,
enabled:true,
formIndex:2,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftpHostPublicKey",
visible:true
},
name:"attachment_ftp_host_key",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3237D97C-064A-47C1-8076-B34900030A60"
},
{
cssPosition:"113,-1,-1,150,220,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"150",
right:"-1",
top:"113",
width:"220"
},
dataProviderID:"default_customer_id",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"5837523A-C96F-4A28-9F22-2F49215E4106",
visible:true
},
name:"default_customer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3516DBB2-1EB4-47E8-8C07-5250D2FA6FAA"
},
{
cssPosition:"221,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"221",
width:"385"
},
dataProviderID:"print_comment_on_po",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_po",
visible:true
},
name:"print_comment_on_po",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"37445DFD-F427-415E-AE66-6A7C9D824E5E"
},
{
cssPosition:"194,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"194",
width:"140"
},
enabled:true,
labelFor:"attachment_username",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_attachmentUsername",
visible:true
},
name:"component_1ABE8344",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"38CDE3A1-144D-4DA1-B8BB-7C7E3556B703"
},
{
cssPosition:"329,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"329",
width:"140"
},
enabled:true,
labelFor:"senderUniqueID",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.uniqueID",
visible:true
},
name:"component_9A6FC1B0",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"39F31DD5-B2BE-464B-9D4A-9AEA27DFC029"
},
{
cssPosition:"32,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"32",
width:"220"
},
dataProviderID:"sender_id",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"senderSelection",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"422F948B-3E01-4B2C-A5DB-033348E06FD6"
},
{
cssPosition:"86,-1,-1,373,132,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"373",
right:"-1",
top:"86",
width:"132"
},
dataProviderID:"use_time_zone",
styleClass:"checkbox_bts",
text:"i18n:avanti.lbl.UseTimeZone",
toolTipText:"i18n:avanti.lbl.UseTimeZoneTT"
},
name:"use_time_zone",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"440DF24D-C8DB-4B93-B01B-5A23515AA4BD"
},
{
cssPosition:"194,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"194",
width:"385"
},
dataProviderID:"print_comment_on_invoice",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_invoice",
visible:true
},
name:"print_comment_on_inv",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"48AA70C5-CBC5-4214-B7B8-C9AC31F58556"
},
{
cssPosition:"59,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"59",
width:"140"
},
enabled:true,
labelFor:"document_stream",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_documentStream",
visible:true
},
name:"component_1638172E",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"56D6A68C-D518-4103-8F93-F7A7B108C38D"
},
{
cssPosition:"275,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"275",
width:"140"
},
enabled:true,
labelFor:"attachment_category",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_attachmentCategory",
visible:true
},
name:"attachment_category_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"573587B2-1E65-4D28-8DC9-91DEF57B2335"
},
{
cssPosition:"140,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"140",
width:"140"
},
enabled:true,
labelFor:"generic_line_item",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_genericLineItem",
visible:true
},
name:"component_AB850CB4",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"658D99F7-244B-4BC0-97F0-C7917D590644"
},
{
cssPosition:"275,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"275",
width:"385"
},
dataProviderID:"spawn_sections",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.spawnSections",
toolTipText:"i18n:avanti.tooltip.spawnSections",
visible:true
},
name:"spawn_sections",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"6E48E69B-54C0-4384-91D5-ACB8290EB2E5"
},
{
cssPosition:"302,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"302",
width:"385"
},
dataProviderID:"return_response_early",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.returnResponseEarly",
toolTipText:"i18n:avanti.tooltip.returnResponseEarly",
visible:true
},
name:"return_response_early",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"7FA3A0D7-BDBE-47A6-8678-3E29F8F0ECAA"
},
{
cssPosition:"113,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"113",
width:"385"
},
dataProviderID:"print_comment_on_job",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_jobticket",
visible:true
},
name:"print_comment_on_job",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"81F37D9A-15B2-4E80-94E9-CABA6CEA1E10"
},
{
cssPosition:"275,-1,-1,150,220,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"275",
width:"220"
},
dataProviderID:"docs_mgmt_category_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"78255307-ACE1-4D3B-9D57-82115649650B",
visible:true
},
name:"attachment_category",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8C402AA8-B0BF-4498-8975-77728D4E3DDB"
},
{
cssPosition:"86,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"86",
width:"220"
},
dataProviderID:"date_format",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.sender_dateFormatToolTip",
visible:true
},
name:"date_format",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"90B9002C-7EA6-461B-901D-BAEC6B29B475"
},
{
cssPosition:"248,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"248",
width:"385"
},
dataProviderID:"set_doc_for_job",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.docflag_job",
visible:true
},
name:"set_doc_for_job",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"91C9F476-C2B5-4E12-8CD1-83AF9EAD3161"
},
{
cssPosition:"5,5,-1,0,1245,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"5",
top:"5",
width:"1245"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_detailView",
visible:true
},
name:"lbl_group_item_class",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"93B607BD-**************-13BE60FB9B0C"
},
{
cssPosition:"113,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"113",
width:"140"
},
enabled:true,
labelFor:"default_customer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_defaultCustomer",
visible:true
},
name:"component_15F44457",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9A8FDA7C-430F-4175-8B2E-44E606900AEE"
},
{
cssPosition:"167,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"167",
width:"385"
},
dataProviderID:"print_comment_on_pack",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.tooltip.commentflag_packingslip",
visible:true
},
name:"print_comment_on_pack",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"9E887187-2328-41A4-8816-2BD0235E050D"
},
{
height:822,
partType:5,
typeid:19,
uuid:"A90351BB-1EF1-470E-846E-1587E3ED57E9"
},
{
cssPosition:"59,-1,-1,150,220,24",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"24",
left:"150",
right:"-1",
top:"59",
width:"220"
},
dataProviderID:"docnum_stream",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"977D392F-CA84-47DA-A3E6-15EFA92B60B6",
visible:true
},
name:"document_stream",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"AE310E18-3696-4CC5-BA11-393CB3D61338"
},
{
cssPosition:"248,-1,-1,150,60,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"248",
width:"60"
},
dataProviderID:"attachment_port",
editable:true,
enabled:true,
formIndex:1,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftp_port",
visible:true
},
name:"attachment_port",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B527D79C-81AD-4BEB-B81D-43986D426A12"
},
{
cssPosition:"167,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"167",
width:"220"
},
dataProviderID:"attachment_prefix",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"attachment_prefix",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C3A4BBF6-BC56-4807-85CC-0B4F666083E1"
},
{
cssPosition:"329,-1,-1,150,365,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"329",
width:"365"
},
dataProviderID:"unique_id_path",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2FC7F2D4-3152-44DF-AE3E-26EDC59A3C31",
visible:true
},
name:"senderUniqueID",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C64979E1-829E-4389-A943-6CAA0050F924"
},
{
cssPosition:"329,-1,-1,520,385,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"329",
width:"385"
},
dataProviderID:"material_task_as_item_code",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.useItemCodeAsSectionDescription",
toolTipText:"i18n:avanti.lbl.useItemCodeAsSectionDescription",
visible:true
},
name:"use_item_code_as_section_desc",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"C9D3BC97-931D-4B65-B989-9FC96344075C"
},
{
cssPosition:"221,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"221",
width:"220"
},
dataProviderID:"attachment_password",
editable:true,
enabled:true,
inputType:"password",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"attachment_password",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DE4B2C98-9BE6-4AB5-A0DA-EB9DA80B562E"
},
{
cssPosition:"248,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"248",
width:"140"
},
dataProviderID:"attachment_use_sftp",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.ftpUseSFTP",
visible:true
},
name:"attachment_use_sftp",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"DF6B07DD-19DA-4654-BFBF-4DDE2656DBF7"
},
{
cssPosition:"221,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"221",
width:"140"
},
enabled:true,
labelFor:"attachment_password",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_attachmentPassword",
visible:true
},
name:"component_CFBAA207",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E9222BA4-D6E5-42C4-BB8E-1E30DA6DBC29"
},
{
cssPosition:"86,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"86",
width:"140"
},
enabled:true,
labelFor:"date_format",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_dateFormat",
visible:true
},
name:"component_6A792AB0",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EAD9CAA6-3E6D-4B87-8298-72C06862A518"
},
{
cssPosition:"32,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"32",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_id",
visible:true
},
name:"component_987D3780",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EB35A19F-13EA-45D0-82B7-9DBF3581849B"
},
{
cssPosition:"302,-1,-1,150,365,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"302",
width:"365"
},
dataProviderID:"attachment_link_file",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.sender_attachmentLinkAttachments",
visible:true
},
name:"attachment_link_file",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"FE1AF94B-A704-43E2-AB6B-FAC90D4EDB50"
}
],
name:"sys_sender_dtl",
onNewRecordCmdMethodID:"-1",
onNextRecordCmdMethodID:"-1",
onPreviousRecordCmdMethodID:"-1",
scrollbars:33,
size:"1250,822",
typeid:3,
uuid:"6B525FEB-0BF6-4BC2-BF34-1E5BD48418F3"