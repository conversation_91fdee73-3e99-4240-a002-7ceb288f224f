customProperties:"formComponent:false,\
methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sch_milestone_group",
encapsulation:108,
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
cssPosition:"3,-1,-1,170,173,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"170",
right:"-1",
top:"3",
width:"173"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_C598BC82",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0A64E33C-7B0B-477D-BA33-827032AE09C3"
},
{
cssPosition:"101,-1,9,563,80,22",
json:{
cssPosition:{
bottom:"9",
height:"22",
left:"563",
right:"-1",
top:"101",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"A105A2BF-155E-40AA-BF6B-82485D5B7D58",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_77401B9F",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"3C4520BF-3CA9-499B-97AE-A2251458EFDD"
},
{
cssPosition:"3,238,-1,352,161,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"352",
right:"238",
top:"3",
width:"161"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_desc",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"cost_centre",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4FBE1A8D-4DC8-4767-B1E8-056354047A61"
},
{
cssPosition:"101,-1,8,648,80,22",
json:{
cssPosition:{
bottom:"8",
height:"22",
left:"648",
right:"-1",
top:"101",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"89F75E30-F482-4CA9-B43F-532C2A4E5B57",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_B3929A10",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"6D140649-DC30-4494-AE8A-586BB2C314AC"
},
{
cssPosition:"28,227,-1,529,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"529",
right:"227",
top:"28",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_2",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"6EB47E5E-0CBF-4183-A469-93B2F7BF3C6E"
},
{
height:135,
partType:8,
typeid:19,
uuid:"83B8FE9D-BE58-48EF-B5DC-3F6072DA716A"
},
{
cssPosition:"28,27,-1,556,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"556",
right:"27",
top:"28",
width:"200"
},
dataProviderID:"qa_check_pos_2",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:6,
visible:true
},
name:"qa_check_field_2",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"973A4FF6-A3D4-44F5-AB13-1523C688E213"
},
{
height:80,
partType:5,
typeid:19,
uuid:"B5AC99A4-F8C3-4969-B6D9-908363D4CEF6"
},
{
cssPosition:"3,27,-1,556,199,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"556",
right:"27",
top:"3",
width:"199"
},
dataProviderID:"qa_check_pos_1",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:5,
visible:true
},
name:"qa_check_field_1",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BB76F842-**************-CCB23CDA7415"
},
{
cssPosition:"3,227,-1,529,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"529",
right:"227",
top:"3",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_1",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"DB762DFC-6508-4118-8A45-190904688C41"
},
{
cssPosition:"53,227,-1,529,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"529",
right:"227",
top:"53",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
enabled:true,
styleClass:"text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_3",
styleClass:"text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"DF582E46-9B40-444D-8860-5D2B3D784BF4"
},
{
cssPosition:"3,-1,-1,0,163,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"-1",
top:"3",
width:"163"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_EC28F0BE",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E7CEEB1F-E633-4711-818C-22594FEAC4E4"
},
{
cssPosition:"53,27,-1,556,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"556",
right:"27",
top:"53",
width:"200"
},
dataProviderID:"qa_check_pos_3",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:7,
visible:true
},
name:"qa_check_field_3",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"FB22C383-3DAF-4636-97EF-AEFF00C86907"
}
],
name:"sf_main_dtl_labour_qa_checks_by_job",
navigatorID:"-1",
onShowMethodID:"EA567FCC-C472-4E39-8EDC-01F85CCDB488",
scrollbars:36,
showInMenu:true,
size:"760,480",
typeid:3,
uuid:"8735CA54-C385-4AE0-AD2A-2E92C2E9682F"