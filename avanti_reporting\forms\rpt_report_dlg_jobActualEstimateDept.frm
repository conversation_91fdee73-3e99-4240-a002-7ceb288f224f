borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"15,-1,-1,420,140,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"15",
width:"140"
},
enabled:true,
formIndex:30,
labelFor:"fromPlant",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0B9A41CD-9598-4BCE-94DC-FDACC0CC789D"
},
{
cssPosition:"166,-1,-1,420,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"166",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobStartDate",
visible:true
},
name:"_toJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"20CC9A77-A21A-4130-AD43-25562DED7686"
},
{
cssPosition:"136,-1,-1,420,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"136",
width:"140"
},
enabled:true,
labelFor:"_toJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobNumber",
visible:true
},
name:"_toJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"21DDC682-C28D-4303-840E-1B8146C5F2BD"
},
{
cssPosition:"196,-1,-1,150,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"196",
width:"250"
},
dataProviderID:"_fromDept",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"59D5B30F-3C25-42F7-BA75-816A2F26AA9A",
visible:true
},
name:"_fromDept",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"25C1FC1A-809D-451E-9B1B-9CE460C21DD2"
},
{
cssPosition:"166,-1,-1,150,160,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"166",
width:"160"
},
dataProviderID:"_dateFrom",
enabled:true,
onDataChangeMethodID:"62B2D45F-FFEA-45C5-9D55-E2A777E2FC24",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"27E3FA0A-330E-44DA-8206-FD990C986488"
},
{
cssPosition:"136,-1,-1,565,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"565",
right:"-1",
top:"136",
width:"250"
},
dataProviderID:"_toJobNr",
enabled:true,
onDataChangeMethodID:"156BC114-225D-42B3-8AAB-553167B46D74",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_toJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2C1EA27B-6A2D-4017-B7C8-6F4EE7A33B08"
},
{
cssPosition:"166,-1,-1,565,160,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"565",
right:"-1",
top:"166",
width:"160"
},
dataProviderID:"_dateTo",
enabled:true,
onDataChangeMethodID:"4DCD0418-EE3D-4DA3-9CB6-E7B1DED64E17",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"2DCEFC0E-92B1-4F03-AE27-AEB8C22720EE"
},
{
cssPosition:"15,-1,-1,5,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"15",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2F47F2C2-C74D-4DF0-BC73-A7486E32FA79"
},
{
cssPosition:"196,-1,-1,565,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"565",
right:"-1",
top:"196",
width:"250"
},
dataProviderID:"_toDept",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"59D5B30F-3C25-42F7-BA75-816A2F26AA9A",
visible:true
},
name:"_toDept",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"530EEF2C-3E93-495C-9EC7-DC56A0801D4E"
},
{
cssPosition:"226,-1,-1,565,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"565",
right:"-1",
top:"226",
width:"250"
},
dataProviderID:"_toEquip",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"6D4734AD-AE6F-47EC-8615-AB8504CA1179",
visible:true
},
name:"_toEquip",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7316958E-6C71-454C-B25F-5D601D06874D"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:320,
typeid:19,
uuid:"742AE99A-B5CB-4D65-A42D-8B95DB982D1C"
},
{
cssPosition:"15,-1,-1,565,250,112",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"565",
right:"-1",
top:"15",
width:"250"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"7D84DB43-205E-45C3-94BC-896069E343B0"
},
{
cssPosition:"256,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"256",
width:"140"
},
enabled:true,
labelFor:"_fromEquip",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.completedJobs",
visible:true
},
name:"_completedJobs_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7D9DF772-EE4C-4744-8FB9-633706DC49DD"
},
{
cssPosition:"226,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"226",
width:"140"
},
enabled:true,
labelFor:"_fromEquip",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromMachine",
visible:true
},
name:"_fromEquip_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8D214C4F-DB2F-41F8-B41D-B22B63DD5837"
},
{
height:200,
partType:5,
typeid:19,
uuid:"8E50F1C5-434A-4754-9DFF-2924A6219775"
},
{
cssPosition:"136,-1,-1,150,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"136",
width:"250"
},
dataProviderID:"_fromJobNr",
enabled:true,
onDataChangeMethodID:"7266C489-C1D8-4BA9-AD4A-69A032E6689A",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_fromJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8E74417D-2427-4353-BE46-CF9554C1C4DE"
},
{
cssPosition:"226,-1,-1,420,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"226",
width:"140"
},
enabled:true,
labelFor:"_toEquip",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toMachine",
visible:true
},
name:"_toEquip_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"957B5C26-E95C-40DF-9769-22665F6B69D5"
},
{
cssPosition:"226,-1,-1,150,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"226",
width:"250"
},
dataProviderID:"_fromEquip",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"6D4734AD-AE6F-47EC-8615-AB8504CA1179",
visible:true
},
name:"_fromEquip",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"9B613C15-4CA4-46D4-B3DA-5B0D61BD4BB3"
},
{
cssPosition:"166,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"166",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobStartDate",
visible:true
},
name:"_fromJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BA2C784E-01BF-4AF1-A359-848A405A3883"
},
{
cssPosition:"256,-1,-1,150,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"256",
width:"250"
},
dataProviderID:"_completedJobs",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"5ADF4034-6D4D-4FCB-BE9F-3B9F579CA140",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"_completedJobs",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"C2FC9949-A5DC-47D0-9E47-6F69FF3D28F1"
},
{
cssPosition:"196,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"196",
width:"140"
},
enabled:true,
labelFor:"_fromDept",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromDept",
visible:true
},
name:"_fromDept_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D3B4BE67-46E0-45A7-B02C-EEDB63B23BC7"
},
{
cssPosition:"136,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"136",
width:"140"
},
enabled:true,
labelFor:"_fromJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E156C2F4-4C81-470C-8F0E-9105498431E8"
},
{
cssPosition:"15,-1,-1,150,250,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"150",
right:"-1",
top:"15",
width:"250"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"CE1A129F-EA6C-4673-930D-D7108B2E0CE5",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E8D94BE6-76A0-4D0C-819E-450F7D9E910A"
},
{
cssPosition:"196,-1,-1,420,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"196",
width:"140"
},
enabled:true,
labelFor:"_toDept",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toDept",
visible:true
},
name:"_toDept_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ED1F2E23-0499-476D-B01E-7E037AE6E84C"
}
],
name:"rpt_report_dlg_jobActualEstimateDept",
navigatorID:"-2",
onShowMethodID:"64F97BB4-458D-49E3-B56F-E00F7B1B1460",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"C3BC6623-56AC-4908-A7AC-DFD15213E1C5"