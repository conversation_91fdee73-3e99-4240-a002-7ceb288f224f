borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"41,-1,-1,13,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"13",
right:"-1",
top:"41",
width:"140"
},
enabled:true,
formIndex:2,
labelFor:"_basedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoicesBasedOn",
visible:true
},
name:"_basedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3103D184-9840-4164-A1E3-49B09D150BA4"
},
{
cssPosition:"68,-1,-1,158,140,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"68",
width:"140"
},
dataProviderID:"_sPaymentsBasedOn",
enabled:true,
formIndex:1,
onDataChangeMethodID:"F05E8100-9B3B-473D-9F32-BE476BF8E44F",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EAB5FF47-F115-4943-A3F4-95ABD141AF3A",
visible:true
},
name:"_sPaymentsBasedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"597B46A7-F63F-43B0-A25D-A27FC0B949A8"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:281,
typeid:19,
uuid:"84D30F3F-3EC6-49D7-AFD6-03C47A819707"
},
{
cssPosition:"14,-1,-1,158,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"14",
width:"140"
},
dataProviderID:"asAtDate",
enabled:true,
formIndex:2,
format:"yyyy-MM-dd",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"asDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"A53A71A9-7242-44BD-8BC1-7090CDD610C4"
},
{
cssPosition:"41,-1,-1,158,140,22",
formIndex:3,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"41",
width:"140"
},
dataProviderID:"_basedOn",
enabled:true,
formIndex:3,
onDataChangeMethodID:"4D22D189-37DF-4A4E-AB0B-9728E7C91901",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2AEB8639-FBAA-4F6B-B650-07B708369F0B",
visible:true
},
name:"_basedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A9CA3442-FBAD-4678-A221-C26CB0F8A0FF"
},
{
cssPosition:"7,-1,-1,5,690,114",
json:{
cssPosition:{
bottom:"-1",
height:"114",
left:"5",
right:"-1",
top:"7",
width:"690"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_16435CA7",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C41405C9-D21F-4B9E-BBF2-393699BA753F"
},
{
cssPosition:"14,-1,-1,13,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"13",
right:"-1",
top:"14",
width:"140"
},
enabled:true,
formIndex:1,
labelFor:"asDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dateAsOf",
visible:true
},
name:"component_D13BAA57",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DD9D6357-5811-4353-B2DC-234C46632013"
},
{
height:200,
partType:5,
typeid:19,
uuid:"F3FE4CE4-89CC-4EFA-9EFD-371E289F20BE"
},
{
cssPosition:"68,-1,-1,15,138,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"68",
width:"138"
},
enabled:true,
formIndex:3,
labelFor:"_sPaymentsBasedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentsBasedOn",
visible:true
},
name:"_sPaymentsBasedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FDC82917-A90F-4FF2-8424-F418D6B0757F"
}
],
name:"rpt_report_dlg_ar_aging",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"69F9CD0E-3A7E-46C3-95E7-A2E706F18E35"