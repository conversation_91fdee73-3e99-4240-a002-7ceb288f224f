customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sys_sender",
extendsID:"8B1F9F73-2E11-4617-8C26-D80FB568651A",
items:[
{
cssPosition:"223,234,-1,-1,26,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"234",
top:"223",
width:"26"
},
formIndex:2,
onActionMethodID:"D25BE79B-8781-4617-B96F-70422E71B1AF",
styleClass:"material-symbols-outlined forms_add_on",
toolTipText:"i18n:avanti.lbl.sender_addSchemaNode_toolTip"
},
name:"label_8cccccc",
styleClass:"material-symbols-outlined forms_add_on",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"051F1B9B-14A6-437F-874C-4E0CB0FBFA6F"
},
{
cssPosition:"223,-1,-1,224,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"224",
right:"-1",
top:"223",
width:"26"
},
onActionMethodID:"A7A8BF75-34B4-4885-A7A6-193DBAE116A4",
styleClass:"material-symbols-outlined forms_add_on",
toolTipText:"i18n:avanti.lbl.sender_addXMLNode"
},
name:"label_8ccc",
styleClass:"material-symbols-outlined forms_add_on",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0D89BEFF-2065-4543-ABA1-A1516B5F1B15"
},
{
cssPosition:"301,229,-1,-1,18,18",
json:{
cssPosition:{
bottom:"-1",
height:"18",
left:"-1",
right:"229",
top:"301",
width:"18"
},
enabled:true,
formIndex:0,
onActionMethodID:null,
styleClass:"label_bts text-center",
tabSeq:0,
text:"2",
visible:true
},
name:"component_E8378C2E",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"565E47FB-8AB6-4293-AC73-1716AEE7CB6C"
},
{
cssPosition:"32,-1,-1,809,155,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"809",
right:"-1",
top:"32",
width:"155"
},
dataProviderID:"enable_related_tasks",
enabled:true,
formIndex:14,
onDataChangeMethodID:"85A68E08-6524-4039-B354-121A52FDAFA3",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.xml_RelatedTask",
toolTipText:"i18n:avanti.lbl.xml_RelatedTask_tooltip",
visible:true
},
name:"related_task",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"5B431A60-74ED-48A7-B417-CD1CA078449D"
},
{
height:524,
partType:5,
typeid:19,
uuid:"5D7FFB67-52CF-4A28-92B0-B1EA6B2544B5"
},
{
cssPosition:"32,9,-1,-1,224,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"9",
top:"32",
width:"224"
},
enabled:true,
onActionMethodID:"B88616AD-87E9-4535-B159-AB5E1359855C",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.xml_reloadXMLSchema",
toolTipText:"i18n:avanti.lbl.xml_reloadXMLSchemaSource",
visible:true
},
name:"component_C5856C4C",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"688DBC23-3DB2-4193-8DB8-2D555667EA91"
},
{
cssPosition:"32,-1,-1,252,214,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"252",
right:"-1",
top:"32",
width:"214"
},
dataProviderID:"attribute_value_is_node",
enabled:true,
formIndex:14,
onDataChangeMethodID:"AA956555-B0B2-4074-A3D2-FA7C4FD93BAE",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.sender_attributeValueIsNode",
visible:true
},
name:"component_7490BB3E",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"70B6698E-8371-4C20-B385-C6D9C65DA3EE"
},
{
cssPosition:"5,-1,-1,0,121,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"-1",
top:"5",
width:"121"
},
enabled:true,
formIndex:12,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.currentFile",
visible:true
},
name:"component_968A8FDC",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7CE74FEA-41AB-4B0F-875E-492FE757C2FB"
},
{
cssPosition:"247,-1,-1,225,26,22",
formIndex:25,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"225",
right:"-1",
top:"247",
width:"26"
},
enabled:true,
formIndex:25,
onActionMethodID:"378B05AD-DF7D-49ED-88E3-DED875491F4F",
styleClass:"material-symbols-outlined delete",
tabSeq:0,
visible:true
},
name:"component_ECDC63B8",
styleClass:"material-symbols-outlined delete",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"84C2DCD4-9F74-48B3-8CFD-397B8D190068"
},
{
cssPosition:"227,229,-1,-1,18,18",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"18",
left:"-1",
right:"229",
top:"227",
width:"18"
},
enabled:true,
formIndex:1,
onActionMethodID:null,
styleClass:"label_bts text-center",
tabSeq:0,
text:"1",
visible:true
},
name:"component_E8378C2Ec",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9C7D68FA-48F4-4E89-9551-39687F1CC8C5"
},
{
cssPosition:"5,-1,-1,131,231,22",
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"131",
right:"-1",
top:"5",
width:"231"
},
enabled:true,
formIndex:13,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_269B8796",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A9E77589-C9D9-4696-AA55-8A5897F20DD4"
},
{
cssPosition:"32,-1,-1,0,224,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"-1",
top:"32",
width:"224"
},
enabled:true,
onActionMethodID:"9030999A-0A4E-4E99-955C-1E9C3BC2BFE6",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.newFile",
visible:true
},
name:"component_EE753010",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"AC570F8C-CF53-4F43-942F-C5A9B24F7107"
},
{
cssPosition:"32,-1,-1,649,155,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"649",
right:"-1",
top:"32",
width:"155"
},
dataProviderID:"evaluate_element_value",
enabled:true,
formIndex:14,
onDataChangeMethodID:"B1FA0F75-F857-4708-A26F-6C97EB71E890",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.sender_evaluateXMLElement",
toolTipText:"i18n:avanti.lbl.sender_elementValue_toolTip",
visible:true
},
name:"evaluate_element_value",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"AED159CE-B37C-4D43-AC6F-457B1629B492"
},
{
cssPosition:"322,234,-1,-1,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"234",
top:"322",
width:"26"
},
onActionMethodID:"A8917E60-5A60-4158-A7DF-C351FB75A994",
styleClass:"material-symbols-outlined delete"
},
name:"label_8cc",
styleClass:"material-symbols-outlined delete",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BA45B470-CAD5-4C2F-9A03-D0574FF36EAE"
},
{
cssPosition:"32,-1,-1,426,131,22",
formIndex:15,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"426",
right:"-1",
top:"32",
width:"131"
},
enabled:true,
formIndex:15,
labelFor:"attribute_value_array_base",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.sender_attributeArrayBase",
toolTipText:"i18n:avanti.lbl.sender_attributeArrayBase_toolTip",
visible:true
},
name:"attribute_value_array_base_label",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BF0EC864-E55D-4757-A6CC-E4A1902E2FBE"
},
{
cssPosition:"59,9,7,-1,224,458",
formIndex:5,
json:{
containedForm:"89E665E3-0DE8-4ABA-97E5-6DFAD487C84B",
cssPosition:{
bottom:"7",
height:"458",
left:"-1",
right:"9",
top:"59",
width:"224"
},
formIndex:5,
visible:true
},
name:"db_tabless",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"C03D7456-D193-4E0F-9502-5A9F92BE3A00"
},
{
cssPosition:"59,-1,7,0,224,458",
formIndex:3,
json:{
containedForm:"C26F1F1B-25BA-4267-BF81-48314F5A9AF2",
cssPosition:{
bottom:"7",
height:"458",
left:"0",
right:"-1",
top:"59",
width:"224"
},
formIndex:3,
relationName:"sys_sender_to_xml_template",
visible:true
},
name:"xml_tree_tabless",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"C3F46E2E-2AA1-4D56-89B6-1B1E22A9C68C"
},
{
cssPosition:"248,234,-1,-1,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"234",
top:"248",
width:"26"
},
onActionMethodID:"A37BBC36-6436-4AC9-8D28-85128A26AE00",
styleClass:"material-symbols-outlined delete"
},
name:"label_8c",
styleClass:"material-symbols-outlined delete",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C984C5ED-AB36-4F64-85E0-6FC5ABFBF9F7"
},
{
cssPosition:"32,-1,-1,562,31,22",
formIndex:16,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"562",
right:"-1",
top:"32",
width:"31"
},
dataProviderID:"attribute_value_array_base",
editable:true,
enabled:true,
formIndex:16,
format:"|#(1)",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.sender_attributeArrayBase_toolTip",
visible:true
},
name:"attribute_value_array_base",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CC1ED394-870F-4CA5-AC13-F1C93D654941"
},
{
cssPosition:"59,261,30,252,712,435",
formIndex:4,
json:{
containedForm:"B9500452-D218-460D-82B5-FDCB6539A4D5",
cssPosition:{
bottom:"30",
height:"435",
left:"252",
right:"261",
top:"59",
width:"712"
},
formIndex:4,
relationName:"sys_sender_to_xml_selected",
visible:true
},
name:"xml_db_selected_tabless",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"DDC1F32A-1DC6-402A-B7EE-E6AB0772DE4D"
},
{
cssPosition:"297,234,-1,-1,26,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"234",
top:"297",
width:"26"
},
formIndex:3,
onActionMethodID:"12107D4E-4BC9-4C8A-902A-84A0C85A619A",
styleClass:"material-symbols-outlined forms_add_on",
toolTipText:"i18n:avanti.lbl.sender_addSchemaNode2_toolTip"
},
name:"label_8ccccc",
styleClass:"material-symbols-outlined forms_add_on",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FBFEA258-2E3A-4C0E-97AB-70C4B93F9783"
}
],
name:"xml_mapper_main_dtl",
onNextRecordCmdMethodID:"-1",
onPreviousRecordCmdMethodID:"-1",
onRenderMethodID:"-1",
onShowMethodID:"3D4E9E85-7EA4-4E0B-A152-E1E4005473CF",
scrollbars:33,
size:"1225,517",
transparent:false,
typeid:3,
uuid:"6C9F7E62-AD75-47DD-A67C-B44278164081"