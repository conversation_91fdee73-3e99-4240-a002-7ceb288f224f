

/**
 * Registers all the plugins for the HC, and loads all the cron jobs into scheduler
 * TO ADD ANY CODE HERE PLEASE BE SURE that
 * globals.org_id, globals.owner_id and globals.org_name are properly set
 * IN HEADLESS CLIENT those variables are undefined
 *
 * <AUTHOR>
 * @since 2012-05-16
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"A219F2A0-2706-4497-A71E-ACCD5E1432EA"}
 */
function onHeadlessClientStart() {
	var time_delay = 95000;
	var startDate = new Date();
	startDate.setTime(startDate.getTime()+time_delay);
	plugins.scheduler.addJob('onHeadlessClientStart',startDate, onHeadlessClientStart_get_started );
}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"130702AC-7A77-48DA-A298-3E1BF6FDF0A1"}
 */
function onHeadlessClientStart_get_started(){
	
	plugins.scheduler.removeJob('onHeadlessClientStart');
	
	// !!!!! TO ADD ANY CODE HERE PLEASE BE SURE that
	// globals.org_id, globals.owner_id and globals.org_name are properly set
	// IN HEADLESS CLIENT those variables are undefined !!!!!!
	
    application.output("Starting HC1 Headless Client", LOGGINGLEVEL.INFO);
    globals.dbLog('Starting Headless Client For HC1', 'hc1', 'startup', null, null, null, 'logging', 'Detail', null, null, null, null);
       
    /*** @type {JSFoundSet<db:/avanti/app_scheduled_jobs>} */
    var fsJobs = databaseManager.getFoundSet("avanti", 'app_scheduled_jobs');
    fsJobs.loadAllRecords();
    var rJob;
    /***@type {Function}*/
    var sMethod;
    var sJob;
    var sTiming;
    var i;
    
    if (fsJobs.find()) {
        fsJobs.is_active = 1;
        fsJobs.processor_name = "_avanti_headless_client";
        fsJobs.search();
        
        if (fsJobs.getSize() > 0) {
            for (i = 1; i <= fsJobs.getSize(); i++) {
                rJob = fsJobs.getRecord(i);
                
                sMethod = rJob.method_name;
                sJob = rJob.job_name;
                sTiming = rJob.cron_timings;
                
                // sl-2544 - there is a system pref to allow them to change time stafa cust upload runs
                if (sJob == 'state farm customer upload') {
                    var sCustomTime = globals.avBase_getSystemPreference_String(93);
                    if (sCustomTime && sCustomTime != '2:00 AM') { // if theyve set something and its not the default
                        sCustomTime = createCronStringFromTime(sCustomTime);
                        if (sCustomTime) {
                            sTiming = sCustomTime;
                        }
                    }
                }
                
                // see: http://www.quartz-scheduler.org/docs/tutorials/crontrigger.html for more info
                try {
                    plugins.scheduler.addCronJob(sJob, sTiming, globals[sMethod]);
                }
                catch (e) {
                    globals.dbLog("ERROR Scheduling Job: " + sJob, "hc1", "running", "", "", null, "logging", "Detail", null, null, null, null); // Detail or Summary
                }
            }
        }
    }

    //add sys_scheduled_jobs to the table
    /*** @type {JSFoundSet<db:/avanti/sys_scheduled_job>} */
    var fsSysScheduledJob = databaseManager.getFoundSet("avanti", 'sys_scheduled_job');
    fsSysScheduledJob.loadAllRecords();

    var dCurDate = application.getServerTimeStamp();
    dCurDate.setHours(0, 0, 0, 0);

    for (i = 1; i <= fsSysScheduledJob.getSize(); i++) {
        /*** @type {JSRecord<db:/avanti/sys_scheduled_job>} */
        var rSysJob = fsSysScheduledJob.getRecord(i);

        if ( ( rSysJob.sj_start_date <= dCurDate ) && ( rSysJob.sj_end_date > dCurDate )) {
            var sJobName = rSysJob.sj_job_name + i;
            var sJobTiming = rSysJob.sj_cron_timings;
            var sJobMethod = rSysJob.sj_method_name;
            var dJobStartDate = null;
            var dJobEndDate = rSysJob.sj_end_date;
            var aJobArgs = [];
            if (rSysJob.sj_method_args != null && rSysJob.sj_method_args != "") {
                aJobArgs = rSysJob.sj_method_args.split(",");
            }

            // see: http://www.quartz-scheduler.org/docs/tutorials/crontrigger.html for more info
            try {
                plugins.scheduler.removeJob(sJobName);
                plugins.scheduler.addCronJob(sJobName, sJobTiming, globals[sJobMethod], dJobStartDate, dJobEndDate, aJobArgs);
            }
            catch (e) {
                globals.dbLog("ERROR Scheduling Job: " + sJobName, "hc1", "running", "", "", null, "logging", "Detail", null, null, null, null); // Detail or Summary
            }
        }
    }
}

/**
 * @param {String} sTime
 * @return {String}
 *
 * @properties={typeid:24,uuid:"44A83513-2352-49ED-B8D9-CE4C753FBC19"}
 */
function createCronStringFromTime(sTime) {
    // '0 30 14 ? * *' is 2:30 pm
    
    if (sTime.indexOf(':') > -1) {
        var aNums = sTime.split(':');
        /***@type {Number} */
        var hours = parseInt(aNums[0]);
        
        if (aNums[1].indexOf(' ') > -1) {
            var aMinAndMeridian = aNums[1].split(' ');
            var mins = aMinAndMeridian[0];
            
            if (hours == 12) {
                if (aMinAndMeridian[1].toLowerCase() == 'am') {
                    hours = 0;
                }
            }
            else if (aMinAndMeridian[1].toLowerCase() == 'pm') {
                hours += 12;
            }
            
            return '0 ' + mins + ' ' + hours.toString() + ' ? * *';
        }
    }
    
    return null;
}

/**
 * Backup data call for 12am
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-05-16
 *
 *
 * @properties={typeid:24,uuid:"8C29D033-C222-4D37-BA44-06A4432EA5B1"}
 */
function _backupData_12()
{
	forms._data_migrator_import.btnCreateBackup(12);
}

/**
 * Backup data call for 12am
 *
 * <AUTHOR> Dotzlaw
 * @since 2012-05-16
 *
 *
 * @properties={typeid:24,uuid:"366CF94B-39E9-497A-AA81-A4D8FEB0C86E"}
 */
function _backupData_24()
{
	forms._data_migrator_import.btnCreateBackup(24); 
}

/**
 * @properties={typeid:24,uuid:"634E1C11-145E-40C3-86CB-585CF1D25265"}
 */
function doAsciiCustImport_headless(){
	/***@type {JSFoundset<db:/avanti/sys_organization>} */
	var fs_sys_organization = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_organization')
	fs_sys_organization.loadAllRecords()
	fs_sys_organization.setSelectedIndex(1)
	if(fs_sys_organization.org_name.toString().indexOf('State Farm') > -1){ // this is state farm - run the job
		// '0 0 2 ? * *' - fire at 2am everyday - unless they overrode default
		forms.utils_ascii_upload_dtl.doAsciiCustImport_headless()
	}
}

/**
 * @properties={typeid:24,uuid:"47C7BD8B-01DC-4FB2-B16D-6031F502FECE"}
 */
function doAutoDataImport() {
	
    /**@type {JSFoundset<db:/avanti/sys_auto_data_import>} */
    var fsAutoDataImport = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_auto_data_import');

    fsAutoDataImport.loadAllRecords();

    if (fsAutoDataImport.getSize() > 0) {
        fsAutoDataImport.sort('org_id');
        
        if (!globals.avDocs_oDocs || !globals.avDocs_oDocs.docTypes) {
            forms["_docs_base"].init_avDocs_oDocs();
        }

        // there is a different auto data import rec for each import type/subtype. process each
        for (var i = 1; i <= fsAutoDataImport.getSize(); i++) {
            var rAutoDataImport = fsAutoDataImport.getRecord(i);

            if (rAutoDataImport.adi_enabled && rAutoDataImport.adi_ftp_host && rAutoDataImport.adi_ftp_user && rAutoDataImport.adi_ftp_pwd && rAutoDataImport.adi_import_type) {
                if (rAutoDataImport.adi_import_subtype || !scopes.avUtils.doesDataImportTypeUseSubType(rAutoDataImport.adi_import_type)) {
                    if (globals.org_id != rAutoDataImport.org_id.toString()) {
                        globals.org_id = rAutoDataImport.org_id.toString();
                        globals.svy_sec_lgn_organization_id = globals.org_id;
                        globals.svy_sec_owner_id = scopes.avDB.SQLQuery("SELECT owner_id FROM sys_organization WHERE org_id = ?", null, [globals.org_id]);
                        globals.svy_nav_filterOrganization();
                    }

                    scopes.avUtils.doDataImportFromFTP(rAutoDataImport.adi_import_type, rAutoDataImport.adi_import_subtype,
                        rAutoDataImport.adi_ftp_host, rAutoDataImport.adi_ftp_user, rAutoDataImport.adi_ftp_pwd, rAutoDataImport.adi_ftp_folder,
                        rAutoDataImport.adi_ftp_port, rAutoDataImport.adi_delete_file == 1, rAutoDataImport.adi_replace_existing_records,
						rAutoDataImport.adi_use_sftp);
                }
            }
        }
    }
}

/**
 * @properties={typeid:24,uuid:"B737D6F1-F92E-42D1-9855-2667935BF69F"}
 */
function deletePastScheduleCapacityRecs() {
    var dYesterday = plugins.DateUtils.addDays(new Date(), -1);
    var sECSQL = " from sch_emp_capacity where schedule_date_num < " + scopes.avDate.getDateAsNum(dYesterday);
    var sSQL1 = "delete from sch_ms_emp_capacity where ec_id in (select ec_id " + sECSQL + ")";
    var sSQL2 = "delete " + sECSQL;

    scopes.avDB.RunSQL(sSQL1);
    scopes.avDB.RunSQL(sSQL2);
}

/**
 * @properties={typeid:24,uuid:"5C8F9875-470C-4142-952F-8F126F74856B"}
 */
function autoCloseFiscalPeriodsHC(){
	
	//Loop over each org id and call the auto close for fiscal periods.
	var oSQL ={};		
	oSQL.server = globals.avBase_dbase_framework;
    oSQL.args = [];
    
    /** @type {JSDataSet} */
	var dsData = null;
	
    oSQL.sql = "SELECT organization_id FROM sec_organization";
    oSQL.args = [];
	dsData = globals["avUtilities_sqlDataset"](oSQL);
    	
	if(dsData != null){
		for(var i = 1; i <= dsData.getMaxRowIndex(); i++){
			var sOrgID = dsData.getValue(i,1);
			if(sOrgID != null && utils.stringTrim(sOrgID) != ""){
				scopes.avAccounting.autoCloseFiscalPeriods(sOrgID);
			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"165CC88A-0CE6-4E3D-9018-B3BB56BD72B8"}
 */
function resetAutoRegisterScheduler(){
	var aAllJobs = plugins.scheduler.getCurrentJobNames();
	var i;
	
	//remove all jobs for the auto_register
	for(i = 0; i < aAllJobs.length; i++){
		var sJobName = aAllJobs[i];
		if(utils.stringPatternCount(sJobName,"auto_register") > 0 && sJobName != "update_auto_register_jobs"){
			plugins.scheduler.removeJob(sJobName);
		}
	}
	
	//re-add any auto_register jobs
	//add sys_scheduled_jobs to the table
	/*** @type {JSFoundSet<db:/avanti/sys_scheduled_job>} */
	var fsSysScheduledJob = databaseManager.getFoundSet("avanti", 'sys_scheduled_job');
	fsSysScheduledJob.loadAllRecords();
	
	var dCurDate = application.getServerTimeStamp();
	dCurDate.setHours(0,0,0,0);
	
	for(i = 1; i <= fsSysScheduledJob.getSize(); i++)
	{
		/*** @type {JSRecord<db:/avanti/sys_scheduled_job>} */
		var rSysJob = fsSysScheduledJob.getRecord(i);
		
		if(rSysJob.sj_job_name == "auto_register"){
			if((rSysJob.sj_start_date <= dCurDate) && (rSysJob.sj_end_date > dCurDate)){
				sJobName = rSysJob.sj_job_name + i;
				var sJobTiming = rSysJob.sj_cron_timings;
				var sJobMethod = rSysJob.sj_method_name;
				var dJobStartDate = null;
				var dJobEndDate = rSysJob.sj_end_date;
				var aJobArgs = [];
				if(rSysJob.sj_method_args != null && rSysJob.sj_method_args != ""){
					aJobArgs = rSysJob.sj_method_args.split(",");
				}
				
				// see: https://wiki.servoy.com/display/DOCS/scheduler for more info
				
				// see: http://www.quartz-scheduler.org/docs/tutorials/crontrigger.html for more info
				try {
                    plugins.scheduler.addCronJob(sJobName, sJobTiming, globals[sJobMethod], dJobStartDate, dJobEndDate, aJobArgs);
				} catch (e) {
				    globals.dbLog("ERROR Scheduling Job: " + sJobName + ", with timing: " + sJobTiming, "hc1", "running", "", "", null, "logging", "Detail", null, null, null, null); // Detail or Summary
				}
                
                try {
    				if (aJobArgs.length > 0) {
                        if (globals.verboseLogging(aJobArgs[1])) {
                            globals.dbLog("Scheduling Job: " + sJobName + ", with timing: " + sJobTiming, "hc1", "running", "", "", aJobArgs[1], "logging", "Detail", null, null, null, null); // Detail or Summary
                        }
                    }
                } catch (e) {
                    globals.dbLog("ERROR Logging about Scheduling Job: " + sJobName + ", with timing: " + sJobTiming, "hc1", "running", "", "", null, "logging", "Detail", null, null, null, null); // Detail or Summary
                }
			}
		}
	}
}

/**
 * @param {String} sRegisterType
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 * @param {Boolean} [bTest]
 * 
 * @properties={typeid:24,uuid:"616146E1-F45C-4E8E-82D6-E8520F91D247"}
 */
function runAutoRegister(sRegisterType, sOrgID, sSJID, sARID, bTest){
    /***@type {JSRecord<db:/avanti/sys_record_lock>} */
    var rRecordLock = scopes.avDB.getRec('sys_record_lock', 
    		['recordlock_program_name', 'recordlock_user_id', 'recordlock_record_pk'], 
			['onDataChange_registerNumberFormatting', globals.user_id, '5CE4F953-89D4-443B-981D-623D5B03061D']);
    
	if(!rRecordLock && sRegisterType != null && sOrgID != null){
		//Initialize org_id and system preferences
		globals.org_id = sOrgID;
		
		if (utils.hasRecords(_to_sys_organization)) {
			globals.org_name = _to_sys_organization.org_name;
			globals.owner_id = _to_sys_organization.owner_id;
		}
		
		initSystemPrefs();
		
		if (globals.verboseLogging(sOrgID)) {
		   globals.dbLog("Running Auto Register: " + sRegisterType, "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null);
		}
		
		if (sRegisterType == "APIR") {
			//A/P Invoice Register|APIR
			runAPInvoiceRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "CRR") {
			//Cash Receipts Register|CRR
			runCashReceiptsRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "IR") {
			//Invoice Register|IR
			runInvoiceRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "PRR") {
			//Purchase Receipts Register|PRR
			runPurchaseReceiptsRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "RFPR") {
			//Receipts from Production Register|RFPR
			runReceiptsFromProductionRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "ITR") {
			//Inventory Transaction Register|ITR
			runInventoryTransactionRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "WIPAFGR") {
			//WIP and FG Register|WIPAFGR
			runWIPAndFGRegister(sOrgID, sSJID, sARID);
		}
		else if (sRegisterType == "CBP") {
			//Chargeback|CBP
			runChargebackRegister(sOrgID, sSJID, sARID, bTest);
		}
	}
}

/**
 * @private 
 * 
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 * @param {Boolean} [bTest]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"02FCF479-C1C8-4C3A-B284-3A6866ACC504"}
 */
function runChargebackRegister(sOrgID, sSJID, sARID, bTest) {
	var bSuccess = false;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = "Fail"
	var sDetails = "";

	if (sOrgID && sARID && (sSJID || bTest)) {
		globals.org_id = sOrgID;

		if (globals.verboseLogging(sOrgID)) {
			globals.dbLog("Run Chargeback Register", "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null); 
		}

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (areThereValidCBFiles()) {
				bSuccess = (forms.sa_chargeback_posting_dtl.doPost(rAutoRegister) > 0);

				if (bSuccess) {
					sStatus = "Posted";
					sDetails = "Chargebacback succesfully posted."
				}
				else {
					sStatus = "Skipped";
					sDetails = "No records to post"
				}

				updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
			}
			else {
				sStatus = "Fail";
				sDetails = "Must have chargeback files that use hotfolder"
			}
		}
	}

	if (!bTest) {
		updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);
	}
	
	return bSuccess;
	
	function areThereValidCBFiles() {
		if (rAutoRegister.sys_auto_register_to_sa_chargeback_batch_cb_file$selected) {
			for (var i = 1; i <= rAutoRegister.sys_auto_register_to_sa_chargeback_batch_cb_file$selected.getSize(); i++) {
				var rCBF = rAutoRegister.sys_auto_register_to_sa_chargeback_batch_cb_file$selected.getRecord(i);
				
				// HC must use hotfolder 
				if (utils.hasRecords(rCBF.sa_chargeback_batch_cb_file_to_sys_chargeback_file) 
						&& rCBF.sa_chargeback_batch_cb_file_to_sys_chargeback_file.cbf_active
						&& rCBF.sa_chargeback_batch_cb_file_to_sys_chargeback_file.cbf_file_placement_options == "Hot Folder"
						&& rCBF.sa_chargeback_batch_cb_file_to_sys_chargeback_file.cbf_output_folder
						&& utils.hasRecords(rCBF.sa_chargeback_batch_cb_file_to_sys_chargeback_file.sys_chargeback_file_to_sys_chargeback_file_field)) {
							
					return true;
				}
			}
		}
		
		return false;
	}
}

/**
 * Runs the AP Invoice Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"D9552857-B667-4B65-8BEB-04B967E29E7C"}
 */
function runAPInvoiceRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/ap_invoice_register>} */
	var fsAPInvoiceRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Run A/P Invoice Register", "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT apinv_reg_id \
							FROM ap_invoice_register \
							WHERE org_id = ? \
								AND apinv_reg_date_reviewed IS NULL AND apinv_reg_date_updated IS NULL AND apinv_reg_employee_id IS NULL";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "ap_invoice_register";
				fsAPInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsAPInvoiceRegister && fsAPInvoiceRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.

					//Create New Register
					fsAPInvoiceRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'ap_invoice_register');
					var rAPInvoiceRegister = fsAPInvoiceRegister.getRecord(fsAPInvoiceRegister.newRecord(false));
					rAPInvoiceRegister.apinv_reg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.AccountsPayableInvoice);
					rAPInvoiceRegister.apinv_reg_date_reviewed_from = null;
					rAPInvoiceRegister.apinv_reg_date_reviewed_to = application.getServerTimeStamp();
					rAPInvoiceRegister.apinv_reg_employee_id = null;
					rAPInvoiceRegister.apinv_reg_approved = 0;
					databaseManager.saveData(rAPInvoiceRegister);

					// Perform auto-review
					var nPaymentCount = globals.autoReviewAPInvoiceRegister(rAPInvoiceRegister);

					// Check if zero records, if so reset the review flag
					if (nPaymentCount == 0) {
						fsAPInvoiceRegister.deleteRecord(rAPInvoiceRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.AccountsPayableInvoice);
						
						sStatus = "Skipped";
						sDetails = "No Payments Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {
						
						// Update Register
						bResult = globals.updateAPInvoiceRegister(true, fsAPInvoiceRegister, fsAPInvoiceRegister.ap_invoice_register_to_ap_invoice);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT apinv_reg_id \
							FROM ap_invoice_register \
							WHERE org_id = ? \
							AND apinv_reg_date_reviewed IS NOT NULL AND	apinv_reg_date_updated IS NULL AND apinv_reg_approved = 1";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "ap_invoice_register";
				fsAPInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsAPInvoiceRegister != null && fsAPInvoiceRegister.getSize() == 1) {
					bResult = globals.updateAPInvoiceRegister(true, fsAPInvoiceRegister, fsAPInvoiceRegister.ap_invoice_register_to_ap_invoice);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}

	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Runs the Cash Receipts Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"C7795024-C56B-4E6F-AE97-5A9C52654D58"}
 */
function runCashReceiptsRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/sa_cash_receipt_register>} */
	var fsCRInvoiceRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Run Cash Receipts Register", "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT sacr_reg_id \
					FROM sa_cash_receipt_register \
					WHERE org_id = ? \
					AND sacr_reg_date_reviewed IS NULL AND sacr_reg_date_updated IS NULL AND sacr_reg_empl_id IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "sa_cash_receipt_register";
				fsCRInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsCRInvoiceRegister && fsCRInvoiceRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.
				
					//Create New Register
					fsCRInvoiceRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'sa_cash_receipt_register');
					var rCRInvoiceRegister = fsCRInvoiceRegister.getRecord(fsCRInvoiceRegister.newRecord(false));
					rCRInvoiceRegister.sacr_reg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.CashReceiptsRegister);
					rCRInvoiceRegister.sacr_reg_date_reviewed_from = null;
					rCRInvoiceRegister.sacr_reg_date_reviewed_to = application.getServerTimeStamp();
					rCRInvoiceRegister.sacr_reg_empl_id = null;
					rCRInvoiceRegister.sacr_reg_approved = 0;
					databaseManager.saveData(rCRInvoiceRegister);

					// Perform auto-review
					var nReceiptCount = globals.autoReviewCashReceiptsRegister(rCRInvoiceRegister);

					// Check if zero records, if so reset the review flag
					if (nReceiptCount == 0) {
						fsCRInvoiceRegister.deleteRecord(rCRInvoiceRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.CashReceiptsRegister);
						
						sStatus = "Skipped";
						sDetails = "No Cash Receipts Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {
						
						// Update Register
						bResult = globals.updateCashReceiptRegister(true, fsCRInvoiceRegister, fsCRInvoiceRegister.sa_cash_receipt_register_to_sa_cash_receipt, fsCRInvoiceRegister.sa_cash_receipt_register_to_sa_cash_receipt_cancel);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT sacr_reg_id \
	    			FROM sa_cash_receipt_register \
	    			WHERE org_id = ? \
	    			AND sacr_reg_date_reviewed IS NOT NULL AND	sacr_reg_date_updated IS NULL AND sacr_reg_approved = 1 ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "sa_cash_receipt_register";
				fsCRInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsCRInvoiceRegister != null && fsCRInvoiceRegister.getSize() == 1) {
					bResult = globals.updateCashReceiptRegister(true, fsCRInvoiceRegister, fsCRInvoiceRegister.sa_cash_receipt_register_to_sa_cash_receipt, fsCRInvoiceRegister.sa_cash_receipt_register_to_sa_cash_receipt_cancel);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}

	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Runs the Invoice Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"14093AED-A0FD-49AA-87E1-9E11530B678F"}
 */
function runInvoiceRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/sa_invoice_register>} */
	var fsInvoiceRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";
	var bDivPlantEnabled = Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter));
	
	/**@type {JSFoundSet<db:/avanti/sa_apply_credit_note>} */
	var fsApplyCreditNote;

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Running Invoice Register", "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;
		
		//Get auto run settings
		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register",["ar_id"],[sARID]);
		
		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post && !bDivPlantEnabled) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT invreg_id \
					FROM sa_invoice_register \
					WHERE org_id = ? \
					AND invreg_date_reviewed IS NULL AND invreg_date_updated IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "sa_invoice_register";
				fsInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);
				
				if (fsInvoiceRegister && fsInvoiceRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.
					
					// Create New Register
					fsInvoiceRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'sa_invoice_register');
					var rInvoiceRegister = fsInvoiceRegister.getRecord(fsInvoiceRegister.newRecord(false));
					rInvoiceRegister.invreg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.InvoiceRegister);
					rInvoiceRegister.invreg_date_reviewed_from = null;
					rInvoiceRegister.invreg_date_reviewed_to = application.getServerTimeStamp();
					rInvoiceRegister.invreg_employee_id = null;
					rInvoiceRegister.invreg_div_id = null;
					rInvoiceRegister.invreg_plant_id = null;
					rInvoiceRegister.invreg_approved = 0;
					databaseManager.saveData(rInvoiceRegister);
					
					// Perform auto-review
					var nInvoiceCount = globals.autoReviewInvoiceRegister(rInvoiceRegister);
					
					// Check if zero records, if so delete register otherwise update
					if (nInvoiceCount == 0) {
						fsInvoiceRegister.deleteRecord(rInvoiceRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.InvoiceRegister);
						
						sStatus = "Skipped";
						sDetails = "No Invoices Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
					else {
						// Update Register
						/**@type {JSFoundSet<db:/avanti/sa_apply_credit_note>} */
						fsApplyCreditNote = fsInvoiceRegister.sa_invoice_register_to_sa_apply_credit_note;

						bResult = globals.updateInvoiceRegister(true, fsInvoiceRegister, fsInvoiceRegister.sa_invoice_register_to_sa_invoice, fsApplyCreditNote);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
				
			} else {

				oSQL.sql = "SELECT invreg_id \
					FROM sa_invoice_register \
					WHERE invreg_date_reviewed IS NOT NULL \
					AND org_id = ? AND invreg_date_updated IS NULL AND invreg_approved = 1";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "sa_invoice_register";
				fsInvoiceRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsInvoiceRegister != null && fsInvoiceRegister.getSize() == 1) {

					/**@type {JSFoundSet<db:/avanti/sa_apply_credit_note>} */
					fsApplyCreditNote = fsInvoiceRegister.sa_invoice_register_to_sa_apply_credit_note;

					bResult = globals.updateInvoiceRegister(true, fsInvoiceRegister, fsInvoiceRegister.sa_invoice_register_to_sa_invoice, fsApplyCreditNote);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}
	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Runs the Purchase Receipts Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}	
 *
 * @properties={typeid:24,uuid:"D6B7FD27-45CF-4B18-81D0-9AFA0920A6F7"}
 */
function runPurchaseReceiptsRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/po_receipt_register>} */
	var fsPOReceiptRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";
	var bDivPlantEnabled = Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter));

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Running Purchase Receipts Register", "hc1", "running", null, null, null, "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post && !bDivPlantEnabled) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT porreg_id \
							FROM po_receipt_register \
							WHERE org_id = ? \
							AND porreg_date_reviewed IS NULL AND porreg_date_updated IS NULL AND porreg_employee_id IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "po_receipt_register";
				fsPOReceiptRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsPOReceiptRegister && fsPOReceiptRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.

					//Create New Register
					fsPOReceiptRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'po_receipt_register');
					var rPOReceiptRegister = fsPOReceiptRegister.getRecord(fsPOReceiptRegister.newRecord(false));
					rPOReceiptRegister.porreg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.PurchaseReceiptsRegister);
					rPOReceiptRegister.porreg_from_date = null;
					rPOReceiptRegister.porreg_to_date = application.getServerTimeStamp();
					rPOReceiptRegister.porreg_employee_id = null;
					rPOReceiptRegister.porreg_div_id = null;
					rPOReceiptRegister.porreg_plant_id = null;
					rPOReceiptRegister.porreg_approved = 0;
					databaseManager.saveData(rPOReceiptRegister);

					// Perform auto-review
					var nReceiptCount = globals.autoReviewPurchaseReceiptsRegister(rPOReceiptRegister);

					// Check if zero records, if so reset the review flag
					if (nReceiptCount == 0) {
						fsPOReceiptRegister.deleteRecord(rPOReceiptRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.PurchaseReceiptsRegister);
						
						sStatus = "Skipped";
						sDetails = "No Purchase Receipts Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {
						
						// Update Register
						bResult = globals.updatePurchaseReceiptsRegister(true, fsPOReceiptRegister, fsPOReceiptRegister.po_receipt_register_to_po_receipt, fsPOReceiptRegister.po_receipt_register_to_po_receipt_cancel);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT porreg_id \
							FROM po_receipt_register \
							WHERE org_id = ? \
							AND porreg_date_reviewed IS NOT NULL AND porreg_date_updated IS NULL AND porreg_approved = 1 ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "po_receipt_register";
				fsPOReceiptRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsPOReceiptRegister != null && fsPOReceiptRegister.getSize() == 1) {
					bResult = globals.updatePurchaseReceiptsRegister(true, fsPOReceiptRegister, fsPOReceiptRegister.po_receipt_register_to_po_receipt, fsPOReceiptRegister.po_receipt_register_to_po_receipt_cancel);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}

	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Runs the Receipts from Production Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"470B16D3-E89D-432E-9E7D-0781B2268D92"}
 */
function runReceiptsFromProductionRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/prod_receipt_register>} */
	var fsProdReceiptRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Running Receipts From Prod Register", "hc1", "running", "", "", "", "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT prodrec_reg_id \
			    			FROM prod_receipt_register \
			    			WHERE org_id = ? \
			    			AND prodrec_reg_date_reviewed IS NULL AND prodrec_reg_date_updated IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "prod_receipt_register";
				fsProdReceiptRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsProdReceiptRegister && fsProdReceiptRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.

					//Create New Register
					fsProdReceiptRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'prod_receipt_register');
					var rProdReceiptRegister = fsProdReceiptRegister.getRecord(fsProdReceiptRegister.newRecord(false));
					rProdReceiptRegister.prodrec_reg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.ReceiptsFromProduction);
					rProdReceiptRegister.prodrec_from_date = null;
					rProdReceiptRegister.prodrec_to_date = application.getServerTimeStamp();
					rProdReceiptRegister.prodrec_empl_id = null;
					rProdReceiptRegister.prodrec_div_id = null;
					rProdReceiptRegister.prodrec_plant_id = null;
					rProdReceiptRegister.prodrec_approved = 0;
					databaseManager.saveData(rProdReceiptRegister);

					// Perform auto-review
					var nReceiptCount = globals.autoReviewProductionReceiptsRegister(rProdReceiptRegister);

					// Check if zero records, if so reset the review flag
					if (nReceiptCount == 0) {
						fsProdReceiptRegister.deleteRecord(rProdReceiptRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.ReceiptsFromProduction);

						sStatus = "Skipped";
						sDetails = "No Receipts Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {

						// Update Register
						bResult = globals.updateProdReceiptsRegister(true, fsProdReceiptRegister, fsProdReceiptRegister.prod_receipt_register_to_prod_receipt);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT prodrec_reg_id \
			    			FROM prod_receipt_register \
			    			WHERE org_id = ? \
			    			AND prodrec_reg_date_reviewed IS NOT NULL AND prodrec_reg_date_updated IS NULL AND prodrec_approved = 1 ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "prod_receipt_register";
				fsProdReceiptRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsProdReceiptRegister != null && fsProdReceiptRegister.getSize() == 1) {
					bResult = globals.updateProdReceiptsRegister(true, fsProdReceiptRegister, fsProdReceiptRegister.prod_receipt_register_to_prod_receipt);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}
	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}

/**
 * Runs the Inventory Transaction Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}	
 *
 * @properties={typeid:24,uuid:"58F294BF-55C5-4907-A0DC-87AB6AAD75A9"}
 */
function runInventoryTransactionRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/in_transaction_register>} */
	var fsINTransactionRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";
	var bDivPlantEnabled = Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter));

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Running Invetory Transaction Register", "hc1", "running", "", "", "", "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post && !bDivPlantEnabled) {
				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT itreg_id \
							FROM in_transaction_register \
							WHERE org_id = ? \
							AND itreg_date_reviewed IS NULL AND itreg_date_updated IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "in_transaction_register";
				fsINTransactionRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsINTransactionRegister && fsINTransactionRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.

					// Create New Register
					fsINTransactionRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'in_transaction_register');
					var rINTransactionRegister = fsINTransactionRegister.getRecord(fsINTransactionRegister.newRecord(false));
					rINTransactionRegister.itreg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.ItemTransactionRegister);
					rINTransactionRegister.itreg_from_date = null;
					rINTransactionRegister.itreg_to_date = application.getServerTimeStamp();
					rINTransactionRegister.itreg_div_id = null;
					rINTransactionRegister.itreg_plant_id = null;
					rINTransactionRegister.itreg_approved = "0";
					databaseManager.saveData(rINTransactionRegister);

					// Perform auto-review
					var nCostCount = globals.autoReviewInventoryTransactionRegister(rINTransactionRegister, 1, false);

					// Check if zero records, if so delete register otherwise update
					if (nCostCount == 0) {
						fsINTransactionRegister.deleteRecord(rINTransactionRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.ItemTransactionRegister);

						sStatus = "Skipped";
						sDetails = "No Transactions Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {
						// Update Register
						bResult = globals.updateInventoryTransactionRegister(true, fsINTransactionRegister.getRecord(1), fsINTransactionRegister.in_transaction_register_to_in_trans_entry_header);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT itreg_id \
							FROM in_transaction_register \
							WHERE itreg_date_reviewed IS NOT NULL \
							AND itreg_date_updated IS NULL AND itreg_approved = 1 AND org_id = ?";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "in_transaction_register";
				fsINTransactionRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsINTransactionRegister != null && fsINTransactionRegister.getSize() == 1) {

					bResult = globals.updateInventoryTransactionRegister(true, fsINTransactionRegister.getRecord(1), fsINTransactionRegister.in_transaction_register_to_in_trans_entry_header);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}
	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Runs the WIP and FG Register for the given org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sARID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"7B56E50E-**************-42E43F254E95"}
 */
function runWIPAndFGRegister(sOrgID, sSJID, sARID) {
	var bResult = false;
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	/** @type {JSFoundSet<db:/avanti/prod_job_cost_register>} */
	var fsWIPFGRegister = null;
	var dTimestamp = application.getServerTimeStamp();
	var sStatus = ""
	var sDetails = "";
	var bDivPlantEnabled = Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter));

	if (globals.verboseLogging(sOrgID)) {
		globals.dbLog("Running WIP & FG Register", "hc1", "running", "", "", "", "logging", "Detail", null, null, null, null); // Detail or Summary
	}

	if (sOrgID != null && utils.stringTrim(sOrgID) != "") {
		globals.org_id = sOrgID;

		/**@type {JSFoundSet<db:/avanti/sys_auto_register>} */
		var rAutoRegister = scopes.avDB.getRec("sys_auto_register", ["ar_id"], [sARID]);

		if (rAutoRegister) {
			if (rAutoRegister.ar_auto_post && !bDivPlantEnabled) {

				// Make sure no un-posted registers exist
				oSQL.sql = "SELECT jc_reg_id \
					FROM prod_job_cost_register \
					WHERE org_id = ? \
					AND jc_reg_date_reviewed IS NULL AND jc_reg_date_updated IS NULL ";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "sa_invoice_register";
				fsWIPFGRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsWIPFGRegister && fsWIPFGRegister.getSize() == 0) { // If there happens to be an existing open register, skip this process as per specification.

					// Create New Register
					fsWIPFGRegister = databaseManager.getFoundSet(avBase_dbase_avanti, 'prod_job_cost_register');
					var rWIPFGRegister = fsWIPFGRegister.getRecord(fsWIPFGRegister.newRecord(false));
					rWIPFGRegister.jc_reg_number = globals.avBase_getCurrentRegisterNumber(scopes.avAccounting.REGISTER_TYPE.JobCost);
					rWIPFGRegister.jc_reg_from_date = null;
					rWIPFGRegister.jc_reg_to_date = application.getServerTimeStamp();
					rWIPFGRegister.jc_reg_div_id = null;
					rWIPFGRegister.jc_reg_plant_id = null;
					rWIPFGRegister.jc_reg_approved = "0";
					databaseManager.saveData(rWIPFGRegister);

					// Perform auto-review
					var nCostCount = globals.autoReviewWIPRegister(rWIPFGRegister, 1, false);

					// Check if zero records, if so delete register otherwise update
					if (nCostCount == 0) {
						fsWIPFGRegister.deleteRecord(rWIPFGRegister);
						globals.avBase_resetLastRegisterNumber(scopes.avAccounting.REGISTER_TYPE.JobCost);

						sStatus = "Skipped";
						sDetails = "No Production Costs Found.";
						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					} else {
						// Update Register
						bResult = globals.updateWIPANDFGRegister(true, rWIPFGRegister, rWIPFGRegister.prod_job_cost_register_to_prod_job_cost_transactions);

						if (bResult) {
							sStatus = "Success";
							sDetails = "Register succesfully run."
						} else {
							sStatus = "Fail";
							sDetails = "Failed to run the register."
						}

						updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
					}
				}
			} else {
				oSQL.sql = "SELECT jc_reg_id \
	    			FROM prod_job_cost_register \
	    			WHERE jc_reg_date_reviewed IS NOT NULL \
	    			AND	jc_reg_date_updated IS NULL AND jc_reg_approved = '1' AND org_id = ?";
				oSQL.args = [globals.org_id.toString()];
				oSQL.table = "prod_job_cost_register";
				fsWIPFGRegister = globals.avUtilities_sqlFoundset(oSQL);

				if (fsWIPFGRegister != null && fsWIPFGRegister.getSize() == 1) {
					rWIPFGRegister = fsWIPFGRegister.getRecord(1);
					bResult = globals.updateWIPANDFGRegister(true, rWIPFGRegister, rWIPFGRegister.prod_job_cost_register_to_prod_job_cost_transactions);

					if (bResult) {
						sStatus = "Success";
						sDetails = "Register succesfully run."
					} else {
						sStatus = "Fail";
						sDetails = "Failed to run the register."
					}

					updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dTimestamp);
				} else {
					bResult = true;
					sStatus = "Success";
					sDetails = "No approved register to run.";
				}
			}
		}
	} else {
		sDetails = "No org_id supplied.";
	}

	if (bResult) {
		sStatus = "Success";
	} else {
		sStatus = "Fail";
	}

	updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dTimestamp, sDetails);

	return bResult;
}


/**
 * Updates the last run datetime and status in sys_auto_register for a given ar_id and org_id
 * @param {String} sOrgID
 * @param {String} sARID
 * @param {String} sStatus
 * @param {Date} dDatetime
 *
 * @properties={typeid:24,uuid:"A0B9B6CD-A8D5-4C0E-BEFF-E586711615B6"}
 */
function updateAutoRegisterRunStatus(sOrgID, sARID, sStatus, dDatetime){
	var bResult = false;
	
	if (globals.verboseLogging(sOrgID)) {	
    	globals.dbLog("Updating Auto Register Run Status", "hc1", "running", "sARID: " + sARID + ", Status: " + sStatus, "", sOrgID, "logging", "Detail", null, null, null, null); // Detail or Summary
    }
	
	if(sOrgID != null && sARID != null && sStatus != null && dDatetime != null){
		var oSQL ={};
		oSQL.server = globals.avBase_dbase_avanti;
		oSQL.args = [];
		var dsData = null;
		
		//update sys_auto_register
	    oSQL.sql = "select ar_id from sys_auto_register where ar_id = ? and org_id = ?";
	    oSQL.args = [sARID, sOrgID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
		/** @type {JSFoundSet<db:/avanti/sys_auto_register>}*/
		var fsAutoRegister = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sys_auto_register");
		fsAutoRegister.loadRecords(dsData);
		
		if(fsAutoRegister.getSize() == 1){
			fsAutoRegister.ar_last_reg_run_datetime = scopes.avUtils.convertTimeStampToOrgTZ(dDatetime);
			fsAutoRegister.ar_last_reg_run_status = sStatus;
			
			if(databaseManager.saveData(fsAutoRegister)){
				bResult = true;
				plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti,"sys_auto_register");
			}
		}
	}
	
	return bResult;
}

/**
 * Updates the last run datetime and status in sys_scheduled_job for a given sj_id and org_id
 * @param {String} sOrgID
 * @param {String} sSJID
 * @param {String} sStatus
 * @param {Date} dDatetime
 * @param {String} sDetails
 *
 * @properties={typeid:24,uuid:"6ADDF6D4-C8B3-424C-86F9-11A01E245FB6"}
 */
function updateScheduledJobRunStatus(sOrgID, sSJID, sStatus, dDatetime, sDetails){
	var bResult = false;
	
	if (globals.verboseLogging(sOrgID)) {
        globals.dbLog("Updating Scheduled Job Run Status", "hc1", "running", "sSJID: " + sSJID + ", Status: " + sStatus, "", sOrgID, "logging", "Detail", null, null, null, null); // Detail or Summary
	}
	
	if(sOrgID != null && sSJID != null && sStatus != null && dDatetime != null){
		var oSQL ={};
		oSQL.server = globals.avBase_dbase_avanti;
		oSQL.args = [];
		var dsData = null;
		
		//update sys_auto_register
	    oSQL.sql = "select sj_id from sys_scheduled_job where sj_id = ? and org_id = ?";
	    oSQL.args = [sSJID, sOrgID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
		/** @type {JSFoundSet<db:/avanti/sys_scheduled_job>}*/
		var fsScheduledJob = databaseManager.getFoundSet(globals.avBase_dbase_avanti,"sys_scheduled_job");
		fsScheduledJob.loadRecords(dsData);
		
		if(fsScheduledJob.getSize() == 1){
			fsScheduledJob.sj_last_run = dDatetime;
			fsScheduledJob.sj_last_run_result = sStatus;
			fsScheduledJob.sj_last_run_result_details = sDetails;
			
			if(databaseManager.saveData(fsScheduledJob)){
				bResult = true;
				plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti,"sys_scheduled_job");
			}
		}
	}
	
	return bResult;
}

/**
 * @properties={typeid:24,uuid:"F3BB623D-4A0F-4406-B52C-3C973E05C194"}
 */
function initSystemPrefs(){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = {},
		/***@type {JSDataSet} */
		dsData;

	// GD - Nov 28, 2016: SL-10445: Found that the above queries checking for matching number of pref's was not accurate enough, and we need to look for any missing one
	oSQL.sql = "SELECT COUNT(*) FROM app_preference app WHERE app.pref_id NOT IN (SELECT sp.pref_id FROM sys_preference sp WHERE sp.org_id = ?)";
	oSQL.args = [globals.org_id];
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	if( dsData.getValue(1,1) > 0) {
	
		updatePreferences();	
	}
	
	// GD - Nov 12, 2014: Build preference object
	scopes.avUtils.getPrefs();
}

/**
 * Scans the in_item table to find items which have expiry date less than
 * current time and sets it to expired status 
 * for the organizations having sys pref 241 set to Yes
 * 
 * @properties={typeid:24,uuid:"2FA4BEC4-C37C-4B52-94F6-5C67EFA6DE1D"}
 */
function runExpireInventoryItems(){
    application.output("Starting Expire Inventory Items script",LOGGINGLEVEL.DEBUG);
    
    var sSql = "SELECT item.item_id from sys_preference as syspref \
                INNER JOIN in_item as item ON item.org_id = syspref.org_id \
                WHERE syspref.pref_id='BD6954D5-96C3-4179-BDA3-4A7134C1CA60' \
                AND syspref.syspref_value='1' AND item.item_status !='E' AND item.item_expiry_date<=?";

    var args = [plugins.DateUtils.dateFormat(application.getServerTimeStamp(), 'yyyy-MM-dd')];

    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, -1);

    /** @type{JSFoundSet<db:/avanti/in_item>} */
    var fsItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
    fsItems.loadRecords(ds);

    for (var i = 1; i <= fsItems.getSize(); i++) {
        fsItems.getRecord(i).item_status = 'E';
    }
    
    if (i == 1){
        application.output("No items were found to update",LOGGINGLEVEL.INFO);    
        return false;
     }
    
    application.output("Successfully expired "+(i-1)+" item(s).", LOGGINGLEVEL.INFO);
    return databaseManager.saveData(fsItems);
}


/**
 * Scans the order table to find estimates which have good until date less than
 * current time and sets it to expired status 
 * @properties={typeid:24,uuid:"57412708-90F2-4FF6-B924-0104B8087BE2"}
 */
function runExpireEstimates(){
	application.output("Starting Expire Estimates script", LOGGINGLEVEL.DEBUG);

	var sSql = "SELECT TOP (20000) ord.ordh_id \
	 			  FROM sys_preference as syspref \
            INNER JOIN sa_order as ord ON ord.org_id = syspref.org_id \
               	 WHERE syspref.pref_id='B3F2A0DE-2622-40F4-A87E-2D47A2BBB56D' \
               	   AND syspref.syspref_value IS NOT NULL \
               	   AND syspref.syspref_value > 0 \
               	   AND ord.ordh_document_type = ? \
               	   AND ord.ordh_estimate_status = ? \
               	   AND ord.ordh_estimate_good_until_date < ?";

	var args = [scopes.avUtils.DOCUMENT_TYPE.Estimate, scopes.avUtils.ENUM_ESTIMATE_STATUS.Open, plugins.DateUtils.dateFormat(application.getServerTimeStamp(), 'yyyy-MM-dd')];

	var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, -1);

	if (ds && ds.getMaxRowIndex() > 0) {
		/** @type{JSFoundSet<db:/avanti/sa_order>} */
		var fsOrder = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order');
		fsOrder.loadRecords(ds);
		
		var updater = databaseManager.getFoundSetUpdater(fsOrder);
		updater.setColumn('ordh_estimate_status', scopes.avUtils.ENUM_ESTIMATE_STATUS.Expired);
		updater.performUpdate();

		application.output("Successfully expired " + ds.getMaxRowIndex() + " estimate(s).", LOGGINGLEVEL.INFO);
		return true;
	}
	else {
		application.output("No estimates were found to update", LOGGINGLEVEL.INFO);
		return false;
	}
}

/**
 * @param {Number} triggersAlertsId
 *
 * @properties={typeid:24,uuid:"3C12A0DE-6E0F-43A3-9E45-DA4EA59646E6"}
 */
function deleteSysTriggersAlertsRow( triggersAlertsId ){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	
	oSQL.sql = "DELETE FROM sys_triggers_alerts \
 		   WHERE sys_triggers_alerts_id = ?";
	
	oSQL.args = [triggersAlertsId];
	globals["avUtilities_sqlRaw"](oSQL);
}

/**
 * @properties={typeid:24,uuid:"CB30D98D-D031-4AAB-9711-AD5136558C6C"}
 */
function runAutomationTasks() {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL1 = { };
	/***@type {JSDataSet} */
	var dsData;
	/***@type {{org_id:UUID, owner_id:UUID, user_id:Number, org_name:String}}*/
	var paramVar = { };
	var datasource;
	var status;
	var recordId;
	var triggersAlertsId;
	var info = { };
	var credentials;
	var apiUrl;
	var stopProcessing = false;
	var useValue;
	var timeOut;
	var clientTimeOut = 60000;
	var in_progress_date;
	
	useValue = plugins.UserManager.getSettingsProperty('user.automationtasks.enabled');
	apiUrl = plugins.UserManager.getSettingsProperty('user.automationtasks.url');
	timeOut = plugins.UserManager.getSettingsProperty('user.automationtasks.timeout');
	
	if (timeOut && timeOut != null && timeOut != ""){
		clientTimeOut = parseInt(timeOut);
	}
	
	if (apiUrl && apiUrl != null && apiUrl != "") {
		apiUrl = apiUrl + "api/data";
	} else {
		if (useValue != null && useValue != "0") {
			application.output("Unable to retrieve the automation task URL.", LOGGINGLEVEL.ERROR);
		}
		return false;
	}

	oSQL.server = globals.avBase_dbase_avanti;
	oSQL1.server = globals.avBase_dbase_avanti;
	oSQL.sql = "SELECT sys_triggers_alerts_id, FORMAT ( \
		in_progress_date \
		, 'yyyy-MM-dd hh:mm:ss') as 'in_progress_date' \
	 	, datasource, data \
	 	FROM sys_triggers_alerts \
		WHERE status = ? ";
	
	oSQL.args = ['In Progress'];
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0) {
	
		triggersAlertsId = dsData.getValue(1, 1);
		in_progress_date = dsData.getValue(1, 2);	
		if(in_progress_date == null || in_progress_date == ''){
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			var oSQL2 = { };
			oSQL2.server = globals.avBase_dbase_avanti;
			oSQL2.sql = "UPDATE sys_triggers_alerts SET \
				in_progress_date = GETDATE() \
			    WHERE sys_triggers_alerts_id = ?";
			oSQL2.args = [triggersAlertsId];
			globals["avUtilities_sqlRaw"](oSQL2);
		} else {
			var current_ten_minutes_ago = new Date();
			current_ten_minutes_ago.setMinutes( current_ten_minutes_ago.getMinutes() - 10); 
			var inProgressDate = new Date( utils.parseDate(in_progress_date, 'yyyy-MM-dd hh:mm:ss') );	
			if(inProgressDate <= current_ten_minutes_ago ){ // 10 mins ago
				// same row in progress for more then 10 mins
				var message = "Automation Task, row in progress for more than 10 minutes, " + " In Progress Date : " + in_progress_date +
				    " ten minutes ago: " + current_ten_minutes_ago.toString() +
					" datasource: " +
					dsData.getValue(1, 3) + 
					" data: " + dsData.getValue(1, 4);				
				application.output(message , LOGGINGLEVEL.ERROR);
				deleteSysTriggersAlertsRow( triggersAlertsId );
			}
		}
		return false;
	}

	oSQL.sql = "SELECT sys_triggers_alerts_id, org_id, owner_id, user_id, org_name, record_id, datasource, status, data, credentials, created_date \
				  FROM sys_triggers_alerts \
			  ORDER BY sys_triggers_alerts_id ASC";
	oSQL.args = [];
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0) {
		
		var httpClient = null;
		try {
			httpClient = plugins.http.createNewHttpClient();
			httpClient.setTimeout(clientTimeOut);
		}
		catch(e) {
			application.output("Automation Task unable to create a new Http Client. " + e.message, LOGGINGLEVEL.ERROR);
			return false;
		}
		
		for (var c = 1; c <= dsData.getMaxRowIndex(); c++) {
			if (stopProcessing === true) {
				break;
			}
			triggersAlertsId = dsData.getValue(c, 1);
			paramVar.org_id = dsData.getValue(c, 2);
			paramVar.owner_id = dsData.getValue(c, 3);
			paramVar.user_id = dsData.getValue(c, 4);
			paramVar.org_name = dsData.getValue(c, 5);
			recordId = dsData.getValue(c, 6);
			datasource = dsData.getValue(c, 7);
			status = dsData.getValue(c, 8);
			var infoData = dsData.getValue(c, 9);			
			if(infoData === null){
				// no data to send
				deleteSysTriggersAlertsRow( triggersAlertsId );
				continue;
			}
			infoData = infoData.trim();
			if(infoData == '' || infoData == '{}' || infoData == '{ }' ){
				// no data to send
				deleteSysTriggersAlertsRow( triggersAlertsId );
				continue;
			}	
			info = JSON.parse(infoData);
			credentials = JSON.parse(dsData.getValue(c, 10));

			if (status != 'Pending') {
				continue;
			}

			if (paramVar.org_id && paramVar.owner_id && credentials && apiUrl) {
				oSQL1.sql = "UPDATE sys_triggers_alerts \
					SET status = ? , in_progress_date = GETDATE() \
					WHERE sys_triggers_alerts_id = ?";
				oSQL1.args = ['In Progress', triggersAlertsId];
				if (!globals["avUtilities_sqlRaw"](oSQL1)) {
					
					httpClient.close();
					application.output("Automation Task, failed to update row to: In Progress status", LOGGINGLEVEL.ERROR);
					return false;
				}

				var send = {
					source: datasource,
					data: info,
					options: { paramVar: paramVar }
				};

				// Send data to automation task for triggers and alerts.
				var statusCode = null;
				var response;
				try {

					var request = httpClient.createPostRequest(apiUrl);
					if (send) {
						request.addHeader('Content-type', 'application/json');
						request.setBodyContent(JSON.stringify(send));
					}
					response = request.executeRequest(credentials.username, credentials.password);
					
					statusCode = response.getStatusCode();
					if (statusCode != 200) {
						application.output("Automation Task http status code " + statusCode + " detected. Expecting status code 200.", LOGGINGLEVEL.ERROR);
					}
										
					if (statusCode != null) {
						response.close();
					} else {						
						application.output("Automation Task, statusCode is null", LOGGINGLEVEL.ERROR);
					}
				} catch (e) {
					application.output("executeRequest from triggers and alerts failed " + e.message, LOGGINGLEVEL.ERROR);
				}

				if (statusCode == 200) {
					deleteSysTriggersAlertsRow( triggersAlertsId );
				} else {
					/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
					var oSQL2 = { };
					oSQL2.server = globals.avBase_dbase_avanti;
					stopProcessing = true;
					application.output("Automation task stopped processing due to invalid status code " + statusCode + ". " + response.getException(), LOGGINGLEVEL.ERROR);
					oSQL2.sql = "UPDATE sys_triggers_alerts \
						SET status = ? , in_progress_date = NULL \
					    WHERE sys_triggers_alerts_id = ?";
					oSQL2.args = ['Pending', triggersAlertsId];
					globals["avUtilities_sqlRaw"](oSQL2);
				}
			}
		}
		
		if(httpClient) {	
			httpClient.close();
		}
	}
	return true;
}

/**
 * @param {String} org_id
 * @param {String} data
 * @param {String} url
 *
 * @properties={typeid:24,uuid:"08FED933-1351-425E-82A9-6AC73CE097A9"}
 */
function SendDataToInventoryWebhook( org_id, data, url ){

	var ret = { status: false, messsage: i18n.getI18NMessage('avanti.dialog.descartesExportSuccessfully') };
	
	var setTimeout = 3000; // 3 sec's
	var httpClient = plugins.http.createNewHttpClient();
	httpClient.setTimeout(setTimeout);
	
	var oPostRequest = httpClient.createPostRequest(url);		
	oPostRequest.addHeader('Content-type', 'application/json');

	var sData = '';
	
	try {
		sData = JSON.stringify(data,null, 2);
		oPostRequest.setBodyContent(sData);
		
		var oPostResponse = oPostRequest.executeRequest();
		
	 	var status = oPostResponse.getStatusCode();
	 	if (status !== plugins.http.HTTP_STATUS.SC_OK) {
	 		throw new Error('Execute request responded with incorrect status code ' + status);
	 	}
	 	ret.status = true;
	} catch (ex) {
		application.output('Error in sending data. ' + ex.message, LOGGINGLEVEL.ERROR);
		ret.messsage = ex.message;
    } finally {
    	if (httpClient) {
    		httpClient.close();
        }
    	
        var message = ret.messsage;
        var source_param = 'json_export'; 		
        var type_param = 'export';
        var type_details_param = 'json' 		
        var method_param = 'http'; 				
        var function_param ='json_export'; 		
        
        var log_status_param = 'Success';
        if(!ret.status){
        	log_status_param = 'Error'
        }
        
		var dbLogParentID = globals.dbLog(
			message, 
			source_param, 
			type_param, 
			type_details_param,
			method_param,
			application.getUUID(org_id),
			function_param, 
			'Summary', 
			null,
			log_status_param);
		
		 globals.dbLogUpdate(dbLogParentID, 
			 message,
			 log_status_param,
			 '',					// Job Number
			 source_param, 
			 type_param,
			 type_details_param,	
			 method_param,			
			 'Export Inventory'); 	
		
		globals.dbLogWriteDetails(dbLogParentID, 'Export', null, sData );
		return ret;
    }
}

/**
 * @param {Object} input
 *
 * @properties={typeid:24,uuid:"17801887-CF4C-4389-AE4B-CCA3C6260034"}
 */
function processingInventoryWebhook(input){
	
    globals.org_id = input.org_id.toString();
    globals.svy_sec_lgn_organization_id = globals.org_id;
    globals.owner_id = input.owner_id.toString();
    globals.svy_sec_owner_id = globals.owner_id;
       
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL1 = { };
	/***@type {JSDataSet} */
	var dsData;
	
	var is_running = 0;
	var last_sync_date = null;
	var current_run_datetime = new Date();
	current_run_datetime.setSeconds(0);
	current_run_datetime.setMilliseconds(0);
    	
	var tmp_item_inventory_webhook_uuid = null;
	
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.sql = "SELECT is_running, last_sync_date, tmp_item_inventory_webhook_uuid, org_id FROM tmp_item_inventory_webhook WHERE org_id = ?";	
	oSQL.args = [input.org_id];
	
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	
	if (dsData && dsData.getMaxRowIndex() > 0) {
		dsData.rowIndex = 1;
		is_running = dsData['is_running'];
		last_sync_date = dsData['last_sync_date'];
		tmp_item_inventory_webhook_uuid = dsData['tmp_item_inventory_webhook_uuid'];
		
		if(last_sync_date === null){
			last_sync_date = current_run_datetime;
		}
	} else {		
		// never been run
		last_sync_date = current_run_datetime;
	}
	if(is_running === 1){
		// currently running
		application.output('Inventory webhook still currently processing.', LOGGINGLEVEL.WARNING);
		return;
	}
	
	var new_last_sync_date = null;
	
	try {
		// set lock
		if(tmp_item_inventory_webhook_uuid !== null){	
			oSQL.sql = "UPDATE tmp_item_inventory_webhook SET is_running = 1, last_sync_date = ? WHERE org_id = ?";
			oSQL.args = [last_sync_date, input.org_id];
		} else {
			oSQL.sql = "INSERT INTO tmp_item_inventory_webhook (org_id, is_running, last_sync_date, tmp_item_inventory_webhook_uuid) VALUES (?,?,?,?)";
			oSQL.args = [input.org_id, 1, last_sync_date, application.getUUID().toString()];
		}
		plugins.rawSQL.executeSQL(oSQL.server, oSQL.sql, oSQL.args);
		
		var sLast_sync_date = plugins.DateUtils.dateFormat( last_sync_date, 'yyyy-MM-dd HH:mm:ss');
		var parameters = {
			LastModifiedDate: sLast_sync_date,
			IncludeProducts: true,
			org_id:	input.org_id
		}
		
		// will return item_data >= LastModifiedDate
		var response = scopes['avWirelessInventory'].methods['InventoryInfo_ALL_List'](parameters);
		if(	response && 
			response.InventoryInfo_ALL_ListResponse &&
			response.InventoryInfo_ALL_ListResponse.InventoryInfo_ALL_ListResult &&
			response.InventoryInfo_ALL_ListResponse.InventoryInfo_ALL_ListResult.InventoryInfo && 
			response.InventoryInfo_ALL_ListResponse.InventoryInfo_ALL_ListResult.InventoryInfo.length > 0){
				
			var inventoryInfo = response.InventoryInfo_ALL_ListResponse.InventoryInfo_ALL_ListResult.InventoryInfo;
			
			// send data	
			var itemInfo = inventoryInfo.reduce( function (latest, current) {
				var currentDate = new Date(current.LastModifiedDate);
				var latestDate = new Date(latest.LastModifiedDate);
				
				return currentDate > latestDate ? current : latest;
			}, inventoryInfo[0]);
			
			// Create Date object as UTC
			var utcDate = new Date(itemInfo.LastModifiedDate + "Z");
			// Add 1 second do to item_data >= LastModifiedDate
			utcDate.setSeconds(utcDate.getSeconds() + 1);
			
			// Adjust to local time by accounting for timezone offset
			new_last_sync_date = new Date(
			  utcDate.getUTCFullYear(),
			  utcDate.getUTCMonth(),
			  utcDate.getUTCDate(),
			  utcDate.getUTCHours(),
			  utcDate.getUTCMinutes(),
			  utcDate.getUTCSeconds()
			);
					
			var ret = SendDataToInventoryWebhook(input.org_id, 
				inventoryInfo,
				input.url_inventory_webhook );
		} else {		
			// no items with last modified date found
			new_last_sync_date = current_run_datetime;	
		}
	} catch (ex) {
		application.output(ex.message, LOGGINGLEVEL.ERROR);
	} finally {
		// free lock/ finished
		oSQL.sql = "UPDATE tmp_item_inventory_webhook SET is_running = 0, last_sync_date = ? WHERE org_id = ?";
		oSQL.args = [new_last_sync_date, input.org_id];
		plugins.rawSQL.executeSQL(oSQL.server, oSQL.sql, oSQL.args);
	}
}

/**
 * @properties={typeid:24,uuid:"0075ED0F-38FC-4843-AD9D-07F044464664"}
 */
function runInventoryWebhook() {
	
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL1 = { };
	
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.sql = "SELECT enable_inventory_webhook, url_inventory_webhook, org_id, owner_id FROM sys_organization";
	
	/***@type {JSDataSet} */
	var dsOrganizationData;
	dsOrganizationData = globals["avUtilities_sqlDataset"](oSQL);
	
	if (dsOrganizationData && dsOrganizationData.getMaxRowIndex() > 0) {
		
		for (var i = 1; i <= dsOrganizationData.getMaxRowIndex(); i++) {
			
			dsOrganizationData.rowIndex = i;
			var enable_inventory_webhook = dsOrganizationData['enable_inventory_webhook'];
			var url_inventory_webhook = dsOrganizationData['url_inventory_webhook'];
			var org_id = dsOrganizationData['org_id'];
			var owner_id = dsOrganizationData['owner_id'];
			
			if(enable_inventory_webhook === null || enable_inventory_webhook === 0){
				continue;
			}
			if(url_inventory_webhook === null || url_inventory_webhook === ''){
				continue;
			}

			var input = {
				owner_id: owner_id,
				org_id: org_id,
				url_inventory_webhook: url_inventory_webhook,
				enable_inventory_webhook: enable_inventory_webhook
			};
			
			processingInventoryWebhook(input);
		}
	}
}
