/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"910B9653-01AE-40E3-BA0E-8CE201BE3720",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"223C6245-9D94-4059-9CB7-6E42896B226F"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"A7BEF945-2006-4BC5-8E3E-4C799A8D5222"}
 */
function onShowForm(_firstShow, _event)
{

    if (_firstShow) {
    	if (!_gridReady) {
    		application.executeLater(onShowForm, 500, [true, _event]);
    		return null;
    	}
    }

	elements.btnAddOperationsToJob.visible = (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.AddOperationsToProductionJobView) == 1);
    
    checkAndLoadAdditionalOperationsToJobView();
	if(_firstShow) {
		forms._sa_order_est_base.setVL_salesLineItemCodes_all(_firstShow);
		
	    elements.grid.myFoundset.foundset.sort('job_date desc');
	    
	}
	// GD - 2013-06-14: support for foundset being passed in by widget
	if (!_loadFoundset)
	{
		if (_jobStatusFilter == null) _jobStatusFilter = 'Open';
		elements._jobStatusFilter.enabled = true;
		refreshFilter(_firstShow);				
	}
	else
	{
		controller.loadRecords(_loadFoundset);
		_loadFoundset = null;
		_jobStatusFilter = null;
	}
	elements.grid.getColumn(elements.grid.getColumnIndex("order_exp_ship_date")).format= globals.avBase_dateFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("job_completed_date")).format= globals.avBase_dateFormat;
	if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.DueAtCustomerFormatInSalesOrders) == "DATETIME") {
		elements.grid.getColumn(elements.grid.getColumnIndex("ordrevh_promise_date")).format= globals.avBase_dateTimeFormat;
	} 
	else {
		elements.grid.getColumn(elements.grid.getColumnIndex("ordrevh_promise_date")).format= globals.avBase_dateFormat;
	}
	elements.grid.getColumn(elements.grid.getColumnIndex("ordh_order_date")).format= globals.avBase_dateFormat;
	_super.onShowForm(_firstShow,_event);
	
	elements._jobStatusFilter.enabled = true;
}

/**
 * @properties={typeid:35,uuid:"1379BD02-777E-4B17-A552-DE3E7BF7F3E1",variableType:-4}
 */
var _loadFoundset = null;

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"6518E0D5-4CEC-4FCA-A095-F65352B3CA0D",variableType:-4}
 */
var tblFormNames = ['prod_job_view_tbl', 'prod_job_view_sections', 'prod_job_view_detail_task_tbl',
					'sch_job_milestone_tbl', 'prod_job_docs_tbl','prod_job_view_materials', 
					'prod_job_view_outsource_services', 'prod_job_note_tbl', 'prod_job_view_comments_tbl'];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D22145B4-1B4D-4543-B9C6-4199F3F8B9EB"}
 */
var _operation1Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"17E7D194-D46B-4D29-B309-9E73DBE09D0C"}
 */
var _operation2Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"70DCFA6B-D7AC-44D8-87A1-5D1EDE8F3D49"}
 */
var _operation3Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EA3B85CC-8D16-426D-94AA-9F79D18895CC"}
 */
var _operation4Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BC4E31E2-D841-48F3-856A-315963BED209"}
 */
var _operation5Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A1923584-45B7-41A9-B3DF-9CF97E7450BB"}
 */
var _operation6Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F2E7F547-14D5-4B9E-A051-AFD06F2F6A92"}
 */
var _operation7Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"CFF31352-769A-4ED9-B12A-E28825B76796"}
 */
var _operation8Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A405B786-CA5C-4725-AA86-F8310639CE60"}
 */
var _operation9Title = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4A86E7C3-A3C1-4AD2-90E4-0E3408E9CBF8"}
 */
var _operation10Title = '';

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"A057AED3-7CCE-41F2-AC0C-9208FD66C013"}
 */
function onRecordSelection (_event, _form) {
	
	// GD - Nov 9, 2014: SL-3634: Not loading all material records
	if (utils.hasRecords(prod_job_to_sa_order_revision_detail) 
			&& utils.hasRecords(prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item) ) {
		prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revd_item.loadAllRecords();
	}
	
	if (scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabsJobDetails") == 2) {
		scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tabsJobDetails", 1);
	}
	
	forms.prod_job_docs_tbl.refreshUI(); 
	
	forms.prod_job_view_comments_tbl.setJob(job_id);
	
	resetNoteFiltersTab(job_id);
	
	if ( foundset.jobstat_id === globals.$JobStatus_OnHold && globals.avSecurity_checkForUserRight('Production_Released_Order_View', 'btn_edit', globals.avBase_employeeUserID) ) {
		elements.btnReleaseJobHold.visible = true;
		elements.btnJobHold.visible = false;
	} else {
		elements.btnReleaseJobHold.visible = false;
		elements.btnJobHold.visible = true;
	}
	
	// Ensure sections foundset is properly loaded before selection
	if(utils.hasRecords(prod_job_to_sa_order_revision_detail) 
		&& utils.hasRecords(prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted)) {
		// Load all section records
		prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.loadAllRecords();
		// Sort sections
		forms.prod_job_view_sections.foundset.sort('sequence_nr asc');
		// Clear any existing task records first
		forms.prod_job_view_detail_task_tbl.foundset.clear();
		// Set the section selection
		forms.prod_job_view_sections.foundset.setSelectedIndex(1);
		// Trigger the section selection handler
		forms.prod_job_view_sections.onRecordSelection(_event,_form);
	}
	
	forms.prod_job_view_materials.checkItemPickStatus(); // to enable/disable pick btn
		
	forms.sch_job_milestone_tbl.refreshUI();
	
	//show/hide 
	forms.prod_job_view_materials.foundset.loadAllRecords();
	_super.onRecordSelection(_event, _form);
}

/**
 * Reset all the filters inside the notes tab
 * @param jobId
 *
 * @properties={typeid:24,uuid:"748D05E4-4B4F-4CD6-BA14-D460E8492A9F"}
 */
function resetNoteFiltersTab(jobId){
    forms.prod_job_note_tbl.setNoteSource(foundset.getSelectedRecord() ? foundset.getSelectedRecord().cust_id : null, jobId, globals.$NoteObjectRelation_Job, controller.getName());    
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"DF5D05FF-23F2-441A-B03B-8838ED68EA56"}
 */
function onDataChange_jobStatusFilter(oldValue, newValue, event) {
	refreshFilter(true);
	return true
}

/**
 * refreshFilter
 * @param {Boolean} bReApplyFilters
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"A0B0FAD1-9AD7-4F85-8251-172DD762A533"}
 */
function refreshFilter(bReApplyFilters) {
    
    if (bReApplyFilters) {
    	foundset.removeFoundSetFilterParam('JobViewStatus');
        if (_jobStatusFilter != 'All') {
            foundset.addFoundSetFilterParam('jobstat_id', '=', _jobStatusFilter, 'JobViewStatus')
        }

        foundset.addFoundSetFilterParam('jobstat_id', '!=', 'Comboed', 'JobViewGangedStatus');
        foundset.addFoundSetFilterParam('job_is_reservation', '^||=', '0', 'reservations');

        foundset.sort('job_date desc');
        foundset.loadAllRecords();
        elements.grid.myFoundset.foundset.loadRecords(foundset);
        // GD - Nov 6, 2016: SL-9218: They do not want the quicksearch value to be cleared, but rather it to run again
        //		forms.utils_quickSearch._qs_quickSearch = '';
       // quickSearch_onDataChange(null, _qs_quickSearch, null);
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"8BE42678-CFE0-43FD-944E-F53BE1EA9B1C"}
 */
function noSort(event) {
	
}

/**
 * @properties={typeid:24,uuid:"EF40C3B7-2580-41C3-A3D0-283F1F27D4C7"}
 */
function gotoBrowse() {
	_super.gotoBrowse.apply(this, arguments);
	forms.sch_job_milestone_tbl.gotoBrowse()
}

/**
 * @properties={typeid:24,uuid:"423E21A3-F206-4679-B5A0-AAAE1414B028"}
 */
function gotoEdit() {
	_super.gotoEdit.apply(this, arguments);
	forms.sch_job_milestone_tbl.gotoEdit()
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"C30BCA00-D89C-47A1-9871-3C38C54CA28F"}
 */
function onActionReleaseJobHold(event) {
	
	foundset.held_by_id = null;
	foundset.modified_by_id = globals.avBase_employeeUUID;
	foundset.modified_date = new Date();
	
	//keep the previous status to rollback after user releases the job again
	if ( foundset.jobstat_id_before_held !== null && foundset.jobstat_id_before_held !== 'OnHold') {
		foundset.jobstat_id = foundset.jobstat_id_before_held;
	} else {
		foundset.jobstat_id = 'Open';
	}
	
	databaseManager.saveData(foundset);
	
	elements.btnReleaseJobHold.visible = false;
}

/**
 * set job to on hold
 * 
 * <AUTHOR> Keen
 * @since 2015-11-18
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"FD54D92B-34B5-4857-B040-DFAD389F15DD"}
 */
function onActionPutJobHold(event) {
	globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.info'), i18n.getI18NMessage('avanti.dialog.YouMustEnterANote'), i18n.getI18NMessage('avanti.dialog.ok'));
		
	onActionAddNote(event);
				
	foundset.held_by_id = scopes.globals.avBase_employeeUUID;
	foundset.jobstat_id_before_held = foundset.jobstat_id; 
	foundset.jobstat_id = globals.$JobStatus_OnHold
	foundset.modified_by_id = scopes.globals.avBase_employeeUUID;
	foundset.modified_date = new Date();
	databaseManager.saveData(foundset);
	elements.btnJobHold.visible = false;
	elements.btnReleaseJobHold.visible = true;
}

/**
 * display note entry dialog
 * 
 * <AUTHOR> Keen
 * @since 2015-11-18
 * 
 * @param {JSEvent} event the event that triggered the action
 * 
 * @properties={typeid:24,uuid:"7D7AE955-71A8-4EF9-B26F-F2F0F4E540FD"}
 */
function onActionAddNote(event) {
	var bSetBackBrowseMode = false;
	if (globals.nav.mode === "browse") {
		globals.nav.mode = 'add';
		bSetBackBrowseMode = true;
	}

	var oParams = new Object();

	oParams.fields = ['note_object_source_type', 'note_object_source_id', 'note_object_relation_type', 'note_object_relation_id', 'note_creation_empl_id', '_additionalRelation1'];
	oParams.mode = "newcommit";

	/** @type{JSFoundSet<db:/avanti/prod_job>} */
	var fsJob = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job');
	fsJob.loadRecords(foundset.job_id);

	var sCustomerID = fsJob.prod_job_to_sa_customer.cust_id;

	var sNoteSourceType = "Customer";
	var sNoteSourceID = sCustomerID;
	var sNoteRelationType = "Job";
	var sNoteRelationID = foundset.job_id;
	var sAdditionalRelation1 = "";

	oParams.data = [sNoteSourceType, sNoteSourceID, sNoteRelationType, sNoteRelationID, globals.avBase_employeeUUID, sAdditionalRelation1];

	globals.svy_nav_showLookupWindow(event, "note_id", "System_Notes", null, null, oParams);
	if (bSetBackBrowseMode) {
		globals.nav.mode = "browse";
	}
	scopes.globals.svy_nav_dc_setStatus(globals.nav.mode, globals.nav.programContainer);
}

/**
 * @param {JSEvent} [_event] the event that triggered the action
 * @param {String}	[_triggerForm] (svy_nav_fr_buttonbar_browser/svy_nav_fr_buttonbar_viewer)
 * @return  none
 *
 * @properties={typeid:24,uuid:"945B13B7-3529-429D-9875-364D60BF59DA"}
 */
function dc_resetTableViewPersistance(_event, _triggerForm) {
	
	forms["prod_job_view_sections"].controller.recreateUI()
	forms["prod_job_view_detail_task_tbl"].controller.recreateUI()
	forms["sch_job_milestone_tbl"].controller.recreateUI()
	forms["prod_job_docs_tbl"].controller.recreateUI()
	forms["prod_job_view_materials"].controller.recreateUI()
	forms["prod_job_view_outsource_services"].controller.recreateUI()
	forms["prod_job_note_tbl"].controller.recreateUI()
	forms["prod_job_view_comments_tbl"].controller.recreateUI()
	
	_super.dc_resetTableViewPersistance(_event, _triggerForm);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"81857B14-1DE6-4D8F-8322-2F309E136251"}
 */
function onAction_btnAddOperationsToJob(event) {
    var aReturn = new Array();
    var aDisplay = new Array();

    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();
    oSQL.sql = "SELECT opcat_desc+': '+cc_desc, cc_id \
        FROM sys_cost_centre sysm LEFT JOIN sys_operation_category sysc ON sysm.opcat_id = sysc.opcat_id  \
        WHERE " + scopes.avDB.getSafeConditionsClause('sysm') + " AND cc_active = 1 \
        ORDER BY opcat_desc ASC "
    oSQL.args = [];
    oSQL.server = globals.avBase_dbase_avanti_base;
    var dsData = globals.avUtilities_sqlDataset(oSQL);

    if (dsData && dsData.getMaxRowIndex() > 0) {
        for (var i = 1; i <= dsData.getMaxRowIndex(); i++) {
            aDisplay.push(dsData.getValue(i, 1));
            aReturn.push(dsData.getValue(i, 2));
        }
    }
    application.setValueListItems("vl_ProductionMilestones", aDisplay, aReturn);

    globals.DIALOGS.showFormInModalDialog(forms.prod_job_view_dialog, -1, -1, 605, 400, i18n.getI18NMessage('avanti.lbl.productionMilestones'), false, false, "dlgExportShipmentDialog", true);
}

/**
 * @properties={typeid:24,uuid:"8BC98CCD-8308-467A-BFE8-7A415E51424C"}
 */
function checkAndLoadAdditionalOperationsToJobView() {
    if (foundset.getCurrentSort() == 'job_id asc') {
        scopes.globals.sSort_prod_job_view_tbl = 'job_number desc';
    }
    else {
        scopes.globals.sSort_prod_job_view_tbl = foundset.getCurrentSort();
    }
    
    var oSQL = new Object();
    oSQL.sql = "SELECT ms_id1, ms_id2, ms_id3, ms_id4, ms_id5, ms_id6, ms_id7, ms_id8, ms_id9, ms_id10, \
                date_field_type1, date_field_type2, date_field_type3, date_field_type4, date_field_type5, date_field_type6, date_field_type7, \
                date_field_type8, date_field_type9, date_field_type10 \
                FROM prod_job_view_operations  \
                WHERE org_id = ? AND empl_id = ? "
    oSQL.args = [globals.org_id, scopes.globals.avBase_employeeUUID];
    oSQL.server = globals.avBase_dbase_avanti_base;
    var dsData = globals.avUtilities_sqlDataset(oSQL);

    if (dsData && dsData.getMaxRowIndex() > 0) {
        setOpertationValues(dsData.getValue(1, 1), dsData.getValue(1, 11), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation1")), 1);
        setOpertationValues(dsData.getValue(1, 2), dsData.getValue(1, 12), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation2")), 2);
        setOpertationValues(dsData.getValue(1, 3), dsData.getValue(1, 13), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation3")), 3);
        setOpertationValues(dsData.getValue(1, 4), dsData.getValue(1, 14), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation4")), 4);
        setOpertationValues(dsData.getValue(1, 5), dsData.getValue(1, 15), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation5")), 5);
        setOpertationValues(dsData.getValue(1, 6), dsData.getValue(1, 16), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation6")), 6);
        setOpertationValues(dsData.getValue(1, 7), dsData.getValue(1, 17), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation7")), 7);
        setOpertationValues(dsData.getValue(1, 8), dsData.getValue(1, 18), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation8")), 8);
        setOpertationValues(dsData.getValue(1, 9), dsData.getValue(1, 19), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation9")), 9);
        setOpertationValues(dsData.getValue(1, 10), dsData.getValue(1, 20), elements.grid.getColumn(elements.grid.getColumnIndex("additionalOperation10")), 10);
    }

    databaseManager.refreshRecordFromDatabase(foundset, -1);
    if (scopes.globals.sSort_prod_job_view_tbl) {
        foundset.sort(scopes.globals.sSort_prod_job_view_tbl);
    }
}

/**
 * @param sAdditionalOperationID
 * @param sAdditionalOperationDateOption
 * @param additionalOperationElement {RunTimeDataField}
 * @param nIndex
 *
 * @properties={typeid:24,uuid:"BB93F653-7C41-4940-856F-B7CE960F5F49"}
 */
function setOpertationValues(sAdditionalOperationID, sAdditionalOperationDateOption, additionalOperationElement, nIndex) { 
    if (sAdditionalOperationID) {
        additionalOperationElement.visible = true;        
        var sOperationHeaderName = getOperationHeaderName(sAdditionalOperationID);
        additionalOperationElement.headerTitle = sOperationHeaderName;
        if (nIndex >= 1 && nIndex <= 10) {
            forms.prod_job_view_tbl["_operation" + nIndex + "Title"] = sOperationHeaderName;
        }

        if (sAdditionalOperationID && sAdditionalOperationDateOption == 1) {
            additionalOperationElement.tooltip = i18n.getI18NMessage('i18n:avanti.lbl.scheduledDate');
        }
        else {
            additionalOperationElement.tooltip = i18n.getI18NMessage('i18n:avanti.lbl.completedDate');
        }
    }
    else {
        additionalOperationElement.visible = false;
    }
}

/**
 * @param sMS_ID
 *
 * @return
 * @properties={typeid:24,uuid:"6713D921-ECB2-4D12-89CA-C80BAF2C0733"}
 */
function getOperationHeaderName(sMS_ID) {

    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();
    oSQL.sql = "SELECT opcat_desc+': '+cc_desc, cc_id \
        FROM sys_cost_centre sysm LEFT JOIN sys_operation_category sysc ON sysm.opcat_id = sysc.opcat_id  \
        WHERE " + scopes.avDB.getSafeConditionsClause('sysm') + " AND cc_id = ? \
        ORDER BY opcat_desc ASC "
    oSQL.args = [sMS_ID];
    oSQL.server = globals.avBase_dbase_avanti_base;
    var dsData = globals.avUtilities_sqlDataset(oSQL);
    if (dsData && dsData.getMaxRowIndex() > 0) {
        return dsData.getValue(1, 1);
    }
    else {
        return "-";
    }
}

/**
 * Perform sort.
 *
 * @param {String} dataProviderID element data provider
 * @param {Boolean} asc sort ascending [true] or descending [false]
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"58C86B0C-28AE-4A9E-8F72-1AF13A2870A3"}
 */
function onSort(dataProviderID, asc, event) {    
    foundset.sort(dataProviderID + ( asc ? ' asc' : ' desc' ), false);
}
