/**
 * @private 
 * 
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9F0A2442-77B7-479A-841A-48F9A940248F"}
 */
var _sName = "";

/**
 * @private 
 * 
 * @type {String}
 *
 * @properties={typeid:35,uuid:"94D852CC-5476-464B-A235-EB373E581B7F"}
 */
var _sTooltip = "";

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3F98A4BE-5B37-4A79-AE8F-49D07F7EBC93",variableType:8}
 */
var nLastHideTime = 0.0;

/**
 * @properties={typeid:35,uuid:"CC9614F5-BB07-47EF-908F-566AECDB0FF1",variableType:-4}
 */
var nLastShowID = null;

/**
 * @public 
 * 
 * @param {String} sName
 * @param {UUID} uID
 * @param {String} sTooltip
 * @param {Number} [x]
 * @param {Number} [y]
 * @param {Number} [nWidth]
 * @param {Number} [nHeight]
 * @param {Number} [nNumSecondsToShow]
 *
 * @properties={typeid:24,uuid:"A8847018-37EA-41F4-9A81-1BBBE233C981"}
 */
function show(sName, uID, sTooltip, x, y, nWidth, nHeight, nNumSecondsToShow) {
	var nShowTime = scopes.avDate.getDateAsNum(new Date(), null, true);
	
	// if we are trying to show the tooltip again for the same object within a quarter second of hiding it then it is glitching off and on - just return
	if (uID == nLastShowID && nShowTime < nLastHideTime + 0.000000250) {
		return;
	}
	
	_sName = sName;
	nLastShowID = uID; 
	_sTooltip = sTooltip;
	
	if (!x) {
		x = 0;
	}
	if (!y) {
		y = 0;
	}
	if (!nWidth) {
		nWidth = 250;
	}
	if (!nHeight) {
		nHeight = 250;
	}
	
	// default to showing for 3 seconds
	if (!nNumSecondsToShow) {
		nNumSecondsToShow = 3;
	}
	
    if (plugins.scheduler.getCurrentJobNames().indexOf('hideTooltip') == -1){
        plugins.scheduler.addJob('hideTooltip', plugins.DateUtils.add(new Date(), 0, 0, 0, 0, 0, nNumSecondsToShow), hide, nNumSecondsToShow);
    }
	
	globals.DIALOGS.showFormInModalDialog(forms.tooltip, x, y, nWidth, nHeight, "", false, false, sName, false);
}

/**
 * @public 
 * 
 * @properties={typeid:24,uuid:"6B6574EA-61ED-414C-8133-40147F455593"}
 */
function hide() {
	globals.DIALOGS.closeForm(null, _sName);
}
/**
 * @param {JSEvent} event
 *
 * @private
 * @override
 *
 * @properties={typeid:24,uuid:"0C4D03C3-44E2-4C37-9689-E97B72EE039C"}
 */
function onHide(event) {
	nLastHideTime = scopes.avDate.getDateAsNum(new Date(), null, true);
	plugins.scheduler.removeJob('hideTooltip');
	return _super.onHide(event);
}
