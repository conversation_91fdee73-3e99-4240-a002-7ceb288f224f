/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"874FA195-1A83-4275-BD94-1FC5FC418132",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"0F3486CE-D3FE-4254-B3CB-FBE0197CC446"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"BE7606A0-D830-487D-A821-6155E1BB8268"}
 * @override
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
 return _super.onShowForm(firstShow, event);
}
/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"F448E435-5BAB-45F1-B194-BF483C2D4FD2"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "div_selected_for_this_emp_class" && col.styleClass.search(' disabled') == -1) {
		divSelected_onDataChange(oldValue, newValue, event);
	}
	if (col.id == "chkDefault" && col.styleClass.search(' disabled') == -1) {
		defDiv_onDataChange(oldValue, newValue, event);
	}
}

/**
 *
 * @param {Boolean} firstShow
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"D8A3F4C0-1F30-45DA-915E-EEBC831CFDF3"}
 */
function onShow(firstShow, event) {
	// sl-2154 - have to remove div + plant filters so we can see all divs and plants here
	globals.avBase_setTableFilter()
}

/**
*
* @param {JSEvent} event
*
* @properties={typeid:24,uuid:"E9D688C7-B27D-4C5B-9ABC-A3C6301491CB"}
*/
function onHide(event) {
	_super.onHide(event)
	//sl-2154 - restore div and plant filters that were removed in onShowForm()
	globals.avBase_removeTableFilter()
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"1AE42A12-2618-40BE-8F35-06A0895ACA3D"}
 * @AllowToRunInFind
 */
function divSelected_onDataChange(oldValue, newValue, event) {
	/***@type {JSFoundset<db:/avanti/sys_employee_class_div>} */
	var fs_sys_employee_class_div = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_class_div')

	if(fs_sys_employee_class_div.find() || fs_sys_employee_class_div.find()){
		fs_sys_employee_class_div.emplclass_id = globals.avEmpSetup_curEmpClassID
		fs_sys_employee_class_div.div_id = foundset.div_id
		if(fs_sys_employee_class_div.search() > 0){ // found one
			if(!newValue){ // unselect
				fs_sys_employee_class_div.deleteRecord()
			}
		}
		else if(newValue){ // dint find one - and slelect - create a new rec
			fs_sys_employee_class_div.newRecord()
			fs_sys_employee_class_div.emplclass_id = globals.avEmpSetup_curEmpClassID
			fs_sys_employee_class_div.div_id = foundset.div_id
			fs_sys_employee_class_div.all_plants=1
		}
	}

	forms.empl_employee_class_plant_dtl.allPlants_onDataChange(oldValue,newValue,event)
	forms.empl_employee_class_plant_dtl.elements.chkSelectAll.enabled=newValue 
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkPlantSelected")).enabled = newValue? true : false;
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkDefPlant")).enabled = newValue? true : false;

	if(!newValue){
		forms.empl_employee_class_div_dtl.foundset.all_divs = 0 
	}
	
	databaseManager.saveData()
	return true
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"42DB2006-DAB5-4CA7-8F49-44109928D5AA"}
 */
function onRecordSelection(_event, _form) {
	var retval = _super.onRecordSelection(_event, _form)
	globals.avEmpSetup_curDivID = foundset.div_id
	forms.empl_employee_class_plant_dtl.elements.chkSelectAll.enabled = (foundset.div_selected_for_this_emp_class == 1)
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkPlantSelected")).enabled  = (foundset.div_selected_for_this_emp_class == 1)
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkDefPlant")).enabled  = (foundset.div_selected_for_this_emp_class == 1)
	return retval
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0CB26645-4102-40F6-8C50-DD257CD14751"}
 */
function defDiv_onDataChange(oldValue, newValue, event) {
	if (newValue && !foundset.div_selected_for_this_emp_class) {
		divSelected_onDataChange(oldValue, newValue, event);
	}

    if (newValue) {
        clearDefaultFlagForAllDivs();
        clearDefaultFlagForAllPlants();
    }
    else {
    	forms.empl_employee_class_plant_tbl.clearDefaultFlagForAllPlantsThisDiv();
    }
	
	foundset.sys_division_to_sys_employee_class_div$cur_setup_emp.default_div = newValue;	
	databaseManager.saveData(foundset.sys_division_to_sys_employee_class_div$cur_setup_emp);

	return true;
}

/**
 * @properties={typeid:24,uuid:"B6DFEBD6-063F-4BC6-94A0-1EA9938A44BD"}
 */
function clearDefaultFlagForAllDivs(){
    for (var i = 1; i <= foundset.getSize(); i++) {
        var rDiv = foundset.getRecord(i);

		if (utils.hasRecords(rDiv.sys_division_to_sys_employee_class_div$cur_setup_emp) && rDiv.sys_division_to_sys_employee_class_div$cur_setup_emp.default_div) {
			rDiv.sys_division_to_sys_employee_class_div$cur_setup_emp.default_div = 0;
		}
    }
}

/**
 * @properties={typeid:24,uuid:"907AECDC-C6C8-448A-BCCD-6E8F71430AB8"}
 */
function clearDefaultFlagForAllPlants() {
    for (var d = 1; d <= foundset.getSize(); d++) {
        var rDiv = foundset.getRecord(d);
        
    	for (var p = 1; p <= rDiv.sys_division_to_sys_plant.getSize(); p++) {
            var rPlant = rDiv.sys_division_to_sys_plant.getRecord(p);
            
    		if (utils.hasRecords(rPlant.sys_plant_to_sys_employee_class_plant$cur_setup_emp) && rPlant.sys_plant_to_sys_employee_class_plant$cur_setup_emp.emplplant_is_primary) {
            	rPlant.sys_plant_to_sys_employee_class_plant$cur_setup_emp.emplplant_is_primary = 0;
            }
        }
    }
}
