/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EF3B616E-8B55-4D62-BBF8-FF4223DCFE1E"}
 */
var _OrderByColumn = 'D';



/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"822186D4-49E6-41F2-8CA3-3907D6F69034",variableType:4}
 */
var _includeAllItemClass = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BCB931C0-3584-4EF1-A429-5B8AB8B5886C"}
 */
var _toItemClass = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"559E4DCE-649C-435D-BD93-2ACED5FE2BBB"}
 */
var _fromItemClass = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"BA9BB990-499C-486E-BAD9-508C2E3EBD58",variableType:4}
 */
var _includeAllItems = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7D35271C-53E6-407E-9F21-D459E0580159"}
 */
var _toItem = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7BFB0699-65B8-49D1-AC4A-4DB4B4B05D41"}
 */
var _fromItem = null;

/**
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"410E7EC1-B1B1-42BD-A800-A065BF2F031D",variableType:93}
 */
var _fromDate = null;

/**
 * 
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"7F1AB531-D701-4D61-AAE1-7C66343BFCEB",variableType:93}
 */
var _toDate = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B62111B9-76C7-4C88-A271-3EC8AA71C81A",variableType:4}
 */
var _includeAllItemGroups = 1;

/**
 *
 * @properties={typeid:35,uuid:"528ABBD6-DF42-42CC-B3D9-84041442A74D",variableType:-4}
 */
var _fromItemGroup = null;

/**
 *
 * @properties={typeid:35,uuid:"53DF72E8-200E-46E1-A755-E31D3BA6F741",variableType:-4}
 */
var _toItemGroup = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"1A82C7AB-AC0E-4C55-B9A2-30A0A193689F",variableType:-4}
 */
var _selectedItemGroupArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"9EF72059-EAFB-4149-92F7-1E5138EB71D6",variableType:4}
 */
var _includeSelectedItemClassCode = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"70AA1E46-C0E1-40BB-A23D-D501F750C8E6",variableType:4}
 */
var _includeSelectedItem = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D085B117-8E43-457F-ADA5-D3CA1BAC389C",variableType:4}
 */
var _includeSelectedItemGroups = 0;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"1D636ECF-5F97-4B48-B435-A26F16EE7B4A",variableType:8}
 */
var includeSelectedItemClasses = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"614B58CE-26EB-494A-8D97-1BCE0CDB178C",variableType:-4}
 */
var _selectedItemClassArray = new Array();

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"5E4553FF-F7A5-4221-A8AA-BE5DC77F8A77",variableType:8}
 */
var includeSelectedItems = 0;

/**
 *
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"C41EBAAC-3583-4CB2-9F60-C056061E057F",variableType:-4}
 */
var _selectedItemArray = new Array();

/**
 * @properties={typeid:35,uuid:"00CE5A63-4728-463F-802D-4C56DA460951",variableType:-4}
 */
var _bProfileModified = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"93519EDA-655D-4158-AD9F-5DBF1C5A2145"}
 */
var fromPlant = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"37D74315-3E6E-4595-B144-9F740EF9A23B"}
 */
var fromWhse = "";

/**
 * @properties={typeid:35,uuid:"37794224-5E9E-4055-9199-1B876360A57D",variableType:-4}
 */
var _aPlantsReturn = [];

/**
 * @properties={typeid:35,uuid:"6ACBA594-7673-4268-9C46-82163357EAAB",variableType:-4}
 */
var _aPlantsDisplay = [];

/**
 * @properties={typeid:35,uuid:"141C628B-CA66-495B-8A1E-6B271C423AE3",variableType:-4}
 */
var _aDivsReturn = [];

/**
 * @properties={typeid:35,uuid:"EAED2A70-CD75-46C8-A56E-5DC1640263E0",variableType:-4}
 */
var _aDivsDisplay = [];

/**
 * @properties={typeid:35,uuid:"DC131B50-2E58-48AF-A8AB-62F0C73F2B1D",variableType:-4}
 */
var _aWhsesReturn = [];

/**
 * @properties={typeid:35,uuid:"6A409431-2FB7-4E31-993B-ED44FC0228C1",variableType:-4}
 */
var _aWhsesDisplay = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7F353779-C4AF-4BE2-A88A-D9A0CDB8EF78"}
 */
var fromDiv = "";

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"5A395272-2802-44E4-BBD0-C48D9CB13EE9",variableType:8}
 */
var includeAllCustomers = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"CF4B8461-5367-471C-831C-3033F7098D18",variableType:8}
 */
var includeSelectedCustomers = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"CEDCA606-F431-47D4-96A5-CC7A37C555FE"}
 */
var fromCustomer = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"7DE379A8-20C4-4D64-8F42-08B61C8C0E9E"}
 */
var toCustomer = null;

/**
*
* @type {Array}
*
*
* @properties={typeid:35,uuid:"EEC558A4-DB86-42FF-B7CD-B11065313B80",variableType:-4}
*/
var _selectedCustomerArray = new Array();

/**
 * @type {Number}
 *
 *
 *
 * @properties={typeid:35,uuid:"FF73553D-5382-448D-8480-432FDCCDC613",variableType:8}
 */
var _onSelectAllWarehouses = 0;

/**
 * @properties={typeid:35,uuid:"257A43D5-B9FC-4A4E-A28B-4EC51C52D8CC",variableType:-4}
 */
var fromCustomerCode = null;

/**
 * @properties={typeid:35,uuid:"37A9B4B9-5899-4678-B471-D265A5507980",variableType:-4}
 */
var toCustomerCode = null;

/**
 * Standard Method for getting the Parameters from this filter form
 *
 * @returns {{aParamNames: Array, aParamValues: Array, whereClause: String}} Returns an object containing the param names you need and values
 *
 *
 * @properties={typeid:24,uuid:"D6409B1E-B65B-486F-A4FD-DE6D78072D23"}
 */
function getFilterParams()
{
	var oParams = new Object();
	var sPPlants = "";
	var sPWhse = "";
	
	var sWhere = " WHERE i.org_id = '" + globals.org_id + "' ";
	
	sWhere += " AND ttype.intranstype_trans_code IN (\'SFMI\',\'SH\',\'CS\',\'IM\I',\'II\',\'TSMA\',\'FP\') ";
	
	// To load only selected item class types under customers' environmental tab 
	sWhere += " AND ISNULL(env_rpt.is_selected_for_report,0) = 1 ";
	
	//Item Groups
	var sPSelectedItemGroupsSelection ="";
	for (var i = 0; i <= _selectedItemGroupArray.length - 1; i++) {
		if (i != 0) {
			sPSelectedItemGroupsSelection += ',';
		}
		sPSelectedItemGroupsSelection += "'" + _selectedItemGroupArray[i] + "' ";
	}
	
	//Item Class
	var sPSelectedItemClassSelection ="";
	for (var j = 0; j <= _selectedItemClassArray.length - 1; j++) {
		if (j != 0) {
			sPSelectedItemClassSelection += ',';
		}
		sPSelectedItemClassSelection += "'" + _selectedItemClassArray[j] + "' ";
	}
    
	//Item
	var sPSelectedItemSelection ="";
	for (var k = 0; k <= _selectedItemArray.length - 1; k++) {
		if (k != 0) {
			sPSelectedItemSelection += ',';
		}
		sPSelectedItemSelection += "'" + _selectedItemArray[k] + "'";
	}
	
	if (_selectedItemClassArray.length > 0) {
		sWhere += " AND i.itemclass_id IN (" + sPSelectedItemClassSelection + ") ";
	}
	else {
		if (_fromItemClass != null) sWhere += " AND i.itemclass_code >= '" + _fromItemClass + "' ";
		if (_toItemClass != null) sWhere += " AND i.itemclass_code <= '" + _toItemClass + "' ";
	}
	
	if (_selectedItemArray.length > 0) {
		sWhere += " AND i.item_id IN (" + sPSelectedItemSelection + ") ";
	} 
	else {
		if (_fromItem != null) sWhere += " AND i.item_code >= '" + _fromItem + "' ";
		if (_toItem != null) sWhere += " AND i.item_code <= '" + _toItem + "' ";
	}
	
	if (_selectedItemGroupArray.length > 0) {
		sWhere += " AND grp.ingroup_id IN (" + sPSelectedItemGroupsSelection + ") ";
	} 
	else {
		if (_fromItemGroup != null) sWhere += " AND grp.ingroup_code >= '" + _fromItemGroup + "' ";
		if (_toItemGroup != null) sWhere += " AND grp.ingroup_code <= '" + _toItemGroup + "' ";
	}

	//Customer
	var sPSelectedCustomerSelection = "";
	for (var l = 0; l <= _selectedCustomerArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedCustomerSelection += ',';
		}
		sPSelectedCustomerSelection += "'" + _selectedCustomerArray[l] + "'";
	}
	
	if (_selectedCustomerArray.length > 0 && globals.avBase_selectedEnvReportingCustomersViewOption == 0) {
		sWhere += " AND cust.cust_id IN (" + sPSelectedCustomerSelection + ") ";
	}
	else {
		if (fromCustomer != null && fromCustomer != '') sWhere += " AND cust.cust_code >= '" + fromCustomerCode + "' ";
		if (toCustomer != null && toCustomer != '') sWhere += " AND cust.cust_code <= '" + toCustomerCode + "' ";
	}

	var fromDateParam = null;
	var toDateParam = null;
	
	if (_fromDate) {
		fromDateParam = plugins.DateUtils.dateFormat(_fromDate, 'yyyy-MM-dd');
		sWhere += " AND CAST(ihead.itemtransh_date AS DATE) >= '" + fromDateParam + "' " ;
	}
	
	if (_toDate) {
		toDateParam = plugins.DateUtils.dateFormat(_toDate, 'yyyy-MM-dd');
		sWhere += " AND CAST(ihead.itemtransh_date AS DATE) <= '" + toDateParam + "' " ;
	}
	
	if (fromPlant) {
		var aFromPlants = fromPlant.split('\n');
		for (var m = 0; m <= aFromPlants.length - 1; m++) {
			if (m != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + aFromPlants[m] + "'";
		}
	} 
	else {
		for (var n = 0; n <= _aPlantsReturn.length - 1; n++) {
			if (n != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + _aPlantsReturn[n] + "'";
		}		
	}
	
	if (sPPlants) {
		sWhere += " AND plant.plant_id IN (" + sPPlants + ") ";
	}
//issue is in this block - start 
	if (fromWhse) {
		var aFromWhses = fromWhse.split('\n');
		for (var p = 0; p <= aFromWhses.length - 1; p++) {
			if (p != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + aFromWhses[p] + "'";
		}
	} 
	else {
		for (var q = 0; q <= _aWhsesReturn.length - 1; q++) {
			if (q != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + _aWhsesReturn[q] + "'";
		}		
	}
	
	if (sPWhse) {
		sWhere += " AND (wa.whse_id IN (" + sPWhse + ")) ";
	}
//issue is in this block - start 	
	var sOrderBy =  " ORDER BY wa.whse_code ";

	if (_OrderByColumn == 'S'){ 		//Sales Order
		
		sOrderBy +=  " , i.item_code, ISNULL(ISNULL(so.ordh_document_num,jso.ordh_document_num),'') ";
	}
	else if (_OrderByColumn == 'C'){  	//Item Class
		
		sOrderBy +=  " , i.itemclass_code, i.item_code ";
	}
	else if (_OrderByColumn == 'P'){  	//Substrate
		
		sOrderBy +=  " , ISNULL(paper_grade.papergrade_name, ''), i.item_code ";
	}
	else if (_OrderByColumn == 'I'){  	//Ink Type
		
		sOrderBy +=  " , ISNULL(tab_inkType.ink_types, ''), i.item_code ";
	}
	else {								//Default
		sOrderBy +=  " , i.item_code, idet.custproj_desc ";
	}
	
	application.output(sWhere);
	application.output(sOrderBy);
	oParams.whereClause = null;	
	oParams.aParamNames = ["pFromItem","pToItem", "pFromItemClass","pToItemClass","pFromDate","pToDate", "pOrgId", "pWhere",  "pOrderByClause", "pGroupBy"];
	oParams.aParamValues = [_fromItem, _toItem, _fromItemClass,_toItemClass,_fromDate, _toDate, globals.org_id, sWhere, sOrderBy, _OrderByColumn];
	return oParams;
}

/** *
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"FB1971D1-C594-4EE1-9434-8FE185CD3FD0"}
 */
function onShow(firstShow, event) {
	 _super.onShow(firstShow, event)
	 
	 globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	 
	 
	 _includeAllItems = 1;
	 includeSelectedItems = 0;
	 _fromItem = null;
	 _toItem = null;
	 _selectedItemArray = new Array();
	 
	 _includeAllItemClass = 1;
	 includeSelectedItemClasses = 0;
	 _fromItemClass = null;
	 _toItemClass = null;
	 _selectedItemClassArray = new Array();
	 
	 _includeAllItemGroups = 1;
	 _includeSelectedItemGroups = 0;
	 _fromItemGroup = null;
	 _toItemGroup = null;
	 _selectedItemGroupArray = new Array();
	 
	 globals.avBase_selectedEnvReportingCustomersViewOption = 0;
	 includeAllCustomers = 1;
	 includeSelectedCustomers = 0;
	 fromCustomer = null;
	 fromCustomerCode = null;
	 toCustomer = null;
	 toCustomerCode = null;
	 _selectedCustomerArray = new Array();
	 
	 _toDate = application.getServerTimeStamp();
	 _fromDate = globals.avUtilities_dateGetFirstOfMonth(_toDate);
	 elements._fromDate.format = globals.avBase_dateFormat;
	 elements._toDate.format = globals.avBase_dateFormat;
	 
	 
	 _onSelectAllWarehouses = 0;
	 elements.fromWhse.enabled = true;
	 fromWhse = _to_in_warehouse$avbase_employeedefaultwarehouse.whse_code;
	 
	 _OrderByColumn = 'D';
	 
	 refreshUI();
	 buildEnvironmentalRptCustomersVL();
	 load_vl_DivisionshasWarehouses()
	 loadPlantsForDiv('From');
	 setDefaultDivisionFilter();
	 loadWarehousesForPlant('From');
}

/**
 * 
 * @properties={typeid:24,uuid:"20DB5B33-5FD5-474D-A3AC-AFE8116A3815"}
 */
function refreshUI()
{
	if (_includeAllItemClass == 1)
	{
		elements._fromItemClass.editable = false;
		elements._toItemClass.editable = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;
	}
	else
	{
		elements._fromItemClass.editable = true;
		elements._fromItemClass.enabled = true;
		elements._toItemClass.editable = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
	}
	
	
	if (_includeAllItems == 1)
	{
		elements._fromItem.editable = false;
		elements._toItem.editable = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;
	}
	else
	{
		elements._fromItem.editable = true;
		elements._fromItem.enabled = true;
		elements._toItem.editable = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
	}
	
	//Item Group
	if (_includeAllItemGroups == 1 || _includeSelectedItemGroups == 1) {
		elements._fromItemGroup.enabled = false;
		elements._toItemGroup.enabled = false;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;

		if (_includeSelectedItemGroups == 1) {
			elements._selectedItemGroupMessage_label.visible = true;
			_includeAllItemGroups = 0;
		}
	} 
	else {
		elements._fromItemGroup.enabled = true;
		elements._toItemGroup.enabled = true;
		elements.btnLookupItemGroup_From.enabled = false;
		elements.btnLookupItemGroup_To.enabled = false;
		elements._selectedItemGroupMessage_label.visible = true;
		_includeSelectedItemGroups = 0;
		_includeAllItemGroups = 0;
	}
    
	 elements._selectedItemGroupMessage_label.text = 
     (_selectedItemGroupArray && _includeSelectedItemGroups == 1 ?
     "<html><a href='#'>(" + _selectedItemGroupArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemGroupsSelected") +") </a></html>" : null);
     
	//Item Class
	if (_includeAllItemClass == 1 || includeSelectedItemClasses == 1) {
		elements._fromItemClass.enabled = false;
		elements._toItemClass.enabled = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;

		if (includeSelectedItemClasses == 1) {
			elements._selectedItemClassMessage_label.visible = true;
			_includeAllItemClass = 0;
		}
	} 
	else {
		elements._fromItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
		elements._selectedItemClassMessage_label.visible = false;
	}

	elements._selectedItemClassMessage_label.text = (_selectedItemClassArray && includeSelectedItemClasses == 1 ? "<html><a href='#'>(" + _selectedItemClassArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemClassesSelected") + ") </a></html>" : null); 
	
	//Item
	if (_includeAllItems == 1 || includeSelectedItems == 1) {
		elements._fromItem.enabled = false;
		elements._toItem.enabled = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;

		if (includeSelectedItems == 1) {
			elements._selectedItemMessage_label.visible = true;
			_includeAllItems = 0;
		}
	} 
	else {
		elements._fromItem.enabled = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
		elements._selectedItemMessage_label.visible = false;
	}

	elements._selectedItemMessage_label.text = (_selectedItemArray && includeSelectedItems == 1 ? "<html><a href='#'>(" + _selectedItemArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemsSelected") + ") </a></html>" : null);

	var bFlag = false;
	// Customers
    bFlag = (includeAllCustomers == 1 || includeSelectedCustomers == 1 ? false : true);
    elements.fromCustomer.enabled = bFlag;
    elements.toCustomer.enabled = bFlag;
    elements.btnLookupFromCustomer.enabled = bFlag;
    elements.btnLookupToCustomer.enabled = bFlag;
    elements._selectedCustomerMessage_label.visible = !bFlag;
    
    elements._selectedCustomerMessage_label.text =
        (_selectedCustomerArray && includeSelectedCustomers == 1 ?
        "<html><a href='#'>(" + _selectedCustomerArray.length + " " +  i18n.getI18NMessage("avanti.lbl.customersSelected") +") </a></html>": null);

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"4611E3B9-9B6E-45BC-8BF0-6EC7EA9ABEDA"}
 */
function onDataChange_includeAllItemClasses(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItemClass = null;
		_toItemClass = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F40840C8-4E07-4059-BFB6-D58D841681F4"}
 */
function onDataChangeIncludeAllItems(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromItem = null;
		_toItem = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"6B71D2CF-63EC-4AD5-983B-8938DF31E1A1"}
 */
function onDataChange_fromItemClass(oldValue, newValue, event) {
	_toItemClass = _fromItemClass
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3A8170B2-70E1-4162-90C1-F789B066DE91"}
 */
function onDataChange_fromItem(oldValue, newValue, event) {
	_toItem = _fromItem
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"6A381FEE-F25C-465D-9622-675890F09718"}
 */
function onDataChange_toItemClass(oldValue, newValue, event) {
	if(_fromItemClass>_toItemClass)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"EC516848-422A-469F-8292-65CBF5BD22A7"}
 */
function onDataChange_toItem(oldValue, newValue, event) {
	if(_fromItem>_toItem)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"8DF9EA1E-8C9F-4D33-B201-6FD035AD3AEA"}
 */
function afterFromItemClassLookup(record)
{
	if (record) _fromItemClass = record.itemclass_code;
	onDataChange_fromItemClass(null,_fromItemClass,null)
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"70FF0EA1-0711-4D24-B9F3-4FCEBEA86167"}
 */
function afterToItemClassLookup(record)
{
	if (record) _toItemClass = record.itemclass_code;
}

/**
 * From Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"FD0E0775-6EDF-4017-9E4C-F6EB0E36ED43"}
 */
function lookupFilter_ItemFrom(fs) 
{
	fs.addFoundSetFilterParam('item_code','>','^','PlannedPurchasing_FromItemLookupFilter');
	
	return fs;
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"A9FAD04C-9030-4B95-AA02-703ED75B52AA"}
 */
function afterFromItemLookup(record)
{
	if (record) _fromItem = record.item_code;
	onDataChange_fromItem(null,_fromItem,null);
	
}

/**
 * To Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"7E2033AB-2119-48F7-9504-F9800025B860"}
 */
function lookupFilter_ItemTo(fs) 
{
	fs.addFoundSetFilterParam('item_code','>=',_fromItem,'PlannedPurchasing_ToItemLookupFilter');
	
	return fs;
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"5B0AE700-702F-434E-83F4-2ACF08C668D3"}
 */
function afterToItemLookup(record)
{
	if (record) _toItem = record.item_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"98C86EAC-AD0E-4AB4-B015-CB6610AB3958"}
 */
function onDataChange_fromItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;		
	}
	_toItemGroup = _fromItemGroup;
	refreshUI();
	elements._toItemGroup.requestFocus();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 *
 * @properties={typeid:24,uuid:"CD704195-A455-44B0-85E9-513BB5D4BB4D"}
 */
function onDataChange_toItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;
		refreshUI();
	}
	return true;
}

/**
 * 
 * @param aSelectedItemGroups
 *
 *
 * @properties={typeid:24,uuid:"C2FA4059-2896-48A9-9277-66B9DD84559E"}
 */
function setSelectedItemGroups(aSelectedItemGroups) {
	_selectedItemGroupArray = aSelectedItemGroups;
	refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"A4A484F1-5F59-4E6B-885E-B9DA17918424"}
 */
function onAction_selectedItemGroup(event) {
	showSelectedItemGroupDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"DD23EE23-AFCF-4F30-A3C1-79C6D1B5FBD9"}
 */
function showSelectedItemGroupDialog() {
    forms.sysDialog_selected_item_groups._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_groups._sCallBackMethod = "setSelectedItemGroups";
    forms.sysDialog_selected_item_groups._aSelectedItemGroups = _selectedItemGroupArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_groups, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemGroups"), true, false, "sysDialogSelectedItemGroups", true);
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"0155EEFD-493E-4EEF-AB34-552E85390DD1"}
 */
function onIncludeAllItemGroupDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        _includeSelectedItemGroups = 0;
        elements._fromItemGroup.enabled = false;
        elements._toItemGroup.enabled = false;
        elements._selectedItemGroupMessage_label.text = null;
        elements._chkSelectedItemGroups
    }
    else {
    	 _includeSelectedItemGroups = 0;
         elements._fromItemGroup.enabled = true;
         elements._toItemGroup.enabled = true;
         elements._selectedItemGroupMessage_label.text = null;
    }
    
    _fromItemGroup = null;
    _toItemGroup = null;

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"096192C4-4A93-4B75-A2A6-FA7604BF3276"}
 */
function onIncludeSelectedItemGroupToDataChange(oldValue, newValue, event) {
	if (newValue == 1) {
        _includeAllItemGroups = 0;
        _fromItemGroup = null;
        _toItemGroup = null;
        showSelectedItemGroupDialog();
        elements._selectedItemGroupMessage_label.enabled = true;
    }
    else {
        elements._selectedItemGroupMessage_label.text = null;
        _selectedItemGroupArray = [];
        _includeAllItemGroups = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"B5045D61-45AF-484C-9AD2-4A26F3CAE6A7"}
 */
function onIncludeSelectedItemClassDataChange(oldValue, newValue, event) {

    if (newValue == 1) {
        _includeAllItemClass = 0;
        _fromItemClass = null;
        _toItemClass = null;
        showSelectedItemClassDialog();
        elements._selectedItemClassMessage_label.enabled = true;
    }
    else {
        elements._selectedItemClassMessage_label.text = null;
        _selectedItemClassArray = [];
        _includeAllItemClass = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"D0176411-BE0D-4520-A746-94A4CAF61E77"}
 */
function onAction_selectedItemClasses(event) {
    showSelectedItemClassDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"1F4B8DA9-1003-4675-A99C-5BED15A0B394"}
 */
function showSelectedItemClassDialog() {
    forms.sysDialog_selected_item_classes._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_classes._sCallBackMethod = "setSelectedItemClasses";
    forms.sysDialog_selected_item_classes._aSelectedItemClasses = _selectedItemClassArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_classes, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemClasses"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItemClasses
 *
 *
 * @properties={typeid:24,uuid:"6337E478-E51A-41B9-867E-C42482555F43"}
 */
function setSelectedItemClasses(aSelectedItemClasses) {
    _selectedItemClassArray = aSelectedItemClasses;
    refreshUI();
}

// Item

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"44875BAE-CDF6-4004-B6E2-96E59C30E3F1"}
 */
function onIncludeSelectedItemsDataChange(oldValue, newValue, event) {
	
    if (newValue == 1) {
        _includeAllItems = 0;
        _fromItem = null;
        _toItem = null;
        showSelectedItemDialog();
    }
    else {
        elements._selectedItemMessage_label.text = null;
        _selectedItemArray = [];
        _includeAllItems = 1;
    }

    refreshUI();
    return true;
}

/**
 *
 * @properties={typeid:24,uuid:"9C0E6728-69C2-42B1-AEC6-FF4130AC3547"}
 */
function showSelectedItemDialog() {
    forms.sysDialog_selected_items._sCallBackForm = controller.getName();
    forms.sysDialog_selected_items._sCallBackMethod = "setSelectedItems";
    forms.sysDialog_selected_items._aSelectedItems = _selectedItemArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_items, -1, -1, 695, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItems"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItems
 *
 *
 * @properties={typeid:24,uuid:"EABCE0F1-347E-4AD7-903E-70E0DFA4D026"}
 */
function setSelectedItems(aSelectedItems) {
    _selectedItemArray = aSelectedItems;
    refreshUI();
}

/**
 * 
 * @param event
 *
 *
 * @properties={typeid:24,uuid:"1E188311-D76C-4960-8A42-95AC78F29556"}
 */
function onAction_selectedItems(event) {
    showSelectedItemDialog();
}

/**
 * @properties={typeid:24,uuid:"791558F0-4F02-498B-A198-26B3050772CD"}
 */
function load_vl_DivisionshasWarehouses() {

	_aDivsReturn = [];
	_aDivsDisplay = [];
	
	fromDiv = "";

	var iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} ***/
		dsData;

	oSQL.sql = "SELECT DISTINCT CONCAT(div.div_code,': ',div.div_name) AS div , div.div_id \
			    FROM  sys_division AS div \
			    INNER JOIN in_warehouse AS whse ON whse.div_id = div.div_id \
			    WHERE div.org_id = ? \
			    AND div.div_id IN (SELECT div_id FROM sys_employee_div WHERE empl_id = ?) \
			    ORDER BY CONCAT(div.div_code,': ',div.div_name) ASC ";

	oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
	dsData = globals["avUtilities_sqlDataset"](oSQL);

	iMax = dsData.getMaxRowIndex();
	for (var i = 1; i <= iMax; i++) {
		_aDivsDisplay.push(dsData.getValue(i, 1));
		_aDivsReturn.push(dsData.getValue(i, 2));
	}

	application.setValueListItems("vl_DivisionshasWarehouses", _aDivsDisplay, _aDivsReturn);
}

/**
 *
 * @param sDivType
 *
 * @properties={typeid:24,uuid:"5DD88DCF-F0B2-439F-8EF6-C1B766D17EB2"}
 */
function loadPlantsForDiv(sDivType) {

	var aFromDiv = [];
	var sFromDiv = "";

	_aPlantsReturn = [];
	_aPlantsDisplay = [];

	fromPlant = "";
	fromWhse = "";
	
	if (!fromDiv) {
		setDefaultDivisionFilter();
	}
		aFromDiv = fromDiv.split('\n');

	for (var i = 0; i <= aFromDiv.length - 1; i++) {
		if (i != 0) {
			sFromDiv += ',';
		}
		sFromDiv += "'" + aFromDiv[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";

		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";
		
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	}
	iMax = dsData.getMaxRowIndex();
	
	if (iMax == 0) {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
			INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
			WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
			ORDER BY div.div_code ASC, plant.plant_code ASC";
	
			oSQL.args = [globals["org_id"]];
			dsData = globals["avUtilities_sqlDataset"](oSQL);
			iMax = dsData.getMaxRowIndex();
	}
	
	for (i = 1; i <= iMax; i++) {
		_aPlantsDisplay.push(dsData.getValue(i, 1));
		_aPlantsReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromPlant =  dsData.getValue(i, 2);
		}
	}
		
	application.setValueListItems("vl_Plants_rpt_dlg_base_on_div", _aPlantsDisplay, _aPlantsReturn);
}

/**
 * 
 * @param sPlantType
 *
 * @properties={typeid:24,uuid:"17C9F1E6-BB04-40CC-97E3-9C380FE4D33A"}
 */
function loadWarehousesForPlant(sPlantType) {

	var aFromPlant = [];
	var sFromPlant = "";

	_aWhsesReturn = [];
	_aWhsesDisplay = [];

	
	aFromPlant = fromPlant.split('\n');

	for (var i = 0; i <= aFromPlant.length - 1; i++) {
		if (i != 0) {
			sFromPlant += ',';
		}
		sFromPlant += "'" + aFromPlant[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

	oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, div.div_id, plant.plant_id \
				 FROM in_warehouse AS whse \
					INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
					INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
				 WHERE div.org_id = ? \
					AND plant.plant_id IN ( " + sFromPlant + " ) \
				 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
				 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";	
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
			oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, plant.plant_id \
						 FROM in_warehouse AS whse \
							INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
							INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
						 WHERE div.org_id = ? \
							AND plant.plant_id IN ( " + sFromPlant + " ) \
						 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? ) \
						 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
	}
	iMax = dsData.getMaxRowIndex();
	for (i = 1; i <= iMax; i++) {
		_aWhsesDisplay.push(dsData.getValue(i, 1));
		_aWhsesReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromWhse =  dsData.getValue(i, 2);
		}
		else if (i>1 && _onSelectAllWarehouses == 1) {
			fromWhse += "\n";
			fromWhse +=  dsData.getValue(i, 2);
			 
		}
		
	}
		
	application.setValueListItems("vl_Warehouses_rpt_dlg_base_on_plant", _aWhsesDisplay, _aWhsesReturn);
	
}

/**
 * @properties={typeid:24,uuid:"D5E552E6-4A03-427E-BCD3-09BA9500C4AB"}
 */
function setDefaultDivisionFilter() {
    
    elements.fromDiv.readOnly = false;
    
	if (utils.hasRecords(_to_sys_division$org)) {
		var rDivision = _to_sys_division$org.getRecord(1);
		fromDiv = rDivision.div_id;
	}
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"0035FF1D-076A-4B92-A74E-C572649353CA"}
 */
function onDataChange_fromDiv (oldValue, newValue, event) {
	loadPlantsForDiv('From');
	onDataChange_fromPlant (oldValue, newValue, event);

}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"D446837D-12F1-4662-9F03-E7597A3E31EA"}
 */
function onDataChange_fromPlant (oldValue, newValue, event) {
	fromWhse = "";
	loadWarehousesForPlant('From');
}

/**
 * @properties={typeid:24,uuid:"C25B8ADB-FE33-4B5D-9D35-C6B9C9F42439"}
 */
function showSelectedCustomerDialog() {
    forms.sysDialog_selected_environmental_reporting_customers._sCallBackForm = controller.getName();
    forms.sysDialog_selected_environmental_reporting_customers._sCallBackMethod = "setSelectedCustomers";
    forms.sysDialog_selected_environmental_reporting_customers._aSelectedCustomers = _selectedCustomerArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_environmental_reporting_customers, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.selectCustomers"), true, false, "sysDialogSelectedCustomer", true);
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} _fs
 * @return {JSFoundSet<db:/avanti/in_item_supplier>}
 *
 *
 *
 * @properties={typeid:24,uuid:"937E1F53-0C12-4047-8B9B-F6667090B0F7"}
 */
function lookupFilter_Customers(_fs)
{
//	_fs.addFoundSetFilterParam('cust_cust_id','!=',null,'LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"E40543B5-5229-4C7C-B9A0-9258B9CC7D40"}
 */
function afterToCustomerLookup(record) {
	if (record) toCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"D2F9D236-1019-468E-9CDB-28A2186B133C"}
 */
function afterFromCustomerLookup(record) {
	if (record) fromCustomer = record.cust_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"8BF95B68-008F-45D6-A633-3D0D9F175D9A"}
 */
function onFromCustomerDataChange(oldValue, newValue, event) {
	if (!toCustomer) {
		toCustomer = fromCustomer;
	} else if (fromCustomer > toCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"44B296B8-A3D2-421E-8712-E1E245428C6F"}
 */
function onToCustomerDataChange(oldValue, newValue, event) {
	if (fromCustomer > toCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F86F12CC-607D-4A1C-AC95-594ED4847F03"}
 */
function onIncludeAllCustomerDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedCustomers = 0;
        fromCustomer = null;
        toCustomer = null;
        elements._selectedCustomerMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"166094FB-7E8B-4247-B1F3-566DC01542A3"}
 */
function onIncludeSelectedCustomerDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedEnvReportingCustomersViewOption = 0;
	
    if (newValue == 1) {
        includeAllCustomers = 0;
        fromCustomer = null;
        toCustomer = null;
        showSelectedCustomerDialog();
    }
    else if (newValue == 0) {
    	includeAllCustomers = 1;
        fromCustomer = null;
        toCustomer = null;
        _selectedCustomerArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedCustomers
 * public
 *
 * @properties={typeid:24,uuid:"D4F7CBBF-BB96-47C2-B764-93C0FF89B723"}
 */
function setSelectedCustomers(aSelectedCustomers) {
    _selectedCustomerArray = aSelectedCustomers;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4478A9A6-D04D-4E56-9324-A2EFB7C8D80B"}
 */
function onAction_selectedCustomer(event) {
    showSelectedCustomerDialog();
}

/**
 * 
 * @param event
 *
 * 
 *
 * @properties={typeid:24,uuid:"67684833-9D5E-4934-B141-429792156A24"}
 */
function onAction_showToSelectedCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomersViewOption = 2;
	showSelectedCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedCustomerArray[0]], null, true);
	toCustomer = rCustomer.cust_id;
	toCustomerCode = rCustomer.cust_code;
	
	if (toCustomerCode < fromCustomerCode) {
		 toCustomer = null;
		 toCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D8E2F143-CDD6-4714-B448-62FDC8F5488D"}
 */
function onAction_SelectAllWarehouses(event) {
	if (_onSelectAllWarehouses == 1) {
		elements.fromWhse.enabled = false;
  	    loadWarehousesForPlant('From');
	}
	else {
		 elements.fromWhse.enabled = true;
		 loadWarehousesForPlant('From');
	}
}


/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"66C42177-7FFE-4D4E-B014-63073D70700C"}
 */
function onShowFromCustomerLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'cust_code', 'Customers', 'afterFromCustomerLookup', 'lookupFilter_Customer', { mode: "rptlookup" });
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"D295D84E-70D4-4BB4-8C3A-C1C003C64EF4"}
 */
function onShowToCustomerLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'cust_code', 'Customers', 'afterToCustomerLookup', 'lookupFilter_Customer', { mode: "rptlookup" });
}



/**
 *
 *
 * @properties={typeid:24,uuid:"1E9C56FF-E288-4329-BDB4-A6C6C3651375"}
 */
function buildEnvironmentalRptCustomersVL()
{
    var aReturn = new Array();
    var aDisplay = new Array();
    
    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();
    // CONCAT(cust.cust_code,' - ',cust.cust_name)
    oSQL.sql = "SELECT DISTINCT cust.cust_code, cust.cust_id \
                FROM sa_customer AS cust \
                INNER JOIN sa_customer_environmental_reporting AS envrpt \
					ON cust.cust_id = envrpt.cust_id \
					AND envrpt.is_selected_for_report = 1 \
				WHERE cust.org_id =   '" + globals.org_id + "' AND cust.cust_status = 'A' \
				ORDER BY cust.cust_code ";
    oSQL.args = [];
    oSQL.server = globals.avBase_dbase_avanti_base;
    var dsData = globals.avUtilities_sqlDataset(oSQL);
    
   //aDisplay.push('All');
   //aReturn.push('All'); 
   
    if (dsData && dsData.getMaxRowIndex() > 0)
    {
        for (var i = 1; i <= dsData.getMaxRowIndex(); i++)
        {
            aDisplay.push(dsData.getValue(i,1));
            aReturn.push(dsData.getValue(i,2));
        }
    }
   
    application.setValueListItems("vl_buildEnvironmentalRptCustomers", aDisplay, aReturn);
}



/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"C55EC07E-1F86-499D-89F9-7C6D5A88700E"}
 */
function onAction_showFromSelectedEnvReportingCustomerDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedEnvReportingCustomersViewOption = 2;
	showSelectedEnvReportingCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedCustomerArray[0]], null, true);
	fromCustomer = rCustomer.cust_id;
	fromCustomerCode = rCustomer.cust_code;
	
	if (fromCustomerCode > toCustomerCode) {
		fromCustomer = null;
		fromCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 *
 * @param event
 *
 * @properties={typeid:24,uuid:"C4DEE5B0-F113-4C08-BD8E-C39AFA8DF32D"}
 */
function onAction_showToSelectedEnvReportingCustomerDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedEnvReportingCustomersViewOption = 2;
	showSelectedEnvReportingCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedCustomerArray[0]], null, true);
	toCustomer = rCustomer.cust_id;
	toCustomerCode = rCustomer.cust_code;
	
	if (toCustomerCode < fromCustomerCode) {
		 toCustomer = null;
		 toCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}



/**
 * @properties={typeid:24,uuid:"AD79CF00-9DE8-49BF-ACD5-5C5EDB3B4624"}
 */
function showSelectedEnvReportingCustomerDialog() {
    forms.sysDialog_selected_environmental_reporting_customers._sCallBackForm = controller.getName();
    forms.sysDialog_selected_environmental_reporting_customers._sCallBackMethod = "setSelectedCustomers";
    forms.sysDialog_selected_environmental_reporting_customers._aSelectedCustomers = _selectedCustomerArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_environmental_reporting_customers, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedCustomers"), true, false, "sysDialogEnvironmentalReportingCustomer", true);
}