/**
 * @properties={typeid:24,uuid:"C2991585-54D0-497D-A92F-48A75DB17318"}
 */
function onChange_split_udf(previousIndex, event) {
    var dividerLocation = elements.split_udf.getDividerLocation();
    scopes.avUtils.setUserPref("ap_suppliers_udf_dtl_split_udf_divider", dividerLocation.toString());
}
/**
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"F3E46A71-E167-4ED4-9529-99C2C60CA5BE"}
 */
function onLoad(event) {
    elements.split_udf.setLeftForm(forms.sys_udf_tree);
    elements.split_udf.setRightForm(forms.sys_udf_tree_answers);
	return _super.onLoad(event);
}

/** *

    // Restore divider location for split_udf
    var sPrefType = "ap_suppliers_udf_dtl_split_udf_divider";
    var savedLocation = scopes.avUtils.getUserPref(sPrefType);
    if (savedLocation) {
        elements.split_udf.setDividerLocation(parseInt(savedLocation));
    }

 * @param _firstShow
 * @param _event
 *
 * @return
 * @properties={typeid:24,uuid:"7BDA4A37-F2D6-499F-A3B1-B0F350160A5D"}
 */
function onShowForm(_firstShow, _event) {
	forms.sys_udf_tree.formUDFCode = 'SUPPLIER';
	forms.sys_udf_tree.formSource = 'Supplier';
	forms.sys_udf_tree.loadTree();
	
	forms.sys_udf_tree_answers.refreshFieldValues();
	return _super.onShowForm(_firstShow, _event);
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"05061915-68C3-4E20-81B5-23FC8662F9BE"}
 */
var selectedUUID = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"AFA812D4-0576-4028-AC0E-8DEFBEF10D92",variableType:-4}
 */
var selectedPath = new Array();

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8537F3A9-20CC-4AB7-9FF2-78F1F51DCBE3"}
 */
var searchField = '';
