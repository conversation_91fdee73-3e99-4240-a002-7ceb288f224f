/**
 * <PERSON><PERSON> changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"68BC5D73-8342-4132-AFF4-EA15D609861A"}
 * @AllowToRunInFind
 */
function allDivs_onDataChange(oldValue, newValue, event) {
	/***@type {JSFoundset<db:/avanti/sys_division>} */
	var fs_sys_division = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_division')
	fs_sys_division.loadAllRecords()
	var iNumDivs = fs_sys_division.getSize()
	var sDivIDBak = globals.avEmpSetup_curDivID
	
	if(iNumDivs > 0){
		for(var i=1;i<=iNumDivs;i++){
			var rec_sys_division = fs_sys_division.getRecord(i)
			
			if(utils.hasRecords(rec_sys_division, 'sys_division_to_sys_employee_class_div$cur_setup_emp')){ 
				if(!newValue){ // unselect all + there's a rec - delete it
					rec_sys_division.sys_division_to_sys_employee_class_div$cur_setup_emp.deleteRecord()
				}
			}
			else if(newValue){ // select all + there's no rec - create it
				rec_sys_division.sys_division_to_sys_employee_class_div$cur_setup_emp.newRecord()
				rec_sys_division.sys_division_to_sys_employee_class_div$cur_setup_emp.emplclass_id = globals.avEmpSetup_curEmpClassID
				rec_sys_division.sys_division_to_sys_employee_class_div$cur_setup_emp.div_id = rec_sys_division.div_id
				rec_sys_division.sys_division_to_sys_employee_class_div$cur_setup_emp.all_plants=1
			}
			
			globals.avEmpSetup_curDivID = rec_sys_division.div_id 
			forms.empl_employee_class_plant_dtl.allPlants_onDataChange(oldValue,newValue,event)
		}
	}
	
	forms.empl_employee_class_plant_dtl.elements.chkSelectAll.enabled=newValue 
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkPlantSelected")).enabled = newValue? true : false;
	forms.empl_employee_class_plant_tbl.elements.grid.getColumn(forms.empl_employee_class_plant_tbl.elements.grid.getColumnIndex("chkPlantSelected")).enabled = newValue? true : false;
	globals.avEmpSetup_curDivID=sDivIDBak
	databaseManager.saveData()
	return true
}
