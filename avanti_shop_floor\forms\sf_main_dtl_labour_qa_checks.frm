customProperties:"formComponent:false,\
methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sch_milestone_group",
encapsulation:108,
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
cssPosition:"28,347,-1,236,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"236",
right:"347",
top:"28",
width:"200"
},
dataProviderID:"qa_check_pos_2",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:6,
visible:true
},
name:"qa_check_field_2",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"058A0958-97B5-4886-A3A6-9DD6D773889F"
},
{
cssPosition:"3,591,-1,8,187,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"8",
right:"591",
top:"3",
width:"187"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_desc",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"cost_centre",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"1E11F28D-FF91-4D37-AD4D-064D36D18888"
},
{
height:80,
partType:5,
typeid:19,
uuid:"1EE78573-40E4-49B4-A618-7ACE3C21E64E"
},
{
cssPosition:"3,347,-1,236,199,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"236",
right:"347",
top:"3",
width:"199"
},
dataProviderID:"qa_check_pos_1",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:5,
visible:true
},
name:"qa_check_field_1",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"49F67367-3433-4702-9EAA-1761E2737D62"
},
{
cssPosition:"28,547,-1,209,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"209",
right:"547",
top:"28",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_2",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"64AB3586-9059-4D8F-B67D-6730E473AA31"
},
{
cssPosition:"101,-1,9,563,80,22",
json:{
cssPosition:{
bottom:"9",
height:"22",
left:"563",
right:"-1",
top:"101",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"F187392D-215F-49F6-8CC5-DF369EEF46F9",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_77401B9F",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"733B0E37-31A0-40A3-A912-C9F53A563343"
},
{
cssPosition:"53,547,-1,209,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"209",
right:"547",
top:"53",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
enabled:true,
styleClass:"text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_3",
styleClass:"text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"79B3D8D0-A39B-4D46-ABC4-B65921ADCEF1"
},
{
cssPosition:"101,-1,8,648,80,22",
json:{
cssPosition:{
bottom:"8",
height:"22",
left:"648",
right:"-1",
top:"101",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"D794F2AB-7DCA-4A96-8EAF-08F09472D80E",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_B3929A10",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"82A0D5D2-3584-4578-805A-7E682F825926"
},
{
cssPosition:"3,547,-1,209,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"209",
right:"547",
top:"3",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_1",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"83665FD3-6A3D-49A1-B0EE-E28270926288"
},
{
height:135,
partType:8,
typeid:19,
uuid:"969C4169-0DC4-4CDB-BBD8-2C3DEB6E6E48"
},
{
cssPosition:"53,347,-1,236,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"236",
right:"347",
top:"53",
width:"200"
},
dataProviderID:"qa_check_pos_3",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:7,
visible:true
},
name:"qa_check_field_3",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F326F6A6-2B5D-4BE5-B66F-532162A76F26"
}
],
name:"sf_main_dtl_labour_qa_checks",
navigatorID:"-1",
onShowMethodID:"AF94E4ED-8BE2-47FA-8B78-63E70B325D1C",
scrollbars:36,
showInMenu:true,
size:"760,480",
typeid:3,
uuid:"CB38D856-ECD2-4422-B4E9-106702FB7801"