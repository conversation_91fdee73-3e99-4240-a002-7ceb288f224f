/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 * @global {JSFoundset<db:/avanti/in_item_warehouse_count>} foundset
 *
 * @properties={typeid:24,uuid:"E15030F2-EBF3-44EB-B92D-87878720B60E"}
 */
function onShowForm(firstShow, event) {
	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
	if (foundset.in_itemwhsecount_countstatus == 'U') {
		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
	}
	else {
		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
	}
	elements.fld_lastused.format = globals.avBase_dateFormatWithTime;
	if(in_itemwhscount_counttype == 'C') {
		forms.in_item_warehouse_count_items_tbl.showOnlyIncluded();
	}
	refreshUI();
	_super.onShowForm(firstShow, event);
	return;
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"D7AFE946-3093-4F9D-9FC8-1D71E161A7E6"}
 */
function onRecordSelection(event, _form) {
	if(in_itemwhscount_counttype == 'C') {
		forms.in_item_warehouse_count_items_tbl.showOnlyIncluded();
	}
	refreshUI();
	return _super.onRecordSelection(event, _form);
}

/**
 * Refresh the current display.
 * 
 * @properties={typeid:24,uuid:"6484B45A-E405-45F7-8786-1B2CD0F43D49"}
 */
function refreshUI() {
	
	var bHasRecords = false;
	var bVarianceHasBeenSeen = false;
	var sCurrentStatus = "";
	var rInItemWarehouseCount = foundset.getSelectedRecord();
	
	bHasRecords = forms.in_item_warehouse_count_dtl.hasAtLeastOneRow();
	
	if (bHasRecords && rInItemWarehouseCount && rInItemWarehouseCount.in_itemwhsecount_varianceseen) {
		bVarianceHasBeenSeen = true;
	}
	
	if(rInItemWarehouseCount) {
		sCurrentStatus = rInItemWarehouseCount.in_itemwhsecount_countstatus;
		forms.in_item_warehouse_count_dtl.updateUI(sCurrentStatus,bHasRecords,bVarianceHasBeenSeen);
	}
	
	return;
}

/**
 * Enable/disable UI elements based on state of currently 
 * selected record.
 * 
 * @param {String} sCurrentStatus - status of count record (Open|Started|Exported)
 * @param {Boolean} bHasRecords - are there any items in the countitems table?
 * @param {Boolean} bVarianceHasBeenSeen - has the variance been inspected by the user?
 *
 * @properties={typeid:24,uuid:"E767F83C-6201-4AA1-996C-57A5AE717FE6"}
 */
function updateUI(sCurrentStatus,bHasRecords,bVarianceHasBeenSeen) {
	
	switch (sCurrentStatus) {
		
		case 'O': // (O)pen
		
			forms.in_item_warehouse_count_dtl.elements.btnStartCount.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnResetCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnPrintCountSheets.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnExportCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnEnterCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnVarianceReport.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnUpdateCount.enabled = false;
			
			forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_countcode.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_desc.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_type.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_cycleoption.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_numitems.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_batchsize.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.fld_employee.enabled = true;
			
			break;
			
		case 'S': case 'X': // (S)tarted | e(X)ported
		
			forms.in_item_warehouse_count_dtl.elements.btnStartCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnResetCount.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnPrintCountSheets.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnExportCount.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnEnterCount.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnVarianceReport.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnUpdateCount.enabled = false;			
			
			forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_countcode.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_desc.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_type.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_cycleoption.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_numitems.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_batchsize.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_employee.enabled = false

			break;
			
		case 'E': // (E)ntered
		
			forms.in_item_warehouse_count_dtl.elements.btnStartCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnResetCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnPrintCountSheets.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnExportCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnEnterCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnVarianceReport.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnUpdateCount.enabled = false;			
			
			forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_countcode.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_desc.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_type.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_cycleoption.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_numitems.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_batchsize.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_employee.enabled = false
			break;
			
		case 'C': // (C)onfirmed
		
			forms.in_item_warehouse_count_dtl.elements.btnStartCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnResetCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnPrintCountSheets.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnExportCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnEnterCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnVarianceReport.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnUpdateCount.enabled = true;			
			
			forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_countcode.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_desc.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_type.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_cycleoption.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_numitems.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_batchsize.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_employee.enabled = false
			
			break;
			
		case 'U': // (U)pdated
		
			forms.in_item_warehouse_count_dtl.elements.btnStartCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnResetCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnPrintCountSheets.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnExportCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnEnterCount.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.btnVarianceReport.enabled = true;
			forms.in_item_warehouse_count_dtl.elements.btnUpdateCount.enabled = false;			
			
			forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_countcode.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_desc.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_type.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_cycleoption.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_numitems.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_batchsize.enabled = false;
			forms.in_item_warehouse_count_dtl.elements.fld_employee.enabled = false
			
			break;
			
		default:
			return; // occurs during initialization
		
	}
	if(globals.nav.mode == 'add' || globals.nav.mode == 'edit') {
		globals.svy_nav_setFieldsColor('in_item_warehouse_count_dtl', 'edit');
	}
	
	if ('C' == in_itemwhscount_counttype) {
		elements.fld_cycleoption.visible = true;
		elements.fld_numitems.visible = true;
	}
	else {
		elements.fld_cycleoption.visible = false;
		elements.fld_numitems.visible = false;
	}

	return;
}

/**
 * SetNoteSource
 * 
 * @param sInItemWarehouseCountItemsId
 *
 * @properties={typeid:24,uuid:"A114FB44-9EFA-4CDF-AFA9-BDDC99A9BAE0"}
 */
function setNoteSource(sInItemWarehouseCountItemsId) {
    forms.sys_note_tbl.setNoteSource(sInItemWarehouseCountItemsId, sInItemWarehouseCountItemsId, globals.$NoteObjectRelation_CountItem);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * 
 * @global {JSFoundSet<in_item_warehouse_count>} foundset
 *
 * @properties={typeid:24,uuid:"0695A71F-41AB-4753-A72F-B9F90F4265F1"}
 */
function onAction_btnStartCount(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly()) {
		if (!in_itemwhscount_countcode) {
			scopes.avText.pleaseEnterAValueFor("i18n:avanti.lbl.CountCode");
			return;
		}
		else if (!in_itemwhsecount_desc) {
			scopes.avText.pleaseEnterAValueFor("i18n:avanti.lbl.description");
			return;
		}
		
		var sAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.areYouSureYouWantToStartTheCount'), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
		
		if (sAnswer != i18n.getI18NMessage('avanti.dialog.yes')) {
			return;
		}
		
		var rInItemWarehouseCount = forms.in_item_warehouse_count_dtl.foundset.getSelectedRecord();
		forms.in_item_warehouse_count_dtl.reLineNumberItems(); // renumbers the linenumbers, they are now fixed for this count		
		forms.in_item_warehouse_count_dtl.handler_StartCount(rInItemWarehouseCount);
		refreshUI();
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return;	
}

/**
 * Populate the in_item_warehouse_count record with default values. 
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse_count>} rInItemWarehouseCount
 *
 * @properties={typeid:24,uuid:"D339EAD6-49A5-44C6-B9A6-FBFB1835FCDB"}
 */
function createCountRecord(rInItemWarehouseCount) {
	
	var dNow = application.getServerTimeStamp();

	if (!rInItemWarehouseCount) {
		application.output('in_item_warehouse_count_dtl.js:createCountRecord():ERROR:rInItemWarehouseCount is null/undefined.',LOGGINGLEVEL.ERROR);
		throw new Error('rInItemWarehouseCount is null/undefined.');
	}
	
	rInItemWarehouseCount.in_itemwhscount_counttype = 'P'; // Default := 'Physical'
	rInItemWarehouseCount.in_itemwhsecount_countstatus = 'O'; // Default := 'Open'		
	rInItemWarehouseCount.in_itemwhsecount_date = dNow;
	rInItemWarehouseCount.in_itemwhsecount_lastused = dNow;
	
	return;	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @global {JSFoundSet<in_item_warehouse_count_tbl>} foundset
 *
 * @properties={typeid:24,uuid:"28706CB2-A2BE-4232-9F79-C478C0032A9B"}
 */
function onAction_btnResetCount(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly()) {
		
		var sAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.areYouSureYouWantToResetTheCount'), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
		
		if (sAnswer != i18n.getI18NMessage('avanti.dialog.yes')) {
			return;
		}
		
		var rInItemWarehouseCount = forms.in_item_warehouse_count_dtl.foundset.getSelectedRecord();
		forms.in_item_warehouse_count_dtl.handler_ResetCount(rInItemWarehouseCount);
		refreshUI();
		
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return;	
}

/**
 * Changes the Count Status to "Open"
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse_count>} rInItemWarehouseCount
 * 
 * @properties={typeid:24,uuid:"ABBF0CB5-8179-4A04-8D5B-69B1A13C93B1"}
 */
function handler_ResetCount(rInItemWarehouseCount) {
	rInItemWarehouseCount.in_itemwhsecount_countstatus = 'O'; // Reset Status to "Open"
	return;
}

/**
 * Changes the Count Status to "Started"
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse_count>} rInItemWarehouseCount
 *
 * @properties={typeid:24,uuid:"977F559E-2975-4497-8CAC-DB1AC08D4042"}
 */
function handler_StartCount(rInItemWarehouseCount) {
	rInItemWarehouseCount.in_itemwhsecount_countstatus = 'S'; // Change Status to "Started"
	return;
}

/**
 * Changes the Count Status to "Started"
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse_count>} rInItemWarehouseCount
 *
 * @properties={typeid:24,uuid:"F838DF03-2ACF-48EE-9023-1B5CF11BDA9E"}
 */
function handler_Exported(rInItemWarehouseCount) {
	rInItemWarehouseCount.in_itemwhsecount_countstatus = 'X'; // Change Status to "Exported"
	databaseManager.saveData(rInItemWarehouseCount);
	return;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A473716E-99E6-4E86-BC94-E3D5D655B3FA"}
 */
function onAction_btnPrintCountSheets(event) {
	//globals.DIALOGS.showFormInModalDialog(forms.in_item_warehouse_count_print_count_sheets_dlg, -1, -1, 515, 110, i18n.getI18NMessage('avanti.lbl.printCountSheets'), false, false, "dlgPrintCountSheets", true);
	
	globals.avRpt_selectedReportName = "Inventory Count Sheet"
	if (utils.hasRecords(_to_rpt_report$avrpt_selectedreportname))
	{
		globals.avRpt_runReport(globals.UUIDtoString(_to_rpt_report$avrpt_selectedreportname.rpt_id))
	}
	
	return;	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @global {JSFoundSet<in_item_warehouse_count_tbl>} foundset
 *
 * @properties={typeid:24,uuid:"97BEAF15-17EF-4BF9-82E2-7A428C3213C3"}
 */
function onAction_btnExportCount(event) {
	
	var sAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.areYouSureYouWantToExportTheCount'), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
	
	if (sAnswer != i18n.getI18NMessage('avanti.dialog.yes')) {
		return;
	}
	
	var rInItemWarehouseCount = forms.in_item_warehouse_count_dtl.foundset.getSelectedRecord();
	
	if (true == forms.in_item_warehouse_count_dtl.doExportCount(rInItemWarehouseCount)) {
		forms.in_item_warehouse_count_dtl.handler_Exported(rInItemWarehouseCount);
		refreshUI();
	}
	else {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.csvExportFailed"), i18n.getI18NMessage("avanti.dialog.ok"));
	}
	
	return;	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"7CCAC681-4F4D-4714-92F9-20936A4F040E"}
 */
function onAction_btnEnterCount(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly()) {
		globals.DIALOGS.showFormInModalDialog(forms.in_item_warehouse_count_entry, -1, -1, 1325, 800, i18n.getI18NMessage('avanti.lbl.countEntry'), true, false, "dlgCountEntry", true);
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"ED87E28C-D507-4108-9F11-2E19ED6637CD"}
 */
function onAction_btnVarianceReport(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly() || foundset.in_itemwhsecount_countstatus == 'U') {
		forms.in_item_warehouse_count_variance_tbl.foundset.loadRecords(forms.in_item_warehouse_count_items_tbl.foundset.duplicateFoundSet());
		globals.DIALOGS.showFormInModalDialog(forms.in_item_warehouse_count_variance_report_dlg, -1, -1, 1670, 800, i18n.getI18NMessage('avanti.lbl.countVarianceReport'), false, false, "dlgCountVarianceReport", true);
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"C7FDE3D1-31EC-4AB9-90B6-9E953BFA0882"}
 */
function onAction_btnUpdateCount(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly()) {
		globals.DIALOGS.showFormInModalDialog(forms.in_item_warehouse_count_update_dlg, -1, -1, 515, 250, i18n.getI18NMessage('avanti.lbl.InventoryCountUpdate'), false, false, "dlgUpdateCount", true);
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return;	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"209AE663-4B41-4679-A560-ED1792B1CCE6"}
 * 
 * @return {Boolean} - true if successful, false otherwise
 */
function onAction_btnDeleteRow(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly()) {
		
		//** @type {JSFoundset<db:/avanti/in_item_warehouse_count>} */
		var fsPreviouslyLoadedItems = forms.in_item_warehouse_count_items_tbl.foundset;
		var iSelectedIndex = fsPreviouslyLoadedItems.getSelectedIndex();
		
		if (!fsPreviouslyLoadedItems.deleteRecord(iSelectedIndex)) {
			 globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.deleteItem"), i18n.getI18NMessage("avanti.lbl.rowCouldNotBeDeleted"), i18n.getI18NMessage("avanti.dialog.ok"));
			return false;
		}
		else {
			databaseManager.saveData(fsPreviouslyLoadedItems);
			refreshUI();		
		}
		
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"08EE0395-AB35-4218-A303-C2528B859658"}
 */
function onAction_btnAddRows(event) {
	if (!scopes.avUtils.isNavModeReadOnly()) {
		if(in_itemwhscount_counttype == 'C' && (!in_itemwhscount_cycleoption || !in_itemwhscount_numitems)) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.fillInCycleInfo_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
			return;
		}
		globals.DIALOGS.showFormInModalDialog(forms.in_item_warehouse_count_dialog, -1, -1, -1, -1, i18n.getI18NMessage('avanti.lbl.AddToCount'), false, false, "dlgAddToCount", true);
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	return;
}

/**
 * @properties={typeid:24,uuid:"24055213-D2E7-493E-A482-2A386519B0E6"}
 */
function callback_onAction_BtnAddRows() {

	var fsInItemCountItems = forms.in_item_warehouse_count_items_tbl.foundset;
	databaseManager.saveData(fsInItemCountItems);
	fsInItemCountItems.sort('in_item_warehouse_count_items_to_in_warehouse.whse_desc, \
							 in_item_warehouse_count_items_to_in_item_warehouse_location.whseloc_bin_location, \
							 in_item_warehouse_count_items_to_in_item.item_code, \
							 in_item_warehouse_count_items_to_in_item_project.custproj_desc, \
							 in_item_warehouse_count_items_to_in_item_roll.initemroll_roll_number');	
	forms.in_item_warehouse_count_dtl.reLineNumberItems();
	refreshUI();
	return;
}

/**
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 * 
 * @global {JSFoundSet<in_item_warehouse_count_tbl>} forms.in_item_warehouse_count_tbl.foundset
 * @properties={typeid:24,uuid:"8B017747-36D9-4DC8-A9AB-419A50B50203"}
 * 
 * @returns {Boolean} - always returns true
 */
function onDataChange_CountType(oldValue, newValue, event) {
	
	if ('C' != newValue) {
		in_itemwhscount_cycleoption = null;
		in_itemwhscount_numitems = null;
		forms.in_item_warehouse_count_items_tbl.updateCount();		
	} 
	refreshUI();
	return true;
}

/**
 * Renumbers the line Items whenever the count items change. 
 * 
 * Note: Once the count has been started, the line numbers should never change.
 * 
 * @properties={typeid:24,uuid:"BBC2EBB7-C0F3-4928-8637-64F2A9ED163F"}
 */
function reLineNumberItems() {
	var bProjectInventory = scopes.avInv.isProjectInventoryOn();
	var rInItemWarehouseCount = foundset.getSelectedRecord();
	
	if (!rInItemWarehouseCount) {
		return; // no items to renumber
	}
	
	if (null != rInItemWarehouseCount.in_itemwhsecount_countstatus && 'O' != rInItemWarehouseCount.in_itemwhsecount_countstatus) {
		throw new Error('in_item_warehouse_count_dtl.js:reLineNumberItems():ERROR:This function should only be called for a count with a status of (O)pen.');
	}
	
	var fsInItemCountItems = forms.in_item_warehouse_count_items_tbl.foundset;
	
	databaseManager.saveData(fsInItemCountItems);
	fsInItemCountItems.sort('in_item_warehouse_count_items_to_in_warehouse.whse_desc, \
							 in_item_warehouse_count_items_to_in_item_warehouse_location.whseloc_bin_location, \
							 in_item_warehouse_count_items_to_in_item.item_code, \
							 in_item_warehouse_count_items_to_in_item_project.custproj_desc, \
							 in_item_warehouse_count_items_to_in_item_roll.initemroll_roll_number');	
	
	for (var i=1; i<=fsInItemCountItems.getSize(); i++) {
		var rInItemCountItem = fsInItemCountItems.getRecord(i);
		
		rInItemCountItem.line_number = i;
		rInItemCountItem.book_quantity = 0;
		
		if (rInItemWarehouseCount.in_itemwhscount_batchsize) {
			rInItemCountItem.batch_number = 1 + Math.floor( (i - 1) / rInItemWarehouseCount.in_itemwhscount_batchsize);
		}
		else {
			rInItemCountItem.batch_number = null;
		}
		
		if (bProjectInventory && utils.hasRecords(rInItemCountItem.in_item_warehouse_count_items_to_in_item_project)) {
			rInItemCountItem.book_quantity = rInItemCountItem.in_item_warehouse_count_items_to_in_item_project.ip_qty_on_hand - rInItemCountItem.in_item_warehouse_count_items_to_in_item_project.clc_unavailable_qty;
		}
		else if (utils.hasRecords(rInItemCountItem.in_item_warehouse_count_items_to_in_item_roll)) {
			rInItemCountItem.book_quantity = rInItemCountItem.in_item_warehouse_count_items_to_in_item_roll.initemroll_qty_on_hand - rInItemCountItem.in_item_warehouse_count_items_to_in_item_roll.clc_unavailable_qty;
		}
		else if (utils.hasRecords(rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse_location)) {
			rInItemCountItem.book_quantity = rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse_location.itemwhseloc_onhand_qty - rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse_location.itemwhseloc_unavailible_qty;
		}
		else if (utils.hasRecords(rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse)) {
			rInItemCountItem.book_quantity = rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse.itemwhse_onhand_qty - rInItemCountItem.in_item_warehouse_count_items_to_in_item_warehouse.itemwhse_unavailable_qty;
		}
		
		databaseManager.saveData(rInItemCountItem);		
	}
	
	return;
}

/**
 * Return whether or not there is at lease one row present
 * 
 * @global {JSFoundSet<in_item_warehouse_count_tbl>} forms.in_item_warehouse_count_tbl.foundset
 * 
 * @properties={typeid:24,uuid:"34EC6391-3F86-46D3-BD0F-F4DCF84C24EC"}
 * 
 * @return {Boolean} - true if there is at least one row present
 */
function hasAtLeastOneRow() {
	
	var fsInItemWarehouseCount = forms.in_item_warehouse_count_items_tbl.foundset;
	
	if (fsInItemWarehouseCount.getSize() > 0) {
		return true;	
	}
	else {
		return false;
	}		
}

/**
 * Returns the status of the currently selected record.
 * 
 * @properties={typeid:24,uuid:"0FBA55F4-4FCA-46EB-8F14-EDA86A3543C1"}
 * 
 * @return {String} - the status (Open|Started|Exported)
 */
function getSelectedRecordStatus() {

		var rInItemWarehouseCount = foundset.getSelectedRecord();
		
		if (!rInItemWarehouseCount) {
			return rInItemWarehouseCount.in_itemwhsecount_countstatus;
		}
		else {
			return null;
		}
}

/**
 * @AllowToRunInFind
 * 
 * Exports the count.
 * 
 * @param {JSRecord<db:/avanti/in_item_warehouse_count>} rInItemWarehouseCount
 * 
 * @properties={typeid:24,uuid:"5437C1C8-EA5F-4625-8E3E-CEA5A6155BF7"}
 * 
 * @return {Boolean} - true if successful, false otherwise
 */
function doExportCount(rInItemWarehouseCount) {
	
	var sPrevBatchNumber = null; 
	var sCSVText = "";
	var sCSVHeadings = "";
//	var aCSVFiles = [];
	var sZipFolderPath = "";
	var sCSVSavePath = globals.avUtilities_serverGetPath( '\\counts\\');
	var bProjectInventory = scopes.avInv.isProjectInventoryOn();
	
	sCSVHeadings += 'LineNumber,';
	sCSVHeadings += 'ItemCode,';
	sCSVHeadings += 'Description,';
	sCSVHeadings += 'Warehouse,';
	sCSVHeadings += 'Location,';
	sCSVHeadings += 'Roll,';
	
	if (bProjectInventory) {
		sCSVHeadings += 'Project,';
	}
	
	sCSVHeadings += 'Batch,';
	sCSVHeadings += 'CountQty,';
	sCSVHeadings += 'StockingUnit,';
	sCSVHeadings += 'Size,';
	sCSVHeadings += 'Color,';
	sCSVHeadings += 'Notes';
	sCSVHeadings += "\n";
	
	if (!rInItemWarehouseCount) {
		throw new Error('in_item_warehouse_dtl.js:doExportCount():ERROR:rInItemWarehouseCount is null/undefined');
	}
	
	var rInItemWarehouseCountId = rInItemWarehouseCount.in_item_warehouse_count_id;
	
	/** @type JSFoundSet<db:/avanti/in_item_warehouse_count_items> */
	var fsInItemWarehouseCountItems = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse_count_items');
	
	if (fsInItemWarehouseCountItems.find()) {
		
		fsInItemWarehouseCountItems.in_item_warehouse_count_id = rInItemWarehouseCountId;
		fsInItemWarehouseCountItems.include_in_count = 1;
		if (fsInItemWarehouseCountItems.search() > 0) {
			
			fsInItemWarehouseCountItems.sort('batch_number, line_number');
			
			for (var i=1; i<=fsInItemWarehouseCountItems.getSize(); i++) {
				
				/** @type JSRecord<db:/avanti/in_item_warehouse_count_items> */
				var rInItemWarehouseCountItems = fsInItemWarehouseCountItems.getRecord(i);

				var sLineNumber = "";
				var sItemNumber = "";
				var sDescription = "";
				var sWarehouse = "";
				var sLocation = "";
				var sRoll = "";
				var sProject= "";
				var nBatch = "";
				var sCountQty = "";				
				var sStockingUnit = "";
				var sSize = "";
				var sColor = "";
				
				sLineNumber = rInItemWarehouseCountItems.line_number;
				nBatch = rInItemWarehouseCountItems.batch_number;
				
				if (nBatch != sPrevBatchNumber) {
					
					// Save the previous batch file if batch number has changes this iteration:
					if (sCSVText.length > 0) { // ensure we have some content before saving
						sZipFolderPath = saveCSVFile(sCSVHeadings+sCSVText, sPrevBatchNumber, sCSVSavePath);
					}
					
					sCSVText = ""; // clears saved records
				}
				sPrevBatchNumber = nBatch;				
				
				if (utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item)) {
					
					sItemNumber = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.item_code;
					sDescription = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.item_desc1;
					sColor = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.item_color;
					sSize = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.item_full_dimension;
					
					if (utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.in_item_to_sys_unit_of_measure_stocking_uom)) {
						sStockingUnit = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item.in_item_to_sys_unit_of_measure_stocking_uom.uom_code;						
					}
					
					if (utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_warehouse)) {
						sWarehouse = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_warehouse.whse_desc;							
					}
					
					if (utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_warehouse_location)) {
						sLocation = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_warehouse_location.whseloc_bin_location;
					}

					if (utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_roll)) {
						sRoll = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_roll.initemroll_roll_number;
					}
					
					if (bProjectInventory && utils.hasRecords(rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_project)) {
						sProject = rInItemWarehouseCountItems.in_item_warehouse_count_items_to_in_item_project.custproj_desc;
					}
				}
				
				// Get Main Record Fields:
				sCountQty = rInItemWarehouseCountItems.count_quantity;
				
				sCSVText += '"' + sLineNumber +'","';
				sCSVText += utils.stringReplace(sItemNumber, "\"", "''") +'","';
				sCSVText += utils.stringReplace(sDescription, "\"", "''") +'","';
				sCSVText += utils.stringReplace(sWarehouse, "\"", "''") +'","';
				sCSVText += utils.stringReplace(sLocation, "\"", "''") +'","';
				sCSVText += utils.stringReplace(sRoll, "\"", "''") +'","';
				
				if (bProjectInventory) {
					sCSVText += utils.stringReplace(sProject, "\"", "''") +'","';
				}
				
				sCSVText += nBatch+'","';
				sCSVText += sCountQty+'","';		
				sCSVText += utils.stringReplace(sStockingUnit, "\"", "''") +'","';				
				sCSVText += utils.stringReplace(sSize, "\"", "''") +'","';
				sCSVText += utils.stringReplace(sColor, "\"", "''") + '"';
				
				// Get Notes:
				
				// (stores note in last column, if there is more than one note then use column, j,k,l, etc... where j is the last column):
				sCSVText += ','+getNotesTextForCountItem(rInItemWarehouseCountItems.in_item_warehouse_cntitems_id); 		
				sCSVText += "\n";
			}
			
			// Save Last Batch File (or the entire file if there are no batches):			
			sZipFolderPath = saveCSVFile(sCSVHeadings+sCSVText, nBatch, sCSVSavePath);

			try {
				// Attempt to delete the zip file that might already exist.
				plugins.file.deleteFile(sZipFolderPath + '.zip');
			} catch(ex) {
				
			}
			plugins.it2be_tools.zip(sZipFolderPath);

			forms._docs_base.getFileFromServer(sZipFolderPath + '.zip');

			try {
				// Attempt to delete the folder that is being zipped.
				plugins.file.deleteFolder(sZipFolderPath, false);
			} catch(ex) {
				
			}
			return true;
		}
		else {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.info'),i18n.getI18NMessage('avanti.dialog.noItemsFoundInCount'),i18n.getI18NMessage('avanti.dialog.okay'));
			return false;
		}		
	}
	else {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.info'),i18n.getI18NMessage('avanti.dialog.noCountsFound'),i18n.getI18NMessage('avanti.dialog.okay'));
		return false;
	}
}

/**
 * This will save the given text to a CSV file.
 * 
 * @param {String} sCSVText - the text to save as a CSV file (pre-formatted)
 * @param {String} sBatchNumber - (optional) if present then append this name to the file
 * @param {String} sFilePath - path to where the folder containing the batch files should be stored
 *
 * @properties={typeid:24,uuid:"BE0B5B88-09DB-44AD-B8DD-B071C648DAE4"}
 * 
 * @return {String} - the path to the saved file or null upon error
 */
function saveCSVFile(sCSVText, sBatchNumber, sFilePath) {
	
	// Save File:
	
	var sFileBasename = "count";
	
	if (forms.in_item_warehouse_count_dtl.in_itemwhscount_countcode) {
		sFileBasename = forms.in_item_warehouse_count_dtl.in_itemwhscount_countcode;
		sFileBasename = sFileBasename.replace(/^[ ]+|[ ]+$/g,''); // .trim() equivalent since Servoy does not appear to support str.trim()
		sFileBasename = scopes.avText.removeInvalidFileNameCharacters(sFileBasename);		
	}

	var sFileName = 'Count ' + sFileBasename;
	
	var sZipFolderName = sFileName;
	
	if (sBatchNumber) {
		sFileName += '_batch'+sBatchNumber;
	}
	
	sFileBasename = sFileBasename+'_'+application.getServerTimeStamp().toString();
	sFileName += '.csv';
	
	var oFileHandle = null;
	var sZipFileFolderPath = sFilePath+'/'+sZipFolderName;
	
	plugins.it2be_tools.server().createDirectory(sZipFileFolderPath+'/');

	oFileHandle = plugins.file.createFile(sZipFileFolderPath+'/'+sFileName);

	var bSuccess = plugins.file.writeTXTFile(oFileHandle, sCSVText);

	
	if (bSuccess) {
		return sZipFileFolderPath;
	}
	else {
		return null;
	}
}

/**
 * Fetch all associated notes with the given count item
 * 
 * @param {UUID} sInItemWarehouseCountItemsId - in_item_warehouse_count_items.in_item_warehouse_cntitems_id
 *
 * @properties={typeid:24,uuid:"CA8EA9B2-2CEF-4AF2-BBDD-6986431D53B1"}
 * 
 * @return {String} - the text of the notes
 * @AllowToRunInFind
 */
function getNotesTextForCountItem(sInItemWarehouseCountItemsId) {
	
	var sNoteText = "";
	
	if (!sInItemWarehouseCountItemsId) {
		throw new Error('in_item_warehouse_count_dtl.js:getNotesTextForCountItem():ERROR: received null/undefined sInItemWarehouseCountItemsId value.');
	}
	
	/** @type JSFoundSet<db:/avanti/sys_note> */
	var fsSysNote = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_note');
	
	if (fsSysNote.find()) {
		
		fsSysNote.note_object_source_id = sInItemWarehouseCountItemsId; // filter to notes pertaining to this count item
		
		if (fsSysNote.search() > 0) {
			
			fsSysNote.sort('note_creation_date');			
			
			for (var i=1; i<=fsSysNote.getSize(); i++) {
				
				/** @type JSRecord<db:/avanti/sys_note> */
				var rSysNote = fsSysNote.getRecord(i);
				
				if (i != 1) {
					sNoteText += ',';	
				}
				
				sNoteText += '"'+escapeDoubleQuotes(rSysNote.note_text)+'"';				
			}
		}
		
	}	
	
	return sNoteText;
}

/**
 * Replace " chars with "" to escape them for the CSV
 * 
 * @param {String} sText - the string to escape double quote characters in
 *
 * @properties={typeid:24,uuid:"CD58AF23-B564-4B28-A570-667789873B88"}
 * 
 * @return {String} - the escaped string
 */
function escapeDoubleQuotes(sText) {
	
	var sEscapedText = "";
	
	if (sText) {
		sEscapedText = sText.split('"').join('""');
	}
	
	return sEscapedText;
}

/**
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @properties={typeid:24,uuid:"B8103C48-D3EA-4C0D-A5F1-C3BC9D588257"}
 */
function dc_new(_event, _triggerForm) {
	_super.dc_new(_event, _triggerForm);
	var rInItemWarehouseCount = foundset.getSelectedRecord();	
	forms.in_item_warehouse_count_dtl.createCountRecord(rInItemWarehouseCount);
	refreshUI();
	return;
}

/**
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"2BBF2141-2142-400C-A868-587D66EFE2BD"}
 */
function dc_save(_event, _triggerForm) {
	
	var rInItemWarehouseCount = forms.in_item_warehouse_count_dtl.foundset.getSelectedRecord();
	
	if (!rInItemWarehouseCount.in_itemwhscount_countcode) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.Error"), i18n.getI18NMessage("avanti.dialog.countCodeMustBeFilledIn"), i18n.getI18NMessage("avanti.dialog.ok"));
        return null;
	}
	
	if (!rInItemWarehouseCount.in_itemwhsecount_desc) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.Error"), i18n.getI18NMessage("avanti.dialog.countDescMustBeFilledIn"), i18n.getI18NMessage("avanti.dialog.ok"));
        return null;
	}
	
	refreshUI();
	
    // have to save before calling processTempInsertRecs() as it uses sql 
    var r = _super.dc_save(_event, _triggerForm);
    
	processTempInsertRecs();
	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
	if (foundset.in_itemwhsecount_countstatus == 'U') {
		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
	}
	else {
		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
	}
    return r;
}

/**
 * @private 
 * 
 * @param {Boolean} [bCancel]
 *
 * @properties={typeid:24,uuid:"48153450-DA3D-47F7-A02B-0A866F50107C"}
 */
function processTempInsertRecs(bCancel) {
	// sl-21081 - when you import a bin that doesnt exist on the item then we create the item bin rec. but if they cancel the count without saving then we dont want to keep the 
	// item bin rec created, so delete them. we use intraneh_id_temp_insert to id these recs. if they save the count then clear intraneh_id_temp_insert flag. 
	
	if (in_item_warehouse_count_id) {
		// sl-25487 - if saving we still may need to delete temp wares/bins. they may have selected a ware/bin, then selected a different one. the initial one needs to be deleted 
		if (!bCancel) {
			// delete temp bins that arent used by any items on this count - 5th param on getFSFromSQL is bDeleteRecordsFound
			var sBinSQL = "SELECT L.itemwhseloc_id \
						   FROM in_item_warehouse_location L \
						   LEFT JOIN in_item_warehouse_count_items C ON L.intraneh_id_temp_insert = C.in_item_warehouse_count_id AND L.itemwhseloc_id = C.itemwhseloc_id \
						   WHERE \
							   L.intraneh_id_temp_insert = ? \
							   AND C.in_item_warehouse_cntitems_id IS NULL";
			scopes.avDB.getFSFromSQL(sBinSQL, "in_item_warehouse_location", [in_item_warehouse_count_id.toString()], null, true);
			
			// delete temp wares that arent used by any items on this count - 5th param on getFSFromSQL is bDeleteRecordsFound
			var sWareSQL = "SELECT W.itemwhse_id \
							FROM in_item_warehouse W \
							LEFT JOIN in_item_warehouse_count_items C ON W.intraneh_id_temp_insert = C.in_item_warehouse_count_id AND W.whse_id = C.whse_id \
							WHERE \
								W.intraneh_id_temp_insert = ? \
								AND C.in_item_warehouse_cntitems_id IS NULL";
			scopes.avDB.getFSFromSQL(sWareSQL, "in_item_warehouse", [in_item_warehouse_count_id.toString()], null, true);
		}
		
	    /*** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
		var fsWare = scopes.avDB.getFSFromSQL("SELECT itemwhse_id FROM in_item_warehouse WHERE intraneh_id_temp_insert = ?", "in_item_warehouse", [in_item_warehouse_count_id.toString()]);
	    /*** @type {JSFoundSet<db:/avanti/in_item_warehouse_location>} */
		var fsBin = scopes.avDB.getFSFromSQL("SELECT itemwhseloc_id FROM in_item_warehouse_location WHERE intraneh_id_temp_insert = ?", "in_item_warehouse_location", [in_item_warehouse_count_id.toString()]);
		
		if (utils.hasRecords(fsWare)) {
			if (bCancel) {
				fsWare.deleteAllRecords();
			}
			else {
				scopes.avDB.updateFS(fsWare, ["intraneh_id_temp_insert"], [null]);
			}
		}
		
		if (utils.hasRecords(fsBin)) {
			if (bCancel) {
				fsBin.deleteAllRecords();
			}
			else {
				scopes.avDB.updateFS(fsBin, ["intraneh_id_temp_insert"], [null]);
			}
		}
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"53A5BB1B-B156-4221-A644-87D4E383A716"}
 */
function onDataChange_batchSize(oldValue, newValue, event) {
	
	if (newValue) {
		forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = true;
	}
	else {
		forms.in_item_warehouse_count_dtl.elements.btnAddRows.enabled = false;		
	}
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"38BA1F63-B3D4-4671-A24F-C7E1E404D606"}
 */
function onDataChange_cycleoption(oldValue, newValue, event) {
	if(in_itemwhscount_numitems > 0) {
		forms.in_item_warehouse_count_items_tbl.updateCount();
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"2110778D-02FC-408F-9567-97EB2322DF05"}
 */
function onDataChange_numitems(oldValue, newValue, event) {
	if(newValue > 0) {
		forms.in_item_warehouse_count_items_tbl.updateCount();
	}
	return true;
}

/**
 * This will flag that a user has opened the variance report window 
 * and had an opportunity to adjust any variances.
 * 
 * @properties={typeid:24,uuid:"7D9F1C92-6BD2-4A81-B485-6EBA2F1279AD"}
 */
function flagVarianceAsSeen() {
	
	/** @type JSRecord<db:/avanti/in_item_warehouse_count> */	
	var rInItemWarehouseCount = foundset.getSelectedRecord();
	
	rInItemWarehouseCount.in_itemwhsecount_varianceseen = 1;
	databaseManager.saveData(rInItemWarehouseCount);
	
	refreshUI();
	
	return;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"889F0970-8D19-40C8-9A0E-E04F2291D568"}
 */
function onDataChange_countCode(oldValue, newValue, event) {
	foundset.getSelectedRecord();
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D7496EBB-0708-41DB-A2E6-D6844B02EF6E"}
 */
function onActionDeleteAllRows(event) {
	if (!scopes.avUtils.isNavModeReadOnly()) {
		if(in_itemwhsecount_countstatus == 'O') {
			var sAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.areYouSureYouWantToDeleteAllItems'), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
			
			if (sAnswer != i18n.getI18NMessage('avanti.dialog.yes')) {
				return;
			}
			foundset.in_item_warehouse_count_to_in_item_warehouse_count_items.deleteAllRecords();
		}
		
	} else if(globals.nav.mode != 'edit') {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
	} 	
	return;
}

/**
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * @param {String} [_answer]
 * @param {Boolean} [bBypassSaveData]
 *
 * @return {Number} - 1 = success, -1 = failure
 * @override
 *
 * @properties={typeid:24,uuid:"666CCF9F-C749-4302-ABA7-FE973B8ADBC2"}
 */
function dc_cancel(_event, _triggerForm, _answer, bBypassSaveData) {
    var _ok = i18n.getI18NMessage('avanti.dialog.ok')
    var _no = i18n.getI18NMessage('avanti.dialog.cancel')

    if (globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('svy.fr.lbl.cancel'), i18n.getI18NMessage('svy.fr.dlg.cancel_record'), _ok, _no) == _ok) {
    	// when you flag the count as entered it saves it to the db, and you cant add/delete items, so after that a cancel is effectively the same as a save
		if (scopes.avDB.SQLQuery("SELECT COUNT(*) FROM in_item_warehouse_count WHERE in_item_warehouse_count_id = ?", null, [in_item_warehouse_count_id.toString()]) > 0) {
	    	processTempInsertRecs();
		}
		else {
	    	processTempInsertRecs(true);
		}
		
    	var r = _super.dc_cancel(_event, _triggerForm, _ok, bBypassSaveData);
    	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
    	if (foundset.in_itemwhsecount_countstatus == 'U') {
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
    	}
    	else {
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
    	}
    	return r;
    }
    else {
    	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
    	if (foundset.in_itemwhsecount_countstatus == 'U') {
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
    	}
    	else {
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
    		forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
    	}
    	return null;
    }
}
