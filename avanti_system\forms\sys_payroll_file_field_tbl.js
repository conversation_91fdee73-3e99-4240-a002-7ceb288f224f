/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9643C1F6-F634-4A61-8631-A86BB5C1CF48"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldUseDatabaseFieldFlag") {
		onUseDatabaseField(event);
	}
	if (col.id == "btnDeleteRow") {
		onAction_btnDelete(event);
	}
	if (col.id == "sort_icon") {
		scopes.globals.avUtilities_shuffle(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"752F9BAE-38FE-417C-9812-7195DD8C037D"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldFieldName") {
		onDataChange_avantiField(oldValue, newValue, event);
	}
}

/**
 * @param {Number} iSeqNum
 * 
 * @return {JSRecord<db:/avanti/sys_payroll_file_field>}
 *
 * @properties={typeid:24,uuid:"D3EB07D6-5088-422F-86EA-E06413E592BE"}
 * @AllowToRunInFind
 */
function getRecFromSeqNum(iSeqNum){
	return foundset.getRecord(iSeqNum)
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"1DAA59AF-8F90-4E93-BCB7-C1F84ACA3D1E"}
 */
function onAction_btnAdd(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
		foundset.newRecord(false, true);
		foundset.org_id=globals.org_id
		foundset.sequence_nr = GetNextSeqNum()
		databaseManager.saveData(foundset)
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"0D185213-7EFC-419A-A5D4-C4002537F834"}
 */
function GetNextSeqNum(){
	var tiRetVal

	tiRetVal = globals.Query("SELECT max(sequence_nr) from sys_payroll_file_field where prf_id = '" + forms.sys_payroll_file_dtl.foundset.prf_id + "'", true)
	if(tiRetVal=='')
		tiRetVal=1
	else
		tiRetVal+=1
		
	return tiRetVal
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"521410A6-489E-4041-913F-3BA4B477AFD2"}
 */
function onAction_btnDelete(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
		globals.avUtilities_delete(event);
	}
    
	return;
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"BD446703-1469-4FDB-AC26-C2FEA88B634F",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"D19B4E1B-092C-466A-912D-3A08B1CE34AB"}
 */
function onReady() {
    _gridReady = 1;
}

/**
*
* @param {Boolean} _firstShow
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"88E279AE-9A91-4317-8D68-8BF6490A29D8"}
*/
function onShowForm(_firstShow, _event) {
	
	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}
	
	var retval = _super.onShowForm(_firstShow, _event)
	return retval
}

/**
*
* @param {JSEvent} event
*
 * @return
* @properties={typeid:24,uuid:"C4B032B6-0BD7-40BA-B724-7759D508921E"}
*/
function onLoad(event) {
	return _super.onLoad(event);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"15F76AB6-D08A-4D02-BB13-A90FEDB77251"}
 */
function onDataChange_avantiField(oldValue, newValue, event) {
	if(!newValue){
		prff_pr_data_table_field_num = null
	}
	else{
		var newDataType = getDataType(newValue)

		if(oldValue){
			var oldDataType = getDataType(oldValue)
			if(!sameDataType(oldDataType, newDataType)){
				prff_pr_data_table_field_num = getNextCBTableColNum(newDataType)
			}
		}
		else{
			prff_pr_data_table_field_num = getNextCBTableColNum(newDataType)
		}
	}
	
	return true
}

/**
 * @param {String} type1
 * @param {String} type2
 *
 * @return
 * @properties={typeid:24,uuid:"CF9CCA64-3170-4C72-870D-36E738B126B8"}
 */
function sameDataType(type1, type2){
	var same = false
	
	if(type1==type2){
		same = true
	}
	else if((type1 == 'number' || type1 == 'currency') && (type2 == 'number' || type2 == 'currency')){
		same = true
	}
	
	return same
}

/**
 * @param {String} dataType
 *
 * @return
 * @properties={typeid:24,uuid:"4C4B0E1A-6D2E-4939-869C-1377C736477D"}
 */
function getNextCBTableColNum(dataType){
	var last_num = 0
	var sql = 'select max(prff_pr_data_table_field_num) ' +
	  'from sys_payroll_file_field sys ' +
	  'inner join app_avail_payroll_fields app on app.aprf_id = sys.aprf_id ' +
	  'where sys.prf_id = ? and (app.aprf_data_type = ? or app.aprf_data_type = ?)'

	 if(dataType == 'number' || dataType == 'currency'){
		 last_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(prf_id), 'number', 'currency'])
	 }
	 else{
		 last_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(prf_id), dataType, dataType])
	 }
	 
	 last_num++
	 
	 return last_num
}

/**
 * @param {Number} appFieldID
 *
 * @return
 * @properties={typeid:24,uuid:"B7668DE8-BEC8-4617-81C2-10DDA316D141"}
 */
function getDataType(appFieldID){
	return scopes.avDB.getVal('app_avail_payroll_fields', ['aprf_id'], [appFieldID], 'aprf_data_type')
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"C144D981-3436-4648-AE6E-EB9964EE5CA5"}
 */
function onRecordSelection(_event, _form) {
	return _super.onRecordSelection(_event, _form);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D92E46FA-3D30-45DC-9501-85CF89050262"}
 */
function onDataChange_prTableCol(oldValue, newValue, event) {
	if(newValue){
		if(scopes.avDB.doesFSHaveValue(foundset, 'prff_pr_data_table_field_name', newValue, foundset.getSelectedIndex())){
			globals.showWarning('ThisColumnHasAlreadyBeenUsed.')
//			return false
			prff_pr_data_table_field_name = oldValue
		}
		prff_pr_data_table_field_num = getPRTableColNum(newValue)
	}
	else{
		prff_pr_data_table_field_num = null	
	}
	 
	return true
}

/**
 * @param {String} prTableColName
 *
 * @return
 * @properties={typeid:24,uuid:"8E30FD23-89C8-490C-8397-B1099F60D933"}
 */
function getPRTableColNum(prTableColName){
	var num = null 

	if(prTableColName){
		var underscorePos = prTableColName.lastIndexOf('_')
		if(underscorePos){
			num = parseInt(prTableColName.substr(underscorePos+1)) 
		}
	}
	
	return num
}

/**
 * Toggle the fields based on the selection.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6E3B5968-FAE4-4762-AD79-ECB572E49010"}
 */
function onUseDatabaseField(event) {
	if(prff_use_database_field_flag === 0){
		aprf_id = null;
	} else {
		prff_user_defined_value = null;

	}
}
