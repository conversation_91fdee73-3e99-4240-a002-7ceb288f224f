customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_batch_inv_file_sum_field",
extendsID:"BD3226CB-C29A-4CAE-A46B-646C3FE287B0",
initialSort:"sequence_nr asc",
items:[
{
cssPosition:"8,-1,-1,39,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"39",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"D7796229-799F-461C-A637-127E94B12655",
styleClass:"listview_noborder label_bts",
tabSeq:2,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnD<PERSON>te",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"34474598-DE4B-45BA-B9A8-42EC2C479919"
},
{
height:255,
partType:8,
typeid:19,
uuid:"4DE5C4F4-A534-4326-93EB-6A338D7A5D22"
},
{
height:250,
partType:5,
typeid:19,
uuid:"5D387B45-C383-4FA2-AF5F-2270A40E0383"
},
{
anchors:15,
cssPosition:"41px,0px,5px,0px,1001px,209px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.SummaryFieldOrder",
id:"fldColOrder",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"B8D03697-0149-4E0A-B70D-90940F3AE614",
valuelist:null,
visible:true,
width:151
},
{
autoResize:true,
dataprovider:"bifsf_position",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.position",
id:"fldPosition",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"3B237CBB-CD42-4671-8E9D-B696E0A8A704",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"bifsf_length",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.length2",
id:"fldLength",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"16D47A54-CE46-4081-A13A-630C464918F1",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"abif_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:false,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.avantiFieldName",
id:"fldFieldName",
isEditableDataprovider:"clc_avantiFieldBatchInvSummary",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"2605AD8A-AF3A-4652-BD85-3FDA4F006D79",
valuelist:"F33BACB7-891E-437F-8E4B-5527621B6C8E",
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"bifsf_summary_type",
editType:"COMBOBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.SummaryType",
id:"fldSumType",
isEditableDataprovider:"clc_avantiFieldBatchInvSummary",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D198182D-EF07-4197-8A5E-07443FDDCAC3",
valuelist:"89E0B410-3D10-4D35-9298-1A0E9DC0F8DB",
visible:true,
width:125
},
{
autoResize:true,
dataprovider:"bifsf_col_header",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnHeader",
id:"fldColHeader",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"E3C2041E-7FCD-4854-AB5C-A7A6746E08FD",
valuelist:null,
visible:true,
width:251
},
{
autoResize:false,
dataprovider:"bifsf_use_udv",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.useUDV",
id:"fldUseUDV",
isEditableDataprovider:"clc_UDVFieldBatchInvSummary",
maxWidth:154,
minWidth:154,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"A13D4ECA-403E-4C92-9793-D3B80B5514E1",
valuelist:null,
visible:true,
width:154
},
{
autoResize:true,
dataprovider:"bifsf_user_defined_value",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.UserDefinedValue",
id:"fldUDV",
isEditableDataprovider:"clc_UDVFieldBatchInvSummary",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"3A99D9A2-F43B-435B-8CE3-DACA5A8FF107",
valuelist:null,
visible:true,
width:236
}
],
cssPosition:{
bottom:"5px",
height:"209px",
left:"0px",
right:"0px",
top:"41px",
width:"1001px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"CB7178D9-D467-4D2E-A6E2-A291DA1D390F",
onColumnDataChange:"26E07193-4C41-4603-9319-B395737BD795",
onReady:"E9CAD275-B668-4D77-903E-4AAD996ABBF8",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"356CB1DF-380D-4EF8-BFE3-397126B2F797"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"81EB4A05-40AE-43F5-B93A-1DC00083F3F7"
},
{
cssPosition:"8,-1,-1,184,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"184",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"CDA7B955-84DF-4D6E-9E06-40349F493845",
styleClass:"btn btn-default button_bts",
tabSeq:4,
text:"i18n:avanti.lbl.moveDown",
visible:true
},
name:"component_6277B705",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"8C4F3404-85C6-4D50-9FBA-ECF5708C13C7"
},
{
cssPosition:"8,-1,-1,9,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"01E0796C-31A0-444D-9BC4-F27573CC0137",
styleClass:"listview_noborder label_bts",
tabSeq:1,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9A2D3D03-FDE6-4934-BD9E-F8D7DE1E52E7"
},
{
cssPosition:"8,-1,-1,295,118,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"295",
right:"-1",
top:"8",
width:"118"
},
enabled:true,
onActionMethodID:"C5E39357-1304-42E5-89AC-0500EAC65C0C",
styleClass:"btn btn-default button_bts",
tabSeq:5,
text:"i18n:avanti.lbl.ClearPositions",
visible:true
},
name:"btnClearPositions",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"B2D26F3E-C7D1-476A-AE01-078AE26510EC"
},
{
height:41,
partType:1,
typeid:19,
uuid:"E53D08F5-DABD-48E8-B301-D26A1688E18A"
},
{
cssPosition:"8,-1,-1,74,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"74",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"0041E3F0-79FA-41E5-B37A-97751734199D",
styleClass:"btn btn-default button_bts",
tabSeq:3,
text:"i18n:avanti.lbl.moveUp",
visible:true
},
name:"component_E6306B9D",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"FB155C80-DCBD-4E53-81EA-B86656D19726"
}
],
name:"sys_batch_inv_file_summary_field_tbl",
navigatorID:"-1",
onShowMethodID:"74184E6C-98BE-44E0-B495-AD1EF02C2A39",
onSortCmdMethodID:"A0061674-5B27-4586-8726-15F739B9DECE",
paperPrintScale:100,
scrollbars:33,
size:"1001,585",
styleName:null,
typeid:3,
uuid:"4AFBDDBA-4937-4530-B229-70FB85E3BE9E",
view:0