borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"18,357,-1,-1,48,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"357",
top:"18",
width:"48"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"176CA206-8E1D-4428-994D-C1A458A748FE"
},
{
cssPosition:"135,-1,-1,155,250,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"135",
width:"250"
},
dataProviderID:"asAtDate",
enabled:true,
formIndex:2,
format:"yyyy-MM-dd",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"asDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"3A5B32E4-79AC-43BE-9344-23541232EEDF"
},
{
cssPosition:"135,-1,-1,10,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"135",
width:"140"
},
enabled:true,
formIndex:1,
labelFor:"asDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dateAsOf",
visible:true
},
name:"component_3817189D",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"457A6F2C-C390-4D5C-8102-527DDB31521A"
},
{
cssPosition:"189,-1,-1,155,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"189",
width:"250"
},
dataProviderID:"_sPaymentsBasedOn",
enabled:true,
onDataChangeMethodID:"12F95215-B2E5-4D81-BAE4-C9856A3879B1",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EAB5FF47-F115-4943-A3F4-95ABD141AF3A",
visible:true
},
name:"_sPaymentsBasedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"49239755-76B4-405A-877E-BB5F2CDF5AD0"
},
{
cssPosition:"18,-1,-1,155,250,112",
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"155",
right:"-1",
top:"18",
width:"250"
},
dataProviderID:"fromDiv",
onDataChangeMethodID:"E047163D-F8F1-4609-AE93-3EA8057647F3",
showAs:null,
styleClass:"checkbox_column",
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA"
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"58D88D2D-0BBB-4BA4-BDBB-116441A8E8AA"
},
{
height:200,
partType:5,
typeid:19,
uuid:"*************-4CEC-AF06-CF09679557FD"
},
{
cssPosition:"189,-1,-1,10,138,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"189",
width:"138"
},
enabled:true,
formIndex:3,
labelFor:"_sPaymentsBasedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentsBasedOn",
visible:true
},
name:"_sPaymentsBasedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"675D60BD-3916-41AE-A600-8A4172B57557"
},
{
cssPosition:"18,-1,-1,10,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"18",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"99D41ABE-7C66-444C-8A9E-ED10CB2070CE"
},
{
cssPosition:"162,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"162",
width:"140"
},
enabled:true,
labelFor:"_basedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoicesBasedOn",
visible:true
},
name:"_basedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C408E377-3C72-445D-AFE7-670E49556636"
},
{
cssPosition:"162,-1,-1,155,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"162",
width:"250"
},
dataProviderID:"_basedOn",
enabled:true,
onDataChangeMethodID:"CBC0B421-3F27-4386-992C-2D8AEE5229F5",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2AEB8639-FBAA-4F6B-B650-07B708369F0B",
visible:true
},
name:"_basedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"DD864FAF-84EF-42C2-9E2B-7B4122BBE977"
},
{
cssPosition:"15,-1,-1,483,250,115",
json:{
cssPosition:{
bottom:"-1",
height:"115",
left:"483",
right:"-1",
top:"15",
width:"250"
},
dataProviderID:"fromPlant",
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0"
},
name:"fromPlant",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"EE2BF454-D67C-4C71-B6A5-42B0C3005CE5"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:281,
typeid:19,
uuid:"F51C122C-43C3-4DA4-9D3C-5CBEAE2657BC"
}
],
name:"rpt_report_dlg_ar_aging_withDivPlant",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"B63190E4-B399-464B-BC03-7C6211484AF1"