/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DFAEB4B0-D91B-48DA-8429-26AE96A2B4DF"}
 */
var original_mode = '';

/**
 * <PERSON><PERSON> changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D3DC9D59-EF61-4A6F-A6FE-5D8B25F77634"}
 */
function onCustomerNameChange(oldValue, newValue, event) {
	
	//Update the primary address name with the customer name	
	sa_customer_to_sa_customer_address_primary.custaddr_address_name = cust_name

	return true
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"6ED25C37-E388-4BCF-9D0F-A8FFFA4F7906"}
 */
function onDataChange_CustomerClass(oldValue, newValue, event) {
	
	if (utils.hasRecords(sa_customer_to_sa_customer_class))
	{
		// Set the edit mode of the form
		var sMode;
		sMode = globals.avUtilities_getLookupWindowMode(event.getFormName())		
		
		if (sMode == 'edit' && oldValue != null ){
			
			//Prompt user to copy class defaults
			var loYes = i18n.getI18NMessage('avanti.dialog.yes'); 
			var loNo = i18n.getI18NMessage('avanti.dialog.no');
			var loQuestion = i18n.getI18NMessage('avanti.customer.dialog.ChangeCustomerClass') + ' \n' + sa_customer_to_sa_customer_class.custclass_code + '?';
			var loAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.dialog.confirmUpdate'),loQuestion,loYes,loNo);
			
		}
		
		if (sMode == 'add' || (sMode == 'edit' && oldValue == null) || (sMode == 'edit' && oldValue != null && loAnswer == loYes)){
			if (sa_customer_to_sa_customer_class.curr_id) {
				curr_id = sa_customer_to_sa_customer_class.curr_id;
			}
			if (sa_customer_to_sa_customer_class.taxgroup_id) {
				taxgroup_id = sa_customer_to_sa_customer_class.taxgroup_id;
				forms.sa_customer_dtl_accountinfo.onDataChange_TaxGroup(null,taxgroup_id,event);
			}
			
			if (sa_customer_to_sa_customer_class.salesper_id) {
				salesper_id = sa_customer_to_sa_customer_class.salesper_id;

				// sl-8027 - if cust sales rep is inactive then alter user and clear order rep 
				if (utils.hasRecords(sa_customer_to_sa_sales_person) && !sa_customer_to_sa_sales_person.salesper_active) {
					salesper_id = null;
					scopes.avText.showWarning('custClassSalesRepIsInactive');
				}
			}
			
			if (sa_customer_to_sa_customer_class.terr_id) {
				terr_id = sa_customer_to_sa_customer_class.terr_id;
			}
			if (sa_customer_to_sa_customer_class.shipmethod_id) {
				shipmethod_id = sa_customer_to_sa_customer_class.shipmethod_id;
			}
			if (sa_customer_to_sa_customer_class.terms_id) {
				terms_id = sa_customer_to_sa_customer_class.terms_id;
			}
			if (sa_customer_to_sa_customer_class.form_lang_code) {
				form_lang_code = sa_customer_to_sa_customer_class.form_lang_code;
			}
			foundset.chargebackcustomer = sa_customer_to_sa_customer_class.chargeback_customer;
		}
			
	}
			
	return true
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"13FB05E0-BD1F-44E5-97C4-186361DFA21D"}
 */
function dc_edit(_event, _triggerForm) {

	// Prevent editing of Avanti Work Template Customer
	if (foundset.cust_code != scopes.avUtils.ENUM_AVANTI_CUSTOMER.customerCode) {
		_super.dc_edit(_event, _triggerForm);

		if (forms.sa_customer_dtl_web_user.foundset.getSize()) {
			forms.sa_customer_dtl_web_user.setFormReadOnly(false);
		}
		else {
			forms.sa_customer_dtl_web_user.setFormReadOnly(true);
		}
	}
}

/**
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"3D8A7AA0-A56C-4CB0-A768-8F9EBF9E0A9D"}
 * @override
 */
function dc_delete(_event, _triggerForm) {
	
	// Prevent deletion of Avanti Work Template Customer
	if (foundset.cust_code != scopes.avUtils.ENUM_AVANTI_CUSTOMER.customerCode) {
		_super.dc_delete(_event, _triggerForm);
	}
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"F8502ACC-886B-4358-AF2C-30C7FD561761"}
 */
function dc_new(_event, _triggerForm) {
	_super.dc_new(_event, _triggerForm);
	
	forms.sa_customer_dtl_web_user.setFormReadOnly(true);

}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"8C4C3C99-0751-4540-9D0B-7612962D1D05"}
 */
function dc_save(_event, _triggerForm) {
	
	if (globals.nav.mode == 'find') {
        globals.svy_sea_search();
        globals.svy_nav_dc_setStatus("browse", _triggerForm);
        return 1;
    }
	
    //HL - SL17176 - hardcoded value used by other form caused validation to error
	var retVal = _super.dc_save_validate(databaseManager.getFoundSet(controller.getDataSource()), globals.nav_program_name);
	
	if(retVal == -1){
		return -1;
	}
	
	original_mode = globals.nav.mode;
	if(sa_customer_to_sa_customer_address.custaddr_code == null) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.dialog.addressCodeCannotBeEmpty_msg'),
			i18n.getI18NMessage('avanti.dialog.addressCodeCannotBeEmpty_msg'),
			i18n.getI18NMessage('avanti.dialog.ok'));
		scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tabs_365", 1);
		return -1;
	}
    
    
	_super.dc_save(_event, _triggerForm);
	
	forms.sa_customer_dtl_web_user.setButtonsEnabled(false);
	
	forms.sa_customer_contact_dtl_contact.elements._search.enabled = true;
	forms.sa_customer_address_dtl.elements._search.enabled = true;
	
	// CH - 2014-05-09: Reload records and sort after adding to ensure filter on one time addresses is made.
	forms.sa_customer_address_dtl.foundset.loadRecords();
	forms.sa_customer_address_dtl.foundset.sort('custaddr_code asc');
    
	// HL - SL-17176: Toggle the required asterisks. The framework does not have these fields required.
    if (forms.sa_customer_dtl_address) {
        forms.sa_customer_dtl_address.setRequiredFields();
    }	
	
	try {
		var sAccountingIntegrationType = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration);
		if(original_mode =='add') {
			var currentRecord = foundset.getSelectedRecord();
			if (cust_type == 'C' &&	sAccountingIntegrationType == 'GP') {
				addOrUpdateCustomer(currentRecord);
			}
			forms.utils_quickSearch._qs_quickSearch = currentRecord.cust_code;
			globals.avBase_quickSearchCache.currentForm = 'sa_customer_tbl';
			forms.utils_quickSearch.quickSearch_onDataChange('',currentRecord.cust_code,_event);
		}
		
		if(original_mode == 'edit') {
			if (cust_type == 'C' &&	sAccountingIntegrationType == "GP") {
				addOrUpdateCustomer(foundset.getSelectedRecord());
			}
		}
		if (utils.hasRecords(foundset.getSelectedRecord().sa_customer_to_sa_customer_address_primary.sa_customer_address_to_sys_address)) {
			if (foundset.getSelectedRecord().sa_customer_to_sa_customer_address_primary.sa_customer_address_to_sys_address.addr_address1 != null && foundset.getSelectedRecord().sa_customer_to_sa_customer_address_primary.sa_customer_address_to_sys_address.addr_city != null && foundset.getSelectedRecord().sa_customer_to_sa_customer_address_primary.sa_customer_address_to_sys_address.addr_postal != null) {
				writeAuditHistoryRecordForCustomer(foundset.getSelectedRecord());
				forms["sa_pack_history_tbl"]["refreshUI"]();
			}
		}
	} catch (ex) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),ex.message)
	}
	
	//Refresh the notes display as it reset the display to all notes after a save.
	if (scopes.globals.avUtilities_tabGetName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) == "tabCustomerNotes"){
		forms.sa_customer_note_tbl.refreshUI();
		 
	}
	
	return 1;
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"D88524C3-FB0C-43B1-9F35-EA5F5A2720D1"}
 */
function dc_cancel(_event, _triggerForm) {
	
	if (globals.nav.mode == 'find') {
        globals.svy_sea_search();
        globals.svy_nav_dc_setStatus("browse", _triggerForm);
        return 1;
    }
	
	// Note: cancel reloads the original record overriding any address changes
	setAddressModified(false); // flag address modified flag to false and clear list of modified addresses
	
	_super.dc_cancel(_event, _triggerForm);
	
	forms.sa_customer_dtl_web_user.setButtonsEnabled(false);
	
	// HL - SL-17176: Toggle the required asterisks. The framework does not have these fields required.
	if (forms.sa_customer_dtl_address) {
        forms.sa_customer_dtl_address.setRequiredFields();
    }
}

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"02FBEE49-C015-41CB-A0B5-063C86A36C6D"}
 */
function onRecordSelection(_event, _form) {
	_super.onRecordSelection(_event, _form)
	refreshUI()

	var sCurrentForm = scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365"))
	if (sCurrentForm == 'sa_customer_udf_dtl') {
		forms['sys_udf_tree_answers'].refreshFieldValues()
	}
	else if (sCurrentForm == "sa_customer_crm_docs_tbl") {
		forms.sa_customer_crm_docs_tbl.refreshUI();
	}
	else if (sCurrentForm != "sa_customer_contact_dtl_contact") {
		forms.sa_customer_note_tbl.refreshUI();
	}
	forms.sa_customer_dtl_accountinfo.renderTaxOptions();
	forms.sa_customer_dtl_accountinfo.handleCustFOB();
	forms.sa_customer_environmental_reporting_tbl.syncItemClassTypes();
	globals.avSales_selectedCustomerUUID = cust_id;
	scopes.avSystem.selectedDivPlantObjectUUID = cust_id;
	scopes.avSystem.selectedDivPlantObjectType = 'CUST';
}

/**
 * Callback method when the user changes tab in a tab panel or divider position in split pane.
 *
 * @param {Number} previousIndex index of tab shown before the change
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"54E97A3E-E079-4A76-AD48-D8799174B39C"}
 */
function onTabChange(previousIndex, event)
{
	
	//Set the appropriate Customers security privileges depending on which tab we are on
	var bHasRightNew = false;
	var bHasRightEdit = false;
	var bHasRightDelete = false;
	var bHasRightDuplicate = false;
	var bHasRightPrint = false;
	var bHasRightFunction = false;
	var bHasRightExport = false;
	
	if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_dtl_address') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_contact_dtl_contact') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_address_dtl') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_dtl_accountinfo') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_crm_docs_tbl') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sys_note_tbl') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sa_customer_udf_dtl') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	} else if (scopes.globals.avUtilities_tabGetFormName(controller.getName(), "tabs_365", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_365")) === 'sys_object_div_dtl') {
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	}
	
	if (globals["avSecurity_checkForUserRight"]('Customers', 'customers_history_tab', globals.avBase_employeeUserID)) {
	    globals.avUtilities_tabAdd("sa_customer_dtl","tabs_365","sa_pack_history_tbl","avanti.lbl.History",null,null);
	    (scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_new', globals.avBase_employeeUserID)) ? bHasRightNew = true : bHasRightNew = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_edit', globals.avBase_employeeUserID)) ? bHasRightEdit = true : bHasRightEdit = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_delete', globals.avBase_employeeUserID)) ? bHasRightDelete = true : bHasRightDelete = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_duplicate', globals.avBase_employeeUserID)) ? bHasRightDuplicate = true : bHasRightDuplicate = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_print', globals.avBase_employeeUserID)) ? bHasRightPrint = true : bHasRightPrint = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_methods', globals.avBase_employeeUserID)) ? bHasRightFunction = true : bHasRightFunction = false;
		(scopes.globals.avSecurity_checkForUserRight("Customers", 'btn_export', globals.avBase_employeeUserID)) ? bHasRightExport = true : bHasRightExport = false;
	}
	else {
		globals["avUtilities_tabRemove"]("sa_customer_dtl","tabs_365","avanti.lbl.History");
	}

	forms.svy_nav_fr_buttonbar_browser.elements.btn_new.enabled = bHasRightNew;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = bHasRightEdit;	
	forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = bHasRightDelete;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = bHasRightDuplicate;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_print.enabled = bHasRightPrint;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_methods.enabled = bHasRightFunction;
	forms.svy_nav_fr_buttonbar_browser.elements.btn_export.enabled = bHasRightExport;
	
	// save before moving to the next tab if the user does not have rights to be in edit mode
	if (!scopes.avUtils.isNavModeReadOnly() && globals.nav.mode != scopes.avUtils.ENUM_NAV_MODE.Add && !bHasRightEdit) {
	    globals.svy_nav_dc_onClick(event,'dc_save', 'svy_nav_fr_buttonbar_browser');
	}
	
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"299626D0-F590-4245-87A4-6DEF5DCBD9E8"}
 */
function onShow(firstShow, event) {
    //remove the Division & Plants if the filter is not enabled
    if (firstShow && !globals.avBase_getSystemPreference_Number(79)) scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "tabs_365", 8);

    elements.cust_date_created.format = globals.avBase_dateFormat;
    if (utils.hasRecords(sa_customer_to_sys_organization) && sa_customer_to_sys_organization.org_pti_tokens_enabled) {
        elements.lbl_pti_token.visible = true;
        elements.pti_token.visible = true;
    }
    else {
        elements.lbl_pti_token.visible = false;
        elements.pti_token.visible = false;
    }
    setNoteSource();
    refreshUI();

    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ) { 
        globals.avUtilities_setFormEditMode(event.getFormName(), "edit");
    }
    else {
        globals.avUtilities_setFormEditMode(event.getFormName(), "browse");
    }
    
    application.output("vl name: " + elements.cust_parent_cust_id.valuelist.name)
    
    return;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"BEC9B2F9-D63D-4DBF-8CF4-CB3C74D1D89B"}
 */
function onActionCreateActivity(event) {
	/** @type{JSRecord<db:/avanti/crm_activity>} */
	var activity_rec = forms['crm_activity_dtl_window'].foundset.getRecord(forms['crm_activity_dtl_window'].foundset.newRecord());
	activity_rec.activity_regarding = i18n.getI18NMessage('avanti.lbl.activityRegarding_customer');
	activity_rec.activity_customer_id = foundset.cust_id;
	
	updateCRMActivityWindow();
	
	globals.DIALOGS.showFormInModalDialog(forms['crm_activity_dtl_window'], -1, -1, -1, -1, i18n.getI18NMessage('avanti.lbl.createActivity'), false, false, 'newPreferenceFolder', true)
}

/**
 * Handle changed data.
 *
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"4CE1B449-6BF3-4505-B708-04A3A32A2632"}
 */
function onDataChangeType(oldValue, newValue, event) {
	
	if(newValue == 'C' && !globals.avSecurity_checkForUserRight('Customers', 'update_to_customer', globals.avBase_employeeUserID)) {
		cust_type = oldValue
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.tooltip.noPermissions'))
	}
	
	var sAccountingIntegrationType = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration)

    if (sAccountingIntegrationType == "GP") {
		if(newValue == 'C' && (oldValue == 'S' || oldValue == 'P')) {
			addOrUpdateCustomer(foundset.getSelectedRecord());
		}
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"C88FB7E8-A19A-4201-92BD-210FECB21884"}
 */
function onDataChangeCode(oldValue, newValue, event) {
	return true;
}

/**
 * Check if customer exists in GP and if it does, update, otherwise create new.
 * @param {JSRecord<db:/avanti/sa_customer>} rCustomer
 *
 * @properties={typeid:24,uuid:"02B10A47-41F3-470B-82E6-239D554BA212"}
 */
function addOrUpdateCustomer(rCustomer) {
	if(scopes.avAccounting.checkCustomerCodeExists(rCustomer.cust_code)) {
		askUserToUpdateCustomerInGP(rCustomer);
	} else {
		askUserToCreateCustomerInGP(rCustomer);
	}
}

/**
 * Prompt user to add a customer in Dynamics GP
 * @param {JSRecord<db:/avanti/sa_customer>} rCustomer
 *
 * @properties={typeid:24,uuid:"CFA16CE2-9AC3-40E9-BD69-B8FB8CB71FED"}
 */
function askUserToCreateCustomerInGP(rCustomer) {
	var loAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.dialog.confirmUpdate'),i18n.getI18NMessage('avanti.dialog.addDynamicsGP_customer'),i18n.getI18NMessage('avanti.dialog.yes'),i18n.getI18NMessage('avanti.dialog.no'));
	if(loAnswer == i18n.getI18NMessage('avanti.dialog.yes')) {
		var response = scopes.avAccounting.addCustomerToDynamicsGP(rCustomer)
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),response, i18n.getI18NMessage('avanti.dialog.okay'));
	}
}

/**
 * Prompt user to update customer in Dynamics GP
 * @param {JSRecord<db:/avanti/sa_customer>} rCustomer
 *
 * @properties={typeid:24,uuid:"B0BBB228-307B-458D-906D-D1D574597103"}
 */
function askUserToUpdateCustomerInGP(rCustomer) {
	var loAnswer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.dialog.confirmUpdate'),i18n.getI18NMessage('avanti.dialog.updateDynamicsGP_customer'),i18n.getI18NMessage('avanti.dialog.yes'),i18n.getI18NMessage('avanti.dialog.no'));
	if(loAnswer == i18n.getI18NMessage('avanti.dialog.yes')) {
		try { 
			scopes.avAccounting.updateCustomerInDynamicsGP(rCustomer)
		} catch (ex) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
		}
		
		
	}
}

/**
 * @properties={typeid:24,uuid:"1D35353F-78F8-4D60-8349-4D1B29124CC5"}
 */
function refreshUI() {
	if(globals.avBase_getSystemPreference_String(121) == 1) {
		elements.bypass_deactivation.visible = true
	} 
	if (globals["avSecurity_checkForUserRight"]('Customers', 'customers_history_tab', globals.avBase_employeeUserID))
	{
		   globals.avUtilities_tabAdd("sa_customer_dtl","tabs_365","sa_pack_history_tbl","avanti.lbl.History",null,null);
		   
	}	
	else {
		globals["avUtilities_tabRemove"]("sa_customer_dtl","tabs_365","avanti.lbl.History");
	}   
}

/**
 * Clears the plant_id when div_id is modified
 * 
 * <AUTHOR> Keen
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CE3C4F3C-5235-472C-9317-2E02F2CE7D5F"}
 */
function onDataChangeDiv(oldValue, newValue, event) {
	//clear the plant_id when changing div_id to prevent a plant_id from another div to remain
	plant_id = null;
	
	return true
}

/**
 *
 * @param {JSEvent} event
 *
 * @return
 * @properties={typeid:24,uuid:"072A949D-7B95-4248-9CF6-15C98E379EA4"}
 */
function onHide(event) {

	return _super.onHide(event);
}

/**
 * Write audiot history record for customer
 * 
 * @param {JSRecord<db:/avanti/sa_customer>} rCustomer
 *
 * @properties={typeid:24,uuid:"D1791430-B862-4717-A967-78ADBF012637"}
 */
function writeAuditHistoryRecordForCustomer(rCustomer) {
	var rAuditHistory;
	var dsData;
	var dsData1;
	var date;
	if (!utils.hasRecords(rCustomer.sa_customer_to_audit_history)) {
		rCustomer.sa_customer_to_audit_history.newRecord();
		rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
		rAuditHistory.audit_module_name = "Customer";
		rAuditHistory.original_record_number = rCustomer.cust_code;
		date = rAuditHistory.created_date;
		rAuditHistory.audit_field_name = "Customer Code";
		if (rCustomer.cust_code != null) {
			rAuditHistory.audit_field_value = rCustomer.cust_code;
		} else {
			rAuditHistory.audit_field_value = '';
		}

		databaseManager.saveData(rAuditHistory);
		rCustomer.sa_customer_to_audit_history.duplicateRecord();
		rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
		rAuditHistory.created_date = date;
		rAuditHistory.audit_field_name = "Customer Name";
		if (rCustomer.cust_name != null) {
			rAuditHistory.audit_field_value = rCustomer.cust_name;
		} else {
			rAuditHistory.audit_field_value = '';
		}
        databaseManager.saveData(rAuditHistory);
		if (rCustomer.custcat_id != null) {
			var oSQL1 = { };
			oSQL1.sql = "SELECT custcat_desc\
		             FROM   sa_customer_category\
		             WHERE   custcat_id = ? ";

			oSQL1.args = [rCustomer.custcat_id.toString()];

			dsData = globals["avUtilities_sqlDataset"](oSQL1);
			if (dsData.getMaxRowIndex() >= 1) {
				rCustomer.sa_customer_to_audit_history.duplicateRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.created_date = date;
				rAuditHistory.audit_field_name = "Customer Category";
				rAuditHistory.audit_field_value = dsData.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}
		} else {
			rCustomer.sa_customer_to_audit_history.duplicateRecord();
			rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
			rAuditHistory.created_date = date;
			rAuditHistory.audit_field_name = "Customer Category";
			rAuditHistory.audit_field_value = '';
			databaseManager.saveData(rAuditHistory);
		}
		if (rCustomer.custclass_id != null) {
			oSQL1 = { };
			oSQL1.sql = "SELECT custclass_desc\
			         FROM   sa_customer_class\
			          WHERE   custclass_id = ? ";

			oSQL1.args = [rCustomer.custclass_id.toString()];

			dsData = globals["avUtilities_sqlDataset"](oSQL1);
			if (dsData.getMaxRowIndex() >= 1) {
				rCustomer.sa_customer_to_audit_history.duplicateRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.created_date = date;
				rAuditHistory.audit_field_name = "Customer Class";
				rAuditHistory.audit_field_value = dsData.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}
		} else {
			rCustomer.sa_customer_to_audit_history.duplicateRecord();
			rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
			rAuditHistory.created_date = date;
			rAuditHistory.audit_field_name = "Customer Class";
			rAuditHistory.audit_field_value = '';
			databaseManager.saveData(rAuditHistory);
		}
		if (rCustomer.terms_id != null) {
			oSQL1 = { };
			oSQL1.sql = "SELECT terms_desc\
			             FROM   sys_payment_terms\
			             WHERE   terms_id = ? ";

			oSQL1.args = [rCustomer.terms_id.toString()];

			dsData = globals["avUtilities_sqlDataset"](oSQL1);
			if (dsData.getMaxRowIndex() >= 1) {
				rCustomer.sa_customer_to_audit_history.duplicateRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.created_date = date;
				rAuditHistory.audit_field_name = "Payment Terms";
				rAuditHistory.audit_field_value = dsData.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}
		} else {
			rCustomer.sa_customer_to_audit_history.duplicateRecord();
			rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
			rAuditHistory.created_date = date;
			rAuditHistory.audit_field_name = "Payment Terms";
			rAuditHistory.audit_field_value = '';
			databaseManager.saveData(rAuditHistory);
		}

	} else {
		var oSQL = { };
		oSQL.sql = "SELECT audit_field_value \
                   FROM audit_history \
                   WHERE audit_field_name like ? and link_to_record_id = ? \
                  ORDER BY created_date DESC  		"

		oSQL.args = ["Customer Code", rCustomer.cust_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (!rCustomer.cust_code && dsData1.getValue(1, 1)) {
			if (dsData1.getValue(1, 1) != "Data deleted by user") {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Customer Code";
				rAuditHistory.audit_field_value = 'Data deleted by user';
				databaseManager.saveData(rAuditHistory);
			}
		} else if (dsData.getValue(1, 1) != rCustomer.cust_code) {
			rCustomer.sa_customer_to_audit_history.newRecord();
			rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
			rAuditHistory.audit_module_name = "Customer";
			rAuditHistory.original_record_number = rCustomer.cust_code;
			if (date != null) {
				rAuditHistory.created_date = date;
			} else {
				date = rAuditHistory.created_date;
			}

			rAuditHistory.audit_field_name = "Customer Code";
			rAuditHistory.audit_field_value = rCustomer.cust_code;
			databaseManager.saveData(rAuditHistory);
		}

		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC  		"

		oSQL.args = ["Customer Name", rCustomer.cust_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (!rCustomer.cust_name && dsData.getValue(1, 1)) {
			if (dsData1.getValue(1, 1) != "Data deleted by user") {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}

				rAuditHistory.audit_field_name = "Customer Name";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);
			}

		} else if (dsData.getValue(1, 1) != rCustomer.cust_name) {
			rCustomer.sa_customer_to_audit_history.newRecord();
			rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
			rAuditHistory.audit_module_name = "Customer";
			rAuditHistory.original_record_number = rCustomer.cust_code;
			if (date != null) {
				rAuditHistory.created_date = date;
			} else {
				date = rAuditHistory.created_date;
			}

			rAuditHistory.audit_field_name = "Customer Name";
			rAuditHistory.audit_field_value = rCustomer.cust_name;
			databaseManager.saveData(rAuditHistory);
		}
		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC  		"
		oSQL.args = ["Customer Category", rCustomer.cust_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rCustomer.custcat_id && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Customer Category";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);
			}

		} else if (rCustomer.custcat_id != null) {
			var oSQL2 = { };
			oSQL2.sql = "SELECT custcat_desc\
		             FROM   sa_customer_category\
		             WHERE   custcat_id = ? ";

			oSQL2.args = [rCustomer.custcat_id.toString()];
			/**@type {JSDataSet} **/
			dsData1 = globals["avUtilities_sqlDataset"](oSQL2);

			if (dsData.getValue(1, 1) != dsData1.getValue(1, 1)) {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Customer Category";
				rAuditHistory.audit_field_value = dsData1.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}
		}
		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC  		"
		oSQL.args = ["Customer Class", rCustomer.cust_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rCustomer.custclass_id && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Customer Class";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);
			}

		} else if (rCustomer.custclass_id != null) {
			oSQL2 = { };
			oSQL2.sql = "SELECT custclass_desc\
				             FROM   sa_customer_class\
				             WHERE   custclass_id = ? ";
			oSQL2.args = [rCustomer.custclass_id.toString()];
			/**@type {JSDataSet} **/
			dsData1 = globals["avUtilities_sqlDataset"](oSQL2);

			if (dsData.getValue(1, 1) != dsData1.getValue(1, 1)) {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Customer Class";
				rAuditHistory.audit_field_value = dsData1.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}
		}
		oSQL.sql = "SELECT audit_field_value \
            FROM audit_history \
            WHERE audit_field_name like ? and link_to_record_id = ? \
           ORDER BY created_date DESC  		"
		oSQL.args = ["Payment Terms", rCustomer.cust_id.toString()];
		/**@type {JSDataSet} **/
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (!rCustomer.terms_id && dsData.getValue(1, 1)) {
			if (dsData.getValue(1, 1) != "Data deleted by user") {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Payment Terms";
				rAuditHistory.audit_field_value = "Data deleted by user";
				databaseManager.saveData(rAuditHistory);
			}

		} else if (rCustomer.terms_id != null) {
			oSQL2 = { };
			oSQL2.sql = "SELECT terms_desc\
	            FROM   sys_payment_terms\
	            WHERE   terms_id = ? ";

			oSQL2.args = [rCustomer.terms_id.toString()];

			/**@type {JSDataSet} **/
			dsData1 = globals["avUtilities_sqlDataset"](oSQL2);
			if (dsData.getValue(1, 1) != dsData1.getValue(1, 1)) {
				rCustomer.sa_customer_to_audit_history.newRecord();
				rAuditHistory = rCustomer.sa_customer_to_audit_history.getSelectedRecord();
				rAuditHistory.audit_module_name = "Customer";
				rAuditHistory.original_record_number = rCustomer.cust_code;
				if (date != null) {
					rAuditHistory.created_date = date;
				} else {
					date = rAuditHistory.created_date;
				}
				rAuditHistory.audit_field_name = "Payment Terms";
				rAuditHistory.audit_field_value = dsData1.getValue(1, 1);
				databaseManager.saveData(rAuditHistory);
			}

		}

	}
}

/**
*
* @param _foundset
*
* @properties={typeid:24,uuid:"36764324-168D-4ED0-99FD-C9A452C7886C"}
*/
function dc_new_post(_foundset) {
	_super.dc_new_post(_foundset);
	
	// sl-11649 - added this code here as its called from svy_nav_lkp_showRecord() when 'new record' selected on lookup
	if(globals.avSecurity_checkForUserRight('Customers', 'limit_to_prospect_suspect', globals.avBase_employeeUserID)){
		application.setValueListItems('vl_CustomerType_detailView',new Array('Prospect','Suspect'),new Array('P','S'));
		cust_type = application.getValueListDisplayValue('vl_CustomerType_detailView','P');
	} 
	else{
		application.setValueListItems('vl_CustomerType_detailView',new Array('Customer','Prospect','Suspect'),new Array('C','P','S'));
	}
}

/**
 * Handle focus lost event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"083FA1E1-8031-434C-A596-B55EC11D7E5D"}
 * @AllowToRunInFind
 */
function onFocusLost_custCode(event) {
    if (globals.nav.mode == "add" && cust_code != null) {
        //SL-13447 remove div/plant filters before checking for duplicate customers
        if (globals.avBase_getSystemPreference_Number(79)) {
            globals.avBase_removeTableFilter();
        }

        var fsCust = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_customer');
        if (fsCust.find()) {
            fsCust.cust_code = cust_code;
            if (fsCust.search() > 0) {
                globals.DIALOGS.showWarningDialog('i18n:svy.fr.lbl.excuse_me', "Customer code: " + cust_code + " already exists", i18n.getI18NMessage('avanti.dialog.ok'));
                cust_code = null;
            }
        }
    }
}

/**
 * Handle focus lost event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E9553DE8-8E6D-4620-93AE-92D989B7DA30"}
 */
function onFocusLost_custName(event) {
	if (!cust_code 
			&& cust_name 
			&& globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration) == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoQuickBooksOnline) {
		cust_code = scopes.globals.generateCustomerCode(cust_name);
	}
}
