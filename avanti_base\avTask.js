/**
 * @properties={typeid:35,uuid:"05152DC1-C361-46E7-962E-E07B6D462BFB",variableType:-4}
 */
var minChargeSecondRecalc = false;

/**
 * @ enum 
 * @ public
 * 
 * @properties={typeid:35,uuid:"9A69EA80-AFE6-425E-9EEE-72CF6FF1DA9D",variableType:-4}
 */
var TASK_PRICING_METHOD = {
    Markup: 'M', 
    Chart: 'C'
};

/**
 * @ enum 
 * @ public
 * 
 * @properties={typeid:35,uuid:"145082B9-4EE7-46ED-A857-1E352E24F38D",variableType:-4}
 */
var TASK_CLICK_CALC_BASED_ON = {
    Impressions: 'I', 
    LinearFeet: 'F',
	Meters: 'M'
};

/**
 * @ enum 
 * @ public
 * 
 * @properties={typeid:35,uuid:"EBC047A0-86FD-4A28-A3B8-0C9F678CAECD",variableType:-4}
 */
var TASK_RUN_TYPE_STDIDS = {
    Fold: 'd2d40fde-c5fc-40dc-b66f-5ea20cb43b9a', 
    Sheet: '5087dff2-e5ef-4b18-8b34-22a83cbaf836',
    Fan: '967e02a6-9d83-4a22-812e-4f8ba638e075',
    Roll: 'a957be96-ac60-4cfc-997a-dfa035081db8'   
};

/**
 * @ enum 
 * @ public
 * 
 * @properties={typeid:35,uuid:"CBA55859-D0FD-46D4-989A-EEE12479F101",variableType:-4}
 */
var WEB_TOWER_CONFIG = {
    Perfecting: null, // the default
    Sheetwise: "S"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"49D3E97D-5B42-4C82-AE6E-C29D03C13780",variableType:-4}
 */
var TASK_BINDERY_TYPE = {
    SaddleStich      : "S",
    PerfectBinding   : "P",
    Stapling         : "T"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"5F1664D2-7181-4E9F-A53F-5E1F255F5BE9",variableType:-4}
 */
var TASK_BINDING_EDGE = {
    FS1              : "FS1",
    FS2              : "FS2"
};

 /**
  * @ enum 
  * @ public
  *
  * @properties={typeid:35,uuid:"********-AABF-4604-87A7-0FF2036C608B",variableType:-4}
  */
 var TASK_MATERIAL_TYPE = {
     Gumming_Adhesive       : "GA",
     Envelope_Adhesive      : "EA",
     Skid                   : "SK",
     Rollcore               : "RC",
     Patch_Material_Front   : "PF",
     Patch_Material_Back    : "PB",
     Patch_Adhesive         : "PA",
     Die_Press_Die          : "DI",
     Die_Press_Foil         : "FO",
     Die_Press_Wipe         : "WI",
     Die_Press_Counter      : "CO",
     Coating_Plate_Blanket  : "CP"
 };
 
/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"ED7E55AF-939C-44DF-BA79-51A2F41E97E6",variableType:-4}
 */
var TASK_SHIPPING_BY = {
    Weight              : "W",
    Height              : "H",
    Best                : "B"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"58F5E18B-9C8D-4531-9F49-43BE629EF821",variableType:-4}
 */
var TASK_ROLL_TO_SHEET_CONVERTER_OPTIONS = {
    Inline              : "inline",
    Offline             : "offline"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"7100FE30-B78E-45C6-805F-C88946AABD0B",variableType:-4}
 */
var TASK_CALC_METHOD = {
    FullSheets              : "S",
    ImageArea               : "I"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"A2FE1A5F-4F7E-4B91-8861-964B73DB9D04",variableType:-4}
 */
var TASK_OPERATION_KEY = {
    Buyout                  : "i18n:avanti.lbl.buyout",
    BuyoutSetup             : "i18n:avanti.lbl.buyout_setup",
    Helper                  : "i18n:avanti.lbl.helper",
    InternalFreight         : "i18n:avanti.lbl.internalFreight",
    InternalLabour          : "i18n:avanti.lbl.internalLabor",
    Postage                 : "i18n:avanti.lbl.postage",
    SupplierFreight         : "i18n:avanti.lbl.supplierFreight",
    TaskClickCharge         : "i18n:avanti.lbl.task_clickCharge",
    TaskExtraPLateMakeReady : "i18n:avanti.lbl.task_extraPlMR",
    TaskFirstMakeReady      : "i18n:avanti.lbl.task_firstMR",
    TaskHangDie             : "i18n:avanti.lbl.task_hangDie",
    TaskInk                 : "i18n:avanti.lbl.task_ink",
    TaskPerfScoreMakeReady  : "i18n:avanti.lbl.task_perfScoreMR",
    TaskPlating             : "i18n:avanti.lbl.task_plating",
    TaskRepeatMakeReady     : "i18n:avanti.lbl.task_repeatMR",
    TaskRun                 : "i18n:avanti.lbl.task_run",
    TaskSetup               : "i18n:avanti.lbl.task_setup",
    TaskSetupPress          : "i18n:avanti.lbl.task_setupPress",
    TaskWashUp              : "i18n:avanti.lbl.task_washup",
    TaskWorkTurnMakeReady   : "i18n:avanti.lbl.task_workTurnMR"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"6ED2BBCE-9866-43A3-A116-DF2F5C860FD3",variableType:-4}
 */
var TASK_CALC_TYPE_EDGE_FINISHING = {
    LinearFeet              : "2",
    LinearInches            : "4",
    LinearMeters            : "2",
    LinearMillimeters       : "4"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"34E19552-4CCB-476F-A853-BDC101E04C9D",variableType:-4}
 */
var TASK_CALC_TYPE = {
    SingleUnit              : "U",
    Forms                   : "F",
    Pages                   : "P",
    ProductionQuantity      : "Q",
    Sheets                  : "S",
    ProdQtyOrSheetQtyOption : "E",
    Default                 : "I",
    SqIn                    : "1",
    LinearFeet              : "2",
    LinearInches            : "4",
    LinearMeters            : "2",
    LinearMillimeters       : "4",
    SqFeet                  : "3",
    BedsPerHour             : "4",
    InchesPerMin            : "1",
    FeetPerHour             : "2",
    FeetPerMin              : "3",
    InchesPerHour           : "4",
    MillimetersPerMin       : "1",
    MetersPerHour           : "2",
    MetersPerMin            : "3",
    MillimetersPerHour      : "4"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"33AE0E4B-13E2-48D5-9BD4-596C15D10BFF",variableType:-4}
 */
var TASK_PAPER_TYPES = {
    Sheets  : "S",
    Rolls   : "R",
    Both    : "B"
};

/**
 * @properties={typeid:35,uuid:"5FC8C416-3C1A-47D2-91D4-9300C046D32E",variableType:-4}
 */
var CUTTER_TYPES = {
    PreTrim 		: "PT",
    FinalTrim 		: "FT",
    InterimCutter 	: "IC",
    SignatureCutter : "SC"
};

/**
 * @public 
 * 
 * @type {Array<JSRecord<db:/avanti/sa_task>>}
 * 
 * @properties={typeid:35,uuid:"63E89C1E-BD4D-48AB-B88A-077D9F47B79B",variableType:-4}
 */
var aPressFTCutters = [];

/**
 * @public 
 * 
 * @type {Array<JSRecord<db:/avanti/sa_task>>}
 * 
 * @properties={typeid:35,uuid:"B163ADA2-DA9C-4ED1-A128-F55CFFF0C640",variableType:-4}
 */
var aPressPTCutters = [];

/**
 * @public 
 * 
 * @type {Array<{nDim1:Number, nDim2:Number, nCuts:Number}>}
 * 
 * @properties={typeid:35,uuid:"A3B90576-9019-43AF-AEF2-F3276B0B6768",variableType:-4}
 */
var aPressFTCutterInputs = [];

/**
 * @public 
 * 
 * @type {Array<{nDim1:Number, nDim2:Number, nCuts:Number}>}
 * 
 * @properties={typeid:35,uuid:"3C3511E2-4BB1-441B-B356-36A539F4E4BF",variableType:-4}
 */
var aQtyFTCutterInputs = [];

/**
 * @public 
 * 
 * @type {Array<{nDim1:Number, nDim2:Number, nCuts:Number}>}
 * 
 * @properties={typeid:35,uuid:"EBC455E1-ECC6-4DCE-AE68-5E4D919BA602",variableType:-4}
 */
var aPressPTCutterInputs = [];

/**
 * @public 
 * 
 * @type {Array<{nDim1:Number, nDim2:Number, nCuts:Number}>}
 * 
 * @properties={typeid:35,uuid:"B8933E9C-F0E7-4358-8DE1-3AB5A259FA90",variableType:-4}
 */
var aQtyPTCutterInputs = [];

/**
 * @type {Array<{priceRuleMethod:String, price:Number, priceUnits: String, unitPrice: Number, priceQty: Number, uPriceRuleDetailUUID: UUID}>}
 * @properties={typeid:35,uuid:"8FE3C698-99A3-4820-8043-D5B4CB221E1E",variableType:-4}
 */
var oPressTaskPriceRule = {}

/**
 * @type {String}
 * @properties={typeid:35,uuid:"5EBE9E84-28A3-4BEB-B7AC-9252C43C85B3"}
 */
var selectedTaskCutoffID = null;

/**
 * @type {String}
 * @properties={typeid:35,uuid:"DC22589A-1404-4AC3-A08F-E619AF004644"}
 */
var selectedTaskSpeedID = null;

/**
 * @type {String}
 * @properties={typeid:35,uuid:"CA588068-B551-4C46-8CED-C532E80788D9"}
 */
var selectedTaskPaperID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"345803FF-F68A-4B9A-9E24-8610637FFAB8"}
 */
var sortTasks = "sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas_outer_join.systaskfunc_desc asc,  sequence_nr asc";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"23A59CF4-7CBE-4C3F-BDF6-BFEFD61346A9"}
 */
var selectedGangTaskID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D4C1C7AA-92DD-4A04-B094-3C1627A8467E"}
 */
var selectedGangTaskCutType = null;

/**@type {{nMrkUp:Number, bItemPriceFound: Boolean, bTaskItemPriceFound: Boolean, uPriceRuleDetailUUID: UUID, sPriceMethod: String, nActualPrice: Number, sActualPriceUnits: String, nActualUnitPrice: Number, nActualPriceQty: Number, nDiscountPct: Number, sBreakMethod: String}} *

 * @properties={typeid:35,uuid:"5BC9467A-67EB-4BDC-9E12-84150013909C",variableType:-4}
 */
var oListPrice;

/**
 * Constructor function
 *
 * <AUTHOR> Dotzlaw
 * @since 2015-02-03
 *
 * @return
 * @properties={typeid:24,uuid:"62769B74-0835-4F8C-B1F2-B80A89BD8B42"}
 */
function ObjTaskQty(){
	var oTaskQty = {
		ordrevdstqty_id_orig			: "",
//		ordrevdstqty_t_cost_item_over	: 0.0,
		ordrevdstqty_mrk_pct_item_over	: 0.0,
//		ordrevdstqty_t_item_over		: 0.0,
//		ordrevdstqty_t_cost_lab_over	: 0.0,
		ordrevdstqty_mrk_pct_lab_over	: 0.0,
//		ordrevdstqty_t_lab_over			: 0.0,
		item_id							: "",
//		ordrevdstqty_t_cost_over		: 0.0,
//		ordrevdstqty_t_over				: 0.0,
		ordrevdstqty_t_mrk_pct_over		: 0.0,
		taskfldopt_id_perf				: "",
		taskfldopt_id_score				: "",
		taskfldopt_id_inkjetting		: "",
		taskfldopt_id_numbering			: "",
		taskfldopt_id_other1			: "",
		taskfldopt_id_other2			: "",
		taskfldopt_id_other3			: "",
		taskstd_trimmer_option			: 0,
		ordrevdstqty_qty_item2			: 0.0,
		item_id_2						: "",
//		ordrevdstqty_t_cost_item_over2	: 0.0,
		ordrevdstqty_mrk_pct_item_ove2	: 0.0,
//		ordrevdstqty_t_item_over2		: 0.0,
		ordrevdstqty_overlap			: 0.0,
//		ordrevdstqty_click_cost_over	: 0.0,
//		ordrevdstqty_click_price_over	: 0.0,
		ordrevdstqty_click_mrk_over		: 0.0,
//		ordrevdstqty_spoils_over		: 0.0,
		ordrevdstqty_qty_task_over		: 0.0,
		ordrevdstqty_qty_item_over		: 0.0,
		ordrevdstqty_time_setup_over	: 0.0,
//		ordrevdstqty_time_run_over		: 0.0,
		taskmachine_id					: "",
		taskspoil_id					: "",
		tasksetup_id					: "",
		taskmachine_id_over				: "",
		taskspoil_id_over				: "",
		tasksetup_id_over				: "",
		ordrevdstqty_linear_inches		: 0.0,
		item_id_die						: "",
		ordrevdstqty_width				: 0.0,
		ordrevdstqty_feed_edge			: 0.0,
		ordrevdstqty_sheet_size_over	: "",
		ordrevdstqty_up_over			: 0.0,
		taskacc_id_coverdeck			: "",
		taskacc_id_handfeeder			: "",
		taskacc_id_cardfeeder			: "",
		ordrevdstqty_flg_coverdeck		: 0,
		taskacc_id_facetrim				: "",
		ordrevdstqty_flg_handfeeder		: 0,
		ordrevdstqty_flg_cardfeeder		: 0,
		ordrevdstqty_flg_facetrim		: 0,
//		ordrevdstqty_price_help			: 0,
//		ordrevdstqty_price_intlabor		: 0,
//		ordrevdstqty_price_run			: 0,
//		ordrevdstqty_price_setup		: 0,
//		ordrevdstqty_oprice_help		: 0,
//		ordrevdstqty_oprice_intlabor	: 0,
//		ordrevdstqty_oprice_setup		: 0,
//		ordrevdstqty_oprice_run			: 0,
		ordrevdstqty_omrk_addmr 		: 0,
		ordrevdstqty_omrk_firstmr 		: 0,
		ordrevdstqty_omrk_wtmr 			: 0,
		ordrevdstqty_omrk_wash 			: 0,
		ordrevdstqty_omrk_perf 			: 0,
		ordrevdstqty_omrk_plate 		: 0,
		ordrevdstqty_omrk_platemr 		: 0,
		ordrevdstqty_omrk_explate 		: 0,
		ordrevdstqty_omrk_explatemr 	: 0,
		ordrevdstqty_omrk_ink 			: 0,
		ordrevdstqty_omrk_inkmr 		: 0,
		ordrevdstqty_omrk_presssetup 	: 0,
//		ordrevdstqty_oprice_firstmr 	: 0,
//		ordrevdstqty_oprice_addmr 		: 0,
//		ordrevdstqty_oprice_wtmr 		: 0,
//		ordrevdstqty_oprice_wash 		: 0,
//		ordrevdstqty_oprice_perf 		: 0,
//		ordrevdstqty_oprice_plate 		: 0,
//		ordrevdstqty_oprice_platemr 	: 0,
//		ordrevdstqty_oprice_explate 	: 0,
//		ordrevdstqty_oprice_explatemr 	: 0,
//		ordrevdstqty_oprice_ink 		: 0,
//		ordrevdstqty_oprice_inkmr 		: 0,
//		ordrevdstqty_oprice_presssetup 	: 0,
//		ordrevdstqty_oprice_click 		: 0,
		ordrevdstqty_omrk_click 		: 0,
		ordrevdstqty_qty_over			: 0,
		ordrevdstqty_overide_coil_size	: 0,
		ordrevdstqty_coil_item_over		: "",
		ordrevdstqty_coil_size_over		: 0,
		ordrevdstqty_num_help_override	: 0,
		ordrevdstqty_omrk_setup 		: 0,
		ordrevdstqty_omrk_run 			: 0,
		ordrevdstqty_qty_task_overuser	: 0,
		ordrevdstqty_mat_multiplier		: 0,
		ordrevdstqty_mat_multiplier_over	: 0
	};
	return oTaskQty;
}

/**
 * Constructor function
 *
 * <AUTHOR> Dotzlaw
 * @since 2015-02-03
 *
 * @return
 * @properties={typeid:24,uuid:"76CD47D4-245E-4B7E-B637-3B8A99EEF2D3"}
 */
function ObjTaskSupplierQty(){
	var oTaskSupplierQty = {
	
		ordrevdstsqty_id_orig		: "",
		ordrevdstsqty_cost			: 0.0,
		ordrevdstsqty_est_uom		: "",
		ordrevdstsqty_freight_out	: 0.0,
		ordrevdstsqty_freight_in	: 0.0,
		ordrevdstsqty_int_time		: 0.0,
		ordrevdstsqty_int_cost		: 0.0,
		ordrevdstsqty_mrkup			: 0.0,
		ordrevdstsqty_mrkup_over	: 0.0,
		ordrevdstsqty_is_selected	: 0,
		ordrevdqty_id_orig			: "",
		ordrevdstqty_id_orig		: "",
		ordrevdstsqty_setup_cost	: 0.0,
		ordrevdstsqty_cost_run		: 0.0,
		ordrevdstsqty_t_cost		: 0.0,
		ordrevdstsqty_t				: 0.0,
		ordrevdstsqty_setup_sheets	: 0,
		ordrevdstsqty_expected_date	: "",
		ordrevdstsqty_frt_in_type	: "",
		ordrevdstsqty_frt_out_type	: "",
		ordrevdstsqty_turnaround_time : 0,
		ordrevdstsqty_t_over		: 0
		
	};
	return oTaskSupplierQty;
}


/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3D0F5650-BD29-4CCB-99B8-D6D98843FA4D"}
 */
var selectedDieID = null;

/**
 * @enum 
 * @public 
 * @properties={typeid:35,uuid:"ED15A93C-E1D0-4EA6-A080-48080E3C1678",variableType:-4}
 */
var TASKTYPEID = {
	ConventionalPress: 1,
	WebPress: 2,
	WideFormatPress: 3,
	GrandFormatPress: 4,
	Ink: 5,
	PrePress: 6,
	FlexoPress: 7,
	ScreenPress: 8,
	DigitalSheetPress: 9,
	DigitalRollPress: 10,
	Folder: 11,
	Binding: 12,
	Cutting: 13,
	Finishing: 14,
	LetterPress: 15,
	PackagingShipping: 16,
	Other: 17,
	Laminating: 18,
	Mounting: 19,
	Drilling: 20,
	WaferSealing: 21,
	Shrinkwrapping: 22,
	Padding: 23,
	Proofing: 24,
	Handwork: 25,
	EdgeBinding: 26,
	EdgeFinishing: 27,
	PackByQty: 29,
	Labeling: 30,
	Inserting: 31,
	InkJetting: 32,
	FolderGluer: 33,
	Postage: 34,
	DieCutting: 35,
	OffsetFolderSheeter: 36,
	OfflineCoating: 37,
	HighDieCutter: 38,
	PaddingFanningCarbonless: 39,
	DiePress: 40,
	Rewinder: 41,
	StampingPress: 42,
	RollToSheetConversion: 43,
	FlexoPlateMaking: 44,
	Plating: 96,
	Material: 97,
	Paper: 98,
	PressRun: 99,
	OutsideService: 100
}

/**
 * An array of all press task types. whenever a new press type is added to the system it needs to be added here.
 * 
 * @public 
 * 
 * @properties={typeid:35,uuid:"7EF38FFD-7962-491B-8E5F-76B0C422ECE9",variableType:-4}
 */
var aPressTaskTypes = [TASKTYPEID.ConventionalPress, 
                       TASKTYPEID.WebPress, 
                       TASKTYPEID.WideFormatPress, 
                       TASKTYPEID.GrandFormatPress,
                       TASKTYPEID.FlexoPress, 
                       TASKTYPEID.ScreenPress, 
                       TASKTYPEID.DigitalSheetPress, 
                       TASKTYPEID.DigitalRollPress, 
                       TASKTYPEID.LetterPress,
                       TASKTYPEID.DiePress,
                       TASKTYPEID.StampingPress,
                       TASKTYPEID.PressRun];

/**
 * These are all the tasks that use spoils - as per calcSectionSpoilsThisQty_new()
 * 
 * @public  
 * 
 * @properties={typeid:35,uuid:"DF99A739-FB0D-48D0-B3B2-7CB46E14B72D",variableType:-4}
 */
var aTaskTypesThatUseSpoils = [TASKTYPEID.Folder, TASKTYPEID.Cutting, TASKTYPEID.Finishing, TASKTYPEID.Binding, TASKTYPEID.Drilling, TASKTYPEID.Other, 
	TASKTYPEID.OutsideService, TASKTYPEID.Laminating, TASKTYPEID.Mounting, TASKTYPEID.WaferSealing, TASKTYPEID.Handwork, TASKTYPEID.EdgeBinding, 
	TASKTYPEID.EdgeFinishing, TASKTYPEID.Labeling, TASKTYPEID.Inserting, TASKTYPEID.InkJetting, TASKTYPEID.FolderGluer, TASKTYPEID.DieCutting, 
	TASKTYPEID.Padding, TASKTYPEID.HighDieCutter, TASKTYPEID.PaddingFanningCarbonless, TASKTYPEID.Rewinder,	TASKTYPEID.OfflineCoating];

/**
 * These are all the tasks that use the Other spoils bucket
 * 
 * @public  
 * 
 * @properties={typeid:35,uuid:"26259B62-582A-4D9C-8EFE-85942F45BD6D",variableType:-4}
 */
var aTaskTypesThatUseOtherSpoils = [TASKTYPEID.Drilling, TASKTYPEID.Padding, TASKTYPEID.Other, TASKTYPEID.Rewinder, TASKTYPEID.OutsideService, 
	TASKTYPEID.WaferSealing, TASKTYPEID.Handwork, TASKTYPEID.EdgeBinding, TASKTYPEID.EdgeFinishing, TASKTYPEID.Labeling, TASKTYPEID.Inserting, 
	TASKTYPEID.InkJetting, TASKTYPEID.FolderGluer, TASKTYPEID.DieCutting, TASKTYPEID.HighDieCutter, TASKTYPEID.PaddingFanningCarbonless, 
	TASKTYPEID.OfflineCoating
];

/**
 * These are all the tasks that use the Other Machine Var Spoils (see calcSectionSpoilsThisQty_new)
 * 
 * @public  
 * 
 * @properties={typeid:35,uuid:"AE50ED40-8302-4251-A8E4-63D8DFE65ED6",variableType:-4}
 */
var aTaskTypesThatUseOtherMachineVarSpoils = [TASKTYPEID.WaferSealing, TASKTYPEID.Handwork, TASKTYPEID.EdgeBinding, TASKTYPEID.EdgeFinishing, 
	TASKTYPEID.Labeling, TASKTYPEID.Inserting, TASKTYPEID.InkJetting, TASKTYPEID.FolderGluer, TASKTYPEID.DieCutting, TASKTYPEID.HighDieCutter, 
	TASKTYPEID.PaddingFanningCarbonless, TASKTYPEID.OfflineCoating];

/**
 * These are all the tasks that use the Finishing spoils bucket
 * 
 * @public  
 * 
 * @properties={typeid:35,uuid:"644D9A18-7D5E-4A5F-8797-55F88AC65906",variableType:-4}
 */
var aTaskTypesThatUseFinishingSpoils = [TASKTYPEID.Finishing, TASKTYPEID.Laminating, TASKTYPEID.Mounting];

/**
 * These are all the tasks that use the Folder spoils bucket
 * 
 * @properties={typeid:35,uuid:"E54DE4BE-2DDE-4E54-BA39-8225580EEB83",variableType:-4}
 */
var aTaskTypesThatUseFolderSpoils = [TASKTYPEID.Folder];

/**
 * These are all the tasks that use the Cutting spoils bucket
 * 
 * @properties={typeid:35,uuid:"065C1E5F-B075-4FDC-A604-72080E70AB5B",variableType:-4}
 */
var aTaskTypesThatUseCutterSpoils = [TASKTYPEID.Cutting];

/**
 * These are all the tasks that use the Binding spoils bucket
 * 
 * @properties={typeid:35,uuid:"9C929E67-7CFE-4200-90D9-4C597C516082",variableType:-4}
 */
var aTaskTypesThatUseBinderSpoils = [TASKTYPEID.Binding];

/**
 * @properties={typeid:24,uuid:"1A8A1864-F018-4507-9899-9A8ADD232C82"}
 */
function TaskQtyOvers () {
	this.ordrevdstqty_t_cost_item_over = null;
	this.ordrevdstqty_mrk_pct_item_over = null;
	this.ordrevdstqty_t_item_over = null;
	this.ordrevdstqty_t_cost_lab_over = null;
	this.ordrevdstqty_mrk_pct_lab_over = null;
	this.ordrevdstqty_t_lab_over = null;
	this.ordrevdstqty_t_cost_over = null;
	this.ordrevdstqty_t_over = null;
	this.ordrevdstqty_t_mrk_pct_over = null;
	this.ordrevdstqty_paper_cost_over = null;
	this.ordrevdstqty_t_cost_item_over2 = null;
	this.ordrevdstqty_mrk_pct_item_ove2 = null;
	this.ordrevdstqty_t_item_over2 = null;
	this.ordrevdstqty_click_mrk_over = null;
	this.ordrevdstqty_spoils_over = null;
	this.ordrevdstqty_qty_task_over = null;
	this.ordrevdstqty_qty_item_over = null;
	this.taskmachine_id_over = null;
	this.ordrevdstqty_time_setup_over = null;
	this.ordrevdstqty_time_run_over = null;
	this.taskspoil_id_over = null;
	this.tasksetup_id_over = null;
	this.ordrevdstqty_click_cost_over = null;
	this.ordrevdstqty_click_price_over = null;
	this.ordrevdstqty_sheet_size_over = null;
	this.ordrevdstqty_up_over = null;
	this.ordrevdstqty_qty_over = null;
	this.ordrevdstqty_num_help_override = null;
	this.ordrevdstqty_coil_item_over = null;
	this.ordrevdstqty_coil_size_over = null;
	this.ordrevdstqty_feed_edge_over = null;
	this.ordrevdstqty_click_price_o_clr = null;
	this.ordrevdstqty_click_price_o_bw = null;
	this.ordrevdstqty_nr_rolls_over = null;
	this.ordrevdstqty_qty_core_over = null;
	this.ordrevdstqty_oprice_setup = null;
	this.ordrevdstqty_omrk_setup = null;
	this.ordrevdstqty_oprice_run = null;
	this.ordrevdstqty_omrk_run = null;

}

/**
 * @properties={typeid:35,uuid:"D9A783C5-5223-4010-BF47-220249AD0B31",variableType:-4}
 */
var oTaskQtyOver = {};

/**
 * @type {String}
 * @public
 * @properties={typeid:35,uuid:"AEDDFEA6-42A6-47B8-AA47-E44B3FE4F541"}
 */
var selectedWorkTypePressID = null;

/**
 * @type {String}
 * @public
 * @properties={typeid:35,uuid:"7D7273B2-5BF8-4153-869F-C5280E7F73F6"}
 */
var selectedTaskOperKey = "";

/**
 * @type {String}
 * @public
 * @properties={typeid:35,uuid:"E503AE55-8984-4D36-9863-EF71AA49F83E"}
 */
var selectedTaskOperID = null;

/**
 * @type {Number}
 * @public
 * @properties={typeid:35,uuid:"06BFC7DC-A89E-4816-8F7C-FE15DC222872",variableType:4}
 */
var selectedTaskTypeID = 0;

/**
 * @type {String}
 * @public
 * @properties={typeid:35,uuid:"84F8408D-EC62-4E72-871F-6B967C9B2B0C"}
 */
var selectedTaskID = null;

/**
 * Get a specific cost link for a task
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-19
 *
 * @param {JSRecord<db:/avanti/sa_task>} rTask - the task
 * @param {String} sKey - the i18n key (like "avanti.lbl.helper")
 *
 * @returns {Number} The rate for the cost link
 *
 * @properties={typeid:24,uuid:"53383C3B-C9CE-4CD0-8599-1FDA6D1EB1D8"}
 * @AllowToRunInFind
 */
function getCostLinkRate (rTask, sKey) {
	
	var nRate = 0,
		iTaskTypeID = rTask.tasktype_id;
	
	if (sKey && sKey.search("i18n:") === -1){
		sKey = "i18n:" + sKey;
	}
	
	selectedTaskOperKey = sKey;
	selectedTaskTypeID = iTaskTypeID;
	
	// GD - 2014-03-19: - PERFTUNE: Check the cache for the markup
	if(globals.avBase_oCalcs && globals.avBase_oCalcs.getCostLinkRate){
		if(globals.avBase_oCalcs.getCostLinkRate[rTask.task_id.toString() + "_" + sKey]) {
			return globals.avBase_oCalcs.getCostLinkRate[rTask.task_id.toString() + "_" + sKey];
		}
	}
		
	if (utils.hasRecords(_to_app_task_operation$selectedtaskoperkey_selectedtasktypeid)){
	    
		selectedTaskOperID = _to_app_task_operation$selectedtaskoperkey_selectedtasktypeid.taskoper_id.toString();
		selectedTaskID = rTask.task_id.toString();

		if (utils.hasRecords(_to_sa_task_cost_link$selectedtaskid_selectedtaskoperid)
				&& utils.hasRecords(_to_sa_task_cost_link$selectedtaskid_selectedtaskoperid.sa_task_cost_link_to_sys_cost_centre)){
			nRate = _to_sa_task_cost_link$selectedtaskid_selectedtaskoperid.sa_task_cost_link_to_sys_cost_centre.cc_total_rate;
		}
	}
	
	// Store result in cache
	if(globals.avBase_oCalcs && globals.avBase_oCalcs.getCostLinkRate) globals.avBase_oCalcs.getCostLinkRate[rTask.task_id.toString() + "_" + sKey] = nRate;
	
	return nRate;
}

/**
 * Setup time for the Offset Folder/Sheeter
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-19
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @returns {Number} Time
 *
 * @properties={typeid:24,uuid:"316ED3A4-F2A6-42FE-AA80-B49675A7E4B7"}
 */
function getTimeSetup_folderSheeterOffset (rOrdTaskQty) {
	
	var nTime = 0;
	
	if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine)){
		
		nTime = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.getRecord(1).taskmachine_setup_min;
		
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$perf)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$perf.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$score)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$score.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$inkjetting)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$inkjetting.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$numbering)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$numbering.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other1)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other1.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other2)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other2.taskfldopt_setup;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other3)) nTime += rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_folder_options$other3.taskfldopt_setup;
	}
	
	return nTime;
}

/**
 * Run time for the Offset Folder/Sheeter
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-19
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @returns {Number} Time
 *
 * @properties={typeid:24,uuid:"77345C08-6050-4F5E-AC37-6975CF92FE2B"}
 */
function getTimeRun_folderSheeterOffset (rOrdTaskQty, rSection) {
	
	var nTime = 0;
//		rOrdTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getSelectedRecord(),
//		rSection = rOrdTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);

	if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)){
		
		nTime = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1).mpress_run_time * 60;
	}
	
	return nTime;
}

/**
 *  Whether or not the sheet fits on the task (used for offline coating)
 *
 * <AUTHOR> Dotzlaw
 * @since Aug 28, 2015
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The Task Qty record
 * @returns {{bPaperFits: Boolean, nW:Number, nL:Number}}
 * @public
 *
 * @properties={typeid:24,uuid:"5DED1015-C352-4800-B10B-17EF87199836"}
 */
function getSheetFits (rTaskQty) {
	
	var rTask = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getSelectedRecord(),
		rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
		rTaskStd = null,
		oSheetSize = {},
		bPaperFits = false,
		nW = 0,
		nL = 0,
		sCalcType = "",
		oObj = {};
	
	if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_task)) rTaskStd = rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.getRecord(1);
	if (rTaskStd ) {
		
		oSheetSize = getIncomingSheetSize(rTask);
		
		sCalcType = rTaskStd.taskstd_calc_type;
		if (sCalcType === "E") {
			
			(oSheetSize.bInterimCutting) ? sCalcType = "S" : sCalcType = "Q";
		}
		
		if ((sCalcType === "S"
			&& (
			(oSheetSize.nSheetWidth >= rTaskStd.taskstd_min_paper_width && oSheetSize.nSheetLength >= rTaskStd.taskstd_min_paper_heigth)
			||
			(oSheetSize.nSheetLength >= rTaskStd.taskstd_min_paper_width && oSheetSize.nSheetWidth >= rTaskStd.taskstd_min_paper_heigth)
			)
			&& (
			(oSheetSize.nSheetWidth <= rTaskStd.taskstd_max_paper_width && oSheetSize.nSheetLength <= rTaskStd.taskstd_max_paper_height)
			||
			(oSheetSize.nSheetLength <= rTaskStd.taskstd_max_paper_width && oSheetSize.nSheetWidth <= rTaskStd.taskstd_max_paper_height)
			))
			|| 
			(sCalcType === "S" 
				&& (!rTaskStd.taskstd_min_paper_width
						|| !rTaskStd.taskstd_min_paper_heigth
						|| !rTaskStd.taskstd_max_paper_width
						|| !rTaskStd.taskstd_max_paper_height))) {
			
			nW = oSheetSize.nSheetWidth;
			nL = oSheetSize.nSheetLength;
			bPaperFits = true;
			
		} else if ((sCalcType === "Q"
					&& (
					(parseFloat(rSection.ordrevds_trim_size_width) >= rTaskStd.taskstd_min_paper_width && parseFloat(rSection.ordrevds_trim_size_length) >= rTaskStd.taskstd_min_paper_heigth)
					||
					(parseFloat(rSection.ordrevds_trim_size_length) >= rTaskStd.taskstd_min_paper_width && parseFloat(rSection.ordrevds_trim_size_width) >= rTaskStd.taskstd_min_paper_heigth)
					)
					&& (
					(parseFloat(rSection.ordrevds_trim_size_width) <= rTaskStd.taskstd_max_paper_width && parseFloat(rSection.ordrevds_trim_size_length) <= rTaskStd.taskstd_max_paper_height)
					||
					(parseFloat(rSection.ordrevds_trim_size_length) <= rTaskStd.taskstd_max_paper_width && parseFloat(rSection.ordrevds_trim_size_width) <= rTaskStd.taskstd_max_paper_height)
					))
					|| 
					(sCalcType === "Q" 
						&& (!rTaskStd.taskstd_min_paper_width
								|| !rTaskStd.taskstd_min_paper_heigth
								|| !rTaskStd.taskstd_max_paper_width
								|| !rTaskStd.taskstd_max_paper_height))) {
			
			nW = parseFloat(rSection.ordrevds_trim_size_width);
			nL = parseFloat(rSection.ordrevds_trim_size_length);
			bPaperFits = true;
			
		} else {
			
			bPaperFits = false;
			
			scopes.avText.showWarning(i18n.getI18NMessage("avanti.dialog.trimSizeTooSmall_msg"),i18n.getI18NMessage("avanti.dialog.trimSizeTooSmall_msg"),[],i18n.getI18NMessage("avanti.dialog.trimSizeTooSmall_title"),true);
		}
	}
	
	oObj.nW = nW;
	oObj.nL = nL;
	oObj.bPaperFits = bPaperFits;
	
	return oObj;
}

/**
 * <AUTHOR> Dotzlaw
 * @since 2015-08-13
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @return
 * @properties={typeid:24,uuid:"1543BA39-B2FA-4938-A6CF-89B0B22AD20A"}
 */
function getTimeRun_offlineCoater (rTaskQty, oEstOrdTask, rSection) {
	
	var nTime = 0,
		rTask = oEstOrdTask.rTask, //rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getSelectedRecord(),
//		rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
		rTaskStd = oEstOrdTask.rEstStdTaskStd,
		nSpeed = 0,
		nSides = 1,
		nDimMax = 0,
		bPaperFits = false,
		nW = 0,
		nL = 0,
		nFeedLength = 0,
		oObj = {},
		nGap = 0; // they have not specified any gap yet
	
//	if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_task)) rTaskStd = rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.getRecord(1);

	// Check to see if the sheet fits
	oObj = getSheetFits(rTaskQty);
	bPaperFits = oObj.bPaperFits;
	nW = oObj.nW;
	nL = oObj.nL;
	
	if (!bPaperFits) return 0;
	
	if (!rSection.ordrevds_trim_size_length || !rSection.ordrevds_trim_size_width) return 0;
	if (!utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_task_machine)) return 0;
	nSpeed = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_speed;
	nSides = rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_sides;
	
	// Get the max edge of the trimmed piece; assuming the same approach as the folder, although they asked for no feed length in the UI yet
	if (rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_speed_unit === 1) {
		
		// compute using in/min
		
		if (rTaskQty.ordrevdstqty_feed_edge_over != null)
		{
			nDimMax = rTaskQty.ordrevdstqty_feed_edge_over + nGap;
			
		} else {
			
			if (nL > nW && nL <= rTaskStd.taskstd_max_paper_width) {
				
				nFeedLength = nW;
				
			} else {
				
				nFeedLength = nL;
			}
			
			rTaskQty.ordrevdstqty_feed_edge = nFeedLength; //scopes.globals["avCalcs_task_setFolderFeedEdge"](rTask);
			nDimMax = rTaskQty.ordrevdstqty_feed_edge + nGap;
			
			// apply speed adjustment from associated tasks; none at this time
//			nSpeed = scopes.globals["getAdjustedSpeed"](rTaskQty,nSpeed);

		}
		
		nTime = rTaskQty.ordrevdstqty_qty_task * nDimMax * nSides / nSpeed;
		
	} else {
		
		// compute using impressions
		
		nTime = rTaskQty.ordrevdstqty_qty_task * nSides / nSpeed * 60;
	}
	
	return nTime;
}

/**
 * <AUTHOR> Dotzlaw
 * @since 2015-08-13
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The Task Qty record
 *
 * @return
 * @properties={typeid:24,uuid:"33E10E1C-E34D-41C9-A9F2-340A0A9A3DB8"}
 */
function getTimeSetup_offlineCoating (rTaskQty) {
	
	if (!utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_task_machine)) return 0;
	
	return rTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_setup_min;
}

/**
 * Gets the run cost for a helper
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {Number} [nTime] - The time the helper is involved in mins (defaults to the run time)
 * @param {Number} [nHelpers] - optional number of helpers if more than one
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"60007FF2-6FF7-44E6-9267-DA3104C72B68"}
 */
function getCost_helper (rOrdTaskQty, nTime, nHelpers, oEstOrdTask) {
	var nRate = 0,
		nCost = 0;
	
	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
	
	if (iTaskTypeID === TASKTYPEID.Finishing 
            || iTaskTypeID === TASKTYPEID.PrePress
			|| iTaskTypeID === TASKTYPEID.Other 
			|| iTaskTypeID === TASKTYPEID.Cutting 
			|| iTaskTypeID === TASKTYPEID.DieCutting 
			|| iTaskTypeID === TASKTYPEID.EdgeFinishing 
			|| iTaskTypeID === TASKTYPEID.Folder 
			|| iTaskTypeID === TASKTYPEID.FolderGluer 
			|| iTaskTypeID === TASKTYPEID.Handwork 
			|| iTaskTypeID === TASKTYPEID.Binding 
			|| iTaskTypeID === TASKTYPEID.EdgeBinding 
			|| iTaskTypeID === TASKTYPEID.OffsetFolderSheeter
			|| iTaskTypeID === TASKTYPEID.OfflineCoating
			|| iTaskTypeID === TASKTYPEID.HighDieCutter
			|| iTaskTypeID === TASKTYPEID.Inserting
			|| iTaskTypeID === TASKTYPEID.InkJetting
			|| iTaskTypeID === TASKTYPEID.PaddingFanningCarbonless
			|| iTaskTypeID === TASKTYPEID.Rewinder
			|| iTaskTypeID === TASKTYPEID.RollToSheetConversion){
		
		if (!nHelpers) nHelpers = 1;
		if (!nTime){
			if (rOrdTaskQty.ordrevdstqty_time_run_over){
				nTime = rOrdTaskQty.ordrevdstqty_time_run_over;
			} else {
				nTime = rOrdTaskQty.ordrevdstqty_time_run;
			}
		}
		nRate = (oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.helper" + '"']) ? oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.helper" + '"'].nRate : 0; //getCostLinkRate (rEstStdTask, "avanti.lbl.helper");
		
		if (nRate && nRate > 0){
			
			if (iTaskTypeID === TASKTYPEID.OffsetFolderSheeter){
				if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_press_pool_task$avsales_selectedrevisionsectionid)){
					// associated task
					nHelpers = rTask.sa_order_revds_task_to_sa_order_revds_press_pool_task$avsales_selectedrevisionsectionid.spresstask_helpers;
					nCost += nHelpers * nTime * nRate / 60;
				} 
			}

			// Get the helpers for the pockets
			nHelpers = getNumHelpers(rOrdTaskQty);
            if (nHelpers) {
                nCost += nHelpers * nTime * nRate / 60;
            }
            else {
                nCost = 0;
            }
			
			// Check for accessory helpers
			if (rOrdTaskQty.ordrevdstqty_flg_cardfeeder === 1 
					&& rOrdTaskQty.taskacc_id_cardfeeder 
					&& utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$cardfeeder)) {
				
				nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$cardfeeder.taskacc_nr_helpers;
				nCost += nHelpers * nTime * nRate / 60;
			}
			if (rOrdTaskQty.ordrevdstqty_flg_coverdeck === 1 
					&& rOrdTaskQty.taskacc_id_coverdeck
					&& utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$coverdeck)){
				
				nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$coverdeck.taskacc_nr_helpers;
				nCost += nHelpers * nTime * nRate / 60;
			}
			if (rOrdTaskQty.ordrevdstqty_flg_handfeeder === 1 
					&& rOrdTaskQty.taskacc_id_handfeeder 
					&& utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$handfeeder)){
					
				nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$handfeeder.taskacc_nr_helpers;
				nCost += nHelpers * nTime * nRate / 60;
			}
			if (rOrdTaskQty.ordrevdstqty_flg_facetrim === 1 
					&& rOrdTaskQty.taskacc_id_facetrim 
					&& utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$facetrim)){
					
				nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_accessory$facetrim.taskacc_nr_helpers;
				nCost += nHelpers * nTime * nRate / 60;
			}
			
			rOrdTaskQty.ordrevdstqty_cost_help = scopes.avUtils.roundNumber(nCost);
		}
	}
	
	return rOrdTaskQty;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * 
 * @public 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"3324967A-BF9F-4BF8-9D43-75CD0513EA6D"}
 */
function getNumHelpers(rOrdTaskQty){
	
	if (!rOrdTaskQty) {
		return 0;
	}
	
    var nHelpers = 0;
    var rTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);
    var rTaskStd = rTask.sa_task_to_sa_task_standard.getRecord(1); 
    var bUsesStandardsNumHelpers = false;
    var aVariableBasedOnTasks = [TASKTYPEID.Binding, TASKTYPEID.Finishing, TASKTYPEID.Other];
    var aVariableBasedOnValues = ["L", "N"];
    
	if (rTask.tasktype_id == TASKTYPEID.FolderGluer) {
		bUsesStandardsNumHelpers =  true;
	}
	else if (aVariableBasedOnTasks.includes(rTask.tasktype_id) && aVariableBasedOnValues.includes(rTaskStd.taskstd_machine_var_based_on)) {
		bUsesStandardsNumHelpers =  true;
	}

    if (rOrdTaskQty.ordrevdstqty_num_help_override != null) {

        nHelpers = rOrdTaskQty.ordrevdstqty_num_help_override;
    }
    else if (bUsesStandardsNumHelpers) {
    	if (rTaskStd.taskstd_helpers) {
    		nHelpers = rTaskStd.taskstd_helpers; 
    	}
    }
    else if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine) 
            && rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_helpers) {

        nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_helpers;
    }

    // Apply Associated task helpers
    var oMax = {maxHelper : nHelpers, maxSpeedAdj: null},
    oAssocTask = getAssocTaskAdjustments(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1), oMax);
    nHelpers = oAssocTask.helpers;

    return nHelpers;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * 
 * @public 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"869B48BF-0B9B-417C-9282-34868B3872E1"}
 */
function getNumHelpers_taskStd(rOrdTaskQty){
	
	var nHelpers = 0,
	    rOrdTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),
	    bPressTask = (rOrdTask.sa_order_revds_task_to_sa_task.tasktype_id == scopes.avTask.TASKTYPEID.PressRun) ? true : false,
	    rSection = rOrdTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
	    
    if (rOrdTaskQty.ordrevdstqty_num_help_override != null) {
        nHelpers = rOrdTaskQty.ordrevdstqty_num_help_override;
    }
    else if (bPressTask) {
    	var rPressPool = null;
    	
    	if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press) 
    			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press.sa_order_revds_press_to_sa_order_revds_press_pool)) {
    		rPressPool = rSection.sa_order_revision_detail_section_to_sa_order_revds_press.sa_order_revds_press_to_sa_order_revds_press_pool.getRecord(1);
        }
    	// child section - have to get press pool from the parent
        else if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press) 
        		&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press.sa_order_revds_press_to_sa_order_revds_press_pool$parent)) {
    		rPressPool = rSection.sa_order_revision_detail_section_to_sa_order_revds_press.sa_order_revds_press_to_sa_order_revds_press_pool$parent.getRecord(1);
        }
    	
		if (rPressPool) {
	        var rPressStd = null;
	        
			if (utils.hasRecords(rPressPool.sa_order_revds_press_pool_to_sa_task) 
					&& utils.hasRecords(rPressPool.sa_order_revds_press_pool_to_sa_task.sa_task_to_sa_task_standard)) {
				rPressStd = rPressPool.sa_order_revds_press_pool_to_sa_task.sa_task_to_sa_task_standard.getRecord(1);
			}

			if (utils.hasRecords(rPressPool.sa_order_revds_press_pool_to_sa_order_revds_press_pool_task) 
					&& rPressPool.sa_order_revds_press_pool_to_sa_order_revds_press_pool_task.spresstask_helpers > 0) {
				nHelpers = rPressPool.sa_order_revds_press_pool_to_sa_order_revds_press_pool_task.spresstask_helpers; 
			}
			else {
	        	nHelpers = rPressStd.taskstd_helpers;
			}
		    
		    if (rPressStd && rPressStd.taskstd_max_helpers > 0 && rPressStd.taskstd_max_helpers < nHelpers + rPressStd.taskstd_helpers) {
		        nHelpers = rPressStd.taskstd_max_helpers;
		    }
		}
    }

	if (nHelpers == null) {
		nHelpers = 0;
	}
    
	return nHelpers;
}

/**
 * Gets the cost of internal labor
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 25, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {JSRecord<db:/avanti/sa_order_revds_task_suppl_qty>} [rSQty] - optional supplier qty record to use
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * 
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @SuppressWarnings(wrongparameters)
 * @properties={typeid:24,uuid:"62B4226D-60AC-4896-8A1B-924E64966719"}
 */
function getCost_internalLabor(rOrdTaskQty, rSQty, oEstOrdTask) {
	

	var nCost = 0,
		nTime = 0,
		nRate = 0;
	
	if (!oEstOrdTask) oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rOrdTaskQty.ordrevdstask_id); 
	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
//	var rSection = oEstOrdSection.rSection;
//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
//	var rDetail = oEstOrdDetail.rDetail;
	
	// GD - 2012-08-21: SL-371
	if (iTaskTypeID == 100)
	{
		if (rSQty){
			rOrdTaskQty.ordrevdstqty_cost_run = rSQty.ordrevdstsqty_cost_run;
			rOrdTaskQty.ordrevdstqty_cost_intlabor = rSQty.ordrevdstsqty_int_cost;
			
			// GD - Dec 31, 2014: Fixed this to work with either markup
			if (rSQty.ordrevdstsqty_mrkup_over != null) {
				rOrdTaskQty.ordrevdstqty_omrk_intlabor = rSQty.ordrevdstsqty_mrkup_over;
			} else {
				rOrdTaskQty.ordrevdstqty_omrk_intlabor = rSQty.ordrevdstsqty_mrkup;
			}
			
			rOrdTaskQty = getPrice_internalLabor(rOrdTaskQty, oEstOrdTask);
		} else {
			// Set the task to the cost from the used supplier
			rOrdTaskQty.ordrevdstqty_cost_intlabor = 0;

			if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)){
				rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);
				nCost = rSQty.ordrevdstsqty_int_cost;
				rOrdTaskQty.ordrevdstqty_cost_intlabor =  scopes.avUtils.roundNumber(nCost);
			}
		}
	} else if (iTaskTypeID === 34){
		nTime = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.ordrevdstaskpost_time_int;
		nRate = (oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.helper" + '"']) ? oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.helper" + '"'].nRate : 0;  //getCostLinkRate (rEstStdTask, "avanti.lbl.internalLabor");
		nCost = nTime * nRate / 60;
		rOrdTaskQty.ordrevdstqty_cost_intlabor = scopes.avUtils.roundNumber(nCost);
	}
	
	return rOrdTaskQty;
}

/**
 * Gets the run price/markup for a helper
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"3E3E6A20-8792-4E21-AD43-63A878E97487"}
 */
function getPrice_helper (rOrdTaskQty, oEstOrdTask) {
	
	var rTask = oEstOrdTask.rTask; 
	if(utils.hasRecords(rOrdTaskQty, 'sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section')) {
		var rSection = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
		
	//	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	//	var rEstStdTask = oEstOrdTask.rEstStdTask;
	//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	//	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
	//	var rSection = oEstOrdSection.rSection;
	//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
	//	var rDetail = oEstOrdDetail.rDetail;
		var nHelpers = 0;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine)) {
			nHelpers = rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_helpers;
		}
		
		if (rOrdTaskQty.ordrevdstqty_is_task_pricerule == 1 && rOrdTaskQty.ordrevdstqty_prule_help_x != null){
			rOrdTaskQty.ordrevdstqty_lprice_help_x = rOrdTaskQty.ordrevdstqty_prule_help_x;
			rOrdTaskQty.ordrevdstqty_mrk_help_list = (rOrdTaskQty.ordrevdstqty_cost_help > 0 ? (rOrdTaskQty.ordrevdstqty_lprice_help_x - rOrdTaskQty.ordrevdstqty_cost_help) / rOrdTaskQty.ordrevdstqty_cost_help * 100: 100);
		}
		else{
            oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](rTask, rOrdTaskQty.ordrevdstqty_time_run * nHelpers, "avanti.lbl.helper", rOrdTaskQty.ordrevdstqty_cost_help, rOrdTaskQty, rSection, rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1)));
            var nMrkup = oListPrice.nMrkUp;

            if (oListPrice.sPriceMethod == "A") {
                rOrdTaskQty.ordrevdstqty_lprice_help_x = oListPrice.nActualPrice;
                rOrdTaskQty.ordrevdstqty_mrk_help_list = nMrkup;
            }
            else {
                var oRevisedCost = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_help, nMrkup, oListPrice.nDiscountPct);
                rOrdTaskQty.ordrevdstqty_lprice_help_x = oRevisedCost.nRevisedCost;
                rOrdTaskQty.ordrevdstqty_mrk_help_list = oRevisedCost.nRevisedMarkup;
            }

            rOrdTaskQty.ordrevdstqty_prule_help_x = null;
		}
		
		if (rOrdTaskQty.ordrevdstqty_ouser_help != null) {	
			rOrdTaskQty.ordrevdstqty_lprice_help_x = rOrdTaskQty.ordrevdstqty_lprice_help_x + rOrdTaskQty.ordrevdstqty_ouser_help;
			if (rOrdTaskQty.ordrevdstqty_adj_help == null || rOrdTaskQty.ordrevdstqty_adj_help == 0) {
	            rOrdTaskQty.ordrevdstqty_oprice_help = rOrdTaskQty.ordrevdstqty_lprice_help_x;   
	        }
		}
		if (rOrdTaskQty.ordrevdstqty_adj_help != null && rOrdTaskQty.ordrevdstqty_adj_help != 0) {
		    rOrdTaskQty.ordrevdstqty_oprice_help = rOrdTaskQty.ordrevdstqty_lprice_help_x + rOrdTaskQty.ordrevdstqty_adj_help;   
		}
		
		if (scopes.avDetail.bClearAllEstCalcAdjustments  
		   || (rOrdTaskQty.ordrevdstqty_ouser_help == null 
		      && !rOrdTaskQty.ordrevdstqty_adj_setup 
		      && rOrdTaskQty.ordrevdstqty_is_min_charge != 1 
		      && !rOrdTaskQty.ordrevdstqty_adj_help)) {
	        rOrdTaskQty.ordrevdstqty_oprice_setup = null;
	        rOrdTaskQty.ordrevdstqty_omrk_setup = null;
	    }
		
        if (rOrdTaskQty.ordrevdstqty_omrk_help != null) {
            rOrdTaskQty.ordrevdstqty_mrk_help = rOrdTaskQty.ordrevdstqty_omrk_help;
            var oRevisedCostH = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_help, rOrdTaskQty.ordrevdstqty_mrk_help, oListPrice.nDiscountPct);
            rOrdTaskQty.ordrevdstqty_price_help_x = oRevisedCostH.nRevisedCost;
            rOrdTaskQty.ordrevdstqty_mrk_help = oRevisedCostH.nRevisedMarkup;

        }
        else if (rOrdTaskQty.ordrevdstqty_oprice_help != null) {
            rOrdTaskQty.ordrevdstqty_price_help_x = rOrdTaskQty.ordrevdstqty_oprice_help;
            rOrdTaskQty.ordrevdstqty_mrk_help = ( rOrdTaskQty.ordrevdstqty_cost_help > 0 ? ( rOrdTaskQty.ordrevdstqty_oprice_help - rOrdTaskQty.ordrevdstqty_cost_help ) / rOrdTaskQty.ordrevdstqty_cost_help * 100 : 100 );

        }
        else {
            if (!nMrkup) {
                nMrkup = 0;
            }
            rOrdTaskQty.ordrevdstqty_mrk_help = rOrdTaskQty.ordrevdstqty_mrk_help_list;
            rOrdTaskQty.ordrevdstqty_price_help_x = rOrdTaskQty.ordrevdstqty_lprice_help_x //(oListPrice.sPriceMethod == "A" ? oListPrice.nActualPrice : rOrdTaskQty.ordrevdstqty_cost_help * (1 + rOrdTaskQty.ordrevdstqty_mrk_help / 100));
        }
		
		rOrdTaskQty.ordrevdstqty_price_help_x = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_price_help_x);
		rOrdTaskQty.ordrevdstqty_price_help = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_help_x, null, rOrdTaskQty);
		
	//	application.output("TaskQty_helper before discount: " + rOrdTaskQty.ordrevdstqty_price_help_x + " -- TaskQty_helpers after discount: " + rOrdTaskQty.ordrevdstqty_price_help);	
		
	}
	return rOrdTaskQty;
}

/**
 * Gets the run price/markup for internal labor
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * 
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"81DF56A9-5B0F-442E-A8B1-4E67B33797FD"}
 */
function getPrice_internalLabor (rOrdTaskQty, oEstOrdTask) {
	
	if (!oEstOrdTask) oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rOrdTaskQty.ordrevdstask_id); 
	var rSection = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
//	var rTask = oEstOrdTask.rTask; 
//	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
//	var rEstStdTask = oEstOrdTask.rEstStdTask;
//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
//	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
//	var rSection = oEstOrdSection.rSection;
//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
//	var rDetail = oEstOrdDetail.rDetail;
	
	var nMrkUp = 0;
	
    if (rOrdTaskQty.ordrevdstqty_oprice_intlabor != null) {
        rOrdTaskQty.ordrevdstqty_price_intlabor_x = rOrdTaskQty.ordrevdstqty_oprice_intlabor;
        rOrdTaskQty.ordrevdstqty_mrk_intlabor = ( rOrdTaskQty.ordrevdstqty_oprice_intlabor - rOrdTaskQty.ordrevdstqty_cost_intlabor ) / rOrdTaskQty.ordrevdstqty_cost_intlabor * 100;
    }
    else {
        if (rOrdTaskQty.ordrevdstqty_omrk_intlabor != null) {
            rOrdTaskQty.ordrevdstqty_mrk_intlabor = rOrdTaskQty.ordrevdstqty_omrk_intlabor;
            rOrdTaskQty.ordrevdstqty_price_intlabor_x = ( rOrdTaskQty.ordrevdstqty_cost_intlabor * ( 1 + rOrdTaskQty.ordrevdstqty_mrk_intlabor / 100 ) );

        }
        else {
            if (rOrdTaskQty.ordrevdstqty_is_task_pricerule != 1 && !rOrdTaskQty.ordrevdstqty_prule_intlabor_x != null) {
                rOrdTaskQty.ordrevdstqty_price_intlabor_x = rOrdTaskQty.ordrevdstqty_prule_intlabor_x;
                rOrdTaskQty.ordrevdstqty_mrk_intlabor = ( rOrdTaskQty.ordrevdstqty_cost_intlabor > 0 ? ( rOrdTaskQty.ordrevdstqty_price_intlabor_x - rOrdTaskQty.ordrevdstqty_cost_intlabor ) / rOrdTaskQty.ordrevdstqty_cost_intlabor * 100 : 100 );
            }
            else {
                oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](oEstOrdTask.rTask, null, "avanti.lbl.internalLabor", rOrdTaskQty.ordrevdstqty_cost_intlabor, rOrdTaskQty, rSection, rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1)));
                nMrkUp = oListPrice.nMrkUp;
                if (!nMrkUp) nMrkUp = 0;

                if (oListPrice.sPriceMethod == "A") {
                    rOrdTaskQty.ordrevdstqty_price_intlabor_x = oListPrice.nActualPrice;
                    rOrdTaskQty.ordrevdstqty_mrk_intlabor = nMrkUp;
                }
                else {
                    var oRevisedCost = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_intlabor, nMrkUp, oListPrice.nDiscountPct);
                    rOrdTaskQty.ordrevdstqty_price_intlabor_x = oRevisedCost.nRevisedCost;
                    rOrdTaskQty.ordrevdstqty_mrk_intlabor = oRevisedCost.nRevisedMarkup;
                }
                rOrdTaskQty.ordrevdstqty_prule_intlabor_x = null;
            }
        }
    }
	
	rOrdTaskQty.ordrevdstqty_price_intlabor_x = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_price_intlabor_x);
	rOrdTaskQty.ordrevdstqty_price_intlabor = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_intlabor_x, null, rOrdTaskQty);
	
//	application.output("TaskQty_intlabor before discount: " + rOrdTaskQty.ordrevdstqty_price_intlabor_x + " -- TaskQty_intlabor after discount: " + rOrdTaskQty.ordrevdstqty_price_intlabor);	
	
	return rOrdTaskQty;
}

/**
 * Gets the setup cost
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {Number} [nTime] - The time in mins
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"3076BD14-EE2F-4CFB-84DB-B30642538F29"}
 */
function getCost_setup (rOrdTaskQty, nTime, oEstOrdTask) {
	var nRate = 0,
		nCost = 0,
		rSQty = null,
		rPost,
		i = 0,
		iMax = 0,
		fsUDF,
		rUDF;
	
	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
//	var rSection = oEstOrdSection.rSection;
//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
//	var rDetail = oEstOrdDetail.rDetail;
	
	if (!rEstStdTask) return rOrdTaskQty;


	// GD - Mar 26, 2014: Outsourced is done differently then other tasks
	if (iTaskTypeID == 100)
	{
		// Set the task to the cost from the used supplier
		rOrdTaskQty.ordrevdstqty_cost_setup = 0;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)){
			rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);
			nCost = rSQty.ordrevdstsqty_setup_cost;
			
			// GD - May 7, 2014: SL-2575 - Not totalling correctly; the cost needs to include the freight, regardless if internal or external
			nCost += rSQty.ordrevdstsqty_freight_in;
			nCost += rSQty.ordrevdstsqty_freight_out;
//			if (rSQty.ordrevdstsqty_frt_in_type === "I"){
//				nCost += rSQty.ordrevdstsqty_freight_in;
//			} 
//			if (rSQty.ordrevdstsqty_frt_out_type === "I"){
//				nCost += rSQty.ordrevdstsqty_freight_out;
//			} 
			rOrdTaskQty.ordrevdstqty_cost_setup = scopes.avUtils.roundNumber(nCost);
		}
		return rOrdTaskQty;
		
	} else if (iTaskTypeID === 34) {
		
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post)){
			rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
			nCost += rPost.ordrevdstaskpost_cost_add;
			
			// Get all the additional udf cost
			fsUDF = rPost.sa_order_revds_task_post_to_sa_order_revds_task_post_udf;
			iMax = fsUDF.getSize();
			for ( i = 1; i <= iMax; i++ ) {
				rUDF = fsUDF.getRecord(i);
				if (rUDF.ordrevdstaskpostudf_cost != null){
					nCost += rUDF.ordrevdstaskpostudf_cost;
				}
			}
			rOrdTaskQty.ordrevdstqty_cost_setup = scopes.avUtils.roundNumber(nCost);
		}
		return rOrdTaskQty;
	}
	
	// Normal tasks
	nRate = (oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.task_setup" + '"']) ? oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.task_setup" + '"'].nRate : 0;  //getCostLinkRate (rEstStdTask, "avanti.lbl.task_setup");
	
	if (!nTime){
		if (rOrdTaskQty.ordrevdstqty_time_setup_over) {
			nTime = rOrdTaskQty.ordrevdstqty_time_setup_over;
		} else {
			nTime = rOrdTaskQty.ordrevdstqty_time_setup;
		}
	}

	if (!nTime) nTime = 0;
	
	nCost = nTime * nRate / 60;
	
	if (!nCost) nCost = 0;
	
	rOrdTaskQty.ordrevdstqty_cost_setup = scopes.avUtils.roundNumber(nCost);
	
	return rOrdTaskQty;
}

/**
 * Gets the setup price/markup
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"539D343F-BEA3-4D1A-B052-7582C7A750FE"}
 */
function getPrice_setup (rOrdTaskQty, oEstOrdTask) {
	
	
	/*** @type {{
	 * cust_id:UUID,
	 * custcat_id:UUID,
	 * custclass_id:UUID,
	 * item_id:UUID,
	 * itemclass_id:UUID,
	 * ingroup_id:UUID,
	 * inktype_id:UUID,
	 * tasktype_id:Number,
	 * task_id:UUID,
	 * taskoper_id:UUID,
	 * dept_id:UUID,
	 * cc_id:UUID,
	 * type:String,
	 * cost:Number,
	 * qty:Number,
	 * ordh_id:UUID,
	 * date:Date,
	 * rSecTask:JSRecord<db:/avanti/sa_order_revds_task>,
	 * rTask:JSRecord<db:/avanti/sa_task>,
	 * rItem:JSRecord<db:/avanti/in_item>,
	 * taskmachine_id:UUID,
	 * applypricerules:Number,
	 * baseCost:Number,
	 * rSecPriceTaskQty:JSRecord<db:/avanti/sa_order_revds_task_qty>,
	 * selluom_id:UUID
	 * }}
	 */
	var oPriceObj = new Object();
	
	var rSecTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rSecTask.ordrevds_id);
	var rSection = oEstOrdSection.rSection;

	var nMrkUp = 0,
		nMrkUpFreight = 0,
		rSQty,
		rPost;

	// GD - Mar 26, 2014: Outsourced is done differently then other tasks
	if (iTaskTypeID === scopes.avTask.TASKTYPEID.OutsideService)
	{
		rOrdTaskQty.ordrevdstqty_price_setup_x = 0;
		rOrdTaskQty.ordrevdstqty_price_setup = 0;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)){
			
			rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);
			
			// GD - Dec 31, 2014: Fixed this to work with either markup
            if (rSQty.ordrevdstsqty_mrkup_over != null) {
                nMrkUp = rSQty.ordrevdstsqty_mrkup_over;
            }
            else {
                // Markup already calculated in "getCostOutsourced" which runs earlier
                nMrkUp = rSQty.ordrevdstsqty_mrkup;
            }
            if (rSQty.ordrevdstsqty_mrkup_frght_over != null) {
                nMrkUpFreight = rSQty.ordrevdstsqty_mrkup_frght_over;
            }
            else {
                nMrkUpFreight = rSQty.ordrevdstsqty_mrkup_frght;
            }
			
			// GD - Sep 9, 2015: SL-5705 - Internal cost was part of setup price and run price; can only be one or the other
			rOrdTaskQty.ordrevdstqty_price_setup_x = (rSQty.ordrevdstsqty_setup_cost) * (1 + nMrkUp / 100);
			rOrdTaskQty.ordrevdstqty_lprice_setup_x = (rSQty.ordrevdstsqty_setup_cost) * (1 + rSQty.ordrevdstsqty_mrkup / 100);
			rOrdTaskQty.ordrevdstqty_price_setup_x += (rSQty.ordrevdstsqty_freight_in + rSQty.ordrevdstsqty_freight_out) * (1 + nMrkUpFreight / 100);
			rOrdTaskQty.ordrevdstqty_lprice_setup_x += (rSQty.ordrevdstsqty_freight_in + rSQty.ordrevdstsqty_freight_out) * (1 + rSQty.ordrevdstsqty_mrkup_frght / 100);
			
			rOrdTaskQty.ordrevdstqty_price_setup = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty);
		}
		return rOrdTaskQty;
		
	} else if (iTaskTypeID === scopes.avTask.TASKTYPEID.Postage) {
		
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post)){
			rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
			nMrkUp = rPost.ordrevdstaskpost_mrk_over; 
			// GD - Aug 15, 2014: Had to take out the rounding; problems with Est Calculator
			if (rOrdTaskQty.ordrevdstqty_oprice_setup > 0){
				rOrdTaskQty.ordrevdstqty_price_setup_x = rOrdTaskQty.ordrevdstqty_oprice_setup;
			} else {
				rOrdTaskQty.ordrevdstqty_price_setup_x = rOrdTaskQty.ordrevdstqty_cost_setup * (1 + nMrkUp / 100);
			}
			rOrdTaskQty.ordrevdstqty_lprice_setup_x = rOrdTaskQty.ordrevdstqty_cost_setup * (1 + nMrkUp / 100);
//			rOrdTaskQty.ordrevdstqty_price_setup_x = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_setup * (1 + nMrkUp / 100));
			rOrdTaskQty.ordrevdstqty_price_setup = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty);
		}
		return rOrdTaskQty;
	}
	
	// Normal tasks
//	if (rOrdTaskQty.ordrevdstqty_is_task_pricerule != 1 && !rOrdTaskQty.ordrevdstqty_oprice_setup && !rOrdTaskQty.ordrevdstqty_omrk_setup){
//    	oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](rEstStdTask, rOrdTaskQty.ordrevdstqty_time_setup, "avanti.lbl.task_setup", rOrdTaskQty.ordrevdstqty_cost_setup, rOrdTaskQty,  rSection, rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1)));
//    	nMrkUp = oListPrice.nMrkUp;
//    	
//    	if (!nMrkUp) nMrkUp = 0;
//    	rOrdTaskQty.ordrevdstqty_lprice_setup_x = ( oListPrice.sPriceMethod == "A" ? oListPrice.nActualPrice : scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, nMrkUp, oListPrice.nDiscountPct));
//    	rOrdTaskQty.ordrevdstqty_mrk_setup_list = nMrkUp;
//    }

	if (rOrdTaskQty.ordrevdstqty_is_task_pricerule == 1 && rOrdTaskQty.ordrevdstqty_prule_setup_x != null){
		rOrdTaskQty.ordrevdstqty_lprice_setup_x = rOrdTaskQty.ordrevdstqty_prule_setup_x;
		rOrdTaskQty.ordrevdstqty_mrk_setup_list = (rOrdTaskQty.ordrevdstqty_cost_setup > 0 ? (rOrdTaskQty.ordrevdstqty_lprice_setup_x - rOrdTaskQty.ordrevdstqty_cost_setup) / rOrdTaskQty.ordrevdstqty_cost_setup * 100: 100);
	}
	else{
        oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](rEstStdTask, rOrdTaskQty.ordrevdstqty_time_setup, "avanti.lbl.task_setup", rOrdTaskQty.ordrevdstqty_cost_setup, rOrdTaskQty, rSection, rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1)));
        nMrkUp = oListPrice.nMrkUp;

        if (!nMrkUp) nMrkUp = 0;
        if (oListPrice.sPriceMethod == "A") {
            rOrdTaskQty.ordrevdstqty_lprice_setup_x = oListPrice.nActualPrice;
            rOrdTaskQty.ordrevdstqty_mrk_setup_list = nMrkUp;
        }
        else {
            var oRevisedCost = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, nMrkUp, oListPrice.nDiscountPct);
            rOrdTaskQty.ordrevdstqty_lprice_setup_x = oRevisedCost.nRevisedCost;
            rOrdTaskQty.ordrevdstqty_mrk_setup_list = oRevisedCost.nRevisedMarkup;
        }
        rOrdTaskQty.ordrevdstqty_prule_setup_x = null;
	}
	
	if (rOrdTaskQty.ordrevdstqty_ouser_setup != null) {	
		rOrdTaskQty.ordrevdstqty_lprice_setup_x = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_ouser_setup;
		if (rOrdTaskQty.ordrevdstqty_adj_setup == null || rOrdTaskQty.ordrevdstqty_adj_setup == 0) {
	        rOrdTaskQty.ordrevdstqty_oprice_setup = rOrdTaskQty.ordrevdstqty_lprice_setup_x;   
	    }
	} 
	if (rOrdTaskQty.ordrevdstqty_adj_setup != null && rOrdTaskQty.ordrevdstqty_adj_setup != 0) {
	    rOrdTaskQty.ordrevdstqty_oprice_setup = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;   
	}
    // GD - Jun 16, 2016: Need to clear out the override if we request it; keeps resetting and can't find the cause (FT cutter)
	if (scopes.avDetail.bClearAllEstCalcAdjustments  || (rOrdTaskQty.ordrevdstqty_ouser_setup == null && !rOrdTaskQty.ordrevdstqty_adj_setup && rOrdTaskQty.ordrevdstqty_is_min_charge != 1)) {
		rOrdTaskQty.ordrevdstqty_oprice_setup = null;																																		
		rOrdTaskQty.ordrevdstqty_omrk_setup = null;
	}
	
    if (rOrdTaskQty.ordrevdstqty_omrk_setup != null) {
    	var nDiscount = oListPrice && oListPrice.nDiscountPct ? oListPrice.nDiscountPct : 0;
        var oRevisedCostS = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, rOrdTaskQty.ordrevdstqty_omrk_setup, nDiscount);
        rOrdTaskQty.ordrevdstqty_price_setup_x = oRevisedCostS.nRevisedCost;
        rOrdTaskQty.ordrevdstqty_mrk_setup = oRevisedCostS.nRevisedMarkup;

    }
    else if (rOrdTaskQty.ordrevdstqty_oprice_setup != null) {
        rOrdTaskQty.ordrevdstqty_price_setup_x = rOrdTaskQty.ordrevdstqty_oprice_setup;
        rOrdTaskQty.ordrevdstqty_mrk_setup = ( rOrdTaskQty.ordrevdstqty_cost_setup > 0 ? ( rOrdTaskQty.ordrevdstqty_oprice_setup - rOrdTaskQty.ordrevdstqty_cost_setup ) / rOrdTaskQty.ordrevdstqty_cost_setup * 100 : 100 );

    }
    else {
        if (!nMrkUp) nMrkUp = 0;
        rOrdTaskQty.ordrevdstqty_mrk_setup = rOrdTaskQty.ordrevdstqty_mrk_setup_list;
        rOrdTaskQty.ordrevdstqty_price_setup_x = rOrdTaskQty.ordrevdstqty_lprice_setup_x;
    }
	
	// GD - Aug 15, 2014: Had to take out the rounding; problems with Est Calculator
//	rOrdTaskQty.ordrevdstqty_price_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
//	rOrdTaskQty.ordrevdstqty_price_setup_x = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_price_setup_x);
	rOrdTaskQty.ordrevdstqty_price_setup = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty);
	
//	application.output("TaskQty_setup before discount: " + rOrdTaskQty.ordrevdstqty_price_setup_x + " -- TaskQty_setup after discount: " + rOrdTaskQty.ordrevdstqty_price_setup);	
	
	return rOrdTaskQty;
}

/**
 * Gets the run cost
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {Number} [nTime] - The time in mins
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"F734CDBA-CE94-4CFB-BC9B-3F22AA7B945D"}
 */
function getCost_run (rOrdTaskQty, nTime, oEstOrdTask) {
	var nRate = 0,
		nCost = 0,
		rSQty,
		rPost;
	
	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
//	var rSection = oEstOrdSection.rSection;
//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
//	var rDetail = oEstOrdDetail.rDetail;
	
	// GD - Mar 26, 2014: Outsourced is done differently then other tasks
	if (iTaskTypeID == TASKTYPEID.OutsideService)
	{
		rOrdTaskQty.ordrevdstqty_cost_run = 0;
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)){
			rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);
			nCost = rSQty.ordrevdstsqty_cost_run;
			
			// GD - Sep 9, 2015: SL-5705 - Freight cost is already on the setup, so why is it here also?
//			if (rSQty.ordrevdstsqty_frt_in_type === "S"){
//				nCost += rSQty.ordrevdstsqty_freight_in;
//			} 
//			if (rSQty.ordrevdstsqty_frt_out_type === "S"){
//				nCost += rSQty.ordrevdstsqty_freight_out;
//			} 
			rOrdTaskQty.ordrevdstqty_cost_run = scopes.avUtils.roundNumber(nCost);
		}
		return rOrdTaskQty;
		
	} else if (iTaskTypeID === TASKTYPEID.Postage) {
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post)){
			rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
			nCost += rPost.ordrevdstaskpost_cost_post;
			rOrdTaskQty.ordrevdstqty_cost_run = scopes.avUtils.roundNumber(nCost);
		}
		return rOrdTaskQty;
	}
	
	// Normal tasks
	(rEstStdTask) ? nRate = (oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.task_run" + '"']) ? oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.task_run" + '"'].nRate : 0 : 0;  //getCostLinkRate (rEstStdTask, "avanti.lbl.task_run");
	
	if (!nTime){
		if (rOrdTaskQty.ordrevdstqty_time_run_over) {
			nTime = rOrdTaskQty.ordrevdstqty_time_run_over;
		} else {
			nTime = rOrdTaskQty.ordrevdstqty_time_run;
		}
	}

	if (!nTime) nTime = 0;
	
	nCost = nTime * nRate / 60;
	
	if (!nCost) nCost = 0;
	
	rOrdTaskQty.ordrevdstqty_cost_run = scopes.avUtils.roundNumber(nCost);
	
	return rOrdTaskQty;
}

/**
 * Gets the run price/markup
 *
 * <AUTHOR> Dotzlaw
 * @since 2014-03-21
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"0846B8C3-D30C-4800-BD6D-FB69AC3B5D5C"}
 */
function getPrice_run (rOrdTaskQty, oEstOrdTask) {
	
	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
	var rSection = oEstOrdSection.rSection;
	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
	var rDetail = oEstOrdDetail.rDetail;

	var nMrkUp = 0,
		rSQty,
		rPost;
	
	// GD - Mar 26, 2014: Outsourced is done differently then other tasks
	if (iTaskTypeID == TASKTYPEID.OutsideService)
	{

	    rOrdTaskQty.ordrevdstqty_price_run_x = 0;
		rOrdTaskQty.ordrevdstqty_price_run = 0;
        if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)) {
            rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);

            if (rSQty.ordrevdstsqty_mrkup_over != null) {
                rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_cost_run * ( 1 + rSQty.ordrevdstsqty_mrkup_over / 100 );
            }
            else {
                // sl-29717 - took out code applying nCustDiscount. its already included in ordrevdstsqty_mrkup
                rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_cost_run * ( 1 + rSQty.ordrevdstsqty_mrkup / 100 );
            }

            rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_lprice_run_x;
            rOrdTaskQty.ordrevdstqty_mrk_run = rSQty.ordrevdstsqty_mrkup_over;
            rOrdTaskQty.ordrevdstqty_price_run = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty);
            
        }
		return rOrdTaskQty;
		
	} else if (iTaskTypeID === TASKTYPEID.Postage) {
		if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post)){
			rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
			nMrkUp = rPost.ordrevdstaskpost_mrk_over; 
			// GD - Aug 15, 2014: Had to take out the rounding; problems with Est Calculator
			if (rOrdTaskQty.ordrevdstqty_oprice_run > 0){
				rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_oprice_run;
			} else {
				rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_cost_run * (1 + nMrkUp / 100);
			}
			rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_cost_run * (1 + nMrkUp / 100);
			rOrdTaskQty.ordrevdstqty_price_run = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty);
		}
		return rOrdTaskQty;
	}

	// SL-8669c - We need to always recalc the price rules when the change order is locked
	// Normal tasks
    if (!scopes.avChangeOrders.checkChgOrdLock(scopes.avSales.oEstOrd.rRev) 
            && rOrdTaskQty.ordrevdstqty_is_task_pricerule == 1 
            && rOrdTaskQty.ordrevdstqty_prule_run_x != null) {
                
    	rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_prule_run_x;
		rOrdTaskQty.ordrevdstqty_mrk_run_list = (rOrdTaskQty.ordrevdstqty_cost_run > 0 ? (rOrdTaskQty.ordrevdstqty_lprice_run_x - rOrdTaskQty.ordrevdstqty_cost_run) / rOrdTaskQty.ordrevdstqty_cost_run * 100: 100);
    }
	else {
		oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](rEstStdTask, rOrdTaskQty.ordrevdstqty_time_run, "avanti.lbl.task_run", rOrdTaskQty.ordrevdstqty_cost_run, rOrdTaskQty, rSection, rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1))); //nMrkUp = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getPriceObject"](rOrdTask, null, false));    
		nMrkUp = oListPrice.nMrkUp;
		
	    if (oListPrice.bTaskItemPriceFound == true && rOrdTaskQty.ordrevdstqty_apply_price_rules == 1 && oListPrice.sPriceMethod != 'A'){
	        rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_is_task_pricerule = 1;
	        rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_pricerule_mkup = nMrkUp;
	    }
	    else{
	    	  rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_is_task_pricerule = null;
	    	oListPrice.bTaskItemPriceFound = false;
	    	
	    	if (rOrdTaskQty.ordrevdstqty_prule_run_x){
	    	  rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_is_task_pricerule = null;
	          rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_pricerule_mkup = null;
	    	}
	    }
		
		if (!nMrkUp) nMrkUp = 0;
        if (oListPrice.sPriceMethod == "A") {
            rOrdTaskQty.ordrevdstqty_lprice_run_x = oListPrice.nActualPrice;
            rOrdTaskQty.ordrevdstqty_mrk_run_list = nMrkUp;
        }
        else {
            var oRevisedCostS = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_run, nMrkUp, oListPrice.nDiscountPct);
            rOrdTaskQty.ordrevdstqty_lprice_run_x = oRevisedCostS.nRevisedCost;
            rOrdTaskQty.ordrevdstqty_mrk_run_list = oRevisedCostS.nRevisedMarkup;
        }
        rOrdTaskQty.ordrevdstqty_prule_run_x = null;	
	}
    
    if (rOrdTaskQty.ordrevdstqty_ouser_run != null) {	
		rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_lprice_run_x + rOrdTaskQty.ordrevdstqty_ouser_run;
		if (rOrdTaskQty.ordrevdstqty_adj_run == null || rOrdTaskQty.ordrevdstqty_adj_run == 0) {
	        rOrdTaskQty.ordrevdstqty_oprice_run = rOrdTaskQty.ordrevdstqty_lprice_run_x;   
	    }
	}
	if (rOrdTaskQty.ordrevdstqty_adj_run != null && rOrdTaskQty.ordrevdstqty_adj_run != 0) {
	    rOrdTaskQty.ordrevdstqty_oprice_run = rOrdTaskQty.ordrevdstqty_lprice_run_x + rOrdTaskQty.ordrevdstqty_adj_run;   
	}
	
	// GD - Jun 16, 2016: Need to clear out the override if we request it; keeps resetting and can't find the cause (FT cutter)
	if (scopes.avDetail.bClearAllEstCalcAdjustments 
	   || (rOrdTaskQty.ordrevdstqty_ouser_run == null 
	      && !rOrdTaskQty.ordrevdstqty_adj_run 
	      && rOrdTaskQty.ordrevdstqty_is_min_charge != 1 
	      && !rOrdTaskQty.ordrevdstqty_adj_run)) {
		rOrdTaskQty.ordrevdstqty_oprice_run = null;
		rOrdTaskQty.ordrevdstqty_omrk_run = null;
	}
	
	if (rOrdTaskQty.ordrevdstqty_omrk_run != null) {
		rOrdTaskQty.ordrevdstqty_mrk_run = rOrdTaskQty.ordrevdstqty_omrk_run;
        var nDiscount = (oListPrice ? oListPrice.nDiscountPct : rDetail.ordrevd_disc_amt)
		var oRevisedCostR = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_run, rOrdTaskQty.ordrevdstqty_mrk_run, nDiscount);
		rOrdTaskQty.ordrevdstqty_price_run_x = oRevisedCostR.nRevisedCost;
		rOrdTaskQty.ordrevdstqty_mrk_run = oRevisedCostR.nRevisedMarkup;
		
	} else if (rOrdTaskQty.ordrevdstqty_oprice_run != null){
		rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_oprice_run;
		rOrdTaskQty.ordrevdstqty_mrk_run = (rOrdTaskQty.ordrevdstqty_cost_run > 0 ? (rOrdTaskQty.ordrevdstqty_oprice_run - rOrdTaskQty.ordrevdstqty_cost_run) / rOrdTaskQty.ordrevdstqty_cost_run * 100 : 100);
	
	} else {
		if (!nMrkUp) nMrkUp = 0;
		rOrdTaskQty.ordrevdstqty_mrk_run = rOrdTaskQty.ordrevdstqty_mrk_run_list; //nMrkUp;		
		rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_lprice_run_x; //(sPriceMethod == "A" ? nActualPrice : rOrdTaskQty.ordrevdstqty_cost_run * (1 + nMrkUp / 100));
	}
	
	// GD - Aug 15, 2014: Had to take out the rounding; problems with Est Calculator
//	rOrdTaskQty.ordrevdstqty_price_run_x = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_price_run_x);
	rOrdTaskQty.ordrevdstqty_price_run = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty);
	
//	application.output("TaskQty_run before discount: " + rOrdTaskQty.ordrevdstqty_price_run_x + " -- TaskQty_run after discount: " + rOrdTaskQty.ordrevdstqty_price_run);	
	
	return rOrdTaskQty;
}

/**
 * Computes the task prices
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 25, 2014
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * 
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"5A679E66-142F-45AD-81EE-DB01E28E3BB1"}
 */
function setPrice_task (rOrdTaskQty, oEstOrdTask) {
  
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	
	if (iTaskTypeID != TASKTYPEID.Ink 
			&& iTaskTypeID != TASKTYPEID.PressRun 
			&& iTaskTypeID != TASKTYPEID.Paper 
			&& iTaskTypeID != TASKTYPEID.Material 
			&& iTaskTypeID != TASKTYPEID.Plating){
		rOrdTaskQty = scopes.avTask.getPrice_setup(rOrdTaskQty, oEstOrdTask);
		rOrdTaskQty = scopes.avTask.getPrice_run(rOrdTaskQty, oEstOrdTask);
		if (isValidOperationForTask(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),"i18n:avanti.lbl.helper")){
			rOrdTaskQty = scopes.avTask.getPrice_helper(rOrdTaskQty, oEstOrdTask);
		}
		if (isValidOperationForTask(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),"i18n:avanti.lbl.internalLabor")){
			rOrdTaskQty = scopes.avTask.getPrice_internalLabor(rOrdTaskQty, oEstOrdTask);
		}
	}
	
	return rOrdTaskQty;
}

/**
 * Gets the labor cost total
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 26, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"94C8B0F7-8144-4DFC-AF18-D94CD9FCBB49"}
 */
function getTotalCost_labor(rOrdTaskQty, oEstOrdTask) {
    
    var iTaskTypeID = oEstOrdTask.iTaskTypeId;
    var nTotal = 0,
        rPost,
        rSQty;
    
    if (iTaskTypeID === TASKTYPEID.Postage) { // Postage uses separate tables for costs
        rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
        
        if (rPost) {
            rOrdTaskQty.ordrevdstqty_t_cost_orig = rPost.ordrevdstaskpost_t_cost;
            rOrdTaskQty.ordrevdstqty_t_orig = scopes.avDetail.applyDetailDiscount(rPost.ordrevdstaskpost_t, null, rOrdTaskQty);
        }
    }
    else if (iTaskTypeID === TASKTYPEID.OutsideService) {
        if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected)) {
            rSQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_suppl_qty$is_selected.getRecord(1);
            rOrdTaskQty = getCostOutsourced(rSQty, rOrdTaskQty, oEstOrdTask);
            rOrdTaskQty.ordrevdstqty_t_cost_lab_orig = rOrdTaskQty.ordrevdstqty_cost_setup + rOrdTaskQty.ordrevdstqty_cost_run + rOrdTaskQty.ordrevdstqty_cost_intlabor;
            
            nTotal = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_t_cost_lab_orig);
        }
    }
    else {
        nTotal += rOrdTaskQty.ordrevdstqty_cost_setup;
        nTotal += rOrdTaskQty.ordrevdstqty_cost_run;
        nTotal += rOrdTaskQty.ordrevdstqty_cost_help;
        nTotal += rOrdTaskQty.ordrevdstqty_cost_intlabor;
        rOrdTaskQty.ordrevdstqty_t_cost_lab_orig = scopes.avUtils.roundNumber(nTotal);
    }
    
    rOrdTaskQty = setCostPiece(rOrdTaskQty, nTotal, oEstOrdTask);
    
    // GD - Feb 1, 2016: SL-7606 Need to pick up the cost per piece override if it was applied to OS task
    if (iTaskTypeID === TASKTYPEID.OutsideService && rOrdTaskQty.ordrevdstqty_is_cost_piece === 1) {
        rOrdTaskQty.ordrevdstqty_t_cost_lab_orig = rOrdTaskQty.ordrevdstqty_cost_setup + rOrdTaskQty.ordrevdstqty_cost_run + rOrdTaskQty.ordrevdstqty_cost_intlabor;
    }
    
    return rOrdTaskQty;
}

/**
 * Gets the total cost of material
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 29, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"05A2370D-6C10-44DA-B947-9D57DC2F805B"}
 */
function getTotalCost (rOrdTaskQty, oEstOrdTask) {
	
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var nTotal = 0;


	nTotal += rOrdTaskQty.ordrevdstqty_t_cost_lab_orig;
	nTotal += rOrdTaskQty.ordrevdstqty_t_cost_item_orig; 
	nTotal += rOrdTaskQty.ordrevdstqty_t_cost_item_orig2; 

	if (iTaskTypeID == TASKTYPEID.DigitalSheetPress || iTaskTypeID == TASKTYPEID.DigitalRollPress) nTotal += rOrdTaskQty.ordrevdstqty_click_cost;

	nTotal = scopes.avUtils.roundNumber(nTotal);

	if (!nTotal) nTotal = 0;

	// Set the task
	rOrdTaskQty.ordrevdstqty_t_cost_orig = nTotal;

	return rOrdTaskQty;
}

/**
 * Gets the labor price total
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 26, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} [oEstOrdTask]
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * 
 * @properties={typeid:24,uuid:"FC91C905-6579-41B0-8B41-3CD2094ADEFC"}
 */
function getTotalPrice_labor (rOrdTaskQty, oEstOrdTask) {
	var nTotal = 0,
		nTotal_x = 0;
	
	if (!oEstOrdTask) oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rOrdTaskQty.ordrevdstask_id);
	
	var iTaskTypeID = oEstOrdTask.iTaskTypeId;

	// GD - Feb 1, 2016: SL-7606 Cost per piece pricing for OS
	if (iTaskTypeID == scopes.avTask.TASKTYPEID.OutsideService 
			&& rOrdTaskQty.ordrevdstqty_is_cost_piece === 1) {

		nTotal += rOrdTaskQty.ordrevdstqty_t_orig;
		nTotal_x += rOrdTaskQty.ordrevdstqty_t_orig_x;
		
	} else {
	    
		// GD - Feb 1, 2016: SL-7606 needs to support 0 override
		if (rOrdTaskQty.ordrevdstqty_oprice_setup != null){
			nTotal += rOrdTaskQty.ordrevdstqty_oprice_setup;
			nTotal_x += scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_oprice_setup, null, rOrdTaskQty);
		} else {
			nTotal += rOrdTaskQty.ordrevdstqty_price_setup_x;
			nTotal_x += rOrdTaskQty.ordrevdstqty_price_setup;
		}
		if (rOrdTaskQty.ordrevdstqty_oprice_run != null){
			nTotal += rOrdTaskQty.ordrevdstqty_oprice_run;
			nTotal_x += scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_oprice_run, null, rOrdTaskQty);
		} else {
			nTotal += rOrdTaskQty.ordrevdstqty_price_run_x;
			nTotal_x += rOrdTaskQty.ordrevdstqty_price_run;
		}
		if (rOrdTaskQty.ordrevdstqty_oprice_help != null){
			nTotal += rOrdTaskQty.ordrevdstqty_oprice_help;
			nTotal_x += scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_oprice_help, null, rOrdTaskQty);
		} else {
			nTotal += rOrdTaskQty.ordrevdstqty_price_help_x;
			nTotal_x += rOrdTaskQty.ordrevdstqty_price_help;
		}
		if (rOrdTaskQty.ordrevdstqty_oprice_intlabor != null){
			nTotal += rOrdTaskQty.ordrevdstqty_oprice_intlabor;
			nTotal_x += scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_oprice_intlabor, null, rOrdTaskQty);
		} else {
			nTotal += rOrdTaskQty.ordrevdstqty_price_intlabor_x;
			nTotal_x += rOrdTaskQty.ordrevdstqty_price_intlabor;
		}
	}
	rOrdTaskQty.ordrevdstqty_t_lab_orig_x = scopes.avUtils.roundNumber(nTotal);
//	rOrdTaskQty.ordrevdstqty_t_lab_orig = scopes.avDetail.applyDetailDiscount( rOrdTaskQty.ordrevdstqty_t_lab_orig_x, null, rOrdTaskQty);
	
	// GD - Feb 1, 2016: Moved all this into the ifs above
//	nTotal_x += rOrdTaskQty.ordrevdstqty_price_setup;
//	nTotal_x += rOrdTaskQty.ordrevdstqty_price_run;
//	nTotal_x += rOrdTaskQty.ordrevdstqty_price_help;
//	nTotal_x += rOrdTaskQty.ordrevdstqty_price_intlabor;

	rOrdTaskQty.ordrevdstqty_t_lab_orig = scopes.avUtils.roundNumber(nTotal_x);
	
	return rOrdTaskQty;
}

/**
 * Gets the total
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 26, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * @param {Boolean} [bTaskTotalOnly] - do task totalling only (used for Line Gang)
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"82016E71-FB4A-42E2-8E02-E3E55A5412BF"}
 */
function getTotal(rOrdTaskQty, oEstOrdTask, bTaskTotalOnly) {
    if (!oEstOrdTask.rEstStdTask) {
        return rOrdTaskQty;
    }

    var rTask = oEstOrdTask.rTask;
    var iTaskTypeID = oEstOrdTask.iTaskTypeId;
    var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
    var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);

    var nTotal = 0,
        nTotal_x = 0,
        nMinPrice = rEstStdTaskStd.taskstd_min_charge;

    //See if there is an actual price based task price rule.
    /**@type {{nMrkUp:Number, bItemPriceFound: Boolean, bTaskItemPriceFound: Boolean, uPriceRuleDetailUUID: UUID, sPriceMethod: String, nActualPrice: Number, sActualPriceUnits: String,nActualUnitPrice: Number, nActualPriceQty: Number}} **/
    oListPrice = globals["avCalcs_task_getListPrice"](globals["avCalcs_task_getCostPrice"](oEstOrdTask.rEstStdTask, null, null, 0, rOrdTaskQty, oEstOrdSection.rSection, rTask));
    var bCostPrice = false;
    
    // Need to check for press task
    var rOrdTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1); 
    var sTaskId = rOrdTask.task_id;
    var rSection = rOrdTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
    if (!bTaskTotalOnly
            && utils.hasRecords(rOrdTask.sa_order_revds_task_to_sa_task)
            && rOrdTask.sa_order_revds_task_to_sa_task.tasktype_id == scopes.avTask.TASKTYPEID.PressRun
            && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)) {
        sTaskId = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.task_id;                                                      
    }
    
    if (bTaskTotalOnly) {
        getTotal_x();
        rOrdTaskQty.ordrevdstqty_t_lab_orig = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_lab_orig_x, null, rOrdTaskQty);
        rOrdTaskQty.ordrevdstqty_t_item_orig = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_orig_x, null, rOrdTaskQty);
        rOrdTaskQty.ordrevdstqty_t_item_ori2 = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori2_x, null, rOrdTaskQty);
        rOrdTaskQty.ordrevdstqty_t_item_ori3 = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori3_x, null, rOrdTaskQty);
    }
    else if (globals.avBase_oCalcs.getCostTasks[sTaskId] && rOrdTaskQty.ordrevdstqty_apply_price_rules) { 
        
        var nCostMarkup = globals["calculateCostBreakups"](globals.avBase_oCalcs.getCostTasks[sTaskId],
            rOrdTaskQty.ordrevdstqty_t_cost_orig);
        if (reallocateCostMarkupsToOperations(rOrdTaskQty, nTotal_x, oEstOrdTask, nCostMarkup, oListPrice.nDiscountPct)) {
            nTotal_x = 0;
            bCostPrice = true;
            getTotal_x();
        }
    }
    else if (oListPrice.sPriceMethod == 'A' && oListPrice.nActualPrice > 0) {
        var nActualPriceRounded = scopes.avUtils.roundNumber(oListPrice.nActualPrice, 2);
        //Need to have the total task add up to the actual task price based on the price rule calculation.
        //Pro-rate based on existing totals.
        rOrdTaskQty.ordrevdstqty_is_task_pricerule = 1;
        rOrdTaskQty.ordrevdstqty_is_min_charge = null;
        rOrdTaskQty.priceruledtl_id = oListPrice.uPriceRuleDetailUUID;
        rOrdTaskQty.ordrevdstqty_actual_unit_price = oListPrice.nActualUnitPrice;
        rOrdTaskQty.ordrevdstqty_actual_price_uom = oListPrice.sActualPriceUnits;
        rOrdTaskQty.ordrevdstqty_actual_price_qty = oListPrice.nActualPriceQty;
        if (reallocateTaskPriceToOperations(rOrdTaskQty, null, nTotal_x, nActualPriceRounded, oEstOrdTask)) {
            rOrdTaskQty = setPrice_task(rOrdTaskQty, oEstOrdTask);
            rOrdTaskQty = getTotalPrice_labor(rOrdTaskQty);
            rOrdTaskQty = globals["avCalcs_task_getTotalMaterial"](rOrdTaskQty, oEstOrdTask, true);
        }
        nTotal_x = 0;
        getTotal_x();
    }
    else if (rOrdTaskQty.ordrevdstqty_is_task_pricerule === 1) {
        rOrdTaskQty.ordrevdstqty_is_task_pricerule = null;
        rOrdTaskQty.priceruledtl_id = null;
        rOrdTaskQty.ordrevdstqty_actual_unit_price = null;
        rOrdTaskQty.ordrevdstqty_actual_price_uom = null;
        rOrdTaskQty.ordrevdstqty_actual_price_qty = null;
        rOrdTaskQty = setPrice_task(rOrdTaskQty, oEstOrdTask);
        rOrdTaskQty = getTotalPrice_labor(rOrdTaskQty);
        rOrdTaskQty = globals["avCalcs_task_getTotalMaterial"](rOrdTaskQty, oEstOrdTask);

        nTotal_x = 0;
        getTotal_x();

    }
    else {
        rOrdTaskQty.ordrevdstqty_is_task_pricerule = null;
        rOrdTaskQty.priceruledtl_id = null;
        rOrdTaskQty.ordrevdstqty_actual_unit_price = null;
        rOrdTaskQty.ordrevdstqty_actual_price_uom = null;
        rOrdTaskQty.ordrevdstqty_actual_price_qty = null;
        
        //need to pickup the clicks below for press task
        if (iTaskTypeID == TASKTYPEID.PressRun) {
            bCostPrice = true; 
        }
        getTotal_x();
    }

    // Check and apply min charge for task
    if (!bTaskTotalOnly) {
        if (rEstStdTaskStd.taskstd_min_charge != null 
                && rEstStdTaskStd.taskstd_min_charge > 0 
                && !rOrdTaskQty.ordrevdstqty_adj_labor 
                && !rOrdTaskQty.ordrevdstqty_adj_item
                && rOrdTaskQty.ordrevdstqty_is_min_charge != -1) {
            
        	// Truncate the number to 2 decimals to avoid rounding issues
            nTotal_x = scopes.avMath.toFixed(nTotal_x, 2);
            
            if (nMinPrice > nTotal_x
                    || ( nMinPrice == nTotal_x && rOrdTaskQty.ordrevdstqty_is_min_charge == 1 )) {
                rOrdTaskQty.ordrevdstqty_is_min_charge = 1;
                rOrdTaskQty = setPriceOverride(rOrdTaskQty, nTotal_x, nMinPrice, oEstOrdTask);
    
                nTotal_x = 0;
                getTotal_x();
    
            }
            else if (rOrdTaskQty.ordrevdstqty_is_min_charge === 1) {
                rOrdTaskQty.ordrevdstqty_is_min_charge = null;
                rOrdTaskQty = clearPriceOverRide(rOrdTaskQty);
                rOrdTaskQty = setPrice_task(rOrdTaskQty, oEstOrdTask);
                rOrdTaskQty = getTotalPrice_labor(rOrdTaskQty);
                rOrdTaskQty = globals["avCalcs_task_getTotalMaterial"](rOrdTaskQty, oEstOrdTask);
    
                nTotal_x = 0;
                getTotal_x();
            }
        }
        else {
            rOrdTaskQty.ordrevdstqty_is_min_charge = (rOrdTaskQty.ordrevdstqty_is_min_charge == -1) ? -1 : null;
            rOrdTaskQty.ordrevdstqty_adj_minchrg_labor = 0;
            rOrdTaskQty.ordrevdstqty_adj_minchrg_item = 0;
        }
    }

    if (iTaskTypeID === TASKTYPEID.OutsideService || iTaskTypeID === TASKTYPEID.Postage) {

        nTotal = rOrdTaskQty.ordrevdstqty_t_orig;

    }
    else {

        nTotal += rOrdTaskQty.ordrevdstqty_t_lab_orig;
        nTotal += rOrdTaskQty.ordrevdstqty_t_item_orig;
        nTotal += rOrdTaskQty.ordrevdstqty_t_item_ori2;
        nTotal += rOrdTaskQty.ordrevdstqty_t_item_ori3;
        //SL-15662 non-cost based items are getting correct price that is why special condition added for cost based price rules to add clicks
        if (bCostPrice) {
            nTotal += rOrdTaskQty.ordrevdstqty_click_price;
        }
    }

    nTotal = nTotal || 0;

    // GD - May 4, 2016: I took out the rounding, as I think it will cause problems
    rOrdTaskQty.ordrevdstqty_t_orig = nTotal;

    // Set the total markup
    rOrdTaskQty = getTotalMrkUp(rOrdTaskQty);

    // gets the total before discount
    function getTotal_x() {

        if (iTaskTypeID === TASKTYPEID.OutsideService || iTaskTypeID === TASKTYPEID.Postage) {

            nTotal_x = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_orig, null, rOrdTaskQty);

        }
        else {

            nTotal_x += rOrdTaskQty.ordrevdstqty_t_lab_orig_x;
            nTotal_x += rOrdTaskQty.ordrevdstqty_t_item_orig_x;
            nTotal_x += rOrdTaskQty.ordrevdstqty_t_item_ori2_x;
            nTotal_x += rOrdTaskQty.ordrevdstqty_t_item_ori3_x;
            //SL-15662 non-cost based items are getting correct price that is why special condition added for cost based price rules to add clicks
            if (bCostPrice) {
                nTotal_x += rOrdTaskQty.ordrevdstqty_click_price_x;
            }
        }

        nTotal_x = nTotal_x || 0;

        rOrdTaskQty.ordrevdstqty_t_orig_x = nTotal_x;
    }

    return rOrdTaskQty;
}

/**
 * Sets the total markup
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 26, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"4A69B5BA-A335-469A-8419-48048A5368CE"}
 */
function getTotalMrkUp (rOrdTaskQty) {
	
	rOrdTaskQty.ordrevdstqty_t_over = null;
	rOrdTaskQty.ordrevdstqty_t_mrk_pct_over = null;

	if (rOrdTaskQty.ordrevdstqty_t_cost_orig > 0) {
		rOrdTaskQty.ordrevdstqty_t_mrk_pct_orig = (rOrdTaskQty.ordrevdstqty_t_orig - rOrdTaskQty.ordrevdstqty_t_cost_orig) / rOrdTaskQty.ordrevdstqty_t_cost_orig;
	} else {
		rOrdTaskQty.ordrevdstqty_t_mrk_pct_orig = 0;
	}

	return rOrdTaskQty;
}

/**
 * Clears the price override fields
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 28, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 *
 * @properties={typeid:24,uuid:"345D45ED-449E-4BDC-B9A0-E306330E68F0"}
 */
function clearPriceOverRide (rOrdTaskQty) {
	
	rOrdTaskQty.ordrevdstqty_oprice_setup = null;
	rOrdTaskQty.ordrevdstqty_oprice_run = null;
	rOrdTaskQty.ordrevdstqty_oprice_help = null;
	rOrdTaskQty.ordrevdstqty_oprice_intlabor = null;
	
	rOrdTaskQty.ordrevdstqty_t_lab_over = null;
	rOrdTaskQty.ordrevdstqty_t_item_over = null;
	rOrdTaskQty.ordrevdstqty_t_item_over2 = null;
	rOrdTaskQty.ordrevdstqty_t_item_over3 = null;
	
	rOrdTaskQty.ordrevdstqty_adj_minchrg_item = null;
	rOrdTaskQty.ordrevdstqty_adj_minchrg_labor = null;
	rOrdTaskQty.ordrevdstqty_is_min_charge = null;
	
	return rOrdTaskQty;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 *
 * @properties={typeid:24,uuid:"7B806130-A25E-4568-997D-B38A12774B0F"}
 */
function clearPrices (rOrdTaskQty) {
    
    rOrdTaskQty.ordrevdstqty_price_run = 0;
    rOrdTaskQty.ordrevdstqty_price_run_x = 0;
    rOrdTaskQty.ordrevdstqty_price_setup = 0;
    rOrdTaskQty.ordrevdstqty_price_setup_x = 0;
    rOrdTaskQty.ordrevdstqty_price_help = 0;
    rOrdTaskQty.ordrevdstqty_price_help_x = 0;
    
    rOrdTaskQty.ordrevdstqty_t_item_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_item_orig_x = 0;
    rOrdTaskQty.ordrevdstqty_t_item_ori2 = 0;
    rOrdTaskQty.ordrevdstqty_t_item_ori2_x = 0;
    rOrdTaskQty.ordrevdstqty_t_item_ori3 = 0;
    rOrdTaskQty.ordrevdstqty_t_item_ori3_x = 0;
    rOrdTaskQty.ordrevdstqty_t_item_over = null;
    rOrdTaskQty.ordrevdstqty_t_item_over2 = null;
    rOrdTaskQty.ordrevdstqty_t_item_over3 = null;
    rOrdTaskQty.ordrevdstqty_t_lab_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_lab_orig_x = 0;
    rOrdTaskQty.ordrevdstqty_t_lab_over = null;
    rOrdTaskQty.ordrevdstqty_t_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_orig_x = 0;
    rOrdTaskQty.ordrevdstqty_t_over = 0;

    return rOrdTaskQty;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 *
 * @properties={typeid:24,uuid:"D11E6EDA-DB08-4D73-BC46-86720576F220"}
 */
function clearCosts (rOrdTaskQty) {
    
    rOrdTaskQty.ordrevdstqty_t_cost_item_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_cost_item_orig2 = 0;
    rOrdTaskQty.ordrevdstqty_t_cost_item_orig3 = 0;
    rOrdTaskQty.ordrevdstqty_t_cost_item_over = null;
    rOrdTaskQty.ordrevdstqty_t_cost_item_over2 = null;
    rOrdTaskQty.ordrevdstqty_t_cost_lab_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_cost_lab_over = null;
    rOrdTaskQty.ordrevdstqty_t_cost_orig = 0;
    rOrdTaskQty.ordrevdstqty_t_cost_over = 0;
    
    return rOrdTaskQty;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 *
 * @properties={typeid:24,uuid:"B821A136-6DBE-4657-83BF-E457846DC135"}
 */
function clearTimes (rOrdTaskQty) {
    
    rOrdTaskQty.ordrevdstqty_time_run = 0;
    rOrdTaskQty.ordrevdstqty_time_run_over = null;
    rOrdTaskQty.ordrevdstqty_time_setup = 0;
    rOrdTaskQty.ordrevdstqty_time_setup_over = null;
    rOrdTaskQty.ordrevdstqty_time_setup_t = 0;
    rOrdTaskQty.ordrevdstqty_time_setup_t_over = null;
    
    return rOrdTaskQty;
}

/**
 * Sets the cost per piece for tasks
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 29, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {Number} nTotal - the total of all the comuted costs for this task
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 * @properties={typeid:24,uuid:"2F73CBB2-8924-4EB4-9BD2-1BED405D7A9C"}
 */
function setCostPiece (rOrdTaskQty, nTotal, oEstOrdTask) {
	
 	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId;
	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
	var rSection = oEstOrdSection.rSection;
	var	nQty,
		rPress,
		nCostPiece = 0,
		nTotalCostPiece = 0;
	
	if (rEstStdTaskStd && rEstStdTaskStd.taskstd_cost_per_piece === 1) {
		if (iTaskTypeID === scopes.avTask.TASKTYPEID.PressRun 
				&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)){
			rPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1);
			rEstStdTaskStd = rPress.sa_order_revds_press_to_sa_task.sa_task_to_sa_task_standard.getRecord(1);
			nQty = rPress.mpress_press_sheets;
		} 
		else if (iTaskTypeID === scopes.avTask.TASKTYPEID.Postage) {
			nQty = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.ordrevdstaskpost_qty;
		}
		else if(rEstStdTaskStd.taskstd_cost_per_piece_type == 'PKG'){ // PACKAGE
			nQty = rOrdTaskQty.ordrevdstqty_qty_item;
		}
		else if(rEstStdTaskStd.taskstd_cost_per_piece_type == 'PAD'){ // PAD
			nQty = rOrdTaskQty.ordrevdstqty_qty_task / rOrdTaskQty.ordrevdstqty_qty;
		}
		else if(rEstStdTaskStd.taskstd_cost_per_piece_type == 'BUND'){ // BUNDLE
			nQty = rOrdTaskQty.ordrevdstqty_qty_task / rOrdTaskQty.ordrevdstqty_qty;
		}
		else if(rEstStdTaskStd.taskstd_cost_per_piece_type == 'PROD'){ // PRODUCTION QTY
			nQty = oEstOrdSection.aSectionQtys[oEstOrdSection.iSelectedQtyIdx].ordrevdsqty_qty;
		}
		else{
			nQty = rOrdTaskQty.ordrevdstqty_qty_task; // default - task qty
		}
		
		var oCostPiece = getCostPerPiece(rOrdTaskQty, nQty, oEstOrdTask);
		var nSetupCost = oCostPiece.setup;
		nCostPiece = oCostPiece.cost;
		nTotalCostPiece = nCostPiece * nQty + nSetupCost;
		
		if (iTaskTypeID === 99){
			
			if (rPress.mpress_presssetup_cost > 0) rPress.mpress_presssetup_cost = scopes.avUtils.roundNumber(rPress.mpress_presssetup_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_firstmr_cost > 0) rPress.mpress_firstmr_cost = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_run * nTotalCostPiece / nTotal);
			if (rPress.mpress_addmr_cost > 0) rPress.mpress_addmr_cost = scopes.avUtils.roundNumber(rPress.mpress_addmr_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_wtmr_cost > 0) rPress.mpress_wtmr_cost = scopes.avUtils.roundNumber(rPress.mpress_wtmr_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_perfscoremr_cost > 0) rPress.mpress_perfscoremr_cost = scopes.avUtils.roundNumber(rPress.mpress_perfscoremr_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_run_cost > 0) rPress.mpress_run_cost = scopes.avUtils.roundNumber(rPress.mpress_run_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_setup_cost > 0) rPress.mpress_setup_cost = scopes.avUtils.roundNumber(rPress.mpress_setup_cost * nTotalCostPiece / nTotal);
			if (rPress.mpress_wash_cost > 0) rPress.mpress_wash_cost = scopes.avUtils.roundNumber(rPress.mpress_wash_cost * nTotalCostPiece / nTotal);
			
			if (rPress.mpress_setup_cost > 0) rOrdTaskQty.ordrevdstqty_cost_setup = rPress.mpress_setup_cost;
			if (rPress.mpress_presssetup_cost > 0) rOrdTaskQty.ordrevdstqty_cost_setup = rPress.mpress_presssetup_cost;
			if (rPress.mpress_run_cost > 0) rOrdTaskQty.ordrevdstqty_cost_run = rPress.mpress_run_cost;
	
			nTotal = 0;
			if (rPress.mpress_presssetup_cost > 0) nTotal += rPress.mpress_presssetup_cost;
			if (rPress.mpress_firstmr_cost > 0) nTotal += rPress.mpress_firstmr_cost;
			if (rPress.mpress_addmr_cost > 0) nTotal += rPress.mpress_addmr_cost;
			if (rPress.mpress_perfscoremr_cost > 0) nTotal += rPress.mpress_perfscoremr_cost;
			if (rPress.mpress_wtmr_cost > 0) nTotal += rPress.mpress_wtmr_cost;
			if (rPress.mpress_run_cost > 0) nTotal += rPress.mpress_run_cost;
			if (rPress.mpress_setup_cost > 0) nTotal += rPress.mpress_setup_cost;
			if (rPress.mpress_wash_cost > 0) nTotal += rPress.mpress_wash_cost;
			
			// sl-4275 - if all costs are zero arbitrarily set the whole amount to run  
			if(nTotalCostPiece && nTotal==0){
				rPress.mpress_run_cost = nTotalCostPiece;
				nTotal = nTotalCostPiece;
			}
		} else {
			
			if (rOrdTaskQty.ordrevdstqty_cost_setup > 0) rOrdTaskQty.ordrevdstqty_cost_setup = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_setup * nTotalCostPiece / nTotal);
			if (rOrdTaskQty.ordrevdstqty_cost_run > 0) rOrdTaskQty.ordrevdstqty_cost_run = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_run * nTotalCostPiece / nTotal);
			if (rOrdTaskQty.ordrevdstqty_cost_help > 0) rOrdTaskQty.ordrevdstqty_cost_help = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_help * nTotalCostPiece / nTotal);
			if (rOrdTaskQty.ordrevdstqty_cost_intlabor > 0) rOrdTaskQty.ordrevdstqty_cost_intlabor = scopes.avUtils.roundNumber(rOrdTaskQty.ordrevdstqty_cost_intlabor * nTotalCostPiece / nTotal);

			nTotal = 0;
			if (rOrdTaskQty.ordrevdstqty_cost_setup > 0) nTotal += rOrdTaskQty.ordrevdstqty_cost_setup;
			if (rOrdTaskQty.ordrevdstqty_cost_run > 0) nTotal += rOrdTaskQty.ordrevdstqty_cost_run;
			if (rOrdTaskQty.ordrevdstqty_cost_help > 0) nTotal += rOrdTaskQty.ordrevdstqty_cost_help;
			if (rOrdTaskQty.ordrevdstqty_cost_intlabor > 0) nTotal += rOrdTaskQty.ordrevdstqty_cost_intlabor;

			// sl-4275 - if all costs are zero arbitrarily set the whole amount to run  
			if(nTotalCostPiece && nTotal==0){
				rOrdTaskQty.ordrevdstqty_cost_run = nTotalCostPiece;
				nTotal = nTotalCostPiece;
			}
		}
		
		// GD - Feb 1, 2016: SL-7606 I took out the rounding here, because if cost/piece is very small (0.0043), and qty is 1, it calculates no price
		rOrdTaskQty.ordrevdstqty_t_cost_lab_orig = nTotal;

		rOrdTaskQty.ordrevdstqty_is_cost_piece = 1;
		
	} else {
		
		rOrdTaskQty.ordrevdstqty_is_cost_piece = null;
	}
	
	return rOrdTaskQty;
}

/**
 * Clears the cost over ride fields
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 28, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {Number} nTotal - the current total
 * @param {Number} nOverTotal - The total override price
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 *
 *
 * @properties={typeid:24,uuid:"37A19831-7659-46DE-83DC-B7DAFCF4173C"}
 */
function setPriceOverride (rOrdTaskQty, nTotal, nOverTotal, oEstOrdTask) {
    
    var nAdjustedTotal = 0;
	
	// GD - Oct 28, 2014: Pazazz bug; Trying without rounding
	if (rOrdTaskQty.ordrevdstqty_price_setup_x > 0) rOrdTaskQty.ordrevdstqty_oprice_setup = rOrdTaskQty.ordrevdstqty_price_setup_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_price_run_x > 0) rOrdTaskQty.ordrevdstqty_oprice_run = rOrdTaskQty.ordrevdstqty_price_run_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_price_help_x > 0) rOrdTaskQty.ordrevdstqty_oprice_help = rOrdTaskQty.ordrevdstqty_price_help_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_price_intlabor_x > 0) rOrdTaskQty.ordrevdstqty_oprice_intlabor = rOrdTaskQty.ordrevdstqty_price_intlabor_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_t_item_orig_x > 0) rOrdTaskQty.ordrevdstqty_t_item_over = rOrdTaskQty.ordrevdstqty_t_item_orig_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_t_item_ori2_x > 0) rOrdTaskQty.ordrevdstqty_t_item_over2 = rOrdTaskQty.ordrevdstqty_t_item_ori2_x * nOverTotal / nTotal;
	if (rOrdTaskQty.ordrevdstqty_t_item_ori3_x > 0) rOrdTaskQty.ordrevdstqty_t_item_over3 = rOrdTaskQty.ordrevdstqty_t_item_ori3_x * nOverTotal / nTotal;
	
	if (nTotal === 0){
		rOrdTaskQty.ordrevdstqty_price_run_x = rOrdTaskQty.ordrevdstqty_oprice_run = nOverTotal;
	}
	else{  // sl-3725 - rounding issue
	
		// GD - Apr 9, 2015: SL-4799 - We can not round before we look for the diff
		nAdjustedTotal = rOrdTaskQty.ordrevdstqty_oprice_setup + 
							 rOrdTaskQty.ordrevdstqty_oprice_run + 
							 rOrdTaskQty.ordrevdstqty_oprice_help + 
							 rOrdTaskQty.ordrevdstqty_oprice_intlabor + 
							 rOrdTaskQty.ordrevdstqty_t_item_over + 
							 rOrdTaskQty.ordrevdstqty_t_item_over2 +
							 rOrdTaskQty.ordrevdstqty_t_item_over3 +
							 rOrdTaskQty.ordrevdstqty_click_price_x;

		var nDiff = nOverTotal - nAdjustedTotal

		if (Math.abs(nDiff) >= 0.01) {
			if (rOrdTaskQty.ordrevdstqty_oprice_run > 0) rOrdTaskQty.ordrevdstqty_oprice_run += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_oprice_setup > 0) rOrdTaskQty.ordrevdstqty_oprice_setup += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_oprice_help > 0) rOrdTaskQty.ordrevdstqty_oprice_help += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_oprice_intlabor > 0) rOrdTaskQty.ordrevdstqty_oprice_intlabor += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_t_item_over > 0) rOrdTaskQty.ordrevdstqty_t_item_over += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_t_item_over2 > 0) rOrdTaskQty.ordrevdstqty_t_item_over2 += nDiff;
			else if (rOrdTaskQty.ordrevdstqty_t_item_over3 > 0) rOrdTaskQty.ordrevdstqty_t_item_over3 += nDiff;
		}
	}
	
	var nAdjustedTotalLabor = 
	                    rOrdTaskQty.ordrevdstqty_oprice_setup + 
                        rOrdTaskQty.ordrevdstqty_oprice_run + 
                        rOrdTaskQty.ordrevdstqty_oprice_help + 
                        rOrdTaskQty.ordrevdstqty_oprice_intlabor;
                        
    var nListTotalLabor = 
                        rOrdTaskQty.ordrevdstqty_lprice_setup_x + 
                        rOrdTaskQty.ordrevdstqty_lprice_run_x + 
                        rOrdTaskQty.ordrevdstqty_lprice_help_x + 
                        rOrdTaskQty.ordrevdstqty_price_intlabor_x;
                        
    if (nAdjustedTotalLabor - nListTotalLabor > 0) {
        rOrdTaskQty.ordrevdstqty_adj_minchrg_labor = nAdjustedTotalLabor - nListTotalLabor;
    }
    else {
        rOrdTaskQty.ordrevdstqty_adj_minchrg_labor = 0;
    }    
    
    var nAdjustedTotalItem =  
                        rOrdTaskQty.ordrevdstqty_t_item_over + 
                        rOrdTaskQty.ordrevdstqty_t_item_over2 +
                        rOrdTaskQty.ordrevdstqty_t_item_over3;
        
    var nListTotalItem = 
                        rOrdTaskQty.ordrevdstqty_lprice_item1_x + 
                        rOrdTaskQty.ordrevdstqty_lprice_item2_x +
                        rOrdTaskQty.ordrevdstqty_lprice_item3_x;
                        
    if (nAdjustedTotalItem - nListTotalItem > 0) {
        rOrdTaskQty.ordrevdstqty_adj_minchrg_item = nAdjustedTotalItem - nListTotalItem;
    }
    else {
        rOrdTaskQty.ordrevdstqty_adj_minchrg_item = 0;
    }   
		
	rOrdTaskQty = setPrice_task (rOrdTaskQty, oEstOrdTask);
	rOrdTaskQty = getTotalPrice_labor(rOrdTaskQty);
	rOrdTaskQty = globals["avCalcs_task_getTotalMaterial"](rOrdTaskQty, oEstOrdTask);
	
	return rOrdTaskQty;
}

/**
 * Gets the Cost Per Piece from the Task Cost Rate table
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-03-29
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The TaskQty record
 * @param {Number} nQty - Then Qty to get the per piece price for
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 *
 * @returns {{cost: Number, setup: Number}} Returns the cost per piece object
 *
 * @properties={typeid:24,uuid:"4B13458D-336F-4876-A688-456B39360854"}
 */
function getCostPerPiece(rTaskQty, nQty, oEstOrdTask)
{
//	if (!oEstOrdTask) oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rTaskQty.ordrevdstask_id);
 	var rTask = oEstOrdTask.rTask; 
	var iTaskTypeID = oEstOrdTask.iTaskTypeId; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
//	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
	var rSection = oEstOrdSection.rSection;
//	var oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id);
//	var rDetail = oEstOrdDetail.rDetail;
	
	var nCost = 0,
		i = 0,
//		rSecTask = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),
//		rSec = rSecTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
//		rTask = rSecTask.sa_order_revds_task_to_sa_task.getRecord(1),
//		iTaskTypeID = rTask.tasktype_id,
		fsCostRate,
		rCostRate,
		nSetup = 0,
		oObj = {};

	if (iTaskTypeID == 99) {
		if (!utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)) return oObj;

		rEstStdTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1).sa_order_revds_press_to_sa_task.getRecord(1);
	}

	if (utils.hasRecords(rEstStdTask.sa_task_to_sa_task_cost_rate)) {
		fsCostRate = rEstStdTask.sa_task_to_sa_task_cost_rate;
		fsCostRate.sort("sequence_nr asc");

		for (i = 1; i <= fsCostRate.getSize(); i++)	{
			rCostRate = fsCostRate.getRecord(i);

			if(nQty >= rCostRate.taskcostrate_qty_from && (nQty <= rCostRate.taskcostrate_qty_to || rCostRate.taskcostrate_qty_to == null))
			{
				nCost = rCostRate.taskcostrate_cost_per;
				nSetup = rCostRate.taskcostrate_setup;
				break;
			}
		}
	}
	// Take the last bracket value as the default
	if (nCost === 0 && rCostRate && rCostRate.taskcostrate_cost_per > 0) nCost = rCostRate.taskcostrate_cost_per; 
	
	oObj.cost = nCost;
	oObj.setup = nSetup;
	
	return oObj;
}

/**
 * Computes the cost for the outsourced service task
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 7, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_suppl_qty>} rSQty - The SecQty record
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The TaskQty record
 * @param {scopes.avSales.oEstOrdTask} [oEstOrdTask]
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} 
 *
 * @SuppressWarnings(wrongparameters)
 * @properties={typeid:24,uuid:"8A151738-F945-44AF-ACFE-F9B0A41FCA68"}
 */
function getCostOutsourced(rSQty, rTaskQty, oEstOrdTask) {
	
    if (!rTaskQty || !rSQty) {
        return null;
    }
	if (!oEstOrdTask) {	    
    	oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rTaskQty.ordrevdstask_id);
	}
	if (!oEstOrdTask) {
	    return null;
	}
 	var rTask = oEstOrdTask.rTask; 
	var rEstStdTask = oEstOrdTask.rEstStdTask;
	var rEstStdTaskStd = oEstOrdTask.rEstStdTaskStd;
	var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);

	var nRate = 0,
		nMrkUp = 1,
		nCustDiscount = 0,
		nMrkUpFreight = 0;
	
	if ( rSQty.ordrevdstsqty_est_uom == "E" ) {
		rSQty.ordrevdstsqty_cost_run_curr = rTaskQty.ordrevdstqty_qty_task * rSQty.ordrevdstsqty_cost_curr;
	} else if ( rSQty.ordrevdstsqty_est_uom == "T" ) {
		rSQty.ordrevdstsqty_cost_run_curr = rTaskQty.ordrevdstqty_qty_task / 1000 * rSQty.ordrevdstsqty_cost_curr;
	} else if ( rSQty.ordrevdstsqty_est_uom == "H" ) {
		rSQty.ordrevdstsqty_cost_run_curr = rTaskQty.ordrevdstqty_qty_task / 100 * rSQty.ordrevdstsqty_cost_curr;
	} else if (!rSQty.ordrevdstsqty_est_uom){
		// Lot is the default
		rSQty.ordrevdstsqty_est_uom = "L";
		rSQty.ordrevdstsqty_cost_run_curr = rSQty.ordrevdstsqty_cost_curr;
	} else {
		rSQty.ordrevdstsqty_cost_run_curr = rSQty.ordrevdstsqty_cost_curr;
	}

	// GD - 2014-03-05: SL-2218 - Cost is not calculated when a change is fired
	nRate = (oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.internalLabor" + '"']) ? oEstOrdSection.aTaskCostLinks['"' + rEstStdTask.task_id + "_i18n:avanti.lbl.internalLabor" + '"'].nRate : 0; //scopes.avTask.getCostLinkRate(rEstStdTask, "i18n:avanti.lbl.internalLabor");
	rSQty.ordrevdstsqty_int_cost = rSQty.ordrevdstsqty_int_time * nRate / 60;
	rTaskQty = scopes.avTask.getCost_internalLabor(rTaskQty, rSQty, oEstOrdTask); 
	rTaskQty = scopes.avTask.getPrice_internalLabor(rTaskQty, oEstOrdTask);
	
	rSQty.ordrevdstsqty_t_cost = rSQty.ordrevdstsqty_cost_run;
	rSQty.ordrevdstsqty_t_cost_freight = rSQty.ordrevdstsqty_freight_in + rSQty.ordrevdstsqty_freight_out;
	rSQty.ordrevdstsqty_t_cost += rSQty.ordrevdstsqty_setup_cost;
	rSQty.ordrevdstsqty_t_cost += rSQty.ordrevdstsqty_int_cost;
	
    // GD - Feb 1, 2016: SL-7606 If this is cost per piece pricing, then we have to override the markup here
    if (rTaskQty.ordrevdstqty_is_cost_piece === 1) {
        rTaskQty = setCostPiece(rTaskQty, rSQty.ordrevdstsqty_t_cost, oEstOrdTask);
        rSQty.ordrevdstsqty_t_cost = rTaskQty.ordrevdstqty_t_cost_lab_orig;
    }

    if (rEstStdTaskStd.taskstd_mrkup_type === "V") {
        rSQty.ordrevdstsqty_t_outsourced = getPriceWithVariableMarkup(rEstStdTask, rSQty.ordrevdstsqty_t_cost);
        nMrkUp = ( rSQty.ordrevdstsqty_t_outsourced - rSQty.ordrevdstsqty_t_cost ) / rSQty.ordrevdstsqty_t_cost * 100;

        rSQty.ordrevdstsqty_mrkup = nMrkUp;
        rSQty.ordrevdstsqty_mrkup_frght = rEstStdTaskStd.taskstd_mrkup * 100;

    }
    else {

        // Normal markup
        var uCustID = globals["UUIDtoString"](rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.cust_id);
        /** @type {{nMrkUp: Number, nDiscountPct: Number}}*/
        var oMrkUpCustDisc = globals["avCalcs_task_getPricing"](rTask, rEstStdTask, uCustID);
        if (oMrkUpCustDisc.nMrkUp != null) {
            rSQty.ordrevdstsqty_mrkup = oMrkUpCustDisc.nMrkUp;
        }
        nMrkUp = rSQty.ordrevdstsqty_mrkup;
        nCustDiscount = oMrkUpCustDisc.nDiscountPct;
        var oRevisedCost = scopes.avCost.getMarkupCalculation(rSQty.ordrevdstsqty_t_cost, nMrkUp, nCustDiscount);
        rSQty.ordrevdstsqty_t_outsourced = oRevisedCost.nRevisedCost;
        rSQty.ordrevdstsqty_mrkup = oRevisedCost.nRevisedMarkup;
        rSQty.ordrevdstsqty_mrkup_frght = oRevisedCost.nRevisedMarkup;
    }
    rSQty.ordrevdstsqty_t_outsourced_lp = rSQty.ordrevdstsqty_t_outsourced;
    
    if (rSQty.ordrevdstsqty_mrkup_over != null) {
        nMrkUp = rSQty.ordrevdstsqty_mrkup_over;
        rSQty.ordrevdstsqty_t_outsourced = (rSQty.ordrevdstsqty_t_cost * (1 + nMrkUp / 100));
    }

    if (rSQty.ordrevdstsqty_mrkup_frght_over != null) {
        nMrkUpFreight = rSQty.ordrevdstsqty_mrkup_frght_over;
    }
    else {
        nMrkUpFreight = rSQty.ordrevdstsqty_mrkup_frght;
    }

    rSQty.ordrevdstsqty_t_freight = (rSQty.ordrevdstsqty_t_cost_freight * (1 + nMrkUpFreight / 100));
    
    if (rSQty.ordrevdstsqty_t_over > 0) {
        rSQty.ordrevdstsqty_t = scopes.avUtils.roundNumber(rSQty.ordrevdstsqty_t_over);
        rSQty.ordrevdstsqty_mrkup_over = (rSQty.ordrevdstsqty_t_over - rSQty.ordrevdstsqty_t_cost - rSQty.ordrevdstsqty_t_freight) / rSQty.ordrevdstsqty_t_cost * 100;
        rSQty.ordrevdstsqty_t_outsourced = rSQty.ordrevdstsqty_t_over - rSQty.ordrevdstsqty_t_freight;
    }
    else {
        rSQty.ordrevdstsqty_t = rSQty.ordrevdstsqty_t_outsourced + rSQty.ordrevdstsqty_t_freight;
    }

    if (!rSQty.ordrevdstsqty_t_cost) {
        rSQty.ordrevdstsqty_t_cost = 0;
    }
    if (!rSQty.ordrevdstsqty_t) {
        rSQty.ordrevdstsqty_t = 0;
    }

    rTaskQty.ordrevdstqty_t_cost_item_orig = 0;

    rTaskQty.ordrevdstqty_t_cost_lab_orig = rSQty.ordrevdstsqty_setup_cost + rSQty.ordrevdstsqty_int_cost + 
        rSQty.ordrevdstsqty_cost_run + rSQty.ordrevdstsqty_freight_in + rSQty.ordrevdstsqty_freight_out;

    rTaskQty.ordrevdstqty_t_orig_x = scopes.avUtils.roundNumber(rSQty.ordrevdstsqty_t);
    rTaskQty.ordrevdstqty_t_orig = scopes.avDetail.applyDetailDiscount(rTaskQty.ordrevdstqty_t_orig_x, null, rTaskQty);

    databaseManager.saveData(rSQty);

    return rTaskQty;
}

/**
 * Gets the Material task for a particular item_id, and sets the qty
 * If the task is not found, then it inserts it
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 19, 2014
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSec - the section
 * @param {UUID|String} sItemUUID - the item uuid
 * @param {Number} nQty - the qty to set the task to
 * 
 * @param {String} [sMatType] - optional material type to find (in case replacing existing), 
 * @param {Boolean} [bAddQty] - option to add qty to existing material task
 * @param {UUID|String} [sTaskId] - if you want this material task to be assoc with a parent task, so that deleting the parent removes the material
 * USE ENUM "TASK_MATERIAL_TYPE"
 * GA - Gumming Adhesive, EA - Envelope Adhesive, SK - Skid, RC - Rollcore, PF - Patch Material Front, PB - Patch Material Back, PA - Patch Adhesive
 * DI - Die press die, FO - Die press foil, WI - Die press wipe, CO - Die press counter
 * CP - Coating Plate/Blanket
 * 
 * @return {JSRecord<db:/avanti/sa_order_revds_task>}
 * 
 * @properties={typeid:24,uuid:"9B9A156A-440E-44E7-9771-DE091C4E2E59"}
 */
function getMaterialTask(rSec, sItemUUID, nQty, sMatType, bAddQty, sTaskId) {
    
    var i = 0,
        rSysTask,
        rTask,
        rTaskQty,
        rSecQty,
        iMaxSeq = 0,
        fsSecQty = rSec.sa_order_revision_detail_section_to_sa_order_revds_qty,
        bFound = false,
        oEstSec = scopes.avSales.oEstOrd.getSectionObject(rSec.ordrevds_id);

    // First try to find task by sMatType
    if (sMatType) {
        
        for (i = 0; i < oEstSec.aTasks.length; i++) {
    
            rTask = oEstSec.aTasks[i].rTask;
            iMaxSeq = rTask.sequence_nr;

            if (oEstSec.aTasks[i].iTaskTypeId == TASKTYPEID.Material 
                    && rTask.ordrevdstask_material_type == sMatType) {

                rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1);
                rTaskQty.item_id = sItemUUID;
                if (bAddQty) {   
                    rTaskQty.ordrevdstqty_qty_item += nQty;
                    rTaskQty.ordrevdstqty_qty_item_over += nQty;
                }
                else {
                    rTaskQty.ordrevdstqty_qty_item = nQty;
                    rTaskQty.ordrevdstqty_qty_item_over = nQty;
                }
                bFound = true;
                break;
            }
        }
    }
    
    // If we did not find task by sMatType, then try by ItemID
    if (!bFound 
    		&& sMatType != TASK_MATERIAL_TYPE.Patch_Material_Back
			&& sMatType != TASK_MATERIAL_TYPE.Patch_Material_Front) {
        
        for (i = 0; i < oEstSec.aTasks.length; i++) {

            rTask = oEstSec.aTasks[i].rTask;
            iMaxSeq = rTask.sequence_nr;
            
            if (oEstSec.aTasks[i].iTaskTypeId == TASKTYPEID.Material 
                    && rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.item_id == sItemUUID) {

                rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1);
                if (bAddQty) {   
                    rTaskQty.ordrevdstqty_qty_item += nQty;
                    rTaskQty.ordrevdstqty_qty_item_over += nQty;
                }
                else {
                    rTaskQty.ordrevdstqty_qty_item = nQty;
                    rTaskQty.ordrevdstqty_qty_item_over = nQty;
                }

                bFound = true;
                break;
            }
        }
    }

    // If still not found then, insert we need to insert the task
    if (!bFound) {

        globals.avSales_selectedRevisionSectionTaskTypeID = TASKTYPEID.Material;
        if (utils.hasRecords(_to_sa_task$avsales_selectedrevisionsectiontasktypeid)) {
            rSysTask = _to_sa_task$avsales_selectedrevisionsectiontasktypeid.getRecord(1);

            if (rSysTask) {
            	var fsSecTasks = rSec.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted;
            	
                rTask = fsSecTasks.getRecord(fsSecTasks.newRecord(false, true));
                rTask.task_id = rSysTask.task_id;
                rTask.sequence_nr = iMaxSeq;
                rTask.ordrevdstask_material_type = sMatType;
                rTask.plant_id = rSec.plant_id;
                rTask.div_id = rSec.div_id;
                rTask.ordrevdstask_id_assoc = sTaskId;
                
                databaseManager.saveData(rTask);

                // Create qty records for the task
                for (i = 1; i <= fsSecQty.getSize(); i++) {

                    rSecQty = fsSecQty.getRecord(i);
                    rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(rTask.sa_order_revds_task_to_sa_order_revds_task_qty.newRecord(false, true));
                    rTaskQty.ordrevdsqty_id = rSecQty.ordrevdsqty_id;
                    rTaskQty.ordrevdqty_id = rSecQty.ordrevdqty_id;
                    rTaskQty.item_id = sItemUUID;
                    rTaskQty.ordrevdstqty_apply_price_rules = rSecQty.sa_order_revds_qty_to_sa_order_revision_detail_qty.ordrevdqty_apply_price_rules; // When adding new tasks, the apply price rule default comes from the detail qty record

                    if (rTaskQty.ordrevdqty_id === globals.avSales_selectedRevisionDetailQtyUUID
                            || sMatType == TASK_MATERIAL_TYPE.Coating_Plate_Blanket) {
                        rTaskQty.ordrevdstqty_qty_item = nQty;
                        rTaskQty.ordrevdstqty_qty_item_over = nQty;
                    }
                }

                databaseManager.saveData(rTask.sa_order_revds_task_to_sa_order_revds_task_qty);

                if (!scopes.avWebToPrint.bFromWebToPrint) {
                    scopes.avSales.oEstOrd.refreshTasks(rTask.ordrevds_id);
                }
            }
        }
    }
    return rTask;
}


/**
 * Get or add the postage task to a section
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSec - the section
 * @param {Number} nQty - the qty to set the task to
 * @param {UUID} [sContractID]
 * @param {UUID} [sMatTaskQtyID]
 * 
 * @properties={typeid:24,uuid:"BBBF1DB8-45A3-4891-8523-58EB783BF989"}
 * @AllowToRunInFind
 */
function getPostageTask(rSec, nQty, sContractID, sMatTaskQtyID) {
    var i = 0,
        rSysTask,
        rTask,
        rTaskQty,
        iMaxSeq = 0,
        fsSecTasks = rSec.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted,
        bFound = false,
        rPostTask,
        rPostAcct;

    // Loop throught the tasks for the section and see if we can find a postage task with the item
    for (i = 1; i <= fsSecTasks.getSize(); i++) {

        rTask = fsSecTasks.getRecord(i);
        iMaxSeq = rTask.sequence_nr;
        if (rTask.sa_order_revds_task_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.Postage) {

            rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(1);
            rTaskQty.ordrevdstqty_qty_item = nQty;
            rTaskQty.ordrevdstqty_qty_item_over = nQty;
            
            rPostTask = rTask.sa_order_revds_task_to_sa_order_revds_task_post.getRecord(1);
            rPostTask.postacct_id = rSec.postacct_id;

            if (sContractID) {
                rPostTask.postacctcontract_id = sContractID;
            }
            
            if (sMatTaskQtyID) {
                rPostTask.material_ordrevdstqty_id = sMatTaskQtyID;
            }
            
            // if we have an indicia num on the post task then set the postage task not_billable flag from the is_billable on the indicia (postage acct) 
            if (utils.hasRecords(rPostTask.sa_order_revds_task_post_to_sys_post_account)) {
                rPostAcct = rPostTask.sa_order_revds_task_post_to_sys_post_account.getRecord(1);
                
                rPostTask.not_billable = rPostAcct.is_billable ? 0 : 1;
                rPostTask.ach_ref_num = rPostAcct.indicia_num;
            }
            
            databaseManager.saveData(rTask);                        
            databaseManager.saveData(rPostTask);                        
            
            bFound = true;
            break;
        }
    }

    // If not found then insert it
    if (!bFound) {

        globals.avSales_selectedRevisionSectionTaskTypeID = 34;
        if (utils.hasRecords(_to_sa_task$avsales_selectedrevisionsectiontasktypeid)) {
            if(_to_sa_task$avsales_selectedrevisionsectiontasktypeid.find()) {
                _to_sa_task$avsales_selectedrevisionsectiontasktypeid.sa_task_to_sa_task_standard.use_for_mailing_items = 1;
                if(_to_sa_task$avsales_selectedrevisionsectiontasktypeid.search() > 0) {
                    rSysTask = _to_sa_task$avsales_selectedrevisionsectiontasktypeid.getRecord(1);

                    if (rSysTask) {
                        forms["sa_order_revision_detail_task_tbl"].onDataChange_task('', rSysTask.task_id, new JSEvent());
                        forms["sa_order_revision_detail_task_tbl"]._task_id = rSysTask.task_id;
                        forms["sa_order_revision_detail_task_tbl"].resetAddItemRowLabels();
                        
                        rTask = forms["sa_order_revision_detail_task_tbl"].saveAddItemRow(new JSEvent());
                        rTask.sequence_nr = iMaxSeq;

                        rPostTask = rTask.sa_order_revds_task_to_sa_order_revds_task_post.getRecord(1);
                        rPostTask.postacct_id = rSec.postacct_id;
                        
                        if (sContractID) {
                            rPostTask.postacctcontract_id = sContractID;
                        }

                        if (sMatTaskQtyID) {
                            rPostTask.material_ordrevdstqty_id = sMatTaskQtyID;
                        }
                        
                        // if we have an indicia num on the post task then set the postage task not_billable flag from the is_billable on the indicia (postage acct) 
                        if (utils.hasRecords(rPostTask.sa_order_revds_task_post_to_sys_post_account)) {
                            rPostAcct = rPostTask.sa_order_revds_task_post_to_sys_post_account.getRecord(1);
                            
                            rPostTask.not_billable = rPostAcct.is_billable ? 0 : 1;
                            rPostTask.ach_ref_num = rPostAcct.indicia_num;
                        }
                        
                        databaseManager.saveData(rTask);                        
                        databaseManager.saveData(rPostTask);                        
                    }
                }
            }            
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 *
 * @properties={typeid:24,uuid:"2282CBA0-3D01-4CF2-8803-EAE71252D934"}
 */
function autoCalcCoilSize(rRecQty){
	
	if (!rRecQty) return;
	
	rRecQty.ordrevdstqty_calc_spine_thick = getSpineThickness(rRecQty);
	
	// GD - Aug 10, 2016: SL-9521: Use coil size override if available
	if (rRecQty.ordrevdstqty_coil_size_over) {
		
		rRecQty.ordrevdstqty_calc_coil_size = rRecQty.ordrevdstqty_coil_size_over;
		
	} else {
		
		rRecQty.ordrevdstqty_calc_coil_size = getCoilSize(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1), rRecQty.ordrevdstqty_calc_spine_thick)
		
	}
	
	// sl-2122b
//	rRecQty.ordrevdstqty_coil_item_id = getCoilItem(rRecQty, rRecQty.ordrevdstqty_calc_coil_size)
	getMatchingCoils(rRecQty, rRecQty.ordrevdstqty_calc_coil_size)
	
	if(rRecQty.ordrevdstqty_coil_item_id){
		rRecQty.ordrevdstqty_coil_qty = getCoilQty(rRecQty)
		application.output("Coil Qty: " + rRecQty.ordrevdstqty_coil_qty);
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 *
 * @return
 * @properties={typeid:24,uuid:"CC612820-420D-4FCA-B222-3614770EFB00"}
 */
function getCoilQty(rRecQty){
    var qty = 0,
        rSect = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
        fs = rSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children,
        rChild = null,
        nCoilQty = 0;
    
    if (rSect.ordrevds_is_gang == 1
            && utils.hasRecords(fs)) {
        
        // Process child sections to get the qty of coil material needed
        for (var i = 1; i <= fs.getSize(); i++) {
            rChild = fs.getRecord(i);
            qty = rChild.sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdsqty_qty;
            if(utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_sa_task_machine)
                    && rRecQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_fs1 === 1){
                nCoilQty += qty * parseFloat(rChild.ordrevds_trim_size_width);
            }
            else{
                nCoilQty += qty * parseFloat(rChild.ordrevds_trim_size_length);
            }
        }   
        return nCoilQty;
    }
    else {  
    	if(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_is_assembly){
    		qty = rRecQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty.ordrevdqty_qty; 
    	}
    	else if(utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_qty)){
    		qty = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_qty.ordrevdsqty_qty; 
    	}	
    	else{
    		qty = rRecQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty.ordrevdqty_qty; 
    	}
    	
    	if(rRecQty.sa_order_revds_task_qty_to_in_item$coil.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code == 'EA'){
    		return qty;
    	}
    	else{ // rolls
    		return qty * getFinishedCoilLength(rRecQty);	
    	}
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 * @param {Number} coilSize
 *
 * @return
 * @properties={typeid:24,uuid:"9240AE48-CDD1-4D05-83B3-B06C17889A98"}
 */
function getCoilItem(rRecQty, coilSize){
		/**@type {JSFoundset<db:/avanti/in_item>} **/
	var fsMatchingCoils = getMatchingCoils(rRecQty, coilSize)
	if(fsMatchingCoils.getSize() > 0){
		return fsMatchingCoils.getRecord(1).item_id;
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 * @param {Number} coilSize
 * @return {JSFoundset<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"577780A4-6B62-4564-8FC8-A5473C32FFBF"}
 * @AllowToRunInFind
 */
function getMatchingCoils(rRecQty, coilSize){
	var itemClass = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_spine_item_class
	var aReturn = []
	var aDisplay = []
	var bExistingCoilMatches = false
	
	// match based on these criteria:
	// 1. itemclass
	// 2. coil width
	// 3. uom=roll || (uom=each && coil length is large enough to accomodate fin piece length)
//	
	/***@type {JSFoundset<db:/avanti/in_item>} */
	var fsMatchingCoils = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
	
	// GD - Jan 29, 2015: SL-4114 - David reporting that changes not showing up until you log out and back in; trying to force the client to refresh
	databaseManager.saveData(fsMatchingCoils);
//	databaseManager.refreshRecordFromDatabase(fsMatchingCoils,-1); // Uncomment this if still not working with the current fix?
	fsMatchingCoils.loadAllRecords();

	if(fsMatchingCoils.find()){
		fsMatchingCoils.itemclass_id = itemClass;
		fsMatchingCoils.item_status = 'A';
		fsMatchingCoils.item_dimension_width = '>=' + coilSize.toString() // dp says bigger coils too
		// GD - Jan 30, 2015: I took this out, because if they are stocking in inches or feet, nothing shows up
//		fsMatchingCoils.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code = 'EA||RL'
			
		if(fsMatchingCoils.search()){
			fsMatchingCoils.sort('item_dimension_width asc')
			var finishedCoilLength = getFinishedCoilLength(rRecQty)
			
			for(var c=1;c<=fsMatchingCoils.getSize();c++){
				var rec = fsMatchingCoils.getRecord(c)

				// 3rd condition - length must be adequate
				if(utils.hasRecords(rec, 'in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom') && rec.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code == 'EA' && rec.item_dimension_length < finishedCoilLength){
					fsMatchingCoils.omitRecord(c)
					c--
				}
				else{
					aReturn.push(rec.item_id)
					aDisplay.push(rec.item_desc1)
					
					if(rec.item_id == rRecQty.ordrevdstqty_coil_item_id && rRecQty.ordrevdstqty_coil_item_over){
						bExistingCoilMatches = true
					}
				}
			}

			// sl-2122b
			if(bExistingCoilMatches == false && fsMatchingCoils.getSize() > 0){
				rRecQty.ordrevdstqty_coil_item_id = fsMatchingCoils.getRecord(1).item_id
			}
		}
		else if(rRecQty.ordrevdstqty_coil_item_id){
			rRecQty.ordrevdstqty_coil_item_id = null
		}
	}
	
	application.setValueListItems('vl_coilMatches', aDisplay, aReturn);
	
	return fsMatchingCoils
}

//function getMatchingCoils_OLD(rRecQty, coilSize){
//	var itemClass = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_spine_item_class
//	var aReturn = []
//	var aDisplay = []
//	
//	// match based oin these criteria:
//	// 1. itemclass
//	// 2. coil width
//	// 3. uom=roll || (uom=each && coil length is large enough to accomodate fin piece length)
////	
//	/***@type {JSFoundset<db:/avanti/in_item>} */
//	var fsMatchingCoils = scopes.avDB.getFS('in_item', ['itemclass_id', 'item_dimension_width'], [itemClass, coilSize], 'item_code asc') // dp says bigger coils too
//
//	var finishedCoilLength = getFinishedCoilLength(rRecQty)
//	
//	for(var c=1;c<=fsMatchingCoils.getSize();c++){
//		var rec = fsMatchingCoils.getRecord(c)
//
//		// we only really know how to measure qty for rolls and each
//		if(rec.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code != 'EA' && rec.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code != 'RL'){
//			fsMatchingCoils.omitRecord(c)
//			c--
//		}
//		// 3rd condition - length must be adequate
//		else if(rec.in_item_to_sys_unit_of_measure_stocking_uom.sys_unit_of_measure_to_app_uom.appuom_code == 'EA' && rec.item_dimension_length < finishedCoilLength){
//			fsMatchingCoils.omitRecord(c)
//			c--
//		}
//		else{
//			aReturn.push(rec.item_id)
//			aDisplay.push(rec.item_desc1)
//		}
//	}
//	
//	application.setValueListItems('vl_coilMatches', aDisplay, aReturn);
//	
//	
//	return fsMatchingCoils
//}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 *
 * @return
 * @properties={typeid:24,uuid:"454222CA-15B2-4185-AB4E-FC405C899E64"}
 */
function getFinishedCoilLength(rRecQty){
	var rSect = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);

	if(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_is_assembly){
		rSect = getPrevSection(rSect)
		if(!rSect){
			return 0
		}
	}

	// GD - Aug 31, 2015: SL-5618 - was not switching to use other dimension, fs1 could be 0, so it was always using the length
	if(utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_sa_task_machine)
			&& rRecQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_fs1 === 1){
		return parseFloat(rSect.ordrevds_trim_size_width)
	}
	else{
		return parseFloat(rSect.ordrevds_trim_size_length)
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"82315469-CA98-49DE-A1EB-D4502F25B335"}
 */
function getPrevSection(rSect){
	if(rSect.sequence_nr > 1){
		var fsSections = rSect.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section.duplicateFoundSet(); 
		fsSections.sort('sequence_nr asc');
		return fsSections.getRecord(rSect.sequence_nr-1);
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @param {Number} spineThickness
 *
 * @return
 * @properties={typeid:24,uuid:"962BB695-2825-463C-B7D0-83F08FECC41F"}
 */
function getCoilSize(rTask, spineThickness){
	
	var coilSize = 0,
		rStdTask = (utils.hasRecords(rTask.sa_order_revds_task_to_sa_task)) ? rTask.sa_order_revds_task_to_sa_task.getRecord(1) : null,
		rStdTaskStd = (rStdTask && utils.hasRecords(rStdTask.sa_task_to_sa_task_standard)) ? rStdTask.sa_task_to_sa_task_standard.getRecord(1) : null;
		
	if (!rStdTaskStd) return 0;
	
	// Check to see if we have a variable amount to add to the coil
	if (rStdTaskStd.taskstd_auto_calc_coil_size == 1 && rStdTaskStd.taskstd_std_spine_thickness == 0) {
		
		coilSize = spineThickness + getVaribleAmountToAddToSpineThickness(rStdTask, spineThickness);
//		application.output("variable amount to add to spine thickness: " + (coilSize - spineThickness));
		
	} else {
		
		coilSize = spineThickness + rStdTaskStd.taskstd_add_to_spine_thickness;
//		application.output("Standard amount to add to spine thickness: " + (coilSize - spineThickness));
	}
	
	var remainder = coilSize % rStdTaskStd.taskstd_spine_rounding_factor;
	
	if(remainder){
		
		coilSize = coilSize - remainder;
		coilSize = coilSize + rStdTaskStd.taskstd_spine_rounding_factor;
	}
	
	return coilSize;
}

/**
 * <AUTHOR> Dotzlaw
 * @since Aug 11, 2016
 * @param {JSRecord<db:/avanti/sa_task>} rTask - the sa_task record
 * @param {Number} nSpine - the spine thickness
 * @returns {Number} The amount to add to the spine thickness
 * @private
 *
 * @properties={typeid:24,uuid:"B9C62434-8A6E-4B59-86C9-7DF77EC4464A"}
 */
function getVaribleAmountToAddToSpineThickness (rTask, nSpine) {
	
	var nAmountToAdd = 0,
		fsSpeed = null,
		rSpeed = null,
		i = 0;
	
	if (!rTask || !utils.hasRecords(rTask.sa_task_to_sa_task_speed) || !nSpine) return 0;
	
	fsSpeed = rTask.sa_task_to_sa_task_speed;
	fsSpeed.sort("sequence_nr asc");
	
	for ( i = 1; i <= fsSpeed.getSize(); i++ ) {
		
		rSpeed = fsSpeed.getRecord(i);
		
		if (rSpeed.taskspeed_from_float <= nSpine && (rSpeed.taskspeed_to_float >= nSpine || rSpeed.taskspeed_to_float == null)) break;
		
	}
	
	if (rSpeed) nAmountToAdd = rSpeed.taskspeed_nr;
	
	return nAmountToAdd;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rRecQty
 *
 * @return
 * @properties={typeid:24,uuid:"704AC1E6-BCC1-437F-949C-01F203F059DB"}
 */
function getSpineThickness(rRecQty) {
    var totSpineThickness = 0;

    if (rRecQty && utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task) 
            && rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_is_assembly) {

        var fsSects = rRecQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty.sa_order_revision_detail_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section;
        fsSects.sort('sequence_nr asc');

        // loop backwards collecting all sections until we hit the start or hit another assembly section
        for (var s = rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.sequence_nr - 1; s > 0; s--) {
            var sect = fsSects.getRecord(s);

            if (sect) {
                if (sect.ordrevds_is_assembly) {
                    break;
                }
                else if (sect.is_parent == 1) {
                    // Skip the parent of child records; not sure there are any possible exceptions
                } 
                else {
                    totSpineThickness += getSpineThicknessForSection(sect);
                }
            }
        }
    }
    else if (rRecQty && utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task)) {
        totSpineThickness = getSpineThicknessForSection(rRecQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1));
    }

    // backer
    if (rRecQty && utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_in_item)) {
        var recItem = rRecQty.sa_order_revds_task_qty_to_in_item.getRecord(1);

        if (recItem.in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id == 'FI') {
            totSpineThickness += recItem.item_caliper;
        }
        else if (utils.hasRecords(recItem.in_item_to_in_item_paper)) {
            totSpineThickness += recItem.in_item_to_in_item_paper.paper_caliper;
        }
    }

    // cover
    if (rRecQty && utils.hasRecords(rRecQty.sa_order_revds_task_qty_to_in_item$item_id_2)) {
        var recItem2 = rRecQty.sa_order_revds_task_qty_to_in_item$item_id_2.getRecord(1);

        if (recItem2.in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_id == 'FI') {
            totSpineThickness += recItem2.item_caliper;
        }
        else if (utils.hasRecords(recItem2.in_item_to_in_item_paper)) {
            totSpineThickness += recItem2.in_item_to_in_item_paper.paper_caliper;
        }
    }

    return totSpineThickness;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"1CA9B060-CCBF-4F7F-A900-7D986E24BBC2"}
 */
function getSpineThicknessForSection(rSect) {
    if (utils.hasRecords(rSect.sa_order_revision_detail_section_to_in_item_paper)) {
        // GD - Dec 30, 2014: SL-3790: Spine thickness needs to be divided by 2
        if (rSect.ordrevds_pages >= 2 && rSect.ordrevds_work_type != "F") {
            return rSect.ordrevds_pages / 2 * scopes.avSection.getSectionSubstrateCaliper(rSect);
        }
        else {
            return rSect.ordrevds_pages * scopes.avSection.getSectionSubstrateCaliper(rSect);
        }
    }
    else {
        return 0;
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"F8493568-9203-44BC-ACAF-244CF1DBE698"}
 */
function getSectQty(rSect){
	// cumbersome way to get this. there should be a direct connection between sa_order_revision_detail_section and sa_order_revds_qty
	var ordrevdqty_id = scopes.avDB.Query('sa_order_revision_detail_qty', ['ordrevd_id'], [rSect.ordrevd_id], 'ordrevdqty_id')
	if(ordrevdqty_id){
		/***@type {JSRecord<db:/avanti/sa_order_revds_qty>} */
		var rec = scopes.avDB.getRec('sa_order_revds_qty', ['ordrevdqty_id', 'sequence_nr'], [ordrevdqty_id, rSect.sequence_nr])
		if(rec){
			return rec.ordrevdsqty_qty
		}
	}

	return 0 
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"22ECF853-4CF3-43CD-AC91-6E18C4DC4C3B"}
 */
function getSectQtyRec(rSect){
	// cumbersome way to get this. there should be a direct connection between sa_order_revision_detail_section and sa_order_revds_qty
	var ordrevdqty_id = scopes.avDB.Query('sa_order_revision_detail_qty', ['ordrevd_id'], [rSect.ordrevd_id], 'ordrevdqty_id')
	if(ordrevdqty_id){
		/***@type {JSRecord<db:/avanti/sa_order_revds_qty>} */
		var rec = scopes.avDB.getRec('sa_order_revds_qty', ['ordrevdqty_id', 'sequence_nr'], [ordrevdqty_id, rSect.sequence_nr])
		if(rec){
			return rec
		}
	}

	return null
}

/**
 * @param {String} orderNum
 * @param {String} lineDesc
 * @param {String} sectDesc
 * @param {String} taskDesc
 *
 * <AUTHOR> - i put this in for debugging purposes
 * @return
 * @properties={typeid:24,uuid:"EB5119B3-929A-4F6F-A988-6582DE04FF07"}
 * @AllowToRunInFind
 */
function getTaskQtyMachine(orderNum, lineDesc, sectDesc, taskDesc){
//	getTaskQtyMachine('ST00000004', '20pg+Cov8.5x11: 20 Pages + Cover  8.5x11', 'Text (1x4)', 'Mac Prep')

	/***@type {JSRecord<db:/avanti/sa_order>} */
	var rOrd = scopes.avDB.getRec('sa_order', ['ordh_document_num'], [orderNum])
	if(rOrd){
		/***@type {JSRecord<db:/avanti/sa_order_revision_header>} */
		var rRev = scopes.avDB.getRecFromFS(rOrd.sa_order_to_sa_order_revision_header, ['ordrevh_revision'], [0])
		if(rRev){
			rRev.sa_order_revision_header_to_sa_order_revision_detail.loadAllRecords()
			/***@type {JSRecord<db:/avanti/sa_order_revision_detail>} */
			var rLine = scopes.avDB.getRecFromFS(rRev.sa_order_revision_header_to_sa_order_revision_detail, ['ordrevd_prod_desc'], [lineDesc])
			if(rLine){
				rLine.sa_order_revision_detail_to_sa_order_revision_detail_section.loadAllRecords()
				/***@type {JSRecord<db:/avanti/sa_order_revision_detail_section>} */
				var rSect = scopes.avDB.getRecFromFS(rLine.sa_order_revision_detail_to_sa_order_revision_detail_section, ['ordrevds_description'], [sectDesc])
				if(rSect){
					rSect.sa_order_revision_detail_section_to_sa_order_revds_task.loadAllRecords()
					for(var i=1;i<=rSect.sa_order_revision_detail_section_to_sa_order_revds_task.getSize();i++){
						/***@type {JSRecord<db:/avanti/sa_order_revds_task>} */
						var rTask = rSect.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(i)
						if(rTask.sa_order_revds_task_to_sa_task.task_description == taskDesc){
							return rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_description
						}
					}
				}
			}
		}
	}
	
	return null
}

/////////////////////////////

/**
 * <AUTHOR>
 * @param {scopes.avSales.oEstOrdSection} oSection
 *
 * @returns {Object} The master override object
 *
 * @properties={typeid:24,uuid:"5C2454B4-0F40-4ABF-B605-8879EB922AEA"}
 */
function getTaskQtyOverRideObjects (oSection) {
    
    if (!oSection) {
        return;
    }
	
	var sTempQtyUUID = globals.avSales_selectedRevisionDetailQtyUUID,
		rSec = oSection.rSection;
	
	if (!rSec || rSec.ordrevds_line_type === "A" || rSec.ordrevds_line_type === "G") return null;
	
	// Initialize the global master object
	oTaskQtyOver = {};

	var bProcessedChildren = false;
	if (utils.hasRecords(rSec.sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent)){
		// Only work with the parent
		rSec = rSec.sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent.getRecord(1);
		processChildren();
	} 
	else if (utils.hasRecords(rSec.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted)){
		// This section has children, see if anything needs to be captured
		processChildren();
	} 
	
	// Process the main section
	if(bProcessedChildren){
		oSection = scopes.avSales.oEstOrd.getSectionObject(rSec.ordrevds_id);
	}
	processQtys(rSec);
	
	function processChildren(){
		var rChild,
			iChild = 0,
			iChildMax = 0;
		
		iChildMax = rSec.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.getSize();
		for ( iChild = 1; iChild <= iChildMax; iChild++ ) {
			
			rChild = rSec.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.getRecord(iChild);
			
			if (rChild.ordrevds_line_type === "D"){
				oSection = scopes.avSales.oEstOrd.getSectionObject(rChild.ordrevds_id);
				processQtys(rChild);
				bProcessedChildren = true;
			}
		}
	}
	
	/**
	 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection - paramDescription
	 */
	function processQtys(rSection){
		
		var iMaxTask = 0,
			iTask = 0,
			/** @type {JSRecord<db:/avanti/sa_order_revds_task>} */ 
			rTask = null,
			iMaxTaskQty = 0,
			iTaskQty = 0,
			rTaskQty = null,
			rLineQty = null,
			sKey = "",
			oEstOrdDetail = scopes.avSales.oEstOrd.getDetailObject(rSection.ordrevd_id),
			rDetail = null;
		
		if (!oEstOrdDetail) {
			
			// Show a dialog if no object, but not if it is a child missing a parent (case where copy web estimates not working and leaving orphaned children)
			if (rSection.ordrevd_id && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail)) {
				
				var sMsg = i18n.getI18NMessage("avanti.dialog.objectError_msg").replace("<<description>>", rSection.ordrevds_description);
				
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.objectError_title"),
					sMsg,
					i18n.getI18NMessage("avanti.dialog.ok"));
			}
			return;
		}
		
		rDetail = oEstOrdDetail.rDetail;
		
		if (oSection && oSection.aTasks) {
    		iMaxTask = oSection.aTasks.length;
    		for ( iTask = 0; iTask < iMaxTask; iTask++ ) {
    			
    			rTask = oSection.aTasks[iTask].rTask;
    			
    			iMaxTaskQty = oSection.aTasks[iTask].aTaskQtys.length;
    			for ( iTaskQty = 0; iTaskQty < iMaxTaskQty; iTaskQty++ ) {
    				
    				rTaskQty = oSection.aTasks[iTask].aTaskQtys[iTaskQty];
    				rLineQty = rTaskQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty.getRecord(1); 
    				
    				sKey = '"' + rDetail.sequence_nr + rSection.ordrevds_description + rTask.task_id + rTask.ordrevdstask_cut_type + rTaskQty.ordrevdstqty_qty + rLineQty.ordrevdqty_id + '"';
    				
    				oTaskQtyOver[sKey] = getOverRideObject(rTaskQty);
    			}
    		}
		}
		
//		var nTasks = rSection.sa_order_revision_detail_section_to_sa_order_revds_task.getSize()
//		
//		for (var t = 1; t <= nTasks; t++ ){			
//			var rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(t);
//			var nQtys = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getSize() 
//
//			for (var q = 1; q <= nQtys; q++ ){
//				var rQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(q);
//				var key = '"' + rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.sequence_nr + rSection.ordrevds_description + rTask.task_id + rQty.ordrevdstqty_qty + '"'
//				
//				oTaskQtyOver[key] = getOverRideObject(rQty);
//			}
//		}
	}
	
	globals.avSales_selectedRevisionDetailQtyUUID = sTempQtyUUID;
	
	return oTaskQtyOver;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rQty
 *
 * @returns {TaskQtyOvers} An object containing all the task qty overrides 
 *
 * @properties={typeid:24,uuid:"062F2EB3-7558-41F4-9601-141B87DE5706"}
 */
function getOverRideObject (rQty) {
	
	var oObj = new TaskQtyOvers()
	var prop;
	var bFoundOne = false
	
	for(prop in oObj){
		if (rQty[prop] != null){
			oObj[prop] = rQty[prop];
			bFoundOne = true
		}
	}
	
	if(bFoundOne){
		return oObj;
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rQty
 *
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} The task qty record with all the overrides applied 
 *
 * @properties={typeid:24,uuid:"AF07358C-4693-48B8-ADC9-B8199648FBDD"}
 */
function setOverRideObject (rQty){	
	
	if (!rQty) return null;

	var rLineQty = rQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty.getRecord(1);
	var key = '"' + rQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sequence_nr + 
					rQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revision_detail_section.ordrevds_description + 
					rQty.sa_order_revds_task_qty_to_sa_order_revds_task.task_id + 
					rQty.sa_order_revds_task_qty_to_sa_order_revds_task.ordrevdstask_cut_type +
					rQty.ordrevdstqty_qty + 
					rLineQty.ordrevdqty_id + '"';
	var oObj = oTaskQtyOver[key]
	var prop;	
	var non_over_prop;
	
	if(oObj){
		var aCols = rQty.foundset.alldataproviders
		
		for(prop in oObj){
			if (oObj[prop] != null){
				rQty[prop] = oObj[prop];
				non_over_prop = prop.replace('_over', '')
				if(aCols.indexOf(non_over_prop) >= 0){
					rQty[non_over_prop] = oObj[prop];
				}
			}
		}
	}
	
	return rQty;
}

/**
 * Get the task type description based on tasktype_id
 *
 * <AUTHOR> Dol
 * @since Oct 7, 2014
 *
 * @param {Number} iTaskTypeId - Task Type Id
 *
 * @returns {String} Task Type description
 *
 *
 * @properties={typeid:24,uuid:"C2CC237E-649C-49A9-BF98-7C5BE0220595"}
 */
function getTaskTypeDesc(iTaskTypeId) 
{
	/** @type {JSRecord<db:/avanti/app_task_type>} */
	var rTaskType = scopes.avDB.getRec("app_task_type",["tasktype_id"],[iTaskTypeId])
	
	if (rTaskType != null && rTaskType.tasktype_i18n != null)
	{
		return i18n.getI18NMessage(rTaskType.tasktype_i18n);
	}
	else
	{
		return null;
	}
}

/**
 * Get a specific cost centre record for a section task operation cost link based on key
 *
 * @param {JSRecord<db:/avanti/sa_task>} rTask - The Task Record associated with the section task
 * @param {String} sKey - the task operation i18n key (like "avanti.lbl.helper")
 *
 * @returns {JSRecord} The selected cost link record
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"6CA497FF-17CC-449E-A21E-BE85B12E3C98"}
 */
function getTaskOperationCostCentreByOperationKey (rTask, sKey) {
	
	if (rTask)
	{
		var iTaskTypeID = rTask.tasktype_id;

		if (sKey && sKey.search("i18n:") === -1){
			sKey = "i18n:" + sKey;
		}
		
		selectedTaskOperKey = sKey;
		selectedTaskTypeID = iTaskTypeID;
		
		if (utils.hasRecords(_to_app_task_operation$selectedtaskoperkey_selectedtasktypeid)){
			selectedTaskOperID = _to_app_task_operation$selectedtaskoperkey_selectedtasktypeid.taskoper_id.toString();
			selectedTaskID = rTask.task_id.toString();
			if (utils.hasRecords(_to_sa_task_cost_link$selectedtaskid_selectedtaskoperid)
					&& utils.hasRecords(_to_sa_task_cost_link$selectedtaskid_selectedtaskoperid.sa_task_cost_link_to_sys_cost_centre)){ // Make sure there is a cost centre
				return _to_sa_task_cost_link$selectedtaskid_selectedtaskoperid.sa_task_cost_link_to_sys_cost_centre.getRecord(1);
			}
		}
	}
	
	return null;
}

/**
 * @return
 * @properties={typeid:24,uuid:"E187DB85-23D7-4962-9C94-2277AAEB8050"}
 */
function getTaskSortString(){	
	return "sort_key asc"

	// hp - commenetd the below - all recs should have sort_key filled in - this wa done in update scripts
	
//	var oldSortString = "ordrevdstask_sort_level asc, sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.sequence_nr asc, sa_order_revds_task_to_sa_task.sequence_nr asc"
//	var newSortString = "sort_key asc"
//
//	// if they have passed in a foundset - see if we can use the old or new string
//	if(fsOrdTasks){
//		var max = fsOrdTasks.getSize()
//		var nRecFoundWithoutSortKey = false
//		
//		for(var i=1;i<=max;i++){
//			if(!fsOrdTasks.getRecord(i).sort_key){
//				nRecFoundWithoutSortKey = true
//				break
//			}
//		}
//		
//		if(nRecFoundWithoutSortKey){
//			return oldSortString
//		}
//		else{
//			return newSortString
//		}
//	}
//	else{ // just use new string
//		return newSortString
//	}
}

/**
 * @param {Number} taskTypeID
 *
 * @return
 * @properties={typeid:24,uuid:"37A6C0F2-D16E-4552-8CF7-E083B2B68F89"}
 */
function doesTaskTypeAllowAddingOfInterimCutting(taskTypeID){
	// randy + implementers said to use this list
	return (taskTypeID == TASKTYPEID.Padding
			|| taskTypeID == TASKTYPEID.InkJetting
			|| taskTypeID == TASKTYPEID.OfflineCoating
			|| taskTypeID == TASKTYPEID.Folder
			|| taskTypeID == TASKTYPEID.DieCutting
			|| taskTypeID == TASKTYPEID.FolderGluer 
			|| taskTypeID == TASKTYPEID.Laminating
			|| taskTypeID == TASKTYPEID.EdgeBinding
			|| taskTypeID == TASKTYPEID.EdgeFinishing
			|| taskTypeID == TASKTYPEID.OutsideService
			|| taskTypeID == TASKTYPEID.Binding
			|| taskTypeID == TASKTYPEID.Drilling
			|| taskTypeID == TASKTYPEID.Finishing
			|| taskTypeID == TASKTYPEID.Handwork
			|| taskTypeID == TASKTYPEID.Inserting
			|| taskTypeID == TASKTYPEID.Labeling
			|| taskTypeID == TASKTYPEID.Mounting
			|| taskTypeID == TASKTYPEID.OffsetFolderSheeter
			|| taskTypeID == TASKTYPEID.Other
			|| taskTypeID == TASKTYPEID.PackByQty
			|| taskTypeID == TASKTYPEID.PackagingShipping
			|| taskTypeID == TASKTYPEID.Padding
			|| taskTypeID == TASKTYPEID.Shrinkwrapping
	)			
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} [rSection]
 * @param {scopes.avSales.oEstOrdTask} [oEstOrdTask]
 *
 * @return
 * @properties={typeid:24,uuid:"ECCD4A0D-EE70-405B-93A6-8C37216E179D"}
 */
function getDynamicTaskCalcType(rTask, rSection, oEstOrdTask) {
    var calcType = null;
    /** @type {scopes.avSales.oEstOrdSection} */
    var oEstOrdSection = {};

    if (!oEstOrdTask) {
        oEstOrdTask = scopes.avSales.oEstOrd.getTaskObject(rTask.ordrevdstask_id); 
    }

    if (oEstOrdTask && oEstOrdTask.rEstStdTaskStd) {
        calcType = oEstOrdTask.rEstStdTaskStd.taskstd_calc_type;
    }
    // sl-21547 - if we dont have a oEstOrdTask then use relation - this isnt set when we are selecting a new task to add to section
    else if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_task) && utils.hasRecords(rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard)) {
        calcType = rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_calc_type;
    }

	if (calcType == TASK_CALC_TYPE.ProdQtyOrSheetQtyOption) { // either Q (prod qty) or S (sheets)
		if (rSection) {
			oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rSection.ordrevds_id);
		}
		else {
			oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rTask.ordrevds_id);
		}

		// randy said if an interim cut has been done then we are going to use sheets, otherwise use prod qty
		if (hasAnInterimCutBeenDoneOnThisSection(oEstOrdSection, rSection)) {
			calcType = TASK_CALC_TYPE.Sheets;
		}
		else {
			calcType = TASK_CALC_TYPE.ProductionQuantity;
		}
	}
    
    return calcType;
}


/**
 * @param {scopes.avSales.oEstOrdSection} oEstOrdSection
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} [rSection]
 *
 * @return
 * @properties={typeid:24,uuid:"92DAC189-21D3-47CD-9C20-67CF78C1A4A4"}
 */
function hasAnInterimCutBeenDoneOnThisSection(oEstOrdSection, rSection) {
	var i;
	
	// sl-22291 - we were getting an error when adding a work template line with a task that used an Either calctype. the line hadnt finshed adding and we didnt have a oEstOrdSection yet,
	// so it threw an error. so now this function takes rSection as well, and uses that if we dont have a oEstOrdSection.
	if (oEstOrdSection) {
	    for (i = 0; i < oEstOrdSection.aTasks; i++) {
	        if (oEstOrdSection.aTasks[i].rPreCutCuttingTask) {
	            return true;
	        }
	    }
	}
	else if (rSection && rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted) {
	    for (i = 1; i <= rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getSize(); i++) {
	        var rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getRecord(i);

	        if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task)) {
	            return true;
	        }
	    }
	}

    return false;
}


/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 *
 * @return
 * @properties={typeid:24,uuid:"4774D002-D7A0-4E88-8732-693CB0F8A8CA"}
 */
function getPrevTask(rTask){
	var fs = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_task
	fs.sort(getTaskSortString())
	fs.selectRecord(rTask.ordrevdstask_id)
	var thisIdx = fs.getSelectedIndex()
	
	if(thisIdx > 1){
		return fs.getRecord(thisIdx-1)
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @return
 * @properties={typeid:24,uuid:"B2FB96F5-5EE5-4916-AC32-909EA08094FB"}
 */
function getIncomingSheets(rTask){
	var incomingSheets = 0
	var rTaskQty;
	
	if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)) rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1)
	
	if(rTaskQty && rTaskQty.ordrevdstqty_incoming_sheets){
		incomingSheets = rTaskQty.ordrevdstqty_incoming_sheets
	}
	else if(rTaskQty && rTask.isPressTask){
		/***@type {JSRecord<db:/avanti/sa_order_revds_press>} */
		var rPress = scopes.avDB.getRec('sa_order_revds_press', ['ordrevdqty_id', 'ordrevds_id'], [rTaskQty.ordrevdqty_id, rTask.ordrevds_id])
		if(rPress){
			incomingSheets = rPress.mpress_press_sheets - getPretrimSpoils(rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1))				
		}
	}
	// need this here otherwise task after interim cutting task will not get increased number of sheets
	else if(rTaskQty 
			&& utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_precut_qty) 
			&& rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_precut_qty.taskprecutqty_num_sheets){
		incomingSheets = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_precut_qty.taskprecutqty_num_sheets
	}
	else{
		var rPrevTask = getPrevTask(rTask)

		if(rPrevTask && rPrevTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_incoming_sheets){
			var prevTaskQty = rPrevTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getSelectedRecord()
			var prevTaskSpoils = prevTaskQty.ordrevdstqty_spoils_over ? prevTaskQty.ordrevdstqty_spoils_over : prevTaskQty.ordrevdstqty_spoils
			incomingSheets = prevTaskQty.ordrevdstqty_incoming_sheets - prevTaskSpoils  
		}
	}
	
	return incomingSheets
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"01998E3D-6270-4AEC-A2BD-5F34A119CF29"}
 */
function getPretrimSpoils(rSect){
	var spoils = 0
	/***@type {JSRecord<db:/avanti/sa_order_revds_task>} */
	var rPretrimTask = scopes.avDB.getRec('sa_order_revds_task', ['ordrevds_id', 'ordrevdstask_cut_type'], [rSect.ordrevds_id, 'PT'])
	
	if(rPretrimTask){
		if(rPretrimTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_spoils_over){
			spoils = rPretrimTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_spoils_over 
		}
		else{
			spoils = rPretrimTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_spoils
		}
	}
	
	return spoils
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask
 *
 * @return
 * @properties={typeid:24,uuid:"90A4D3EE-09E6-4965-89D5-B489025CAA34"}
 */
function isPressTask(rOrdTask) {
    return rOrdTask.isPressTask;
}

/**
 * @public 
 * 
 * @param {Number} nTaskType
 *
 * @return
 * @properties={typeid:24,uuid:"665A39CD-1EE8-403F-8EA7-9E94570F3BFA"}
 */
function isPressTaskType(nTaskType) {
    return aPressTaskTypes.indexOf(nTaskType) > -1;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} ordTask
 *
 * @return
 * @properties={typeid:24,uuid:"D8ABB273-80E4-4E32-BE6D-C48AD1EBADFF"}
 */
function checkPressTask(ordTask) {
    var bIsPressTask = 0;

    if (utils.hasRecords(ordTask.sa_order_revds_task_to_sa_task) 
        && aPressTaskTypes.indexOf(ordTask.sa_order_revds_task_to_sa_task.tasktype_id) > -1) {
            
        bIsPressTask = 1;
    }

    return bIsPressTask;
}

/**
 * Checks if a task has a click charge cost link
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 *
 * @return
 * @properties={typeid:24,uuid:"09516E8A-0030-46F4-B6CD-E6A03D93FC31"}
 */
function checkIfDigitalPress(rTask){
	
	if(rTask && (rTask.tasktype_id === scopes.avTask.TASKTYPEID.DigitalSheetPress || 
			rTask.tasktype_id === scopes.avTask.TASKTYPEID.DigitalRollPress)){
				return true;
	}
	return false;
}

/**
 * Gets the cost link and then the task and checks if the task is press task then returns the task
 * @param {JSFoundSet<db:/avanti/sa_task_cost_link>} fsCostLink
 *
 * @return
 * @properties={typeid:24,uuid:"106B541E-28B2-4ABF-B765-E34C2F2A9008"}
 */
function checkPressRunCostLink(fsCostLink) {
    for (var i = 1; i <= fsCostLink.getSize(); i++) {
        var rLink = fsCostLink.getRecord(i);
        
        // sl-16112 - added 'task run' condition. as function name indicates we are looking for the press run cost link
        if (utils.hasRecords(rLink.sa_task_cost_link_to_app_task_operation)
                && rLink.sa_task_cost_link_to_app_task_operation.taskoper_key == 'i18n:avanti.lbl.task_run'
                && rLink.sa_task_cost_link_to_sa_task) {
                    
            for (var j = 1; j <= rLink.sa_task_cost_link_to_sa_task.getSize(); j++) {
                var rTask = rLink.sa_task_cost_link_to_sa_task.getRecord(j);

                if (rTask.task_active && isPressTaskType(rTask.tasktype_id)) {
                    return rTask;
                }
            }
        }
    }

    return null;
}

/**
 * Returns the press using the cost links associated to the cost centre of the
 * operation added by the user
 * @param {JSFoundSet<db:/avanti/sa_task_cost_link>} fsCostLink
 *
 * @return
 * @properties={typeid:24,uuid:"770A89B8-5E7E-4862-AC1D-325D977DA666"}
 */
function getOperationPress(fsCostLink){
	var rTask = null;
	
	for(var i = 1; i <= fsCostLink.getSize(); i++){
		var rLink = fsCostLink.getRecord(i);
		if(utils.hasRecords(rLink.sa_task_cost_link_to_sa_task)){
			for(var j = 1; j <= rLink.sa_task_cost_link_to_sa_task.getSize(); j++){
				rTask = rLink.sa_task_cost_link_to_sa_task.getRecord(j);
				if(utils.hasRecords(rTask.sa_task_to_sa_order_revds_press)){
					//Returns the press associated with the task
					return rTask.sa_task_to_sa_order_revds_press.getRecord(1);
				}
			}
		}
	}
	return null;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} ordTask
 *
 * @return
 * @properties={typeid:24,uuid:"14F66D0A-1D7B-458C-8F7C-7306A62D930E"}
 */
function isMainFolder(ordTask){
	// sl-5612 - if taskfolder_not_for_modelling checked then it isnt used for modelling 
	return ordTask.ordrevdstask_fold_type == "S" || (isOnlyFolderInSection(ordTask) && ordTask.ordrevdstask_fold_type != "G" 
		&& !ordTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_folder.taskfolder_not_for_modelling);
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} ordTask
 *
 * @return
 * @properties={typeid:24,uuid:"5CAB25C6-C8D0-4327-BCB1-93C751AE9398"}
 */
function isOnlyFolderInSection(ordTask){
	var bIsOnlyFolder = false
	
	if(ordTask.sa_order_revds_task_to_sa_task.tasktype_id == TASKTYPEID.Folder){
		bIsOnlyFolder = true
		var max = ordTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_task.getSize()

		for(var i=1;i<=max;i++){
			var r = ordTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(i)
			if(r.sa_order_revds_task_to_sa_task.tasktype_id == TASKTYPEID.Folder && r.ordrevdstask_id != ordTask.ordrevdstask_id){
				bIsOnlyFolder = false
				break
			}
		}
	}
	
	return bIsOnlyFolder
}

/**
 * @param {JSRecord<db:/avanti/sa_task_worktype_tasks>} wtTask
 *
 * @return
 * @properties={typeid:24,uuid:"A8C71313-844A-45BF-816B-F085C9012F14"}
 */
function isOnlyFolderInWorkTypeSection(wtTask){
	var bIsOnlyFolder = false

	if(wtTask.sa_task_worktype_tasks_to_sa_task.tasktype_id == TASKTYPEID.Folder){
		bIsOnlyFolder = true
		var max = wtTask.sa_task_worktype_tasks_to_sa_task_worktype_section.sa_task_worktype_section_to_sa_task_worktype_tasks.getSize()

		for(var i=1;i<=max;i++){
			var r = wtTask.sa_task_worktype_tasks_to_sa_task_worktype_section.sa_task_worktype_section_to_sa_task_worktype_tasks.getRecord(i)
			if(r && utils.hasRecords(r.sa_task_worktype_tasks_to_sa_task) && r.sa_task_worktype_tasks_to_sa_task.tasktype_id == TASKTYPEID.Folder && r.worktypetask_id != wtTask.worktypetask_id){
				bIsOnlyFolder = false
				break
			}
		}
	}
	
	return bIsOnlyFolder
}

/**
 * <AUTHOR> Dotzlaw
 * @since Oct 4, 2016
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSec
 * @returns {JSRecord<db:/avanti/sa_task>} Returns the folder
 * @public
 *
 * @properties={typeid:24,uuid:"C3FBA7E1-D0A6-4D0D-958E-85B2F2C454ED"}
 */
function getTaskFolder (rSec) {
	
	var i = 0,
		fsTasks = rSec.sa_task_worktype_section_to_sa_task_worktype_tasks,
		rTask = null,
		rFolder = null;
	
	// Find the first task that is a folder for the worktemplate section and then return it
	for ( i = 1; i <= fsTasks.getSize(); i++ ) {
		
		rTask = fsTasks.getRecord(i);
		
		if (utils.hasRecords(rTask.sa_task_worktype_tasks_to_sa_task)
				&& rTask.sa_task_worktype_tasks_to_sa_task.tasktype_id == TASKTYPEID.Folder) {
					
			rFolder = rTask.sa_task_worktype_tasks_to_sa_task.getRecord(1);
			break;
			
		} 
	}
	return rFolder;
}

/**
 * Gets the time to run a High Die Cutter
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 6, 2015
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * @returns {Number} The time required
 * @public
 *
 * @properties={typeid:24,uuid:"B3834711-0C30-4281-8F9A-F748AF0329A4"}
 */
function getTimeRun_highDieCutter (rTaskQty, oEstOrdTask) {
	
	var 
//		rTask = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getSelectedRecord(),
		nTime = 0,
//		rEstTask = rTask.sa_order_revds_task_to_sa_task.getRecord(1),
		rStd = oEstOrdTask.rEstStdTaskStd, //rEstTask.sa_task_to_sa_task_standard.getRecord(1),
		rMachine;

	if (rTaskQty.taskmachine_id_over) {
		scopes.avUtils.UUID_1 = rTaskQty.taskmachine_id_over;
	} else {
		scopes.avUtils.UUID_1 = rTaskQty.taskmachine_id;
	}
	if (utils.hasRecords(_to_sa_task_machine$uuid_1)) {
		rMachine = _to_sa_task_machine$uuid_1.getRecord(1);
	}
	if (!rMachine || !rStd.taskstd_inches_per_lift) return 0;
	
	nTime = rMachine.taskmachine_strokes_lift * globals["avCalcs_task_getLifts"](rTaskQty, oEstOrdTask) / rMachine.taskmachine_strokes_hr * 60;
	
	return nTime;
}

/**
 * Gets the time to run 
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 29, 2015
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - The Task Qty record
 * @param {scopes.avSales.oEstOrdTask} oEstOrdTask
 * @returns {Number} The time required
 * @public
 *
 * @properties={typeid:24,uuid:"31A58B3C-DFFA-4400-9B80-71E529C3FBC0"}
 */
function getTimeRun_paddingFanningCarbonlessSets (rTaskQty, oEstOrdTask) {
	
	var 
//		rTask = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getSelectedRecord(),
		nTime = 0,
//		rEstTask = rTask.sa_order_revds_task_to_sa_task.getRecord(1),
		rStd = oEstOrdTask.rEstStdTaskStd, //rEstTask.sa_task_to_sa_task_standard.getRecord(1),
		rMachine;
	
	if (rTaskQty.taskmachine_id_over) {
		scopes.avUtils.UUID_1 = rTaskQty.taskmachine_id_over;
	} else {
		scopes.avUtils.UUID_1 = rTaskQty.taskmachine_id;
	}
	if (utils.hasRecords(_to_sa_task_machine$uuid_1)) {
		rMachine = _to_sa_task_machine$uuid_1.getRecord(1);
	}
	if (!rMachine || !rStd.taskstd_inches_per_lift) return 0;
	
	nTime = globals["avCalcs_task_getLifts"](rTaskQty, oEstOrdTask) / rMachine.taskmachine_per_hour * 60;
	
	return nTime;
}

/**
 * Sets the Hi Die for Converting press
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 7, 2015
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rRec - The Task record
 * @param {Boolean} [bConvertingPress] - option to force the function to recognize the Flexo press as a converting press
 * @returns {JSRecord<db:/avanti/sa_order_revds_task>} 
 * @public
 *
 * @properties={typeid:24,uuid:"6EED6647-FF20-4F48-ACFD-BCDE50005EC6"}
 */
function setHighDie_forConvertingPress (rRec, bConvertingPress) {
	
	var rRecQty,
		rSection,
//		rSpoil,
//		rSetup = null,
		fsMachine = null,
		rMachine = null,
		i = 0;
	
	if (!utils.hasRecords(rRec.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid) || !utils.hasRecords(rRec.sa_order_revds_task_to_sa_order_revision_detail_section)) return rRec;
		
	rRecQty = rRec.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1);
	rSection = rRec.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
	
	// GD - Dec 23, 2015: Check for gang
	if (rSection.ordrevds_is_gang == 1 
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$gang)
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$gang.sa_order_revision_detail_section_to_sa_order_revision_detail_section$use_prev_section)) {
		
		rSection = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$gang.getRecord(1).sa_order_revision_detail_section_to_sa_order_revision_detail_section$use_prev_section.getRecord(1);
	}
	
	// GD - Feb 7, 2015: If this is a hidie with a converting press, then get all its info from the converting press
	if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid) 
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task)
			&& rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.FlexoPress
			&& (rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.sa_task_to_sa_task_standard.taskstd_is_converting_press === 1
			        || bConvertingPress)
			&& rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.sa_task_to_sa_task_standard.taskstd_press_type === "B"
			&& rSection.item_id_die
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_in_item$die)
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_in_item$die.in_item_to_in_item_die)
			&& rSection.itemdie_up
			&& utils.hasRecords(rRec.sa_order_revds_task_to_sa_task)
			&& utils.hasRecords(rRec.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_machine)){
		
		rRecQty.item_id_die = rSection.item_id_die;
		
		rRecQty.ordrevdstqty_sheet_size = rSection.itemdie_sheet_w + " x " + rSection.itemdie_sheet_l;
		rRecQty.ordrevdstqty_sheet_size_over = rRecQty.ordrevdstqty_sheet_size;
		rRecQty.ordrevdstqty_up = rSection.itemdie_up;
		rRecQty.ordrevdstqty_is_converting = 1;
		
		fsMachine = rRec.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_machine;
		
		for ( i = 1; i <= fsMachine.getSize(); i++ ) {
			
			rMachine = fsMachine.getRecord(i);
			
			if ((rRecQty.taskmachine_id_over && rRecQty.taskmachine_id_over == rMachine.taskmachine_id)
			        || (!rRecQty.taskmachine_id_over && rMachine.taskmachine_nr_up === rSection.itemdie_up)) {
				
				rRecQty.taskmachine_id = rMachine.taskmachine_id;
				rRecQty.taskmachine_id_over = rMachine.taskmachine_id;
				rRecQty.taskspoil_id = null;
				rRecQty.taskspoil_id_over = null;
				rRecQty.tasksetup_id = null;
				rRecQty.tasksetup_id_over = null;

				if (utils.hasRecords(rMachine.sa_task_machine_to_sa_task_spoil)) {
					if (rMachine.sa_task_machine_to_sa_task_spoil.getSize() == 1) {
						rRecQty.taskspoil_id = rMachine.sa_task_machine_to_sa_task_spoil.taskspoil_id;
						rRecQty.taskspoil_id_over = rMachine.sa_task_machine_to_sa_task_spoil.taskspoil_id;
					}
					else {
						// sl-21808 - getFirstRecUsingOldSort() is just a temp fix to make it select the record it used to, before the pk changes. we will delete this once the bad data is corrected.
						/**@type {JSRecord<db:/avanti/sa_task_spoil>} */
						var rSpoil = scopes.avDB.getFirstRecUsingOldSort(rMachine.sa_task_machine_to_sa_task_spoil, "sa_task_spoil", "taskspoil_id", "taskmachine_id", rMachine.taskmachine_id);
						
						if (rSpoil) {
							rRecQty.taskspoil_id = rSpoil.taskspoil_id;
							rRecQty.taskspoil_id_over = rSpoil.taskspoil_id;
						}
					}
				} 
				
				if (rRec.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_variable_data == 1 && utils.hasRecords(rMachine.sa_task_machine_to_sa_task_setup)) {
					if (rMachine.sa_task_machine_to_sa_task_setup.getSize() == 1) {
						rRecQty.tasksetup_id = rMachine.sa_task_machine_to_sa_task_setup.tasksetup_id;
						rRecQty.tasksetup_id_over = rMachine.sa_task_machine_to_sa_task_setup.tasksetup_id;
					}
					else {
						// sl-21808 - getFirstRecUsingOldSort() is just a temp fix to make it select the record it used to, before the pk changes. we will delete this once the bad data is corrected.
						/**@type {JSRecord<db:/avanti/sa_task_setup>} */
						var rSetup = scopes.avDB.getFirstRecUsingOldSort(rMachine.sa_task_machine_to_sa_task_setup, "sa_task_setup", "tasksetup_id", "taskmachine_id", rMachine.taskmachine_id);
						
						if (rSetup) {
							rRecQty.tasksetup_id = rSetup.tasksetup_id;
							rRecQty.tasksetup_id_over = rSetup.tasksetup_id;
						}
					}
				} 
				
				break;
			}
		} 		
	} 
	else {
		
		rRecQty.ordrevdstqty_is_converting = null;
	}
	
	databaseManager.saveData(rRecQty);
	
	return rRec;
}

/**
 * <AUTHOR>
 * 
 * @param {UUID|String} taskID
 *
 * @return
 * @properties={typeid:24,uuid:"6EBF9027-586C-4F35-9179-742924F91A65"}
 */
function getTaskTypeDescr(taskID){
	/**@type {JSRecord<db:/avanti/sa_task>} */
    var rTask = scopes.avDB.getRec('sa_task', ['task_id'], [taskID.toString()]);
    if (rTask) {
        return i18n.getI18NMessage(rTask.sa_task_to_app_task_type.tasktype_i18n);
    }

    return null;
}

/**
 * @param {Number} iTaskTypeId
 * @return {JSFoundset<db:/avanti/sa_task>}
 * 
 * @properties={typeid:24,uuid:"F296C9C8-183E-4089-9985-FA71493F24AC"}
 */
function getTasksByType(iTaskTypeId){
	// sort string taken from getTaskByType()
	/**@type {JSFoundset<db:/avanti/sa_task>} */
	var fs = scopes.avDB.getFS('sa_task', ['tasktype_id', 'task_active', 'task_is_sys_standard'], [iTaskTypeId, 1, "^"], sortTasks); 
	return fs;
}

/**
 * @return {JSFoundset<db:/avanti/sa_task>}
 * @properties={typeid:24,uuid:"7C8EFCBC-C796-46F9-96A3-B44916A6C222"}
 */
function getFolders(){
	return getTasksByType(TASKTYPEID.Folder)
}

/**
 * @param {UUID|String} jdffoldID
 * @return {JSRecord<db:/avanti/sa_task_machine>}
 *
 * @properties={typeid:24,uuid:"0120B0EF-CC41-4F82-A056-69352172BE97"}
 */
function getFolderVarFromFstFolderWithFold(jdffoldID){
	/**@type {JSFoundset<db:/avanti/sa_task>} */
	var fs = getFolders()
	
	if(utils.hasRecords(fs)){
		var mx = fs.getSize()
		
		for(var i=1;i<=mx;i++){
			var rFolder = fs.getRecord(i)
			
			if(!utils.hasRecords(rFolder.sa_task_to_sa_task_machine)){
				rFolder.sa_task_to_sa_task_machine.loadAllRecords() // related fs is empty
			}
			
			/**@type {JSRecord<db:/avanti/sa_task_machine>} */
			var rFolderVar = scopes.avDB.getRecFromFS(rFolder.sa_task_to_sa_task_machine, ['jdffold_id'], [jdffoldID])
			
			if(rFolderVar){
				return rFolderVar
			}
		}
	}
	
	return null
}

/**
 * gets sortkey before this task 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 *
 * @return
 * @properties={typeid:24,uuid:"80EAE42C-4788-4B6F-8A81-DD374A62C80B"}
 */
function getPrevSortkey(rTask){
	return getSortkey(rTask, -1)
}

/**
 * gets sortkey after this task 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 *
 * @return
 * @properties={typeid:24,uuid:"269BB1EF-F371-4F76-A2E2-7142DBBAE36C"}
 */
function getNextSortkey(rTask){
	return getSortkey(rTask, 1)
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} record
 * @param {Number} [offset] 1 indicates right after this sort key (ie used to place a task right before this one), -1 means right before, -2 means 2 places before, etc
 *
 * @return
 * @properties={typeid:24,uuid:"C65C1B95-3B70-4DE5-8882-76149DE8A5F2"}
 */
function getSortkey(record, offset)
{
	if (record.ordrevdstask_is_deleted == 1) {
		return null;
	}
	
	if(record.sort_key_over){
		return record.sort_key_over
	}
	else if(globals.bCopyingEstimate){
		return record.sort_key
	}
	else{
		var mainSegment;
		var placementAdjustSegment = 100 // 100 = no adjustment
		
		// task table will use new sort_key col. if its a regular task this will consists of the 3 cols it already sorted on plus '100'. if its an interim cutting task, it needs to 
		// appear before the task it was created for. so the key will be the 3 cols of the task it was created for plus '099' to put it right before it in sort order. this will allow 
		// arbitrary placing of tasks in the sort order before and after existing tasks
		// HP - added getSignatureKeySegment()
		if(utils.hasRecords(record.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task) && utils.hasRecords(record.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task.sa_order_revds_task_precut_to_sa_order_revds_task)){	
			var nextTask = record.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task.sa_order_revds_task_precut_to_sa_order_revds_task.getRecord(1)
			
			if(utils.hasRecords(nextTask.sa_order_revds_task_to_sa_task) &&
				utils.hasRecords(nextTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard) && 
				utils.hasRecords(nextTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas)) {
					mainSegment= globals.zeroPad(nextTask.ordrevdstask_sort_level, 4) 
					  + globals.zeroPad(nextTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.sequence_nr, 4) 
					  + globals.zeroPad(nextTask.sa_order_revds_task_to_sa_task.sequence_nr, 4)
					  + getSignatureKeySegment(nextTask)
					  + nextTask.ordrevdstask_material_type;
					  
				placementAdjustSegment--;
			} else {
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.confirmationRequired"), i18n.getI18NMessage("avanti.dialog.missingWorkflowGroups_task") + '\n' + nextTask.sa_order_revds_task_to_sa_task.task_description, i18n.getI18NMessage("avanti.dialog.ok"));
			}
			
		}
		
		else{
			if(utils.hasRecords(record.sa_order_revds_task_to_sa_task) &&
			utils.hasRecords(record.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard) && 
			utils.hasRecords(record.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas)) {
				mainSegment = globals.zeroPad(record.ordrevdstask_sort_level, 4) 
				  + globals.zeroPad(record.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.sequence_nr, 4) 
				  + globals.zeroPad(record.sa_order_revds_task_to_sa_task.sequence_nr, 4)
				  + getSignatureKeySegment(record)
				  + record.ordrevdstask_material_type;
			} else {
				globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.lbl.confirmationRequired"), i18n.getI18NMessage("avanti.dialog.missingWorkflowGroups_task") + '\n' + record.sa_order_revds_task_to_sa_task.task_description, i18n.getI18NMessage("avanti.dialog.ok"));
			}
		}

		if(offset){
			placementAdjustSegment += offset
		}
		return mainSegment + globals.zeroPad(placementAdjustSegment, 3) 
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} record 
 *
 * @return
 * @properties={typeid:24,uuid:"7DA5B089-F605-4C20-8CF3-8385DBC0C444"}
 */
function getSignatureKeySegment(record){
	if(utils.hasRecords(record.sa_order_revds_task_to_sa_order_revds_signature)){
		return globals.zeroPad(record.sa_order_revds_task_to_sa_order_revds_signature.sequence_nr, 4)
	}
	else{
		return '0000'
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rSectionTask 
 * @param {scopes.avSales.oEstOrdTask} [oTask]
 *
 * @return
 * @properties={typeid:24,uuid:"CFAE23E9-AF73-4F69-A382-B8AE2AD14709"}
 */
function isSignatureFolder(rSectionTask, oTask) {
    
    if (!oTask) {
        
        oTask = scopes.avSales.oEstOrd.getTaskObject(rSectionTask.ordrevdstask_id);
    }
    
	return rSectionTask.ordrevdstask_fold_type == 'G' && oTask.rSignature;
}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/sa_task_worktype_tasks>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"679FF41D-C090-49E1-B3D9-7A723C6D0ED1"}
 */
function getDataSetForValueList_TaskWorkTypeMachines(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	
	/** @type  {JSDataSet} */
	var result = null;
	if (record != null && record.task_id != null && displayValue == null && realValue == null && record.worktypetask_assoc_task_id && record.task_id) {
	    // Apply Associated task machine variables               
		args = [record.worktypetask_assoc_task_id.toString(), record.task_id.toString()]
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT tm.taskmachine_description, assoc.taskmachine_id FROM sa_task_associated_tasks assoc INNER JOIN sa_task_machine tm ON tm.taskmachine_id = assoc.taskmachine_id WHERE assoc.task_id = ? AND assoc.taskassociated_task_id = ? ORDER BY tm.taskmachine_description", args, 1000);
	}
	else if (record != null && record.task_id != null && displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		args = [record.task_id.toString(), record.org_id.toString()]
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, 'select case when jdffold_code is not null then jdffold_desc else taskmachine_description end as \'taskDesc\', taskmachine_id from sa_task_machine inner join sa_task on sa_task_machine.task_id = sa_task.task_id left outer join app_jdf_fold on app_jdf_fold.jdffold_id = sa_task_machine.jdffold_id where sa_task_machine.task_id = ? and sa_task_machine.org_id = ? and tasktype_id != 13 order by taskDesc', args, 1000);
	} else if (record != null && record.task_id != null && displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [record.task_id.toString(), displayValue + "%", record.org_id.toString()];
//		query.result.add(query.columns.taskmachine_description).add(query.columns.taskmachine_id).root.where.add(query.and.add(query.columns.task_id.eq(record.task_id.toString())).add(query.or.add(query.columns.taskmachine_description.lower.like(args[0] + '%'))));
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, 'select case when jdffold_code is not null then jdffold_desc else taskmachine_description end as \'taskDesc\', taskmachine_id from sa_task_machine inner join sa_task on sa_task_machine.task_id = sa_task.task_id  left outer join app_jdf_fold on app_jdf_fold.jdffold_id = sa_task_machine.jdffold_id where sa_task_machine.task_id = ? and taskmachine_description like ? and sa_task_machine.org_id = ? and tasktype_id != 13 order by taskDesc', args, 1000);
	} else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue.toString()];
			result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, 'select case when jdffold_code is not null then jdffold_desc else taskmachine_description end as \'taskDesc\', taskmachine_id from sa_task_machine inner join sa_task on sa_task_machine.task_id = sa_task.task_id  left outer join app_jdf_fold on app_jdf_fold.jdffold_id = sa_task_machine.jdffold_id where taskmachine_id = ? and tasktype_id != 13 order by taskDesc', args, 1000);
		}
	}
	return result;

}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/sa_task_worktype_tasks>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"69D84794-A171-40A4-A626-A4B430715F01"}
 */
function getDataSetForValueList_TaskWorkTypeMaterial1(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	var query = null;
	if(record != null && utils.hasRecords(record.sa_task_worktype_tasks_to_sa_task)) {
		var tasktype_id = record.sa_task_worktype_tasks_to_sa_task.tasktype_id
		
		switch (tasktype_id) {
			case scopes.avTask.TASKTYPEID.Material:
				// Grab query from globals.avUtilities_appSqlQueryGet('VL', null, 'avSales_sectionTaskMaterials', globals.avBase_dbase_version);
				if (displayValue) {
    				query = "SELECT \
    				            CASE WHEN i.item_desc2 IS NOT NULL THEN i.item_code + \': \' + i.item_desc1 + \' - \' + i.item_desc2 \
    				            ELSE i.item_code + \': \' + i.item_desc1 END AS ItemDesc, i.item_id AS ItemID\
    						 FROM in_item i \
    						 WHERE i.org_id = ? AND i.item_status = 'A' \
    						    AND ( LOWER(i.item_code + \': \' + i.item_desc1) LIKE ? ) \
    						    AND ( /* div + plant filter portion */ \'0\' IN \
    						        (SELECT syspref_value FROM sys_preference WHERE pref_id IN \
    						            (SELECT pref_id FROM app_preference WHERE pref_number = 79))\
    						    OR i.item_id IN (SELECT item_id FROM in_item_warehouse WHERE whse_id IN \
    						        (SELECT whse_id FROM in_warehouse WHERE plant_id IN \
    						        (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND currently_visible = 1))))\
    						 ORDER BY ItemDesc ASC";
    				args = [record.org_id.toString(), displayValue.toLowerCase() + "%", globals.avBase_employeeUUID];
				} 
				else {
    				query = "SELECT \
    				            CASE WHEN i.item_desc2 IS NOT NULL THEN i.item_code + \': \' + i.item_desc1 + \' - \' + i.item_desc2 \
    				            ELSE i.item_code + \': \' + i.item_desc1 END AS ItemDesc, i.item_id AS ItemID\
    						 FROM in_item i \
    						 WHERE i.org_id = ? AND i.item_status = 'A' \
    						    AND ( /* div + plant filter portion */ \'0\' IN \
    						        (SELECT syspref_value FROM sys_preference WHERE pref_id IN \
    						        (SELECT pref_id FROM app_preference WHERE pref_number = 79))\
    						    OR i.item_id IN (SELECT item_id FROM in_item_warehouse WHERE whse_id IN \
    						        (SELECT whse_id FROM in_warehouse WHERE plant_id IN \
    						        (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND currently_visible = 1))))\
    						 ORDER BY ItemDesc ASC";
    				args = [record.org_id.toString(), globals.avBase_employeeUUID];
				}
				break;
			case scopes.avTask.TASKTYPEID.Laminating:
				query = "SELECT d.item_desc1, d.item_id \
				         FROM sa_task_worktype_tasks AS a \
				         INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
				         INNER JOIN sa_task_machine AS c ON c.task_id = b.task_id \
				         INNER JOIN in_item AS d ON d.item_id = c.item_id AND a.org_id = ? \
				         WHERE d.item_status = 'A' \
				         ORDER BY c.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()];
				break;
			case scopes.avTask.TASKTYPEID.Mounting:
    				query = "SELECT i.item_code + ': ' + i.item_desc1 AS ItemName, i.item_id AS ItemCode \
    				         FROM in_item i \
        					 WHERE i.org_id = ? AND i.item_status = ? AND i.itemclass_id IN \
        					    (\
        					    SELECT ic.itemclass_id \
        					    FROM in_item_class ic \
        					    WHERE ic.itemclass_type = ? OR ic.itemclass_type = ? AND ic.org_id = ?\
        					    ) \
        					 ORDER BY ItemName ASC";
    				args = [globals.org_id, "A", "FI", "P", globals.org_id];
				break;
			case scopes.avTask.TASKTYPEID.Proofing:
    			query = "SELECT d.item_desc1, d.item_id \
    			         FROM sa_task_worktype_tasks AS a \
    			         INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
    			         INNER JOIN sa_task_setup AS c ON c.task_id = b.task_id \
    			         INNER JOIN in_item AS d ON d.item_id = c.item_id AND a.org_id = ? \
                         WHERE d.item_status = 'A' \
    			         ORDER BY c.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()];
				break;
			case scopes.avTask.TASKTYPEID.Padding:
    			query = "SELECT e.item_desc1, e.item_id \
    			         FROM sa_task_worktype_tasks AS a \
    			         INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
    			         INNER JOIN sa_task_standard AS c ON c.task_id = b.task_id AND c.taskstd_requires_backer = 1 \
    			         INNER JOIN sa_task_material AS d ON b.task_id = d.task_id AND d.taskmaterial_is_backer = 1 \
    			         INNER JOIN in_item AS e ON e.item_id = d.item_id AND a.org_id = ? \
                         WHERE e.item_status = 'A' \
    			         ORDER BY d.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()];
				break;
			case scopes.avTask.TASKTYPEID.EdgeBinding:	
    			query = "SELECT e.item_desc1, e.item_id \
    			         FROM sa_task_worktype_tasks AS a \
    			         INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
    			         INNER JOIN sa_task_material AS c ON b.task_id = c.task_id AND c.taskmaterial_is_backer = 1 \
    			         INNER JOIN in_item AS e ON e.item_id = c.item_id AND a.org_id = ? \
                         WHERE e.item_status = 'A' \
    			         ORDER BY c.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()];
				break;
			case scopes.avTask.TASKTYPEID.RollToSheetConversion:
				if (record.sa_task_worktype_tasks_to_sa_task_worktype_section.worktypesection_roll_to_sheet === "offline" 
					|| !record.sa_task_worktype_tasks_to_sa_task_worktype_section.worktypesection_roll_to_sheet) {
        			query = "SELECT d.item_desc1, d.item_id \
                			 FROM sa_task_worktype_tasks AS a \
                			 INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
                			 INNER JOIN sa_task_material AS c ON c.task_id = b.task_id \
                			 INNER JOIN in_item AS d ON d.item_id = c.item_id AND a.org_id = ? \
                             WHERE d.item_status = 'A' \
                			 ORDER BY c.sequence_nr ASC";
					args = [record.worktypetask_id.toString(), record.org_id.toString()];
				}
				break;
			default:
    			query = "SELECT d.item_desc1, d.item_id \
            			 FROM sa_task_worktype_tasks AS a \
            			 INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? \
            			 INNER JOIN sa_task_material AS c ON c.task_id = b.task_id \
            			 INNER JOIN in_item AS d ON d.item_id = c.item_id AND a.org_id = ? \
                         WHERE d.item_status = 'A' \
            			 ORDER BY c.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()];
				break;
		} 
	}
	
	if(query == null) {
		return null
	}
	/** @type  {JSDataSet} */
	var result = null;
	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue.toString()];
			result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
		}
	}
	return result;

}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/sa_task_worktype_tasks>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"4AE465C3-45DB-4C76-8B7E-FF62B592545D"}
 */
function getDataSetForValueList_TaskWorkTypeMaterial2(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	var query = null;
	if(record != null && utils.hasRecords(record.sa_task_worktype_tasks_to_sa_task)) {
		var tasktype_id = record.sa_task_worktype_tasks_to_sa_task.tasktype_id
		
		switch (tasktype_id) {
			case scopes.avTask.TASKTYPEID.Padding: 
				query = "SELECT e.item_desc1, e.item_id \
				         FROM sa_task_worktype_tasks AS a \
				         INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? AND a.org_id = ? \
				         INNER JOIN sa_task_standard AS c ON c.task_id = b.task_id AND c.taskstd_requires_cover = 1 \
				         INNER JOIN sa_task_material AS d ON b.task_id = d.task_id AND d.taskmaterial_is_cover = 1 \
				         INNER JOIN in_item AS e ON e.item_id = d.item_id \
                         WHERE e.item_status = 'A' \
				         ORDER BY d.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()]
				break;
			case scopes.avTask.TASKTYPEID.EdgeBinding:
				query = "SELECT e.item_desc1, e.item_id \
        				 FROM sa_task_worktype_tasks AS a \
        				 INNER JOIN sa_task AS b ON b.task_id = a.task_id AND a.worktypetask_id = ? AND a.org_id = ? \
        				 INNER JOIN sa_task_material AS d ON b.task_id = d.task_id AND d.taskmaterial_is_cover = 1 \
        				 INNER JOIN in_item AS e ON e.item_id = d.item_id \
                         WHERE e.item_status = 'A' \
        				 ORDER BY d.sequence_nr ASC";
				args = [record.worktypetask_id.toString(), record.org_id.toString()]
				break;
			case scopes.avTask.TASKTYPEID.Mounting: 
			    if (displayValue)  {
    				query = "SELECT i.item_code + ': ' + i.item_desc1 AS ItemName, i.item_id AS ItemCode \
    				         FROM in_item i \
    				         WHERE i.org_id = ?  AND ( LOWER(i.item_code + \': \' + i.item_desc1) LIKE ? ) \
    				            AND i.item_status = ? AND i.itemclass_id IN \
    				                (SELECT ic.itemclass_id \
    				                FROM in_item_class ic \
    				                WHERE (ic.itemclass_type = ? OR ic.itemclass_type = ?) AND ic.org_id = ?) \
    				         ORDER BY ItemName ASC";
    				args = [globals.org_id, displayValue.toLowerCase() + "%", "A", "FI", "P", globals.org_id];
			    }
			    else {
    				query = "SELECT i.item_code + ': ' + i.item_desc1 AS ItemName, i.item_id AS ItemCode \
    				         FROM in_item i \
    					     WHERE i.org_id = ?  AND i.item_status = ? AND i.itemclass_id IN \
    					        (SELECT ic.itemclass_id \
    					        FROM in_item_class ic \
    					        WHERE (ic.itemclass_type = ? OR ic.itemclass_type = ?) AND ic.org_id = ?) \
    					     ORDER BY ItemName ASC";
    				args = [globals.org_id, "A", "FI", "P", globals.org_id];  
			    }
				break;
			default:
				return null
				break;
		} 
	}
	
	if(query == null) {
		return null
	}	
	/** @type  {JSDataSet} */
	var result = null;
	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		args = [record.task_id.toString(), displayValue + "%"];
//		query.result.add(query.columns.taskmachine_description).add(query.columns.taskmachine_id).root.where.add(query.and.add(query.columns.task_id.eq(record.task_id.toString())).add(query.or.add(query.columns.taskmachine_description.lower.like(args[0] + '%'))));
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue.toString()];
			result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
		}
	}
	return result;

}

/**
 * Called when the valuelist needs data, it has 3 modes.
 * real and display params both null: return the whole list
 * only display is specified, called by a typeahead, return a filtered list
 * only real value is specified, called when the list doesnt contain the real value for the give record value, this will insert this value into the existing list
 *
 * @param {String} displayValue The value of a lookupfield that a user types
 * @param realValue The real value for a lookupfield where a display value should be get for
 * @param {JSRecord<db:/avanti/sa_task_worktype_tasks>} record The current record for the valuelist.
 * @param {String} valueListName The valuelist name that triggers the method. (This is the FindRecord in find mode, which is like JSRecord has all the columns/dataproviders, but doesn't have its methods)
 * @param {Boolean} findMode True if foundset of this record is in find mode
 * @param {Boolean} rawDisplayValue The raw displayValue without being converted to lower case
 *
 * @returns {JSDataSet} A dataset with 1 or 2 columns display[,real]
 *
 * @properties={typeid:24,uuid:"48FA739C-79BB-46F1-B3BD-676582EAAF9C"}
 */
function getDataSetForValueList_TaskWorkTypeMaterial3(displayValue, realValue, record, valueListName, findMode, rawDisplayValue) {
	var args = null;
	var query = null;
	if(record != null 
			&& utils.hasRecords(record.sa_task_worktype_tasks_to_sa_task)
			&& utils.hasRecords(_to_in_item_class$adhesive)
			&& _to_in_item_class$adhesive.itemclass_id) {
		var tasktype_id = record.sa_task_worktype_tasks_to_sa_task.tasktype_id
		
		switch (tasktype_id) {
			case scopes.avTask.TASKTYPEID.Mounting: 
			case scopes.avTask.TASKTYPEID.Laminating:
			    if (displayValue) {
			        
    				query = "SELECT i.item_code + ': ' + i.item_desc1 AS ItemName, i.item_id AS ItemCode \
    				         FROM in_item i \
    				         WHERE i.itemclass_id = ? AND i.org_id = ?  AND ( LOWER(i.item_code + \': \' + i.item_desc1) LIKE ? ) AND i.item_status = ? \
    				         ORDER BY itemName ASC";
    				args = [_to_in_item_class$adhesive.itemclass_id.toString(), record.org_id.toString(), displayValue.toLowerCase() + "%", "A"];
			    } 
			    else {
			        
    				query = "SELECT i.item_code + ': ' + i.item_desc1 AS ItemName, i.item_id AS ItemCode \
    				         FROM in_item i \
    				         WHERE i.itemclass_id = ? AND i.org_id = ? AND i.item_status = ? \
    				         ORDER BY itemName ASC";
    				args = [_to_in_item_class$adhesive.itemclass_id.toString(), record.org_id.toString(), "A"];
			    }
				break;
			default:
				return null
				break;
		} 
	}
	
	if(query == null) {
		return null
	}	
	/** @type  {JSDataSet} */
	var result = null;
	if (displayValue == null && realValue == null) {
		// TODO think about caching this result. can be called often!
		// return the complete list
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (displayValue != null) {
		// TYPE_AHEAD filter call, return a filtered list
		query = "SELECT i.item_code + ': ' + i.item_desc1 AS itemName, i.item_id AS ItemCode \
		         FROM in_item i \
		         WHERE i.itemclass_id = ? AND i.org_id = ? AND i.item_status = ? AND i.item_code + ': ' + i.item_desc1 LIKE ? \
		         ORDER BY itemName ASC";
		args = [_to_in_item_class$adhesive.itemclass_id.toString(), record.org_id.toString(), "A", displayValue + "%"]
		
		result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
	} else if (realValue != null) {
		// TODO think about caching this result. can be called often!
		// real object not found in the current list, return 1 row with display,realvalue that will be added to the current list
		// dont return a complete list in this mode because that will be added to the list that is already there
		var re = new RegExp("[A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12}");

		if (re.test(realValue)) {
			args = [realValue.toString()];
			result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, query, args, 1000);
		}
	}
	return result;

}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_signature>} rSig
 * @return {JSRecord<db:/avanti/sa_order_revds_task>} 
 *
 * @properties={typeid:24,uuid:"D88CDE74-DD8D-4F15-B21E-F10CC4F2A647"}
 */
function getSignatureFolder(rSig){
    if (rSig.ordrevdssig_folder_group_num) {
        /** @type{JSRecord<db:/avanti/sa_order_revds_task>} */
        var rTaskRecord = scopes.avDB.getRec('sa_order_revds_task', ['ordrevds_id', 'ordrevdstask_sig_folder_group'], [rSig.ordrevds_id, rSig.ordrevdssig_folder_group_num], null, null, true);
        return rTaskRecord;
    }
    if (utils.hasRecords(rSig.sa_order_revds_signature_to_sa_order_revds_task)) {
        var mx = rSig.sa_order_revds_signature_to_sa_order_revds_task.getSize();

        for (var i = 1; i <= mx; i++) {
            var r = rSig.sa_order_revds_signature_to_sa_order_revds_task.getRecord(i);
            if (r.ordrevdstask_fold_type == 'G') {
                return r;
            }
        }
    }

    return null;
}

/**
 * @param {Number} taskTypeID
 *
 * @return
 * @properties={typeid:24,uuid:"839A1394-C58E-429E-8045-1963091ADA4F"}
 */
function isDigitalPress(taskTypeID){
	return (taskTypeID == TASKTYPEID.DigitalRollPress || taskTypeID == TASKTYPEID.DigitalSheetPress)
}

/**
 * @return {JSRecord<db:/avanti/sa_task>}
 * @properties={typeid:24,uuid:"0AA215F0-9918-46B6-87EC-81AC78E846D9"}
 */
function getFirstFolder(){
	var fs = getFolders()
	
	if(fs && fs.getSize() > 0){
		return fs.getRecord(1)
	}
	else{
		return null
	}
	
}

/**
 * Copies a changed field value to all Qtys
 *
 * <AUTHOR> Dotzlaw
 * @since May 13, 2015
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @param {String} sField - The field to work with
 * @param {Number|String|UUID} nValue - The value to set the field to
 * @param {Number|String|UUID} [nValueOver] - the override value
 * @param {Number|String|UUID} [nValue2] - A second value used by folder options
 * @param {Number|String|UUID} [nValue2Over] - A second override value
 * @param {String|UUID} [sFk] - Optional Fk to use
 * @public
 *
 * @properties={typeid:24,uuid:"C8ACFBE7-CD79-4C8A-B5F0-1507D92C9D01"}
 */
function setFieldForAllQtys (rSection, rTask, sField, nValue, nValueOver, nValue2, nValue2Over, sFk) {
	
	var i = 0,
		fsTaskQty = null;
	
	// Get all the task qty records for this section (all qtys)
	fsTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty;

	for ( i = 1; i <= fsTaskQty.getSize(); i++ ) {
		
		fsTaskQty.setSelectedIndex(i);
		
		// Set both fields to the new value
		fsTaskQty[sField] = nValue;
		fsTaskQty[sField + "_over"] = nValueOver;
		
		// These two fields are tied together; only one can be in use at a time
		if (sField === "ordrevdstqty_spoils_run") {

            fsTaskQty.ordrevdstqty_spoils_run_p_over = null;

        }
        else if (sField === "ordrevdstqty_spoils_run_p") {

            fsTaskQty.ordrevdstqty_spoils_run_over = null;

        }
        else if (sField === "ordrevdstqty_qty_task") {

            fsTaskQty.ordrevdstqty_qty_task_overuser = nValueOver;
        }
		
		// Also need to clear the other field if dealing with run spoils (two ways to adjust, % and sheets)
		if (nValue2) {
			
			switch ( sField ) {

				case "ordrevdstqty_f_perf_setup":
					
					fsTaskQty.taskfldopt_id_perf = sFk;
					fsTaskQty["ordrevdstqty_f_perf_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_perf_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_perf_runf":
					
					fsTaskQty.taskfldopt_id_perf = sFk;
					fsTaskQty["ordrevdstqty_f_perf_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_perf_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_scor_setup":
					
					fsTaskQty.taskfldopt_id_score = sFk;
					fsTaskQty["ordrevdstqty_f_scor_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_scor_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_scor_runf":
					
					fsTaskQty.taskfldopt_id_score = sFk;
					fsTaskQty["ordrevdstqty_f_scor_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_scor_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_line_setup":
					
					fsTaskQty.taskfldopt_id_numbering = sFk;
					fsTaskQty["ordrevdstqty_f_line_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_line_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_line_runf":
					
					fsTaskQty.taskfldopt_id_numbering = sFk;
					fsTaskQty["ordrevdstqty_f_line_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_line_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_inkj_setup":
					
					fsTaskQty.taskfldopt_id_inkjetting = sFk;
					fsTaskQty["ordrevdstqty_f_inkj_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_inkj_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_inkj_runf":
					
					fsTaskQty.taskfldopt_id_inkjetting = sFk;
					fsTaskQty["ordrevdstqty_f_inkj_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_inkj_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth1_setup":
					
					fsTaskQty.taskfldopt_id_other1 = sFk;
					fsTaskQty["ordrevdstqty_f_oth1_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth1_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth1_runf":
					
					fsTaskQty.taskfldopt_id_other1 = sFk;
					fsTaskQty["ordrevdstqty_f_oth1_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth1_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth2_setup":
					
					fsTaskQty.taskfldopt_id_other2 = sFk;
					fsTaskQty["ordrevdstqty_f_oth2_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth2_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth2_runf":
					
					fsTaskQty.taskfldopt_id_other2 = sFk;
					fsTaskQty["ordrevdstqty_f_oth2_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth2_setup_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth3_setup":
					
					fsTaskQty.taskfldopt_id_other3 = sFk;
					fsTaskQty["ordrevdstqty_f_oth3_runf"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth3_runf_over"] = nValue2Over;
					break;
					
				case "ordrevdstqty_f_oth3_runf":
					
					fsTaskQty.taskfldopt_id_other3 = sFk;
					fsTaskQty["ordrevdstqty_f_oth3_setup"] = nValue2;
					fsTaskQty["ordrevdstqty_f_oth3_setup_over"] = nValue2Over;
					break;
				
				default:
					break;
			}
		}
	}

	databaseManager.saveData(fsTaskQty);
	return;
}

/**
 * Computes the final cost after applying the computed variable markup
 *
 * <AUTHOR> Dotzlaw
 * @since Aug 26, 2015
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {Number} nTotal
 * @returns {Number} The final cost with the markup applied
 * @public
 *
 * @properties={typeid:24,uuid:"1AC30E55-C50B-4ECA-8F85-14A655219492"}
 */
function getPriceWithVariableMarkup(rTask, nTotal) {
	
	var nFinal = 0,
		nTotalTemp = nTotal,
		nMrkup = 0,
		rTaskStd = rTask.sa_task_to_sa_task_standard.getRecord(1),
		fsTaskCosts = rTask.sa_task_to_sa_task_cost_rate,
		rTaskCost = null,
		i = 0,
		bFound = false,
		nPrevCostTo = 0;
	
	if (!rTaskStd.taskstd_mrkup_type || rTaskStd.taskstd_mrkup_type === "F" || fsTaskCosts.getSize() === 0) {
		
		return nTotal * (1 + rTaskStd.taskstd_mrkup);
	}
	
	fsTaskCosts.sort("sequence_nr asc");
	
	if (rTaskStd.taskstd_mrkup_calc_type === "S") {
		
		// Straight markup
		for ( i = 1; i <= fsTaskCosts.getSize(); i++ ) {
			
			rTaskCost = fsTaskCosts.getRecord(i);
			
			if (rTaskCost.taskcostrate_cost_from <= nTotal && rTaskCost.taskcostrate_cost_to >= nTotal) {
				
				nMrkup = rTaskCost.taskcostrate_cost_mrkup;
				bFound = true;
				break;
			}
		}
		if (!bFound) {
			
			rTaskCost = fsTaskCosts.getRecord(i-1); // take the last record
			nMrkup = rTaskCost.taskcostrate_cost_mrkup;
		}
		
		nFinal = nTotal * (1 + nMrkup);
		
	} else {
		
		// Incremental markup
		for ( i = 1; i <= fsTaskCosts.getSize(); i++ ) {
			
			rTaskCost = fsTaskCosts.getRecord(i);
			
			if (rTaskCost.taskcostrate_cost_to === null || nTotalTemp <= rTaskCost.taskcostrate_cost_to - nPrevCostTo) {
				
				nFinal += nTotalTemp * (1 + rTaskCost.taskcostrate_cost_mrkup);
				break;
				
			} else if (nTotalTemp >= rTaskCost.taskcostrate_cost_to - nPrevCostTo) {
				
				nFinal +=  (rTaskCost.taskcostrate_cost_to - nPrevCostTo) * (1 + rTaskCost.taskcostrate_cost_mrkup);
				nTotalTemp -= rTaskCost.taskcostrate_cost_to - nPrevCostTo;
				nPrevCostTo = rTaskCost.taskcostrate_cost_to;
			}
			
		}
	}
	
	return nFinal;
}


/**
 * Gets the sheet size coming in to the given task, either from the prior tasks (if interim cutting is involved)
 * or from the press task
 *
 * <AUTHOR> Dotzlaw
 * @since Aug 25, 2015
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @returns {{nSheetWidth : Number, nSheetLength : Number, bInterimCutting: Boolean}} 
 *
 * @properties={typeid:24,uuid:"76725555-D31C-4B75-AC97-D020C1BF1574"}
 */
function getIncomingSheetSize(rTask){

	var	rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
		fsTasks = rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted,
		rPress = null,
		bFound = false,
		bInterimCutting = false,
		nW = 0,
		nL = 0,
		i = 0,
		rPriorTask = null,
		oObj = {};
	
	if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)) rPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1);
	
	if (fsTasks.selectRecord(rTask.ordrevdstask_id)) {
		
		fsTasks.sort("sort_key");
		for ( i = fsTasks.getSelectedIndex() - 1; i > 0; i-- ) {
			
			rPriorTask = fsTasks.getRecord(i);
			
			if (rPriorTask.sa_order_revds_task_to_sa_task.tasktype_id === 99) break; // hit the press task
			
			if (rPriorTask.ordrevdstask_sheet_width 
					&& rPriorTask.ordrevdstask_sheet_width > 0
					&& rPriorTask.ordrevdstask_sheet_length
					&& rPriorTask.ordrevdstask_sheet_length > 0) {
				
				bFound = true;
				nW = rPriorTask.ordrevdstask_sheet_width;
				nL = rPriorTask.ordrevdstask_sheet_length;
				bInterimCutting = true;
				break;
			}
		}
		
		if (!bFound && rPress) {
			
			nW = rPress.mpress_press_sheet_w;
			nL = rPress.mpress_press_sheet_l;
		}
	}
	
	oObj.nSheetWidth = nW;
	oObj.nSheetLength = nL;
	oObj.bInterimCutting = bInterimCutting;
	
	return oObj;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"A33D6833-960F-4EC6-A951-662CF4E977EC"}
 */
function isGangChildCommonTask(rOrdTask){

	// GD - Sep 9, 2015: SL-6224 - All we need to do is avoid any tasks with "ordrevds_id_gang != null". If there is an id, it tells us it is a gang cost distribution record (common)
	if (rOrdTask.ordrevds_id_gang != null) return true;

	return false;
}


/**
 * Deletes material task
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 17, 2015
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {String} sCode - material task code 
 * @public
 *
 * @properties={typeid:24,uuid:"EE434BD0-0F57-4EB1-8D12-AD1D437AF6DB"}
 */
function deleteMaterialTask(rSection, sCode) {
    // sl-14820 - this had been written to loop thru est object aTasks[], but i had to re-write using a foundset loop as it was throwing a
    // ‘Invalid input, Record not from this foundset’ error when deleting the record from the foundset because the record had not been directly
    // retrieved from the foundset, even tho the record was a member of the foundset. this had worked in servoy 7.4.5, but stopped working 
    // in 7.4.10
    
    for (var i = 1; i <= rSection.sa_order_revision_detail_section_to_sa_order_revds_task.getSize(); i++) {
        var rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(i);

        if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_task)) {
            var rStdTask = rTask.sa_order_revds_task_to_sa_task.getRecord(1);

            if (rStdTask.tasktype_id === TASKTYPEID.Material && rTask.ordrevdstask_material_type === sCode) {
                rSection.sa_order_revision_detail_section_to_sa_order_revds_task.deleteRecord(rTask);
                i--;
            }
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask - the task
 *
 * @properties={typeid:24,uuid:"791173F2-6148-4A94-A3FF-E9B60FA6F7CD"}
 */
function backupSystemTrimComments(rTask){
	 if(utils.hasRecords(rTask.sa_order_revds_task_to_sys_comment)){
	 	var rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
		var sKey = rTask.ordrevdstask_cut_type + ',' + rSection.ordrevds_pages + ',' + rSection.ordrevds_form_num + ',' + rSection.ordrevds_version_num + ',' + 
			rSection.ordrevds_child_num + ',' + rSection.ordrevds_web_number;	
		var nMax = rTask.sa_order_revds_task_to_sys_comment.getSize();
		
		for(var i=nMax; i>0; i--){
			var rComment = rTask.sa_order_revds_task_to_sys_comment.getRecord(i);
			// null ordrevdstask_id so comments wont be deleted when task is deleted. use sKey to find an restore comments after ft recreated 
			rComment.ordrevdstask_id = null;
			rComment.comment_backup_key = sKey; 
			databaseManager.saveData(rComment);
		}
	 }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask - the task
 * @param {String} sCutType - PT or FT
 *
 * @properties={typeid:24,uuid:"BE75199C-6569-438C-B1E1-BBB692B26648"}
 */
function restoreSystemTrimComments(rTask, sCutType){
 	var rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
	var sKey = sCutType + ',' + rSection.ordrevds_pages + ',' + rSection.ordrevds_form_num + ',' + rSection.ordrevds_version_num + ',' + 
		rSection.ordrevds_child_num + ',' + rSection.ordrevds_web_number;	
	/**@type {JSFoundset<db:/avanti/sys_comment>} */
	var fsComment = scopes.avDB.getFS('sys_comment', ['comment_backup_key'], [sKey]);
	var nMax = fsComment.getSize();
	
	for(var i=1; i<=nMax; i++){
		 var rComment = fsComment.getRecord(i);
		 rComment.ordrevdstask_id = rTask.ordrevdstask_id;
		 rComment.comment_backup_key = null;
		 databaseManager.saveData(rComment);
	}
}


/**
 * Will return the first task of the specified type from the estimating standards library
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {Number} iTaskTypeId
 * @returns {JSRecord<db:/avanti/sa_task>} the first task
 * @public
 *
 * @properties={typeid:24,uuid:"2647E653-4B61-4F95-BEE3-7253742DCD06"}
 */
function getTask (iTaskTypeId) {
	
	var fs = getTasksByType(iTaskTypeId);
	
	if (fs && fs.getSize() > 0) {
		
		return fs.getRecord(1);
	}
	return null;
}

/**
 * Inserts the roll to sheet converter task into the Sections tasks, before the press task
 * and returns it
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rInk
 * @param {JSRecord<db:/avanti/sa_task>} [rConvTask] optional converter task to insert
 * @returns {JSRecord<db:/avanti/sa_order_revds_task>} 
 * @public
 *
 * @properties={typeid:24,uuid:"A6294042-8F79-4465-93D6-ACC1CFE82283"}
 */
function rollToSheetConverter_insertTask (rSection, rInk, rConvTask) {
	
	// Insert after the Ink task
	var fsTasks = rSection.sa_order_revision_detail_section_to_sa_order_revds_task,
		iIndex = 1,
		iNum = 1,
		rSecTask = null,
		iSortKeyOver = null;
	
	if (!rConvTask) {
	    rConvTask = getTask(TASKTYPEID.RollToSheetConversion);
	}
	if (rConvTask 
			&& rInk
			&& fsTasks
			&& fsTasks.getSize() >= 3 ) {
		
		fsTasks.selectRecord(rInk.ordrevdstask_id);
		iNum = fsTasks.sequence_nr + 1;
		iIndex = fsTasks.getSelectedIndex() + 1;
		iSortKeyOver = rInk.sort_key;
		//increment iSortKeyOver by 1 so it's right after the ink task
		if (iSortKeyOver) {
			iSortKeyOver = iSortKeyOver.substring(0, iSortKeyOver.length - 1) + ((parseInt(iSortKeyOver.charAt(iSortKeyOver.length - 1)) + 1) % 10);
		}
		
//                                                      (fsInsertSectionTasks, iNum, iIndex, sTaskUUID, sSectionUUID, sCutType, bSkipSave, iSortKeyOver)
		rSecTask = globals["avCalcs_section_insertTask"](fsTasks, iNum, iIndex, rConvTask.task_id, rSection.ordrevds_id, null, null, iSortKeyOver);
		
	} 

	return rSecTask;
}

/**
 * Validates that a paper can be used with the Roll to Sheet Converter
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {JSRecord<db:/avanti/in_item_paper>} rPaper - The paper record
 * @param {JSRecord<db:/avanti/sa_task>} rConverter
 * @returns {Boolean} 
 * @public
 *
 * @properties={typeid:24,uuid:"DCC489D8-E8DC-42CE-9B4B-60339855B956"}
 */
function rollToSheetConverter_validatePaper (rPaper, rConverter) {
	
	if (!rPaper || !rConverter) return false;
	
	var bValid = true,
		rStd = rConverter.sa_task_to_sa_task_standard.getRecord(1);
	
	if (rPaper.paper_first_dim < rStd.taskstd_min_roll_size || rPaper.paper_first_dim > rStd.taskstd_max_roll_size) bValid = false;
	
	return bValid;
}

/**
 * Validates that the press sheet will fit onto the Roll
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/in_item_paper>} rPaper - The paper record
 * @returns {Boolean}
 * @public
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"9A88715D-95D6-46CB-9395-77CD74FFC67B"}
 */
function rollToSheetConverter_validatePressSheetSize (rSection, rPaper) {
	
	if (!rPaper || !rSection || !rSection.ordrevds_over_press_sheet_size || rSection.ordrevds_over_press_sheet_size.toLowerCase().search("x") == -1) return false;
	
	var bValid = true,
		nDim1 = parseFloat(rSection.ordrevds_over_press_sheet_size.toLowerCase().split("x")[0]),
		nDim2 = parseFloat(rSection.ordrevds_over_press_sheet_size.toLowerCase().split("x")[1]);
	
	if (nDim1 > rPaper.paper_first_dim && nDim2 > rPaper.paper_first_dim) bValid = false;
	
	return bValid;
}

/**
 * Orientates the press sheet into landscape if it fits
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/in_item_paper>} rPaper - The paper record
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rConverter - The converter task
 * @param {Boolean} [bFlexoBlankRoll] - used for Flexo Blank Roll work
 * @returns {{bRotate: Boolean, iAcrossRoll: Number, nFeedLength: Number, nWidth: Number}} 
 * @public
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"60946B14-C6EB-4E9A-9957-82D5966A1D3D"}
 */
function rollToSheetConverter_rotatePressSheet (rSection, rPaper, rConverter, bFlexoBlankRoll) {
	
	if (!rSection || !rPaper || !rConverter) return null;
	
	var nDim1 = 0,
		nDim2 = 0,
		sGrain = rConverter.ordrevdstask_grain,
		iUpDim101 = 0,
		iUpDim102 = 0, 
		iMath1 = 0,
		iMath2 = 0,
		bRotate = false,
		nFeedLength = 0,
		nWidth = 0,
		iAcrossRoll = 0,
		oReturn = {};
	
	if (bFlexoBlankRoll && rSection.ordrevds_over_die_parent_sheet) {

	    nDim1 = parseFloat(rSection.ordrevds_over_die_parent_sheet.toLowerCase().split("x")[0]);
	    nDim2 = parseFloat(rSection.ordrevds_over_die_parent_sheet.toLowerCase().split("x")[1]);
	}
	else {
	    
	    nDim1 = parseFloat(rSection.ordrevds_over_press_sheet_size.toLowerCase().split("x")[0]);
	    nDim2 = parseFloat(rSection.ordrevds_over_press_sheet_size.toLowerCase().split("x")[1]);
	}
	
	// Also calculate the number across the roll and set that on the section, if no override
	
	if (!sGrain ) {
		
		// Take the most efficient orientation; use the number across the roll
		iUpDim101 = rPaper.paper_first_dim / nDim1;
		iUpDim102 = rPaper.paper_first_dim / nDim2;

		iMath1 = Math.floor(iUpDim101) * nDim1 * nDim2 / (rPaper.paper_first_dim * nDim2);
		iMath2 = Math.floor(iUpDim102) * nDim1 * nDim2 / (rPaper.paper_first_dim * nDim1);
		
		if (iMath2 > iMath1) {
			
			iAcrossRoll = Math.floor(iUpDim102);
			nFeedLength = nDim1;
			nWidth = nDim2;
			bRotate = true;  
			
		} else {
			
			iAcrossRoll = Math.floor(iUpDim101);
			nFeedLength = nDim2;
			nWidth = nDim1;
		}
	} else if (sGrain == "L") {
		
		// Shortest side has to go across the roll
		if (nDim1 <= nDim2 && nDim1 <= rPaper.paper_first_dim){
			
			iAcrossRoll = Math.floor(rPaper.paper_first_dim / nDim1);
			nFeedLength = nDim2;
			nWidth = nDim1;
			
		} else {
			
			iAcrossRoll = Math.floor(rPaper.paper_first_dim / nDim2);
			nFeedLength = nDim1;
			nWidth = nDim2;
			bRotate = true; 
		}
		
	} else {
		
		// Longest side has to go across the roll
		if (nDim1 >= nDim2 && nDim1 <= rPaper.paper_first_dim){
			
			iAcrossRoll = Math.floor(rPaper.paper_first_dim / nDim1);
			nFeedLength = nDim2;
			nWidth = nDim1;
			
		} else {
			
			iAcrossRoll = Math.floor(rPaper.paper_first_dim / nDim2);
			nFeedLength = nDim1;
			nWidth = nDim2;
			bRotate = true; 
		}
	}
	
	oReturn.bRotate = bRotate;
	oReturn.iAcrossRoll = iAcrossRoll;
	oReturn.nFeedLength = nFeedLength;
	oReturn.nWidth = nWidth;
	
	return oReturn;
}

/**
 * Checks to see if the sheeter is needed for the Section
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * 
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {UUID|String} [uPaperItemIDOver]
 * 
 * @returns {{bRequired: Boolean, rConvTask: JSRecord<db:/avanti/sa_task>}}
 * @public
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"B4F7B2E7-1F92-421F-9687-B283968E6EEC"}
 */
function rollToSheetConverter_checkIfNeeded(rSection, uPaperItemIDOver) {

    var uPaperItemID = uPaperItemIDOver ? uPaperItemIDOver : rSection.ordrevds_over_paper_item_id, 
        bRollPaper = ( uPaperItemID ) ? scopes.avInv.isRoll(null, uPaperItemID.toString()) : false,
        oReturn = {bRequired: false, rConvTask: null};

    // sl-17912 - if we are bChangingPressForSubstrateChange, eg. change paper to roll and so changing selected press from sheet press to roll press, then we dont
    // want to return true here or it will keep sheet press and use a roll 2 sheet converter, when we want to force it to pick the roll press.
    if (bRollPaper 
            && scopes.avTask.getTask(scopes.avTask.TASKTYPEID.RollToSheetConversion)
			&& !scopes.avSection.bChangingPressForSubstrateChange) {

        if (rSection.parent_id 
                && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent)) {
            rSection = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent.getRecord(1);
        }
        
        // use prod of prev section we dont need roll 2 sheet converter
		if (rSection.ordrevds_use_prev_sec) {
			oReturn.bRequired = false;
		}
        // sl-19679 - changed this from areThereAnyWebPressesInPool() to areThereAnyRollPressesInPool()
		else if (areThereAnyRollPressesInPool(rSection)) {
            oReturn.bRequired = false;  

            if (rSection.ordrevds_flg_rolltosheet == 1) {
                
                var rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getRecord(1).sa_order_revds_press_pool_to_sa_task.getRecord(1);
                
                if (rTask.tasktype_id === TASKTYPEID.GrandFormatPress) {
                    
                    oReturn.rConvTask = scopes.avPress.getGrandFormatRollToSheetConverter(rTask);
                    oReturn.bRequired = true;  
                }
            }
		}
        // Only for sheetfed presses
		else if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool) 
                && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getRecord(1).sa_order_revds_press_pool_to_sa_task)) {

            oReturn.bRequired = true;
        }
    }

    return oReturn;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 *
 * @return
 * @properties={typeid:24,uuid:"B95C576C-BE51-441B-83E1-C1DEEAEE3AC8"}
 */
function areThereAnyRollPressesInPool(rSection) {
	var bAreThereAnyRollPressesInPool = false;
	
	if (rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool) {
		var TT = scopes.avTask.TASKTYPEID;
		var aRollPressTypes = [TT.WebPress, TT.DigitalRollPress, TT.FlexoPress, TT.GrandFormatPress, TT.WideFormatPress];

		for (var i = 1; i <= rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getSize(); i++) {
			var rPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getRecord(i);
			var rTask = rPress.sa_order_revds_press_pool_to_sa_task.getRecord(1);
			var rTaskStd = rTask.sa_task_to_sa_task_standard.getRecord(1);

			if (aRollPressTypes.indexOf(rTask.tasktype_id) > -1) {
				if (rTask.tasktype_id == TT.FlexoPress) {
					// press type = Web
					if (rTaskStd.taskstd_press_type == "W") {
						bAreThereAnyRollPressesInPool = true;
						break;
					}
				}
				else if (rTask.tasktype_id == TT.GrandFormatPress || rTask.tasktype_id == TT.WideFormatPress) {
					// paper type = Roll or Both
					if (rTaskStd.taskstd_paper_type == "R" || rTaskStd.taskstd_paper_type == "B") {
						bAreThereAnyRollPressesInPool = true;
						break;
					}
				}
				else {
					bAreThereAnyRollPressesInPool = true;
					break;
				}
			}
		}
	}

	return bAreThereAnyRollPressesInPool;
}

/**
 * Calculates all the paper info for the roll to sheet converter in offline mode
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 7, 2016
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty
 * @param {scopes.avSales.oEstOrdSection} oEstOrdSection 
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} ReturnDesc
 * @public
 *
 * @properties={typeid:24,uuid:"A21DF361-536A-4AC9-9FC1-59392D5B2DD3"}
 */
function rollToSheetConverter_calculateOffline (rTaskQty, oEstOrdSection) {
	
	var rSection = oEstOrdSection.rSection,
		nQty = rSection.sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdsqty_qty,
		rPaper = oEstOrdSection.oPaper.rPaper,
		rConverter = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),
		oRotate = {},
		oPaper = {},
		nRollWeight = 0,
		nPaperWeight = 0,
		nPaperMWeight = 0,
		rSkid = null,
		nSkidMaxWeight = 0,
		nSkids = 0,
		bRollPaper = (rPaper) ? scopes.avInv.isRoll(null,rPaper.item_id.toString()) : false;
	
	// Check to see if we have roll paper
	if (rPaper
		&& bRollPaper
		&& rSection.ordrevds_over_press_sheet_size
		&& rollToSheetConverter_validatePressSheetSize (rSection, rPaper) 
		&& rollToSheetConverter_validatePaper (rPaper, rConverter.sa_order_revds_task_to_sa_task.getRecord(1))) {
//	if (rPaper
//		&& rPaper.in_item_paper_to_in_item.in_item_to_in_item_class.in_item_class_to_app_item_class_type.itemclasstype_code == 'ROLLPAPER'
//		&& rSection.ordrevds_over_press_sheet_size
//		&& rollToSheetConverter_validatePressSheetSize (rSection, rPaper) 
//		&& rollToSheetConverter_validatePaper (rPaper, rConverter.sa_order_revds_task_to_sa_task.getRecord(1))) {
		
		// Rotate the paper according to grain
		oRotate = rollToSheetConverter_rotatePressSheet (rSection, rPaper, rConverter);
		
		rSection.ordrevds_across_roll = (rSection.ordrevds_across_roll_over) ? rSection.ordrevds_across_roll_over : oRotate.iAcrossRoll;
		
		rConverter.ordrevdstask_roll_feed_length = oRotate.nFeedLength;
		
		oPaper = rollToSheetConverter_calculatePaper (rSection, rPaper, rConverter, nQty);
		
		if( scopes.globals.avPref_dimension_unit === "M")	{
			
			nRollWeight = globals["avCalcs_paper_weightMetricGSM"](rPaper.paper_first_dim, oPaper.nLinearFeet * 1000, rPaper.in_item_paper_to_in_paper_brand.paperbrand_basis_weight);
			
			nPaperWeight = nQty * globals["avCalcs_paper_weightMetricGSM"](oRotate.nWidth, oRotate.nFeedLength, rPaper.in_item_paper_to_in_paper_brand.paperbrand_basis_weight);

			rTaskQty.ordrevdstqty_weight = nPaperWeight;
			rTaskQty.ordrevdstqty_weight_roll = nRollWeight;
			
		} else {
			
			nRollWeight = globals["avCalcs_paper_mWeight"](rPaper.paper_first_dim, oPaper.nLinearFeet * 12, rPaper, null, true) / 1000;
			
			nPaperMWeight = globals["avCalcs_paper_mWeight"](oRotate.nWidth, oRotate.nFeedLength, rPaper, null, true);
			nPaperWeight = nPaperMWeight * ( nQty / 1000 );
			
		}
		
		rTaskQty.ordrevdstqty_nr_rolls = Math.ceil(oPaper.nRollsRequired);
		rTaskQty.ordrevdstqty_qty_rolls = oPaper.nRollsRequired;
		rTaskQty.ordrevdstqty_length =  oPaper.nLinearFeet;
		rTaskQty.ordrevdstqty_t_spoils = oPaper.nTotalSpoilsFt;
		rTaskQty.ordrevdstqty_weight = nPaperWeight;
		rTaskQty.ordrevdstqty_weight_roll = nRollWeight;
		
		// Need to compute the skids needed for the paperWeight (pack by skid max weight)
		if (rSection.ordrevds_flg_rolltosheet 
				&& rSection.ordrevds_roll_to_sheet == "offline"
				&& rTaskQty.item_id
				&& utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_in_item)) {
			
			rSkid = rTaskQty.sa_order_revds_task_qty_to_in_item.getRecord(1);
			nSkidMaxWeight = rSkid.item_max_weight;
			nSkids = Math.ceil(nPaperWeight / nSkidMaxWeight);
			
			rTaskQty.ordrevdstqty_qty_item = nSkids;
			
		} else {
			
			rTaskQty.item_id = null;
			rTaskQty.ordrevdstqty_qty_item = null;			
		}
		
		
		databaseManager.saveData(rConverter);
			
	}
	
	return rTaskQty;
}

/**
 * Calculates the paper details for the converting task
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 7, 2016
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/in_item_paper>} rPaper - The paper record
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rConverter - The converter task
 * @param {Number} nPressSheets - the qty to produce
 * @param {Number} [nPressOverTotalSpoilsFt] - optional override value
 * @returns {{nConvPressSpoils : Number, nConvRunSpoils : Number, nRollLength : Number, nRollsRequired : Number, nRollsSetup : Number, nConvSetupSpoils : Number, nTotalSpoilsFt : Number, nLinearFeet : Number}} ReturnDesc
 * @public
 *
 * @properties={typeid:24,uuid:"326702CC-E840-491C-ABCD-7B3A7D340272"}
 * @AllowToRunInFind
 */
function rollToSheetConverter_calculatePaper (rSection, rPaper, rConverter, nPressSheets, nPressOverTotalSpoilsFt) {
	
	var nConvPressSpoils = 0,
		nConvRunSpoils = 0,
		nRollLength = 0,
		nRollsRequired = 0,
		nRollsSetup = 0,
		nConvSetupSpoils = 0,
		nTotalSpoilsFt = 0,
		nLinearFeet = 0,
		oReturn = {},
		iPaperDim2 = 0;
		
	if (rSection.ordrevds_is_gang == 1 && scopes.avSection.oGang) {
		
		oReturn.nConvPressSpoils = null;
		oReturn.nConvRunSpoils = null;
		oReturn.nRollLength = null;
		oReturn.nRollsRequired = null;
		oReturn.nRollsSetup = null;
		oReturn.nConvSetupSpoils = null;
		oReturn.nTotalSpoilsFt = null;
		nLinearFeet = scopes.avSection.oGang.paper_l * nPressSheets;
		nLinearFeet = (scopes.globals.avPref_dimension_unit === "M") ? nLinearFeet / 1000 : nLinearFeet / 12;
		oReturn.nLinearFeet = nLinearFeet;
		return oReturn;
	}
	
    if (!rSection.ordrevds_across_roll) {
        rSection.ordrevds_across_roll = 1;
    }
    if (!rConverter.ordrevdstask_roll_feed_length) {
        
        if (rSection.ordrevds_over_paper_size) {
            
            iPaperDim2 = ( rSection.ordrevds_over_paper_size && rSection.ordrevds_over_paper_size.toLowerCase().search("x") > -1 ) ? parseFloat(rSection.ordrevds_over_paper_size.toLowerCase().split("x")[1]) : rPaper.paper_second_dim;
            rConverter.ordrevdstask_roll_feed_length = iPaperDim2;
        }
        else {
            rConverter.ordrevdstask_roll_feed_length = 1;
        }
    }
    
	nConvPressSpoils = Math.ceil(nPressSheets / rSection.ordrevds_across_roll) * rConverter.ordrevdstask_roll_feed_length;
	nConvPressSpoils = (scopes.globals.avPref_dimension_unit === "M") ? nConvPressSpoils / 1000 : nConvPressSpoils / 12;
	
	
	if (utils.hasRecords(rConverter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_spoil$taskmachineid )){
		
		nConvRunSpoils = nConvPressSpoils * rConverter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_spoil$taskmachineid.taskspoil_percent;
		
	} 
	else {
		
		nConvRunSpoils = 0;
	}

	// sl-8176 - if taskstd_calc_roll_changes is off then we assume they arent entering paper_roll_avg_length on items, and dont do any calcs that use that field 
	var bCalcRollChanges = utils.hasRecords(rConverter.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard) 
		&& rConverter.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_calc_roll_changes == 1;  
	
	if(bCalcRollChanges){
		nRollLength = (rPaper.paper_roll_avg_length && rPaper.paper_roll_avg_length > 0) ? rPaper.paper_roll_avg_length : rPaper.paper_second_dim;
		nRollsRequired = (nConvRunSpoils + nConvPressSpoils) / nRollLength;
		nRollsSetup = Math.ceil(nRollsRequired);
	}

	if (utils.hasRecords(rConverter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_setup$taskmachineid )){
		if(bCalcRollChanges){
			nConvSetupSpoils = nRollsSetup * rConverter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_spoil$taskmachineid.taskspoil_setup_roll;
		}
		else{
			nConvSetupSpoils = rConverter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.sa_order_revds_task_qty_to_sa_task_spoil$taskmachineid.taskspoil_setup_roll;
		}		
	}
	
	if (nPressOverTotalSpoilsFt) {
		
		nTotalSpoilsFt = nPressOverTotalSpoilsFt;
		
		if(bCalcRollChanges){
			nRollsRequired = (nTotalSpoilsFt  + nConvPressSpoils) / nRollLength;
		}		
	} 
	else {
	
		nTotalSpoilsFt = nConvRunSpoils + nConvSetupSpoils;
	}
	nLinearFeet = nTotalSpoilsFt + nConvPressSpoils;
	
	oReturn.nConvPressSpoils = nConvPressSpoils;
	oReturn.nConvRunSpoils = nConvRunSpoils;
	oReturn.nRollLength = nRollLength;
	oReturn.nRollsRequired = nRollsRequired;
	oReturn.nRollsSetup = nRollsSetup;
	oReturn.nConvSetupSpoils = nConvSetupSpoils;
	oReturn.nTotalSpoilsFt = nTotalSpoilsFt;
	oReturn.nLinearFeet = nLinearFeet;
	
	return oReturn;
	
}

/**
 * Clears the tasks Run Time and Material, so nothing will calculate for the run
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 4, 2016
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * @param {Number} [nOverValue] optional value to set the override to (like 0 for example; defaults to null)
 * @public
 *
 * @properties={typeid:24,uuid:"2885BDEC-1107-4977-B0CF-782EE71FB751"}
 */
function clearMaterialAndRunTime (rTask, nOverValue) {
	
	if (!rTask) return;
	
	var fsTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty,
		rTaskQty = null,
		i = 0;
	
	if (!nOverValue) nOverValue = null;
	
	for ( i = 1; i <= fsTaskQty.getSize(); i++ ) {
		
		rTaskQty = fsTaskQty.getRecord(i);
		
		rTaskQty.ordrevdstqty_time_run_over = nOverValue;
		rTaskQty.ordrevdstqty_time_run = null;
		rTaskQty.ordrevdstqty_qty_item_over = nOverValue;
		rTaskQty.ordrevdstqty_qty_item = null;
	}
}

/**
 * Gets the first task that matches the tasktypeid
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 10, 2016
 * @param {String} sSectionID
 * @param {Number} iTaskTypeID
 * @returns {JSRecord<db:/avanti/sa_order_revds_task>}
 * @public
 *
 * @properties={typeid:24,uuid:"0D407D7E-8A6F-4B3D-AB3B-369AA55C0032"}
 */
function getTask_sectionTaskByTaskTypeID (sSectionID, iTaskTypeID) {
	
	// Check for task
	var oEstOrdSec = scopes.avSales.oEstOrd.getSectionObject(sSectionID),
		aTasks = oEstOrdSec.aTasks,
	 	i = 0,
		iMax = 0,
		rTask = null;
	
	iMax = aTasks.length;
	
	for ( i = 0; i < iMax; i++ ) {
		
		if (aTasks[i].iTaskTypeId === iTaskTypeID) {
			
			rTask = aTasks[i].rTask;
			
			break;
		}
	}
	return rTask;
}

/**
 * Checks to see whether or not this is one of the tasks whose standard relies on the field "taskstd_machine_var_based_on"
 *
 * <AUTHOR> Dotzlaw
 * @since Mar 15, 2016
 * @param {JSRecord<db:/avanti/sa_task>} rTask - rTask
 * @returns {Boolean} 
 * @public
 *
 * @properties={typeid:24,uuid:"FD814A6F-2117-4807-961A-3114A4BD28C6"}
 */
function checkTask_notUsingMachineVarBasedOn (rTask) {
	
	var bReturn = false,
	iTaskType = rTask.tasktype_id;
	
	if (iTaskType == 6 
		|| iTaskType === scopes.avTask.TASKTYPEID.Laminating 
		|| iTaskType == 19 
		|| iTaskType == 20 
		|| iTaskType == 21 
		|| iTaskType == 22 
		|| iTaskType == 23 
		|| iTaskType == 24 
		|| iTaskType == 25 
		|| iTaskType == 27 
		|| iTaskType == 30 
		|| iTaskType == 31 
		|| iTaskType == 32 
		|| iTaskType == 33 
		|| iTaskType == 35
		|| iTaskType === scopes.avTask.TASKTYPEID.OfflineCoating
		|| iTaskType === scopes.avTask.TASKTYPEID.HighDieCutter
		|| iTaskType === scopes.avTask.TASKTYPEID.PaddingFanningCarbonless
		|| iTaskType === scopes.avTask.TASKTYPEID.Rewinder
		|| iTaskType === scopes.avTask.TASKTYPEID.RollToSheetConversion) {
			
			bReturn = true;
		}
	
	return bReturn;
}

/**
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * 
 * @returns {Boolean} 
 *
 * @properties={typeid:24,uuid:"D84986DF-B987-4569-89A1-65326065DC1C"}
 */
function doesTaskHaveInlineTasks(rTask){
	// GD - Sep 25, 2014: Added exclusions; no presses for now; any other task should try
	var aTaskExcludes = [scopes.avTask.TASKTYPEID.ConventionalPress, 
						scopes.avTask.TASKTYPEID.DigitalRollPress, 
						scopes.avTask.TASKTYPEID.DigitalSheetPress, 
						scopes.avTask.TASKTYPEID.FlexoPress, 
						scopes.avTask.TASKTYPEID.WideFormatPress, 
						scopes.avTask.TASKTYPEID.WebPress, 
						scopes.avTask.TASKTYPEID.ScreenPress, 
						scopes.avTask.TASKTYPEID.LetterPress];
	
	if(rTask 
	   && utils.hasRecords(rTask.sa_task_to_sa_task_standard) 
	   && rTask.sa_task_to_sa_task_standard.taskstd_inline == 1
	   && utils.hasRecords(rTask.sa_task_to_sa_task_associated_tasks)
	   && aTaskExcludes.indexOf(rTask.tasktype_id) == -1){
		
	   	return true;
	}
	else{
		return false;
	}
}

/**
 * Checks to see if the task is a system task
 * 
 * <AUTHOR> Dotzlaw
 * @since July 6, 2016
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask
 * @returns {Boolean} 
 * @public
 * 
 * @properties={typeid:24,uuid:"E005B015-900F-4FDC-8736-AC0134B4A81F"}
 */
function checkForSystemTaskType (rOrdTask) {
	
	if (!utils.hasRecords(rOrdTask.sa_order_revds_task_to_sa_task)) return false;
	
	var iTaskTypeID = rOrdTask.sa_order_revds_task_to_sa_task.tasktype_id;
	
	if (iTaskTypeID === TASKTYPEID.Ink
			|| iTaskTypeID === TASKTYPEID.Plating
			|| iTaskTypeID === TASKTYPEID.PressRun
			|| iTaskTypeID === TASKTYPEID.Paper){
				
			return true;
			
	} else {
		
		return false;
	}
}

/**
 * Checks the Section object to see if there is a manual cutter
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 8, 2018
 * @param {scopes.avSales.oEstOrdSection} oSection
 * @returns {Boolean} ReturnDesc
 * @public
 *
 * @properties={typeid:24,uuid:"A46D774F-FBAF-40D1-9EB2-01AA40702860"}
 */
function checkForManualCutter(oSection) {
    
    var i = 0,
        iMax = 0,
        bFound = false;

    iMax = oSection.aTasks.length;
    for (i = 0; i < iMax; i++) {

        if (oSection.aTasks[i].iTaskTypeId == scopes.avTask.TASKTYPEID.Cutting 
                && oSection.rSection.ordrevds_flg_finaltrim != 1) {

            bFound = true;
            break;
        }
    }
    return bFound;
}

/**
 * Record after-delete trigger.
 *
 * @param {JSRecord<db:/avanti/sa_task>} record record that is deleted
 *
 * @properties={typeid:24,uuid:"D660DBB9-9D14-4686-B9C2-7EAC6C8C190C"}
 */
function afterRecordDelete_sa_task (record) {
	
	// GD - Jul 9, 2016: SL-9045: We originally used a post delete method on the form, to set in_item record to be deleted, if the task that was deleted was an outsourced task
	// However, this did not work, and was instead setting a different outsourced task's in_item record to be deleted
	// This method should fix that
	
	//Set the outsourced item to be deleted.
	if ( utils.hasRecords(record.sa_task_to_in_item$outsidepurchases) ) {
		record.sa_task_to_in_item$outsidepurchases.item_status = "D";
	}
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task_precut>} rPreCut
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"0C2F8942-CEDF-4499-BF99-F0B366A92B22"}
 */
function getPreCutLength(rPreCut){
	var nLength = 0;
	
	if(rPreCut.taskprecut_roll_cutting){
		if(utils.hasRecords(rPreCut.sa_order_revds_task_precut_to_sa_order_revds_task_precut_qty$avsales_selectedrevisiondetailqtyuuid)){
			nLength = rPreCut.sa_order_revds_task_precut_to_sa_order_revds_task_precut_qty$avsales_selectedrevisiondetailqtyuuid.taskprecutqty_length;
		}
	}
	else{
		nLength = rPreCut.taskprecut_length;
	}
	
	return nLength;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask
 *
 * @return
 * @properties={typeid:24,uuid:"64569EF3-C219-46A6-96B8-E56402652464"}
 */
function isRollInterimCutter(rOrdTask){
	if(rOrdTask.ordrevdstask_interim_cutter 
		&& utils.hasRecords(rOrdTask.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task)
		&& rOrdTask.sa_order_revds_task_to_sa_order_revds_task_precut$cutting_task.taskprecut_roll_cutting){
		
		return true;
	}
	else{
		return false;
	}
}

/**
 * @public 
 * @param {Number} nTaskType
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E50FCEEF-AEB8-424B-B505-A8C7DFEE7A35"}
 */
function doesTaskTypeAllowEditingOfMatQty(nTaskType){
	// these are the tasktypes that have an editable mat qty in task dlg
	return nTaskType == TASKTYPEID.Other || nTaskType == TASKTYPEID.Binding || nTaskType == TASKTYPEID.Finishing 
		|| nTaskType == TASKTYPEID.PackagingShipping || nTaskType == TASKTYPEID.Rewinder || nTaskType == TASKTYPEID.OfflineCoating
		|| nTaskType == TASKTYPEID.Material;
}

/**
 * @public 
 * @param {String} sTaskID
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"9DF73660-666E-45C1-A65C-BBB68D69D32E"}
 */
function doesTaskAllowEditingOfMatQty(sTaskID){
	if(sTaskID){
		/**@type {JSRecord<db:/avanti/sa_task>} */
		var rTask = scopes.avDB.getRec('sa_task', ['task_id'], [sTaskID]);
		
		if (rTask && doesTaskTypeAllowEditingOfMatQty(rTask.tasktype_id)){
			if(rTask.tasktype_id == TASKTYPEID.Binding || rTask.tasktype_id == TASKTYPEID.Other || rTask.tasktype_id == TASKTYPEID.Finishing){
				if(utils.hasRecords(rTask.sa_task_to_sa_task_standard) && rTask.sa_task_to_sa_task_standard.taskstd_materials_needed){
					if(rTask.tasktype_id == TASKTYPEID.Binding || rTask.tasktype_id == TASKTYPEID.Other){
						var sMachineVarBasedOn = rTask.sa_task_to_sa_task_standard.taskstd_machine_var_based_on;

						if (sMachineVarBasedOn != "F" && sMachineVarBasedOn != "O"){
							return true;
						}
					}
					else{
						return true;
					}
				}
			}
			else{
				return true;
			}
		}
	}

	return false;
}

/**
 * TODO generated, please specify type and doc for the params
 * @param sCCID
 * @param sTaskID
 *
 * @return
 * @properties={typeid:24,uuid:"4263CCB1-BE75-4C53-9FD8-90A862ABBE4A"}
 */
function isCostCenterUsedByTask(sCCID, sTaskID){
	return scopes.avDB.Exists('sa_task_cost_link', ['task_id', 'cc_id'], [sTaskID, sCCID]);
}

/**
 * Refreshes the task qty records if any are found missing
 *
 * <AUTHOR> Dotzlaw
 * @since Jun 6, 2017
 * @param {String} sSectionId 	
 * @public
 *
 * @properties={typeid:24,uuid:"B9128F6F-0895-4C04-B3A7-DDD05F08E8A6"}
 */
function checkForMissingTaskQtyRecords (sSectionId) {
	
	if (!sSectionId || !scopes.avSales.oEstOrd) return;
	
	var i = 0,
	/** @type {JSRecord<db:/avanti/sa_order_revds_task>} */ 
	rTask = null,
	oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(sSectionId),
	iMax = 0;
	
	if (!oEstOrdSection) return;

	iMax = oEstOrdSection.aTasks.length;
	for ( i = 0; i < iMax; i++ ) {
	
		rTask = oEstOrdSection.aTasks[i].rTask;
		
		if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)
				&& rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_id == undefined){
			
			rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.loadAllRecords();
			
			application.output("sa_order_revision_detail_section_tbl.onRecordSelection(): ERROR: Task Qty records were missing and needed to be reloaded", LOGGINGLEVEL.ERROR);
		}
	}

//	globals.avSales_selectedRevisionSectionID = sSectionId;
//
//	for ( i = 1; i <= _to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted.getSize(); i++ ) {
//		
//		rTask = _to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted.getRecord(i);
//		
//		if (utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)
//				&& rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_id == undefined){
//			
//			rTask.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.loadAllRecords();
//			
//			application.output("sa_order_revision_detail_section_tbl.onRecordSelection(): ERROR: Task Qty records were missing and needed to be reloaded", LOGGINGLEVEL.ERROR);
//		}
//	}
}

/**
 * @public 
 * 
 * @param {UUID|String} sTaskID
 * @param {UUID|String} sTaskMachineID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B0DC3BA9-0D6F-4232-BC1A-1B34DA9DF8D3"}
 */
function taskUsesTaskMachine(sTaskID, sTaskMachineID){
	return scopes.avDB.Exists('sa_task_machine', ['task_id', 'taskmachine_id'], [sTaskID.toString(), sTaskMachineID.toString()]);
}

/**
 * Check if the operaion is valid for the section task
 *
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask - Section Task Record
 * @param {String} sTaskOperationKey - task operation key Ex. i18n:avanti.lbl.task_run
 *
 * @returns {Boolean} True or False
 *
 *
 * @properties={typeid:24,uuid:"3A1A0855-74F6-44EA-A116-4A39D65FC5A6"}
 */
function isValidOperationForTask(rOrdTask, sTaskOperationKey) {

    if (!rOrdTask || !utils.hasRecords(rOrdTask.sa_order_revds_task_to_sa_task)) {
        return false;
    }

    var rTask = rOrdTask.sa_order_revds_task_to_sa_task.getRecord(1);

    var fsAppTaskOperation = scopes.avDB.getFS("app_task_operation", ["tasktype_id", "taskoper_key"], [rTask.tasktype_id, sTaskOperationKey], null, null, true);

    if (fsAppTaskOperation && fsAppTaskOperation.getSize() > 0) {
        return true;
    }
    else {
        return false;
    }
}


/**
 * Reallocate Task Price Rule to Operations
 *
 * <AUTHOR> Dol
 * @since Jun 6, 2017
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - Task Quantity Record
 * @param {JSRecord<db:/avanti/sa_order_revds_press>} rPress - Press Record
 * @param {Number} nCurrentTaskTotal - The current task total
 * @param {Number} nPriceRuleTotal - The total task from price rule
 * @param {scopes.avSales.oEstOrdTask} [oEstOrdTask]
 * 
 * @return {Boolean}  True = operations allocated, false = no allocation
 * 
 * @properties={typeid:24,uuid:"D82EBCEA-9A2E-43D8-A9AF-F4CDBDA4BD26"}
 */
function reallocateTaskPriceToOperations (rOrdTaskQty, rPress, nCurrentTaskTotal, nPriceRuleTotal, oEstOrdTask) {
	
	var sAllocationType = "LM"; // The default for now is based on Labour-Time and Material-Price
		
	// Do not reallocate if there are any task overrides - As per Josh
	if (scopes.avTask.checkForTaskOverrides(rOrdTaskQty,rPress)) {
	    return false;
	}
	
	if (!rOrdTaskQty && 
		!utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task) &&
		!utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)) {
		return false;
	}
	
	var rTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);
	
	/**@type {Number} */
	var nCurrentOperationTotal = 0, nCurrentOperationTime = 0, nNewOperationTotal = 0, nRatio = 0, nTotal = 0, nCurrentTotalMaterial = 0, nTotalTime = 0;
	
	nTotalTime = rOrdTaskQty.ordrevdstqty_time_total; //This excluded helper time as per Jennifer so we will add helper time below. 
	nCurrentTotalMaterial = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
	
	var nTaskTypeID = rTask.tasktype_id;
	
    if (rTask.tasktype_id == 99) {
        nTaskTypeID = rPress.sa_order_revds_press_to_sa_task.tasktype_id;
        nCurrentTotalMaterial += rPress.mpress_click_price_x; //Click charges are material;
        nTotalTime += rPress.clc_time_help;
    }
    else {
        if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine)) {
            nTotalTime += rOrdTaskQty.ordrevdstqty_time_run * rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_helpers;
        }
    }
	
	//Calculate new total labour and material
	var nNewTotalMaterial = nCurrentTotalMaterial; //do not change material as per Randy & Jennifer.
	
	var nNewTotalLabour = scopes.avUtils.roundNumber(nPriceRuleTotal - nNewTotalMaterial,2);
    if (nNewTotalLabour < 0) {
        nNewTotalMaterial += nNewTotalLabour;
        nNewTotalLabour = 0;
    }
	
    if (nCurrentTaskTotal == 0) {
        nCurrentTaskTotal = nPriceRuleTotal;
    } 
	
	/**@type {JSFoundset<db:/avanti/app_task_operation>} */
	var fsAppTaskOperation = scopes.avDB.getFS("app_task_operation",["tasktype_id"],[nTaskTypeID],"sequence_nr",null,true);
	
	for ( var i = 1; i <= fsAppTaskOperation.getSize(); i++ ) {
		var rAppTaskOperation = fsAppTaskOperation.getRecord(i);
		
		switch ( rAppTaskOperation.taskoper_key) {
			case "i18n:avanti.lbl.postage":
				
				if (Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.LimitPostageTasktoOnePostageItem)) &&
						utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post)) {
					var rPost = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_post.getRecord(1);
					
					if (rPost.ordrevdstaskpost_markup_pcto == null && rPost.ordrevdstaskpost_mkupamt_posto == null) {
						rPost.ordrevdstaskpost_mkupamt_post = scopes.avUtils.roundNumber(nPriceRuleTotal - rPost.ordrevdstaskpost_cost_post, 2);
						rPost.ordrevdstaskpost_mkuppct_post = rPost.ordrevdstaskpost_mkupamt_post / rPost.ordrevdstaskpost_price_post * 100;
						rPost.ordrevdstaskpost_price_post_t = scopes.avMath.calculateUsingSQL(rPost.ordrevdstaskpost_price_post, rPost.ordrevdstaskpost_mkupamt_post, "+", 2);
					}
					
					if (rPost.ordrevdstaskpost_mrk_over == null && utils.hasRecords(rTask.sa_task_to_sa_task_standard)) {
						rPost.ordrevdstaskpost_mrk_orig = rTask.sa_task_to_sa_task_standard.taskstd_mrkup * 100;
					}
					
					rPost.ordrevdstaskpost_is_prule = 1;
				}
				
				break;
				
			case "i18n:avanti.lbl.buyout":				
				break;
				
			case "i18n:avanti.lbl.helper":
                if (rTask.tasktype_id == 99) {
                    nCurrentOperationTotal = rPress.mpress_help_price_x;
                    nCurrentOperationTime = rPress.clc_time_help;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rPress.mpress_help_price_x = nNewOperationTotal;
                    rPress.mpress_help_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_help_price_x, null, rOrdTaskQty), 2);
                    rPress.mpress_mrkup_help = ( rPress.mpress_help_cost > 0 ? ( rPress.mpress_help_price_x - rPress.mpress_help_cost ) / rPress.mpress_help_cost * 100 : 100 );
                    rOrdTaskQty.ordrevdstqty_price_help = rPress.mpress_help_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_help_x = rPress.mpress_help_price_x;
                    rPress.mpress_prule_help_price_x = rPress.mpress_help_price_x;
                }
                else {
                    nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_price_help_x;
                    if (utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine)) {
                        nCurrentOperationTime = rOrdTaskQty.ordrevdstqty_time_run * rOrdTaskQty.sa_order_revds_task_qty_to_sa_task_machine.taskmachine_nr_helpers;
                    }
                    else {
                        nCurrentOperationTime = 0;
                    }
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_help_x = nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_help = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_help_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_lprice_help_x = rOrdTaskQty.ordrevdstqty_price_help_x;
                    rOrdTaskQty.ordrevdstqty_mrk_help = ( rOrdTaskQty.ordrevdstqty_cost_help > 0 ? ( rOrdTaskQty.ordrevdstqty_price_help_x - rOrdTaskQty.ordrevdstqty_cost_help ) / rOrdTaskQty.ordrevdstqty_cost_help * 100 : 100 );
                    rOrdTaskQty.ordrevdstqty_mrk_help_list = rOrdTaskQty.ordrevdstqty_mrk_help;
                    rOrdTaskQty.ordrevdstqty_prule_help_x = rOrdTaskQty.ordrevdstqty_price_help_x;
                }
                break;
			
			case "i18n:avanti.lbl.task_extraPlMR":
				nCurrentOperationTotal = rPress.mpress_explatemr_price_x;
				nCurrentOperationTime = rPress.clc_time_extraPlateMr;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_explatemr_price_x = nNewOperationTotal;
				rPress.mpress_explatemr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_explatemr_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_explate = (rPress.mpress_explate_cost > 0 ? (rPress.mpress_explatemr_price_x - rPress.mpress_explate_cost) / rPress.mpress_explate_cost * 100 : 100);
				rPress.mpress_prule_help_price_x = rPress.mpress_explatemr_price_x;
				break;
			
			case "i18n:avanti.lbl.task_firstMR":
				nCurrentOperationTotal = rPress.mpress_firstmr_price_x;
				nCurrentOperationTime = rPress.clc_time_firstmr;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_firstmr_price_x = nNewOperationTotal;
				rPress.mpress_firstmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_firstmr_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_firstmr = (rPress.mpress_firstmr_cost > 0 ? (rPress.mpress_firstmr_price_x - rPress.mpress_firstmr_cost) / rPress.mpress_firstmr_cost * 100 : 100);
				rPress.mpress_prule_firstmr_price_x = rPress.mpress_firstmr_price_x;
				break;
				
			case "i18n:avanti.lbl.task_hangDie":			
			    break;
			    
			case "i18n:avanti.lbl.task_perfScoreMR":
				nCurrentOperationTotal = rPress.mpress_perfscoremr_price_x;
				nCurrentOperationTime = rPress.clc_time_perfmr;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_perfscoremr_price_x = nNewOperationTotal;
				rPress.mpress_perfscoremr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_perfscoremr_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_perf = (rPress.mpress_perfscoremr_cost > 0 ? (rPress.mpress_perfscoremr_price_x - rPress.mpress_perfscoremr_cost) / rPress.mpress_perfscoremr_cost * 100 : 100);
				rPress.mpress_prule_perfscoremr_price_x = rPress.mpress_perfscoremr_price_x;
				break;
				
			case "i18n:avanti.lbl.task_plating":			
			    break;
			    
			case "i18n:avanti.lbl.task_repeatMR":
				nCurrentOperationTotal = rPress.mpress_addmr_price_x;
				nCurrentOperationTime = rPress.clc_time_addmr;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_addmr_price_x = nNewOperationTotal;
				rPress.mpress_addmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_addmr_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_addmr = (rPress.mpress_addmr_cost > 0 ? (rPress.mpress_addmr_price_x - rPress.mpress_addmr_cost) / rPress.mpress_addmr_cost * 100 : 100);
				rPress.mpress_prule_addmr_price_x = rPress.mpress_addmr_price_x;
				break;
				
			case "i18n:avanti.lbl.task_run":
                if (rTask.tasktype_id == 99 && rPress.sa_order_revds_press_to_sa_task.tasktype_id != TASKTYPEID.GrandFormatPress) {
                    nCurrentOperationTotal = rPress.mpress_run_price_x;
                    nCurrentOperationTime = rPress.clc_time_run;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rPress.mpress_run_price_x = nNewOperationTotal;
                    rPress.mpress_run_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_run_price_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_price_run = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = rPress.mpress_run_price_x;
                    rPress.mpress_mrkup_run = ( rPress.mpress_run_cost > 0 ? ( rPress.mpress_run_price_x - rPress.mpress_run_cost ) / rPress.mpress_run_cost * 100 : 100 );
                    rPress.mpress_prule_run_price_x = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rPress.mpress_run_price_x;
                }
                else {
                    nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_price_run_x;
                    nCurrentOperationTime = rOrdTaskQty.ordrevdstqty_time_run;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_run_x = nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_run = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                    rOrdTaskQty.ordrevdstqty_mrk_run = ( rOrdTaskQty.ordrevdstqty_cost_run > 0 ? ( rOrdTaskQty.ordrevdstqty_price_run_x - rOrdTaskQty.ordrevdstqty_cost_run ) / rOrdTaskQty.ordrevdstqty_cost_run * 100 : 100 );
                    rOrdTaskQty.ordrevdstqty_mrk_run_list = rOrdTaskQty.ordrevdstqty_mrk_run;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                }
				break;
				
			case "i18n:avanti.lbl.task_setup":
                if (rPress && utils.hasRecords(rPress.sa_order_revds_press_to_sa_task) && rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.GrandFormatPress) {
                    nCurrentOperationTotal = rPress.mpress_setup_price_x;
                    nCurrentOperationTime = rOrdTaskQty.ordrevdstqty_time_setup;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rPress.mpress_setup_price_x = nNewOperationTotal;
                    rPress.mpress_setup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_setup_price_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_setup_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = rPress.mpress_setup_price_x;
                    rPress.mpress_mrkup_setup = ( rPress.mpress_setup_cost > 0 ? ( rPress.mpress_setup_price_x - rPress.mpress_setup_cost ) / rPress.mpress_setup_cost * 100 : 100 );
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_setup_price_x;
                }
                else {
                    nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_price_setup_x;
                    nCurrentOperationTime = rOrdTaskQty.ordrevdstqty_time_setup;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_setup_x = nNewOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_setup = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
                    rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
                    rOrdTaskQty.ordrevdstqty_mrk_setup = ( rOrdTaskQty.ordrevdstqty_cost_setup > 0 ? ( rOrdTaskQty.ordrevdstqty_price_setup_x - rOrdTaskQty.ordrevdstqty_cost_setup ) / rOrdTaskQty.ordrevdstqty_cost_setup * 100 : 100 );
                    rOrdTaskQty.ordrevdstqty_mrk_setup_list = rOrdTaskQty.ordrevdstqty_mrk_setup;
                    rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x; 
                }
				break;
			
			case "i18n:avanti.lbl.task_setupPress":
                if (rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.DigitalSheetPress || rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.DigitalRollPress) {
                    nCurrentOperationTotal = rPress.mpress_setup_price_x;
                    nCurrentOperationTime = rOrdTaskQty.ordrevdstqty_time_setup;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rPress.mpress_setup_price_x = nNewOperationTotal;
                    rPress.mpress_setup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_setup_price_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_setup_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = rPress.mpress_setup_price_x;
                    rPress.mpress_mrkup_setup = ( rPress.mpress_setup_cost > 0 ? ( rPress.mpress_setup_price_x - rPress.mpress_setup_cost ) / rPress.mpress_setup_cost * 100 : 100 );
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_setup_price_x;
                }
                else {
                    nCurrentOperationTotal = rPress.mpress_presssetup_price_x;
                    nCurrentOperationTime = rPress.clc_time_presssetup;
                    setNewOperationTotal_Labour();
                    nTotal += nNewOperationTotal;
                    rPress.mpress_presssetup_price_x = nNewOperationTotal;
                    rPress.mpress_presssetup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_presssetup_price_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_presssetup_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = rPress.mpress_presssetup_price_x;
                    rPress.mpress_mrkup_presssetup = ( rPress.mpress_setup_cost > 0 ? ( rPress.mpress_presssetup_price_x - rPress.mpress_setup_cost ) / rPress.mpress_setup_cost * 100 : 100 );
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_presssetup_price_x;
                }
				break;
				
			case "i18n:avanti.lbl.task_washup":
				nCurrentOperationTotal = rPress.mpress_wash_price_x;
				nCurrentOperationTime = rPress.clc_time_wash;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_wash_price_x = nNewOperationTotal;
				rPress.mpress_wash_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_wash_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_wash = (rPress.mpress_wash_cost > 0 ? (rPress.mpress_wash_price_x - rPress.mpress_wash_cost) / rPress.mpress_wash_cost * 100 : 100);
				rPress.mpress_prule_wash_price_x = rPress.mpress_wash_price_x;
				break;
				
			case "i18n:avanti.lbl.task_workTurnMR":
				nCurrentOperationTotal = rPress.mpress_wtmr_price_x;
				nCurrentOperationTime = rPress.clc_time_wtmr;
				setNewOperationTotal_Labour();
				nTotal += nNewOperationTotal;
				rPress.mpress_wtmr_price_x = nNewOperationTotal;
				rPress.mpress_wtmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_wtmr_price_x, null, rOrdTaskQty),2);
				rPress.mpress_mrkup_wtmr = (rPress.mpress_wtmr_cost > 0 ? (rPress.mpress_wtmr_price_x - rPress.mpress_wtmr_cost) / rPress.mpress_wtmr_cost * 100 : 100);
				rPress.mpress_prule_wtmr_price_x = rPress.mpress_wtmr_price_x;
				break;
		}
	}
	
    if (rTask.tasktype_id == 99 && rPress.mpress_click_price_x > 0) { //Clicks
        nCurrentOperationTotal = rPress.mpress_click_price_x;
        setNewOperationTotal_Material();
        nTotal += nNewOperationTotal;
        rPress.mpress_click_price_x = nNewOperationTotal;
        rPress.mpress_click_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_click_price_x, null, rOrdTaskQty), 2);
        rPress.mpress_click_mrkup = ( rPress.mpress_click_cost > 0 ? ( rPress.mpress_click_price_x - rPress.mpress_click_cost ) / rPress.mpress_click_cost : 100 );
        rPress.mpress_prule_click_price_x = rPress.mpress_click_price_x;
        rOrdTaskQty.ordrevdstqty_click_price_x = rPress.mpress_click_price_x;
        rOrdTaskQty.ordrevdstqty_click_price = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_click_price_x, null, rOrdTaskQty);
    }
    else if (rTask.tasktype_id == 97) {
        nNewOperationTotal = nCurrentTaskTotal;
        nTotal += nNewOperationTotal;
        rOrdTaskQty.ordrevdstqty_t_item_orig_x = nNewOperationTotal;
        rOrdTaskQty.ordrevdstqty_t_item_orig = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_orig_x, null, rOrdTaskQty);
        rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig = ( rOrdTaskQty.ordrevdstqty_t_cost_item_orig > 0 ? ( rOrdTaskQty.ordrevdstqty_t_item_orig_x - rOrdTaskQty.ordrevdstqty_t_cost_item_orig ) / rOrdTaskQty.ordrevdstqty_t_cost_item_orig : 1 );
        rOrdTaskQty.ordrevdstqty_mrk_item1_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig;
        rOrdTaskQty.ordrevdstqty_prule_item_x = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
    }
    else {
        if (rOrdTaskQty.ordrevdstqty_t_item_orig_x > 0) {
            nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
            setNewOperationTotal_Material();
            nTotal += nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_orig_x = nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_orig = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_orig_x, null, rOrdTaskQty);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig = ( rOrdTaskQty.ordrevdstqty_t_cost_item_orig > 0 ? ( rOrdTaskQty.ordrevdstqty_t_item_orig_x - rOrdTaskQty.ordrevdstqty_t_cost_item_orig ) / rOrdTaskQty.ordrevdstqty_t_cost_item_orig : 1 );
            rOrdTaskQty.ordrevdstqty_mrk_item1_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig;
            rOrdTaskQty.ordrevdstqty_prule_item_x = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
        }

        if (rOrdTaskQty.ordrevdstqty_t_item_ori2_x > 0) {
            nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_t_item_ori2_x;
            setNewOperationTotal_Material();
            nTotal += nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori2_x = nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori2 = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori2_x, null, rOrdTaskQty), 2);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori2 = ( rOrdTaskQty.ordrevdstqty_t_cost_item_orig2 > 0 ? ( rOrdTaskQty.ordrevdstqty_t_item_ori2_x - rOrdTaskQty.ordrevdstqty_t_cost_item_orig2 ) / rOrdTaskQty.ordrevdstqty_t_cost_item_orig2 : 1 );
            rOrdTaskQty.ordrevdstqty_mrk_item2_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori2;
            rOrdTaskQty.ordrevdstqty_prule_item2_x = rOrdTaskQty.ordrevdstqty_t_item_ori2_x;
        }

        if (rOrdTaskQty.ordrevdstqty_t_item_ori3_x > 0) {
            nCurrentOperationTotal = rOrdTaskQty.ordrevdstqty_t_item_ori3_x;
            setNewOperationTotal_Material();
            nTotal += nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori3_x = nNewOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori3 = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori3_x, null, rOrdTaskQty), 2);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori3 = ( rOrdTaskQty.ordrevdstqty_t_cost_item_orig3 > 0 ? ( rOrdTaskQty.ordrevdstqty_t_item_ori3_x - rOrdTaskQty.ordrevdstqty_t_cost_item_orig3 ) / rOrdTaskQty.ordrevdstqty_t_cost_item_orig3 : 1 );
            rOrdTaskQty.ordrevdstqty_mrk_item3_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori3;
            rOrdTaskQty.ordrevdstqty_prule_item3_x = rOrdTaskQty.ordrevdstqty_t_item_ori3_x;
        }
    }
		
	//If there is any rounding issues allocate difference to 'Run' operation
    if (nPriceRuleTotal != nTotal) {
        var nDiff = nPriceRuleTotal - nTotal;

        if (nDiff != 0) {
            if ( ( rTask.tasktype_id == 99 && rPress.mpress_run_price_x + nDiff > 0 ) || ( rTask.tasktype_id != 99 && rOrdTaskQty.ordrevdstqty_price_run_x + nDiff > 0 )) { //Distribute difference to run first
                if (rTask.tasktype_id == 99 && rPress.sa_order_revds_press_to_sa_task.tasktype_id != TASKTYPEID.GrandFormatPress) {
                    rPress.mpress_run_price_x += nDiff;
                    rPress.mpress_run_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_run_price_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_price_run = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = rPress.mpress_run_price_x;
                    rPress.mpress_mrkup_run = ( rPress.mpress_run_cost > 0 ? ( rPress.mpress_run_price_x - rPress.mpress_run_cost ) / rPress.mpress_run_cost * 100 : 100 );
                    rPress.mpress_prule_run_price_x = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rPress.mpress_run_price_x;
                }
                else {
                    rOrdTaskQty.ordrevdstqty_price_run_x += nDiff;
                    rOrdTaskQty.ordrevdstqty_price_run = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty), 2);
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                    rOrdTaskQty.ordrevdstqty_mrk_run = ( rOrdTaskQty.ordrevdstqty_cost_run > 0 ? ( rOrdTaskQty.ordrevdstqty_price_run_x - rOrdTaskQty.ordrevdstqty_cost_run ) / rOrdTaskQty.ordrevdstqty_cost_run * 100 : 100 );
                    rOrdTaskQty.ordrevdstqty_mrk_run_list = rOrdTaskQty.ordrevdstqty_mrk_run;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                }
            }
            else {
                if (rTask.tasktype_id == 99 && rPress.mpress_click_price_x + nDiff > 0) {
                    rPress.mpress_click_price_x = rPress.mpress_click_price_x += nDiff;
                    rPress.mpress_click_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_click_price_x, null, rOrdTaskQty), 2);
                    rPress.mpress_click_mrkup = ( rPress.mpress_click_cost > 0 ? ( rPress.mpress_click_price_x - rPress.mpress_click_cost ) / rPress.mpress_click_cost : 100 );
                    rPress.mpress_prule_click_price_x = rPress.mpress_click_price_x;
                    rOrdTaskQty.ordrevdstqty_click_price_x = rPress.mpress_click_price_x;
                    rOrdTaskQty.ordrevdstqty_click_price = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_click_price_x, null, rOrdTaskQty);
                }
                else {
                    if (rOrdTaskQty.ordrevdstqty_t_item_orig_x += nDiff > 0) { //Then try Material
                        rOrdTaskQty.ordrevdstqty_t_item_orig_x += nDiff;
                        rOrdTaskQty.ordrevdstqty_t_item_orig = scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_orig_x, null, rOrdTaskQty);
                        rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig = ( rOrdTaskQty.ordrevdstqty_t_cost_item_orig > 0 ? ( rOrdTaskQty.ordrevdstqty_t_item_orig_x - rOrdTaskQty.ordrevdstqty_t_cost_item_orig ) / rOrdTaskQty.ordrevdstqty_t_cost_item_orig : 1 );
                        rOrdTaskQty.ordrevdstqty_mrk_item1_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig;
                        rOrdTaskQty.ordrevdstqty_prule_item_x = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
                    }
                }
            }
        }
    }

	return true;
	
	function setNewOperationTotal_Labour () {
		switch (sAllocationType) {
			case "P":
				nRatio = nCurrentOperationTotal / nCurrentTaskTotal;
				nNewOperationTotal = scopes.avUtils.roundNumber(nPriceRuleTotal * nRatio,2);
				break;
		
			default:
				nRatio = (nTotalTime > 0 ? nCurrentOperationTime / nTotalTime : 0);
				nNewOperationTotal = scopes.avUtils.roundNumber(nNewTotalLabour * nRatio,2);
				break;
		}
	}
	
	function setNewOperationTotal_Material () {
		switch (sAllocationType) {
			case "P":
				nRatio = nCurrentOperationTotal / nCurrentTaskTotal;
				nNewOperationTotal = scopes.avUtils.roundNumber(nPriceRuleTotal * nRatio,2);
				break;
		
			default:
				nRatio = (nNewTotalMaterial > 0 ? nCurrentOperationTotal / nNewTotalMaterial : 0);
				nNewOperationTotal = scopes.avUtils.roundNumber(nNewTotalMaterial * nRatio,2);
				break;
		}
	}
}

/**
 * Reallocate Task Cost to Operations
 *
 * <AUTHOR> Zafar
 * @since Nov 11, 2018
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - Task Quantity Record
 * @param {Number} nCurrentTaskTotal - The current task total
 * @param {scopes.avSales.oEstOrdTask} [oEstOrdTask]
 * @param {Number} nOCostMarkup - Cost Markup
 * @param {Number} nCustDiscount - Customer Department
 * @return {Boolean}  True = operations allocated, false = no allocation
 * 
 * @properties={typeid:24,uuid:"A18CD29E-1B46-4C1E-8BC3-801B4A000365"}
 */
function reallocateCostMarkupsToOperations(rOrdTaskQty, nCurrentTaskTotal, oEstOrdTask, nOCostMarkup, nCustDiscount) {

    var nPriceRuleTotal = scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_orig, nOCostMarkup, nCustDiscount).nRevisedCost;
    var nCostMarkup = nOCostMarkup;
    var nCostMarkupd = nCostMarkup / 100;
    var rTask = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);
    /**@type {JSRecord<db:/avanti/sa_order_revds_press>} */
    var rPress;
    if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.PressRun) {
        //implement utils.hasrecords
        var rSaOrd = rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task;
        if (utils.hasRecords(rSaOrd)) {
            var rSaOrdRev = rSaOrd.sa_order_revds_task_to_sa_order_revision_detail_section;
            if (utils.hasRecords(rSaOrdRev)) {
                var rSelectedPress = rSaOrdRev.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid;
                if (utils.hasRecords(rSelectedPress)) {
                    rPress = rSelectedPress.getRecord(1);
                }
                else {
                    return false;
                }
            }
        }
    }

    if (!rOrdTaskQty 
        && !utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task) 
        && !utils.hasRecords(rOrdTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)) {
        return false;
    }
    /**@type {Number} */
    var nCurrentOperationTotal = 0, nTotal = 0;
    var nTotal_x = 0;
    var nTaskTypeID = rTask.tasktype_id;
    if (rTask.tasktype_id == 99) {
        nTaskTypeID = rPress.sa_order_revds_press_to_sa_task.tasktype_id;
    }

    if (nCurrentTaskTotal == 0) {
        nCurrentTaskTotal = nPriceRuleTotal;
    }

    /**@type {JSFoundset<db:/avanti/app_task_operation>} */
    var fsAppTaskOperation = scopes.avDB.getFS("app_task_operation", ["tasktype_id"], [nTaskTypeID], "sequence_nr", null, true);
    var nOverridePrice;
    var nActualCost;
    var nOverrideMrk;
    rOrdTaskQty.ordrevdstqty_price_other_x = 0;
    for (var i = 1; i <= fsAppTaskOperation.getSize(); i++) {
        var rAppTaskOperation = fsAppTaskOperation.getRecord(i);
        clearOverrideValues();
        
        switch (rAppTaskOperation.taskoper_key) {
            case "i18n:avanti.lbl.buyout":
                break;

            case "i18n:avanti.lbl.helper":

                if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.PressRun) {
                    rOrdTaskQty.ordrevdstqty_lprice_help_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_help_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);

                    if (rOrdTaskQty.ordrevdstqty_omrk_help != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_help;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_help != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_help_x + rOrdTaskQty.ordrevdstqty_adj_help;
                        rOrdTaskQty.ordrevdstqty_oprice_help = nOverridePrice;
                    }
                    nActualCost = rPress.mpress_help_cost;
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_help_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rPress.mpress_help_price_x = nCurrentOperationTotal;
                    rPress.mpress_help_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_help_price_x, null, rOrdTaskQty), 2);
                    nTotal_x += rPress.mpress_help_price;
                    rPress.mpress_mrkup_help = nCostMarkup;
                    rOrdTaskQty.ordrevdstqty_price_help = rPress.mpress_help_price;
                    rOrdTaskQty.ordrevdstqty_price_help_x = rPress.mpress_help_price_x;
                    rPress.mpress_prule_help_price_x = rPress.mpress_help_price_x;
                }
                else {
                    rOrdTaskQty.ordrevdstqty_lprice_help_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_help, nOCostMarkup, nCustDiscount).nRevisedCost, 2);

                    if (rOrdTaskQty.ordrevdstqty_omrk_help != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_help;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_help != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_help_x + rOrdTaskQty.ordrevdstqty_adj_help;
                        rOrdTaskQty.ordrevdstqty_oprice_help = nOverridePrice;
                    }
                    nActualCost = rOrdTaskQty.ordrevdstqty_cost_help;
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_help, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_help_x = nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_help = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_help_x, null, rOrdTaskQty), 2);
                    nTotal_x += rOrdTaskQty.ordrevdstqty_price_help;
                    rOrdTaskQty.ordrevdstqty_mrk_help = nCostMarkup;
                    rOrdTaskQty.ordrevdstqty_mrk_help_list = rOrdTaskQty.ordrevdstqty_mrk_help;
                    rOrdTaskQty.ordrevdstqty_prule_help_x = rOrdTaskQty.ordrevdstqty_price_help_x;
                }
                break;

            case "i18n:avanti.lbl.task_extraPlMR":
                if (rOrdTaskQty.ordrevdstqty_omrk_explatemr != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_explatemr;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_explatemr != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_explatemr;
                    nActualCost = rPress.mpress_explatemr_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_explatemr_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_explatemr_price_x = nCurrentOperationTotal;
                rPress.mpress_explatemr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_explatemr_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_explatemr_price;
                rPress.mpress_mrkup_explate = nCostMarkup;
                rPress.mpress_prule_help_price_x = rPress.mpress_explatemr_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;

            case "i18n:avanti.lbl.task_firstMR":
                if (rOrdTaskQty.ordrevdstqty_omrk_firstmr != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_firstmr;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_firstmr != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_firstmr;
                    nActualCost = rPress.mpress_firstmr_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_firstmr_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_firstmr_price_x = nCurrentOperationTotal;
                rPress.mpress_firstmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_firstmr_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_firstmr_price;
                rPress.mpress_mrkup_firstmr = nCostMarkup;
                rPress.mpress_prule_firstmr_price_x = rPress.mpress_firstmr_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;

            case "i18n:avanti.lbl.task_hangDie":
                break;

            case "i18n:avanti.lbl.task_perfScoreMR":
                if (rOrdTaskQty.ordrevdstqty_omrk_perf != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_perf;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_perf != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_perf;
                    nActualCost = rPress.mpress_perfscoremr_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_perfscoremr_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_perfscoremr_price_x = nCurrentOperationTotal;
                rPress.mpress_perfscoremr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_perfscoremr_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_perfscoremr_price;
                rPress.mpress_mrkup_perf = nCostMarkup;
                rPress.mpress_prule_perfscoremr_price_x = rPress.mpress_perfscoremr_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;

            case "i18n:avanti.lbl.task_plating":
                break;

            case "i18n:avanti.lbl.task_repeatMR":
                if (rOrdTaskQty.ordrevdstqty_omrk_addmr != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_addmr;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_addmr != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_addmr;
                    nActualCost = rPress.mpress_addmr_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_addmr_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_addmr_price_x = nCurrentOperationTotal;
                rPress.mpress_addmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_addmr_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_addmr_price;
                rPress.mpress_mrkup_addmr = nCostMarkup;
                rPress.mpress_prule_addmr_price_x = rPress.mpress_addmr_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;

            case "i18n:avanti.lbl.task_run":
                if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.PressRun && rPress.sa_order_revds_press_to_sa_task.tasktype_id != TASKTYPEID.GrandFormatPress) {
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_run_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
                    if (rOrdTaskQty.ordrevdstqty_omrk_run != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_run;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_run != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_run_x + rOrdTaskQty.ordrevdstqty_adj_run;
                        rOrdTaskQty.ordrevdstqty_oprice_run = nOverridePrice;
                    }
                    nActualCost = rPress.mpress_run_cost;
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_run_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rPress.mpress_run_price_x = nCurrentOperationTotal;
                    rPress.mpress_run_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_run_price_x, null, rOrdTaskQty), 2);
                    nTotal_x += rPress.mpress_run_price;
                    rOrdTaskQty.ordrevdstqty_price_run_x = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_price_run = rPress.mpress_run_price;
                    rPress.mpress_mrkup_run = nCostMarkup;
                    rPress.mpress_prule_run_price_x = rPress.mpress_run_price_x;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rPress.mpress_run_price_x;
                }
                else {
                    rOrdTaskQty.ordrevdstqty_lprice_run_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_run, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
                    if (rOrdTaskQty.ordrevdstqty_omrk_run != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_run;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_run != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_run_x + rOrdTaskQty.ordrevdstqty_adj_run;
                        rOrdTaskQty.ordrevdstqty_oprice_run = nOverridePrice;
                    }
                    nActualCost = rOrdTaskQty.ordrevdstqty_cost_run;
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_run, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_run_x = nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_run = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_run_x, null, rOrdTaskQty), 2);
                    nTotal_x += rOrdTaskQty.ordrevdstqty_price_run;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                    rOrdTaskQty.ordrevdstqty_mrk_run = nCostMarkup;
                    rOrdTaskQty.ordrevdstqty_mrk_run_list = rOrdTaskQty.ordrevdstqty_mrk_run;
                    rOrdTaskQty.ordrevdstqty_prule_run_x = rOrdTaskQty.ordrevdstqty_price_run_x;
                }
                break;

            case "i18n:avanti.lbl.task_setup":
                if (rPress && utils.hasRecords(rPress.sa_order_revds_press_to_sa_task) && rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.GrandFormatPress) {
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_setup_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);

                    if (rOrdTaskQty.ordrevdstqty_omrk_setup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_setup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_setup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_setup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_setup = nOverridePrice;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_omrk_presssetup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_presssetup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_presssetup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_setup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_presssetup = nOverridePrice;
                    }
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_setup_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rPress.mpress_setup_price_x = nCurrentOperationTotal;
                    rPress.mpress_setup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_setup_price_x, null, rOrdTaskQty), 2);
                    nTotal_x += rPress.mpress_setup_price;
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_setup_price;
                    rOrdTaskQty.ordrevdstqty_price_setup_x = rPress.mpress_setup_price_x;
                    rPress.mpress_mrkup_setup = nCostMarkup;
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_setup_price_x;
                }
                else {
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, nOCostMarkup, nCustDiscount).nRevisedCost, 2);

                    if (rOrdTaskQty.ordrevdstqty_omrk_presssetup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_presssetup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_presssetup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rOrdTaskQty.ordrevdstqty_cost_setup;
                        rOrdTaskQty.ordrevdstqty_oprice_presssetup = nOverridePrice;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_omrk_setup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_setup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_setup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rOrdTaskQty.ordrevdstqty_cost_setup;
                        rOrdTaskQty.ordrevdstqty_oprice_setup = nOverridePrice;
                    }
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_setup_x = nCurrentOperationTotal;
                    rOrdTaskQty.ordrevdstqty_price_setup = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty), 2);
                    nTotal_x += rOrdTaskQty.ordrevdstqty_price_setup;
                    rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
                    rOrdTaskQty.ordrevdstqty_mrk_setup = nCostMarkup;
                    rOrdTaskQty.ordrevdstqty_mrk_setup_list = rOrdTaskQty.ordrevdstqty_mrk_setup;
                    rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
                }
                break;

            case "i18n:avanti.lbl.task_setupPress":
                if (rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.DigitalSheetPress || rPress.sa_order_revds_press_to_sa_task.tasktype_id == TASKTYPEID.DigitalRollPress) {
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_setup_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
                    if (rOrdTaskQty.ordrevdstqty_omrk_setup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_setup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_setup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_setup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_setup = nOverridePrice;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_omrk_presssetup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_presssetup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_presssetup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_setup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_presssetup = nOverridePrice;
                    }
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_setup_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rPress.mpress_setup_price_x = nCurrentOperationTotal;
                    rPress.mpress_setup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_setup_price_x, null, rOrdTaskQty), 2);
                    nTotal_x += rPress.mpress_setup_price;
                    rOrdTaskQty.ordrevdstqty_price_setup_x = rPress.mpress_setup_price_x;
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_setup_price;
                    rPress.mpress_mrkup_setup = nCostMarkup;
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_setup_price_x;
                }
                else {
                    rOrdTaskQty.ordrevdstqty_lprice_setup_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_presssetup_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
                    if (rOrdTaskQty.ordrevdstqty_omrk_presssetup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_presssetup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_presssetup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_presssetup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_presssetup = nOverridePrice;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_omrk_setup != null) {
                        nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_setup;
                    }
                    else if (rOrdTaskQty.ordrevdstqty_oprice_setup != null) {
                        nOverridePrice = rOrdTaskQty.ordrevdstqty_lprice_setup_x + rOrdTaskQty.ordrevdstqty_adj_setup;
                        nActualCost = rPress.mpress_presssetup_cost;
                        rOrdTaskQty.ordrevdstqty_oprice_setup = nOverridePrice;
                    }
                    nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_presssetup_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                    nTotal += nCurrentOperationTotal;
                    rPress.mpress_presssetup_price_x = nCurrentOperationTotal;
                    rPress.mpress_presssetup_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_presssetup_price_x, null, rOrdTaskQty), 2);
                    nTotal_x += rPress.mpress_presssetup_price;
                    rOrdTaskQty.ordrevdstqty_price_setup_x = rPress.mpress_presssetup_price_x;
                    rOrdTaskQty.ordrevdstqty_price_setup = rPress.mpress_presssetup_price;
                    rPress.mpress_mrkup_presssetup = nCostMarkup;
                    rPress.mpress_prule_presssetup_price_x = rPress.mpress_presssetup_price_x;
                }
                break;

            case "i18n:avanti.lbl.task_washup":
                if (rOrdTaskQty.ordrevdstqty_omrk_wash != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_wash;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_wash != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_wash;
                    nActualCost = rPress.mpress_wash_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_wash_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_wash_price_x = nCurrentOperationTotal;
                rPress.mpress_wash_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_wash_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_wash_price;
                rPress.mpress_mrkup_wash = nCostMarkup;
                rPress.mpress_prule_wash_price_x = rPress.mpress_wash_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;

            case "i18n:avanti.lbl.task_workTurnMR":
                if (rOrdTaskQty.ordrevdstqty_omrk_wtmr != null) {
                    nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_wtmr;
                }
                else if (rOrdTaskQty.ordrevdstqty_oprice_wtmr != null) {
                    nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_wtmr;
                    nActualCost = rPress.mpress_wtmr_cost;
                }

                nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_wtmr_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
                nTotal += nCurrentOperationTotal;
                rPress.mpress_wtmr_price_x = nCurrentOperationTotal;
                rPress.mpress_wtmr_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_wtmr_price_x, null, rOrdTaskQty), 2);
                nTotal_x += rPress.mpress_wtmr_price;
                rPress.mpress_mrkup_wtmr = nCostMarkup;
                rPress.mpress_prule_wtmr_price_x = rPress.mpress_wtmr_price_x;
                rOrdTaskQty.ordrevdstqty_price_other_x += nCurrentOperationTotal;
                break;
        }
        
    }

    if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.PressRun && rPress && rPress.mpress_click_price_x > 0) { //Clicks
        clearOverrideValues();
        if (rOrdTaskQty.ordrevdstqty_omrk_click != null) {
            nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_click;
        }
        else if (rOrdTaskQty.ordrevdstqty_click_price_over != null) {
            nOverridePrice = rOrdTaskQty.ordrevdstqty_click_price_over;
            nActualCost = rPress.mpress_click_cost;
        }
        
        if (rPress.mpress_click_cost_colr > 0
                && rOrdTaskQty.ordrevdstqty_click_price_o_clr != null) {
                
            nOverrideMrk = rPress.mpress_click_mrkup_colr * 100;
        }
        
        rPress.mpress_prule_click_price_x = 0;

        nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
        rPress.mpress_click_price_x = nCurrentOperationTotal;
        rOrdTaskQty.ordrevdstqty_click_lprice_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
        rPress.mpress_click_price = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_click_price_x, null, rOrdTaskQty), 2);
        rPress.mpress_click_mrkup = nCostMarkupd;
        if (rPress.mpress_click_cost_colr == 0 && rPress.mpress_click_cost_bw == 0) {
            rPress.mpress_prule_click_price_x = rPress.mpress_click_price_x;
        }
        rOrdTaskQty.ordrevdstqty_click_price_x = rPress.mpress_click_price_x;
        rOrdTaskQty.ordrevdstqty_click_price = rPress.mpress_click_price;
        
        if (rPress.mpress_click_cost_bw > 0) {
            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost_bw, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            rPress.mpress_click_price_x_bw = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_click_lprice_x_bw = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost_bw, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
            rPress.mpress_click_price_bw = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_click_price_x_bw, null, rOrdTaskQty), 2);
            rPress.mpress_click_mrkup_bw = nCostMarkupd;
            rPress.mpress_prule_click_price_x += rPress.mpress_click_price_x_bw;
            rOrdTaskQty.ordrevdstqty_click_price_x_bw = rPress.mpress_click_price_x_bw;
            rOrdTaskQty.ordrevdstqty_click_price_bw = rPress.mpress_click_price_bw;
        }
        
        if (rPress.mpress_click_cost_colr > 0) {
            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost_colr, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            rPress.mpress_click_price_x_colr = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_click_lprice_x_cl = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rPress.mpress_click_cost_colr, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
            rPress.mpress_click_price_colr = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rPress.mpress_click_price_x_colr, null, rOrdTaskQty), 2);
            rPress.mpress_click_mrkup_colr = nCostMarkupd;
            rPress.mpress_prule_click_price_x += rPress.mpress_click_price_x_colr;
            rOrdTaskQty.ordrevdstqty_click_price_x_clr = rPress.mpress_click_price_x_colr;
            rOrdTaskQty.ordrevdstqty_click_price_clr = rPress.mpress_click_price_x_colr;
        }

    }
    else {
        //Plating has no cost link/operations associated so it will not get markup from above for loop
        if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.Plating) {
            clearOverrideValues();
            if (rOrdTaskQty.ordrevdstqty_omrk_platemr != null) {
                nOverrideMrk = rOrdTaskQty.ordrevdstqty_omrk_platemr;
            }
            else if (rOrdTaskQty.ordrevdstqty_oprice_platemr != null) {
                nOverridePrice = rOrdTaskQty.ordrevdstqty_oprice_platemr;
                nActualCost = rOrdTaskQty.ordrevdstqty_cost_setup;
            }

            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            nTotal += nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_price_setup_x = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_price_setup = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_price_setup_x, null, rOrdTaskQty), 2);
            nTotal_x += rOrdTaskQty.ordrevdstqty_price_setup;
            rOrdTaskQty.ordrevdstqty_lprice_setup_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_cost_setup, nOCostMarkup, nCustDiscount).nRevisedCost, 2);

            rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
            rOrdTaskQty.ordrevdstqty_mrk_setup = nCostMarkup;
            rOrdTaskQty.ordrevdstqty_mrk_setup_list = rOrdTaskQty.ordrevdstqty_mrk_setup;
            rOrdTaskQty.ordrevdstqty_prule_setup_x = rOrdTaskQty.ordrevdstqty_price_setup_x;
        }

        if (rOrdTaskQty.ordrevdstqty_t_item_orig_x > 0) {
            clearOverrideValues();
            if (rOrdTaskQty.ordrevdstqty_mrk_pct_item_over != null) {
                nOverrideMrk = rOrdTaskQty.ordrevdstqty_mrk_pct_item_over;
            }
            else if (rOrdTaskQty.ordrevdstqty_t_item_over != null) {
                nOverridePrice = rOrdTaskQty.ordrevdstqty_t_item_over;
                nActualCost = rOrdTaskQty.ordrevdstqty_t_cost_item_orig;
            }

            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_t_item_orig_x = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_orig = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_orig_x, null, rOrdTaskQty), 2);
            rOrdTaskQty.ordrevdstqty_lprice_item1_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig = nCostMarkupd;
            rOrdTaskQty.ordrevdstqty_mrk_item1_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_orig;
            rOrdTaskQty.ordrevdstqty_prule_item_x = rOrdTaskQty.ordrevdstqty_t_item_orig_x;
        }

        if (rOrdTaskQty.ordrevdstqty_t_item_ori2_x > 0) {
            clearOverrideValues();
            if (rOrdTaskQty.ordrevdstqty_mrk_pct_item_ove2 != null) {
                nOverrideMrk = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ove2;
            }
            else if (rOrdTaskQty.ordrevdstqty_t_item_over2 != null) {
                nOverridePrice = rOrdTaskQty.ordrevdstqty_t_item_over2;
                nActualCost = rOrdTaskQty.ordrevdstqty_t_cost_item_orig2;
            }

            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig2, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_t_item_ori2_x = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori2 = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori2_x, null, rOrdTaskQty), 2);
            rOrdTaskQty.ordrevdstqty_lprice_item2_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig2, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori2 = nCostMarkupd;
            rOrdTaskQty.ordrevdstqty_mrk_item2_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori2;
            rOrdTaskQty.ordrevdstqty_prule_item2_x = rOrdTaskQty.ordrevdstqty_t_item_ori2_x;
        }

        if (rOrdTaskQty.ordrevdstqty_t_item_ori3_x > 0) {
            clearOverrideValues();
            if (rOrdTaskQty.ordrevdstqty_mrk_pct_item_ove3 != null) {
                nOverrideMrk = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ove3;
            }
            else if (rOrdTaskQty.ordrevdstqty_t_item_over3 != null) {
                nOverridePrice = rOrdTaskQty.ordrevdstqty_t_item_over3;
                nActualCost = rOrdTaskQty.ordrevdstqty_t_cost_item_orig3;
            }

            nCurrentOperationTotal = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig3, getOverrideMrkup(), nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_t_item_ori3_x = nCurrentOperationTotal;
            rOrdTaskQty.ordrevdstqty_t_item_ori3 = scopes.avUtils.roundNumber(scopes.avDetail.applyDetailDiscount(rOrdTaskQty.ordrevdstqty_t_item_ori3_x, null, rOrdTaskQty), 2);
            rOrdTaskQty.ordrevdstqty_lprice_item3_x = scopes.avUtils.roundNumber(scopes.avCost.getMarkupCalculation(rOrdTaskQty.ordrevdstqty_t_cost_item_orig3, nOCostMarkup, nCustDiscount).nRevisedCost, 2);
            rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori3 = nCostMarkupd;
            rOrdTaskQty.ordrevdstqty_mrk_item3_list = rOrdTaskQty.ordrevdstqty_mrk_pct_item_ori3;
            rOrdTaskQty.ordrevdstqty_prule_item3_x = rOrdTaskQty.ordrevdstqty_t_item_ori3_x;
        }
    }

    rOrdTaskQty.ordrevdstqty_t_lab_orig_x = nTotal;
    rOrdTaskQty.ordrevdstqty_t_lab_orig = nTotal_x;

    function getOverrideMrkup() {
        if (nOverrideMrk != null) {
            nCostMarkup = nOverrideMrk;
        }
        else if (nOverridePrice != null && nActualCost != 0) {
            nCostMarkup = ( nOverridePrice - nActualCost ) / nActualCost * 100;
        }
        else {
            nCostMarkup = nOCostMarkup;
        }
        nCostMarkupd = nCostMarkup / 100;
        return nCostMarkup;
    }

    function clearOverrideValues() {
        nOverridePrice = null;
        nActualCost = null;
        nOverrideMrk = null;
    }

    return true;

}

/**
 * Check if a task has overrides
 *
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty - Task Quantity Record
 * @param {JSRecord<db:/avanti/sa_order_revds_press>} rPress - Press Record
 *
 * @returns {Boolean} 
 *
 * @properties={typeid:24,uuid:"BE8E48C4-551F-4175-85AB-75D6F07A76D3"}
 */
function checkForTaskOverrides (rTaskQty, rPress) {
	
	var bReturn = false;
	
	if (!rTaskQty && 
		!utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task) &&
		!utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task)) {
		return false;
	}
	
	var rTask = rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.getRecord(1);
	
	var nTaskTypeID = rTask.tasktype_id;
	
	if (rTask.tasktype_id == 99){
		if (rPress){
			nTaskTypeID = rPress.sa_order_revds_press_to_sa_task.tasktype_id;
		}
		else{
			return false;
		}
	}
	
	/**@type {JSFoundset<db:/avanti/app_task_operation>} */
	var fsAppTaskOperation = scopes.avDB.getFS("app_task_operation",["tasktype_id"],[nTaskTypeID],"sequence_nr",null,true);
	
	for ( var i = 1; i <= fsAppTaskOperation.getSize(); i++ ) {
		var rAppTaskOperation = fsAppTaskOperation.getRecord(i);
		
		if (bReturn) break;
		
		switch ( rAppTaskOperation.taskoper_key) {
			
			case "i18n:avanti.lbl.helper":
				if (rTaskQty.ordrevdstqty_omrk_help != null || rTaskQty.ordrevdstqty_oprice_help != null) bReturn = true;
				break;
						
			case "i18n:avanti.lbl.internalLabor":
				if (rTaskQty.ordrevdstqty_omrk_intlabor != null || rTaskQty.ordrevdstqty_oprice_intlabor != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_clickCharge":
				if (rTaskQty.ordrevdstqty_omrk_click != null || rTaskQty.ordrevdstqty_click_price_over != null) bReturn = true;
				break;
			
			case "i18n:avanti.lbl.task_extraPlMR":
				if (rTaskQty.ordrevdstqty_omrk_explatemr != null || rTaskQty.ordrevdstqty_oprice_explatemr != null) bReturn = true;
				break;
			
			case "i18n:avanti.lbl.task_firstMR":
				if (rTaskQty.ordrevdstqty_omrk_firstmr != null || rTaskQty.ordrevdstqty_oprice_firstmr != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_hangDie":
				if (rTaskQty.ordrevdstqty_omrk_hangdie != null || rTaskQty.ordrevdstqty_oprice_hangdie != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_ink":
				if (rTaskQty.ordrevdstqty_omrk_ink != null || rTaskQty.ordrevdstqty_oprice_ink != null) bReturn = true;
				break;
			
			case "i18n:avanti.lbl.task_perfScoreMR":
				if (rTaskQty.ordrevdstqty_omrk_perf != null || rTaskQty.ordrevdstqty_oprice_perf != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_plating":
				if (rTaskQty.ordrevdstqty_omrk_platemr != null || rTaskQty.ordrevdstqty_oprice_platemr != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_repeatMR":
				if (rTaskQty.ordrevdstqty_omrk_addmr != null || rTaskQty.ordrevdstqty_oprice_addmr != null)  bReturn = true
				break;
				
			case "i18n:avanti.lbl.task_run":
				if (rTaskQty.ordrevdstqty_oprice_run != null || rTaskQty.ordrevdstqty_omrk_run != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_setup":
				if (rTaskQty.ordrevdstqty_oprice_setup != null || rTaskQty.ordrevdstqty_omrk_setup != null) bReturn = true;
				break;
			
			case "i18n:avanti.lbl.task_setupPress":
				if (rTaskQty.ordrevdstqty_omrk_setup != null ||
					rTaskQty.ordrevdstqty_oprice_setup != null ||
					rTaskQty.ordrevdstqty_omrk_presssetup != null ||
					rTaskQty.ordrevdstqty_oprice_presssetup != null) bReturn = true
					break;
				
			case "i18n:avanti.lbl.task_washup":
				if (rTaskQty.ordrevdstqty_omrk_wash != null || rTaskQty.ordrevdstqty_oprice_wash != null) bReturn = true;
				break;
				
			case "i18n:avanti.lbl.task_workTurnMR":
				if (rTaskQty.ordrevdstqty_omrk_wtmr != null || rTaskQty.ordrevdstqty_oprice_wtmr != null) bReturn = true;
				break;
		}
	}
	
	//Materials
	if (rTaskQty.ordrevdstqty_t_item_over != null || rTaskQty.ordrevdstqty_mrk_pct_item_over != null ||
		rTaskQty.ordrevdstqty_t_item_over2 != null || rTaskQty.ordrevdstqty_mrk_pct_item_ove2 != null || 
		rTaskQty.ordrevdstqty_t_item_over3 != null || rTaskQty.ordrevdstqty_mrk_pct_item_ove3 != null) bReturn = true;
	
	return bReturn;
}

/** Stitching and Stapling Bindery Types have the exact same functionality - so we should always call this function rather than 
 * refrerring to a Stitching or Stapling Bindery Type
 * 
 * @public 
 * 
 * @param {String} sBinderyType
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"5458737F-9740-4043-8DC8-E2A6BC86C37F"}
 */
function isStitchingStaplingBinderyType(sBinderyType){
	return sBinderyType === 'S' || sBinderyType === 'T';
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rPressTask
 * @param {String} sCutterType - The type of cutting task (PT, FT, IC)
 * 
 * @return {JSRecord<db:/avanti/sa_task>}
 *
 * @properties={typeid:24,uuid:"295D125C-BFD4-491E-AD65-FDE722C7DC76"}
 */
function getPressCutter(rPressTask, sCutterType) {
    /**@type {JSRecord<db:/avanti/sa_task>} */
    var rCutter = null;

    if (rPressTask && utils.hasRecords(rPressTask.sa_task_to_sa_task_standard)) {
        var rStd = rPressTask.sa_task_to_sa_task_standard.getRecord(1);

        if (sCutterType == CUTTER_TYPES.PreTrim && rStd.taskstd_pre_trim_task_id) {
            rCutter = scopes.avDB.getRec('sa_task', ['task_id'], [rStd.taskstd_pre_trim_task_id]);
        }
        else if ( (sCutterType == CUTTER_TYPES.FinalTrim || sCutterType == CUTTER_TYPES.InterimCutter || sCutterType == CUTTER_TYPES.SignatureCutter) 
        		&& rStd.taskstd_final_trim_task_id) {
            rCutter = scopes.avDB.getRec('sa_task', ['task_id'], [rStd.taskstd_final_trim_task_id]);
        }
    }

    return rCutter;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rCutter
 * @param {Number} nDim1
 * @param {Number} nDim2
 * @param {Number} nCuts
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"48B27F4D-85F0-4654-B0B9-C0463439809A"}
 */
function isCutterValid(rCutter, nDim1, nDim2, nCuts){
	var bCutterValid = false;
	
	if (utils.hasRecords(rCutter.sa_task_to_sa_task_standard)) {
		var rStd = rCutter.sa_task_to_sa_task_standard.getRecord(1);
		// extracted this logic from avCalcs_task_getCutter
		var bPaperFitsThisWay = rStd.taskstd_min_paper_width <= nDim1 && rStd.taskstd_min_paper_heigth <= nDim2 
				&& rStd.taskstd_max_paper_width >= nDim1 && rStd.taskstd_max_paper_height >= nDim2; 
		var bPaperFitsThatWay = rStd.taskstd_min_paper_width <= nDim2 && rStd.taskstd_min_paper_heigth <= nDim1 
				&& rStd.taskstd_max_paper_width >= nDim2 && rStd.taskstd_max_paper_height >= nDim1; 
		var bFilterByPlant = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter) === 1 
				&& globals.avBase_plantIDTemp && globals.avBase_plantIDTemp != 'ALL';
		var bTaskUsesPlant = !bFilterByPlant ||	scopes.avSystem.taskUsesPlant(rCutter.task_id, globals.avBase_plantIDTemp);
		
		if (rCutter.task_active && bTaskUsesPlant && (bPaperFitsThisWay || bPaperFitsThatWay)){
			// Now check that we have the right number of cuts available on this cutter
			for (var k = 1; k <= rCutter.sa_task_to_sa_task_machine.getSize(); k++){
				var rMachineVar = rCutter.sa_task_to_sa_task_machine.getRecord(k);
				
				// sl-2916 - added '== null' condition
				if (rMachineVar.taskmachine_from <= nCuts && (rMachineVar.taskmachine_to >= nCuts || rMachineVar.taskmachine_to == null)){
					bCutterValid = true;
					break;
				}
			}
		}
	}
	
	return bCutterValid;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_model>} rModel - The Model being used
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection - The Section
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} [rCutter] - If we already have a cutter, then use it
 * @param {Boolean} [bIsFinalTrimTask]
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"773C045F-0914-4FFE-AA5E-234887CDA6D2"}
 */
function getNumFinalTrimCuts(rModel, rSection, rCutter, bIsFinalTrimTask){
	// THIS CODE EXTRACTED FROM avCalcs_section_setTaskCutter_finalTrim()
	
	var nCuts = 0;
	var sQtyUUID = globals.avSales_selectedRevisionDetailQtyUUID;
	var nSectionKeyIndex = scopes.avSection.getSectionOverrideKey(rSection);
	
	// GD - May 7, 2014: Cutting overrides
	if (bIsFinalTrimTask && scopes.avPress.oOver 
			&& scopes.avPress.oOver['"' + rSection.ordrevds_pages + '|' + nSectionKeyIndex + '"']
			&& scopes.avPress.oOver['"' + rSection.ordrevds_pages + '|' + nSectionKeyIndex + '"']['"' + sQtyUUID + '"']
			&& scopes.avPress.oOver['"' + rSection.ordrevds_pages + '|' + nSectionKeyIndex + '"']['"' + sQtyUUID + '"'].cutterFT
			&& scopes.avPress.oOver['"' + rSection.ordrevds_pages + '|' + nSectionKeyIndex + '"']['"' + sQtyUUID + '"'].cutterFT.ordrevdstqty_qty_over != null){
			
		nCuts = scopes.avPress.oOver['"' + rSection.ordrevds_pages + '|' + nSectionKeyIndex + '"']['"' + sQtyUUID + '"'].cutterFT.ordrevdstqty_qty_over;
	} 
	else if (rCutter 
			&& utils.hasRecords(rCutter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)
			&& rCutter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_qty_over) {
		
		// GD - May 26, 2015: SL-5225 - Need to also check the cuts override field
		nCuts = rCutter.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_qty_over;
	}
	// sl-10762 - if using staggered layout then logic below is not sufficient to calc the num cuts. a staggered layout is essentially 2 sigs.
	// getNumSigCuts() will calc the correct num cuts.
	// sl-14328 - added ordrevdsmodel_up_dimX conditions. if they have staggered layout option selected,
	// but they didnt actually use it (ie. didnt use mixed grain), then we need to calc ft cuts normally  
	else if (rModel && rSection.ordrevds_allow_stggrd_layouts && rModel.ordrevdsmodel_up_dim3 && rModel.ordrevdsmodel_up_dim4) {
		nCuts = scopes.avSection.getNumSigCuts(rSection, rModel);
	}
	else if(rModel){
		var upDim1 = rModel.ordrevdsmodel_up_dim1;
		var upDim2 = rModel.ordrevdsmodel_up_dim2;
		var rPrecut = scopes.avSection.getPreCut(rSection);
		
		if (!upDim1) {
			upDim1 = 1;
		}
		if (!upDim2) {
			upDim2 = 1;
		}
		
		if(rPrecut){			
			// HP - sl-3678 - have to compare to opposite dim if sheet had been rotated
			if(globals["hasSheetBeenRotated"](rSection)){
				if(upDim1 > 1 && rPrecut.taskprecut_num_width_cuts && upDim1 % (rPrecut.taskprecut_num_width_cuts+1) == 0){
					upDim1 = upDim1 / (rPrecut.taskprecut_num_width_cuts+1)
				}
				if(upDim2 > 1 && rPrecut.taskprecut_num_length_cuts && upDim2 % (rPrecut.taskprecut_num_length_cuts+1) == 0){
					upDim2 = upDim2 / (rPrecut.taskprecut_num_length_cuts+1)
				}
			}
			else{
				if(upDim1 > 1 && rPrecut.taskprecut_num_length_cuts && upDim1 % (rPrecut.taskprecut_num_length_cuts+1) == 0){
					upDim1 = upDim1 / (rPrecut.taskprecut_num_length_cuts+1)
				}
				if(upDim2 > 1 && rPrecut.taskprecut_num_width_cuts && upDim2 % (rPrecut.taskprecut_num_width_cuts+1) == 0){
					upDim2 = upDim2 / (rPrecut.taskprecut_num_width_cuts+1)
				}
			}
		}
		
		// cuts width
		var cuts_per_page = rSection.ordrevds_bleed_width ? 2 : 1		
		var cuts_width = (upDim1 - 1) * cuts_per_page 	
	
		// cuts length
		cuts_per_page = rSection.ordrevds_bleed_length ? 2 : 1		
		var cuts_length = (upDim2 - 1) * cuts_per_page 	
		
		// cut waste - assumes image is centered on sheet, unless using flatwork and some sides have bleeds and some dont, and the bleeds max out their dims such that the non bleed 
		// sides are pushed to the sides of the sheet, in which case the num waste cuts could be 1 in a dim, instead of 2 
		var wasteCuts = 0
		
		if (rSection.ordrevds_work_type == "F"){
			// dim 1
			if (rModel.ordrevdsmodel_sheet_usage_dim1 < 1) { // ordrevdsmodel_sheet_usage_dim1 includes bleeds, so if less that 100% we dont care about bleeds, just do 2 wasteCuts
				wasteCuts += 2
			}	
			// if ordrevdsmodel_sheet_usage_dim1 == 1 we still need to do a waste cut if there are bleeds in that dim
			else{
				if (globals.avBase_getSystemPreference_Number(139) === 1) {
					if(rSection.ordrevds_bleed_top > 0) wasteCuts++;
					if(rSection.ordrevds_bleed_bottom > 0) wasteCuts++;
				}
				else{
					if(rSection.ordrevds_bleed_left > 0) wasteCuts++;
					if(rSection.ordrevds_bleed_right > 0) wasteCuts++;
				}
			}
			
			// dim 2
			if (rModel.ordrevdsmodel_sheet_usage_dim2 < 1) { // ordrevdsmodel_sheet_usage_dim1 includes bleeds, so if less that 100% we dont care about bleeds, just do 2 wasteCuts
				wasteCuts += 2
			}	
			// if ordrevdsmodel_sheet_usage_dim1 == 1 we still need to do a waste cut if there are bleeds in that dim
			else{
				if (globals.avBase_getSystemPreference_Number(139) === 1) {
					if(rSection.ordrevds_bleed_left > 0) wasteCuts++;
					if(rSection.ordrevds_bleed_right > 0) wasteCuts++;
				}
				else{
					if(rSection.ordrevds_bleed_top > 0) wasteCuts++;
					if(rSection.ordrevds_bleed_bottom > 0) wasteCuts++;
				}
			}
		}
		else{
			// if ordrevdsmodel_sheet_usage_dim1 == 1 we still need to do a waste cut if there are bleeds in that dim
			if (rModel.ordrevdsmodel_sheet_usage_dim1 < 1 || rSection.ordrevds_bleed_width * rSection.ordrevds_bleed_size > 0) {
				wasteCuts += 2
			}	
			if (rModel.ordrevdsmodel_sheet_usage_dim2 < 1 || rSection.ordrevds_bleed_length * rSection.ordrevds_bleed_size > 0) {
				wasteCuts += 2
			}	
		}
		
		nCuts = cuts_width + cuts_length + wasteCuts
	}
	
	return nCuts;
}

/**
 * @public 
 * 
 * @param {String|UUID} sPressTaskID
 * @param {Number} nDim1 - The first dimension of the paper selected
 * @param {Number} nDim2 - The second dimension of the paper selected
 * @param {Number} nCuts - The number of cuts
 *
 * @properties={typeid:24,uuid:"EE42A5C3-B8DE-4EEE-8E90-DF94848C7513"}
 */
function addPressFTCutterInputs(sPressTaskID, nDim1, nDim2, nCuts){
	// add Cutter Inputs to array
	/**@type {{nDim1:Number, nDim2:Number, nCuts:Number}} */
	var oCutterInputs = {};
	
	oCutterInputs.nDim1 = nDim1;
	oCutterInputs.nDim2 = nDim2;
	oCutterInputs.nCuts = nCuts;
	
	aPressFTCutterInputs[sPressTaskID] = oCutterInputs;
}

/**
 * @public 
 * 
 * @param {{nDim1:Number, nDim2:Number, nCuts:Number}} oCutterInputs
 *
 * @properties={typeid:24,uuid:"12A86397-DCAE-419D-A17A-1A6C574DBD81"}
 */
function addQtyFTCutterInputs(oCutterInputs){
	aQtyFTCutterInputs.push(oCutterInputs);
}

/**
 * @public 
 * 
 * @param {String|UUID} sPressTaskID
 * 
 * @return {{nDim1:Number, nDim2:Number, nCuts:Number}}
 *
 * @properties={typeid:24,uuid:"AD271325-D42F-48F3-A7E9-C425486957F9"}
 */
function getPressFTCutterInputs(sPressTaskID){
	return aPressFTCutterInputs[sPressTaskID];
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rCutter
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E8211B13-AAC6-4F3F-A90E-D71C85ECD401"}
 */
function isFTCutterValidForAllQtys(rCutter){
	for(var i=0; i<aQtyFTCutterInputs.length; i++){
		/**@type {{nDim1:Number, nDim2:Number, nCuts:Number}} */
		var oCutterInputs = aQtyFTCutterInputs[i];
		
		if(!isCutterValid(rCutter, oCutterInputs.nDim1, oCutterInputs.nDim2, oCutterInputs.nCuts)){
			return false;
		}
	}
	
	return true;
}

/**
 * @public 
 * 
 * @param {String|UUID} sPressTaskID
 * @param {Number} nDim1
 * @param {Number} nDim2
 * @param {Number} nCuts
 *
 * @properties={typeid:24,uuid:"F56D3BE5-B81B-4906-8A57-0139A7CD6A26"}
 */
function addPressPTCutterInputs(sPressTaskID, nDim1, nDim2, nCuts){
	// add Cutter Inputs to array
	/**@type {{nDim1:Number, nDim2:Number, nCuts:Number}} */
	var oCutterInputs = {};
	
	oCutterInputs.nDim1 = nDim1;
	oCutterInputs.nDim2 = nDim2;
	oCutterInputs.nCuts = nCuts;
	
	aPressPTCutterInputs[sPressTaskID] = oCutterInputs;
}

/**
 * @public 
 * 
 * @param {{nDim1:Number, nDim2:Number, nCuts:Number}} oCutterInputs
 *
 * @properties={typeid:24,uuid:"ECFE2193-DD6C-47D8-95FC-4DEED174E781"}
 */
function addQtyPTCutterInputs(oCutterInputs){
	aQtyPTCutterInputs.push(oCutterInputs);
}

/**
 * @public 
 * 
 * @param {String|UUID} sPressTaskID
 * 
 * @return {{nDim1:Number, nDim2:Number, nCuts:Number}}
 *
 * @properties={typeid:24,uuid:"E67A211F-5E41-4D95-BADE-C5EDD331FE12"}
 */
function getPressPTCutterInputs(sPressTaskID){
	return aPressPTCutterInputs[sPressTaskID];
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rCutter
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"90987762-B31A-4683-B80F-078184CCE9C2"}
 */
function isPTCutterValidForAllQtys(rCutter){
	for(var i=0; i<aQtyPTCutterInputs.length; i++){
		/**@type {{nDim1:Number, nDim2:Number, nCuts:Number}} */
		var oCutterInputs = aQtyPTCutterInputs[i];
		
		if(!isCutterValid(rCutter, oCutterInputs.nDim1, oCutterInputs.nDim2, oCutterInputs.nCuts)){
			return false;
		}
	}
	
	return true;
}

/**
 * @public
 *  
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {String|UUID} [pDivID]
 * @param {String|UUID} [pPlantID]
 * 
 * @return {JSFoundset<db:/avanti/sa_task_cost_link>}
 *
 * @properties={typeid:24,uuid:"161AA03B-ECAE-4D28-ACA2-FEAE36914434"}
 */
function getTaskCostLinks(rTask, pDivID, pPlantID) {
	/**@type {JSFoundset<db:/avanti/sa_task_cost_link>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task_cost_link');

	if (globals.avBase_getSystemPreference_Number(79)) {
		// avBase_divIDTemp + avBase_plantIDTemp are set from est/ord plant when editing that est/ord
		var sDivID = pDivID ? pDivID : globals.avBase_divIDTemp;
		var sPlantID = pPlantID ? pPlantID : globals.avBase_plantIDTemp;

		if (sDivID) {
			if (sPlantID) {
				fs = getTaskCostLinksFor(rTask, 'plant', sDivID, sPlantID);
			}

			if (fs.getSize() == 0) {
				fs = getTaskCostLinksFor(rTask, 'div', sDivID);
			}
		}

		if (fs.getSize() == 0) {
			fs = getTaskCostLinksFor(rTask, 'task');
		}
	}
	else {
		// sl-28912 - this was using sa_task_to_sa_task_cost_link, but i changed it to get 'task' level cost links using the plant logic because some customers
		// had div/plant cost links even tho the plant filter was off. they must have had the filter on at one time then turned it off. so this will only pickup
		// cost links with no div/plant.
		fs = getTaskCostLinksFor(rTask, 'task');
	}

	return fs;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {String} sTaskDivPlant - 'task', 'div', 'plant'
 * @param {String|UUID} [sDivID]
 * @param {String|UUID} [sPlantID]
 * 
 * @return {JSFoundset<db:/avanti/sa_task_cost_link>}
 *
 * @properties={typeid:24,uuid:"B1A5B852-A81B-443B-9127-A933761EB613"}
 */
function getTaskCostLinksFor(rTask, sTaskDivPlant, sDivID, sPlantID){
	/**@type {JSFoundset<db:/avanti/sa_task_cost_link>} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_task_cost_link');
	var sql = "select taskcostlink_id from sa_task_cost_link where org_id = ? and task_id = ?  ";
	var args = [globals.org_id.toString(), rTask.task_id.toString()];

	if(sTaskDivPlant == 'plant'){
		sql += " and div_id = ? and plant_id = ?";
		args.push(sDivID.toString(), sPlantID.toString());
	}
	else if(sTaskDivPlant == 'div'){
		sql += " and div_id = ? and plant_id is null";
		args.push(sDivID.toString());
	}
	else if(sTaskDivPlant == 'task'){
		sql += " and div_id is null and plant_id is null";
	}
	else{
		return fs;
	}

	fs = scopes.avDB.getFSFromSQL(sql, 'sa_task_cost_link', args);
	return fs;
}

/**
 * @public 
 * 
 * @param {UUID|String} sCCID
 * 
 * @return {JSRecord<db:/avanti/sa_task>}
 *
 * @properties={typeid:24,uuid:"1CFE5FF9-BBF5-47B8-9989-08FBC0B6FFE5"}
 */
function getTaskThatUsesCC(sCCID) {
    var sSQL = "SELECT task_id FROM sa_task_cost_link WHERE cc_id = ?";
    var aArgs = [sCCID.toString()];

    /**@type {JSRecord<db:/avanti/sa_task>} */
    var rTask = scopes.avDB.getRecFromSQL(sSQL, 'sa_task', aArgs);

    return rTask;
}

/**
 * Returns the width and length of the substrate that will be laminated - not including the overlap.
 * This code was extracted from globals.avCalcs_task_getItemCost() so logic could be re-used in loading laminating task feededge vl.
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 * 
 * @return {{nWidth:Number, nLength:Number}}
 *
 * @properties={typeid:24,uuid:"A1463F48-AA5B-4308-B170-84C7F130C7CD"}
 */
function getLaminatingTaskSubstrateDims(rTask){
    var rSection = rTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
    var nWidth;
    var nLength;
    var iStart;
    var bFound = false;
    /**@type {{nWidth:Number, nLength:Number}} */
    var oReturn = {};
    
    // SHEETS
    if (rTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.taskstd_calc_type === "S") {
        if(utils.hasRecords(rTask.sa_order_revds_task_to_sa_order_revds_task_precut)){
            var rPreCut = rTask.sa_order_revds_task_to_sa_order_revds_task_precut.getRecord(1);
        }

        if (rPreCut) {
            // Check to see if a cut has been applied to this task, and if so, use these press sheet dimensions
            nWidth = rPreCut.taskprecut_width;
            nLength = scopes.avTask.getPreCutLength(rPreCut);
        }  
        else {
            var fsTasks = rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted;
            
            // We need to check up the list of tasks to see if a cut has been applied, and grab the sheet from there
            fsTasks.sort("sort_key asc");
            fsTasks.selectRecord(rTask.ordrevdstask_id);
            iStart = fsTasks.getSelectedIndex();
            
            if (iStart > 1){
                iStart -= 1;
            }

            for ( var i = iStart; i > 0; i-- ) {
                var rPriorTask = fsTasks.getRecord(i);

                if (rPreCut){
                    nWidth = rPreCut.taskprecut_width;
                    nLength = scopes.avTask.getPreCutLength(rPreCut);
                    bFound = true;
                    break;
                }
                
                if (rPriorTask.sa_order_revds_task_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.Cutting && rPriorTask.ordrevdstask_cut_type == "FT") {
                    // Final trim cutter, use the trim sizes
                    nWidth = parseFloat(rSection.ordrevds_trim_size_width);
                    nLength = parseFloat(rSection.ordrevds_trim_size_length);
                    bFound = true;
                    break;
                }
            }
            
            if (!bFound) {
                // If we never found a cut down sheet, then use the press sheet size
                var rPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1);

                if (rPress) {
                    if (utils.hasRecords(rPress.sa_order_revds_press_to_sa_task) 
                            && rPress.sa_order_revds_press_to_sa_task.tasktype_id === scopes.avTask.TASKTYPEID.WideFormatPress) {
                        // Wide format is still displaying it as one big press sheet (endless roll)
                        nWidth = parseFloat(rSection.ordrevds_trim_size_width);
                        nLength = parseFloat(rSection.ordrevds_trim_size_length);
                    } 
                    else {
                        nWidth = rPress.mpress_press_sheet_w;
                        nLength = rPress.mpress_press_sheet_l;
                    }
                }
            }
        }
    } 
    
    // PRODUCTION QTY
    else {
        // GD - Apr 10, 2018: SL-14477: Laminating needs to use the trim size, and not include bleeds, according to Randy & Jennifer
        nWidth = parseFloat(rSection.ordrevds_trim_size_width);
        nLength = parseFloat(rSection.ordrevds_trim_size_length);
    }
    
    oReturn.nWidth = nWidth;
    oReturn.nLength = nLength;
    
    return oReturn;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rFolder - The Folder being used for the Section
 * @param {JSRecord<db:/avanti/sa_order_revds_model>} rModel - The Model being used
 * 
 * @return {{nWidth:Number, nLength:Number}}
 *
 * @properties={typeid:24,uuid:"D13B2386-723F-4BDF-A191-74A04D9CF50C"}
 */
function getFolderMaterialDims(rFolder, rModel) {
    /**@type {{nWidth:Number, nLength:Number}} */
    var oDims = { };
    var nWidth = 0;
    var nLength = 0;
    var bleedSizeToExclude_w = 0;
    var bleedSizeToExclude_l = 0;
    var rSection = rFolder.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);

    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ExcludeBleedsFromFeedLength) == 1 && rModel) {
        if (rModel.ordrevdsmodel_grain_dir == 'S') {
            bleedSizeToExclude_w = rModel.ordrevdsmodel_bleed_ovr_trim_2;
            bleedSizeToExclude_l = rModel.ordrevdsmodel_bleed_ovr_trim_1;
        }
        else {
            bleedSizeToExclude_w = rModel.ordrevdsmodel_bleed_ovr_trim_1;
            bleedSizeToExclude_l = rModel.ordrevdsmodel_bleed_ovr_trim_2;
        }
    }

    // 1. Folding a signature
    if (utils.hasRecords(rFolder.sa_order_revds_task_to_sa_order_revds_signature)) {
        var rSig = rFolder.sa_order_revds_task_to_sa_order_revds_signature.getRecord(1);

        nWidth = rSig.ordrevdssig_flat_size_down - bleedSizeToExclude_w;
        nLength = rSig.ordrevdssig_flat_size_across - bleedSizeToExclude_l;
    }

    // 2. Folding a final trimmed item
    if (scopes.avSection.isTaskUpstream(rFolder, scopes.avTask.TASKTYPEID.Cutting, 'FT') || scopes.avSection.isInterimCutUpstream(rFolder)) {
    	// sl-18414 - if there isnt a flat over we need to get sizes from model not section, because some times when this is called the section
    	// flat sizes hold the last model processed, whereas rModel is the model that was chosen for the current press. section flat sizes could be 
    	// too big for folder, but model flat size will fit. section flat sizes will be correctly updated when a press and model are chosen.
		if (rModel && rModel.ordrevdsmodel_flat_w && rModel.ordrevdsmodel_flat_l && !rSection.ordrevds_over_flat_w && !rSection.ordrevds_over_flat_l) {
			nWidth = rModel.ordrevdsmodel_flat_w - bleedSizeToExclude_w;
			nLength = rModel.ordrevdsmodel_flat_l - bleedSizeToExclude_l;
		}
		else {
			nWidth = parseFloat(rSection.ordrevds_flat_size_width) - bleedSizeToExclude_w;
			nLength = parseFloat(rSection.ordrevds_flat_size_length) - bleedSizeToExclude_l;
		}
    }
    // 3. Folding an untrimmed press sheet
    else {
        if (rSection.ordrevds_over_press_sheet_size && rSection.ordrevds_over_press_sheet_size.toLowerCase().indexOf('x') > -1) {
            var sizes = rSection.ordrevds_over_press_sheet_size.toLowerCase().split('x');
            nWidth = parseFloat(sizes[0]);
            nLength = parseFloat(sizes[1]);
        }
        else if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)) {
            var rPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1);

            if (rPress.mpress_press_sheet_w && rPress.mpress_press_sheet_l) {
                nWidth = rPress.mpress_press_sheet_w;
                nLength = rPress.mpress_press_sheet_l;
            }
            else if (utils.hasRecords(rPress.sa_order_revds_press_to_in_item)) {
            	fallbackToItemDims();
            }
        }
        else if (scopes.avSection.isTaskUpstream(rFolder, scopes.avTask.TASKTYPEID.Cutting, 'PT')) {
            // GD - Oct. 22, 2021: SL-22660: Unique scenario where folder is being eliminated because it is not big enough for 
            // parent sheet that is being cut down by PT. There is no selected press yet, so we have to grab the trimmed sheet
            // size from the model record
            nWidth = rModel.ordrevdsmodel_sheet_dim1;
            nLength = rModel.ordrevdsmodel_sheet_dim2;
        }        
        else if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_in_item_paper)) {
        	fallbackToItemDims();
        }
    }

    oDims.nWidth = nWidth;
    oDims.nLength = nLength;

    return oDims;
    
    /**
     * If we cant find the Folder Material Dims anywhere else we have to fallback to the item dims
     */
    function fallbackToItemDims() {
        nWidth = rSection.sa_order_revision_detail_section_to_in_item_paper.paper_first_dim;

        // if it's a roll we can't use the roll length as we will never fold the whole roll length and avCalcs_section_setTaskFolder.folderMaxOK() 
        // will always fail. so use the model dim2 instead. its not exact, but we will be back here once a press is selected and the exact value will be used. 
		if (scopes.avInv.isRoll(null, rSection.ordrevds_over_paper_item_id)) {
            nLength = rModel.ordrevdsmodel_sheet_dim2;
		}
		else {
            nLength = rSection.sa_order_revision_detail_section_to_in_item_paper.paper_second_dim;
		}
    }
}

/**
 * Copies a value from the base qty
 *
 * <AUTHOR> Dotzlaw
 * @since Sep 12, 2018
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty
 * @param {String} sCopyFlagField - the field that is to be used to force the copy
 * @param {String} sField - the field to copy from the base qty
 * @returns {JSRecord<db:/avanti/sa_order_revds_task_qty>} returns the modified record or null
 * @public
 *
 * @properties={typeid:24,uuid:"A8728E7E-DA16-4FCE-8DD4-B7EC9991F76E"}
 */
function copyFromBaseTaskQty(rTaskQty, sCopyFlagField, sField) {
    
    if (!rTaskQty || !sField) {
        return null;
    }
    
    var fsTaskQtyToDetailQty = rTaskQty.sa_order_revds_task_qty_to_sa_order_revision_detail_qty,
        rDetailQtyBase = null,
        /** @type {JSRecord<db:/avanti/sa_order_revds_task_qty>} */ 
        rTaskQtyBase = null;
    
    // check to see if this is the base qty
    if (fsTaskQtyToDetailQty.ordrevdqty_is_base == 1) {
        return null;
    }
    
    if (utils.hasRecords(fsTaskQtyToDetailQty.sa_order_revision_detail_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base)) {
        
        rDetailQtyBase = fsTaskQtyToDetailQty.sa_order_revision_detail_qty_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_qty$is_base.getRecord(1);
        
        rTaskQtyBase = scopes.avDB.getRec("sa_order_revds_task_qty",["ordrevdstask_id", "ordrevdqty_id"],[rTaskQty.ordrevdstask_id, rDetailQtyBase.ordrevdqty_id])
            
        if (rTaskQtyBase && rTaskQtyBase.sa_order_revds_task_qty_to_sa_order_revds_task[sCopyFlagField] == 1) {

            rTaskQty[sField] = rTaskQtyBase[sField];
            rTaskQty[sField + "_over"] = rTaskQtyBase[sField];
            
            if (sField == "ordrevdstqty_qty_task") {
                
                rTaskQty.ordrevdstqty_qty_task_overuser = rTaskQtyBase[sField];
            }
            return rTaskQty;
        }
    }
    
    return null;   
}

/**
 * @AllowToRunInFind
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rTask - the task
 * @param {String} sKey - the key minus the i18n portion eg. "task_run"
 *
 * @returns {JSRecord<db:/avanti/sa_task_cost_link>} 
 *
 * @properties={typeid:24,uuid:"659060F1-BC52-4173-9FF7-DE487E408E1C"}
 */
function getCostLink(rTask, sKey) {
    if (rTask && sKey) {
        sKey = "i18n:avanti.lbl." + sKey;
        
        for (var i = 1; i <= rTask.sa_task_to_sa_task_cost_link.getSize(); i++) {
            var rCostLink = rTask.sa_task_to_sa_task_cost_link.getRecord(i);

            if (utils.hasRecords(rCostLink.sa_task_cost_link_to_app_task_operation) 
                    && rCostLink.sa_task_cost_link_to_app_task_operation.taskoper_key == sKey) {
                return rCostLink;
            }
        }
    }
    
    return null;
}

/**
 * Checks to see if the Hold for All Qtys flag should be showing
 *
 * <AUTHOR> Dotzlaw
 * @since Sep 15, 2018
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @returns {Boolean} 
 * @public
 *
 * @properties={typeid:24,uuid:"8A7BE64A-7424-4BA2-A9F7-FDEC63F62FB0"}
 */
function showHoldForAllTaskQtys(rTask) {
    
    if (!rTask) {
        return false;
    }
    
    var sCalcType = rTask.sa_task_to_sa_task_standard.taskstd_calc_type;
  
    if (sCalcType == TASK_CALC_TYPE.Forms 
        || sCalcType == TASK_CALC_TYPE.SingleUnit 
        || sCalcType == TASK_CALC_TYPE.Pages
        || sCalcType == TASK_CALC_TYPE.Default
        || sCalcType == null) {
                                    
        return true;
    } 
    else {
        return false;
    }
}

/**
 * Checks to see if a task exists in a section Section
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {Number} iTaskTypeId
 * @returns {Boolean}
 * @public
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"EDF7864D-96F4-4E65-A5F6-2FA40F197C79"}
 */
function checkTaskExists(rSection, iTaskTypeId) {

    var i = 0;

    if (scopes.avSales.oEstOrd) {
        
        var oEstOrdSection = scopes.avSales.oEstOrd.getSectionObject(rSection.ordrevds_id);

        for (i = 0; i < oEstOrdSection.aTasks; i++) {
            
            if (oEstOrdSection.aTasks[i].iTaskTypeId == iTaskTypeId) {
                return true;
            }
        }
    }
    return false;
}

/**
 * Sets the BL markup fields on task dialogs
 *
 * <AUTHOR> Dotzlaw
 * @since Feb 24, 2019
 * @param {String} sForm
 * @public
 *
 * @properties={typeid:24,uuid:"6CA56DE3-4D95-46E2-A540-14ED5F87224F"}
 */
function setTaskDialogBlMarkupFields(sForm) {
    
    var aFields = [
        "ordrevdstqty_bl_setup",
        "ordrevdstqty_bl_run",
        "ordrevdstqty_bl_help",
        "ordrevdstqty_bl_firstmr",
        "ordrevdstqty_bl_addmr",
        "ordrevdstqty_bl_explatemr",
        "ordrevdstqty_bl_wash",
        "ordrevdstqty_bl_perf",
        "ordrevdstqty_bl_wtmr",
        "ordrevdstqty_bl_hangdie",
        "ordrevdstqty_bl_plate",
        "ordrevdstqty_bl_explate",
        "ordrevdstqty_bl_intlabor",
        "ordrevdstqty_bl_item1",
        "ordrevdstqty_bl_item2",
        "ordrevdstqty_bl_item3",
        "ordrevdstqty_bl_clicks",
        "ordrevdstqty_bl_click_bw",
        "ordrevdstqty_bl_click_clr",
        "ordrevdstqty_bl_total",
        ],
        aElements = (sForm) ? forms[sForm].elements.allnames : [],
        iMax = 0,
        i = 0,
        bShowField = (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DistributeBLtoTasks) == 1) ? true : false,
        sFormat = (globals.avBase_currencyFormat_order != '') ? globals.avBase_currencyFormat_order : globals.avBase_currencyFormat;

    iMax = aFields.length;
    for (i = 0; i < iMax; i++) {
        
        if (aElements.indexOf(aFields[i]) > -1) {
            
            if (bShowField) {
                
                forms[sForm].elements[aFields[i]].visible = true;
                forms[sForm].elements[aFields[i]].format = sFormat;
            }
            else {
                
                forms[sForm].elements[aFields[i]].visible = false;
            }
        }    
    }
    if (forms[sForm].elements["lblBlMarkup"]) {
        if (bShowField) {
            forms[sForm].elements["lblBlMarkup"].visible = true;
        }
        else {
            forms[sForm].elements["lblBlMarkup"].visible = false;
        }  
    }
}

/**
 * @public 
 * 
 * @param {UUID} uTaskID
 * @param {Number} nNumCuts
 *
 * @return
 * @properties={typeid:24,uuid:"7D73DF5D-641A-4084-A279-20C6F3BE7306"}
 */
function getLiftsPerHourFromNumCuts(uTaskID, nNumCuts) {
    /**@type {Number} */
    var nLiftsPerHour = 0;
    
    if (uTaskID && nNumCuts) {
        var sSQL = "SELECT taskmachine_per_hour \
                    FROM sa_task_machine \
                    WHERE \
                        org_id = ? \
                        AND task_id = ? \
                        AND ? >= taskmachine_from \
                        AND (? <= taskmachine_to OR taskmachine_to IS NULL)";
        var aArgs = [globals.org_id, uTaskID.toString(), nNumCuts, nNumCuts];
        
        nLiftsPerHour = scopes.avDB.SQLQuery(sSQL, null, aArgs);
    }
    
    return nLiftsPerHour;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * 
 * @return {JSRecord<db:/avanti/sa_task_machine>}
 *
 * @properties={typeid:24,uuid:"618B8653-E5AA-4081-820F-C92AFC71D673"}
 */
function getSlowestTaskMachine(rTask) {
    var sSQL = "SELECT taskmachine_id \
                FROM sa_task_machine \
                WHERE \
                    org_id = ? \
                    AND task_id = ?";
    var aArgs = [globals.org_id, rTask.task_id.toString()]; 
    /**@type {JSRecord<db:/avanti/sa_task_machine>} */
    var rTaskMachine = scopes.avDB.getRecFromSQL(sSQL, 'sa_task_machine', aArgs, null, "taskmachine_per_hour asc");
    
    return rTaskMachine;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {String} sMachineName
 * 
 * @return {JSRecord<db:/avanti/sa_task_machine>}
 *
 * @properties={typeid:24,uuid:"D88B0E56-9BB7-4FF3-8E91-E124BB1092A1"}
 */
function getTaskMachineByName(rTask, sMachineName) {
	/**@type {JSRecord<db:/avanti/sa_task_machine>} */
	var rTaskMachine = null;
	
	if (sMachineName) {
	    var sSQL = "SELECT taskmachine_id \
		            FROM sa_task_machine \
		            WHERE \
		                org_id = ? \
		                AND task_id = ? \
		                AND taskmachine_description = ?";
		var aArgs = [globals.org_id, rTask.task_id.toString(), sMachineName];
		
		rTaskMachine = scopes.avDB.getRecFromSQL(sSQL, 'sa_task_machine', aArgs, null, "sequence_nr asc");
	}
    
    return rTaskMachine;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {String} sDifficulty
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"65BDBD85-F60E-4608-AF58-0F2F82697C51"}
 */
function getHighestTaskSetupTime(rTask, sDifficulty) {
    /**@type {Number} */
    var nSetupTime = 0;
    
	if (sDifficulty == 'E' || sDifficulty == 'A' || sDifficulty == 'D') {
	    var sReturnCol;

	    if (sDifficulty == 'E') {
	        sReturnCol = "tasksetup_easy";
	    }
	    else if (sDifficulty == 'A') {
	        sReturnCol = "tasksetup_average";
	    }
	    else if (sDifficulty == 'D') {
	        sReturnCol = "tasksetup_difficult";
	    }
	    
	    var sSQL = "SELECT MAX(" + sReturnCol + ") \
	                FROM sa_task_setup \
	                WHERE \
	                    org_id = ? \
	                    AND task_id = ?";
	    var aArgs = [globals.org_id, rTask.task_id.toString()];
	    
	    nSetupTime = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
    
    return nSetupTime;
}

/**
 * @public 
 * 
 * @param {Number} nDim1
 * @param {Number} nDim2
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"824BD3EA-5A19-4B38-AB18-D069084A661F"}
 */
function areThereCuttersThatFitSheet(nDim1, nDim2) {
    var sSQL = "SELECT COUNT(t.task_id) \
                FROM sa_task t \
                INNER JOIN sa_task_standard st ON st.task_id = t.task_id \
                WHERE \
                    t.org_id = ? \
                    AND t.task_active = 1 \
                    AND t.task_is_sys_standard IS NULL \
                    AND t.tasktype_id = ? \
                    AND ((? >= st.taskstd_min_paper_width AND ? >= st.taskstd_min_paper_heigth) OR (? >= st.taskstd_min_paper_width AND ? >= st.taskstd_min_paper_heigth)) \
                    AND ((? <= st.taskstd_max_paper_width AND ? <= st.taskstd_max_paper_height) OR (? <= st.taskstd_max_paper_width AND ? <= st.taskstd_max_paper_height))";
    var aArgs = [globals.org_id, TASKTYPEID.Cutting, nDim1, nDim2, nDim2, nDim1, nDim1, nDim2, nDim2, nDim1];
    
    if (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @param {Number} iWebNum
 * @return {JSRecord<db:/avanti/sa_task_worktype_section>}
 * @public 
 *
 * @properties={typeid:24,uuid:"D06F38C1-5D04-4B30-806C-34C32F5D9A86"}
 */
function getWebWorkTemplateSection(rSection, iWebNum) {
    
    var rWebWTSect;
    
    if (!rSection || !iWebNum) {
        return null;
    }
    
    if(rSection.is_parent) {
        
        if(utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype_section$children)) {
            
            for(var i = 1; i <= rSection.sa_task_worktype_section_to_sa_task_worktype_section$children.getSize(); i++) {
                
                var rWTChild = rSection.sa_task_worktype_section_to_sa_task_worktype_section$children.getRecord(i);
                
                if(rWTChild.worktypesection_web_number == iWebNum) {
                    rWebWTSect = rWTChild;
                    break;
                }
            }
        }
    }
    else{
        rWebWTSect = rSection;
    }
    
    return rWebWTSect;
}

/**
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @param {String} sPressTaskId
 * @return {JSRecord<db:/avanti/sa_task_worktype_section>}
 * @public 
 *
 * @properties={typeid:24,uuid:"FED989F1-5F8C-43F2-9539-66185ABF88F3"}
 */
function setWorkTemplateSectionType(rSection, sPressTaskId) {
    
    var 
    /** @type {JSRecord<db:/avanti/sa_task>} */ 
    rPressTask = null,
    sTaskTypeId = 0;

    rPressTask = scopes.avDB.getRec("sa_task",["task_id"],[sPressTaskId]);
    
    if (rPressTask) {
        sTaskTypeId = rPressTask.tasktype_id;
        
        switch (sTaskTypeId) {
            case scopes.avTask.TASKTYPEID.DiePress:
                rSection.worktypesection_is_die_press = 1;
                break;
            case scopes.avTask.TASKTYPEID.DigitalRollPress:
                rSection.worktypesection_is_digitalweb = 1;
                break;
            case scopes.avTask.TASKTYPEID.FlexoPress:
                rSection.worktypesection_is_flexo = 1;
                break;
            case scopes.avTask.TASKTYPEID.GrandFormatPress:
                rSection.worktypesection_is_grandformat = 1;
                break;
            case scopes.avTask.TASKTYPEID.WebPress:
                rSection.worktypesection_is_offsetweb = 1;
                break;
            case scopes.avTask.TASKTYPEID.StampingPress:
                rSection.worktypesection_is_stamp_press = 1;
                break;
            default:
                break;
        }
        
    }
    return rSection;
}

/**
 * @param {JSRecord<db:/avanti/sa_task_worktype_section>} rSection
 * @return {JSRecord<db:/avanti/sa_task_worktype_section>}
 * @public 
 *
 * @properties={typeid:24,uuid:"D6E9F236-081D-4B8C-9CE4-EFA1C2F780EA"}
 */
function clearWorkTemplateSectionType(rSection) {
    
    if (utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype)
            && utils.hasRecords(rSection.sa_task_worktype_section_to_sa_task_worktype.sa_task_worktype_to_sys_task_worktype)
            && 
            (rSection.sa_task_worktype_section_to_sa_task_worktype.sa_task_worktype_to_sys_task_worktype.sysworktype_press_type == scopes.avSystem.SYSWORKTYPE_PRESS_TYPE.None
            ||
            rSection.sa_task_worktype_section_to_sa_task_worktype.sa_task_worktype_to_sys_task_worktype.sysworktype_press_type == null)) {
                
        rSection.worktypesection_is_die_press = null;
        rSection.worktypesection_is_digitalweb = null;
        rSection.worktypesection_is_flexo = null;
        rSection.worktypesection_is_grandformat = null;
        rSection.worktypesection_is_offsetweb = null;
        rSection.worktypesection_is_stamp_press = null;
    }

    return rSection;
}

/**
 * @AllowToRunInFind
 *
 * @param {UUID} sTaskUUID
 *
 * @public
 *
 * @properties={typeid:24,uuid:"DD42BF79-022A-49D0-89D5-4F86C3C761FA"}
 */
function setVL_salesTaskVariableData(sTaskUUID) {
    
    var aReturn = new Array();
    var aDisplay = new Array();

    if (sTaskUUID) {
        
        /*** @type {JSFoundSet<db:/avanti/sa_task_machine>} */
        var fsMachine = scopes.avDB.getFS('sa_task_machine',['task_id'], [sTaskUUID.toString()]),
            rMachine = null; 

        if (fsMachine && fsMachine.getSize() > 0) {
            
            fsMachine.sort("sequence_nr asc");

            for (var i = 1; i <= fsMachine.getSize(); i++) {
                
                rMachine = fsMachine.getRecord(i);

                aReturn.push(rMachine.taskmachine_id);
                aDisplay.push(rMachine.taskmachine_description);
            }

        }
    }
    application.setValueListItems("avSales_sectionTaskVariableData", aDisplay, aReturn);
}


/**
 * Creates Plant records for revenue split for the section
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrderSectionTask
 * @param {JSRecord<db:/avanti/sa_order_plant_rev>} rOrderPlantRev
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} [rOrderSectionTaskQty]
 *
 * @public
 *
 * @properties={typeid:24,uuid:"CB44666C-15E0-4175-A403-FDF73F15FCAB"}
 */
function createTaskProducingPlantRecords(rOrderSectionTask, rOrderPlantRev, rOrderSectionTaskQty) {
    
    if (!rOrderSectionTask) {
        return;
    }
    
    if (!rOrderSectionTaskQty) {
        if (utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty)) {
            rOrderSectionTaskQty = rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(1);
        }
    }
    
    if (utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section)) {
        var rSection = rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
    }
   
    
    if (rSection && rOrderSectionTaskQty) {
        var fsDetailSectionTaskPlantQty = rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty;
        
        if (rOrderPlantRev != null) {
            var rNewRecord = fsDetailSectionTaskPlantQty.newRecord();
            var rDetailSectionTaskPlantQty = fsDetailSectionTaskPlantQty.getRecord(rNewRecord);
            rDetailSectionTaskPlantQty.ordrevdstask_id = rOrderSectionTask.ordrevdstask_id;
            rDetailSectionTaskPlantQty.sequence_nr = rOrderPlantRev.sequence_nr;
            rDetailSectionTaskPlantQty.ordhplrev_id = rOrderPlantRev.ordhplrev_id;
            rDetailSectionTaskPlantQty.ordrevd_id = rSection.ordrevd_id;
            
            if (rOrderPlantRev.ordhplrev_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                rDetailSectionTaskPlantQty.ordrevdstaskpq_qty = rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.ordrevdstqty_qty_task;
            }
            else {
                rDetailSectionTaskPlantQty.ordrevdstaskpq_qty = 0;
            }
        }
        else {
            if (utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section) 
                    && utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail) 
                    && utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header) 
                    && utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order)) {
                var rOrder = rOrderSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.getRecord(1);

                for (var i = 1; i <= rOrder.sa_order_to_sa_order_plant_rev.getSize(); i++) {
                    var rOrderPlantRev2 = rOrder.sa_order_to_sa_order_plant_rev.getRecord(i);
                    scopes.globals.avBase_selectedPlantRevenueUUID = rOrderPlantRev2.ordhplrev_id;
                    
                    if (!utils.hasRecords(rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty$selectedplantrevenueuuid)) {
                        rNewRecord = fsDetailSectionTaskPlantQty.newRecord(false);
                        rDetailSectionTaskPlantQty = fsDetailSectionTaskPlantQty.getRecord(rNewRecord);
                        rDetailSectionTaskPlantQty.ordhplrev_id = rOrderPlantRev2.ordhplrev_id;
                        rDetailSectionTaskPlantQty.ordrevdstask_id = rOrderSectionTask.ordrevdstask_id;
                        rDetailSectionTaskPlantQty.sequence_nr = rOrderPlantRev2.sequence_nr;
                        rDetailSectionTaskPlantQty.ordrevd_id = rSection.ordrevd_id;
                        
                        if (rOrderPlantRev2.ordhplrev_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                            rDetailSectionTaskPlantQty.ordrevdstaskpq_qty = rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.ordrevdstqty_qty_task;
                        }
                        else {
                            rDetailSectionTaskPlantQty.ordrevdstaskpq_qty = 0;
                        }
                    }
                }
            }
        }
        
        databaseManager.saveData(rOrderSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty);
    }
}

/**
 * Adjust the task plant revenue quantities
 * 
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rOrderDetailSection
 * @param {UUID} uOrderPlantRevId
 * 
 * @public 
 *
 * @properties={typeid:24,uuid:"1DA7CB6C-C703-47BD-8B95-4EE17644D608"}
 */
function adjustSectionTaskPlantQuantityAllocation(rOrderDetailSection, uOrderPlantRevId) {
    
    if (rOrderDetailSection 
            && utils.hasRecords(rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_qty) 
            && uOrderPlantRevId) {
        globals.avBase_selectedPlantRevenueUUID = uOrderPlantRevId;
        
        var bApplyToProducingPlant = false;
        var nRatio = 0;
        
        if (rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_qty.ordrevdsqty_qty != 0 
                && utils.hasRecords(rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_plant_qty$selectedplantrevenueuuid)) {
            nRatio = rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_plant_qty$selectedplantrevenueuuid.ordrevdspq_qty / rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_qty.ordrevdsqty_qty;
        }
        
        for (var odt = 1; odt <= rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_task.getSize(); odt++) {
            var rOrderDetailSectionTask = rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(odt);
            
            if (utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty) 
                    && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_task)) {
                
                var nTaskQuantity = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.ordrevdstqty_qty_task;
                var rTask = rOrderDetailSectionTask.sa_order_revds_task_to_sa_task.getRecord(1);
                
                if (rOrderDetailSectionTask.sa_order_revds_task_to_sa_task 
                        && Boolean(rOrderDetailSectionTask.sa_order_revds_task_to_sa_task.task_not_transferrable)) {
                    bApplyToProducingPlant = true;
                }
                
                if (utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty$selectedplantrevenueuuid)) {
                    var rSectionTaskPlantQuantity = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty$selectedplantrevenueuuid.getRecord(1);
                
                    if (!bApplyToProducingPlant && Boolean(rSectionTaskPlantQuantity.clc_is_task_in_plant)) {
                        if (nTaskQuantity == 1) {
                            if (rSectionTaskPlantQuantity.clc_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                                rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 1;
                            }
                            else {
                                rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                            }
                        }
                        else {
                            if (Boolean(rTask.clc_allocateToOriginatingPlant)) {
                                if (rSectionTaskPlantQuantity.clc_section_plant_qty > 0) {
                                    rSectionTaskPlantQuantity.ordrevdstaskpq_qty = nTaskQuantity;
                                }
                                else {
                                    rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                                }
                            }
                            else {
                                rSectionTaskPlantQuantity.ordrevdstaskpq_qty = scopes.globals["avUtilities_roundNumber"](nTaskQuantity * nRatio, 0);
                            }
                        }
                    }
                }
                
                databaseManager.saveData(rSectionTaskPlantQuantity);
                scopes.avTask.allocateTaskPlantRevenue(rOrderDetailSectionTask);
            }
        }
    }
}

/**
 * Is task part of an interbranch project plan
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrderDetailSectionTask
 *
 *
 * @return
 * @properties={typeid:24,uuid:"F8A4B663-67BF-48CD-A97C-3B6BB027C5AE"}
 */
function isSectionTaskInterbranchProjectPlan(rOrderDetailSectionTask) {

    if (rOrderDetailSectionTask
            && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section)
            && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail) 
            && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header) 
            && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order) 
            && rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.ordh_is_interbranch_projplan) {
        return true;
    }
    else {
        return false;
    }
}


/**
 * When the task quantity changes we must update the producting plant quantity allocations based on the original ratio
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrderDetailSectionTask
 * @param {Number} nOldQuantity
 * @param {Number} nNewQuantity
 * 
 * @public
 *
 * @properties={typeid:24,uuid:"B9D174ED-BC5E-46CB-8288-792D0526C578"}
 */
function updateTaskProducingPlantQuantityAllocation(rOrderDetailSectionTask, nOldQuantity, nNewQuantity) {
    if (!rOrderDetailSectionTask) {
        return;
    }
    
    if (utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section) 
            && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty)) {
        var rSection = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
        var nQtyAllocated = 0.00;
        
        /**@type {JSRecord<db:/avanti/sa_order_revds_task_plant_qty>} */
        var rHighestValue;

        for (var i = 1; i <= rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getSize(); i++) {
            var rSectionTaskPlantQuantity = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getRecord(i);

            if (nOldQuantity != null) {
                var nOriginalRatio = rSectionTaskPlantQuantity.ordrevdstaskpq_qty / nOldQuantity;
                rSectionTaskPlantQuantity.ordrevdstaskpq_qty = nNewQuantity * nOriginalRatio;
            }
            else {
                if (utils.hasRecords(rSectionTaskPlantQuantity.sa_order_revds_task_plant_qty_to_sa_order_plant_rev) 
                        && rSectionTaskPlantQuantity.sa_order_revds_task_plant_qty_to_sa_order_plant_rev.ordhplrev_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = nNewQuantity;
                }
                else {
                    rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                }
            }
            
            nQtyAllocated += rSectionTaskPlantQuantity.ordrevdstaskpq_qty;
            
            if (!rHighestValue) {
                rHighestValue = rSectionTaskPlantQuantity;
            }
            else {
                if (rSectionTaskPlantQuantity.ordrevdstaskpq_qty > rHighestValue.ordrevdstaskpq_qty) {
                    rHighestValue = rSectionTaskPlantQuantity;
                }
            }
        }
        
        //Make adjustments if total allocated not equal to task quantity.
        var nDiff = nNewQuantity - nQtyAllocated;
        
        if (nDiff != 0 && rHighestValue) {
            rHighestValue.ordrevdstaskpq_qty += nDiff;
        }
        
        //need to do the rounding for one task
        scopes.avTask.roundSectionTaskPlantQuantityAllocations( rSection, rOrderDetailSectionTask);
        scopes.avTask.allocateTaskPlantRevenue(rOrderDetailSectionTask);
        
    }
}

/**
 * Is the task valid for a specific division / plant
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 * @param {String} sPlant
 * @param {String} sDivision
 *
 * @return
 * @properties={typeid:24,uuid:"5AA4F787-DFE9-4568-A7BD-35374B452873"}
 */
function isTaskValidForDivPlant(rTask, sPlant, sDivision) {
    if (!rTask || !sPlant || !sDivision) {
        return false;
    }

    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = {},
        /***@type {JSDataSet} */
        dsData;

    oSQL.args = [globals.org_id, rTask.task_id.toString(), sPlant, sDivision];

    oSQL.sql = "SELECT 1 \
        FROM sa_task t \
        LEFT OUTER JOIN sa_division_plant dp on t.task_id = dp.object_id \
        WHERE t.org_id = ? AND t.task_id = ? \
        AND (t.all_divs = 1 OR \
            (dp.plant_id IS NOT NULL AND dp.plant_id = ?) OR \
            (dp.div_id = ? AND dp.all_plants = 1) \
            ) ";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    if (dsData && dsData.getMaxRowIndex() > 0) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * Adjust the task plant revenue quantities
 * 
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rOrderDetailSection
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} [rDetailSectionTask]
 * 
 * @public 
 *
 *
 * @properties={typeid:24,uuid:"D7792CE3-1F80-4553-875B-934352DDC731"}
 */
function roundSectionTaskPlantQuantityAllocations(rOrderDetailSection, rDetailSectionTask) {
    if (rOrderDetailSection 
            && utils.hasRecords(rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_qty)) {
       
            var nTotal = 0,
            nDiff = 0,
            nAddToNextPlantQty = 0;
           
            /**@type {JSRecord<db:/avanti/sa_order_revds_task_plant_qty>} */
            var rHighestValue;
            
            for (var odt = 1; odt <= rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_task.getSize(); odt++) {
            var rOrderDetailSectionTask = rOrderDetailSection.sa_order_revision_detail_section_to_sa_order_revds_task.getRecord(odt);
            
            if (rDetailSectionTask && rDetailSectionTask.ordrevdstask_id == rOrderDetailSectionTask.ordrevdstask_id) {
                continue;
            }
            
            nTotal = 0;
            rHighestValue = null,
            nAddToNextPlantQty = 0;
            bAllocatedToOriginatingPlant = false;
            
            if (utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty) 
                    && utils.hasRecords(rOrderDetailSectionTask.sa_order_revds_task_to_sa_task)) {
                var nTaskQuantity = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.ordrevdstqty_qty_task;
                var rTask = rOrderDetailSectionTask.sa_order_revds_task_to_sa_task.getRecord(1);
                
                for (var odtpq = 1; odtpq <= rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getSize(); odtpq++) {
                    var rSectionTaskPlantQuantity = rOrderDetailSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getRecord(odtpq);
                    
                    //Make adjustments based on rules
                    if ((rTask.task_is_system || rTask.task_not_transferrable)) {
                        if (rSectionTaskPlantQuantity.clc_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = nTaskQuantity;
                        }
                        else {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                        }
                    }
                    else if (Boolean(rTask.clc_allocateToOriginatingPlant)) {
                        if (rSectionTaskPlantQuantity.clc_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant 
                                && rSectionTaskPlantQuantity.clc_section_plant_qty > 0) {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = nTaskQuantity;
                            bAllocatedToOriginatingPlant = true;
                        }
                        
                        if (!bAllocatedToOriginatingPlant) {
                            if (rSectionTaskPlantQuantity.clc_section_plant_qty == 0) {
                                nAddToNextPlantQty += rSectionTaskPlantQuantity.ordrevdstaskpq_qty;
                                rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                            }
                            else {
                                if (nAddToNextPlantQty > 0) {
                                    rSectionTaskPlantQuantity.ordrevdstaskpq_qty += nAddToNextPlantQty;
                                    nAddToNextPlantQty = 0;
                                }
                            }
                        }
                        else {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                        }
                    }
                    else {
                        if (rSectionTaskPlantQuantity.clc_section_plant_qty == 0) {
                            rSectionTaskPlantQuantity.ordrevdstaskpq_qty = 0;
                        }
                    }
                    
                    nTotal += rSectionTaskPlantQuantity.ordrevdstaskpq_qty;
                    
                    if (!rHighestValue || rHighestValue && rSectionTaskPlantQuantity.ordrevdstaskpq_qty > rHighestValue.ordrevdstaskpq_qty) {
                        if (rSectionTaskPlantQuantity.clc_section_plant_qty > 0) {
                            rHighestValue = rSectionTaskPlantQuantity;
                        }
                    }
                }
                
                databaseManager.saveData(rSectionTaskPlantQuantity);
                
                nDiff = nTaskQuantity - nTotal;
                
                // We apply any balance to the producing plant With the highest quantity
                if (rHighestValue && nDiff != 0) {
                    rHighestValue.ordrevdstaskpq_qty += nDiff;
                    databaseManager.saveData(rHighestValue);
                }
            }
            
            scopes.avTask.allocateTaskPlantRevenue(rOrderDetailSectionTask, false);
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rSectionTask
 * @param {Boolean} [bSkipRevenueRecalc]
 *
 * @properties={typeid:24,uuid:"8948317A-736F-409D-9EDF-926F928FB19C"}
 */
function allocateTaskPlantRevenue(rSectionTask, bSkipRevenueRecalc) {
    if (!rSectionTask && !utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty)) {
        return;
    }
    
    var rSectionTaskQty = rSectionTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(1);
    
    if (rSectionTaskQty 
            && utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_task) 
            && utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section)) {
        
        var rSection = rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1);
        var rTask = rSectionTask.sa_order_revds_task_to_sa_task.getRecord(1);
        
        /*** @type {JSRecord<db:/avanti/sa_order_revds_task_plant_qty>} */
        var rSectionTaskPlantQty_OriginatingPlant;
        
        /*** @type {JSRecord<db:/avanti/sa_order_revds_task_plant_qty>} */
        var rSectionTaskPlantQty_HighestRevenue;
        
        var nTotalAllocationAmount = rSectionTaskQty.ordrevdstqty_t_orig + rSectionTaskQty.ordrevdstqty_bl_total;
        var nPlantTotalRevenue = 0.00,
            nRatioQty = 0.00;
        
        var bOriginatingPlantHasRevenue = false;
           
        if (databaseManager.getEditedRecords() && databaseManager.getEditedRecords().length > 0) {
            databaseManager.saveData(rSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty);
        }
        
        rSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.sort("sequence_nr asc");
        
        for (var i = 1; i <= rSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getSize(); i++) {
            var rSectionTaskPlantQty = rSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty.getRecord(i);
            rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt = 0;
            
            if ((rSectionTaskQty.ordrevdstqty_qty_task > 0 || rTask.task_is_system || rTask.clc_allocateToOriginatingPlant) 
                    && utils.hasRecords(rSectionTaskPlantQty.sa_order_revds_task_plant_qty_to_sa_order_plant_rev)) {
                var rOrderPlantRev = rSectionTaskPlantQty.sa_order_revds_task_plant_qty_to_sa_order_plant_rev.getRecord(1);
                
                if (rOrderPlantRev.ordhplrev_plant_type == scopes.avUtils.ENUM_PLANTREV_TYPE.OriginatingPlant) {
                    //The originating plant gets the balance after producing plants get their cut.
                    rSectionTaskPlantQty_OriginatingPlant = rSectionTaskPlantQty;
                }
                else {
                    if (rSectionTaskPlantQty.clc_is_task_allow_revenue_split) {
                        nRatioQty = rSectionTaskPlantQty.ordrevdstaskpq_qty / rSectionTaskQty.ordrevdstqty_qty_task;
                        
                        if (rTask.task_is_system) {
                            nRatioQty = 0;
                            //Use ratio of the section quantity for same plant
                            if (utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section)
                                    && utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revds_qty)) {
                                var rSectionQty = rSection.sa_order_revision_detail_section_to_sa_order_revds_qty.getRecord(1);
                                for (var sq = 1; sq <= rSection.sa_order_revision_detail_section_to_sa_order_revds_plant_qty.getSize(); sq++) {
                                    var rSectionPlantQty = rSection.sa_order_revision_detail_section_to_sa_order_revds_plant_qty.getRecord(sq);
                                    if (rSectionPlantQty.ordhplrev_id == rSectionTaskPlantQty.ordhplrev_id) {
                                        nRatioQty = rSectionPlantQty.ordrevdspq_qty / rSectionQty.ordrevdsqty_qty;
                                    }
                                }
                            }
                        }
                        
                        //The producing plants get their percentage as indicated on the order revenue split.
                        rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt = scopes.avUtils.roundNumber(nTotalAllocationAmount * nRatioQty * (rOrderPlantRev.ordhplrev_prod_plant_rev_pct / 100 ), 2);
                    
                        if (!bOriginatingPlantHasRevenue && rOrderPlantRev.ordhplrev_prod_plant_rev_pct < 100) {
                            bOriginatingPlantHasRevenue = true;
                        }
                    }
                    else {
                        rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt = 0;
                    }

                    nPlantTotalRevenue += rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt;
                    
                    if (!rSectionTaskPlantQty_HighestRevenue 
                            || (rSectionTaskPlantQty_HighestRevenue 
                            && rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt > rSectionTaskPlantQty_HighestRevenue.ordrevdstaskpq_revenue_amt)) {
                        if (rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt > 0) {
                            rSectionTaskPlantQty_HighestRevenue = rSectionTaskPlantQty;
                        }
                    }
                }
            }
            else {
                rSectionTaskPlantQty.ordrevdstaskpq_revenue_amt = 0;
            }
        }
        
        if (rSectionTaskPlantQty_OriginatingPlant) {
            rSectionTaskPlantQty_OriginatingPlant.ordrevdstaskpq_revenue_amt = nTotalAllocationAmount - nPlantTotalRevenue;
            nPlantTotalRevenue += rSectionTaskPlantQty_OriginatingPlant.ordrevdstaskpq_revenue_amt;

            if (!rSectionTaskPlantQty_HighestRevenue 
                    || ( rSectionTaskPlantQty_HighestRevenue && rSectionTaskPlantQty_OriginatingPlant.ordrevdstaskpq_revenue_amt > rSectionTaskPlantQty_HighestRevenue.ordrevdstaskpq_revenue_amt )) {
                if (rSectionTaskPlantQty_OriginatingPlant.ordrevdstaskpq_revenue_amt > 0) {
                    rSectionTaskPlantQty_HighestRevenue = rSectionTaskPlantQty_OriginatingPlant;
                }
            }
        }
        
        //Any Rounding gets applied to the plant with the highest revenue.
        var nDiff = nTotalAllocationAmount - nPlantTotalRevenue;
        if (nDiff != 0 && rSectionTaskPlantQty_HighestRevenue) {
            rSectionTaskPlantQty_HighestRevenue.ordrevdstaskpq_revenue_amt += nDiff;
        }
    }
    
    databaseManager.saveData(rSectionTask.sa_order_revds_task_to_sa_order_revds_task_plant_qty);
    
    if (!bSkipRevenueRecalc) {
        if (utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section) 
                && utils.hasRecords(rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail)) {
            var rOrderDetail = rSectionTask.sa_order_revds_task_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
            scopes.avDetail.calculatePlantRevenue(rOrderDetail);
        }
    }
}

/**
 * Get the task plant quantity for specified plant
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rSectionTask
 * 
 * @return {Number}  The section plant quantity
 *
 *
 * @properties={typeid:24,uuid:"9F6A89AD-2290-44FE-8161-3B068D447B8D"}
 */
function getTaskQuantityForOriginatingPlant(rSectionTask) {

    /**@type {Number} */
    var nQuantity = null;

    if (rSectionTask) {
        var sSQL = "SELECT tpq.ordrevdstaskpq_qty \
            FROM sa_order_revds_task_plant_qty AS tpq \
            INNER JOIN sa_order_plant_rev AS opr ON tpq.ordhplrev_id = opr.ordhplrev_id \
            WHERE tpq.org_id = ? AND ordrevdstask_id = ? AND opr.ordhplrev_plant_type = 'O' \
             ";

        var aArgs = [globals.org_id, rSectionTask.ordrevdstask_id.toString()];
        nQuantity = scopes.avDB.SQLQuery(sSQL, null, aArgs);
    }

    return nQuantity;
}


/**
 * Get all tasks with a billing code for order line
 * 
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rOrderDetail
 * @return {JSFoundSet<db:/avanti/sa_order_revds_task>}
 *
  *@public 
 *
 * @properties={typeid:24,uuid:"24852961-FBBA-4CD5-84A2-BFF4377EF81E"}
 */
function getTasksWithBillingCode(rOrderDetail) {
    
    if (!rOrderDetail) {
        return null;
    }
    
    /***@type {{sql:String, 
     *          args:Array, 
     *          server:String, 
     *          maxRows:Number, 
     *          table:String}}*/
    var oSQL = {};
    oSQL.sql = "SELECT sectiontask.ordrevdstask_id \
                FROM sa_order_revds_task AS sectiontask \
                INNER JOIN sa_order_revision_detail_section AS section ON sectiontask.ordrevds_id = section.ordrevds_id AND section.ordrevds_is_gang IS NULL \
                INNER JOIN sa_task AS task ON sectiontask.task_id = task.task_id \
                INNER JOIN sa_order_revds_task_qty As taskq ON sectiontask.ordrevdstask_id = taskq.ordrevdstask_id \
                WHERE (sectiontask.org_id = ?) AND (section.ordrevd_id = ?) AND (task.task_billing_code IS NOT NULL) AND (taskq.ordrevdstqty_t_orig > 0) \
                UNION ALL \
                SELECT sectiontask.ordrevdstask_id \
                FROM sa_order_revds_task AS sectiontask \
                INNER JOIN sa_order_revision_detail_section AS section ON sectiontask.ordrevds_id = section.ordrevds_id AND section.ordrevds_is_gang IS NULL \
                INNER JOIN sa_task AS task ON sectiontask.task_id = task.task_id \
                INNER JOIN sa_order_revds_task_qty AS taskq ON sectiontask.ordrevdstask_id = taskq.ordrevdstask_id \
                INNER JOIN sa_order_revds_task_post AS post ON sectiontask.ordrevdstask_id = post.ordrevdstask_id \
                WHERE (sectiontask.org_id = ?) AND (section.ordrevd_id = ?) AND (task.task_billing_code IS NOT NULL) AND (post.ordrevdstaskpost_price_post_t > 0) \
                ";
    oSQL.args = [globals.org_id, rOrderDetail.ordrevd_id.toString(), globals.org_id, rOrderDetail.ordrevd_id.toString()];
    oSQL.table = "sa_order_revds_task";

    /***@type {JSFoundSet<db:/avanti/sa_order_revds_task>} ***/
    var fsOrderRevisionDetailSectionTasks = globals["avUtilities_sqlFoundset"](oSQL);
    
    return fsOrderRevisionDetailSectionTasks;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty
 *
 * @properties={typeid:24,uuid:"4C8647AA-2C5D-4216-B869-ACEDD4A1BC54"}
 */
function setMatTaskItemQtyFromMultiplier(rTaskQty) {
	var nMultiplier = null;
	
	if (rTaskQty.ordrevdstqty_mat_multiplier_over != null) {
		nMultiplier = rTaskQty.ordrevdstqty_mat_multiplier_over;
	}
	else if (rTaskQty.ordrevdstqty_mat_multiplier != null) {
		nMultiplier = rTaskQty.ordrevdstqty_mat_multiplier;
	}		
	
	if (nMultiplier != null) {
		rTaskQty.ordrevdstqty_qty_item_over = nMultiplier * rTaskQty.ordrevdstqty_qty_task;
		rTaskQty.ordrevdstqty_qty_item = rTaskQty.ordrevdstqty_qty_item_over;
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty
 * @return {JSRecord<db:/avanti/sa_order_revds_task_qty>}
 *
 * @properties={typeid:24,uuid:"6ECEF10D-1A00-47BD-ABE6-BEBF0FE819DE"}
 */
function setMatTaskQtyFromLaminator(rTaskQty) {
    
    if (!rTaskQty) {
        return null;
    }
    
        /** @type {scopes.avSales.oEstOrdTask} */
    var oTask = scopes.avSales.oEstOrd.getTaskObject(rTaskQty.ordrevdstask_id);

    
    if (oTask.iTaskTypeId == scopes.avTask.TASKTYPEID.Laminating
            && utils.hasRecords(oTask.rTask.sa_order_revds_task_to_sa_order_revds_task$assoc)
            && utils.hasRecords(rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task_qty_patch)) {
        
        for (var m = 1; m <= oTask.rTask.sa_order_revds_task_to_sa_order_revds_task$assoc.getSize(); m++) {
          
            var rMat = oTask.rTask.sa_order_revds_task_to_sa_order_revds_task$assoc.getRecord(m);
            
            if (utils.hasRecords(rMat.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid)) {
                var rMatQty = rMat.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.getRecord(1);
              
                rMatQty.ordrevdstqty_outgoing_sheets = rTaskQty.ordrevdstqty_outgoing_sheets;
                rMatQty.ordrevdstqty_outgoing_prodqty = rTaskQty.ordrevdstqty_outgoing_prodqty;
                rMatQty.ordrevdstqty_incom_sheets_nosp = rTaskQty.ordrevdstqty_incom_sheets_nosp;
                rMatQty.ordrevdstqty_sheet_multiplier = rTaskQty.ordrevdstqty_sheet_multiplier;
                rMatQty.ordrevdstqty_qty_task = rTaskQty.ordrevdstqty_qty_task;
            }
        } 
    }
    return rTaskQty;
}


/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rTaskQty
 *
 * @return
 * @properties={typeid:24,uuid:"DDE9C78F-50C3-4C47-BA46-7CD6AFF9010F"}
 */
function setSpineThicknessAndGlue(rTaskQty) {

    var oTask = scopes.avSales.oEstOrd.getTaskObject(rTaskQty.ordrevdstask_id),
        rSecTask = (oTask) ? oTask.rTask : rTaskQty.sa_order_revds_task_qty_to_sa_order_revds_task.getRecord(1),
        rSecTaskStd = (oTask) ? oTask.rEstStdTaskStd : rSecTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.getRecord(1),
        rSecAssembly = rSecTask.sa_order_revds_task_to_sa_order_revision_detail_section.getRecord(1),
        rDet = rSecAssembly.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1),
        oDet = scopes.avSales.oEstOrd.getDetailObject(rDet.ordrevd_id),
        nSpineAdd = (rSecTaskStd.taskstd_std_spine_thickness == 1) ? rSecTaskStd.taskstd_add_to_spine_thickness : 0,
        i = 0,
        rPaper = null,
        nSpine = 0,
        aSpine = [],
        aCover = [],
        aCoverOldCalc = [],
        /*** @type {JSRecord<db:/avanti/sa_order_revision_detail_section>} */
        rCover = null,
        /*** @type {JSRecord<db:/avanti/sa_order_revision_detail_section>} */
        rSec = null;

    for (i = 0; i < oDet.aSections.length; i++) {
        
        rSec = oDet.aSections[i].rSection;
        
        if (rSec.ordrevds_is_cover) {
            if (rCover) {
                rCover.ordrevds_spline = scopes.globals["avUtilities_roundUp_byFactor"](nSpine + nSpineAdd, rSecTaskStd.taskstd_spine_rounding_factor);                
            }
            nSpine = 0;
            rCover = rSec;
            aCoverOldCalc.push(rCover.ordrevds_spline);
            aCover.push(rCover);
        }
        else if (!rSec.ordrevds_is_assembly
                && !rSec.ordrevds_is_cover
                && !rSec.ordrevds_is_gang
                && !rSec.ordrevds_is_generic
                && !rSec.is_parent
                && utils.hasRecords(rSec.sa_order_revision_detail_section_to_in_item_paper)) {
            rPaper = rSec.sa_order_revision_detail_section_to_in_item_paper.getRecord(1);

            if (rSec.ordrevds_work_type == scopes.avSection.WORK_TYPE.Flatwork) {
                nSpine += rSec.ordrevds_pages * rPaper.paper_caliper;
            }
            else {
                nSpine += rSec.ordrevds_pages / 2 * rPaper.paper_caliper;
            }
        }
        else if (rSec.ordrevds_is_assembly) {
            aSpine.push(nSpine);
        }
    }
    if (!rCover) {
        return rTaskQty;
    }
    
    // Need to recalculate each cover section to incorporate the spine thickness into the model   
    recalcCovers();
    
    if (rSecTaskStd.taskstd_spine_glue == 0) {
                
         rSecTask.ordrevdstask_spine_glue = 0;        
    }
    else if (rSecTask.ordrevdstask_spine_glue == null) {
        
        if (rSecTaskStd.taskstd_spine_glue == 1) {
            rSecTask.ordrevdstask_spine_glue = 1;
        }
        else {
            rSecTask.ordrevdstask_spine_glue = 0;
        }
    }

    // Need to calculate the glue need from the cover sections
    if (rSecTask.ordrevdstask_spine_glue == 1) {
        var nGlue = 0;
        for (i = 0; i < aCover.length; i++) {
            
            rCover = aCover[i];
            nSpine = aSpine[i];
            
            var nBleeds = 0,
                nHeadFootTrim = rCover.ordrevds_trim_foot + rCover.ordrevds_trim_head,
                nAdd = 0;
            // Randy and Dave say this will always be on the length
            nBleeds = rCover.ordrevds_bleed_size * rCover.ordrevds_bleed_length;
            nAdd = (nBleeds > nHeadFootTrim) ? nBleeds : nHeadFootTrim;
            var nBinderySpoils = (rTaskQty.ordrevdstqty_spoils_over) ? rTaskQty.ordrevdstqty_spoils_over : rTaskQty.ordrevdstqty_spoils;
            nGlue += (parseFloat(rCover.ordrevds_trim_size_length) + nAdd) * rCover.ordrevds_spline * (rCover.sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdsqty_qty + nBinderySpoils);
        }
        rTaskQty.ordrevdstqty_qty_item = nGlue;
    }
    else {
        rTaskQty.ordrevdstqty_qty_item = 0;
    }

    return rTaskQty;
    
    
    function recalcCovers() {

        var nSpineCalc = 0;
        for (i = 0; i < aCover.length; i++) {
            
            rCover = aCover[i];
            
            if (rCover.ordrevds_spline_over) {
                nSpineCalc = rCover.ordrevds_spline_over;
            }
            else {
                if (rSecTaskStd.taskstd_spine_rounding_factor) {
                    nSpineCalc = scopes.globals["avUtilities_roundUp_byFactor"](nSpine + nSpineAdd, rSecTaskStd.taskstd_spine_rounding_factor);
                }
                else {
                    nSpineCalc = scopes.globals["avUtilities_roundNumber"](nSpine + nSpineAdd, 4);
                }
            }
            
            rCover.ordrevds_spline = nSpineCalc;

            rCover.ordrevds_flat_size_recalc = 1;
            databaseManager.saveData(rCover);
            
            // Only recalc if the value has changed
            if (aCoverOldCalc[i] != rCover.ordrevds_spline) {
                rCover.ordrevds_flat_size_recalc = 1;
                forms["sa_order_revision_detail_section"].btnCalculate(null, rCover, false, null, null, false, true, false, true, true, null, null, true);
               //                                                       event, rSection, bRecalc, sJDF, bSinglePress, bSingleSection, bSkipBusyPlugin, bSkipModeling, bSkipObjSectionsInit, bSkipSectionRestore, bSkipBrowseMode, sRemodelKids, bSkipRevCalc, bSkipPressCalc){      
            }
        }
    }
}

/**
 * Add the associated tasks
 *
 * <AUTHOR> Dotzlaw
 * @since Jul 19, 2023
 * 
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask - The Task record
 * @param {JSRecord<db:/avanti/sa_order_revds_task_qty>} rOrdTaskQty - The Task Qty record
 * @param {JSFoundset<db:/avanti/sa_task_associated_tasks>} [fsPressAssocTasks] - specify a sa_task_associated_tasks foundset for press task 
 * 
 * @public
 *
 * @properties={typeid:24,uuid:"5EE28877-43F9-4473-9DFC-58CC17F484F2"}
 */
function addAssocTasks(rOrdTask, rOrdTaskQty, fsPressAssocTasks) {
    
    // Apply Associated task - addAssocTasks()
    var aAddAssocTask = [],
        /*** @type {JSRecord<db:/avanti/sa_task_associated_tasks>} */
        rAssocTask = null,
        sTaskUUID = null,
        /*** @type {JSRecord<db:/avanti/sa_order_revds_task>} */
        rAssocOrdTask = null,
        /*** @type {JSFoundset<db:/avanti/sa_order_revds_task_assoc>} */
        fsTasks = rOrdTask.sa_order_revds_task_to_sa_order_revds_task_assoc,
        rTask = null,
        fsAssocTasks = fsPressAssocTasks ? fsPressAssocTasks : rOrdTask.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_associated_tasks; 
        
    for (var i = 1; i <= fsAssocTasks.getSize(); i++) {
        
        rAssocTask = fsAssocTasks.getRecord(i);
        
        if (utils.hasRecords(rAssocTask.sa_task_associated_tasks_to_sa_task)) {
            
            if (aAddAssocTask.indexOf(rAssocTask.taskassociated_task_id) > -1) {
                continue;
            }
            aAddAssocTask.push(rAssocTask.taskassociated_task_id);

            sTaskUUID = rAssocTask.taskassociated_task_id;
            
            rAssocOrdTask = forms["sa_order_revision_detail_task_tbl"].addTask(sTaskUUID, true, rOrdTaskQty.ordrevdstqty_qty_task);
            rAssocOrdTask.ordrevdstask_id_assoc = rOrdTask.ordrevdstask_id;
            
            rTask = fsTasks.getRecord(fsTasks.newRecord(false, true));
            // Relational fields
            rTask.ordrevdstask_id = rAssocOrdTask.ordrevdstask_id;
            rTask.taskassociated_id = rAssocTask.taskassociated_id;
            rTask.ordrevdstask_id_assoc = rOrdTask.ordrevdstask_id;
            databaseManager.saveData(rTask); 
            // Need this save here, because global vl is firing and 
            // we need to make sure the data in these three fields is set (getting lost sometimes due to vl firing)
            rTask.task_id = rAssocTask.sa_task_associated_tasks_to_sa_task.task_id;
            rTask.tasktype_id = rAssocTask.sa_task_associated_tasks_to_sa_task.tasktype_id;
            rTask.taskmachine_id = rAssocTask.taskmachine_id;
            // Reference data fields
            rTask.ordrevdstask_assoc_add_spoil = rAssocTask.taskassociated_add_spoil;
            rTask.ordrevdstask_assoc_helpers = rAssocTask.taskassociated_helpers;
            rTask.ordrevdstask_assoc_setup = rAssocTask.taskassociated_setup;
            rTask.ordrevdstask_assoc_setup_min = rAssocTask.taskassociated_setup_min;
            rTask.ordrevdstask_assoc_speed_adj = rAssocTask.taskassociated_speed_adj;
            databaseManager.saveData(rTask);
        }
    }
}

/**
 * Get adjustments from Associated Tasks
 *
 * <AUTHOR> Dotzlaw
 * @since Jul 19, 2023
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rOrdTask - The Task record
 * @param {{maxHelper: Number, maxSpeedAdj: Number}} [oMax] - Optional existing num
 * @returns {{adjSpeed:Number, helpers:Number, setupSheets:Number, adjSpoils:Number, setupTime: Number, maxSpoils: Number}} An object containing the data from the associated presses *
 * @public 
 * @properties={typeid:24,uuid:"6D018EAD-013B-40F0-BA1E-80CDFB5E52D4"}
 */
function getAssocTaskAdjustments(rOrdTask, oMax) {

    // Apply Associated task - getAssocTaskAdjustments()
    var nSpeedAdj = (oMax && oMax.maxSpeedAdj) ? oMax.maxSpeedAdj : 0,
        nHelpers = (oMax && oMax.maxHelper) ? oMax.maxHelper : 0,
        nSetupSheets = 0,
        nSetupTime = 0,
        nSpoilAdj = 0,
        oObj = {adjSpeed:nSpeedAdj, helpers:nHelpers, setupSheets:0, adjSpoils:0, setupTime: 0, maxSpoils : 0},
        i = 0,
        fsOrdTaskAssoc = null,
        rOrdTaskAssoc = null;
    
    if (!rOrdTask 
            || !utils.hasRecords(rOrdTask.sa_order_revds_task_to_sa_order_revds_task_assoc$parent)) {       
        return oObj;
    }
    
    fsOrdTaskAssoc = rOrdTask.sa_order_revds_task_to_sa_order_revds_task_assoc$parent;
    for (i = 1; i <= fsOrdTaskAssoc.getSize(); i++) {

        rOrdTaskAssoc = fsOrdTaskAssoc.getRecord(i);
        
        if (rOrdTaskAssoc.ordrevdstask_assoc_speed_adj != null) {   
        	if (rOrdTaskAssoc.ordrevdstask_assoc_speed_adj > 1) {
        		
	            nSpeedAdj += rOrdTaskAssoc.ordrevdstask_assoc_speed_adj / 100;
        	}
        	else {
        		
	            nSpeedAdj += rOrdTaskAssoc.ordrevdstask_assoc_speed_adj;
        	}
        }
        if (rOrdTaskAssoc.ordrevdstask_assoc_helpers != null) {
            nHelpers += rOrdTaskAssoc.ordrevdstask_assoc_helpers;  
        }
        if (rOrdTaskAssoc.ordrevdstask_assoc_setup != null) {
            nSetupSheets += rOrdTaskAssoc.ordrevdstask_assoc_setup; 
        }
        if (rOrdTaskAssoc.ordrevdstask_assoc_setup_min != null) {
            nSetupTime += rOrdTaskAssoc.ordrevdstask_assoc_setup_min;
        }
        if (rOrdTaskAssoc.ordrevdstask_assoc_add_spoil != null) {
            nSpoilAdj += rOrdTaskAssoc.ordrevdstask_assoc_add_spoil;
        }
    }
    
    // Max adjustments for helper, run spoils and run slowdown from parent task
    var rTask = rOrdTask.sa_order_revds_task_to_sa_task.getRecord(1);
    var rAssocTaskStd = rTask.sa_task_to_sa_task_standard.getRecord(1);
    if (rAssocTaskStd.taskstd_max_slowdown > 0 && rAssocTaskStd.taskstd_max_slowdown / 100 < nSpeedAdj) {
        nSpeedAdj = rAssocTaskStd.taskstd_max_slowdown / 100;
        if(nSpeedAdj >= 1){
            nSpeedAdj = 0;
        }
    }
    if (rAssocTaskStd.taskstd_max_runspoils > 0 && rAssocTaskStd.taskstd_max_runspoils / 100 < nSpoilAdj) {
        nSpoilAdj = rAssocTaskStd.taskstd_max_runspoils / 100;
    }
    if (rAssocTaskStd.taskstd_max_helpers > 0 && rAssocTaskStd.taskstd_max_helpers < nHelpers) {
        nHelpers = rAssocTaskStd.taskstd_max_helpers;
    }
    // Randy wants no helpers on digital
    if (rTask.tasktype_id == scopes.avTask.TASKTYPEID.DigitalSheetPress) {
        nHelpers = 0;
    }

    oObj.adjSpeed = nSpeedAdj;
    oObj.helpers = nHelpers;
    oObj.setupSheets = nSetupSheets;
    oObj.setupTime = nSetupTime;
    oObj.adjSpoils = nSpoilAdj;
    oObj.maxSpoils = (rAssocTaskStd.taskstd_max_runspoils > 0) ? rAssocTaskStd.taskstd_max_runspoils / 100 : 0;

    return oObj;
}

/**
 * @public 
 * 
 * @param {Number} tasktype_id
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"447AFDB2-09FF-4AFB-B013-0E9378F3F6BC"}
 */
function taskUsesSpoils(tasktype_id){
	return aTaskTypesThatUseSpoils.indexOf(tasktype_id) > -1; 
}
						
/**
 * @public 
 * 
 * @param {Number} tasktype_id
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"EB7E006C-729F-46D1-B0CF-D7889DA2668C"}
 */
function taskIsOtherMachineVarSpoils(tasktype_id){
	return aTaskTypesThatUseOtherMachineVarSpoils.indexOf(tasktype_id) > -1; 
}


/**
 * @param {JSRecord<db:/avanti/sa_order_revds_task>} rTask
 *
 * @properties={typeid:24,uuid:"51E4BCB7-3510-4504-9FE4-5EF14F31D24B"}
 */
function clearPlatingPriceRuleOverrides (rTask) {
    
    for ( var i = 1; i <= rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getSize(); i++ ) {
        var rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(i);
        
        if (!rTaskQty.ordrevdstqty_apply_price_rules) {
            rTaskQty.ordrevdstqty_ouser_setup = null;
            rTaskQty.ordrevdstqty_ouser_item1  = null;
        }   
    }
}