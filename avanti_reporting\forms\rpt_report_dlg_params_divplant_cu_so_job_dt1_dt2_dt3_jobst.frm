borderType:"EmptyBorder,0,0,0,0",
customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"219,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"219",
width:"140"
},
enabled:true,
labelFor:"dToDue",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to.po.expected.date",
visible:true
},
name:"_toJobDate_labelc",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0389744F-ADE2-46E0-8BD0-DAB1E052B08A"
},
{
cssPosition:"246,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"246",
width:"140"
},
enabled:true,
labelFor:"dToDue",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to.shipped.date",
visible:true
},
name:"_toJobDate_labelcc",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0AE98283-DCE0-4CDD-BBF2-E7E6D3025053"
},
{
cssPosition:"189,-1,-1,522,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"189",
width:"212"
},
dataProviderID:"_dateTo",
enabled:true,
onDataChangeMethodID:"45D0D096-1FD6-4B0C-B552-70964CB09E8E",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"22610FA7-43F1-48A5-91F0-27BE95D64F42"
},
{
height:200,
partType:5,
typeid:19,
uuid:"2443A958-5312-43BB-AD9F-D3F478586A29"
},
{
cssPosition:"8,-1,-1,9,131,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"131"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"25B2B19A-9BA5-4180-8AA0-ACF9D65AEFCB"
},
{
cssPosition:"220,-1,-1,150,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"220",
width:"212"
},
dataProviderID:"_dateFromDue",
enabled:true,
onDataChangeMethodID:"47E30F82-C56D-470B-B5D0-7A7891CCE9CE",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFromDue",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"29041FC1-9613-400B-9026-65547FF6722A"
},
{
cssPosition:"161,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"161",
width:"140"
},
enabled:true,
labelFor:"_toJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobNumber",
visible:true
},
name:"_toJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2B00DB5F-5A34-408E-A036-84F7F98F31A0"
},
{
cssPosition:"5,-1,-1,522,212,94",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"94",
left:"522",
right:"-1",
top:"5",
width:"212"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"3E832397-F609-4F8B-A184-E452A2797690"
},
{
cssPosition:"248,-1,-1,150,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"248",
width:"212"
},
dataProviderID:"_dateFromShipped",
enabled:true,
onDataChangeMethodID:"A1EDC812-B8E1-4160-9FD7-027F5A007B7F",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFromShipDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"49B68564-F2FC-427F-88FA-AE7074F15EEE"
},
{
cssPosition:"108,-1,-1,150,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"108",
width:"212"
},
dataProviderID:"_fromCustomer",
editable:true,
enabled:true,
onDataChangeMethodID:"58E2D8C7-81D4-4478-BBEF-9AA9F52F1A5F",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_fromCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"49F8CB1A-299C-4780-BEB4-9FB011057646"
},
{
cssPosition:"277,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"277",
width:"140"
},
enabled:true,
labelFor:"_JobStatus",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jobStatus",
visible:true
},
name:"_JobStatus_Label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4C831220-CE8E-4799-9078-15A90EA9D1AC"
},
{
cssPosition:"163,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"163",
width:"140"
},
enabled:true,
labelFor:"_fromJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5DCD00E6-49C8-401A-893C-CB7B12B74A5F"
},
{
cssPosition:"8,-1,-1,150,212,94",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"94",
left:"150",
right:"-1",
top:"8",
width:"212"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"3B6E9DC7-FD43-4592-B2DF-2627FA24875C",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"5F4973A6-B800-4F9B-971B-6F5174D3C9BA"
},
{
cssPosition:"105,-1,-1,522,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"105",
width:"212"
},
dataProviderID:"_toCustomer",
enabled:true,
onDataChangeMethodID:"3EDC75FF-EA98-4E2D-8AA3-8742F9E12023",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_toCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6924C89C-34B2-4805-B32D-A74F76BB4E3B"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:328,
typeid:19,
uuid:"6DB2FEE9-2894-4C5C-80E0-C6239E1C8D70"
},
{
cssPosition:"133,-1,-1,522,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"133",
width:"212"
},
dataProviderID:"_toSalesOrder",
enabled:true,
onDataChangeMethodID:"B6C85B4B-706A-4BDB-ABC2-42F963D0F7C5",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_toSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7131188A-B273-47B1-B157-2892D8E1FEE3"
},
{
cssPosition:"108,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"108",
width:"140"
},
enabled:true,
labelFor:"_fromCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromCustomer",
visible:true
},
name:"_customer_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"729AA587-65EF-4CC6-B081-27A97A4BFE69"
},
{
cssPosition:"8,-1,-1,381,140,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"8",
width:"140"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"74C132AB-F104-440A-9107-ABFBA62B607F"
},
{
cssPosition:"248,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"248",
width:"140"
},
enabled:true,
labelFor:"dFromDue",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.from.shipped.date",
visible:true
},
name:"_fromJobDate_labelcc",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8422FA17-A3A3-40CD-AC27-7A7B3002BAB3"
},
{
cssPosition:"276,-1,-1,150,212,22",
formIndex:6,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"276",
width:"212"
},
dataProviderID:"_JobStatus",
enabled:true,
formIndex:6,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"A4F0F800-7FBC-414E-8188-3B7A8AEC7437",
visible:true
},
name:"_JobStatus",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"85518DB0-063C-45CE-B68F-29114CF5CB0A"
},
{
cssPosition:"164,-1,-1,150,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"164",
width:"212"
},
dataProviderID:"_fromJobNr",
enabled:true,
onDataChangeMethodID:"6BC5858C-A191-4465-9FF1-43992AED1E0E",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_fromJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"9EB31E21-9579-43E0-987F-9D9412049C8D"
},
{
cssPosition:"161,-1,-1,522,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"161",
width:"212"
},
dataProviderID:"_toJobNr",
enabled:true,
onDataChangeMethodID:"EBCAE333-86F9-4BBE-BB11-1FB9A710CB72",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_toJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A42F68E2-5879-41E5-AAD9-2AE2521979AB"
},
{
cssPosition:"245,-1,-1,522,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"245",
width:"212"
},
dataProviderID:"_dateToShipped",
enabled:true,
onDataChangeMethodID:"3B173659-88D1-4E37-9C57-7F86B31AF0CD",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dToShippedDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"AC9CFB22-1616-4B9E-B57A-95FDF50831B4"
},
{
cssPosition:"192,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"192",
width:"140"
},
enabled:true,
labelFor:"dFrom",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobDate",
visible:true
},
name:"_fromJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD42BB63-F6BF-45F6-9FA9-510181900EF6"
},
{
cssPosition:"135,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"135",
width:"140"
},
enabled:true,
labelFor:"_fromSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesOrder",
visible:true
},
name:"_fromSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C264F8D7-2696-41AA-8EFB-71652E20B3F1"
},
{
cssPosition:"134,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"134",
width:"140"
},
enabled:true,
labelFor:"_toSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toSalesOrder",
visible:true
},
name:"_toSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C48CEAD1-9F88-4F1B-A4E3-FAAF027FC131"
},
{
cssPosition:"136,-1,-1,150,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"136",
width:"212"
},
dataProviderID:"_fromSalesOrder",
enabled:true,
onDataChangeMethodID:"4A61C605-B75E-48E9-BA77-AC19B594E64E",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_fromSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"CBE0CA67-1602-471C-B7FC-0DC5A19A7838"
},
{
cssPosition:"192,-1,-1,150,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"192",
width:"212"
},
dataProviderID:"_dateFrom",
enabled:true,
onDataChangeMethodID:"4D34DAE8-E037-42D6-B4CA-F0A5328BCB40",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"D6ECC06A-15DE-4005-83F6-8C8C1B72D0E9"
},
{
cssPosition:"190,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"190",
width:"140"
},
enabled:true,
labelFor:"dTo",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobDate",
visible:true
},
name:"_toJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DD613FDC-91EC-4FCC-AD26-8B8C9FCD8812"
},
{
cssPosition:"106,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"106",
width:"140"
},
enabled:true,
labelFor:"_toCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toCustomer",
visible:true
},
name:"_toCustomerCode_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DDBD41F2-C3BC-45B2-9215-8DCD0A0169EE"
},
{
cssPosition:"217,-1,-1,522,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"217",
width:"212"
},
dataProviderID:"_dateToDue",
enabled:true,
onDataChangeMethodID:"20E26259-917E-418B-B1CE-D8CBA845EE98",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dToDue",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"E610A647-F147-497B-9DCD-A4B56BBEC6DA"
},
{
cssPosition:"220,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"220",
width:"140"
},
enabled:true,
labelFor:"dFromDue",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.from.po.expected.date",
visible:true
},
name:"_fromJobDate_labelc",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F92BB23C-FA1D-4D73-9286-4AE4C66595C5"
}
],
name:"rpt_report_dlg_params_divplant_cu_so_job_dt1_dt2_dt3_jobst",
navigatorID:"-2",
onShowMethodID:"31035C80-16D3-4A65-BB9E-A6DB44209865",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"C857FCA7-C091-4B33-8822-319896056EAC"