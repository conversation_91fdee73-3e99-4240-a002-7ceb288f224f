/**
 * <PERSON><PERSON> changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"C8B59A23-3205-44EB-AA44-177A04E7898A"}
 */
function allPlants_onDataChange(oldValue, newValue, event) {
    var iNumPlants = _to_sys_plant$emp_setup_div_id.getSize();
    var sPlantIDBak = globals.avEmpSetup_curPlantID;

    if (iNumPlants > 0) {
        for (var i = 1; i <= iNumPlants; i++) {
            var rec_sys_plant = _to_sys_plant$emp_setup_div_id.getRecord(i);
            if (utils.hasRecords(rec_sys_plant, 'sys_plant_to_sys_employee_plant$cur_setup_emp')) {
                if (!newValue) { // unselect all + there's a rec - delete it
                    rec_sys_plant.sys_plant_to_sys_employee_plant$cur_setup_emp.deleteRecord();
                }
                else{
                    continue;
                }
            }
            else if (newValue) { // select all + there's no rec - create it
                rec_sys_plant.sys_plant_to_sys_employee_plant$cur_setup_emp.newRecord();
                rec_sys_plant.sys_plant_to_sys_employee_plant$cur_setup_emp.empl_id = globals.avEmpSetup_curEmployeeID;
                rec_sys_plant.sys_plant_to_sys_employee_plant$cur_setup_emp.plant_id = rec_sys_plant.plant_id;
                rec_sys_plant.sys_plant_to_sys_employee_plant$cur_setup_emp.all_warehouses = 1;
            }

            globals.avEmpSetup_curPlantID = rec_sys_plant.plant_id;
        }
    }

    forms.empl_employee_warehouse_tbl.elements.grid.getColumn(forms.empl_employee_warehouse_tbl.elements.grid.getColumnIndex("chkDefWhse")).enabled = newValue? true : false;

    globals.avEmpSetup_curPlantID = sPlantIDBak;
    databaseManager.saveData();
    return true;
}
