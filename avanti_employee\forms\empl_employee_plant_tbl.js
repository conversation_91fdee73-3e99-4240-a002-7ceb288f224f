/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2ADF8E04-FC60-46E0-81F5-163A1F791142",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"0E667270-F494-4100-8619-1D8904B89719"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"808E3BD1-6D10-499F-A816-6DB5F4048E2B"}
 * @override
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
 return _super.onShowForm(firstShow, event);
}
/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"0802D333-6D60-4831-B881-6978B2BDFE92"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "chkPlantSelected" && col.styleClass.search(' disabled') == -1) {
		plantSelected_onDataChange(oldValue, newValue, event);
	}
	if (col.id == "chkDefPlant" && col.styleClass.search(' disabled') == -1) {
		defPlant_onDataChange(oldValue, newValue, event);
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2D41404F-1738-4F47-9543-86F5E3405374"}
 * @AllowToRunInFind
 */
function plantSelected_onDataChange(oldValue, newValue, event) {
    
    if (!foundset.plant_id) {
        return false;
    }
    /***@type {JSFoundset<db:/avanti/sys_employee_plant>} */
    var fs_sys_employee_plant = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_plant');

    if (newValue) {
        fs_sys_employee_plant.newRecord();
        fs_sys_employee_plant.empl_id = globals.avEmpSetup_curEmployeeID;
        fs_sys_employee_plant.plant_id = foundset.plant_id;
        fs_sys_employee_plant.all_warehouses = 1;
    }
    else {
        var sSQL = "SELECT emplplant_id FROM sys_employee_plant WHERE empl_id = ? AND org_id = ? AND plant_id = ?";
        var args = [globals.avEmpSetup_curEmployeeID, globals.org_id, foundset.plant_id.toString()];

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, 1);

        if (ds.getMaxRowIndex() > 0) {
            fs_sys_employee_plant.loadRecords(ds);
            fs_sys_employee_plant.deleteAllRecords();
        }
    }
    
    forms.empl_employee_warehouse_tbl.elements.grid.getColumn( forms.empl_employee_warehouse_tbl.elements.grid.getColumnIndex("chkDefWhse")).enabled = newValue? true : false;

    if (!newValue) {
        forms.empl_employee_plant_dtl.foundset.all_plants = 0;
    }

    databaseManager.saveData(fs_sys_employee_plant);

    if (newValue && globals.areAllPlantsSelected()) {
        forms.empl_employee_plant_dtl.foundset.all_plants = 1;
    }

    return true;
}

/**
 * @properties={typeid:24,uuid:"1840CB66-9349-4E2D-869A-7065CD6BC107"}
 */
function clearDefaultFlagForAllPlantsThisDiv() {
    for (var i = 1; i <= foundset.getSize(); i++) {
        var rPlant = foundset.getRecord(i);
        
		if (utils.hasRecords(rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp) && rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary) {
        	rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary = 0;
        }
    }
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"11A66437-1DCD-40C3-BEC7-10B61BF8465C"}
 */
function defPlant_onDataChange(oldValue, newValue, event) {
    if (newValue && !foundset.plant_selected_for_this_emp) {
        plantSelected_onDataChange(oldValue, newValue, event);
    }

    if (newValue) {
        forms.empl_employee_div_tbl.clearDefaultFlagForAllPlants();
        forms.empl_employee_div_tbl.clearDefaultFlagForAllDivs();
    }

    foundset.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary = newValue;
    forms.empl_employee_div_tbl.foundset.sys_division_to_sys_employee_div$cur_setup_emp.default_div = newValue;
    databaseManager.saveData(foundset);
    return true;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"A4245965-9A39-4D98-844B-4287143A2E98"}
 */
function onRecordSelection(_event, _form) {
    var retval = _super.onRecordSelection(_event, _form);
    globals.avEmpSetup_curPlantID = foundset.plant_id;
    forms.empl_employee_warehouse_tbl.elements.grid.getColumn(forms.empl_employee_warehouse_tbl.elements.grid.getColumnIndex("chkDefWhse")).enabled = ( foundset.plant_selected_for_this_emp == 1 );
    return retval;
}
