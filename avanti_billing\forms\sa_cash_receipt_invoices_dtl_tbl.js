/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A949E2D8-7597-4631-B239-139BA931EE0D",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"00F75671-8924-457C-8CF7-82A319119250"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"1539F854-1B5F-49FA-A079-F582CF688005"}
 */
function onShowForm(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, event]);
			return null;
		}
	}

	var result =  _super.onShowForm(firstShow, event)
	refreshUI()
	loadRecords()
	return result
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"455D966E-2739-481C-BB79-DC776778E258"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "payment_amount" && col.styleClass.search(' disabled') == -1) {
		onDataChangePaymentAmount(oldValue, newValue, event);
	}
	if (col.id == "overpayment_amount" && col.styleClass.search(' disabled') == -1) {
		onDataChange_overpayment(oldValue, newValue, event);
	}
	if (col.id == "discount_amount" && col.styleClass.search(' disabled') == -1) {
		onDataChangeDiscountAmount(oldValue, newValue, event);
	}
	if (col.id == "adjustment_amt" && col.styleClass.search(' disabled') == -1) {
		onDataChangeAdjustmentAmount(oldValue, newValue, event);
	}
	if (col.id == "payment_amount_cust" && col.styleClass.search(' disabled') == -1) {
		onDataChangePaymentAmountCustomerCurrency(oldValue, newValue, event);
	}
	if (col.id == "overpayment_amount_cust" && col.styleClass.search(' disabled') == -1) {
		onDataChangeOverpaymentCustomerCurrency(oldValue, newValue, event);
	}
	if (col.id == "discount_amount_cust" && col.styleClass.search(' disabled') == -1) {
		onDataChangeDiscountAmountCustomerCurrency(oldValue, newValue, event);
	}
	if (col.id == "adjustment_amt_cust" && col.styleClass.search(' disabled') == -1) {
		onDataChangeAdjustmentAmountCustomerCurrency(oldValue, newValue, event);
	}
}

/**
 * @properties={typeid:24,uuid:"C3F123D2-5915-4D6B-A41D-CDA6D54971D6"}
 */
function refreshUI(){
	if (scopes.avUtils.trackCurrencyExchange(forms.sa_cash_receipt_dtl.curr_id)) {
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt")).visible= false;
		
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount_cust")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt_cust")).visible= true;
	}
	else {
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount")).visible= true;
		elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt")).visible= true;
		
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount_cust")).visible= false;
		elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt_cust")).visible= false;
	}
	
	elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_gl")).visible= elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt")).visible;
	
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount")).format= globals.avBase_currencyFormat;
	
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_total_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_committed_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("postage_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_balance_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("payment_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount_cust")).format= globals.avBase_currencyFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt_cust")).format= globals.avBase_currencyFormat;
	
	elements.grid.getColumn(elements.grid.getColumnIndex("inv_date")).format= globals.avBase_dateFormat;
	
	if (scopes.avUtils.isNavModeReadOnly()) {
		elements.grid.setReadOnly(true);
	}
	else {
		if(scopes.avBilling.$cash_receipt_status == 'P') {
			elements.btn_clear_all.enabled = false;
			elements.btn_pay_all.enabled = false;
			elements.btn_pay_earliest.enabled = false;
			elements.grid.setReadOnly(true, ["payment_amount"]);
			elements.grid.setReadOnly(true, ["discount_amount"]);
			elements.grid.setReadOnly(true, ["adjustment_amt"]);
			elements.grid.setReadOnly(true, ["adjustment_gl"]);
			elements.grid.setReadOnly(true, ["adjustment_gl"]);
			elements.grid.setReadOnly(true, ["overpayment_amount"]);
			
		} else {
			elements.btn_clear_all.enabled = true;
			elements.btn_pay_all.enabled = true;
			elements.btn_pay_earliest.enabled = true;
			elements.grid.setReadOnly(false, ["payment_amount"]);
			
			if(globals.avSecurity_checkForUserRight('Cash_Receipts', 'update_discounts', globals.avBase_employeeUserID)) {
				elements.grid.setReadOnly(false, ["discount_amount"]);
				elements.grid.getColumn(elements.grid.getColumnIndex('discount_amount')).toolTipText = '';
			} else {
				elements.grid.setReadOnly(true, ["discount_amount"]);
				elements.grid.getColumn(elements.grid.getColumnIndex('discount_amount')).toolTipText = i18n.getI18NMessage('avanti.tooltip.noPermissions');
			}
			
			if(globals.avSecurity_checkForUserRight('Cash_Receipts', 'update_adjustments', globals.avBase_employeeUserID)) {
				elements.grid.setReadOnly(false, ["adjustment_amt"]);
				elements.grid.setReadOnly(false, ["adjustment_gl"]);
				elements.grid.setReadOnly(false, ["adjustment_gl"]);
				
				elements.grid.getColumn(elements.grid.getColumnIndex('adjustment_amt')).toolTipText = '';
				elements.grid.getColumn(elements.grid.getColumnIndex('adjustment_gl')).toolTipText = '';
				
			} else {
				elements.grid.setReadOnly(true, ["adjustment_amt"]);
				elements.grid.setReadOnly(true, ["adjustment_gl"]);
				elements.grid.setReadOnly(true, ["adjustment_gl"]);
				
				elements.grid.getColumn(elements.grid.getColumnIndex('adjustment_amt')).toolTipText = i18n.getI18NMessage('avanti.tooltip.noPermissions');
				elements.grid.getColumn(elements.grid.getColumnIndex('adjustment_gl')).toolTipText = i18n.getI18NMessage('avanti.tooltip.noPermissions');
			}
			
			if(globals.avSecurity_checkForUserRight('Cash_Receipts', 'trackOverpayments', globals.avBase_employeeUserID)){
				elements.grid.setReadOnly(false, ["overpayment_amount"]);
			}
			else{
				elements.grid.setReadOnly(true, ["overpayment_amount"]);
			}
		}
	}
}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"75BB0847-4ABB-4425-9004-9DFA08FE015F"}
 */
function loadRecordsBAK() {
	
	if(forms.sa_cash_receipt_dtl.deposit_account_type == null || forms.sa_cash_receipt_dtl.deposit_account_type == 0) {
		if(foundset.find()) {
			
			if(scopes.avBilling.$cash_receipt_status != 'P') {
				var sCustID = null;
				if(scopes.avBilling.$cash_receipt_cust_id != null && scopes.avBilling.$cash_receipt_cust_id.toString().length == 36) {
					sCustID = scopes.avBilling.$cash_receipt_cust_id;
				} else {
					sCustID = null;
				}
				foundset.inv_cust_id = sCustID;
				foundset.inv_balance_amount = '>0.005';
				foundset.inv_status = globals.$U;
				foundset.newRecord();
				foundset.inv_cust_id = sCustID;
				foundset.inv_balance_amount = '>0.005';
				foundset.inv_status = globals.$P;
				foundset.newRecord();
			}
			foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_id = scopes.avBilling.$sa_cash_receipt_id;
			foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_total_amount = '>0.005';
			if(foundset.search() > 0) {
				foundset.sort('inv_date asc');
				refreshUI();
			}
		}
	}
}

/**
 * rewrote the above function to use sql
 *
 * @properties={typeid:24,uuid:"677FA176-FB76-4A6B-B394-5FBB73E54C30"}
 */
function loadRecords() {
	if(forms.sa_cash_receipt_dtl.deposit_account_type == null || forms.sa_cash_receipt_dtl.deposit_account_type == 0) {
		var sSQL;
		var sConditions;
		
		if(scopes.avBilling.$cash_receipt_status != 'P') {
			if(scopes.avBilling.$cash_receipt_cust_id != null && scopes.avBilling.$cash_receipt_cust_id.toString().length == 36) {
				sConditions = "(inv_cust_id = ? or inv_cust_id in (select cust_id from sa_customer where cust_parent_cust_id = ?) ) \
						and (inv_status = 'U' or inv_status = 'P') \
						and inv_balance_amount > 0.005";
				
				sSQL = scopes.avDB.safeSQL("select inv_id from sa_invoice", sConditions, 'sa_invoice')
				databaseManager.saveData(foundset);
				foundset.clear();
				foundset.loadRecords(sSQL, [scopes.avBilling.$cash_receipt_cust_id, scopes.avBilling.$cash_receipt_cust_id]);
			} 
			else{
				foundset.clear();
			}
		}
		else{
			sSQL = scopes.avDB.safeSQL("select inv_id from sa_cash_receipt_detail", "sa_cash_receipt_id = ? and invoice_total_amount > 0.005", "sa_cash_receipt_detail");
			databaseManager.saveData(foundset);
			foundset.clear();
			foundset.loadRecords(sSQL, [scopes.avBilling.$sa_cash_receipt_id]);
		}
		
		if(foundset.getSize() > 0) {
			foundset.sort('inv_date asc');
			refreshUI();
		}
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 * @param {Boolean} [bOverpayment]
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"0D33D191-775D-40E5-BE67-ABC3FDD13288"}
 */
function onDataChangePaymentAmount(oldValue, newValue, event, bOverpayment) {
	foundset.inv_committed_amount += newValue - oldValue
	
    if (!bOverpayment && utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
            && utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt)) {
        foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setPaymentAmount(foundset);
    }
	
    //Alert the user if there are un-posted cash receipts.
    if (inv_tot_unposted_paym_credits > 0 && newValue) {
        var nBalance = (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Add ? inv_tot_unposted_paym_credits : inv_tot_unposted_paym_credits - newValue);
        var sMessage = i18n.getI18NMessage("avanti.dialog.cashReceipt_unpostedCashReceipts");
        var sCurrencyFormat = globals.avBase_currencyFormat;
        sCurrencyFormat = sCurrencyFormat.substring(0, sCurrencyFormat.indexOf('|'));
        sMessage = sMessage.replace("<amount>",utils.numberFormat(nBalance, sCurrencyFormat));
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.dialog.notification'), sMessage,  i18n.getI18NMessage('avanti.dialog.ok'));
    }
	
	if(foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'))
	}
	if(utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
			&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt) 
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt < 0.005 && foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt > -.005) {
		forms.sa_cash_receipt_dtl.updateStatus(1)
	}
	
	if(utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
			&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt) 
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt > 0.005
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_status == 'M') {
		forms.sa_cash_receipt_dtl.updateStatus(1)
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"11042F5C-7114-4B7C-A4D3-61EAFFFD08ED"}
 */
function onDataChangeAdjustmentAmount(oldValue, newValue, event) {
    
    onDataChangePaymentAmount(oldValue, newValue, event);
    
	foundset.inv_committed_amount += newValue - oldValue
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setAdjustmentAmount(foundset)
	if(foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'))
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"ADAF11E2-8910-47A8-87D4-691FE9132C22"}
 */
function onDataChangeDiscountAmount(oldValue, newValue, event) {
	foundset.inv_committed_amount += newValue - oldValue
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setDiscountAmount(foundset)
	if(foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'))
	}
	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D0E23768-8EB3-4662-B6FF-77F11D42C197"}
 */
function onActionPayAll(event) {
	if(!scopes.avUtils.isNavModeReadOnly() && scopes.avBilling.$cash_receipt_status  != 'P') {
		for(var inv_idx = 1; inv_idx <= foundset.getSize(); inv_idx++) {
			var inv_rec = foundset.getRecord(inv_idx) 
			
			if(utils.hasRecords(inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)) {
				inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount = inv_rec.inv_balance_amount
				
			} else {
				var cr_det_rec = inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.getRecord(inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.newRecord())
				cr_det_rec.invoice_payment_amount = inv_rec.inv_balance_amount
			}
			inv_rec.inv_committed_amount += inv_rec.inv_balance_amount
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setPaymentAmount(foundset)
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setDiscountAmount(foundset)
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setAdjustmentAmount(foundset)
		}
		forms.sa_cash_receipt_dtl.refreshUI()
		
	} else {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'))
	}
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *  @properties={typeid:24,uuid:"6C7992F8-0AAB-4F71-A229-CF5B9C968D43"}
 */
function onActionClearAll(event) {
	if(!scopes.avUtils.isNavModeReadOnly() && scopes.avBilling.$cash_receipt_status  != 'P') {
		if (scopes.avBilling.$cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted 
				&& !forms.sa_cash_receipt_dtl._bWorkatoUseCashReceiptsRegister) {
			return;
		}
		clearAll()
		forms.sa_cash_receipt_dtl.refreshUI()
	} else {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'))
	}
}

/**
 * @properties={typeid:24,uuid:"8EB6F6A5-D6D2-480F-92A9-A8D835DD21ED"}
 */
function clearAll() {
	for(var inv_idx = 1; inv_idx <= foundset.getSize(); inv_idx++) {
		var inv_rec = foundset.getRecord(inv_idx);
		
		if(utils.hasRecords(inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)) {
			inv_rec.inv_committed_amount -= inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_total_amount;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount = 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_discount_amount= 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount = 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount_exchanged = 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_discount_amount_exchanged= 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount_exchanged = 0;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_glacct_id = null;
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setPaymentAmount(foundset);
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setDiscountAmount(foundset);
			inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setAdjustmentAmount(foundset);
		}
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"1168EE93-303D-46B5-ABD8-A60C0979D20B"}
 */
function onActionPayEarliest(event) {
	if(!scopes.avUtils.isNavModeReadOnly() && scopes.avBilling.$cash_receipt_status  != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted) {
		if (scopes.avBilling.$cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted 
				&& !forms.sa_cash_receipt_dtl._bWorkatoUseCashReceiptsRegister) {
			return;
		}
		
		clearAll()
		databaseManager.saveData(foundset)
		foundset.sort('inv_date asc')
		var starting_total = forms.sa_cash_receipt_dtl.getCashReceiptAmount();
		var bHasUnpostedCreditNote = false;
		var aUnpostedCreditNoteInvs = [];
		for(var inv_idx = 1; inv_idx <= foundset.getSize(); inv_idx++) {
			var amount_assigned = 0.00
			if(starting_total > 0.005) {
				var inv_rec = foundset.getRecord(inv_idx) 
				
				if(inv_rec.inv_total_unposted_credit_note != 0){
					bHasUnpostedCreditNote = true;
					aUnpostedCreditNoteInvs.push(inv_rec.inv_number);
				}
				
				if(utils.hasRecords(inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)) {
					if(starting_total > inv_rec.inv_balance_amount) {
						amount_assigned = inv_rec.inv_balance_amount
						inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount = inv_rec.inv_balance_amount
						starting_total -= inv_rec.inv_balance_amount
					} else {
						amount_assigned = starting_total
						inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount = starting_total
						starting_total = 0.00
					}
					
					
				} else {
					var cr_det_rec = inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.getRecord(inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.newRecord())
					if(starting_total > inv_rec.inv_balance_amount) {
						amount_assigned = inv_rec.inv_balance_amount
						cr_det_rec.invoice_payment_amount = inv_rec.inv_balance_amount
						starting_total -= inv_rec.inv_balance_amount
					} else {
						amount_assigned = starting_total
						cr_det_rec.invoice_payment_amount = starting_total
						starting_total = 0.00
					}
				}
				inv_rec.inv_committed_amount += amount_assigned
				inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setPaymentAmount(foundset)
				inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setDiscountAmount(foundset)
				inv_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setAdjustmentAmount(foundset)
			} else {
				break;
			}
		}
		
		//If there is an un-posted credit note alert the user
		if(bHasUnpostedCreditNote){
			var sMessage = i18n.getI18NMessage('avanti.dialog.cashReceipt_unpostedCreditNotes') + " " + aUnpostedCreditNoteInvs.join(", ");
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.dialog.notification'),sMessage, i18n.getI18NMessage('avanti.dialog.ok'));
		}
		
		if(utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
				&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt) 
				&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt < 0.005 && foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt > -.005) {
			forms.sa_cash_receipt_dtl.updateStatus(1)
		}
		
	} else {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'))
	}
}

/**
 * Checks if the balance of the invoices are equal to zero and if the adjustments that are set have a GL to write to.
 * 
 * @return
 * @properties={typeid:24,uuid:"93A8B57C-DE4C-483A-8EEB-FB1CA1B75128"}
 */
function getBalanceAndAdjustmentStatus() {
	loadRecords();
	for (var fs_idx = 1; fs_idx < foundset.getSize(); fs_idx++) {
		var fs_rec = foundset.getRecord(fs_idx);

		if (fs_rec.inv_balance_amount < -0.005) {
			return 1;
		}

		if (utils.hasRecords(fs_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)) {
			if (fs_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount != null 
					&& fs_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount != 0.00 
					&& (fs_rec.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_glacct_id == null)) {
				return 1;
			}
		}
	}

	return 0;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4453BD29-011A-475E-A836-C49A7F0C2F32"}
 */
function onDataChange_overpayment(oldValue, newValue, event) {
	onDataChangePaymentAmount(oldValue, newValue, event, true);	
	forms.sa_cash_receipt_dtl.foundset.total_overpayment_amount += newValue - oldValue;
	
	return true;
}

/**
 * Set column visibility
 * 
 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceipt
 *
 * @public
 *
 * @properties={typeid:24,uuid:"53AF9B58-6481-46D2-952D-20DC180C9EA7"}
 */
function setColumnVisibility(rCashReceipt) {
    
    var bFlag = true;
    
    if (rCashReceipt && utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_payment_method) 
            && (rCashReceipt.sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount 
            || rCashReceipt.sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount)) {
        bFlag = false;
    }
    
    if (scopes.avUtils.trackCurrencyExchange(forms.sa_cash_receipt_dtl.curr_id)) {
    	elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount_cust")).visible= bFlag;
        elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount_cust")).visible= bFlag;
        elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt_cust")).visible= bFlag;
    }
    else {
    	elements.grid.getColumn(elements.grid.getColumnIndex("overpayment_amount")).visible= bFlag;
        elements.grid.getColumn(elements.grid.getColumnIndex("discount_amount")).visible= bFlag;
        elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_amt")).visible= bFlag;
    }
    
    elements.grid.getColumn(elements.grid.getColumnIndex("adjustment_gl")).visible= bFlag;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 * @param {Boolean} [bOverpayment]
 * @param {Boolean} [bAdjustment]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"27072D15-5A5F-4977-8D37-98D048CBAABF"}
 */
function onDataChangePaymentAmountCustomerCurrency(oldValue, newValue, event, bOverpayment, bAdjustment) {
	foundset.inv_committed_amount += globals.convertCustomerCurrencyToOrganizationCurrency(newValue - oldValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
    
    if ((!bOverpayment && !bAdjustment)
    		&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)) {
    	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount = globals.convertCustomerCurrencyToOrganizationCurrency(newValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
    }	
	
    if (!bOverpayment && utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id)
            && utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt)) {
        foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setPaymentAmount(foundset);
    }
	
    //Alert the user if there are un-posted cash receipts.
    if (inv_tot_unposted_paym_credits_exchanged > 0 && newValue) {
        var nBalance = (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Add ? inv_tot_unposted_paym_credits_exchanged : inv_tot_unposted_paym_credits_exchanged - newValue);
        var sMessage = i18n.getI18NMessage("avanti.dialog.cashReceipt_unpostedCashReceipts");
        var sCurrencyFormat = globals.avBase_getCurrencyFormat(_to_sys_organization.org_default_curr_id);
        sCurrencyFormat = sCurrencyFormat.substring(0, sCurrencyFormat.indexOf('|'));
        sMessage = sMessage.replace("<amount>",utils.numberFormat(nBalance, sCurrencyFormat));
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.dialog.notification'), sMessage, i18n.getI18NMessage('avanti.dialog.ok'));
    }
	
	if (foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'))
	}
	if (utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
			&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt) 
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt < 0.005 && foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt > -.005) {
		forms.sa_cash_receipt_dtl.updateStatus(1);
	}
	
	if (utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id) 
			&& utils.hasRecords(foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt) 
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_remaining_amt > 0.005
			&& foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.sa_cash_receipt_status == 'M') {
		forms.sa_cash_receipt_dtl.updateStatus(1);
	}
	
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F3D5D734-BE3D-4FC0-B8B3-EC601C91A406"}
 */
function onDataChangeOverpaymentCustomerCurrency(oldValue, newValue, event) {
	onDataChangePaymentAmountCustomerCurrency(oldValue, newValue, event, true);
	forms.sa_cash_receipt_dtl.foundset.total_overpayment_amount_exchanged += newValue - oldValue;
	forms.sa_cash_receipt_dtl.foundset.total_overpayment_amount += globals.convertCustomerCurrencyToOrganizationCurrency(newValue - oldValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.overpayment_amount = globals.convertCustomerCurrencyToOrganizationCurrency(newValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"4A7450E0-B76F-4CB8-A042-0E65F58F1A20"}
 */
function onDataChangeDiscountAmountCustomerCurrency(oldValue, newValue, event) {
	foundset.inv_committed_amount += globals.convertCustomerCurrencyToOrganizationCurrency(newValue - oldValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_discount_amount = globals.convertCustomerCurrencyToOrganizationCurrency(newValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setDiscountAmount(foundset);
	if(foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E1CEE9D5-8DDA-4195-A4DC-A08378B62BEF"}
 */
function onDataChangeAdjustmentAmountCustomerCurrency(oldValue, newValue, event) {
	onDataChangePaymentAmountCustomerCurrency(oldValue, newValue, event, false, true);
	
	foundset.inv_committed_amount += globals.convertCustomerCurrencyToOrganizationCurrency(newValue - oldValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount = globals.convertCustomerCurrencyToOrganizationCurrency(newValue, foundset.curr_id, forms.sa_cash_receipt_dtl.foundset.sa_cash_receipt_date);
	foundset.sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.sa_cash_receipt_detail_to_sa_cash_receipt.setAdjustmentAmount(foundset);
	if(foundset.inv_balance_amount < -0.005) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt_balanceLessThanZero'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true;
}
