/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8CF65FC2-F334-43B6-9141-00BAA2E4D63B"}
 */
var uDataBroadcastSessionID = null;

/**
 * The values in the enum must be unique, DO NOT ENTER DUPLICATES
 * @enum 
 * @public 
 *
 *
 * @properties={typeid:35,uuid:"3A52BB32-DDB1-4771-AF71-37BD22533589",variableType:-4}
 */
var FOUNDSET_LOCKS = {
    ItemLock: "ItemLock",
    ItemWhseLock: "ItemWhseLock",
    ItemWhseLocLock: "ItemWhseLocLock",
    ItemWhseLocRecLock: "ItemWhseLocRecLock",
	ItemCommitmentLock: "ItemCommitmentLock",
	SaOrderRevdItemLock: "SaOrderRevdItemLock",
	SaOrderRevDetailLock: "SaOrderRevDetailLock",
	BatchJob: "BatchJ<PERSON>"
};


/**
 * @author: <PERSON>, 2011-04-11 
 * @description: Get the PK name for a table
 * 
 * This is duplicated function from avanti_utilities/globals.js avUtilities_tableGetPKName, when being called from trigger and alerts
 * scopes.globals["avUtilities_tableGetPKName"] can not be called.
 *
 * @param {String}  sTable - The table to lookup the PK name for
 * @param {String}  [sServer] - Defaults to globals.avBase_dbase_avanti
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"74872CC0-2E3D-4676-9EFD-8315FB15E2BD"}
 */
function local_avUtilities_tableGetPKName(sTable, sServer) {

    //Pass in table name and it will return the pk Column Name
    var sPK_Name = "";
    var aPK_Names = new Array();
    var jsTable,
        i = 0;

    // Check parameters
    if (!sServer) {
        sServer = globals.avBase_dbase_avanti;
    }

    // Handle any special cases (views) like this
    switch (sTable) {
        case '_v_contact':
            sPK_Name = 'contact_id';
            break;
        case '_v_customer':
            sPK_Name = 'cust_id';
            break;
        case '_v_document':
            sPK_Name = 'doc_id';
            break;
        case '_v_employee':
            sPK_Name = 'empl_id';
            break;
        case '_v_est':
        case '_v_ord':
            sPK_Name = 'ordh_id';
            break;
        default:
            try {
                //application.output("sTable: " + sTable);
                if (sTable) {
                    jsTable = databaseManager.getTable(sServer, sTable);
                    aPK_Names = jsTable.getRowIdentifierColumnNames();

                    // GD - Feb 15, 2017: A few tables have multiple pks, and getFs() will need those to load with sql properly
                    for (i = 0; i < aPK_Names.length; i++) {

                        sPK_Name += aPK_Names[i] + ",";
                    }
                    // remove trailing ","
                    sPK_Name = sPK_Name.slice(0, sPK_Name.length - 1);
                }
            }
            catch (ex) {
                sPK_Name = sTable + '_id';
            }
    }

    return sPK_Name;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * 
 * @return {JSFoundSet}
 *
 * @properties={typeid:24,uuid:"33B1F4C8-71AA-4E28-870D-35F009FB9F35"}
 */
function getFSUsingServoyFind(sTable, aSearchColNames, aSearchColValues) {
	return getFS(sTable, aSearchColNames, aSearchColValues, null, null, null, true);
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"8C06B914-F3E8-42C3-B0C1-92F2D1E9DEE4"}
 */
function getRecUsingServoyFind(sTable, aSearchColNames, aSearchColValues) {
	var rRec = null;
	var fs = getFS(sTable, aSearchColNames, aSearchColValues, null, null, null, true);

	if (fs && fs.getSize()) {
		rRec = fs.getRecord(1);
	}
	
	return rRec;
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {String} [sSortString]
 * @param {String} [sDatabase]
 * @param {Boolean} [bSkipOrgID]
 * @param {Boolean} [bServoyFind]
 *
 * @return {JSFoundSet}
 *
 * @properties={typeid:24,uuid:"76499A99-2E96-4441-8C8D-95C0FA9F976A"}
 */
function getFS(sTable, aSearchColNames, aSearchColValues, sSortString, sDatabase, bSkipOrgID, bServoyFind) {
	
	var 	
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = {},
		sServer = (sDatabase) ? sDatabase : globals.avBase_dbase_avanti,
		/***@type{JSFoundSet} */
		fsTable = datasources.db[sServer][sTable].getFoundSet(),
		sPk = null,
		i = 0,
		iMax = aSearchColNames.length,
		bRelation = false,
		retVal = null;

	// Need to check for relations being passed in
	for (i = 0; i < iMax; i++) {
		if (aSearchColNames && aSearchColNames[i].toString().search("_to_") > -1) {
		    bRelation = true;
		}
		
		if (aSearchColValues && aSearchColValues[i] && isNaN(aSearchColValues[i]) && aSearchColValues[i].toString().search("_to_") > -1) {
		    bRelation = true;
		}
	}
	
	var bUseServoyFind = false;
	var bAddDivCond = false;
	var bAddPlantCond = false;
	
	if (bServoyFind || bRelation) {
		bUseServoyFind = true;
	}
	// using div/plant filter
	else if (globals.avBase_getSystemPreference_Number(79) === 1) {
		// sa_customer and in_item dont filter based on div_id and plant_id cols. its more complicated. just do a ServoyFind
		if (sTable == 'sa_customer') {
			bUseServoyFind = true;
		}
		else if (sTable == 'in_item') {
			//Force using SQL without Div/Plan
		}
		else {
			bAddDivCond = globals.avBase_divID && globals.avBase_divID != 'ALL' && fsTable.alldataproviders.indexOf("div_id") > -1;
			bAddPlantCond = globals.avBase_plantID && globals.avBase_plantID != 'ALL' && fsTable.alldataproviders.indexOf("plant_id") > -1;
		}
	}
	
	// GD - Jan 23, 2016: New approach to get the foundset using SQL; takes 0ms and called often
	if (!bUseServoyFind) {
		
		try {
			
			if (iMax === 0) {
				
				// No search columns, just return the complete foundset
				fsTable.loadAllRecords();
				
			} else {
				
			    sPk = local_avUtilities_tableGetPKName(sTable, sServer);
				
				oSQL.sql = "SELECT " + sPk + " FROM " + sTable;
				if (iMax > 0) oSQL.sql += " WHERE ";
				
				if (sServer == globals.avBase_dbase_avanti && !bSkipOrgID && fsTable.alldataproviders.indexOf("org_id") > -1) {
					oSQL.sql += " org_id = ? "
					if (iMax > 0 || bAddDivCond || bAddPlantCond) oSQL.sql += " AND ";
					oSQL.args = [globals.org_id];
				} else {
					oSQL.args = [];
				}
				
				// if div/plant filter on we need to filter based on selections
				if(bAddDivCond){
					oSQL.sql += " (div_id = ? OR div_id is null) "
					if (iMax > 0 || bAddPlantCond) oSQL.sql += " AND ";
					oSQL.args.push(globals.avBase_divID);
				}
				if(bAddPlantCond){
					oSQL.sql += " (plant_id = ? OR plant_id is null) "
					if (iMax > 0) oSQL.sql += " AND ";
					oSQL.args.push(globals.avBase_plantID);
				}
			}
			
			// Add search cols & values
            for (i = 0; i < iMax; i++) {
            
                if (aSearchColValues) {
                    if (i > 0 && ( aSearchColValues[i] || aSearchColValues[i] === null || aSearchColValues[i] === 0 )) {
                        oSQL.sql += " AND ";
                    }
                    
                    if (aSearchColValues[i] === "^" || aSearchColValues[i] === null) {
                        oSQL.sql += aSearchColNames[i] + " IS NULL ";
                    }
                    else if (aSearchColValues[i] === "^=") {
                        oSQL.sql += " ( " + aSearchColNames[i] + " IS NULL OR " + aSearchColNames[i] + " = ? ) ";
                        oSQL.args.push(0);
                    }
                    else if (aSearchColValues[i] && isNaN(aSearchColValues[i]) && ( aSearchColValues[i].toString().search(">") > -1 || aSearchColValues[i].toString().search("<") > -1 )) {
                        oSQL.sql += aSearchColNames[i] + " " + aSearchColValues[i];
                    }
                    else if (aSearchColValues[i] === "!^") {
                        oSQL.sql += aSearchColNames[i] + " IS NOT NULL ";
                    }
                    else if (aSearchColValues[i] && isNaN(aSearchColValues[i]) && aSearchColValues[i].toString().slice(0, 1) == "!") {
                        oSQL.sql += aSearchColNames[i] + " != ? ) ";
                        var sCheck = aSearchColValues[i].slice(1);
                        oSQL.args.push(sCheck);
                    }
                    else if (aSearchColValues[i] && isNaN(aSearchColValues[i]) && aSearchColValues[i].toString().indexOf('%') > -1) { // sl-11119 - hp - added support for '%'
                        oSQL.sql += aSearchColNames[i] + ' LIKE ?';
                        oSQL.args.push(aSearchColValues[i].toString());
                    }
                    else if (aSearchColValues[i] || aSearchColValues[i] === 0) { // HP - sl-11137 - added 'aSearchColValues[i] === 0' condition - we need to be able to search for cols that have a zero val
                        oSQL.sql += aSearchColNames[i] + " = ?";
                        ( isNaN(aSearchColValues[i]) ) ? oSQL.args.push(aSearchColValues[i].toString()) : oSQL.args.push(aSearchColValues[i]);
                    }
                }
            }
			
			// Trap for any final " AND " or "WHERE" clause that is there and remove it
			if (oSQL.sql && oSQL.sql.slice(oSQL.sql.length - 5) == " AND " ) oSQL.sql = oSQL.sql.slice(0, oSQL.sql.length - 5);
			if (oSQL.sql && oSQL.sql.slice(oSQL.sql.length - 7) == " WHERE " ) oSQL.sql = oSQL.sql.slice(0, oSQL.sql.length - 7);
            
			//Somehow we are ending up with incorrect syntax on some queries. I cannot reproduce so added this code to prevent SQL errors.
			if (oSQL 
			        && oSQL.sql 
			        && oSQL.sql.indexOf(" AND  AND") > -1) {
                oSQL.sql = oSQL.sql.replace(" AND  AND", " AND ");
            }
			
			// GD - Feb 17, 2017: Looks like Servoy does not always maintain the sort order from the select
			// Add sort string
//			if (sSortString && sSortString.search("_to_") == -1) {
//				
//				oSQL.sql += " ORDER BY " + sSortString;
//			}
			
			if (sPk && oSQL.sql) {
				
				fsTable.clear();
				fsTable.loadRecords(oSQL.sql, oSQL.args);
	
	//			oSQL.table = sTable;
	//			fsTable = globals["avUtilities_sqlFoundset"](oSQL);
			}
		
			// Add sort string
			if (fsTable && sSortString) {
			
				fsTable.sort(sSortString);
			}
			
//			if (fsTable && sSortString && sSortString.search("_to_") > -1) {
//				
//				fsTable.sort(sSortString);
//			}
//			
			retVal = fsTable;
		
		} catch ( e ) {
			
            application.output("-----");
            application.output("ERROR: avDB.getFS SQL foundset load failed - report this to Gary D " + e.stack);
            application.output("Failed oSQL.sql = " + oSQL.sql);
            if (oSQL.args) {
                application.output("Failed oSQL.args = " + oSQL.args.join(","));
            }
			// The sql foundset load failed, use the backup metho		
			retVal = useServoyFind();
			
			
		}
	} else {
		
		// GD - Jan 23, 2016: Old way of getting the foundset using Servoy find (slow) - took 21ms +, and called often
		// only used if the request passes in related fields
		
		retVal = useServoyFind();
	}


    function useServoyFind() {
        if (sTable) {
            fsTable.loadAllRecords();
            
            if (aSearchColNames.length > 0 && aSearchColValues.length > 0) {
                if (fsTable.find()) {
                    var bDoSearch = false;
                    
                    for (i = 0; i < aSearchColNames.length; i++) {
                        if (aSearchColValues && aSearchColValues[i] != null) {
                            fsTable.setDataProviderValue(aSearchColNames[i], aSearchColValues[i]);
                            bDoSearch = true;
                        }
                    }
                    
                    if (bDoSearch) {
                        fsTable.search();
                    }
                    // no search because all the params were null - have to get out of find mode
                    // otherwise we return a foundset in find mode, which has negative effects
                    else {
                        fsTable.clear();
                    }
                }
            }
            
            if (sSortString) {
                fsTable.sort(sSortString);
            }
        }
        return fsTable;
    }
	
	return retVal;
}

/**
 * @AllowToRunInFind
 * 
 * <AUTHOR>
 * 
 * Copy of getFS() with added ability to use OR in search. aaSearchColNames + aaSearchColValues are 2 dim arrays
 * 
 * @param {String} sTable
 * @param {Array<Array<String>>} aaSearchColNames
 * @param {Array<Array>} aaSearchColValues
 * @param {String} [sSortString]
 *
 * @return
 * @properties={typeid:24,uuid:"479B2D76-9A12-4DC1-AC5A-292445C845EF"}
 */
function getFSWithOR(sTable, aaSearchColNames, aaSearchColValues, sSortString){
	var retVal = null

	if(sTable){
		/** @type {JSFoundSet} */
		var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable)
		fs.loadAllRecords()
		
		if(aaSearchColNames.length > 0 && aaSearchColValues.length > 0){			
			if(fs.find()){
				var bDoSearch = false
				
				for(var i=0;i<aaSearchColNames.length;i++){
					if(i>0){
						fs.newRecord()
					}
					
					for(var j=0;j<aaSearchColNames[i].length;j++){
						if(aaSearchColValues[i][j] != null){
							fs.setDataProviderValue(aaSearchColNames[i][j], aaSearchColValues[i][j])
							bDoSearch=true
						}
					}
				}
				
				if(bDoSearch){
					fs.search()
				}
			}
		}

		if(sSortString){
			fs.sort(sSortString)
		}

		retVal = fs
	}
	
	return retVal
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|UUID|Number>} aSearchColValues
 * @param {String} [sSortString]
 * @param {Boolean} [bCreateIfDoesntExist]
 * @param {Boolean} [bypassCache]
 * @param {String} [sDatabase]
 * @param {Boolean} [bServoyFind]
 *
 * @return
 * @properties={typeid:24,uuid:"224B6C65-0AEA-4EE7-86A8-661766BD608E"}
 */
function getRec(sTable, aSearchColNames, aSearchColValues, sSortString, bCreateIfDoesntExist, bypassCache, sDatabase, bServoyFind){
	var fs,
		sCols = aSearchColNames ? aSearchColNames.join("_") : "",
		sVals = aSearchColValues ? aSearchColValues.join("_,") : "",
		rRec;

	// sl-12091 - getRec() is called in many places in sched board, and in many of those places it runs into trouble because of
	// the cache. we have several sched tables where records are deleted and recreated, and it was picking up records that had
	// been deleted.
	if(!bypassCache && scopes.avScheduling.bInScheduleBoard){
		bypassCache = true;
	}
	
	// GD - Nov 14, 2014: Perf Tune - Storing the record here in cache, which is init at the start of an est computation
	// cycle, and then destroyed at the end. The reason is I think we can find our records faster using this function
	// along with the cache, so we are not doing repeated finds for the same record (I found 18 calls to this function with simple estimate)
	// using this cache cut the processing time from 400ms to 150ms
	
	// If we did this lookup before, then just return the record from the cache
	if (globals.avBase_oCalcs && !bypassCache 
		&& globals.avBase_oCalcs.getDBrec
		&& globals.avBase_oCalcs.getDBrec[sTable]
		&& globals.avBase_oCalcs.getDBrec[sTable][sCols]
		&& globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals]){
		
		return globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals];
	}
	
	fs = getFS(sTable, aSearchColNames, aSearchColValues, sSortString, sDatabase, null, bServoyFind); // GD - Nov 14, 2014: Added sortString support
	if(fs && fs.getSize()){
		
		rRec = fs.getRecord(1);
		
		// GD - Nov 14, 2014: Perf Tune - If this is the first time we found this record, then add to cache
		if (globals.avBase_oCalcs &&
			globals.avBase_oCalcs.getDBrec &&
			(  !globals.avBase_oCalcs.getDBrec[sTable]
			|| !globals.avBase_oCalcs.getDBrec[sTable][sCols]
			|| !globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals])){
			
			if (!globals.avBase_oCalcs.getDBrec[sTable]) globals.avBase_oCalcs.getDBrec[sTable] = {};
			if (!globals.avBase_oCalcs.getDBrec[sTable][sCols]) globals.avBase_oCalcs.getDBrec[sTable][sCols] = {};
			
			globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals] = rRec;
		}
		return rRec;
	}
	else if(bCreateIfDoesntExist){
		fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable)
		var idx = fs.newRecord()
		
		if(aSearchColNames.length > 0 && aSearchColValues.length > 0){			
			for(var i=0;i<aSearchColNames.length;i++){
				if(aSearchColValues[i] != null){
					fs.setDataProviderValue(aSearchColNames[i], aSearchColValues[i])
				}
			}
		}
		
		databaseManager.saveData(fs)		
		rRec = fs.getRecord(idx); 

		// GD - Nov 14, 2014: Perf Tune - If this is the first time we found this record, then add to cache
		if (globals.avBase_oCalcs &&
			globals.avBase_oCalcs.getDBrec &&
			(  !globals.avBase_oCalcs.getDBrec[sTable]
			|| !globals.avBase_oCalcs.getDBrec[sTable][sCols]
			|| !globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals])){
			
			if (!globals.avBase_oCalcs.getDBrec[sTable]) globals.avBase_oCalcs.getDBrec[sTable] = {};
			if (!globals.avBase_oCalcs.getDBrec[sTable][sCols]) globals.avBase_oCalcs.getDBrec[sTable][sCols] = {};
			
			globals.avBase_oCalcs.getDBrec[sTable][sCols][sVals] = rRec;
		}
		return rRec;
	}
	
	return null
}

/**
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|UUID|Number>} aSearchColValues
 * @param {String} sReturnColName
 * @param {String} [sRelation]
 * @param {String} [sSortString]
 * @param {String} [sDatabase]
 *
 * @return
 * @properties={typeid:24,uuid:"7D2D2934-50AD-4F02-89F3-CDBD0562E0DB"}
 */
function getVal(sTable, aSearchColNames, aSearchColValues, sReturnColName, sRelation, sSortString, sDatabase) {
    var fs = getFS(sTable, aSearchColNames, aSearchColValues, sSortString, sDatabase);
    if (fs.getSize()) {
        if (sRelation) {
            var rec = fs.getRecord(1);
            if (utils.hasRecords(rec, sRelation)) {
                return rec[sRelation].getDataProviderValue(sReturnColName);
            }
        }
        else {
            return fs.getDataProviderValue(sReturnColName);
        }
    }

    return null;
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {String} sReturnColName
 *
 * @return
 * @properties={typeid:24,uuid:"588148A1-E965-44A6-A497-373805C2EE09"}
 */
function Query(sTable, aSearchColNames, aSearchColValues, sReturnColName){
	var fs = getFS(sTable, aSearchColNames, aSearchColValues)
	if(fs.getSize()){
		return fs.getDataProviderValue(sReturnColName)
	}
	
	return null
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {String} [sDatabase]
 *
 * @return
 * @properties={typeid:24,uuid:"A5C2CAFE-34D2-4826-930F-99F03CF8D2BC"}
 */
function Exists(sTable, aSearchColNames, aSearchColValues, sDatabase){
	var fs = getFS(sTable, aSearchColNames, aSearchColValues, null, sDatabase)
	if(fs.getSize()){
		return true
	}
	
	return false
}

/**
 * @param {String} sTable
 * @param {Array<Array<String>>} aaSearchColNames
 * @param {Array<Array>} aaSearchColValues
 *
 * @return
 * @properties={typeid:24,uuid:"69CC4FEA-7FFE-4648-BF58-6C0AE1EE9798"}
 */
function ExistsWithOr(sTable, aaSearchColNames, aaSearchColValues){
	var fs = getFSWithOR(sTable,aaSearchColNames,aaSearchColValues)
	if(fs.getSize()){
		return true
	}
	
	return false
}

/**
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 *
 * @return
 * @properties={typeid:24,uuid:"D8DA2164-98F8-4F24-8B88-6F0375D132EA"}
 */
function getCount(sTable, aSearchColNames, aSearchColValues){
	var fs = getFS(sTable, aSearchColNames, aSearchColValues)
	return databaseManager.getFoundSetCount(fs)
}

/**
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {Array<String>} aSetColNames
 * @param {Array} aSetColValues
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"BAEA2B83-4C38-4720-ADE9-5960097D9AE9"}
 */
function update(sTable, aSearchColNames, aSearchColValues, aSetColNames, aSetColValues){
	var fs = getFS(sTable, aSearchColNames, aSearchColValues);
	var iMax = databaseManager.getFoundSetCount(fs);
	var j;

	// sl-18842 - if updating a single record dont need to use fsUpdater. i found that it was changing selection of form foundset
	if (iMax == 1) {
		for (j = 0; j < aSetColNames.length; j++) {
			fs.setDataProviderValue(aSetColNames[j], aSetColValues[j]);
		}
	}
	else if (iMax > 1) {
		var fsUpdater = databaseManager.getFoundSetUpdater(fs);

		for (j = 0; j < aSetColNames.length; j++) {
			fsUpdater.setColumn(aSetColNames[j], aSetColValues[j]);
		}

		fsUpdater.performUpdate();
	}
	
	return iMax;
}

/**
 * @param {JSFoundset} foundset
 * @param {Array<String>} aSetColNames
 * @param {Array<Object>} aSetColValues
 * @param {Array<String>} [aSearchColNames]
 * @param {Array} [aSearchColValues]
 *
 * @properties={typeid:24,uuid:"1D7A8EC0-3A1A-4A96-B30E-17EEEEA5A37F"}
 */
function updateFS(foundset, aSetColNames, aSetColValues, aSearchColNames, aSearchColValues){
	if(foundset && utils.hasRecords(foundset)){
		var fs = foundset;
		
		if(aSearchColNames && aSearchColValues){
			fs = filterFS(fs, aSearchColNames, aSearchColValues);
		}
		
		if(utils.hasRecords(fs)){
			var fsUpdater = databaseManager.getFoundSetUpdater(fs);
			
			for(var j=0;j<aSetColNames.length;j++){
				fsUpdater.setColumn(aSetColNames[j], aSetColValues[j]);
			}
			
			fsUpdater.performUpdate();
		}
	}
}

/**
 * This function loads a foundset using sSQL and aArgs, then it updates all the recs in that foundset using 
 * aSetColNames and aSetColValues
 * 
 * @public 
 * 
 * @param {String} sTable
 * @param {Array<String>} aSetColNames
 * @param {Array} aSetColValues
 * @param {String} sSQL
 * @param {Array} [aArgs]
 * @param {Array<String>} [aSetColActions] - '+', '-', '*', '/' - adjusts the existing col value by the aSetColValues amount
 * @param {Array<Number>} [aLowerLimits] - ensures we dont pass limit - used with aSetColActions
 * @param {Array<Number>} [aUpperLimits] - ensures we dont pass limit - used with aSetColActions 
 * @param {String} [sDatabase] - defaults to avanti if this isnt specified
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2FCCD201-E15F-43A1-910C-5015325AD94E"}
 */
function updateFSWithSQLQuery(sTable, aSetColNames, aSetColValues, sSQL, aArgs, aSetColActions, aLowerLimits, aUpperLimits, sDatabase) {
    if (!sDatabase) {
        sDatabase = globals.avBase_dbase_avanti;
    }
    
    var fs = databaseManager.getFoundSet(sDatabase, sTable);
    
    fs.loadRecords(sSQL, aArgs);
    
    if (utils.hasRecords(fs)) {
        // if using aSetColActions then can't use updateFS, which uses FoundSetUpdater. if only 1 rec no point
        // in using FoundSetUpdater
        if (aSetColActions || fs.getSize() == 1) {
            for (var i = 1; i <= fs.getSize(); i++) {
                fs.setSelectedIndex(i);

                for (var j = 0; j < aSetColNames.length; j++) {
                    var name = aSetColNames[j];
                    var curVal = fs.getDataProviderValue(name);
                    var newVal = aSetColValues[j];
                    var action = aSetColActions ? aSetColActions[j] : null;
                    
                    if (action ==  '+') {
                        newVal = curVal + newVal; 
                    }
                    else if (action ==  '-') {
                        newVal = curVal - newVal; 
                    }
                    else if (action ==  '*') {
                        newVal = curVal * newVal; 
                    }
                    else if (action ==  '/') {
                        newVal = curVal / newVal; 
                    }
                    
                    if (aLowerLimits && aLowerLimits[j] != null && newVal < aLowerLimits[j]) {
                        newVal = aLowerLimits[j];
                    }
                    else if (aUpperLimits && aUpperLimits[j] != null && newVal > aUpperLimits[j]) {
                        newVal = aUpperLimits[j];
                    }
                    
                    fs.setDataProviderValue(name, newVal);
                }
            }
            
            databaseManager.saveData(fs);
        }
        else {
            updateFS(fs, aSetColNames, aSetColValues);
        }
        
        return true;
    }
    else {
        return false;
    }
}

/**
 * @param {String} sTable
 * @param {String} sSumColName
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * 
 * @public 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"EC05ADD5-ECE2-4CE0-B4AF-A679C75D651E"}
 */
function getSum(sTable, sSumColName, aSearchColNames, aSearchColValues){
	var sum = 0
	
	if(!aSearchColNames){
		aSearchColNames=[]
	}
	if(!aSearchColValues){
		aSearchColValues=[]
	}
	
	var fs = getFS(sTable, aSearchColNames, aSearchColValues)
	var max = fs.getSize()

	if(max > 0){
		for(var i=1;i<=max;i++){
			fs.setSelectedIndex(i)
			sum += fs.getDataProviderValue(sSumColName) 
		}
	}
	
	return sum
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {String} [sDatabase]
 * @param {Boolean} [bSuppressDialog]
 * 
 * @return {Number} - the number of recs deleted
 *
 * @properties={typeid:24,uuid:"2A3410BD-791D-4B59-B7AC-781E7C00D9E5"}
 */
function deleteRecs(sTable, aSearchColNames, aSearchColValues, sDatabase, bSuppressDialog) {
    var fs = getFS(sTable, aSearchColNames, aSearchColValues, null, sDatabase);
    
    if (fs && fs.getSize() > 0) {
        var nRealCount = databaseManager.getFoundSetCount(fs);
        
        try {
            if (fs.deleteAllRecords()) {
                databaseManager.saveData(fs);
                return nRealCount;
            }
        }
        catch (ex) {
            if (!bSuppressDialog) {
                globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('Error Deleting Table: ' + sTable + " - "), ex.message);
            }
        }
    }
    
    return 0;
}

/**
 * @param {JSFoundset} foundset
 * @param {String} colName
 * @param val
 * @param {Number} [excludeIDX]
 *
 * @return
 * @properties={typeid:24,uuid:"20AE4A5C-F05F-486A-B5C1-91E2DA3ED3FA"}
 */
function doesFSHaveValue(foundset, colName, val, excludeIDX){
	var fs = foundset.duplicateFoundSet()
	var max = fs.getSize()
	
	for(var i=1;i<=max;i++){
		fs.setSelectedIndex(i)
		if(i != excludeIDX && fs.getDataProviderValue(colName) == val){
			return true
		}
	}
	
	return false
}
 
 
 /**
 * @AllowToRunInFind
 * 
 * @param {JSFoundset} foundset
 * @param {Array<String>} aSearchColNames
 * @param {Array} aSearchColValues
 * @param {String} [sSortString]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"8FC8FE54-70B0-4840-9563-058F2ED2E2D6"}
 */
function filterFS(foundset, aSearchColNames, aSearchColValues, sSortString){	
	if(foundset && foundset.getSize()){
		if(aSearchColNames.length > 0 && aSearchColValues.length > 0){			
			var fsClone = foundset.duplicateFoundSet(); // clone it so org fs isnt affected

			if(fsClone.find()){
				var bDoSearch = false
				
				for(var i=0;i<aSearchColNames.length;i++){
					if(aSearchColValues[i] != null){
						fsClone.setDataProviderValue(aSearchColNames[i], aSearchColValues[i])
						bDoSearch=true
					}
				}
				
				if(bDoSearch && fsClone.search(false, true)){
					if(sSortString){
						fsClone.sort(sSortString)
					}
				}
			}
		}
	}
	
	return fsClone
}

/**
 * @AllowToRunInFind
 * 
 * @param {JSFoundset} foundset
 * @param {Array<String>} aSearchColNames
 * @param {Array} aSearchColValues
 * @param {String} [sSortString]
 *
 * @return
 * @properties={typeid:24,uuid:"2BE97990-A7DC-4440-ABA4-36ADC4CFA465"}
 */
function getRecFromFS(foundset, aSearchColNames, aSearchColValues, sSortString){
	var rv = null
	var fs = foundset.duplicateFoundSet();
	
	if(fs && fs.getSize()){
		if(aSearchColNames.length > 0 && aSearchColValues.length > 0){			
			var fsClone = foundset.duplicateFoundSet(); // clone it so org fs isnt affected

			if(fsClone.find()){
				var bDoSearch = false
				
				for(var i=0;i<aSearchColNames.length;i++){
					if(aSearchColValues[i] != null){
						fsClone.setDataProviderValue(aSearchColNames[i], aSearchColValues[i])
						bDoSearch=true
					}
				}
				
				if(bDoSearch && fsClone.search(false, true)){
					if(sSortString){
						fsClone.sort(sSortString)
					}

					rv = fsClone.getRecord(1)
				}
			}
		}
	}
	
	return rv
}

/**
 * @AllowToRunInFind
 * 
 * @param {JSFoundset} foundset
 * @param {Array<String>} aSearchColNames
 * @param {Array} aSearchColValues
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"6F907684-A242-4035-A795-F92CE91646B1"}
 */
function ExistsInFS(foundset, aSearchColNames, aSearchColValues){
	if(foundset && foundset.getSize()){
		if(aSearchColNames.length > 0 && aSearchColValues.length > 0){			
			var fsClone = foundset.duplicateFoundSet(); // clone it so org fs isnt affected

			if(fsClone.find()){
				var bDoSearch = false
				
				for(var i=0;i<aSearchColNames.length;i++){
					if(aSearchColValues[i] != null){
						fsClone.setDataProviderValue(aSearchColNames[i], aSearchColValues[i])
						bDoSearch=true
					}
				}
				
				if(bDoSearch && fsClone.search(false, true)){
					return true
				}
			}
		}
	}
	
	return false
}

/**
 * this code based on avLockRecord() below
 * 
 * @public 
 *
 * @param {String} programName - The name of the framework program	
 * @param {JSRecord} record - The selected record
 * @param {Boolean} [bAskToRemoveLock]
 * 
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"77B7B528-4A59-4A4A-96D1-907C7ECA3B9E"}
 */
function isRecordLocked(programName, record, bAskToRemoveLock) {
	var bIsRecordLocked = false;
	
	if (programName && record) {
		var pk = record.getPKs().pop(); 
		
		if (pk) {
			/***@type {JSRecord<db:/avanti/sys_record_lock>} */
			var rRecordLock = scopes.avDB.getRec('sys_record_lock', ['recordlock_program_name', 'recordlock_record_pk'], [programName, pk], null, null, true);

			if (rRecordLock && rRecordLock.recordlock_user_id != globals.user_id) {
				if (bAskToRemoveLock) {
					var sMessage = i18n.getI18NMessage("avanti.dialog.recordEditLock_msg2");
					scopes.globals.avSecurity_selectedUserID = rRecordLock.recordlock_user_id;
					var sUser = _to_sec_user$avsecurity_selecteduserid.user_name;

					if (utils.hasRecords(_to_sec_user$avsecurity_selecteduserid.sec_user_to_sys_employee)) {
						sUser = _to_sec_user$avsecurity_selecteduserid.sec_user_to_sys_employee.empl_full_name;	
					}
					
					sMessage = sMessage.replace("<<user>>", sUser);
					sMessage = sMessage.replace("<<user1>>", sUser);
					
		            var lockDateTime = new Date(rRecordLock.recordlock_created_date);
		            sMessage = sMessage.replace("<<date>>", plugins.DateUtils.dateFormat(lockDateTime,globals.avBase_dateFormat));
		            sMessage = sMessage.replace("<<time>>", plugins.DateUtils.dateFormat(lockDateTime,globals.avBase_timeFormat));
		            
					var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.recordLock_title"), sMessage, i18n.getI18NMessage("avanti.dialog.cancel"), i18n.getI18NMessage("avanti.dialog.RemoveLock"));

					if (sAns == i18n.getI18NMessage("avanti.dialog.RemoveLock")) {
						rRecordLock.foundset.deleteRecord(rRecordLock);
					}
					else {
						bIsRecordLocked = true;
					}
				}
				else {
					bIsRecordLocked = true;
				}
			}
		}
	}
	
	return bIsRecordLocked;
}

/**
 * Add User Record Lock
 *
 * <AUTHOR> Dol
 * @since Oct 29, 2014
 *
 * @param {String} programName - The name of the framework program
 * @param {JSRecord} record - The selected record
 * @param {String} lockType - 'D' - Delete Lock, 'E' - Edit Lock
 * @param {Boolean} [bSkipShowMsg]
 *
 * @public
 *
 * @returns {Boolean} Lock Acquired
 *
 *
 * @properties={typeid:24,uuid:"B6E81CDE-18C2-4608-B1FE-DD20CC561556"}
 */
function avLockRecord (programName, record, lockType, bSkipShowMsg) {
	
	var bLockAquired = false;
	var pk = record.getPKs().pop()  // I believe we are pretty safe in that we only use single primary keys.
	
	if (pk != null){
		
		//Check to see if a lock exists for the specified Program
		/***@type {JSFoundset<db:/avanti/sys_record_lock>} */
		var fsRecordLock = scopes.avDB.getFS('sys_record_lock', ['recordlock_program_name','recordlock_record_pk'], [programName, pk])
		var clientId = plugins.clientmanager.getClientInformation().getClientID();
		if (fsRecordLock.getSize() == 0)
		{
			// Create Record Lock
			fsRecordLock.newRecord();
			fsRecordLock.recordlock_program_name = programName;
			fsRecordLock.recordlock_record_pk = pk;
			fsRecordLock.recordlock_client_id = clientId;
			if (databaseManager.saveData(fsRecordLock)) {
				bLockAquired = true;
			}
			else{
				var fRecord = fsRecordLock.getSelectedRecord();
				application.output(fRecord.exception);
				if (record.exception.getErrorCode() === ServoyException.RECORD_VALIDATION_FAILED) {
					// exception thrown in pre-insert/update/delete event method
					application.output("Record validation failed: " + fRecord.exception.getMessage());
				} 
				// find out the table of the record (similar to getEditedRecords)
				var jstable = databaseManager.getTable(fRecord);
				var tableSQLName = jstable.getSQLName();
				application.output('Table:' + tableSQLName + ' in server:' + jstable.getServerName() + ' failed to save.')
			}
		}
		else
		{
			if (fsRecordLock.recordlock_client_id == clientId)  // current user already has the lock so allow continuation.
			{
				bLockAquired = true;
			}
			else if (!scopes.avSystem.isUserlessProcess() && !bSkipShowMsg)
			{
				//Show User Lock Dialog
				var sMessage = i18n.getI18NMessage("avanti.dialog.recordEditLock_msg")
				scopes.globals.avSecurity_selectedUserID = fsRecordLock.recordlock_user_id;
				var sUser = _to_sec_user$avsecurity_selecteduserid.user_name;

				if (utils.hasRecords(_to_sec_user$avsecurity_selecteduserid.sec_user_to_sys_employee)) {
					sUser = _to_sec_user$avsecurity_selecteduserid.sec_user_to_sys_employee.empl_full_name;
				}
				
				sMessage = sMessage.replace("<<user>>",sUser);
				sMessage = sMessage.replace("<<user1>>",sUser);
				sMessage = sMessage.replace("<<clientID>>",fsRecordLock.recordlock_client_id.toString());

				var lockDateTime = new Date(fsRecordLock.recordlock_created_date);
	            sMessage = sMessage.replace("<<date>>", plugins.DateUtils.dateFormat(lockDateTime,globals.avBase_dateFormat));
	            sMessage = sMessage.replace("<<time>>", plugins.DateUtils.dateFormat(lockDateTime,globals.avBase_timeFormat));
				
				var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.recordLock_title"), sMessage, i18n.getI18NMessage("avanti.dialog.cancel"), i18n.getI18NMessage("avanti.dialog.overrideLock"));

				if (sAns == i18n.getI18NMessage("avanti.dialog.overrideLock")) // User can override lock
				{
					//Update the record lock for current user
					fsRecordLock.recordlock_user_id = globals.user_id;
					fsRecordLock.recordlock_client_id = clientId;
					fsRecordLock.recordlock_created_date = application.getServerTimeStamp();
					databaseManager.saveData(fsRecordLock);
					bLockAquired = true;
				}
			}
		}
	}
	return bLockAquired;
}

/**
 * Remove The Record Lock
 *
 * <AUTHOR> Dol
 * @since Oct 29, 2014
 *
 * @param {String} programName - The name of the framework program	
 *
 * @public 
 * 
 * @returns {Boolean} Lock Removed
 *
 * @properties={typeid:24,uuid:"7A329014-5A63-4CED-BE2F-BD65F4529B1C"}
 */
function avUnLockRecord (programName) {
	
	var bLockRemoved = true;
			
    scopes.avDB.deleteRecs('sys_record_lock',['recordlock_program_name','recordlock_client_id'],[programName, plugins.clientmanager.getClientInformation().getClientID()])
		
	return bLockRemoved
}


/**
 * Remove All User Record Locks
 *
 * <AUTHOR> Dol
 * @since Oct 29, 2014
 *
 * @public 
 *
 * @properties={typeid:24,uuid:"A8ABD2A8-DA58-4BA4-B090-392C13A64E0D"}
 */
function avUnLockAllUserRecords () {
	scopes.avDB.deleteRecs('sys_record_lock',['recordlock_client_id'],[plugins.clientmanager.getClientInformation().getClientID()])
}

/**
 * @param {JSRecord} record
 *
 * @return
 * @properties={typeid:24,uuid:"0F3218ED-611F-42CF-AE59-E842EE807921"}
 */
function getFsIdx(record){
	if(record && record.getPKs()[0]){
		record.foundset.selectRecord(record.getPKs()[0])
		return record.foundset.getSelectedIndex()
	}
	else{
		return null
	}	
}

/**
 * @param {JSRecord} record
 * @param {String} [sortString]
 *
 * @return
 * @properties={typeid:24,uuid:"219A221C-863F-4FEC-94B7-4C8AABAD7DC7"}
 */
function getPrevRec(record, sortString){
	if(sortString){
		record.foundset.sort(sortString)
	}
	
	var thisIdx = getFsIdx(record)
	
	if(thisIdx > 1){
		return record.foundset.getRecord(thisIdx-1)
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord} record
 *
 * @return
 * @properties={typeid:24,uuid:"2B1C88E2-BAD4-4350-9672-1DBBD9E4F373"}
 */
function getNextRec(record){
	var thisIdx = getFsIdx(record)
	
	if(thisIdx < record.foundset.getSize()){
		return record.foundset.getRecord(thisIdx+1)
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord} r1
 * @param {JSRecord} r2
 *
 * @return
 * @properties={typeid:24,uuid:"37E217FD-B909-4FB8-BFAA-35827DD1C954"}
 */
function isSameRec(r1, r2){
	if(r1.getPKs()[0] == r2.getPKs()[0]){
		return true
	}
	else{
		return false
	}
}

/**
 * @param {JSRecord} rec
 *
 * @return
 * @properties={typeid:24,uuid:"BB468DAC-CD71-4D46-86FC-E4CC25FD411A"}
 */
function duplicateRecord(rec){
	if(rec){
		rec.foundset.selectRecord(rec.getPKs())
		rec.foundset.duplicateRecord(rec.foundset.getSelectedIndex(), false, true) 
		return rec.foundset.getSelectedRecord()
	}
	else{
		return null;
	}
}

/**
 * @param sTable
 *
 * @return
 * @properties={typeid:24,uuid:"90768E52-91F2-4FBC-95BF-93A9771391C2"}
 */
function getNewRec(sTable){
	/** @type {JSFoundSet} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable)
	return fs.getRecord(fs.newRecord()) 
}

/**
 * @param {JSFoundset} fs
 * @param {String} colName
 *
 * @return
 * @properties={typeid:24,uuid:"F0A7F1C6-5F6A-4C86-B023-44445D9DD832"}
 */
function getMaxFromFS(fs, colName){
	if(utils.hasRecords(fs)){
		var fs2 = fs.duplicateFoundSet()
		fs2.sort(colName + ' desc')
		fs2.setSelectedIndex(1)
		return fs2.getDataProviderValue(colName)
	}

	return 0
}

/**
 * @param {String} sTable
 * @param {Array<String>} aSearchColNames
 * @param {Array<String|UUID|Number>} aSearchColValues
 * @param {String} colName
 * 
 * @return
 * @properties={typeid:24,uuid:"F90E8729-0281-42A8-95B9-A8E64BA11966"}
 */
function getMax(sTable, aSearchColNames, aSearchColValues, colName){
	var fs = getFS(sTable, aSearchColNames, aSearchColValues, colName + ' desc');
	
	if(fs && fs.getSize() > 0){
		fs.setSelectedIndex(1);
		
		return fs.getDataProviderValue(colName); 
	}
	
	return 0;
}

/**
 * @param {JSFoundset} rel
 * @param colName
 *
 * @return
 * @properties={typeid:24,uuid:"7D0F08AB-7EF6-4E78-9784-94364AA2FE6B"}
 */
function getShallowRelVal(rel, colName){
	if(utils.hasRecords(rel)){
		return rel.getDataProviderValue(colName)
	}
	else{
		return null
	}
}

/**
 * @param {JSRecord} rec
 * @param {String} rel
 * @param {String} colName
 *
 * @return
 * @properties={typeid:24,uuid:"9A085164-5E62-450A-ACB3-1E5787C0C7C3"}
 */
function getDeepRelVal(rec, rel, colName){
	if(rec && rel && utils.hasRecords(rec, rel)){
		return rec[rel].getDataProviderValue(colName)
	}
	else{
		return null
	}
}

/**
 * @public 
 * 
 * @param sSQL
 * @param aArgs
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"82A7DF95-F3D6-494C-A917-************"}
 */
function hasData(sSQL, aArgs) {
	/***@type {{sql:String, args:Array}}*/
	var oSQL = new Object()

	oSQL.sql = sSQL
	oSQL.args = aArgs;

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	
	if (dsData && dsData.getMaxRowIndex() > 0) {
		return true;
	}
	else {
		return false;
	}
}


/**
 * @param {String} sSQL
 * @param {Boolean} [bAddOrgIDCondition]
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * @param {String} [sOrgIDPrefix]
 * @param {Number} [numColsToReturn]
 * @param {String} [sOrderBy]
 * @param {Boolean} [bMultipleRecs]
 *
 * @return
 * @properties={typeid:24,uuid:"07E0A6FD-1A2C-4F59-86F5-E5F92947471F"}
 */
function SQLQuery(sSQL, bAddOrgIDCondition, aArgs, sServer, sOrgIDPrefix, numColsToReturn, sOrderBy, bMultipleRecs) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object()

	if (bAddOrgIDCondition) {
		var orgClause = ''
		if (sOrgIDPrefix) {
			orgClause = sOrgIDPrefix + ".org_id = '" + globals.org_id + "'"
		} else {
			orgClause = "org_id = '" + globals.org_id + "'"
		}

		if (sSQL.indexOf('where ') > -1) {
			sSQL += " and " + orgClause
		} else {
			sSQL += " where " + orgClause
		}
	}

	if(sOrderBy){
		sSQL += ' order by ' + sOrderBy
	}
	
	oSQL.sql = sSQL

	if (sServer) {
		oSQL.server = sServer
	} else {
		oSQL.server = globals.avBase_dbase_avanti
	}

	if (aArgs) {
		var iIndex = 0;
        for (iIndex = 0; iIndex < aArgs.length; iIndex++) {
        	if (aArgs[iIndex] && aArgs[iIndex].toString() == 'none') {
                aArgs[iIndex] = scopes.avUtils.ENUM_DEFAULT_GUID.Default;
            }
        } 
		
		oSQL.args = aArgs;
	}

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0) {
		/** @type {Array} */
		var taColVals;

		if(numColsToReturn){
			var aReturn = [];
			
			for(var i=1;i<=numColsToReturn;i++){
				taColVals = dsData.getColumnAsArray(i);
				aReturn.push(taColVals[0]);
			}
			
			return aReturn;
		}
		else{
			taColVals = dsData.getColumnAsArray(1);

			if(bMultipleRecs){
				return taColVals;
			}
			else{
				return taColVals[0];
			}
		}
	}

	return null;
}

/**
 * @param {String} sSQL
 * @param {Boolean} [bAddOrgIDCondition]
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * @param {String} [sOrgIDPrefix]
 *
 * @return
 * @properties={typeid:24,uuid:"BCA34E30-18AB-467F-8FFB-AAFC050F74B2"}
 */
function SQLExists(sSQL, bAddOrgIDCondition, aArgs, sServer, sOrgIDPrefix) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();

	if (bAddOrgIDCondition) {
		var orgClause = '';
		if (sOrgIDPrefix) {
			orgClause = sOrgIDPrefix + ".org_id = '" + globals.org_id + "'";
		} else {
			orgClause = "org_id = '" + globals.org_id + "'";
		}

		if (sSQL.indexOf('where ') > -1) {
			sSQL += " and " + orgClause;
		} else {
			sSQL += " where " + orgClause;
		}
	}

	oSQL.sql = sSQL;

	if (sServer) {
		oSQL.server = sServer;
	} else {
		oSQL.server = globals.avBase_dbase_avanti;
	}

	if (aArgs) {
		oSQL.args = aArgs;
	}

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	if (dsData && dsData.getMaxRowIndex() > 0) {
		return true;
	}

	return false;
}

/**
 * @param {String} sSQL
 * @param {Boolean} [bAddOrgIDCondition]
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * @param {String} [sOrgIDPrefix]
 * 
 * @return {JSDataSet}
 *
 * @properties={typeid:24,uuid:"2CB931A0-1DDE-4F34-BC9B-BFD4E278295C"}
 */
function SQLQueryDataset(sSQL, bAddOrgIDCondition, aArgs, sServer, sOrgIDPrefix) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object()

	if (bAddOrgIDCondition) {
		var orgClause = ''
		if (sOrgIDPrefix) {
			orgClause = sOrgIDPrefix + ".org_id = '" + globals.org_id + "'"
		} else {
			orgClause = "org_id = '" + globals.org_id + "'"
		}

		if (sSQL.indexOf('where ') > -1) {
			sSQL += " and " + orgClause
		} else {
			sSQL += " where " + orgClause
		}
	}

	oSQL.sql = sSQL

	if (sServer) {
		oSQL.server = sServer
	} else {
		oSQL.server = globals.avBase_dbase_avanti
	}

	if (aArgs) {
		oSQL.args = aArgs
	}

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	return dsData;
}


/**
 * Write Audit Log
 *
 * <AUTHOR> Dol
 * @since Jun 22, 2015
 *
 * @param {Object} oLogEntry Object containing log information
 * @param {String} oLogEntry.serverName Server Name - Example 'avanti', 'svy_framework'
 * @param {String} oLogEntry.tableName Table Name
 * @param {String} oLogEntry.fieldName Field Name
 * @param {String} oLogEntry.action Database Action - 1=Insert, 2=Update, 3=Delete, 4=View
 * @param {String} oLogEntry.source Audit Source - Can be method name or some other appropriate reference
 * @param {String} oLogEntry.primaryKey Primary Key Data
 * @param {String} oLogEntry.oldData Original Data
 * @param {String} oLogEntry.newData New Data
 * @param {String} oLogEntry.documentType Type of Document - EST=Estimate, ORD=Sales Order, INV=Sales Invoice, PO=Purchase Order
 * @param {String} oLogEntry.documentNumber
 * @param {String} oLogEntry.org_id - Will use global unless passed in.
 * @param {String} oLogEntry.programName - The framework program name. Will use global is not passed.
 * 
 * @public 
 * 
`* @return {{status:String, message:String}} result Object containing run status information.		
 *
 * @properties={typeid:24,uuid:"F1865365-2502-4FAA-848D-990323CA6CF8"}
 */
function writeAuditLog (oLogEntry) {
	
	var result = {status:'', message:''};
	
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	var dEventTime = application.getServerTimeStamp();
	var nMonth = dEventTime.getMonth() + 1
	var dEventTimeFormatted = dEventTime.getFullYear() + "-" + nMonth + "-" + dEventTime.getDate() + " " + dEventTime.getHours() + ":" + dEventTime.getMinutes() + ":" + dEventTime.getSeconds() + "." + dEventTime.getMilliseconds();
	
	var sOrgID = oLogEntry.org_id;
	if (sOrgID == null || sOrgID == '') sOrgID = globals.org_id;
	
	var sProgramName = oLogEntry.programName;
	if (sProgramName == null || sProgramName == '') sProgramName = globals.nav.program;
	
	if (oLogEntry.serverName == null) oLogEntry.serverName = globals.avBase_dbase_avanti;
	
	if (oLogEntry.serverName == null || oLogEntry.tableName == null || oLogEntry.fieldName == null || oLogEntry.action == null ||
			oLogEntry.source == null || oLogEntry.primaryKey == null){
				result.status = '0';
				result.message = 'Failed inserting audit log record, missing required fields';
				return result;
			}
	
	var sEmplName = _to_sec_user$user_id.user_name;
	if (utils.hasRecords(_to_sys_employee$avbase_employeeuuid)) sEmplName = _to_sys_employee$avbase_employeeuuid.empl_full_name;
	if (globals.avBase_employeeUUID == null || globals.avBase_employeeUUID == '') globals.avBase_employeeUUID = scopes.avUtils.ENUM_DEFAULT_GUID.Default;
	
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.sql = "INSERT INTO db_audit_log (auditlog_server_name,\
					auditlog_table_name,\
					auditlog_field_name,\
					auditlog_action,\
					auditlog_pk_data, \
					auditlog_old_data,\
					auditlog_new_data,\
					auditlog_event_time,\
					auditlog_empl_id,\
					auditlog_document_type, \
					auditlog_document_number, \
					auditlog_source, \
					org_id, \
					auditlog_program_name, \
					auditlog_empl_name)\
				VALUES ( "
					+ "'" + oLogEntry.serverName + "','" + oLogEntry.tableName + "','" + oLogEntry.fieldName + "','" + oLogEntry.action + "','" + oLogEntry.primaryKey + "'," 
					+ "NULLIF ('" + oLogEntry.oldData + "','') " + "," + "NULLIF( '" + oLogEntry.newData + "','') " + ",'" + dEventTimeFormatted + "'," + "NULLIF('" +  globals.avBase_employeeUUID.toString() + "','') " + ",'" +  oLogEntry.documentType + "'," 
					+ "NULLIF ('" + oLogEntry.documentNumber + "','') " + "," + "NULLIF( '" + oLogEntry.source + "','')" + ",'" + sOrgID + "'," + "NULLIF ('" + sProgramName + "','') " + ",'" + sEmplName + "'"+ ")" ;
		
	if (!globals["avUtilities_sqlRaw"](oSQL)) { 
		//avUtilities_sqlRaw returned false, so we want to return an error status (2) 
		// and a message.
		result.status = '0';
		result.message = 'Error Writing log Entry: ' + oSQL;
		return result;
	}
	
	//all is good, return a success status (1).
	result.status = '1';
	result.message = 'Successfully Inserted Log Record';
	return result;	
}	

/**
 * @param {JSFoundset} fs
 * @param {Array<String>} keyCols
 * @param {Array<String>} updateExcludeCols
 * @param {String} [sInsertOrUpdate]
 * @param {Boolean} [bCreateNewGUIDForPK]
 * @param {String} [sPKColName]
 * @param {String} [sParentKeyCol]
 * @param {String|UUID} [sParentKeyVal]
 * @param {Boolean} [bAddBroadcastOutputStatement] 
 *
 * @return {{sql:String, args:Array, server:String, maxRows:Number, table:String}}
 * 
 * @properties={typeid:24,uuid:"BAD9F45A-CD78-410E-A298-CF43D0E50585"}
 */
function createWriteSQLFromRec(fs, keyCols, updateExcludeCols, sInsertOrUpdate, bCreateNewGUIDForPK, sPKColName, sParentKeyCol, sParentKeyVal, bAddBroadcastOutputStatement) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };

	if (fs && fs.getSize() > 0) {
		var aDatasource = getDatasource(fs);
		var aColumnNames = getColumnNames(fs);
		var column, typeName, val;

		if (aDatasource.length == 2 && aColumnNames.length > 0) {
			var db = aDatasource[0];
			var table = aDatasource[1];
			var oTable = databaseManager.getTable(fs.getDataSource());
			var sBroadcastOutputStatement = "";

			oSQL.server = db;
			oSQL.args = [];

			var bRecExists = false;

			if (sInsertOrUpdate == 'U') {
				bRecExists = true;
			}
			else if (sInsertOrUpdate == 'I') {
				bRecExists = false;
			}

			if (bAddBroadcastOutputStatement) {
				var aPKCols = oTable.getRowIdentifierColumnNames();
			
				if (aPKCols && aPKCols.length == 1) {
					if (bRecExists) {
						sBroadcastOutputStatement = createBroadcastOutputStatement(table, aPKCols[0], scopes.avUtils.e_spBroadcastRecordAction.UPDATE_ACTION);
					}
					else {
						sBroadcastOutputStatement = createBroadcastOutputStatement(table, aPKCols[0], scopes.avUtils.e_spBroadcastRecordAction.INSERT_ACTION);
					}
				}
			}
			
			if (sInsertOrUpdate != 'I' && keyCols && keyCols.length > 0) {
				var whereClause = '';
				var whereClauseArgs = [];

				for (i = 0; i < keyCols.length; i++) {
					if (i > 0) {
						whereClause += ' and ';
					}
					whereClause += keyCols[i] + ' = ? ';

					column = oTable.getColumn(keyCols[i])
					typeName = column.getTypeAsString()
					val = fs.getDataProviderValue(keyCols[i]);
					if (typeName == 'TEXT' && val != null) {
						val = val.toString();
					}

					whereClauseArgs.push(val);
				}

				// make sure org_id there
				if (db == globals.avBase_dbase_avanti && keyCols.indexOf('org_id') == -1) {
					whereClause += " and org_id = ?";
					whereClauseArgs.push(globals.org_id);
				}

				if (!sInsertOrUpdate) {
					bRecExists = SQLQuery('select count(*) from ' + table + ' where ' + whereClause, false, whereClauseArgs) > 0;
				}
			}

			if (bRecExists) {
				oSQL.sql = "UPDATE " + table + " SET ";
			}
			else {
				oSQL.sql = "INSERT INTO " + table + "(" + scopes.avText.arrayToString(aColumnNames, ',') + ") " + sBroadcastOutputStatement + " VALUES (";
			}
			
			var colsAdded = 0;
			for (var i = 0; i < aColumnNames.length; i++) {
				var sColName = aColumnNames[i];
				
				if (updateExcludeCols.indexOf(sColName) == -1) {
					var bAddCol = true;
					
					if (bCreateNewGUIDForPK && sColName == sPKColName) {
						val = application.getUUID();
					}
					else if (sParentKeyCol && sParentKeyVal && sColName == sParentKeyCol) {
						val = sParentKeyVal;
					}
					else {
						val = fs.getDataProviderValue(sColName);

						// sl-19439 - if doing a customer import and the fs col val is null, make this is a column that we update from import. if not then we never set the fs col val. 
						// dont update col or we will wipe out existing col val.						
						if (bRecExists && val == null && table == 'sa_customer' && forms['utils_dataImport_dtl'].aCustImportDBCols) {
							bAddCol = (forms['utils_dataImport_dtl'].aCustImportDBCols.indexOf(sColName) > -1);
						}
					}

					if (bAddCol) {
						column = oTable.getColumn(sColName)
						typeName = column.getTypeAsString()

						if (typeName == 'TEXT' && val != null) {
							val = val.toString();
						}

						if (colsAdded > 0) {
							oSQL.sql += ',';
						}
						
						colsAdded++;

						if (bRecExists) {
							oSQL.sql += sColName + ' = ?';
						}
						else {
							oSQL.sql += '?';
						}
						
						oSQL.args.push(val);
					}
				}
			}

			if (bRecExists) {
				oSQL.sql += sBroadcastOutputStatement;
				oSQL.sql += " where " + whereClause;
				oSQL.args = oSQL.args.concat(whereClauseArgs);
			}
			else {
				oSQL.sql += ')';
			}

			oSQL.sql += '\n';
		}
	}

	return oSQL;
}


/**
 * @param {JSFoundset} fs
 * @param {Array<String>} keyCols
 * @param {Array<String>} updateExcludeCols
 * @param {String} [sInsertOrUpdate]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"9D801E50-FE31-46AB-A719-F20F987FD3B4"}
 */
function writeRecWithSQL(fs, keyCols, updateExcludeCols, sInsertOrUpdate){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = createWriteSQLFromRec(fs, keyCols, updateExcludeCols, sInsertOrUpdate)

	if (oSQL && oSQL.sql && globals["avUtilities_sqlRaw"](oSQL)) {
		return true;
	}
	
	return false;
}

/**
 * @public 
 * 
 * @param {JSFoundset} fs - inserts selected record in this foundset using sql
 * @param {Boolean} [bClearRecAfterInsert] - clears record after insert. if using, make sure you dont need any values from fs after this call
 * @param {Boolean} [bAddBroadcastOutputStatement] 
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"CD06D912-41B7-4D7A-BDF6-B094EE0B1EB7"}
 */
function insertRecWithSQL(fs, bClearRecAfterInsert, bAddBroadcastOutputStatement) {
	var bSuccess = false;
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = createWriteSQLFromRec(fs, [], [], "I", null, null, null, null, bAddBroadcastOutputStatement);

	if (oSQL && oSQL.sql && globals["avUtilities_sqlRaw"](oSQL)) {
		bSuccess = true;
	}
	
	if (bClearRecAfterInsert) {
		fs.getSelectedRecord().revertChanges();
	}
	
	return bSuccess;
}

/**
 * @public 
 * 
 * @param {JSFoundset} fs - updates selected record in this foundset using sql
 * @param {Array<String>} keyCols - an array of column names used to select record to update using sql
 * @param {Boolean} [bClearRecAfterUpdate] - clears record after update. if using, make sure you dont need any values from fs after this call 
 * @param {Boolean} [bAddBroadcastOutputStatement] 
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"03500DD0-755E-4B06-B5E9-FAF317C0FCC2"}
 */
function updateRecWithSQL(fs, keyCols, bClearRecAfterUpdate, bAddBroadcastOutputStatement) {
	var bSuccess = false;
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = createWriteSQLFromRec(fs, keyCols, [], "U", null, null, null, null, bAddBroadcastOutputStatement);

	if (oSQL && oSQL.sql && globals["avUtilities_sqlRaw"](oSQL)) {
		bSuccess = true;
	}
	
	if (bClearRecAfterUpdate) {
		fs.getSelectedRecord().revertChanges();
	}
	
	return bSuccess;
}

/**
 * @param {JSFoundset} [fs]
 * @param {JSRecord} [rec]
 * 
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"ED4C5EE0-F6D8-41C4-B6B0-BE9F9DC7821F"}
 */
function getColumnNames(fs, rec){
	var columnNames = [];
	var aDataSource = getDatasource(fs, rec);

	if(aDataSource.length == 2){
		var jsTable = databaseManager.getTable(fs.getDataSource())
		columnNames = jsTable.getColumnNames();
		
		//for(var i=columnNames.length; i>0; i--){
        	//if((/.*_ci$/.test( columnNames[i] ))){
        	//	columnNames.pop();
    	 	//}
	    //}
	}
	
	return columnNames;
}

/**
 * @param {String} sTable
 * @param {Array<String>} [aExcludeCols]
 *
 * @return
 * @properties={typeid:24,uuid:"854610DA-3696-4DF9-8E43-D2EFE880C21D"}
 */
function getColumnNamesFromTable(sTable, aExcludeCols){
	var jsTable = databaseManager.getTable('db:/avanti/' + sTable);
	var aCols;
	
	if(aExcludeCols && aExcludeCols.length > 0){
	     aCols = jsTable.getColumnNames();
	     
	     for(var i=aCols.length; i>0; i--){
	         if(aExcludeCols.indexOf(aCols[i]) > -1){
	        	 aCols.pop();
	         } else {
	        	 //if((/.*_ic$/.test( aCols[i] ))){
	        		 aCols.pop();
	        	 //} 
	         }
	     }
	     
	     return aCols;
	}
	else{
		aCols = jsTable.getColumnNames();
		
		//for(var i=aCols.length; i>0; i--){
        	//if((/.*_ci$/.test( aCols[i] ))){
        	//	aCols.pop();
    	 	//}
	    //}
	    return aCols;
	}
}

/**
 * @param {String} sTable
 * @param {Array<String>} [aExcludeCols]
 *
 * @return
 * @properties={typeid:24,uuid:"83FE9916-65C3-4379-909A-5BC25E4C156B"}
 */
function createInsertColString(sTable, aExcludeCols){
    var aCols = getColumnNamesFromTable(sTable, aExcludeCols);
    
    if(aCols && aCols.length > 0){
       if(aCols.length == 1){
           return aCols[0];
       }
       else{
           return scopes.avText.arrayToString(aCols, ',');
       }
    }
    else{
       return null;
    }    
}

/**
 * @param {JSFoundset} [fs]
 * @param {JSRecord} [rec]
 * 
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"C397CCEB-20EB-4A57-AA2F-215A98EB77B2"}
 */
function getDatasource(fs, rec){
	var aDataSource = [];
	var sDataSource = null; // eg. db:/avanti/sa_customer_contact

	if(fs){
		sDataSource = fs.getDataSource();
	}
	else if(rec){
		sDataSource = rec.getDataSource();
	}
	
	if(sDataSource && sDataSource.indexOf('/') > -1){
		var aDataSource2 = sDataSource.split('/');
		if(aDataSource2.length == 3){
			var db = aDataSource2[1];
			var table = aDataSource2[2];
			aDataSource.push(db);
			aDataSource.push(table);
		}
	}
	
	return aDataSource;
}

/**
 * @param {JSFoundset} [fs]
 * @param {JSRecord} [rec]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C43FD967-AB6A-48A0-9328-8B50AB145878"}
 */
function getTableName(fs, rec){
	var tableName = '';
	var sDataSource = null; // eg. db:/avanti/sa_customer_contact

	if(fs){
		sDataSource = fs.getDataSource();
	}
	else if(rec){
		sDataSource = rec.getDataSource();
	}
	
	if(sDataSource && sDataSource.indexOf('/') > -1){
		var aDataSource2 = sDataSource.split('/');
		if(aDataSource2.length == 3){
			tableName = aDataSource2[2];
		}
	}
	
	return tableName;
}

/**
 * @param {String} tableName
 * @param {String} [serverName]
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"C3209AF2-A909-4ADC-8FE8-00D55DC5328A"}
 */
function newRecord(tableName, serverName){
	if(!serverName){
		serverName = globals.avBase_dbase_avanti;
	}
	
	var fs = databaseManager.getFoundSet(serverName, tableName);
	return fs.getRecord(fs.newRecord());
}


/**
 * This function checks if the edited record is locked by other clients or not
 * @param {JSClientInformation} currentClient
 * @param {JSRecord} editedRecord
 * @return {String}
 *
 * @properties={typeid:24,uuid:"3945A8C6-F41F-4220-B042-894DF86437D7"}
 */
function getRecordLockingClientID(currentClient, editedRecord){
	var lockingClient = plugins.clientmanager.getLockedByClient(editedRecord.getDataSource(),editedRecord.getPKs());
	//Check if the record is not locked by the current client
	if(lockingClient && (currentClient.getClientID()!=lockingClient.getClientID())){
		application.output('Locked by another client :'+lockingClient, LOGGINGLEVEL.DEBUG);
		//Need to add wait time and retry logic here
		return lockingClient.getClientID();
	}
	return currentClient.getClientID();
}

/**
 * This function should be called to wait (not to obtain lock) for locks to be cleared, only in cases where the records were edited but no critical part of the record was edited
 * e.g. client 1 increased item qty and placed a record lock on item, client 2 updated item description, client 2 has to call this function to wait for client 1 to release lock
 * In case client 2 is also trying to update a critical value like qty increase, it must call waitAndAcquireLock before increasing the qty, 
 * otherwise, client 2 will not get updated change from client 1 and endup overwriting client 1's changes
 * 
 * @param {JSRecord} record
 *
 * @properties={typeid:24,uuid:"C8E544F2-4C85-46B1-BF97-3198A4F37149"}
 */
function waitForRecordLockRelease(record) {
	// Get retry count from user property
	try {
		var retry = application.getUserProperty('db.maxRetryCountForDBFailures');
		if (retry && !scopes.avMath.isNumber(retry)) {
			retry = null;
		}
		/***@type{Number} */
		const MAX_RETRY_COUNT = retry ? retry : 3;

		// Get sleep time from user property
		var sleepTime = application.getUserProperty('db.retryIntervalInMilliseconds');
		if (sleepTime) {
			if (!scopes.avMath.isNumber(sleepTime)) {
				sleepTime = null;
			} else {
				// Convert to number for switch comparison
				sleepTime = Number(sleepTime);
				//Locking has been disabled explicitly in the system
				if (sleepTime < -5) {
					application.output('Warning: Invalid sleepTime value ' + sleepTime + '. Supported values are -1 to -5. Using default locking behavior.', LOGGINGLEVEL.WARNING);
					sleepTime = null;
				} else {
					var dataSource = record.getDataSource();
					switch (sleepTime) {
					case -1: // no locking for sa_order_revd_item table
						if (dataSource === 'db:/avanti/sa_order_revd_item') {
							return;
						}
						break;
					case -2: // no locking for sa_order_revd_item and sa_order_revision_detail tables
						if (dataSource === 'db:/avanti/sa_order_revd_item' || dataSource === 'db:/avanti/sa_order_revision_detail') {
							return;
						}
						break;
					case -3: //only locking the item tables - as introduced from before 1.0.2025
						if (dataSource === 'db:/avanti/sa_order_revd_item' || dataSource === 'db:/avanti/sa_order_revision_detail' || dataSource === 'db:/avanti/in_committment') {
							return;
						}
						break;
					case -4: // no locking for sa_order_revision_detail table
						if (dataSource === 'db:/avanti/sa_order_revision_detail') {
							return;
						}
						break;
					case -5: //no locking at all- not recommended
						return;
					}
				}
			}
		}
		
		
		var currentClient = plugins.clientmanager.getClientInformation();

		/***@type{Number} */
		const SLEEP_TIME = (sleepTime && sleepTime > 0) ? sleepTime : 50;
		
		var retryCounter = 0;
		var locked = false;
		var locks = plugins.clientmanager.getLocks();

		if (locks.getMaxRowIndex() > 0) {
			var currentSleepTime = SLEEP_TIME;
			do {
				var lockingClient = plugins.clientmanager.getLockedByClient(record.getDataSource(), record.getPKs());

				// Check if the record is not locked by the current client
				if (lockingClient && (currentClient.getClientID() != lockingClient.getClientID())) {
					locked = true;
					application.output('Waiting for Client: ' + lockingClient.getClientID() + ' to release a lock on Record: ' + record.getPKs(),
						LOGGINGLEVEL.DEBUG);

					application.sleep(currentSleepTime);
					currentSleepTime *= 2;
					if (currentSleepTime > 1000) {
						currentSleepTime = 1000;
					}
					retryCounter++;
				} else {
					locked = false;
				}
			} while (locked && retryCounter < MAX_RETRY_COUNT);
		}
	} catch (e) {
		application.output('Exception in waitForRecordLockRelease',LOGGINGLEVEL.FATAL);
	}
}

/**
 * @param {String} callingLocation - where this func is being called from - be as detailed as needed - eg. utils_dataImport_dtl.processCustomer.1
 * @param {JSFoundset} [fs]
 * @param {JSRecord} [rec]
 *
 * @properties={typeid:24,uuid:"FB76945E-17BD-4E8E-9C87-1143A9CF8AFD"}
 */
function saveData(callingLocation, fs, rec){
	try{
		var bSuccess;
		
		if(fs){
			bSuccess = databaseManager.saveData(fs);
		}
		else if(rec){
			bSuccess = databaseManager.saveData(rec);
		}
		else{
			bSuccess = databaseManager.saveData();
		}
		
		if(!bSuccess){
			logDbErr(callingLocation, fs, rec, true);
		}
	}
	catch(ex){
		logDbErr(callingLocation, fs, rec, true);
	}
}

/**
 * @param {String} callingLocation - where this func is being called from - be as detailed as needed - eg. utils_dataImport_dtl.processCustomer.1
 * @param {JSFoundset} [fs]
 * @param {JSRecord} [rec]
 * @param {Boolean} [bRevertFailedRecs]
 *
 * @properties={typeid:24,uuid:"E26C6A76-5217-48A4-8CA4-8876AC9ECFB4"}
 */
function logDbErr(callingLocation, fs, rec, bRevertFailedRecs){
	var aFailedRecs = databaseManager.getFailedRecords();
	
	if(aFailedRecs.length > 0){
		for(var i=0;i<aFailedRecs.length;i++){
			var recNum = i+1;
			var msg = '!SAVEDATA FAILED!. callingLocation: ' + callingLocation + '. rec ' + recNum + ': ' + aFailedRecs[i]
			scopes.avUtils.log(msg, scopes.avUtils.LOGGER.System, LOGGINGLEVEL.ERROR);
		}

		if(bRevertFailedRecs){
			if(fs){
				databaseManager.revertEditedRecords(fs);
			}
			else if(rec){
				rec.revertChanges();
			}
			else{
				databaseManager.revertEditedRecords();
			}
		}
	}
}


/**
 * @param {String} purge_period
 * @param {String} orgId
 * @param {Boolean} bAutomaticMode
 * @param {Boolean} [bRequestFromUI]
 *
 * @properties={typeid:24,uuid:"E5FF1724-3026-4B02-AD4F-6901FE444158"}
 */
function purgeDBLog(purge_period, orgId, bAutomaticMode, bRequestFromUI) {
	var dPurgeDate = scopes.avDate.getDateFromPeriod(purge_period);

	if (!dPurgeDate) {
		return;
	}
	var oSQL = new Object();
	oSQL.args = [];

	//Only deleting top 50,000 rows in each execution to avoid locking the table for too long
	//delete sys_integration_error

	if (orgId) {
		oSQL.sql = 'delete TOP(50000) from sys_integration_error where org_id = \'' + orgId + '\' and created_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\'';
	} else {
		oSQL.sql = 'delete TOP(50000) from sys_integration_error where org_id is null and created_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\'';

	}

	if (!globals["avUtilities_sqlRaw"](oSQL)) {
		if (bRequestFromUI) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), 'Could not purge db_log_details data.', i18n.getI18NMessage('avanti.dialog.okay'));
		} else {
			globals.dbLog('Deleting db log details failed', 'log_delete', null, null, null, orgId, null, null, null, null, null, null);
		}
		return;
	}

	//delete db_log_details

	if (orgId) {
		oSQL.sql = 'delete TOP(50000) from db_log_details where db_log_id in (select db_log_id from db_log where org_id = \'' + orgId + '\' and db_log_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\')';
	} else {
		oSQL.sql = 'delete TOP(50000) from db_log_details where db_log_id in (select db_log_id from db_log where org_id is null and db_log_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\')';

	}

	if (!globals["avUtilities_sqlRaw"](oSQL)) {
		if (bRequestFromUI) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), 'Could not purge db_log_details data.', i18n.getI18NMessage('avanti.dialog.okay'));
		} else {
			globals.dbLog('Deleting db log details failed', 'log_delete', null, null, null, orgId, null, null, null, null, null, null);
		}
		return;
	}
	//delete db_log

	if (orgId) {
		oSQL.sql = 'delete TOP(50000) from db_log where org_id = \'' + orgId + '\' and db_log_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\'';
	} else {
		oSQL.sql = 'delete TOP(50000) from db_log where org_id is null and db_log_date <  \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\'';
	}

	if (!globals["avUtilities_sqlRaw"](oSQL)) {
		if (bRequestFromUI) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), 'Could not purge db_log data.', i18n.getI18NMessage('avanti.dialog.okay'));
		} else {
			globals.dbLog('Deleting db log failed', 'log_delete', null, null, null, orgId, null, null, null, null, null, null);
		}
		return;
	}
}

/**
 * @param {String} purgePeriod
 * @param {String} orgId
 * @param {Boolean} [bRequestFromUI]
 *
 * @properties={typeid:24,uuid:"B67BAC87-174D-4710-AB93-2F9BA701A705"}
 */
function purgeDBHistoryRecords(purgePeriod, orgId, bRequestFromUI) {
	var dPurgeDate = scopes.avDate.getDateFromPeriod(purgePeriod);
	
	if (!dPurgeDate) {
		return;
	}
	var oSQL = new Object();
	oSQL.sql = 'delete TOP(50000) from audit_history where org_id = \'' + orgId + '\' and created_date < \'' + utils.dateFormat(dPurgeDate, 'yyyy-MM-dd') + '\'';
	oSQL.args = [];

	if (!globals["avUtilities_sqlRaw"](oSQL)) {
		if (bRequestFromUI) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), 'Could not purge history records  data.', i18n.getI18NMessage('avanti.dialog.okay'));
		}
		else{
			globals.dbLog('Deleting audit log failed', 'log_delete', null, null, null, orgId, null, null, null, null, null, null);
		}
		return;
	}
}

/**
 * @param {String} sTable
 * @param {String} sPKColName
 * @param {String|UUID} [sPKID] - for copying single rec based on pk
 * @param {String} [sFKColName] - for copying multiple recs based on fk
 * @param {String|UUID} [sOldFKID]
 * @param {String|UUID} [sNewFKID]
 * @param {Array<String>} [aOverrideColNames]
 * @param {Array} [aOverrideColVals]
 * @param {Array<String>} [aAddlWhereCols]
 * @param {Array} [aAddlWhereVals]
 * @param {Array<String>} [aSkipCols]
 * @param {Function} [fDuplicateChildrenFunc]
 * @param {Array} [aChildrenOverrideColVals]
 * @param {String} [sServerName]
 * @param {Boolean} [bAppTable] - dont use org_id
 * 
 * @return {Array<{uOldPkID:UUID,uNewPkID:UUID}>}
 *
 * @properties={typeid:24,uuid:"091D5763-0C91-45BE-A3F3-D0173C71183F"}
 */
function duplicateRecsWithSQL(sTable, sPKColName, sPKID, sFKColName, sOldFKID, sNewFKID, aOverrideColNames, aOverrideColVals, aAddlWhereCols, aAddlWhereVals, aSkipCols, fDuplicateChildrenFunc, aChildrenOverrideColVals, sServerName, bAppTable){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	/***@type {{uOldPkID:UUID,uNewPkID:UUID}}*/
	var oPkIDs = new Object();
	var aColNames =	getColumnNamesFromTable(sTable);
	
	if(aSkipCols){
		for(var j=0;j<aSkipCols.length;j++){
			var idx = aColNames.indexOf(aSkipCols[j]);
			if(idx > -1){
				aColNames.splice(idx,1);
			}
		}
	}
	
	var sColNames = scopes.avText.arrayToString(aColNames, ',');

	oSQL.server = sServerName ? sServerName : globals.avBase_dbase_avanti;
	oSQL.table = sTable;
	
	if(sPKID){
		if (bAppTable) {
			oSQL.sql = "select " + sColNames + " from " + sTable + " where " + sPKColName + " = ? ";
			oSQL.args = [sPKID.toString()];
		}
		else {
			oSQL.sql = "select " + sColNames + " from " + sTable + " where " + sPKColName + " = ? and org_id = ? ";
			oSQL.args = [sPKID.toString(), globals.org_id];
		}
	}
	else if(sFKColName && sOldFKID){
		if (bAppTable) {
			oSQL.sql = "select " + sColNames + " from " + sTable + " where " + sFKColName + " = ? ";
			oSQL.args = [sOldFKID.toString()];
		}
		else {
			oSQL.sql = "select " + sColNames + " from " + sTable + " where " + sFKColName + " = ? and org_id = ? ";
			oSQL.args = [sOldFKID.toString(), globals.org_id];
		}
	}
	else if (bAppTable && aAddlWhereCols && aAddlWhereVals) {
		oSQL.sql = "select " + sColNames + " from " + sTable + " where ";
		oSQL.args = [];
	}
	else  {
		return null;
	}
	
	if (aAddlWhereCols && aAddlWhereVals) {
		for (var i = 0; i < aAddlWhereCols.length; i++) {
			if (bAppTable) {
				if (i > 0) {
					oSQL.sql += ' and ' + aAddlWhereCols[i] + ' = ?';
				}
				else {
					oSQL.sql += aAddlWhereCols[i] + ' = ?';
				}
			}
			else {
				oSQL.sql += ' and ' + aAddlWhereCols[i] + ' = ?';
			}
			
			oSQL.args.push(aAddlWhereVals[i]);
		}
	}
	
	/** @type{JSDataSet} */
	var ds = databaseManager.getDataSetByQuery(oSQL.server, oSQL.sql, oSQL.args, -1);
	var nRowMax = ds.getMaxRowIndex(); 
	
	if(nRowMax > 0) {
		var oTable = databaseManager.getTable('db:/' + oSQL.server + '/' + sTable);
		var aCols = [];
		var colsAdded = 0;
		var val;
		var oColumn;
		var oColTypes = new Object();
		var iOverrideColIdx;
		var aPkIDs = [];

		for(var iRow=1; iRow<=nRowMax; iRow++){
			aCols = ds.getRowAsArray(iRow);
			oSQL.sql = 'insert into ' + sTable + '(' + sColNames + ') values (';
			oSQL.args = [];
			colsAdded = 0;
			oPkIDs.uOldPkID = null;
			oPkIDs.uNewPkID = null;
			
			for(var iCol=0; iCol<aCols.length; iCol++){
				if(aSkipCols && aSkipCols.indexOf(aColNames[iCol]) > -1){
					continue;
				}

				if(colsAdded>0){
					oSQL.sql += ',';
				}
				colsAdded++;
				
				oSQL.sql += '?';
			
				if(iRow == 1){
					oColumn = oTable.getColumn(aColNames[iCol]);
					oColTypes[aColNames[iCol]] = oColumn.getTypeAsString(); 
				}
				
				if(aColNames[iCol] == sPKColName){
					val = application.getUUID();
					oPkIDs.uOldPkID = aCols[iCol];
					oPkIDs.uNewPkID = val;
				}
				else if(aColNames[iCol] == sFKColName && sNewFKID){
					val = sNewFKID;
				}
				else{
					if(aOverrideColNames && aOverrideColVals){
						iOverrideColIdx = aOverrideColNames.indexOf(aColNames[iCol]);
					}
					else{
						iOverrideColIdx = -1;
					}
					
					if(iOverrideColIdx > -1){
						val = aOverrideColVals[iOverrideColIdx];
					}
					else{
						val = aCols[iCol];
					}
				}
				
				if(oColTypes[aColNames[iCol]] == 'TEXT' && val != null){
					val = val.toString();
				}
				
				oSQL.args.push(val);
			}
			
			oSQL.sql += ')';
			
			if(globals["avUtilities_sqlRaw"](oSQL)){
				
				aPkIDs.push(oPkIDs);

				if(fDuplicateChildrenFunc){
					fDuplicateChildrenFunc(oPkIDs.uOldPkID, oPkIDs.uNewPkID, aChildrenOverrideColVals);
				}
			}
		}

		return aPkIDs;
	}
	else{
		return null;
	}
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} sPKColName
 * @param {Array<String>} asFKColNames
 * @param {Array<String|UUID|Number>} asOldFKIDs
 * @param {Array<String|UUID|Number>} asNewFKIDs
 * @param {Boolean} [bReturnFoundset]
 * @param {Array<String>} [asSkipCols]
 * @param {String} [sAddlWhereConditions]
 * @param {Array<String>} [aOverrideColNames]
 * @param {Array} [aOverrideColVals]
 * @param {String} [sSortCondition]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"2393A4E1-ECE1-470D-94B8-320FF64A95F7"}
 */
function duplicateRelationWithSQL(sTable, sPKColName, asFKColNames, asOldFKIDs, asNewFKIDs, bReturnFoundset, asSkipCols, sAddlWhereConditions, aOverrideColNames, aOverrideColVals, sSortCondition) {
	/**@type {JSFoundset} */
	var fs = null;
	
	if (sTable && sPKColName && asFKColNames && asOldFKIDs && asNewFKIDs && asFKColNames.length > 0 && asOldFKIDs.length > 0 && asNewFKIDs.length > 0) {
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		var oSQL = new Object();
		var jsTable = databaseManager.getTable('db:/avanti/' + sTable);
		var aColNames =	jsTable.getColumnNames();	
		//for(var i=aColNames.length; i>0; i--){
        //	if((/.*_ci$/.test( aColNames[i] ))){
        //		aColNames.pop();
    	// 	}
	    //}
		
		var i = 0;
		
		if (asSkipCols) {
			for (var j = 0; j < asSkipCols.length; j++) {
				var idx = aColNames.indexOf(asSkipCols[j]);
				
				if (idx > -1) {
					aColNames.splice(idx, 1);
				}
			}
		}

		var sColNames = aColNames.join(',');
		
		oSQL.server = globals.avBase_dbase_avanti;
		oSQL.table = sTable;
		oSQL.sql = "SELECT " + sColNames + " FROM " + sTable + " WHERE org_id = ? ";
		oSQL.args = [globals.org_id];
		
		for (i = 0; i < asFKColNames.length; i++) {
			oSQL.sql += " AND " + asFKColNames[i] + " = ? ";
			oSQL.args.push(asOldFKIDs[i].toString());
		}
		
		if (sAddlWhereConditions) {
			oSQL.sql += " AND " + sAddlWhereConditions;
		}
		
		if (sSortCondition) {
			oSQL.sql += " ORDER BY " + sSortCondition;	    
		}
		
		/** @type{JSDataSet} */
		var ds = databaseManager.getDataSetByQuery(oSQL.server, oSQL.sql, oSQL.args, -1);
		var nRowMax = ds.getMaxRowIndex();
		var aArgsPerRec = 0;
		
		if (nRowMax > 0) {
			var oTable = databaseManager.getTable('db:/avanti/' + sTable);
			var aCols = [];
			var colsAdded = 0;
			var val;
			var oColumn;
			var oColTypes = new Object();
			var nFKColIdx = 0;
			var nOverColIdx = 0;

			oSQL.sql = '';
			oSQL.args = [];
			
			for (var iRow=1; iRow<=nRowMax; iRow++) {
				var uOrigPKID = null;
				
				aCols = ds.getRowAsArray(iRow);
				oSQL.sql += ' INSERT INTO ' + sTable + '(' + sColNames + ') VALUES (';
				colsAdded = 0;
				
				for (var iCol=0; iCol<aCols.length; iCol++) {
					if (colsAdded > 0) {
						oSQL.sql += ',';
					}
					
					colsAdded++;
					
					oSQL.sql += '?';
				
					if (iRow == 1) {
						oColumn = oTable.getColumn(aColNames[iCol]);
						oColTypes[aColNames[iCol]] = oColumn.getTypeAsString();
					}

					if (aColNames[iCol] == sPKColName) {
						uOrigPKID = aCols[iCol];
						val = application.getUUID();
					}
					else if (aColNames[iCol] == sPKColName + '_orig') {
						val = uOrigPKID;
					}
					else {
						nFKColIdx = asFKColNames.indexOf(aColNames[iCol]);
						nOverColIdx = aOverrideColNames ? aOverrideColNames.indexOf(aColNames[iCol]) : -1;
						
						// asNewFKIDs.length may be < asOldFKIDs.length, if we are using asOldFKIDs for query, but not updating this 'FK'
						if (nFKColIdx > -1 && asNewFKIDs.length >= nFKColIdx + 1) {
							val = asNewFKIDs[nFKColIdx];
						}
						else if (nOverColIdx > -1 && aOverrideColVals.length >= nOverColIdx + 1) {
							val = aOverrideColVals[nOverColIdx];
						}
						else {
							val = aCols[iCol];
						}
					}

					if (oColTypes[aColNames[iCol]] == 'TEXT' && val != null) {
						val = val.toString();
					}
					
					oSQL.args.push(val);
				}
				
				oSQL.sql += ') ';
				
				if (iRow == 1) {
					aArgsPerRec = oSQL.args.length;
				}
				// SL-20099 - the servoy error msg says 'The server supports a maximum of 2100 parameters' - however that is incorrect. it appears it supports a max of 2099, because i was
				// still getting the error when i had exactly 2100 args.  i changed it to 2000, just in case. if we are going to go over that in the next loop then insert what we have now 
				// and clear oSQL.
				else if (oSQL.args.length + aArgsPerRec > 2000 && iRow < nRowMax) {
					globals["avUtilities_sqlRaw"](oSQL);
					oSQL.sql = '';
					oSQL.args = [];
				}
			}

			if (oSQL.args.length > 0) {
				globals["avUtilities_sqlRaw"](oSQL);
			}
			
			if (bReturnFoundset) {
				oSQL.sql = "SELECT " + sPKColName + " FROM " + sTable + " WHERE org_id = ? ";
				oSQL.args = [globals.org_id];

				for (i = 0; i < asNewFKIDs.length; i++) {
					oSQL.sql += " AND " + asFKColNames[i] + " = ?  ";
					oSQL.args.push(asNewFKIDs[i].toString());
				}
			    
				fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable);
				fs.clear();
			    fs.loadRecords(oSQL.sql, oSQL.args);
			}
		}
	}
	
	return fs;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} sPKColName
 * @param {UUID} uPKID
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"616C97F2-A4C5-4E82-9DC6-BC26180DF663"}
 */
function duplicateRecWithSQL(sTable, sPKColName, uPKID) {
	/**@type {JSRecord} */
	var rec = null;
	
	if (sTable && sPKColName && uPKID) {
		var aColNames =	getColumnNamesFromTable(sTable);
		var sColNames = aColNames.join(',');
		var sSQL = "SELECT " + sColNames + " FROM " + sTable + " WHERE org_id = ? AND " + sPKColName + " = ? ";
		var aArgs = [globals.org_id, uPKID.toString()];
		var ds = getDataset(sSQL, aArgs);
		
		if (ds.getMaxRowIndex() == 1) {
			var oTable = databaseManager.getTable('db:/avanti/' + sTable);
			var aCols = [];
			var colsAdded = 0;
			var val;
			var oColumn;
			var uNewPKID = null;

			aCols = ds.getRowAsArray(1);
			sSQL = 'INSERT INTO ' + sTable + '(' + sColNames + ') VALUES (';
			aArgs = [];
			colsAdded = 0;
			
			for (var iCol=0; iCol<aCols.length; iCol++) {
				if (colsAdded > 0) {
					sSQL += ',';
				}
				
				colsAdded++;
				
				sSQL += '?';
			
				oColumn = oTable.getColumn(aColNames[iCol]);
				
				if (aColNames[iCol] == sPKColName) {
					val = application.getUUID();
					uNewPKID = val;
				}				
				else {
					val = aCols[iCol];
				}
				
				if (oColumn.getTypeAsString() == 'TEXT' && val != null) {
					val = val.toString();
				}
				
				aArgs.push(val);
			}
			
			sSQL += ')';
			
			if (uNewPKID && RunSQL(sSQL, null, aArgs)) {
				sSQL = "SELECT " + sPKColName + " FROM " + sTable + " WHERE org_id = ? AND " + sPKColName + " = ? ";
				aArgs = [globals.org_id, uNewPKID.toString()];
				rec = getRecFromSQL(sSQL, sTable, aArgs);
			}
		}
	}
	
	return rec;
}

/**
 * @param {String} sTable
 * @param {Boolean} [bFlushAllClients]
 *
 * @properties={typeid:24,uuid:"2C284311-A194-4476-B495-47E7127231C1"}
 */
function refreshFSFromDB(sTable, bFlushAllClients){
	if(sTable){
		databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable), -1)
		if(bFlushAllClients){
			plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, sTable);
		}
	}
}

/**
 * @param {String} sTable
 * @param {String} sColumn
 * @param {String} [sDB]
 * @param {String} [sOrderByCol]
 *
 * @return
 * @properties={typeid:24,uuid:"6381FDA0-9D55-48D3-9813-7CD3A9CEF3DF"}
 */
function getColValsAsArrayNoOrg(sTable, sColumn, sDB, sOrderByCol){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	
	if(sDB){
		oSQL.server = sDB;
	}
	else{
		oSQL.server = globals.avBase_dbase_avanti;
	}

	if(sOrderByCol){
		oSQL.sql = "SELECT " + sColumn + " FROM " + sTable + " order by " + sOrderByCol;
	}
	else{
		oSQL.sql = "SELECT " + sColumn + " FROM " + sTable;
	}

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	
	if (dsData && dsData.getMaxRowIndex() > 0)
	{
		return dsData.getColumnAsArray(1);
	}
	
	return [];
}

/**
 * @param {String} sTable
 * @param {String} sColumn
 * @param {String} [sOrderByCol]
 *
 * @return
 * @properties={typeid:24,uuid:"15AA7233-C8C1-4903-938A-90A8984AC5F9"}
 */
function getColValsAsArray(sTable, sColumn, sOrderByCol){
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = new Object();
	oSQL.server = globals.avBase_dbase_avanti;
	
	if(sOrderByCol){
		oSQL.sql = "SELECT " + sColumn + " FROM " + sTable + " where org_id = '" + globals.org_id + "' and " + sOrderByCol + " != '** NEW **' order by " + sOrderByCol;
	}
	else{
		oSQL.sql = "SELECT " + sColumn + " FROM " + sTable + " where org_id = '" + globals.org_id + "' and " + sColumn + " != '** NEW **' order by " + sColumn;
	}
	

	/**@type {JSDataSet} ***/
	var dsData = globals["avUtilities_sqlDataset"](oSQL);
	
	if (dsData && dsData.getMaxRowIndex() > 0)
	{
		return dsData.getColumnAsArray(1);
	}
	
	return [];
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} sTable
 * @param {String} sSearchColName
 * @param {Array<String|Number|UUID>} aSearchColValues
 * @param {String} [sSortString]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"E39C8AC3-3EBD-41C2-A6FD-BE7A4A33DBAD"}
 */
function getFSWhereColInValues(sTable, sSearchColName, aSearchColValues, sSortString){
	/** @type {JSFoundSet} */
	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable);

	if(sSearchColName && aSearchColValues.length > 0){			
		if(fs.find() || fs.find()){
			for(var i=0;i<aSearchColValues.length;i++){
				if(i>0){
					fs.newRecord();
				}
				fs.setDataProviderValue(sSearchColName, aSearchColValues[i]);
			}

			fs.search();
		}
	}

	if(sSortString){
		fs.sort(sSortString);
	}
	
	return fs;
}

/**
 * @param {String} sSQL
 * @param {String} sTable
 * @param {Array} [aArgs]
 * @param {String} [sDB]
 * @param {Boolean} [bDeleteRecordsFound]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"02500B8B-0886-4D96-89B1-7D5E81DF31EB"}
 */
function getFSFromSQL(sSQL, sTable, aArgs, sDB, bDeleteRecordsFound){
	if(!sDB){
		sDB = globals.avBase_dbase_avanti;
	}
	
    var fs = databaseManager.getFoundSet(sDB, sTable);
    fs.clear();
    fs.loadRecords(sSQL, aArgs);
    
	if (bDeleteRecordsFound && utils.hasRecords(fs)) {
		fs.deleteAllRecords();
	}
    
	return fs;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} sSQL
 * @param {Array} [aArgs]
 *
 * @properties={typeid:24,uuid:"E87EE067-3019-404C-92E1-F1D7BB5F6476"}
 */
function deleteFoundset(sTable, sSQL, aArgs) {
	try {
		if (sTable && sSQL) {
		    var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, sTable);

		    fs.loadRecords(sSQL, aArgs);
		    
			if (utils.hasRecords(fs)) {
				fs.deleteAllRecords();
			}
		}
	}
	catch (ex) {
		application.output("deleteFoundset Error: " + ex.message, LOGGINGLEVEL.ERROR);
	}
}

/**
 * @public 
 * 
 * @param {String} sSQL
 * @param {String} sTable
 * @param {Array} [aArgs]
 * @param {String} [sDB]
 * @param {String} [sSort]
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"1E7638DA-21AB-44A5-AA22-58753D3F1F36"}
 */
function getRecFromSQL(sSQL, sTable, aArgs, sDB, sSort) {
    var fs = getFSFromSQL(sSQL, sTable, aArgs, sDB);

    if (fs && fs.getSize() > 0) {
        if (sSort) {
            fs.sort(sSort);
        }

        return fs.getRecord(1);
    }
    else {
        return null;
    }
}

/**
 * @public 
 * 
 * @param {String} sSQL
 * @param {String} sTable
 * @param {String} sLock
 * @param {Array} [aArgs]
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"23EC991B-9919-477A-88B3-97B752917146"}
 */
function getRecWithLock(sSQL, sTable, sLock, aArgs) {
    var fs = getFSFromSQL(sSQL, sTable, aArgs);

    if (utils.hasRecords(fs) && scopes.avDB.waitAndAcquireLock(fs, 1, sLock, sLock)) {
        return fs.getRecord(1);
    }
    else {
        return null;
    }
}

/**
 * @public 
 * 
 * @param {JSFoundset} fs
 * @param {String} [sAltColName] 
 *
 * @return
 * @properties={typeid:24,uuid:"CFB89407-1410-4C30-A37A-5F4BCE765771"}
 */
function getNextSequenceNr(fs, sAltColName){
	var iSeq = 0;
	var sColName = sAltColName ? sAltColName : 'sequence_nr'; 
	
	for (var i = 1; i <= fs.getSize(); i++){
		var rRec = fs.getRecord(i)
		
		if (rRec[sColName] > iSeq){
			iSeq = rRec[sColName];
		}
	}
	
	return iSeq + 1;
}

/**
 * @public 
 * @param {JSFoundset} fs
 * @return {Array<JSRecord>}
 *
 * @properties={typeid:24,uuid:"B2A5C805-56DA-4DAD-BA80-802341F651D0"}
 */
function foundsetToArray(fs){
	/**@type {Array<JSRecord>} */
	var aArray = [];
	
	if(fs && fs.getSize() > 0){
		for(var i=1; i<=fs.getSize(); i++){
			aArray.push(fs.getRecord(i));
		}
	}
	
	return aArray;
}

/**
 * @public 
 * 
 * @param {String} sSQL
 * @param {Boolean} [bAddOrgIDCondition]
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"1FDB51F3-9A4B-404D-BE1E-270ECE518E77"}
 */
function RunSQL(sSQL, bAddOrgIDCondition, aArgs, sServer) {
    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();

    if (bAddOrgIDCondition) {
        if (sSQL.indexOf(' where ') > -1) {
            sSQL += " and org_id = '" + globals.org_id + "'";
        }
        else {
            sSQL += " where org_id = '" + globals.org_id + "'";
        }
    }

    oSQL.sql = sSQL;

    if (sServer) {
        oSQL.server = sServer;
    }
    else {
        oSQL.server = globals.avBase_dbase_avanti;
    }

    if (aArgs) {
        oSQL.args = aArgs;
    }

    return globals["avUtilities_sqlRaw"](oSQL);
}

/**
 * @public
 *  
 * @param {JSFoundset} fs
 * @param {Array<String>} aMatchColNames
 * @param {Array} aMatchColValues
 * @param {String} [sRelation]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F105065C-EEF5-43B4-8E4E-F29CFD1B2E7E"}
 */
function isThereAMatchingRecordInFoundset(fs, aMatchColNames, aMatchColValues, sRelation){
	var bIsThereAMatchingRecordInFoundset = false;
	
	try{
		if(utils.hasRecords(fs)){
			for(var i=1; i<=fs.getSize(); i++){
				var r = fs.getRecord(i);
				var rTest = null;
			
				if(sRelation){
					if(utils.hasRecords(r[sRelation])){
						rTest = r[sRelation].getRecord(1);
					}
				}
				else{
					rTest = r;
				}
				
				if(rTest){
					var bFoundANonMatch = false;
					
					for(var j=0; j<aMatchColNames.length; j++){
						var sColName = aMatchColNames[j];
						var colVal = aMatchColValues[j];

						// if a colvalue is an array then we use an OR between then. IE, only 1 of these needs to match
						if(Array.isArray(colVal)){
							var bFoundAMatch = false;
							
							for(var k=0; k<colVal.length; k++){
								var innerColVal = colVal[k];
								
								if(rTest[sColName] == innerColVal){
									bFoundAMatch = true;
									break;
								}
							}

							bFoundANonMatch = !bFoundAMatch;
						}
						else if(rTest[sColName] != colVal){
							bFoundANonMatch = true;
						}
						
						if(bFoundANonMatch){
							break;
						}
					}
					
					if(bFoundANonMatch == false){
						bIsThereAMatchingRecordInFoundset = true;
						break;
					}
				}
			}
		}
	}
	catch(ex){
		application.output('Error in isThereAMatchingRecordInFoundset: ' + ex.message, LOGGINGLEVEL.ERROR);
	}
	finally{
		return bIsThereAMatchingRecordInFoundset;
	}
}

/**
 * @public 
 * 
 * @param {String} sSQL - eg. "select * from sa_invoice sa_order o inner join sa_order_revision_header h on o.ordh_id = h.ordh_id "
 * 	 no 'where' at end needed but doesnt hurt
 * @param {String} sConditionsNoWhere - eg: "o.cust_id = ? or o.cust_id = ?" - no 'where' allowed - this is because we put brackets around whole sConditionsNoWhere, 
 *   so they play nice with conditons added in this func 
 * @param {String} sTable - we need the tablename to look up table cols
 * @param {String} [sAddlClauses] - order by clause
 * @param {String} [sTableAlias] - can specify an alias to use for the table instead, when specifying org/div/plant conditions
 * @param {Boolean} [bBypassAddDivPlant] - set to true to bypass adding div plant constions - there are cases where we want to see all 
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"B572BF8C-B6D0-44FF-89B3-A616E04FFF5A"}
 */
function safeSQL(sSQL, sConditionsNoWhere, sTable, sAddlClauses, sTableAlias, bBypassAddDivPlant){
	if(sSQL && sTable){
		var fsTable = datasources.db[globals.avBase_dbase_avanti][sTable].getFoundSet();
		
		if(fsTable){
			var dNeedOrgID = fsTable.alldataproviders.indexOf("org_id") > -1;
			var dNeedDivID = false;
			var dNeedPlantID = false;
			var bAddAnd = false;
			
			// if div/plant filter on 
			if(!bBypassAddDivPlant && globals.avBase_getSystemPreference_Number(79) === 1){
				dNeedDivID = globals.avBase_divID && globals.avBase_divID != 'ALL' && fsTable.alldataproviders.indexOf("div_id") > -1;
				dNeedPlantID = globals.avBase_plantID && globals.avBase_plantID != 'ALL' && fsTable.alldataproviders.indexOf("plant_id") > -1;
			}
			
			if(sSQL.indexOf(" where ") == -1){
				sSQL += " where ";
			}
			
			if(sConditionsNoWhere){
				sSQL += " ( " + sConditionsNoWhere + " ) ";
				bAddAnd = true;
			}
			
			if(dNeedOrgID && sSQL.indexOf(' org_id =') == -1 && sSQL.indexOf(' org_id=') == -1){
				if(bAddAnd){
					sSQL += " and ";
				}
				else{
					sSQL += " ";
					bAddAnd = true;
				}
				
				if(sTableAlias){
					sSQL += sTableAlias + ".org_id = '" + globals.org_id + "'";
				}
				else{
					sSQL += sTable + ".org_id = '" + globals.org_id + "'";
				}
			}
			
			if(dNeedDivID && sSQL.indexOf(' div_id =') == -1 && sSQL.indexOf(' div_id=') == -1){
				if(bAddAnd){
					sSQL += " and ";
				}
				else{
					sSQL += " ";
					bAddAnd = true;
				}
				
				if(sTableAlias){
					sSQL += "(" + sTableAlias + ".div_id = '" + globals.avBase_divID + "' or " + sTableAlias + ".div_id is null)";
				}
				else{
					sSQL += "(" + sTable + ".div_id = '" + globals.avBase_divID + "' or " + sTable + ".div_id is null)";
				}
			}
			
			if(dNeedPlantID && sSQL.indexOf(' plant_id =') == -1 && sSQL.indexOf(' plant_id=') == -1){
				if(bAddAnd){
					sSQL += " and ";
				}
				else{
					sSQL += " ";
				}
				
				if(sTableAlias){
					sSQL += "(" + sTableAlias + ".plant_id = '" + globals.avBase_plantID + "' or " + sTableAlias + ".plant_id is null)";
				}
				else{
					sSQL += "(" + sTable + ".plant_id = '" + globals.avBase_plantID + "' or " + sTable + ".plant_id is null)";
				}
			}
			
			if(sAddlClauses){
				sSQL += " " + sAddlClauses;
			}
		}
	}
	
//	application.output(sSQL);
	return sSQL;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {Array<String>} asColNames
 * @param {Array} aColValues
 * @param {String} [sPKColName]
 * @param {Boolean} [bBypassAddDivPlant] - set to true to bypass adding div plant constions - there are cases where we want to see all
 * 
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"157B32DE-1F34-4E46-AD78-A929ECF83518"}
 */
function insert(sTable, asColNames, aColValues, sPKColName, bBypassAddDivPlant){
	if(sTable && asColNames && aColValues && asColNames.length > 0 && aColValues.length > 0 && asColNames.length == aColValues.length){
		var fsTable = datasources.db[globals.avBase_dbase_avanti][sTable].getFoundSet();
		
		if(fsTable){
			var oTable = databaseManager.getTable(fsTable.getDataSource());
			var sColNames = '';
			var sColValues = '';
			var nMax = asColNames.length;
			var i;
			var dNeedOrgID = fsTable.alldataproviders.indexOf("org_id") > -1;
			var dNeedDivID = false;
			var dNeedPlantID = false;
			
			// if div/plant filter on 
			if(!bBypassAddDivPlant && globals.avBase_getSystemPreference_Number(79) === 1){
				dNeedDivID = globals.avBase_divID && globals.avBase_divID != 'ALL' && fsTable.alldataproviders.indexOf("div_id") > -1;
				dNeedPlantID = globals.avBase_plantID && globals.avBase_plantID != 'ALL' && fsTable.alldataproviders.indexOf("plant_id") > -1;
			}
			
			if(dNeedOrgID){
				sColNames += 'org_id';
				sColValues += "'" + globals.org_id + "'";
			}

			if(dNeedDivID){
				if(sColNames) sColNames += ",";
				if(sColValues) sColValues += ",";
				sColNames += 'div_id';
				sColValues += "'" + globals.avBase_divID + "'";
			}

			if(dNeedPlantID){
				if(sColNames) sColNames += ",";
				if(sColValues) sColValues += ",";
				sColNames += 'plant_id';
				sColValues += "'" + globals.avBase_plantID + "'";
			}
			
			if(sPKColName){
				if(sColNames) sColNames += ",";
				if(sColValues) sColValues += ",";
				sColNames += sPKColName;
				var sPKID = application.getUUID();
				sColValues += "'" + sPKID + "'";
			}

			for(i=0; i<nMax; i++){
				if(sColNames) sColNames += ",";
				if(sColValues) sColValues += ",";
				
				sColNames += asColNames[i];

				var column = oTable.getColumn(asColNames[i]);
				var typeName = column.getTypeAsString();
				
				if(aColValues[i] === null){
					sColValues += "null";
				}
				else if(typeName == 'TEXT'){
					sColValues += "'" + aColValues[i] + "'";
				}
				else{
					sColValues += aColValues[i];
				}
			}

			var sSQL = "insert into " + sTable + " (" + sColNames + ") values (" + sColValues + ")";
			
			RunSQL(sSQL);
			
			// check to see if the insert was successful
			if(sPKColName && SQLExists("select * from " + sTable + " where " + sPKColName + " = ?", null, [sPKID.toString()])){
				return sPKID;
			}
		}
	}
	
	return null;
}

/**
 * @param {String} sSQL
 * @param {String} sConditionsNoWhere 
 * @param {String} sTable
 * @param {Array} [aArgs]
 * @param {Number} [numColsToReturn]
 * @param {String} [sAddlClauses]
 *
 * @return
 * @properties={typeid:24,uuid:"34BA7E21-631E-42B9-9965-F29A18146935"}
 */
function safeSQLQuery(sSQL, sConditionsNoWhere, sTable, aArgs, numColsToReturn, sAddlClauses) {
	sSQL = safeSQL(sSQL,sConditionsNoWhere,sTable);
	return SQLQuery(sSQL, false, aArgs, null, null, numColsToReturn, sAddlClauses);
}

/**
 * @param {String} sSQL
 * @param {String} sConditionsNoWhere 
 * @param {String} sTable
 * @param {Array} [aArgs]
 *
 * @properties={typeid:24,uuid:"FE13DD5E-6A16-4C93-A154-D2E9848EB3FC"}
 */
function safeSQLRun(sSQL, sConditionsNoWhere, sTable, aArgs) {
	sSQL = safeSQL(sSQL, sConditionsNoWhere, sTable);
	RunSQL(sSQL, false, aArgs);
}

/**
 * @public 
 * 
 * @param {String} [sPrefix] The table to get the safe conditions for
 * @param {Boolean} [bUseInvoiceDivPlantFilters] When called from invoicing we use the invoicing div/plant
 * @param {String} [uInvoiceDivisionId]
 * @param {String} [uInvoicePlantId]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"84983779-798B-4A4E-A8A8-0506042E696C"}
 */
function getSafeConditionsClause(sPrefix, bUseInvoiceDivPlantFilters, uInvoiceDivisionId, uInvoicePlantId) {
	var sConditions = null;
	
	// ORG_ID
    if (sPrefix) {
        sConditions = " " + sPrefix + ".org_id = '" + globals.org_id + "' ";
    }
    else {
        sConditions = " org_id = '" + globals.org_id + "' ";
    }

    // note: this code doesnt validate the existence of the div and plant cols in the table. this func should only be called when we
    // know that the table does thave these cols
    if (globals.avBase_getSystemPreference_Number(79) === 1) {
        if (bUseInvoiceDivPlantFilters && uInvoiceDivisionId && uInvoiceDivisionId != i18n.getI18NMessage('avanti.lblAllCaps')) {
            if (sPrefix) {
                sConditions += " AND (" + sPrefix + ".div_id = '" + uInvoiceDivisionId + "') ";
            }
            else {
                sConditions += " AND (div_id = '" + uInvoiceDivisionId + "') ";
            }
        }
        else {
            // DIV_ID
            if (globals.avBase_divID) {
                if (globals.avBase_divID != 'ALL') {
                    if (sPrefix) {
                        sConditions += " AND (" + sPrefix + ".div_id = '" + globals.avBase_divID + "' OR " + sPrefix + ".div_id IS NULL) ";
                    }
                    else {
                        sConditions += " AND (div_id = '" + globals.avBase_divID + "' OR div_id IS NULL) ";
                    }
                }
                else {
                    sConditions += addAllDivFilter(sPrefix);
                }
            }
        }
        
        if (bUseInvoiceDivPlantFilters && uInvoicePlantId && uInvoicePlantId != i18n.getI18NMessage('avanti.lblAllCaps')) {
            if (sPrefix) {
                sConditions += " AND (" + sPrefix + ".plant_id = '" + uInvoicePlantId + "') ";
            }
            else {
                sConditions += " AND (plant_id = '" + uInvoicePlantId + "') ";
            }
        }
        else {
         // PLANT_ID
            if (globals.avBase_plantID) {
                if (globals.avBase_plantID != 'ALL') {
                    if (sPrefix) {
                        sConditions += " AND (" + sPrefix + ".plant_id = '" + globals.avBase_plantID + "' OR " + sPrefix + ".plant_id IS NULL) ";
                    }
                    else {
                        sConditions += " AND (plant_id = '" + globals.avBase_plantID + "' OR plant_id IS NULL) ";
                    }
                }
                else {
                    sConditions += addAllPlantFilter(sPrefix);
                }
            }
        }
    }

    return " (" + sConditions + ") ";
}	

/**
 * @param {String} sTable
 * @param {Array<String>} asSetColNames
 * @param {Array} aSetColValues
 * @param {Array<String>} [asSearchColNames]
 * @param {Array} [aSearchColValues]
 * @param {Boolean} [bBypassAddDivPlant] - set to true to bypass adding div plant conditions 
 *
 * @properties={typeid:24,uuid:"016C6F1F-A6E2-4E2C-BE13-E2F91A99A06E"}
 */
function safeSqlUpdate(sTable, asSetColNames, aSetColValues, asSearchColNames, aSearchColValues, bBypassAddDivPlant){
	if(sTable && asSetColNames && aSetColValues && asSetColNames.length > 0 && aSetColValues.length > 0 && asSetColNames.length == aSetColValues.length){
		var fsTable = datasources.db[globals.avBase_dbase_avanti][sTable].getFoundSet();
		
		if(fsTable){
			var oTable = databaseManager.getTable(fsTable.getDataSource());
			var i;
			var dNeedOrgID = fsTable.alldataproviders.indexOf("org_id") > -1;
			var dNeedDivID = false;
			var dNeedPlantID = false;
			var sSetClause = "";
			var sWhereClause = "";
			var nMax;
			var column;
			var typeName;			
			
			// SET CLAUSE
			nMax = asSetColNames.length;
			for(i=0; i<nMax; i++){
				column = oTable.getColumn(asSetColNames[i]);
				typeName = column.getTypeAsString();

				if(sSetClause) sSetClause += ",";
				
				if(aSetColValues[i] === null){
					sSetClause += asSetColNames[i] + " = null";
				}
				else if(typeName == 'TEXT'){
					sSetClause += asSetColNames[i] + " = '" + aSetColValues[i] + "'";
				}
				else{
					sSetClause += asSetColNames[i] + " = " + aSetColValues[i];
				}
			}
			
			// WHERE CLAUSE
			if(!bBypassAddDivPlant && globals.avBase_getSystemPreference_Number(79) === 1){
				dNeedDivID = globals.avBase_divID && globals.avBase_divID != 'ALL' && fsTable.alldataproviders.indexOf("div_id") > -1;
				dNeedPlantID = globals.avBase_plantID && globals.avBase_plantID != 'ALL' && fsTable.alldataproviders.indexOf("plant_id") > -1;
			}
			
			if(dNeedOrgID){
				sWhereClause += " org_id = '" + globals.org_id + "' ";
			}
			if(dNeedDivID){
				if(sWhereClause) sWhereClause += " and ";
				sWhereClause += " div_id = '" + globals.avBase_divID + "' ";
			}
			if(dNeedPlantID){
				if(sWhereClause) sWhereClause += " and ";
				sWhereClause += " plant_id = '" + globals.avBase_plantID + "' ";
			}

			nMax = asSearchColNames.length;
			for(i=0; i<nMax; i++){
				column = oTable.getColumn(asSearchColNames[i]);
				typeName = column.getTypeAsString();

				if(sWhereClause) sWhereClause += " and ";
				
				if(aSearchColValues[i] === null){
					sWhereClause += asSearchColNames[i] + " is null";
				}
				else if(typeName == 'TEXT'){
					sWhereClause += asSearchColNames[i] + " = '" + aSearchColValues[i] + "'";
				}
				else{
					sWhereClause += asSearchColNames[i] + " = " + aSearchColValues[i];
				}
			}
			
			if(sSetClause){
				var sSQL = "UPDATE " + sTable + " SET " + sSetClause;
				
				if(sWhereClause){
					sSQL += " WHERE " + sWhereClause
				}
				
				RunSQL(sSQL);
			}
		}
	}
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} sColumn
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"BD2DCFAA-67AF-4D03-9C45-6BF363F3B0B1"}
 */
function columnExists(sTable, sColumn){
	if(sTable && sColumn){
		return SQLQuery("select COL_LENGTH('" + sTable + "', '" + sColumn + "')") > 0;
	}
	else{
		return false;
	}
}

/**
 * takes sql requesting a single col, multiple records expected, it returns an array with the column value for each record
 * 
 * @public 
 * 
 * @param {String} sSQL
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * 
 * @return {Array}
 * 
 * @properties={typeid:24,uuid:"836BA5B1-4841-4B73-969E-7D0F82E4187A"}
 */
function SQLQueryReturnMultiRecArray(sSQL, aArgs, sServer) {
    return SQLQuery(sSQL, null, aArgs, sServer, null, null, null, true);
}

/**
 * takes sql requesting a multiple cols, single record expected, it returns an array with the value for each column
 * 
 * @public 
 * 
 * @param {String} sSQL
 * @param {Array} [aArgs]
 * @param {Number} [numColsToReturn]
 * 
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"1BEB1AB4-E0E3-4113-B141-EE8432C6A5D7"}
 */
function SQLQueryReturnMultiColArray(sSQL, aArgs, numColsToReturn){
    return SQLQuery(sSQL, null, aArgs, null, null, numColsToReturn);
}

/**
 * @public 
 * 
 * @param {String} sSQL
 * @param {Array} [aArgs]
 * @param {String} [sServer]
 * 
 * @return {JSDataSet}
 *
 * @properties={typeid:24,uuid:"A4194563-1303-4708-984E-B6120F503093"}
 */
function getDataset(sSQL, aArgs, sServer){
    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = { };
    
    oSQL.sql = sSQL;
    oSQL.args = aArgs;
    
    if(sServer){
        oSQL.server = sServer;
    }

    return globals["avUtilities_sqlDataset"](oSQL);
}

/**
 * @public 
 * 
 * @param {JSRecord} rec
 * @param {String} [sTable]
 * @param {String} [sServer]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"60DEAC96-8368-42AE-B92A-4605C0A5BE3E"}
 */
function convertRecordToFoundset(rec, sTable, sServer){
	if(sTable){
		if(!sServer){
			sServer = globals.avBase_dbase_avanti;
		}
	}
	else{
		var aDatasource = getDatasource(null, rec);
		
		if(aDatasource.length == 2){
			sServer = aDatasource[0];
			sTable = aDatasource[1];
		}
	}

	var fs = databaseManager.getFoundSet(sServer, sTable);
	var sPK = rec.getPKs()[0];
	
	if(sPK){
		fs.clear();
		fs.loadRecords(sPK);
	}

	return fs;
}


/**
 * @public
 *  
 * <AUTHOR> Zafar
 * @since Mar 05, 2018
 * 
 * @param {string} sReturnFields comma separated fields
 * @param {string} sFromClause
 * @param {string} sPrefix
 * @param {string} sWhereClause
 * @param {Array<String|Number|UUID>} aWhereClauseArgs
 * @param {boolean} bCheckDivAndPlant make it 'true' if table has div and palnts fields
 * @param {string} sOrderByClause e.g column_name asc,column_name2 desc
 * 
 * @return
 * @properties={typeid:24,uuid:"33E73AFE-4929-4D1E-BA3A-D8C99FAB5A84"}
 */
function checkHasRecordsAndReturnValue(sReturnFields, sFromClause, sPrefix, sWhereClause, aWhereClauseArgs, bCheckDivAndPlant, sOrderByClause) {
    var sReturnValue = null;
    /***@type {JSDataSet} */
    var dsData = null;

    dsData = hasRecords(sReturnFields, sFromClause, sPrefix, sWhereClause, aWhereClauseArgs, bCheckDivAndPlant, sOrderByClause, null, null);
    if (dsData && dsData.getMaxRowIndex() > 0) {
        sReturnValue = dsData.getValue(1, 1);
    }

    return sReturnValue;
}

/**
 * @public 
 * 
 * <AUTHOR> Zafar
 * @since Mar 09, 2018
 * 
 * @param {string} sReturnFields comma separated fields
 * @param {string} sFromClause
 * @param {string} sPrefix
 * @param {string} sWhereClause
 * @param {Array<String|Number|UUID>} aWhereClauseArgs
 * @param {boolean} bCheckDivAndPlant 'true' if table has div and palnts fields
 * @param {string} sOrderByClause e.g column_name asc,column_name2 desc
 * @param {string} sTable
 * @param {string} sServer
 * 
 * @return
 * @properties={typeid:24,uuid:"4BA2769B-0227-43D5-8DA8-7B4F6D04BEA8"}
 */
function hasRecords(sReturnFields, sFromClause, sPrefix, sWhereClause, aWhereClauseArgs, bCheckDivAndPlant, sOrderByClause, sTable, sServer) { 
    var oSQL = { };
    /***@type {JSDataSet} */
    var dsData = null;
    var sSQL = createSQLQuery(sReturnFields, sFromClause, sPrefix, sWhereClause, aWhereClauseArgs, bCheckDivAndPlant, sOrderByClause);    

    if (bCheckDivAndPlant) {
        oSQL.args = [];
    }
    else {
        oSQL.args = [globals.org_id];
    }
  
    oSQL.args = oSQL.args.concat(aWhereClauseArgs);
    oSQL.sql = sSQL;
    if (sTable) oSQL.table = sTable;
    if (sServer) oSQL.server = sServer;
    
    dsData = globals["avUtilities_sqlDataset"](oSQL); 

    return dsData;
}

/**
 * This method will create SQL query with safe condition
 * 
 * @public 
 * 
 * <AUTHOR> Zafar
 * @since Mar 09, 2018
 * 
 * @param {string} sReturnFields comma separated fields
 * @param {string} sFromClause
 * @param {string} sPrefix
 * @param {string} sWhereClause
 * @param {Array<String|Number|UUID>} aWhereClauseArgs
 * @param {boolean} bCheckDivAndPlant 'true' if table has div and palnts fields
 * @param {string} sOrderByClause e.g column_name asc,column_name2 desc
 * 
 * @return
 * @properties={typeid:24,uuid:"1ED71F00-C154-481B-90BD-1A7AAF567FF4"}
 */
function createSQLQuery(sReturnFields, sFromClause, sPrefix, sWhereClause, aWhereClauseArgs, bCheckDivAndPlant, sOrderByClause) { 
    var sSQL = "SELECT ";

    sSQL += sReturnFields ? sReturnFields + " FROM " + sFromClause : " * FROM " + sFromClause;
    sSQL += " WHERE ";

    if (bCheckDivAndPlant) {
        sSQL += getSafeConditionsClause(sPrefix);
    }
    else {
        if (sPrefix) {
            sSQL += sPrefix + ".org_id = ? ";
        }
        else {
            sSQL += " org_id = ? ";
        }
    }

    sSQL += sWhereClause ? " AND " + sWhereClause : "";
    if (sOrderByClause) sSQL += " ORDER BY " + sOrderByClause;
    
    return sSQL;
}

/**
 * applies Div_Id filter. Divisions that are assigned to an employee.
 * <AUTHOR> ul Hyee
 * @since Jul 11, 2018
 * @param {String} sPrefix table/alias.
 * @returns {String} Division filter condition.
 * 
 * @properties={typeid:24,uuid:"7E18A1D9-782E-4481-8886-AB68A2BD2BDA"}
 */
function addAllDivFilter(sPrefix) {
    var aDiv = scopes.globals.getEmpDivs_forSQL();
    var sReturn = "";
    
    if (aDiv && aDiv.length > 0) {
        if (sPrefix) {
            sReturn += "AND " + sPrefix + ".div_id IN (";
        }
        else {
            sReturn += "AND div_id IN (";
        }
        sReturn += "'" +  aDiv.join("','") + "'";
        sReturn += ") ";
    } else {
    	// using scopes.avUtils.ENUM_DEFAULT_GUID.Default (could be any string) so it will never match with UUID value. 
    	// We have to make a false condition here if no Div assigned to the user.
        sReturn += "AND " + sPrefix + ".div_id IN ('" + scopes.avUtils.ENUM_DEFAULT_GUID.Default + "') "; 
    }
    
    return sReturn;
}

/**
 * applies Plant_Id filter. Plants that are assigned to an employee.
 * <AUTHOR> ul Hyee
 * @since Jul 11, 2018
 * @param {String} sPrefix table/alias.
 * @returns {String} Plant filter condition.
 *
 * @properties={typeid:24,uuid:"AB3CF583-979B-4DA1-81C6-E5BF1E330749"}
 */
function addAllPlantFilter(sPrefix) {
    var sPlants = scopes.globals.getEmpPlants_forSQL(); 
    var sReturn = "";
    
    if (sPlants && utils.stringTrim(sPlants).length > 0) {
        var aPlant = utils.stringTrim(sPlants).split(",");
        if (sPrefix) {
            sReturn += "AND " + sPrefix + ".plant_id IN (";
        }
        else {
            sReturn += "AND plant_id IN (";
        }
        sReturn +=  aPlant.join(",");
        sReturn += ") ";
    } else {
    	// using scopes.avUtils.ENUM_DEFAULT_GUID.Default (could be any string) so it will never match with UUID value. 
    	// We have to make a false condition here if no Div assigned to the user.
        sReturn += "AND " + sPrefix + ".plant_id IN ('" + scopes.avUtils.ENUM_DEFAULT_GUID.Default + "') "; 
    }
    
    return sReturn;
}

/**
 * @public 
 * 
 * @param {String} sTableName
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"47F38175-E33C-4A52-897F-71C295664935"}
 */
function getNextTableSequenceNr(sTableName) {
    var sSQL = "SELECT MAX(sequence_nr) FROM " + sTableName + " WHERE org_id = ?";
    var aArgs = [globals.org_id];
    var nMaxSeqNr = SQLQuery(sSQL, null, aArgs);
    
    if (!nMaxSeqNr) {
        nMaxSeqNr = 0;
    }
    
    return nMaxSeqNr + 1;
}

/**
 * Attempts to acquire a lock on a foundset with retry logic and timeout.
 * 
 * This function implements a robust locking mechanism with exponential backoff retry logic.
 * It will attempt to acquire the lock for the specified timeout period (default 20 seconds).
 * 
 * IMPORTANT:
 * - Always use try-catch or finally blocks to ensure locks are released
 * - Avoid user dialogs UI while holding locks
 * - Follow consistent lock acquisition order to prevent deadlocks
 * - Use waitForRecordLockRelease in onRecordUpdate/onRecordDelete events
 * 
 * WARNING:
 * - Locks should not be held during long-running operations
 * - Avoid nesting lock acquisitions
 * - Example: When locking sa_order_revd_item, item tables are locked internally,
 *   so avoid locking sa_order_revd_item after item tables
 *
 * @param {JSFoundSet} foundset Request lock(s) for a foundset, can be a normal or related foundset.
 * @param {Number} index The record_index can be -1 to lock all rows, 0 to lock the current row, or a specific row of > 0
 * @param {String} lockName
 * @param {String} loggingID
 * @param {Number} [timeout] - Use this wisely, you do not want to make a client wait indefinitely!
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"CE4E38D5-7A79-46BF-88AA-060E037F7212"}
 */
function waitAndAcquireLock(foundset, index, lockName, loggingID, timeout) {
	var success = false;
	try {
		
		
		if(!lockName || !Object.values(FOUNDSET_LOCKS).includes(lockName)){
			//lockName must be one from the enum FOUNDSET_LOCKS
			application.output('lockName must be one from the enum FOUNDSET_LOCKS',LOGGINGLEVEL.ERROR)
			return success;
		}
	   
		if (!timeout) {
			timeout = application.getUserProperty('db.lockTimeoutinMilliseconds');
			
			if (timeout && !scopes.avMath.isNumber(timeout)) {
				timeout = null;
			}
			if (!timeout) { //By Default it will only try for 20 seconds;
				timeout = 20000;
			}
		}
		
		var sleepTime = application.getUserProperty('db.retryIntervalInMilliseconds');

		if (sleepTime) {
			if (!scopes.avMath.isNumber(sleepTime)) {
				sleepTime = null;
			} else {
				// Convert to number for switch comparison
				sleepTime = Number(sleepTime);
				//Locking has been disabled explicitly in the system
				if (sleepTime < -5) {  // Change from -4 to -5
					application.output('Warning: Invalid sleepTime value ' + sleepTime + '. Supported values are -1 to -5. Using default locking behavior.', LOGGINGLEVEL.WARNING);
					sleepTime = null;
				} else {
					switch (sleepTime) {
					case -1: // no locking for sa_order_revd_item table
						if (lockName == FOUNDSET_LOCKS.SaOrderRevdItemLock) {
							application.output('Locking disabled for sa_order_revd_item table', LOGGINGLEVEL.INFO);
							return true;
						}
						break;
					case -2: // no locking for sa_order_revd_item and sa_order_revision_detail tables
						if (lockName == FOUNDSET_LOCKS.SaOrderRevDetailLock || lockName == FOUNDSET_LOCKS.SaOrderRevdItemLock) {
							application.output('Locking disabled for order-related tables', LOGGINGLEVEL.INFO);
							return true;
						}
						break;
					case -3: //only locking the item tables - as introduced from before 1.0.2025
						if (lockName == FOUNDSET_LOCKS.SaOrderRevDetailLock || lockName == FOUNDSET_LOCKS.SaOrderRevdItemLock || lockName == FOUNDSET_LOCKS.ItemCommitmentLock) {
							application.output('Only item tables are locked', LOGGINGLEVEL.INFO);
							return true;
						}
						break;
					case -4: //no locking for sa_order_revision_detail table
						if (lockName == FOUNDSET_LOCKS.SaOrderRevDetailLock ) {
							application.output('Locking disabled for sa_order_revision_detail table', LOGGINGLEVEL.INFO);
							return true;
						}
						break;
					case -5: //no locking at all- not recommended
						application.output('WARNING: All locking disabled - not recommended for production use', LOGGINGLEVEL.INFO);
						return true;
						
					}
					
				}
			}
		}

		/***@type{Number} */
		const SLEEP_TIME = (sleepTime && sleepTime > 0) ? sleepTime : 50;
		
		var startTime = application.getServerTimeStamp().getTime();
		var currentSleepTime = SLEEP_TIME;
		do {
			success = databaseManager.acquireLock(foundset, index, lockName);
			var clientID = plugins.clientmanager.getClientInformation().getClientID();
			
			if (success) {
				application.output(clientID+ " acquired " + lockName +' '+ success+ " for: " + loggingID, LOGGINGLEVEL.DEBUG);
				//use the following block to emulate with long locking time
//				if(application.getApplicationType() != APPLICATION_TYPES.HEADLESS_CLIENT)
//					application.sleep(5000);
			}
			else{
				//pause a little and try again
				application.output(clientID + " will try to acquire " + lockName +' in '+ currentSleepTime+ " Milliseconds " + loggingID, LOGGINGLEVEL.DEBUG);
				application.sleep(currentSleepTime);
				currentSleepTime *= 2;
				if (currentSleepTime > 1000) {
					currentSleepTime = 1000;
				}
			}
		}
		while (!success & application.getServerTimeStamp().getTime() - startTime < timeout);

		if (!success) {
			var message = 'Timed out, could not acquire lock ' + lockName + 'for '+loggingID;
			globals.dbLog(message,loggingID,null,null,null,null,'locking','Summary');
		}    
	} catch (e) {
		application.output('Exception in waitAndAcquireLock',LOGGINGLEVEL.FATAL);
	}
	return success;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} [sParentKeyName]
 * @param {UUID|String} [sParentKeyValue]
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"451A6A02-D900-4916-8335-7A9FB29F7B7B"}
 */
function GetNextSeqNumForAvantiDataTable(sTable, sParentKeyName, sParentKeyValue) {
	var sSQL = "SELECT MAX(sequence_nr) FROM " + sTable + " WHERE org_id = ?";
	var aArgs = [globals.org_id];
	
	if (sParentKeyName && sParentKeyValue) {
		sSQL += " AND " + sParentKeyName + " = ?";
		aArgs.push(sParentKeyValue.toString());
	}
	
	var nMaxSeqNum = SQLQuery(sSQL, null, aArgs);

	if (!nMaxSeqNum) {
		nMaxSeqNum = 0;
	}
	
	return nMaxSeqNum + 1;
}

/**
 * @public 
 * 
 * @param {JSRecord} record
 * @param {String} sColumnName
 * 
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"7358F884-42FE-4EF4-B213-8C131FD984CF"}
 */
function getColumnChangedData(record, sColumnName) {
    var dsData = record.getChangedData();
    var sOldValue = null;
    var sNewValue = null;
    
	if (dsData) {
	    for (var i = 1; i <= dsData.getMaxRowIndex(); i++) {
			if (dsData.getValue(i, 1) == sColumnName) {
	            sOldValue = dsData.getValue(i, 2);
	            sNewValue = dsData.getValue(i, 3);
	            break;
			}
	    }
	}
	
	if (sOldValue != sNewValue) {
		return [sOldValue, sNewValue];
	}
	else {
		return [];
	}
}

/**
 * @public 
 * 
 * @param {JSRecord} record
 * @param {String} sColumnName
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"003F37A6-9135-41FF-BEB8-3DD2A0535CEC"}
 */
function getColumnChangedDataDiff(record, sColumnName) {
	var nDiff = 0;
	var aColumnChangedData = getColumnChangedData(record, sColumnName);
	
	if (aColumnChangedData && aColumnChangedData.length == 2) {
		if (scopes.avMath.isNumber(aColumnChangedData[0]) && scopes.avMath.isNumber(aColumnChangedData[1])) {
			nDiff = aColumnChangedData[1] - aColumnChangedData[0];
			
			if (!scopes.avMath.isNumber(nDiff)) {
				nDiff = 0;
			}
		}
	}
	
	return nDiff;
}

/**
 * @public 
 * 
 * @param {JSFoundSet} fs
 * @param {String} sTableName
 * @param {String} sPKColName
 * @param {String} sFKColName
 * @param {UUID} sFKColID
 * 
 * @return {JSRecord}
 *
 * @properties={typeid:24,uuid:"D5617145-DE9B-40EF-99D0-CE1CD01115E6"}
 */
function getFirstRecUsingOldSort(fs, sTableName, sPKColName, sFKColName, sFKColID) {
	var rFirstRec = null;
	
	if (fs) {
		var nNumRecs = fs.getSize();
		
		if (nNumRecs == 1) {
			rFirstRec = fs.getRecord(1);
		}
		else if (nNumRecs > 1 && sTableName && sPKColName && sFKColName, sFKColID) {
			var sSQL = "SELECT TOP 1 " + sPKColName + " \
						FROM " + sTableName + " \
						WHERE \
							org_id = ? \
							AND " + sFKColName + " = ? \
						ORDER BY CONVERT(NVARCHAR(36), " + sPKColName + ")";
			var aArgs = [globals.org_id, sFKColID.toString()];
			
			// have to use getDataset, if i load a foundset using sql it picks the wrong rec, even tho ive specified a sort in sql and TOP 1
			var ds = scopes.avDB.getDataset(sSQL, aArgs);
			
			if (ds && ds.getMaxRowIndex() > 0) {
				rFirstRec = scopes.avDB.getRec(sTableName, [sPKColName], [ds.getValue(1, 1)]);
			}
		}
	}
	
	return rFirstRec;
}

/**
 * @public 
 * 
 * @param {String} sTable
 * @param {String} sIndex
 * @param {String} [sDatabase]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"0FB1E3D1-8B38-464F-8844-3D54EFD4E531"}
 */
function dropIndex(sTable, sIndex, sDatabase){
	var bDroppedIndex = false; 
	
	if (sTable && sIndex) {
		if (!sDatabase) {
			sDatabase = globals.avBase_dbase_avanti;
		}
		
		try {
			bDroppedIndex = RunSQL("IF EXISTS(SELECT * FROM sys.indexes WHERE Name = '" + sIndex + "') DROP INDEX " + sTable + "." + sIndex, null, null, sDatabase);
		}
		catch (ex) {
		}
	}
	
	return bDroppedIndex;
}

/**
 * CREATES AN SQL 'OUTPUT' STATEMENT TO BE USED WITH scopes.avUtils.broadcastDataChanges()
 * 
 * @public 
 * 
 * @param {String} sTableName
 * @param {String} sPKName
 * @param {String} sRecordAction - scopes.avUtils.e_spBroadcastRecordAction
 * @param {UUID|String} [uSessionID] - will default to application.getUUID() if not present 
 * @param {Number} [nBroadcastOrder] - will default to 1 if not present
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"6E6589B0-9E95-40B5-B042-3BD27F472300"}
 */
function createBroadcastOutputStatement(sTableName, sPKName, sRecordAction, uSessionID, nBroadcastOrder) {
	var sOutputStatement = " ";
	
	if (sTableName && sPKName && sRecordAction) {
		var sEmpID = globals.avBase_employeeUUID ? "'" + globals.avBase_employeeUUID + "'" : "null"; 
		
		if (!nBroadcastOrder) {
			nBroadcastOrder = 1;
		}
		
		if (!uSessionID && uDataBroadcastSessionID) {
			uSessionID = uDataBroadcastSessionID;
		}

		if (uSessionID) {
			uSessionID = uSessionID.toString();
			
			var sActionPrefix = sRecordAction == scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION ? "DELETED" :  "INSERTED";

			sOutputStatement = " OUTPUT '" + globals.org_id + "', " + nBroadcastOrder + ", '" + sRecordAction + "', " + sActionPrefix + "." + sPKName + ", '" + uSessionID + "', '" + sTableName + 
				"', " + sEmpID + ", GetDate() INTO sys_servoy_broadcast ";
		}
	}
	
	return sOutputStatement;
}

/**
 * Converts an array of records to a foundset
 * 
 * @public 
 * 
 * @param {Array<JSRecord>} aRecords
 * @param {String} sTable
 * @param {String} sPKName
 * @param {Boolean} [bBypassOrgID]
 * 
 * @return {JSFoundset}
 *
 * @properties={typeid:24,uuid:"5CEA7FFC-4674-440F-90B9-FCD74EFEAB18"}
 */
function recordArrayToFoundset(aRecords, sTable, sPKName, bBypassOrgID) {
	try {
		/***@type{JSFoundSet} */
		var fs = sTable ? datasources.db[globals.avBase_dbase_avanti][sTable].getFoundSet() : null;
		
		if (aRecords && aRecords.length > 0 && sPKName && fs) {
			var aIDs = [];
			
			for (var i = 0; i < aRecords.length; i++) {
				aIDs.push(aRecords[i][sPKName]);
			}
			
			var sIDs = scopes.avText.arrayToString(aIDs, ",", "'");
			var sSQL = "SELECT " + sPKName + " FROM " + sTable + " WHERE " + sPKName + " IN (" + sIDs + ")";
			var aArgs = [];
			
			if (!bBypassOrgID) {
				sSQL += " AND org_id = ?";
				aArgs = [globals.org_id];
			}
			
			fs.loadRecords(sSQL, aArgs);
		}
		
		return fs;
	}
	catch (ex) {
		scopes.avUtils.devLogError("recordArrayToFoundset", ex);
		return null;
	}
}

/**
 * @param {String} sKey - primary key to use for foundset filterparam
 * @param {Object} oSQL - normal oSQL object
 * @param {String} oSQL.sql - SQL statement to run (Required)
 * @param {Array} [oSQL.args] - arguments to replace in string
 * @param {String}	[oSQL.server] - Defaults to globals.avBase_dbase_avanti
 * @param {String} 	[oSQL.table]
 * @param sFilterName - name of foundset filter to use
 * 
 * @return {JSFoundSet}
 *
 * @properties={typeid:24,uuid:"A77C3860-C527-4D2B-B342-FB6676E5B941"}
 */
function convertSqlToFoundsetFilter(sKey, oSQL, sFilterName) {
	
	if (!oSQL.sql) {
        application.output("Error: No oSQL.sql was passed to avUtilities_sqlFoundest(oSQL)");
        return null;
    }
    if (!oSQL.table || oSQL.table.length == 0) {
        application.output("Error: No oSQL.table was passed to avUtilities_sqlFoundest(oSQL)");
        return null;
    }
    if (!oSQL.server) {
        oSQL.server = globals.avBase_dbase_avanti;
    }
    if (!oSQL.args) {
        oSQL.args = [];
    } else {
	
		/***@type {{sql:String, args:Array<String>, server:String, maxRows:Number, table:String}}*/
		for (var i = 0; i < oSQL.args.length; i++) {
			//only replacing the first un-replaced ? so that the order of parameter is not changed
			if(oSQL.args[i] == null || oSQL.args[i] == undefined){
				oSQL.sql = oSQL.sql.replace(/=\s?\?/, " is null ");
			}
			else{
			oSQL.sql = oSQL.sql.replace("?", "'" + oSQL.args[i] + "'");
			}
		}
    }
    
    // Get the foundset for the table
    var fsTable = databaseManager.getFoundSet(oSQL.server, oSQL.table);
	removeFoundsetFilter(fsTable, sFilterName);
	fsTable.addFoundSetFilterParam(sKey.toString(), "sql:in", oSQL.sql, sFilterName);
	fsTable.loadAllRecords();
	return fsTable;

}

/**
 * @param {JSFoundSet} fs - foundset to use
 * @param sFilterName
 *
 * @properties={typeid:24,uuid:"51549E67-4F1A-4BA3-AF7F-08149F078B64"}
 */
function removeFoundsetFilter(fs, sFilterName) {

	fs.removeFoundSetFilterParam(sFilterName);
}


/**
 * @public 
 * 
 * @param {JSFoundSet} fs
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"A814ACE1-9391-4ED4-8E7C-B68B24F5CEAA"}
 */
function getPKName(fs) {
	var sPKName = null;
	var oTable = databaseManager.getTable(fs.getDataSource());

	if (oTable) {
		var aPKCols = oTable.getRowIdentifierColumnNames();
	
		if (aPKCols && aPKCols.length == 1) {
			sPKName = aPKCols[0];
		}
	}
	
	return sPKName;
}