<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.12.2.final using JasperReports Library version 6.12.2-75c5e90a222ab406e416cbf590a5397028a52de3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="EstCostPriceSummary_func_SR" pageWidth="752" pageHeight="595" orientation="Landscape" columnWidth="752" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="139dbdf4-8be3-40e7-8d12-82f450522b4e">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="CellBorders">
		<box>
			<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#CCCCCC"/>
			<leftPen lineWidth="0.5" lineColor="#CCCCCC"/>
			<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#CCCCCC"/>
			<rightPen lineWidth="0.5" lineColor="#CCCCCC"/>
		</box>
	</style>
	<parameter name="pSQL" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["SELECT rev.*, est.*, det.*, detQty.* FROM _v_est_rev_det_qty detQty INNER JOIN _v_est_rev_det det ON detQty.ordrevd_id = det.ordrevd_id INNER JOIN _v_est_rev rev ON det.ordrevh_id = rev.ordrevh_id INNER JOIN _v_est est ON rev.ordh_id = est.ordh_id"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDate" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["dd-MM-yyyy"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDateSmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[( $P{formatDate}.equals( "dd MMM yyyy" ) ? "dd/MM/yyyy" : "MM/dd/yyyy" )]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrency" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["$#,##0.00"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrencySmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{formatCurrency}.substring(0,$P{formatCurrency}.length()-3).toString()]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["H:\\Avanti_GIT\\jasper_reports\\Estimate Cost Price Report\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="pDetQty" class="java.lang.Integer" isForPrompting="false"/>
	<parameter name="pTaskID" class="java.lang.String" isForPrompting="false"/>
	<parameter name="pDetQtyID" class="java.lang.String" isForPrompting="false"/>
	<queryString>
		<![CDATA[SELECT
	taskQty.*,
	secTask.*
FROM _v_est_rev_det_sec_task_qty_est taskQty
OUTER APPLY (SELECT TOP 1 * FROM _v_est_rev_det_sec_task st WHERE taskQty.ordrevdstask_id = st.ordrevdstask_id AND taskQty.ordrevdqty_id = st.ordrevdqty_id_ordsp ) secTask
WHERE taskQty.ordrevdqty_qty = $P{pDetQty} AND taskQty.ordrevdstask_id = $P{pTaskID}  AND taskQty.ordrevdqty_id=$P{pDetQtyID}]]>
	</queryString>
	<field name="ordrevd_id" class="java.lang.String"/>
	<field name="ordrevh_id" class="java.lang.String"/>
	<field name="ordh_id" class="java.lang.String"/>
	<field name="ordrevdqty_id" class="java.lang.String"/>
	<field name="ordrevdqty_qty" class="java.lang.Integer"/>
	<field name="ordrevdstqty_id" class="java.lang.String"/>
	<field name="ordrevdstask_id" class="java.lang.String"/>
	<field name="item_id" class="java.lang.String"/>
	<field name="Item1Desc" class="java.lang.String"/>
	<field name="item_id_2" class="java.lang.String"/>
	<field name="Item2Desc" class="java.lang.String"/>
	<field name="ordrevdstqty_qty_item" class="java.lang.Double"/>
	<field name="ordrevdstqty_qty_item2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_orig2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_new2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_over2" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_ori2" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_new2" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_ove2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_item_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_item_var" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_item_var_pct" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_item_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_item_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_item_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_item_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_lab_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_lab_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_lab_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_lab_var" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_lab_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_lab_var_pct" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_lab_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_mrk_pct_lab_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_lab_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_lab_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_lab_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_var" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_cost_var_pct" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_mrk_pct_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_mrk_pct_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_new" class="java.lang.Double"/>
	<field name="ordrevdstqty_margin" class="java.lang.Double"/>
	<field name="ordrevdstqty_margin_pct" class="java.lang.Double"/>
	<field name="ordrevdstqty_spoils" class="java.lang.Integer"/>
	<field name="ordrevdstqty_time_run" class="java.lang.Double"/>
	<field name="ordrevdstqty_time_setup" class="java.lang.Double"/>
	<field name="ordrevdstqty_time_total" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_run" class="java.lang.Double"/>
	<field name="ordrevdstqty_cost_setup" class="java.lang.Double"/>
	<field name="ordrevdstqty_is_taxable" class="java.lang.Integer"/>
	<field name="Expr1" class="java.lang.Double"/>
	<field name="ordrevdstqty_qty_task" class="java.lang.Integer"/>
	<field name="ordrevdstqty_desc" class="java.lang.String"/>
	<field name="ordrevdstqty_t_cost_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_paper_cost_over" class="java.lang.Integer"/>
	<field name="ordrevdstqty_overlap" class="java.lang.Double"/>
	<field name="ordrevdstqty_weight" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_cost" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_price" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_mrk_orig" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_mrk_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_mrk_new" class="java.lang.Double"/>
	<field name="ordrevdstask_id_orig" class="java.lang.String"/>
	<field name="priceSetup" class="java.lang.Double"/>
	<field name="priceRun" class="java.lang.Double"/>
	<field name="priceIntLabor" class="java.lang.Double"/>
	<field name="priceHelper" class="java.lang.Double"/>
	<field name="costIntLabor" class="java.lang.Double"/>
	<field name="costHelper" class="java.lang.Double"/>
	<field name="mrkUpRun" class="java.lang.Double"/>
	<field name="mrkUpSetup" class="java.lang.Double"/>
	<field name="mrkUpIntLabor" class="java.lang.Double"/>
	<field name="mrkUpHelper" class="java.lang.Double"/>
	<field name="item1_qty_decimals" class="java.lang.Integer"/>
	<field name="item2_qty_decimals" class="java.lang.Integer"/>
	<field name="Item3Desc" class="java.lang.String"/>
	<field name="item3_qty_decimals" class="java.lang.Integer"/>
	<field name="ordrevdstqty_coil_item_id" class="java.lang.String"/>
	<field name="ordrevdstqty_calc_spine_thick" class="java.lang.Double"/>
	<field name="ordrevdstqty_calc_coil_size" class="java.lang.Double"/>
	<field name="ordrevdstqty_coil_qty" class="java.lang.Integer"/>
	<field name="ordrevdstqty_overide_coil_size" class="java.lang.Double"/>
	<field name="item1_cost" class="java.lang.Double"/>
	<field name="item2_cost" class="java.lang.Double"/>
	<field name="item3_cost" class="java.lang.Double"/>
	<field name="ordrevds_id" class="java.lang.String"/>
	<field name="sequence_nr" class="java.lang.Integer"/>
	<field name="ordrevdqty_id_ordsp" class="java.lang.String"/>
	<field name="task_description" class="java.lang.String"/>
	<field name="task_is_outsourced" class="java.lang.Integer"/>
	<field name="tasktype_id" class="java.lang.Integer"/>
	<field name="task_show_on_quote" class="java.lang.Integer"/>
	<field name="task_append_var_name" class="java.lang.Integer"/>
	<field name="task_quote_description" class="java.lang.String"/>
	<field name="JDFFold" class="java.lang.String"/>
	<field name="TaskFunctionalDescription" class="java.lang.String"/>
	<field name="TaskFunctionalSortOrder" class="java.lang.Integer"/>
	<field name="PaperSize" class="java.lang.String"/>
	<field name="PressName" class="java.lang.String"/>
	<field name="Imposition" class="java.lang.String"/>
	<field name="cut_type" class="java.lang.String"/>
	<field name="mpress_id" class="java.lang.String"/>
	<field name="org_id" class="java.lang.String"/>
	<field name="ordrevdsmodel_id" class="java.lang.String"/>
	<field name="task_id" class="java.lang.String"/>
	<field name="mpress_plates" class="java.lang.Double"/>
	<field name="mpress_plate_cost" class="java.lang.Double"/>
	<field name="mpress_platemr_time" class="java.lang.Double"/>
	<field name="mpress_platemr_cost" class="java.lang.Double"/>
	<field name="mpress_explates" class="java.lang.Integer"/>
	<field name="mpress_explate_cost" class="java.lang.Double"/>
	<field name="mpress_explatemr_time" class="java.lang.Double"/>
	<field name="mpress_explatemr_cost" class="java.lang.Double"/>
	<field name="mpress_paper_desc" class="java.lang.String"/>
	<field name="mpress_paper_min_size" class="java.lang.String"/>
	<field name="mpress_pages_per_form" class="java.lang.Integer"/>
	<field name="mpress_press_sheets" class="java.lang.Double"/>
	<field name="mpress_parent_sheets" class="java.lang.Double"/>
	<field name="mpress_paper_cost" class="java.lang.Double"/>
	<field name="mpress_paper_weight" class="java.lang.Double"/>
	<field name="mpress_description" class="java.lang.String"/>
	<field name="mpress_passes" class="java.lang.Integer"/>
	<field name="mpress_firstmr_time" class="java.lang.Double"/>
	<field name="mpress_firstmr_cost" class="java.lang.Double"/>
	<field name="mpress_addmr_time" class="java.lang.Double"/>
	<field name="mpress_addmr_cost" class="java.lang.Double"/>
	<field name="mpress_wash_time" class="java.lang.Double"/>
	<field name="mpress_wash_cost" class="java.lang.Double"/>
	<field name="mpress_wtmr_time" class="java.lang.Double"/>
	<field name="mpress_wtmr_cost" class="java.lang.Double"/>
	<field name="mpress_run_time" class="java.lang.Double"/>
	<field name="mpress_run_cost" class="java.lang.Double"/>
	<field name="mpress_perfscoremr_time" class="java.lang.Double"/>
	<field name="mpress_perfscoremr_cost" class="java.lang.Double"/>
	<field name="mpress_ink_cost" class="java.lang.Double"/>
	<field name="mpress_inkmr_time" class="java.lang.Double"/>
	<field name="mpress_inkmr_cost" class="java.lang.Double"/>
	<field name="mpress_ink_nr" class="java.lang.Integer"/>
	<field name="mpress_ink_diff" class="java.lang.Integer"/>
	<field name="mpress_ink_nr_back" class="java.lang.Integer"/>
	<field name="mpress_ink_nr_front" class="java.lang.Integer"/>
	<field name="mpress_image_size_length" class="java.lang.Double"/>
	<field name="mpress_image_size_width" class="java.lang.Double"/>
	<field name="mpress_image_area" class="java.lang.Double"/>
	<field name="mpress_total_time" class="java.lang.Double"/>
	<field name="mpress_total_cost" class="java.lang.Double"/>
	<field name="mpress_spoils_cutter" class="java.lang.Integer"/>
	<field name="mpress_spoils_folder" class="java.lang.Integer"/>
	<field name="mpress_spoils_binder" class="java.lang.Integer"/>
	<field name="impmodel_id" class="java.lang.String"/>
	<field name="mpress_model_up" class="java.lang.Integer"/>
	<field name="mpress_model_out" class="java.lang.Integer"/>
	<field name="mpress_model_forms" class="java.lang.Integer"/>
	<field name="mpress_is_selected" class="java.lang.Integer"/>
	<field name="mpress_quantity" class="java.lang.Integer"/>
	<field name="mpress_spoils_run" class="java.lang.Integer"/>
	<field name="mpress_spoils_mr" class="java.lang.Integer"/>
	<field name="item_id_paper" class="java.lang.String"/>
	<field name="mpress_spoils_finishing" class="java.lang.Integer"/>
	<field name="mpress_spoils_other" class="java.lang.Integer"/>
	<field name="mpress_imposition" class="java.lang.String"/>
	<field name="mpress_paperbrand_id" class="java.lang.String"/>
	<field name="mpress_paper_min_dim1" class="java.lang.Double"/>
	<field name="mpress_paper_min_dim2" class="java.lang.Double"/>
	<field name="mpress_press_paper_max_width" class="java.lang.Double"/>
	<field name="mpress_press_paper_max_height" class="java.lang.Double"/>
	<field name="mpress_press_roll_size_max" class="java.lang.Double"/>
	<field name="mpress_press_uses_roll" class="java.lang.Integer"/>
	<field name="taskmachine_id" class="java.lang.String"/>
	<field name="mpress_setup_time" class="java.lang.Double"/>
	<field name="mpress_setup_cost" class="java.lang.Double"/>
	<field name="mpress_impressions" class="java.lang.Double"/>
	<field name="mpress_min_order_qty" class="java.lang.Integer"/>
	<field name="mpress_minqty_paper_weight" class="java.lang.Double"/>
	<field name="mpress_minqty_parent_sheets" class="java.lang.Integer"/>
	<field name="mpress_minqty_paper_cost" class="java.lang.Double"/>
	<field name="mpress_wtmr_price" class="java.lang.Double"/>
	<field name="mpress_setup_price" class="java.lang.Double"/>
	<field name="mpress_firstmr_price" class="java.lang.Double"/>
	<field name="mpress_addmr_price" class="java.lang.Double"/>
	<field name="mpress_platemr_price" class="java.lang.Double"/>
	<field name="mpress_explatemr_price" class="java.lang.Double"/>
	<field name="mpress_run_price" class="java.lang.Double"/>
	<field name="mpress_wash_price" class="java.lang.Double"/>
	<field name="mpress_perfscoremr_price" class="java.lang.Double"/>
	<field name="mpress_click_price" class="java.lang.Double"/>
	<field name="mpress_minqty_paper_price" class="java.lang.Double"/>
	<field name="mpress_paper_price" class="java.lang.Double"/>
	<field name="mpress_total_price" class="java.lang.Double"/>
	<field name="mpress_plate_price" class="java.lang.Double"/>
	<field name="mpress_explate_price" class="java.lang.Double"/>
	<field name="mpress_inkmr_price" class="java.lang.Double"/>
	<field name="mpress_ink_price" class="java.lang.Double"/>
	<field name="mpress_click_mrkup" class="java.lang.Double"/>
	<field name="mpress_click_cost" class="java.lang.Double"/>
	<field name="mpress_spoils_binder_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_cutter_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_finishing_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_folder_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_mr_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_other_over" class="java.lang.Integer"/>
	<field name="mpress_spoils_run_over" class="java.lang.Integer"/>
	<field name="mpress_total_spoils" class="java.lang.Integer"/>
	<field name="mpress_is_selected_over" class="java.lang.Integer"/>
	<field name="mpress_over_addmr_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_addmr_mins" class="java.lang.Integer"/>
	<field name="mpress_over_explatemr_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_explatemr_mins" class="java.lang.Integer"/>
	<field name="mpress_over_firstmr_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_firstmr_mins" class="java.lang.Integer"/>
	<field name="mpress_over_inkmix_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_inkmix_mins" class="java.lang.Integer"/>
	<field name="mpress_over_perf_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_perf_mins" class="java.lang.Integer"/>
	<field name="mpress_over_platemr_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_platemr_mins" class="java.lang.Integer"/>
	<field name="mpress_over_run_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_run_mins" class="java.lang.Integer"/>
	<field name="mpress_over_setup_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_setup_mins" class="java.lang.Integer"/>
	<field name="mpress_over_wash_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_wash_mins" class="java.lang.Integer"/>
	<field name="mpress_over_wtmr_hrs" class="java.lang.Integer"/>
	<field name="mpress_over_wtmr_mins" class="java.lang.Integer"/>
	<field name="Expr2" class="java.lang.String"/>
	<field name="Expr3" class="java.lang.String"/>
	<field name="Expr4" class="java.lang.String"/>
	<field name="tasksetup_id" class="java.lang.String"/>
	<field name="taskspoil_id" class="java.lang.String"/>
	<field name="ordrevdsqty_id" class="java.lang.String"/>
	<field name="Expr5" class="java.lang.String"/>
	<field name="ordrevdstqty_qty_via_forms" class="java.lang.Integer"/>
	<field name="ordrevdstqty_t_mrk_pct_over" class="java.lang.Double"/>
	<field name="taskfldopt_id_perf" class="java.lang.String"/>
	<field name="taskfldopt_id_score" class="java.lang.String"/>
	<field name="taskfldopt_id_inkjetting" class="java.lang.String"/>
	<field name="taskfldopt_id_numbering" class="java.lang.String"/>
	<field name="taskfldopt_id_other1" class="java.lang.String"/>
	<field name="taskfldopt_id_other2" class="java.lang.String"/>
	<field name="taskfldopt_id_other3" class="java.lang.String"/>
	<field name="taskstd_trimmer_option" class="java.lang.Integer"/>
	<field name="ordrevdstqty_t_item_over2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_item_ori2" class="java.lang.Double"/>
	<field name="ordrevdstqty_t_item_new2" class="java.lang.Double"/>
	<field name="ordrevdstqty_qty" class="java.lang.Integer"/>
	<field name="ordrevdstqty_spoils_over" class="java.lang.Integer"/>
	<field name="ordrevdstqty_qty_task_over" class="java.lang.Integer"/>
	<field name="ordrevdstqty_qty_item_over" class="java.lang.Integer"/>
	<field name="taskmachine_id_over" class="java.lang.String"/>
	<field name="ordrevdstqty_time_setup_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_time_run_over" class="java.lang.Double"/>
	<field name="taskspoil_id_over" class="java.lang.String"/>
	<field name="tasksetup_id_over" class="java.lang.String"/>
	<field name="ordrevdstqty_click_cost_over" class="java.lang.Double"/>
	<field name="ordrevdstqty_click_price_over" class="java.lang.Double"/>
	<field name="item_id_die" class="java.lang.String"/>
	<field name="ordrevdstqty_linear_inches" class="java.lang.Double"/>
	<field name="ordrevdstqty_width" class="java.lang.Double"/>
	<field name="taskmachine_description" class="java.lang.String"/>
	<field name="mpress_presssetup_cost" class="java.lang.Double"/>
	<field name="mpress_presssetup_price" class="java.lang.Double"/>
	<field name="mpress_presssetup_time" class="java.lang.Double"/>
	<field name="pressTaskTypeID" class="java.lang.Integer"/>
	<field name="SecTask_task_id" class="java.lang.String"/>
	<field name="mpress_help_time" class="java.lang.Double"/>
	<field name="mpress_help_cost" class="java.lang.Double"/>
	<field name="mpress_help_mrk" class="java.lang.Double"/>
	<field name="mpress_help_price" class="java.lang.Double"/>
	<field name="mpress_mrkup_help" class="java.lang.Double"/>
	<field name="mpress_help_price_x" class="java.lang.Double"/>
	<field name="ordrevdstask_notes" class="java.lang.String"/>
	<field name="sort_key" class="java.lang.String"/>
	<field name="ordrevds_id_gang" class="java.lang.String"/>
	<field name="ordrevds_line_type" class="java.lang.String"/>
	<variable name="vTotalLaborCost" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(
($F{mpress_presssetup_cost}.isNaN() ? 0 : $F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7)) ?  $F{mpress_presssetup_cost}.doubleValue() : $F{mpress_setup_cost}.doubleValue() )
+
($F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() )
+
($F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() )
+
($F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() )
+
($F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() )
+
($F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() )
+
($F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() )
+
($F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() )
+
($F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalMaterialCost" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(
($F{mpress_plate_cost}.isNaN() ? 0 : $F{mpress_plate_cost}.doubleValue() )
+
($F{mpress_explate_cost}.isNaN() ? 0 : $F{mpress_explate_cost}.doubleValue() )
+
($F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalPrice" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(
($F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))  ? $F{mpress_presssetup_price} : $F{mpress_setup_price} )
+
($F{mpress_firstmr_price}.isNaN() ? 0 : $F{mpress_firstmr_price}.doubleValue() )
+
($F{mpress_addmr_price}.isNaN() ? 0 : $F{mpress_addmr_price}.doubleValue() )
+
($F{mpress_run_price}.isNaN() ? 0 : $F{mpress_run_price}.doubleValue() )
+
($F{mpress_wtmr_price}.isNaN() ? 0 : $F{mpress_wtmr_price}.doubleValue() )
+
($F{mpress_wash_price}.isNaN() ? 0 : $F{mpress_wash_price}.doubleValue() )
+
($F{mpress_platemr_price}.isNaN() ? 0 : $F{mpress_platemr_price}.doubleValue() )
+
($F{mpress_plate_price}.isNaN() ? 0 : $F{mpress_plate_price}.doubleValue() )
+
($F{mpress_explatemr_price}.isNaN() ? 0 : $F{mpress_explatemr_price}.doubleValue() )
+
($F{mpress_explate_price}.isNaN() ? 0 : $F{mpress_explate_price}.doubleValue() )
+
($F{mpress_help_price}.isNaN() ? 0 : $F{mpress_help_price}.doubleValue() )
+
($F{mpress_click_price}.isNaN() ? 0 : $F{mpress_click_price}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalLaborPrice" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(
($F{mpress_presssetup_price}.isNaN() ? 0 : $F{mpress_presssetup_price}.doubleValue() )
+
($F{mpress_firstmr_price}.isNaN() ? 0 : $F{mpress_firstmr_price}.doubleValue() )
+
($F{mpress_addmr_price}.isNaN() ? 0 : $F{mpress_addmr_price}.doubleValue() )
+
($F{mpress_run_price}.isNaN() ? 0 : $F{mpress_run_price}.doubleValue() )
+
($F{mpress_wtmr_price}.isNaN() ? 0 : $F{mpress_wtmr_price}.doubleValue() )
+
($F{mpress_wash_price}.isNaN() ? 0 : $F{mpress_wash_price}.doubleValue() )
+
($F{mpress_platemr_price}.isNaN() ? 0 : $F{mpress_platemr_price}.doubleValue() )
+
($F{mpress_explatemr_price}.isNaN() ? 0 : $F{mpress_explatemr_price}.doubleValue() )
+
($F{mpress_help_price}.isNaN() ? 0 : $F{mpress_help_price}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalMaterialPrice" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(

($F{mpress_plate_price}.isNaN() ? 0 : $F{mpress_plate_price}.doubleValue() )
+
($F{mpress_explate_price}.isNaN() ? 0 : $F{mpress_explate_price}.doubleValue() )
+
($F{mpress_click_price}.isNaN() ? 0 : $F{mpress_click_price}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalCost" class="java.lang.Double">
		<variableExpression><![CDATA[new java.lang.Double(
($F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))  ? $F{mpress_presssetup_cost} : $F{mpress_setup_cost} )
+
($F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() )
+
($F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() )
+
($F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() )
+
($F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() )
+
($F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() )
+
($F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() )
+
($F{mpress_plate_cost}.isNaN() ? 0 : $F{mpress_plate_cost}.doubleValue() )
+
($F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() )
+
($F{mpress_explate_cost}.isNaN() ? 0 : $F{mpress_explate_cost}.doubleValue() )
+
($F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() )
+
($F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() )
)]]></variableExpression>
	</variable>
	<variable name="vTotalTime" class="java.lang.Double">
		<variableExpression><![CDATA[new Double(
($F{mpress_presssetup_time}.isNaN() ? 0 : (($F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))) ? $F{mpress_presssetup_time}.doubleValue() : $F{mpress_setup_time}.doubleValue() ) )
+
($F{mpress_firstmr_time}.isNaN() ? 0 : $F{mpress_firstmr_time}.doubleValue() )
+
($F{mpress_addmr_time}.isNaN() ? 0 : $F{mpress_addmr_time}.doubleValue() )
+
($F{mpress_run_time}.isNaN() ? 0 : $F{mpress_run_time}.doubleValue() )
+
($F{mpress_wash_time}.isNaN() ? 0 : $F{mpress_wash_time}.doubleValue() )
+
($F{mpress_platemr_time}.isNaN() ? 0 : $F{mpress_platemr_time}.doubleValue() )
+
($F{mpress_explatemr_time}.isNaN() ? 0 : $F{mpress_explatemr_time}.doubleValue() )
+
($F{mpress_help_time}.isNaN() ? 0 : $F{mpress_help_time}.doubleValue() )
)]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="132">
			<staticText>
				<reportElement style="CellBorders" x="55" y="36" width="170" height="12" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Add MR Time]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" x="55" y="48" width="170" height="12" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Run Time]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" x="55" y="72" width="170" height="12" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Wash Up Time]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" x="55" y="24" width="170" height="12" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[First MR Time]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" x="55" y="12" width="170" height="12" forecolor="#000000" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Setup Time]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement style="CellBorders" x="55" y="84" width="170" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Plate (x" + $F{mpress_plates} + ")"]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatDate}]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="CellBorders" x="55" y="96" width="170" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Ex.Plate (x" + $F{mpress_explates} + ")"]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatDate}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="84" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_platemr_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="24" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_firstmr_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="12" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7)) ?  $F{mpress_presssetup_time} : $F{mpress_setup_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="96" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_explatemr_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="48" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_run_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="72" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wash_time}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="36" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_addmr_time}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="84" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_platemr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="24" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_firstmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="12" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7)) ?  $F{mpress_presssetup_cost} : $F{mpress_setup_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="96" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_explatemr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="48" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_run_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="72" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wash_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="36" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_addmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="84" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_platemr_cost}.isNaN() || $F{mpress_platemr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_platemr_price}.isNaN() ? 0 : $F{mpress_platemr_price}.doubleValue() ) -
    ( $F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() )
)
/
( $F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="24" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_firstmr_cost}.isNaN() || $F{mpress_firstmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_firstmr_price}.isNaN() ? 0 : $F{mpress_firstmr_price}.doubleValue() ) -
    ( $F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() )
)
/
( $F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="12" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))
?

        ($F{mpress_presssetup_cost}.isNaN() || $F{mpress_presssetup_cost}.equals(new Double(0))
           ? 0
        :
        new java.lang.Double(
        (
            ( $F{mpress_presssetup_price}.isNaN() ? 0 : $F{mpress_presssetup_price}.doubleValue() ) -
            ( $F{mpress_presssetup_cost}.isNaN() ? 0 : $F{mpress_presssetup_cost}.doubleValue() )
        )
        /
        ( $F{mpress_presssetup_cost}.isNaN() ? 0 : $F{mpress_presssetup_cost}.doubleValue() ) * 100
        ))

:

        ($F{mpress_setup_cost}.isNaN() || $F{mpress_setup_cost}.equals(new Double(0))
           ? 0
        :
        new java.lang.Double(
        (
            ( $F{mpress_setup_price}.isNaN() ? 0 : $F{mpress_setup_price}.doubleValue() ) -
            ( $F{mpress_setup_cost}.isNaN() ? 0 : $F{mpress_setup_cost}.doubleValue() )
        )
        /
        ( $F{mpress_setup_cost}.isNaN() ? 0 : $F{mpress_setup_cost}.doubleValue() ) * 100
        ))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="96" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_explatemr_cost}.isNaN() || $F{mpress_explatemr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_explatemr_price}.isNaN() ? 0 : $F{mpress_explatemr_price}.doubleValue() ) -
    ( $F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() )
)
/
( $F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="48" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_run_cost}.isNaN() || $F{mpress_run_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_run_price}.isNaN() ? 0 : $F{mpress_run_price}.doubleValue() ) -
    ( $F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() )
)
/
( $F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="72" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_wash_cost}.isNaN() || $F{mpress_wash_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_wash_price}.isNaN() ? 0 : $F{mpress_wash_price}.doubleValue() ) -
    ( $F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() )
)
/
( $F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="36" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_addmr_cost}.isNaN() || $F{mpress_addmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_addmr_price}.isNaN() ? 0 : $F{mpress_addmr_price}.doubleValue() ) -
    ( $F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() )
)
/
( $F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="372" y="84" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_plate_cost} == null ? new java.lang.Double(0) : $F{mpress_plate_cost})]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="372" y="96" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_explate_cost} == null ? "" : $F{mpress_explate_cost})]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="84" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{mpress_plate_cost}.isNaN() ||  $F{mpress_plate_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_plate_price}.isNaN() ? 0 : $F{mpress_plate_price}.doubleValue() ) -
    ( $F{mpress_plate_cost}.isNaN() ? 0 : $F{mpress_plate_cost}.doubleValue() )
)
/
( $F{mpress_plate_cost}.isNaN() ? 0 : $F{mpress_plate_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="96" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{mpress_explate_cost}.isNaN() ||  $F{mpress_explate_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_explate_price}.isNaN() ? 0 : $F{mpress_explate_price}.doubleValue() ) -
    ( $F{mpress_explate_cost}.isNaN() ? 0 : $F{mpress_explate_cost}.doubleValue() )
)
/
( $F{mpress_explate_cost}.isNaN() ? 0 : $F{mpress_explate_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="84" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() )
+
( $F{mpress_plate_cost}.isNaN() ? 0 : $F{mpress_plate_cost}.doubleValue() )]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="24" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_firstmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="582" y="12" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))  ? $F{mpress_presssetup_cost} : $F{mpress_setup_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="96" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() )
+
( $F{mpress_explate_cost}.isNaN() ? 0 : $F{mpress_explate_cost}.doubleValue() )]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="48" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_run_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="72" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wash_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="36" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_addmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="12" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7))
?
            ($F{mpress_presssetup_cost}.isNaN() || $F{mpress_presssetup_cost}.equals(new Double(0))
               ? 0
            :
            new java.lang.Double(
            (
                ( $F{mpress_presssetup_price}.isNaN() ? 0 : $F{mpress_presssetup_price}.doubleValue() ) -
                ( $F{mpress_presssetup_cost}.isNaN() ? 0 : $F{mpress_presssetup_cost}.doubleValue() )
            )
            /
            ( $F{mpress_presssetup_cost}.isNaN() ? 0 : $F{mpress_presssetup_cost}.doubleValue() ) * 100
            ))
:
            ($F{mpress_setup_cost}.isNaN() || $F{mpress_setup_cost}.equals(new Double(0))
               ? 0
            :
            new java.lang.Double(
            (
                ( $F{mpress_setup_price}.isNaN() ? 0 : $F{mpress_setup_price}.doubleValue() ) -
                ( $F{mpress_setup_cost}.isNaN() ? 0 : $F{mpress_setup_cost}.doubleValue() )
            )
            /
            ( $F{mpress_setup_cost}.isNaN() ? 0 : $F{mpress_setup_cost}.doubleValue() ) * 100
            ))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="84" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.lang.Double(
($F{mpress_platemr_price}.isNaN() ? 0 : $F{mpress_platemr_price}.doubleValue() )
+
($F{mpress_plate_price}.isNaN() ? 0 : $F{mpress_plate_price}.doubleValue() )
)]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="24" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_firstmr_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="12" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pressTaskTypeID}.equals(new Integer(2)) || $F{pressTaskTypeID}.equals(new Integer(7)) ? $F{mpress_presssetup_price} : $F{mpress_setup_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="96" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.lang.Double(
($F{mpress_explatemr_price}.isNaN() ? 0 : $F{mpress_explatemr_price}.doubleValue() )
+
($F{mpress_explate_price}.isNaN() ? 0 : $F{mpress_explate_price}.doubleValue() )
)]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="48" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_run_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="72" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wash_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="36" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_addmr_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="24" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_firstmr_cost}.isNaN() || $F{mpress_firstmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_firstmr_price}.isNaN() ? 0 : $F{mpress_firstmr_price}.doubleValue() ) -
    ( $F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() )
)
/
( $F{mpress_firstmr_cost}.isNaN() ? 0 : $F{mpress_firstmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="36" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_addmr_cost}.isNaN() || $F{mpress_addmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_addmr_price}.isNaN() ? 0 : $F{mpress_addmr_price}.doubleValue() ) -
    ( $F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() )
)
/
( $F{mpress_addmr_cost}.isNaN() ? 0 : $F{mpress_addmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="48" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_run_cost}.isNaN() || $F{mpress_run_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_run_price}.isNaN() ? 0 : $F{mpress_run_price}.doubleValue() ) -
    ( $F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() )
)
/
( $F{mpress_run_cost}.isNaN() ? 0 : $F{mpress_run_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="72" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_wash_cost}.isNaN() || $F{mpress_wash_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_wash_price}.isNaN() ? 0 : $F{mpress_wash_price}.doubleValue() ) -
    ( $F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() )
)
/
($F{mpress_wash_cost}.isNaN() ? 0 : $F{mpress_wash_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="84" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_platemr_cost}.isNaN() || $F{mpress_platemr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_platemr_price}.isNaN() ? 0 : $F{mpress_platemr_price}.doubleValue() ) -
    ( $F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() )
)
/
( $F{mpress_platemr_cost}.isNaN() ? 0 : $F{mpress_platemr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="96" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_explatemr_cost}.isNaN() || $F{mpress_explatemr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_explatemr_price}.isNaN() ? 0 : $F{mpress_explatemr_price}.doubleValue() ) -
    ( $F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() )
)
/
( $F{mpress_explatemr_cost}.isNaN() ? 0 : $F{mpress_explatemr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="12" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="12" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="24" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="24" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="36" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="36" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="48" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="48" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="72" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="72" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement style="CellBorders" x="0" y="0" width="752" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["     " + "     " + $F{PressName}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="582" y="0" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="0" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="0" width="65" height="12" forecolor="#000000" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="60" width="40" height="12" uuid="513917d2-0c39-4965-a5c6-068b207b34f3"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_wtmr_cost}.isNaN() || $F{mpress_wtmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_wtmr_price}.isNaN() ? 0 : $F{mpress_wtmr_price}.doubleValue() ) -
    ( $F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() )
)
/
( $F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="60" width="40" height="12" uuid="cf33c2c8-a579-4460-9d4b-c5d179304128"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_wtmr_cost}.isNaN() || $F{mpress_wtmr_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_wtmr_price}.isNaN() ? 0 : $F{mpress_wtmr_price}.doubleValue() ) -
    ( $F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() )
)
/
( $F{mpress_wtmr_cost}.isNaN() ? 0 : $F{mpress_wtmr_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="60" width="65" height="12" uuid="ec9f8661-42a9-4f4a-b654-de047b3bf277"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wtmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="60" width="42" height="12" uuid="dda32718-de1c-4858-91b6-d47fecfaeb3e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wtmr_time}]]></textFieldExpression>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="60" width="65" height="12" forecolor="#000000" uuid="d2a55b1f-0cd5-46bd-8faa-5953c2b8ffa7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="60" width="40" height="12" uuid="b042e84b-b60b-44a1-a5b1-a5a00bb2c407"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="60" width="65" height="12" uuid="1dd727da-9a17-4d06-bfd8-4bc2e4c5e3ca"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wtmr_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<staticText>
				<reportElement style="CellBorders" x="55" y="60" width="170" height="12" forecolor="#000000" uuid="4b274b0b-d7a5-4821-8848-2cfb0d573471"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[WT Time]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="60" width="65" height="12" uuid="f165c80a-7d94-4a2f-af78-ae18f4ebb500"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_wtmr_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="108" width="40" height="12" uuid="3cbbc6e2-69af-4a6a-b45a-2942e8849202"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_help_cost}.isNaN() || $F{mpress_help_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_help_price}.isNaN() ? 0 : $F{mpress_help_price}.doubleValue() ) -
    ( $F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() )
)
/
($F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="108" width="65" height="12" uuid="196d29c0-d762-455a-b3b4-2c4b6765f313"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_help_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="108" width="65" height="12" uuid="81685b3f-ea92-4a80-856c-174e82d28bc3"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_help_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="108" width="40" height="12" uuid="5bc84c7c-f121-458f-b909-51600de7812b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_help_cost}.isNaN() || $F{mpress_help_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_help_price}.isNaN() ? 0 : $F{mpress_help_price}.doubleValue() ) -
    ( $F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() )
)
/
( $F{mpress_help_cost}.isNaN() ? 0 : $F{mpress_help_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="108" width="65" height="12" uuid="2653fbc0-eb6f-4e3c-aa4d-9d0bb7e8050b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_help_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="108" width="42" height="12" uuid="df30b9f4-2fd0-4061-aa26-1d00206bd0b1"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_help_time}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement style="CellBorders" x="55" y="108" width="170" height="12" uuid="581cd6a5-c84a-4ba0-85c4-be9732c4e43e"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Helper]]></text>
			</staticText>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="372" y="108" width="65" height="12" forecolor="#000000" uuid="777f6c46-3171-4c42-bcb5-01abf152ee50"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="108" width="40" height="12" uuid="ef6b3c23-4207-492e-90fa-035f5c323835"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="12" width="65" height="12" forecolor="#000000" uuid="c7cd3dfe-c774-44b1-a8ff-58681d9aaf76"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="12" width="40" height="12" uuid="ed243cc2-9cc1-47df-8385-edd20d58f53b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="0" width="65" height="12" forecolor="#000000" uuid="c2f4c4d1-17bc-4078-b086-69b5ff7673b5"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="0" width="40" height="12" uuid="c51cd6d5-90ee-4778-b406-ee3540847f42"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="24" width="65" height="12" forecolor="#000000" uuid="68afa0c3-175b-4936-b746-53b0e9229a9a"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="24" width="40" height="12" uuid="b4fb06df-2554-43e1-ad8d-ea33754a1537"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="36" width="65" height="12" forecolor="#000000" uuid="ea604ca6-6941-4f09-bcf5-cc2843622ee3"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="36" width="40" height="12" uuid="2f7b1b4b-b6c1-4f25-a5f5-6c8fef4cb5d0"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="48" width="65" height="12" forecolor="#000000" uuid="af6c4d98-462b-464b-bbf3-ac19a4ed1fcd"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="48" width="40" height="12" uuid="753ce44d-f889-403a-8c9a-9d4ee176a558"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="60" width="65" height="12" forecolor="#000000" uuid="44089230-942f-4275-b955-b16145f80225"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="60" width="40" height="12" uuid="a671af44-2178-4ee9-900f-1fa58e337f23"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="72" width="65" height="12" forecolor="#000000" uuid="4662a791-72c5-49b9-9672-************"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="72" width="40" height="12" uuid="a5aea02f-96f6-4441-810e-5f5d2b6e4416"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="84" width="65" height="12" forecolor="#000000" uuid="a02d79fd-8435-45fc-b71a-4e567923b63e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="84" width="40" height="12" uuid="5c261825-6666-4443-ab7c-e2d7a87ded91"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="96" width="65" height="12" forecolor="#000000" uuid="6ebc09aa-09c1-4897-8555-c80b7bcfe712"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="96" width="40" height="12" uuid="a2b57b03-54fb-4698-b4aa-1734f0409995"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="108" width="65" height="12" forecolor="#000000" uuid="600381ca-16b1-4813-b21e-202734bc03b7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="108" width="40" height="12" uuid="bc899991-6ec9-460d-9bcd-3248572427df"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement style="CellBorders" x="55" y="120" width="170" height="12" uuid="1e5002c9-f609-49ef-967d-a9e08e1c43f3"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Clicks]]></text>
			</staticText>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="120" width="65" height="12" uuid="ce737daf-de4d-433d-8371-dec565f1c5a2"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_click_price}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="120" width="65" height="12" forecolor="#000000" uuid="ec4112b5-ed5c-42aa-8e5e-9e7f3366e09f"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="120" width="40" height="12" uuid="8d20e3d6-decc-4172-9347-6753c184ae71"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="120" width="42" height="12" uuid="d14f6f03-4e02-4b62-90a6-491772cd219e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="120" width="65" height="12" uuid="525d8926-39ef-4c2e-b859-720f23f51f86"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="120" width="40" height="12" uuid="50030fff-b56a-40b0-b8d2-b7e4fab68972"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="372" y="120" width="65" height="12" uuid="a1d7c8fe-d300-48c6-8e2b-bf2e3bf3effe"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_click_cost} == null ? "" : $F{mpress_click_cost})]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="120" width="40" height="12" uuid="5edff79b-cb28-433d-ae52-b934c700660e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $F{mpress_click_cost}.isNaN() ||  $F{mpress_click_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_click_price}.isNaN() ? 0 : $F{mpress_click_price}.doubleValue() ) -
    ( $F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() )
)
/
( $F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="120" width="65" height="12" uuid="3e45edab-21f8-4419-8246-bea636b345e7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mpress_click_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="120" width="40" height="12" uuid="a23c0200-3141-48ab-99f5-1947ec662520"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{mpress_click_cost}.isNaN() || $F{mpress_click_cost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $F{mpress_click_price}.isNaN() ? 0 : $F{mpress_click_price}.doubleValue() ) -
    ( $F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() )
)
/
($F{mpress_click_cost}.isNaN() ? 0 : $F{mpress_click_cost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="12" splitType="Stretch">
			<rectangle>
				<reportElement mode="Opaque" x="55" y="0" width="697" height="12" backcolor="#ECECEC" uuid="5f7b8bfb-f0b6-4397-8221-d5539952b1e3"/>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement style="CellBorders" x="55" y="0" width="170" height="12" uuid="c535e8f7-0c1c-4e65-a57d-74b462ccb779"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<text><![CDATA[Total  ]]></text>
			</staticText>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="225" y="0" width="42" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vTotalTime}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="267" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vTotalLaborCost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="332" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[($V{vTotalLaborCost}.isNaN() || $V{vTotalLaborCost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $V{vTotalLaborPrice}.isNaN() ? 0 : $V{vTotalLaborPrice}.doubleValue() ) -
    ( $V{vTotalLaborCost}.isNaN() ? 0 : $V{vTotalLaborCost}.doubleValue() )
)
/
( $V{vTotalLaborCost}.isNaN() ? 0 : $V{vTotalLaborCost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="372" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vTotalMaterialCost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="437" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $V{vTotalMaterialCost}.isNaN() ||  $V{vTotalMaterialCost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $V{vTotalMaterialPrice}.isNaN() ? 0 : $V{vTotalMaterialPrice}.doubleValue() ) -
    ( $V{vTotalMaterialCost}.isNaN() ? 0 : $V{vTotalMaterialCost}.doubleValue() )
)
/
( $V{vTotalMaterialCost}.isNaN() ? 0 : $V{vTotalMaterialCost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="582" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vTotalCost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="687" y="0" width="65" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{vTotalPrice}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="647" y="0" width="40" height="12" uuid="e04d5238-3d29-4474-8049-0b1da9c32c01"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph rightIndent="1"/>
				</textElement>
				<textFieldExpression><![CDATA[( $V{vTotalCost}.isNaN() ||  $V{vTotalCost}.equals(new Double(0))
   ? 0
:
new java.lang.Double(
(
    ( $V{vTotalPrice}.isNaN() ? 0 : $V{vTotalPrice}.doubleValue() ) -
    ( $V{vTotalCost}.isNaN() ? 0 : $V{vTotalCost}.doubleValue() )
)
/
( $V{vTotalCost}.isNaN() ? 0 : $V{vTotalCost}.doubleValue() ) * 100
))]]></textFieldExpression>
			</textField>
			<textField pattern="¤ #,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" mode="Transparent" x="477" y="0" width="65" height="12" forecolor="#000000" uuid="600d14cf-6e27-4e63-a16a-00ebfda7fddb"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement style="CellBorders" x="542" y="0" width="40" height="12" uuid="e74478f0-ebdb-46d1-8989-6a10734ee172"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
			</textField>
		</band>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
