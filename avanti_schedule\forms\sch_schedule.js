/**
 * @properties={typeid:35,uuid:"E6554F19-42B8-448D-BCCE-754491DF9EA7",variableType:-4}
 */
var _aInvalidJobStatuses = [scopes.avUtils.JOB_STATUS.Comboed, scopes.avUtils.JOB_STATUS.OnHold, scopes.avUtils.JOB_STATUS.Completed, scopes.avUtils.JOB_STATUS.Staged, 
	scopes.avUtils.JOB_STATUS.Scheduled, scopes.avUtils.JOB_STATUS.Cancelled];

/**
 * @properties={typeid:35,uuid:"37CBAFB7-2ECD-4373-A830-D66FC8F63F40",variableType:-4}
 */
var _bScheduleEmps = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5A9F3303-8FCE-42FB-A0C5-40096E53F27E"}
 */
var _fsLoadSql = "";

/**
 * @properties={typeid:35,uuid:"A05897A9-07CD-4B37-9064-6A7D694ADBAE",variableType:-4}
 */
var _fsLoadSqlArgs = [];

/**
 * @type {JSDataSet}
 * @properties={typeid:35,uuid:"51B699FB-4C9F-4F25-9F2E-FE82C7AA87C4",variableType:-4}
 */
var _fsLoadDataset = null;

/**
 * @type {JSFoundset<db:/avanti/sa_order_revision_detail_section>}
 * @properties={typeid:35,uuid:"C574051A-95CD-4776-B48F-A041A6111098",variableType:-4}
 */
var _fsLoadFoundset = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D4A6182D-28A8-427E-94F1-4EB93D352A70",variableType:4}
 */
var _fsLoadDatasetCount = 0;

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"8069F3EF-7201-4304-BF6A-5A54898B700A"}
 */
function dc_duplicate(_event, _triggerForm)
{
	var sForm = globals.svy_nav_form_name;
	
	/*** @type{JSRecord<db:/avanti/sch_schedule>} ***/
	var rOrigSch = forms[sForm].foundset.getSelectedRecord();
	
	// Framework will duplicate the original record
	_super.dc_duplicate(_event, _triggerForm);
	
	// Need to duplicate the dependencies
	var rNewSch = forms.sch_schedule_dtl.foundset.getSelectedRecord();
	rNewSch.sch_desc += " ...duplicated";
	
	// Duplicate should never be the active one
	rNewSch.sch_flg_published = 0;
	
	
	var rNewMs;
	var rOrigMs;
	var rNew;
//	var rOrig;
	var iSize1, iSize2;
	var i, j;
	
	// Clone all the original milestones, dep and groups
	if (utils.hasRecords(rOrigSch.sch_schedule_to_sch_milestone))
	{
		iSize1 = rOrigSch.sch_schedule_to_sch_milestone.getSize();
		for (i = 1; i <= iSize1; i++)
		{
			rOrigMs = rOrigSch.sch_schedule_to_sch_milestone.getRecord(i);
			rNewMs = rOrigSch.sch_schedule_to_sch_milestone.getRecord(rOrigSch.sch_schedule_to_sch_milestone.duplicateRecord(i,false,true));
			rNewMs.sch_id = rNewSch.sch_id;
			
			// Duplicate dep
			if (utils.hasRecords(rOrigMs.sch_milestone_to_sch_milestone_dep))
			{
				iSize2 = rOrigMs.sch_milestone_to_sch_milestone_dep.getSize();
				for (j = 1; j <= iSize2; j++)
				{
//					rOrig = rOrigMs.sch_milestone_to_sch_milestone_dep.getRecord(j);
					rNew = rOrigMs.sch_milestone_to_sch_milestone_dep.getRecord(rOrigMs.sch_milestone_to_sch_milestone_dep.duplicateRecord(j,false,true));
					rNew.ms_id = rNewMs.ms_id;
				}
			}
			// Duplicate groups
			if (utils.hasRecords(rOrigMs.sch_milestone_to_sch_milestone_group))
			{
				iSize2 = rOrigMs.sch_milestone_to_sch_milestone_group.getSize();
				for (j = 1; j <= iSize2; j++)
				{
//					rOrig = rOrigMs.sch_milestone_to_sch_milestone_group.getRecord(j);
					rNew = rOrigMs.sch_milestone_to_sch_milestone_group.getRecord(rOrigMs.sch_milestone_to_sch_milestone_group.duplicateRecord(j,false,true));
					rNew.ms_id = rNewMs.ms_id;
				}
			}
		}
	}
	
	// Save everything, and put the user right back in to edit mode
	dc_save(_event,_triggerForm);
	dc_edit(_event,_triggerForm);
}

/**
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 * @param {String} scheduleDirection
 * @param {Date} [schBasedOnDate]
 * @param {Boolean} [hideMsg]
 * @param {Boolean} [bDontFailOnValidateMilestones] - if being called from 'group jobs by press' then we don't
 *                                                    want to return false if validateMilestones() fails. then 
 *                                                    it looks like we couldn't schedule all jobs on the same press
 *                                                    and it abandons 'group jobs by press'. if validateMilestones() 
 *                                                    fails that job just wont be scheduled.
 * @param {JSRecord<db:/avanti/sch_milestone>} [rTimeChangeMilestone]
 * @param {Boolean} [bCommitting]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E9DE5E70-07B2-4963-8304-2830EA19F922"}
 */
function scheduleMilestones(rSchedule, scheduleDirection, schBasedOnDate, hideMsg, bDontFailOnValidateMilestones, rTimeChangeMilestone, bCommitting) {
    var bSuccess = bDontFailOnValidateMilestones ? true : false;
    var bAutoScheduleMode = scopes.avScheduling.bDoingAutoSchedule;
    var bAddingToSchedule = globals.gAddingToSchedule; // globals.gAddingToSchedule is cleared in avCalcs_schedule_buildTimeline

    if (validateMilestones(rSchedule, hideMsg)) {
        bSuccess = false;
        globals.sScheduleDirection = scheduleDirection;

        if (scheduleDirection == 'F') {
            // This method creates the actual timeline by setting the start and due date for each ms
            bSuccess = globals.avCalcs_schedule_buildTimeline(rSchedule, schBasedOnDate, bAutoScheduleMode, null, rTimeChangeMilestone, bCommitting);
        }
        else {
            // This method creates the actual timeline by setting the start and due date for each ms - for backwars scheduling
            bSuccess = globals.avCalcs_schedule_buildTimeline_backwards(rSchedule, schBasedOnDate, bAutoScheduleMode, rTimeChangeMilestone, bCommitting);
        }
    }

    // sl-24452 - log whether this job was scheduled using auto-schedule. there is already a sch_auto_scheduled flag but is set in addOrderToPublishSchedule() regardless of whether we added
    // it successfully. i cant change that logic because the flag is used in getSectionsWithAutoExportJDFs(), which i dont want to mess up. so i create a new sch_auto_schedule_success flag.
    // have check both bAddingToSchedule and bAutoScheduleMode, bAddingToSchedule is only set from RTS.
	if (bAddingToSchedule || bAutoScheduleMode) {
		if (bAutoScheduleMode && bSuccess) {
			rSchedule.sch_auto_schedule_success = 1;
		}
		// set to zero otherwise. they may have auto added to schedule previously then removed and re-added thru RTS. in which case we want to clear the flag
		else {
			rSchedule.sch_auto_schedule_success = 0; 
		}
		
		databaseManager.saveData(rSchedule);
	}

    return bSuccess;
}

/**
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 * @param {Boolean} [hideMsg]
 * 
 * @return
 * @properties={typeid:24,uuid:"4F4F4CA8-7FFC-428C-A380-AAD2FCEABD03"}
 */
function validateMilestones(rSchedule, hideMsg){
    var bScheduleEmps = scopes.avUtils.getSysPrefNum('ScheduleEmployees');
	/***@type {JSFoundSet<db:/avanti/sch_milestone>} */
	var fsMilestones = rSchedule.sch_schedule_to_sch_milestone;
	
	fsMilestones.sort("sequence_nr asc");	
	var rMilestone;
	var rOpCat;
	var i
	var iNumRecs = fsMilestones.getSize()
	var sNoOp =''
    var sNoOpCat =''
	var sNoEq =''
	var sNoShift ='';
	var sNoEqOrEmp = '';
	var bSetMilestoneLagsFailed = false;
	
	for(i = 1; i <= iNumRecs; i++){
		rMilestone = fsMilestones.getRecord(i);
		var bMilestoneValid = true;
		
        if (utils.hasRecords(rMilestone.sch_milestone_to_sys_operation_category)) {
            rOpCat = rMilestone.sch_milestone_to_sys_operation_category.getRecord(1);
        }
        else {
            rOpCat = null;
        }

		// sl-4911
        if (!rMilestone.ms_oper_name) {
            if (sNoOp) {
                sNoOp += ','
            }
            sNoOp += rMilestone.sequence_nr + '\n'
            bMilestoneValid = false;
        }
        else if (!rOpCat) {
            if (sNoOpCat) {
                sNoOpCat += ','
            }
            sNoOpCat += rMilestone.sequence_nr + '\n'
            bMilestoneValid = false;
        }
		// only validate ms from sched depts and ms that have a time needed
        else if (rMilestone.ms_time_budget > 0 && rMilestone.sch_milestone_to_sys_department.dept_schedule_flag) {
            var rEquip = null;
            var bValidEquipShifts = false;
            
            if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)){
                rEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
                bValidEquipShifts = scopes.avScheduling.areThereAnyValidShifts('equipment', rEquip.equip_id);
            }

	        if(bScheduleEmps && rOpCat.opcat_schedule_employees){
	            // no equip nec - but if there is an equip validate shifts
	            if(rEquip && !bValidEquipShifts){
                    if(sNoShift){
                        sNoShift += ','
                    }
                    sNoShift += rMilestone.ms_oper_name + ' (' + rMilestone.sequence_nr + ')\n';
                    bMilestoneValid = false;
	            }
	        }
	        else{
	            if (!rEquip){
	                if(bScheduleEmps){
	                    if(sNoEqOrEmp){
	                        sNoEqOrEmp += ',';
	                    }
	                    sNoEqOrEmp += rMilestone.ms_oper_name + ' (' + rMilestone.sequence_nr + ')\n';
	                    bMilestoneValid = false;
	                }
	                else{
	                    if(sNoEq){
	                        sNoEq += ',';
	                    }
	                    sNoEq += rMilestone.ms_oper_name + ' (' + rMilestone.sequence_nr + ')\n';
	                    bMilestoneValid = false;
	                }
	            }
	            else if(!bValidEquipShifts){
	                if(sNoShift){
	                    sNoShift += ','
	                }
	                sNoShift += rMilestone.ms_oper_name + ' (' + rMilestone.sequence_nr + ')\n';
                    bMilestoneValid = false;
	            }
	        }
		}
        
		if (bMilestoneValid) {
			if (!scopes.avScheduling.setMilestoneLags(rMilestone, hideMsg)) {
				bSetMilestoneLagsFailed = true;
			}
		}
	}
	
	if(!hideMsg){
	    var sMsg = null;
	    
		if(sNoOp){
		    sMsg = i18n.getI18NMessage('avanti.dialog.msNoOp') + '\n\n' + sNoOp;
		}
		else if (sNoOpCat) {
            sMsg = i18n.getI18NMessage('avanti.dialog.msNoOpCat') + '\n\n' + sNoOpCat;
        }
		else if(sNoEq){
            sMsg = i18n.getI18NMessage('avanti.dialog.msNoEquip') + '\n\n' + sNoEq;
		}
		else if(sNoEqOrEmp){
            sMsg = i18n.getI18NMessage('avanti.dialog.msNoEquipOrEmp') + '\n\n' + sNoEqOrEmp;
        }
        else if(sNoShift){
            sMsg = i18n.getI18NMessage('avanti.dialog.msNoShift') + '\n\n' + sNoShift;
		}
		
		if(sMsg){
	        scopes.avText.showError(sMsg, true, null, scopes.avText.getDlgMsg('ProblemSchedMS'), null, 500, 300);
		}
	}
	
    if (sNoOp || sNoOpCat || sNoEq || sNoShift || sNoEqOrEmp || bSetMilestoneLagsFailed) {
        rSchedule.sch_validate_milestones_failed = 1;
        return false;
    }
    else {
        rSchedule.sch_validate_milestones_failed = null;
        return true;
    }
}

/**
 * This will add a job to the schedule.
 * 
 * Warning: This function is also called from 
 * avanti_base/avWebToPrint.js:createJobFromTables()
 * 
 * Note: This was refactored out of onAction_btnAddToSchedule() above so it 
 * could be called from avWebToPrint.js to automatically schedule a job 
 * created via an XML import.
 * 
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 * @param {String} sScheduleDirection - 'F' for "Forwards", 'B' for "Backwards"
 * @param {Boolean} bHideMsg - true to hide UI messages
 * @param {UUID} sDBLogParentUUID - (optional) use for XML import logging
 * 
 * @properties={typeid:24,uuid:"DCD54D14-8305-49BD-B983-4B4660B84616"}
 * 
 * @return {Boolean} - true upon success, false upon error
 */
function handleAddToScheduleEvent(rSchedule, sScheduleDirection, bHideMsg, sDBLogParentUUID) {
    scopes.avScheduling.bCommittingSchedule = true;
	var bSuccess = false;
	var sJobNumber = "";
	
	if (utils.hasRecords(rSchedule.sch_schedule_to_prod_job)) {
		sJobNumber = rSchedule.sch_schedule_to_prod_job.job_number;
	}	
	
	if ('F'==sScheduleDirection) {
		bSuccess = scheduleMilestones(rSchedule, sScheduleDirection, application.getTimeStamp(), bHideMsg);
	}
	else if ('B'==sScheduleDirection) {
		if (globals.jobCannotPossiblyCompleteByDueDate(rSchedule, !bHideMsg, 'B')) {
			if (sDBLogParentUUID) {
				scopes.avScheduling.logAutoScheduleStatus(i18n.getI18NMessage('avanti.dialog.scheduledPastDueDate'), 'Warning', sJobNumber, sDBLogParentUUID, null, null, null);
			}
		}
		else {
			bSuccess = scheduleMilestones(rSchedule, sScheduleDirection, null, bHideMsg);
		}
	}
	else {

		application.output('sch_schedule_tbl_by_job.js:handleAddToScheduleEvent(): ERROR: received invalid value for sScheduleDirection: '+sScheduleDirection, LOGGINGLEVEL.ERROR);
		
		if (sDBLogParentUUID) {
			scopes.avScheduling.logAutoScheduleStatus(i18n.getI18NMessage('avanti.dialog.notForwardOrBackward'), 'Error', sJobNumber, sDBLogParentUUID, "Expected 'F'(Forwards) or 'B'(Backwards) for scheduling direction but received: "+sScheduleDirection, null, null);
		}
		bSuccess = false;
	}
	
	if (bSuccess) {
		commitSchedule(rSchedule,bHideMsg);
	}
	
    scopes.avScheduling.bCommittingSchedule = false;
	return bSuccess;
}

/**
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 * @param {Boolean} [bBypassMsg]
 *
 * @properties={typeid:24,uuid:"16BDDA25-180E-4444-BD0C-C0B82AFAD338"}
 */
function commitSchedule(rSchedule, bBypassMsg)
{
	// This method writes the schedule to the assigned resource
	if(_super.updateAllResourceSchedules(rSchedule)){
		if(!bBypassMsg){
			if(rSchedule.jobstat_id == "Scheduled"){
				globals.DIALOGS.showInfoDialog('',  i18n.getI18NMessage('avanti.dialog.ScheduleUpdated') , i18n.getI18NMessage('avanti.dialog.ok'))
			}
			else if(rSchedule.sch_is_reservation){
				scopes.avText.showInfo('reservationAddedToSchedule');
			}
			else{
				globals.DIALOGS.showInfoDialog('',  i18n.getI18NMessage('avanti.dialog.JobScheduled') , i18n.getI18NMessage('avanti.dialog.ok'))
			}
		}
		
		if (rSchedule.sch_schedule_to_prod_job.jobstat_id != scopes.avUtils.JOB_STATUS.Staged) {
			//Saving the existing status so that the status can be reverted if the job is removed from schedule
			rSchedule.sch_schedule_to_prod_job.jobstat_id_previous = rSchedule.sch_schedule_to_prod_job.jobstat_id;
			// Update the job to scheduled
			rSchedule.sch_schedule_to_prod_job.jobstat_id = scopes.avUtils.JOB_STATUS.Scheduled;
			rSchedule.jobstat_id = scopes.avUtils.JOB_STATUS.Scheduled;
		}
		
		databaseManager.saveData(rSchedule.sch_schedule_to_prod_job)
		databaseManager.saveData(rSchedule)
		//SL-12271 we set the  status of the sections  corresponding to the  job to Scheduled
		// in order to not be displayed in the schedule by section form
		if(utils.hasRecords(rSchedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section)){
			for(var i =1 ; i <= rSchedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section.getSize();i++)
			{  /*** @type {JSRecord<db:/avanti/sa_order_revision_detail_section>} */
				 var rSection = rSchedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section.getRecord(i);
				 rSection.jobstat_id = rSchedule.jobstat_id;
				 databaseManager.saveData(rSection);
			}
		}

	}
}

/**
 * @public 
 * 
 * @param {JSFoundset<db:/avanti/sch_schedule>} fsRTS
 *
 * @properties={typeid:24,uuid:"0B414F31-3361-4C9C-8F61-E88236FD49E7"}
 */
function filterRTSFoundset(fsRTS){
	// called from sched by job tab
	
	fsRTS.removeFoundSetFilterParam('scheduled_status_filter');
	fsRTS.removeFoundSetFilterParam("job_late");
	fsRTS.removeFoundSetFilterParam('press_ready_status_id');
	
	fsRTS.loadAllRecords();
	if (forms.sch_schedule_dtl._formBeingCalledFrom 
			&& forms[forms.sch_schedule_dtl._formBeingCalledFrom]._selectedSchID
			&& fsRTS.getSelectedRecord()
			&& fsRTS.getSelectedRecord().sch_id != forms[forms.sch_schedule_dtl._formBeingCalledFrom]._selectedSchID) {
		forms.sch_schedule_dtl.foundset.loadRecords(application.getUUID(forms[forms.sch_schedule_dtl._formBeingCalledFrom]._selectedSchID.toString()));
		forms.sch_schedule_tbl.foundset.loadRecords(application.getUUID(forms[forms.sch_schedule_dtl._formBeingCalledFrom]._selectedSchID.toString()));
	}
	databaseManager.refreshRecordFromDatabase(fsRTS,-1);
	
	// set this to true to show all jobs (no filtering by status, etc) in developer mode
	var bDebugShowAllJobs = false;
	
	if (bDebugShowAllJobs && application.isInDeveloper()) {
		return;
	}
	
	//SL-8374, Jobs released to schedule should not be displayed
	if (fsRTS.getFoundSetFilterParams('rts_status_filter').length == 0) {
		fsRTS.addFoundSetFilterParam('jobstat_id', 'sql:not in', _aInvalidJobStatuses, 'rts_status_filter');
	}
	
	//SL-8374 Commenting out the filter because the user could schedule the milestones,
	//save and not release to the schedule. In this scenario, the job is neither displayed
	//in Ready to Schedule not Scheduled Jobs section
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_scheduled').length == 0){
        fsRTS.addFoundSetFilterParam('sch_id', 'sql:not in', "SELECT mile.sch_id \
                                                          FROM sch_milestone mile \
                                                          WHERE EXISTS (SELECT NULL \
                                                                        FROM sch_equip_schedule equip \
                                                                        WHERE equip.ms_id = mile.ms_id) \
                                                          GROUP BY mile.sch_id", 'ms_filter_scheduled');
    }
	
    // SL-14103 : Added filter for checking sch_empl_schedule
    if(fsRTS.getFoundSetFilterParams('ms_filter_emp_scheduled').length == 0){
        fsRTS.addFoundSetFilterParam('sch_id', 'sql:not in', "SELECT mile.sch_id \
                                                          FROM sch_milestone mile \
                                                          WHERE EXISTS (SELECT NULL \
                                                                        FROM sch_empl_schedule empl \
                                                                        WHERE empl.ms_id = mile.ms_id) \
                                                          GROUP BY mile.sch_id", 'ms_filter_emp_scheduled');
    }
	
	//SL-8374, Displaying jobs that have at least one milestone that is not completed
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_completed').length == 0){
		fsRTS.addFoundSetFilterParam('sch_id', 'sql:in', "SELECT sch_id \
                                                        FROM sch_milestone \
                                                        WHERE ms_date_completed IS NULL \
                                                        GROUP BY sch_id \
                                                        HAVING COUNT(ms_id) > 0", 'ms_filter_completed');
	}
	
	//SL-8374, Setting the department filter so that jobs belonging 
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_dept').length == 0){
		fsRTS.addFoundSetFilterParam('sch_id', 'sql:in', "SELECT sch_id \
		                                                FROM sch_milestone mile \
		                                                WHERE EXISTS (SELECT dept_id \
		                                                                FROM sys_department dept \
		                                                                WHERE dept.dept_schedule_flag = 1 \
		                                                                    AND dept.dept_id = mile.dept_id)", 'ms_filter_dept');
	}
		
	fsRTS.loadAllRecords();
}

/**
 * @param {JSFoundset<db:/avanti/sch_schedule>} fsScheduled
 *
 * @properties={typeid:24,uuid:"F8BBD5E1-26F9-4233-A69A-5DBC310B33FA"}
 */
function filterScheduledFoundset(fsScheduled){
	// called from scheduled jobs tab
	
	fsScheduled.removeFoundSetFilterParam('rts_status_filter');
	fsScheduled.removeFoundSetFilterParam('ms_filter_scheduled');
	fsScheduled.removeFoundSetFilterParam('ms_filter_emp_scheduled');
	fsScheduled.removeFoundSetFilterParam('ms_filter_dept');
	fsScheduled.removeFoundSetFilterParam('press_ready_status_id');

	if(fsScheduled.getFoundSetFilterParams('scheduled_status_filter').length == 0){
		fsScheduled.addFoundSetFilterParam('jobstat_id', '=', 'Scheduled', 'scheduled_status_filter');
	}
	
	// Displaying jobs that have at least one milestone that is not completed
    if (fsScheduled.getFoundSetFilterParams('ms_filter_completed').length == 0) {
        fsScheduled.addFoundSetFilterParam('sch_id', 'sql:in', 
            "SELECT sch_id FROM sch_milestone WHERE ms_date_completed IS NULL",
            'ms_filter_completed');
    }

	
	fsScheduled.loadRecords();
	application.output(fsScheduled.getSize());
	
}

/**
 * @param {JSFoundset<db:/avanti/sa_order_revision_detail_section>} fsSect
 * @param {Number} [nGrpNum]
 *
 * @properties={typeid:24,uuid:"BE85BDB9-47B5-4410-ABC6-C7016E4751C1"}
 */
function filterSectionFoundset(fsSect, nGrpNum){
	// called from sched by section and ganging tabs
	
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var	oSQL = {},
		/***@type {JSDataSet} */
		dsData = null;
		
	var sInvalidJobStatuses = scopes.avText.arrayToString(_aInvalidJobStatuses, ",", "'");
	
	// GD - Apr 19, 2017: SL-9685 - Perf Tune: Try loading using SQL/dataset
	oSQL.sql = "";
	oSQL.sql += "SELECT s.ordrevds_id \
	 			FROM sa_order_revision_detail_section s \
				INNER JOIN sa_order_revision_detail d ON s.ordrevd_id = d.ordrevd_id \
				WHERE s.org_id = ?" ;
	oSQL.args = [globals.org_id.toString()];
	oSQL.sql += " AND s.jobstat_id NOT IN (" + sInvalidJobStatuses + ")";
	oSQL.sql += " AND s.ordrevds_line_type NOT IN ('N', 'U')";
	oSQL.sql += " AND (s.ordrevds_is_deleted IS NULL OR s.ordrevds_is_deleted = 0)";
	oSQL.sql += " AND d.job_id IS NOT NULL";
	oSQL.sql += " AND s.ordrevds_id IN (SELECT ordrevds_id FROM sch_milestone WHERE ISNULL(ms_date_scheduled, '') = '')";
	
	if(nGrpNum){
		
		oSQL.sql += " AND s.ordrevds_schedule_group = ?";
		oSQL.args.push(nGrpNum);
	}
	
	// GD - Apr 20, 2017: ORDER BY does not work; servoy ignores it
//	oSQL.sql += " ORDER BY CASE WHEN s.ordrevds_schedule_group IS NULL THEN 1 ELSE 0 END ASC, s.ordrevds_schedule_group ASC";
	
	// Save the foundset criteria for other functions to use
	_fsLoadSql = oSQL.sql;
	_fsLoadSqlArgs = oSQL.args;
	
	dsData = globals.avUtilities_sqlDataset(oSQL);
	
	if (dsData) {
		fsSect.loadRecords(dsData);
		fsSect.sort(forms.sch_schedule_tbl_ganging.sortGroups);
		
		_fsLoadFoundset = fsSect.duplicateFoundSet();
		_fsLoadDataset = dsData;
		_fsLoadDatasetCount = dsData.getMaxRowIndex();
	} 
	else {
		
		fsSect.clear();
		
	}
	
	application.output("Total number of Sections available for ganging: " + fsSect.getSize());
}

/**
 * @param {JSFoundset<db:/avanti/sch_schedule>} fsScheduled
 * @param {Array} arr
 *
 * @properties={typeid:24,uuid:"AA8076FB-0644-48CF-A098-926912BDF4F6"}
 */
function filterScheduledFoundsetWithPressReadyStatus(fsScheduled,arr){
	// called from scheduled jobs tab
	
	fsScheduled.removeFoundSetFilterParam('rts_status_filter');
	fsScheduled.removeFoundSetFilterParam('ms_filter_scheduled');
	fsScheduled.removeFoundSetFilterParam('ms_filter_emp_scheduled');	
	fsScheduled.removeFoundSetFilterParam('ms_filter_dept');
	fsScheduled.removeFoundSetFilterParam('press_ready_status_id');
	
	fsScheduled.addFoundSetFilterParam("sch_id","sql:in",arr,'press_ready_status_id');
	
	if(fsScheduled.getFoundSetFilterParams('scheduled_status_filter').length == 0){
		fsScheduled.addFoundSetFilterParam('jobstat_id', '=', 'Scheduled', 'scheduled_status_filter')
	}
	
    // Displaying jobs that have at least one milestone that is not completed
    if (fsScheduled.getFoundSetFilterParams('ms_filter_completed').length == 0) {
        fsScheduled.addFoundSetFilterParam('sch_id', 'sql:in', 
            "SELECT sch_id FROM sch_milestone WHERE ms_date_completed IS NULL",
            'ms_filter_completed');
    }

	fsScheduled.loadRecords();
	application.output(fsScheduled.getSize());
}

/**
 * @return
 * @properties={typeid:24,uuid:"52A25BCC-1C29-4733-A6F2-BFECB1682DDD"}
 */
function getPressReadyJobs() {
	var oSQL = { };
	oSQL.server = globals.avBase_dbase_avanti;
	oSQL.args = [];
	oSQL.table = 'sch_schedule';
	oSQL.sql = "SELECT schedule.sch_id\
                FROM   sch_schedule schedule\
                INNER JOIN  prod_job job on schedule.job_id = job.job_id\
                INNER JOIN  sa_order_revision_detail revds on revds.job_id = job.job_id\
                INNER JOIN sa_order_revision_detail_section section on section.ordrevd_id = revds.ordrevd_id\
                INNER JOIN  sch_milestone milestone on milestone.ordrevds_id = section.ordrevds_id\
                INNER JOIN sa_order_revds_task  order_task on order_task.ordrevdstask_id  = milestone.ordrevdstask_id\
                INNER JOIN  sa_task task  on task.task_id  = order_task.task_id\
                INNER JOIN app_task_type app_task on app_task.tasktype_id = task.tasktype_id\
               WHERE  milestone.ms_flg_ready = ? and ( app_task.tasktype_is_press = ? or app_task.tasktype_id in (1,2,3,7,8,9,10,15,99))  and schedule.org_id = ? ";
	oSQL.args.push(1,1,scopes.globals.org_id);
	 var resultDisplayArr = new Array();
	var resultDs = globals.avUtilities_sqlDataset(oSQL);
	for (var i = 1; i <= resultDs.getMaxRowIndex() ; i++ ) {
        
        resultDisplayArr.push(resultDs.getValue(i,1));
     }
      return resultDisplayArr;
}

/**
 * 
 * @param fsRTS
 * @param {Array} arr
 * @properties={typeid:24,uuid:"657084EC-2B18-46C7-AE76-9FF46C7D9DFB"}
 */
function filterRTSFoundsetWithPressReady(fsRTS,arr){
	// called from sched by job tab
	
	fsRTS.removeFoundSetFilterParam('scheduled_status_filter');
	fsRTS.removeFoundSetFilterParam("job_late");
	fsRTS.removeFoundSetFilterParam('press_ready_status_id');
	
	fsRTS.addFoundSetFilterParam("sch_id","sql:in",arr,'press_ready_status_id');
	
	fsRTS.loadAllRecords();
	databaseManager.refreshRecordFromDatabase(fsRTS,-1);
	
	//SL-8374, Jobs released to schedule should not be displayed
	if(fsRTS.getFoundSetFilterParams('rts_status_filter').length == 0){
		fsRTS.addFoundSetFilterParam('jobstat_id', 'sql:not in', _aInvalidJobStatuses, 'rts_status_filter');
	}
	
	//SL-8374 Commenting out the filter because the user could schedule the milestones,
	//save and not release to the schedule. In this scenario, the job is neither displayed
	//in Ready to Schedule not Scheduled Jobs section
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_scheduled').length == 0){
        fsRTS.addFoundSetFilterParam('sch_id', 'sql:not in', "SELECT mile.sch_id \
                                                          FROM sch_milestone mile \
                                                          WHERE EXISTS (SELECT NULL \
                                                                        FROM sch_equip_schedule equip \
                                                                        WHERE equip.ms_id = mile.ms_id) \
                                                          GROUP BY mile.sch_id", 'ms_filter_scheduled');
    }
	
    // SL-14103 : Added filter for checking sch_empl_schedule
    if(fsRTS.getFoundSetFilterParams('ms_filter_emp_scheduled').length == 0){
        fsRTS.addFoundSetFilterParam('sch_id', 'sql:not in', "SELECT mile.sch_id \
                                                          FROM sch_milestone mile \
                                                          WHERE EXISTS (SELECT NULL \
                                                                        FROM sch_empl_schedule empl \
                                                                        WHERE empl.ms_id = mile.ms_id) \
                                                          GROUP BY mile.sch_id", 'ms_filter_emp_scheduled');
    }
	
	//SL-8374, Displaying jobs that have at least one milestone that is not completed
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_completed').length == 0){
		fsRTS.addFoundSetFilterParam('sch_id', 'sql:in', "SELECT sch_id \
                                                        FROM sch_milestone \
                                                        WHERE ms_date_completed IS NULL \
                                                        GROUP BY sch_id \
                                                        HAVING COUNT(ms_id) > 0", 'ms_filter_completed');
	}
	
	//SL-8374, Setting the department filter so that jobs belonging 
	// SL-14103 : Cleaned up SELECT statement
	if(fsRTS.getFoundSetFilterParams('ms_filter_dept').length == 0){
		fsRTS.addFoundSetFilterParam('sch_id', 'sql:in', "SELECT sch_id \
		                                                FROM sch_milestone mile \
		                                                WHERE EXISTS (SELECT dept_id \
		                                                                FROM sys_department dept \
		                                                                WHERE dept.dept_schedule_flag = 1 \
		                                                                    AND dept.dept_id = mile.dept_id)", 'ms_filter_dept');
	}
	
	fsRTS.loadAllRecords();
	application.output(fsRTS.getSize());
}
