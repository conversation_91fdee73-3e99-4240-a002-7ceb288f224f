columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:37,
length:36,
name:"bifsf_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"abif_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
name:"bif_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:-9,
length:50,
name:"bifsf_col_header"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"bifsf_length"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"bifsf_position"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,5,0]]",
creationOrderIndex:-1,
dataType:12,
length:5,
name:"bifsf_summary_type"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"bifsf_use_udv"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,250,0]]",
creationOrderIndex:-1,
dataType:12,
length:250,
name:"bifsf_user_defined_value"
},
{
allowNull:true,
autoEnterType:5,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
lookupValue:"scopes.globals.org_id",
name:"org_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"sequence_nr"
}
],
name:"sys_batch_inv_file_sum_field",
tableType:0