customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
extendsID:"7E878439-CEC8-4624-97FB-9DC05A389D95",
items:[
{
cssPosition:"674,-1,-1,520,80,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"674",
width:"80"
},
enabled:true,
onActionMethodID:"5A985F4C-CE80-45C1-9941-1C586F5B5451",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.jdfType_TestFTP",
visible:true
},
name:"test_inbound",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"0C94D0EA-A582-4689-8565-DA16B92C6014"
},
{
cssPosition:"62,-1,-1,624,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"624",
right:"-1",
top:"62",
width:"24"
},
enabled:true,
onActionMethodID:"E1EE042F-011C-4357-B115-AA0E00861C99",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
visible:true
},
name:"btnCust",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0D9959C8-16E8-4C9E-950D-95402D0F0258"
},
{
cssPosition:"647,-1,-1,455,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"455",
right:"-1",
top:"647",
width:"60"
},
dataProviderID:"bcc_inbound_port",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftp_port",
visible:true
},
name:"bcc_inbound_port",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"10F30A2A-471E-4077-8F46-F5CB8186AB02"
},
{
cssPosition:"701,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"701",
width:"168"
},
enabled:true,
labelFor:"bcc_inbound_pass",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_InboundPassword",
visible:true
},
name:"component_845E9DEB",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"11B4BA9A-39E1-4D0C-9262-B2E061CFE206"
},
{
cssPosition:"728,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"728",
width:"192"
},
dataProviderID:"bcc_inbound_ftp_folder",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_inbound_ftp_folder",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"11F98B5C-FD28-41CB-BF24-855F67D84320"
},
{
cssPosition:"674,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"674",
width:"192"
},
dataProviderID:"bcc_inbound_username",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_inbound_username",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"14F4E2F5-EFC6-4540-89E0-8E2A144C1C73"
},
{
cssPosition:"87,-1,-1,624,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"624",
right:"-1",
top:"87",
width:"24"
},
enabled:true,
onActionMethodID:"5E998E59-A5D9-4A1B-B0FB-045CF54B410C",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
visible:true
},
name:"btnWorkType",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1A4E7FCE-067A-4C6D-9ED7-B6A05831CBFE"
},
{
cssPosition:"113,-1,-1,150,472,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"113",
width:"472"
},
dataProviderID:"clc_work_templates",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldWorkTemplates",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"25FD7284-5141-4793-A420-6F7A78B55816"
},
{
height:787,
partType:5,
typeid:19,
uuid:"286542F5-E7F6-41A2-B0F7-3D7FB65E6B25"
},
{
cssPosition:"217,-1,-1,535,89,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"535",
right:"-1",
top:"217",
width:"89"
},
enabled:true,
onActionMethodID:"0C16F5FB-3D59-408F-B6DB-8FC011C62EDB",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.download",
visible:true
},
name:"btnDownload",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"31E0AB82-86B7-45D3-A423-5F5D1B7011D9"
},
{
cssPosition:"593,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"593",
width:"168"
},
enabled:true,
labelFor:"bcc_outbound_host",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_OutboundFTPFolder",
visible:true
},
name:"component_A7072B9C",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3571A4ED-5827-498F-8FB6-007235875C00"
},
{
cssPosition:"701,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"701",
width:"192"
},
dataProviderID:"bcc_inbound_password",
editable:true,
enabled:true,
inputType:"password",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_inbound_pass",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3BF70DB5-B6DC-4172-B3C3-0F93DBCF8019"
},
{
cssPosition:"647,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"647",
width:"140"
},
enabled:true,
labelFor:"bcc_inbound_host",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_InboundHost",
visible:true
},
name:"component_AF5613AE",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3D75FB62-BE70-4592-8E3F-030E7909AC15"
},
{
cssPosition:"593,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"593",
width:"192"
},
dataProviderID:"bcc_outbound_ftp_folder",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_outbound_ftp_folder",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"437FCD32-0013-40BB-9FE0-D9F673DDFD0B"
},
{
cssPosition:"5,0,-1,0,930,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"5",
width:"930"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bccWorkflowsDetailView",
visible:true
},
name:"lbl_group_bcc_workflow",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"43D5C1A5-1A81-4540-8743-7382F9075C63"
},
{
cssPosition:"360,-1,-1,640,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"640",
right:"-1",
top:"360",
width:"24"
},
enabled:true,
onActionMethodID:"6492B9C8-5C60-4AA9-9165-936A20C3D266",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_add%%",
toolTipText:"i18n:avanti.lbl.addNewChargeBackCode",
visible:true
},
name:"btnAddUDRpt",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"47B9A987-4561-4CFB-80E3-CECC255B9600"
},
{
cssPosition:"298,-1,-1,172,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"172",
right:"-1",
top:"298",
width:"140"
},
enabled:true,
labelFor:"bcc_presort",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.preSort",
visible:true
},
name:"lbl_bcc_presort",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"49284C38-942A-436B-A592-FFA2FE318071"
},
{
cssPosition:"355,-1,-1,375,250,147",
json:{
containedForm:"0691CD70-A734-4621-AA1F-AB9D34B6B6D4",
cssPosition:{
bottom:"-1",
height:"147",
left:"375",
right:"-1",
top:"355",
width:"250"
},
relationName:"bcc_workflow_to_bcc_workflow_report$distribution",
visible:true
},
name:"tabless",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"4AD122DA-2FE5-40FF-8448-7D1EAA82B911"
},
{
cssPosition:"298,-1,-1,317,262,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"317",
right:"-1",
top:"298",
width:"262"
},
dataProviderID:"bcc_presort",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_presort",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4B952BEC-A7BF-4A89-A105-707D31DCFD9A"
},
{
cssPosition:"218,-1,-1,626,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"626",
right:"-1",
top:"218",
width:"25"
},
enabled:true,
onActionMethodID:"C28A3632-508F-475A-8860-A454FB31CD09",
styleClass:"listview_icon label_bts",
tabSeq:0,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnClearMJB",
styleClass:"listview_icon label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"513F2345-2C49-447F-84C3-D945FADA7D54"
},
{
cssPosition:"355,-1,-1,150,184,147",
json:{
cssPosition:{
bottom:"-1",
height:"147",
left:"150",
right:"-1",
top:"355",
width:"184"
},
dataProviderID:"bcc_reports",
enabled:true,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"8FCBFAE1-17BF-4F13-B9DA-DE18D14F8673",
visible:true
},
name:"fldBCCReports",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"53584F50-17C3-4696-BEF8-1407979C0B0C"
},
{
cssPosition:"272,-1,-1,317,262,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"317",
right:"-1",
top:"272",
width:"262"
},
dataProviderID:"bcc_label_template",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_label_template",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"57B10DF3-4F01-4A99-9124-691F560C528D"
},
{
cssPosition:"62,-1,-1,150,472,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"62",
width:"472"
},
dataProviderID:"clc_customers",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldCusts",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5D0CC978-1ED5-4A9C-BCF9-76F5C3A2CF14"
},
{
cssPosition:"360,-1,-1,345,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"345",
right:"-1",
top:"360",
width:"24"
},
enabled:true,
onActionMethodID:"8CF087B7-2CA7-4549-B514-D2109B1990D0",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_add%%",
toolTipText:"i18n:avanti.lbl.addNewChargeBackCode",
visible:true
},
name:"btnAddDistRpt",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5DDF9131-845B-4189-AED1-147D76ADF3FA"
},
{
cssPosition:"165,-1,-1,150,498,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"165",
width:"498"
},
dataProviderID:"bcc_output_folder",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.bccOutputFolder_tooltip",
visible:true
},
name:"fldBCCOutputFolder",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5E590122-A395-44A0-9D7D-0C80CD6C6103"
},
{
cssPosition:"165,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"165",
width:"140"
},
enabled:true,
labelFor:"fldBCCOutputFolder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bccOutputFolder",
toolTipText:"i18n:avanti.lbl.bccOutputFolder_tooltip",
visible:true
},
name:"component_7CECACDA",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5F903FD7-72C4-4C79-9A82-90BF03405F52"
},
{
cssPosition:"512,-1,-1,444,71,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"444",
right:"-1",
top:"512",
width:"71"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:":",
visible:true
},
name:"component_C43B7DBC",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6008E511-1DFB-4BF0-A766-41753716770B"
},
{
cssPosition:"674,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"674",
width:"168"
},
enabled:true,
labelFor:"bcc_inbound_username",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_InboundUsername",
visible:true
},
name:"component_D1703865",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"642758DF-32F5-4AEE-8A05-19A5C55D0F2B"
},
{
cssPosition:"247,-1,-1,172,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"172",
right:"-1",
top:"247",
width:"140"
},
enabled:true,
labelFor:"bcc_dedup_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.DeduplicationType",
visible:true
},
name:"lbl_bcc_dedup_type",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"68B41D81-1AEF-4BF8-9844-A7A49C9F0D2D"
},
{
cssPosition:"192,-1,-1,150,498,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"192",
width:"498"
},
dataProviderID:"bcc_mailing_list_path",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.bccMailingListPath_tooltip",
visible:true
},
name:"fldBCCMailingListPath",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6B6C5EDF-365C-4BE9-B31B-DE407CF84F7B"
},
{
cssPosition:"62,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"62",
width:"140"
},
enabled:true,
labelFor:"fldCusts",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customers",
visible:true
},
name:"lblCusts",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6C4CD448-87FC-4778-B737-2A8D0F56F5E3"
},
{
cssPosition:"539,-1,-1,520,80,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"539",
width:"80"
},
enabled:true,
onActionMethodID:"CDC27A9A-090F-4E96-90D9-B20E2148CA0A",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.jdfType_TestFTP",
visible:true
},
name:"test_outbound",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"6CF3EC80-E2E4-403D-B085-BCDF48761B67"
},
{
cssPosition:"620,282,-1,520,128,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"282",
top:"620",
width:"128"
},
dataProviderID:"bcc_outbound_ftp_hostkey",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftpHostPublicKey",
visible:true
},
name:"bcc_outbound_hostkey",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"73848C91-4A95-4C56-A9C6-94B7E6164688"
},
{
cssPosition:"113,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"113",
width:"140"
},
enabled:true,
labelFor:"fldWorkTemplates",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.workTemplates",
visible:true
},
name:"lblWorkTemplates",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"792CC2B5-CEDD-4067-A2CC-D5E9C0FAB91E"
},
{
cssPosition:"539,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"539",
width:"192"
},
dataProviderID:"bcc_outbound_username",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_outbound_username",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"79A36415-9623-43BD-A078-9F2A40278759"
},
{
cssPosition:"566,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"566",
width:"168"
},
enabled:true,
labelFor:"bcc_inbound_pass",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_OutboundPassword",
visible:true
},
name:"component_69B7FDBB",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7D8F7273-2F66-4180-8909-F83E3A62DD36"
},
{
cssPosition:"512,-1,-1,520,128,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"512",
width:"128"
},
dataProviderID:"bcc_outbound_enable",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.enabled",
visible:true
},
name:"bcc_outbound_enable",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"7F66F12D-D324-4283-A273-7F87BED92322"
},
{
cssPosition:"539,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"539",
width:"168"
},
enabled:true,
labelFor:"bcc_outbound_username",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_OutboundUsername",
visible:true
},
name:"component_C34D54E8",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"836F65D3-CF25-4563-A1F3-2ABC043AAC00"
},
{
cssPosition:"192,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"192",
width:"140"
},
enabled:true,
labelFor:"fldBCCOutputFolder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bccMailingListPath",
toolTipText:"i18n:avanti.lbl.bccMailingListPath_tooltip",
visible:true
},
name:"component_4C422FB6",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"88F51265-5355-42BB-AD00-916780E734AA"
},
{
cssPosition:"247,-1,-1,317,262,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"317",
right:"-1",
top:"247",
width:"262"
},
dataProviderID:"bcc_dedup_type",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_dedup_type",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8B21CD6A-7E74-46D4-90E3-A481E479091C"
},
{
cssPosition:"243,-1,-1,5,147,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"243",
width:"147"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.BCCProcessingOptions",
visible:true
},
name:"lblProcOptions",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8FE57426-F89E-4B1A-AB17-A956FB66F751"
},
{
cssPosition:"217,-1,-1,461,73,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"461",
right:"-1",
top:"217",
width:"73"
},
enabled:true,
onActionMethodID:"A7CEEFBC-C7E4-4768-A2C4-0AF5086ED903",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:servoy.filechooser.button.upload",
visible:true
},
name:"btnUpload",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"9C91FE3E-**************-5AC9641FB865"
},
{
cssPosition:"647,-1,-1,520,128,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"-1",
top:"647",
width:"128"
},
dataProviderID:"bcc_inbound_enable",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.enabled",
visible:true
},
name:"bcc_inbound_enable",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"9E917D24-8965-499B-B37C-BFD50EC4AA57"
},
{
cssPosition:"566,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"566",
width:"192"
},
dataProviderID:"bcc_outbound_password",
editable:true,
enabled:true,
inputType:"password",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_outbound_password",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"ADB898A8-03BB-4AC2-B073-C4EFFD9FF64D"
},
{
cssPosition:"755,282,-1,520,128,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"520",
right:"282",
top:"755",
width:"128"
},
dataProviderID:"bcc_inbound_ftp_hostkey",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftpHostPublicKey",
visible:true
},
name:"bcc_inbound_hostkey",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"ADD1AAD8-D9B1-4220-86AB-69211FFA769C"
},
{
cssPosition:"87,-1,-1,150,472,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"87",
width:"472"
},
dataProviderID:"clc_work_types",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldWorkTypes",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"ADDE0CF0-C66A-47E2-B2AB-36E66F90A634"
},
{
cssPosition:"355,-1,-1,670,250,147",
json:{
containedForm:"4E6B337D-128C-4F73-9E7A-F66F5661B884",
cssPosition:{
bottom:"-1",
height:"147",
left:"670",
right:"-1",
top:"355",
width:"250"
},
relationName:"bcc_workflow_to_bcc_workflow_report$user_defined",
visible:true
},
name:"tablessc",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"AFE74D36-6562-4491-864B-8C83DCF7B719"
},
{
cssPosition:"755,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"755",
width:"192"
},
dataProviderID:"bcc_inbound_use_sftp",
enabled:true,
onDataChangeMethodID:"AA7526F1-D94E-491B-A165-71C5CAD9ACB7",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.ftpUseSFTP",
visible:true
},
name:"bcc_inbound_use_sftp",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"B16D8535-4689-423E-8A49-E1206C51B9A2"
},
{
cssPosition:"37,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
labelFor:"fldDesc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.itemclass_desc",
visible:true
},
name:"component_6CABE9A8",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B8A1408D-335E-47B9-AB6E-91E9832F8729"
},
{
cssPosition:"512,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"512",
width:"140"
},
enabled:true,
labelFor:"bcc_outbound_host",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_OutboundHost",
visible:true
},
name:"component_5804EFEA",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BC538DF3-DDEC-4FC3-B662-890348E95265"
},
{
cssPosition:"355,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"355",
width:"140"
},
enabled:true,
labelFor:"fldBCCReports",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bccWorkflows_reports",
visible:true
},
name:"component_1B40DEA8",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C1DACACE-6D2B-4C9F-AFA9-8112DCECABFA"
},
{
cssPosition:"218,-1,-1,150,307,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"218",
width:"307"
},
dataProviderID:"bcc_mjb_template_name",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldMJBTemplate",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C41D11A6-DF4E-45CF-A66B-F4C205ADE785"
},
{
cssPosition:"272,-1,-1,172,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"172",
right:"-1",
top:"272",
width:"140"
},
enabled:true,
labelFor:"bcc_label_template",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.LabelTemplate",
visible:true
},
name:"lbl_bcc_label_template",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C6D88ECC-0B4D-4E1E-8560-AB2BFAA16F73"
},
{
cssPosition:"138,-1,-1,150,498,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"138",
width:"498"
},
dataProviderID:"bcc_return_file_name",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldReturnFileName",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C898452E-9A5B-41F6-8B1B-C9243C3E3FA1"
},
{
cssPosition:"647,-1,-1,150,295,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"647",
width:"295"
},
dataProviderID:"bcc_inbound_host",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_inbound_host",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D02B0BD8-537A-4768-8E21-6D83BC02F168"
},
{
cssPosition:"620,-1,-1,323,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"323",
right:"-1",
top:"620",
width:"192"
},
dataProviderID:"bcc_outbound_use_sftp",
enabled:true,
onDataChangeMethodID:"AA7526F1-D94E-491B-A165-71C5CAD9ACB7",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.ftpUseSFTP",
visible:true
},
name:"bcc_outbound_use_sftp",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"D37F0712-044E-48A2-B843-72D5C26A8561"
},
{
cssPosition:"647,-1,-1,444,71,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"444",
right:"-1",
top:"647",
width:"71"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:":",
visible:true
},
name:"component_0F5FB5B2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D3C77283-C985-4C1E-BE10-349EBD886C9E"
},
{
cssPosition:"138,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"138",
width:"140"
},
enabled:true,
labelFor:"fldReturnFileName",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.returnFileName",
visible:true
},
name:"component_13FE2063",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D3F74AFB-B63B-4B9B-938B-EC8454F02D46"
},
{
cssPosition:"728,-1,-1,150,168,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"728",
width:"168"
},
enabled:true,
labelFor:"bcc_outbound_ftp_folder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType_InboundFTPFolder",
visible:true
},
name:"component_1C29F704",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DC41B56B-91E0-4E40-834C-0BCE16625044"
},
{
cssPosition:"218,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"218",
width:"140"
},
enabled:true,
labelFor:"fldMJBTemplate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.MJBTemplate",
visible:true
},
name:"lblMJBTemplate",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E011EADD-5A0C-453D-A4A6-A1A43FE7002A"
},
{
cssPosition:"512,-1,-1,150,295,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"512",
width:"295"
},
dataProviderID:"bcc_outbound_host",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"bcc_outbound_host",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E35F4DC1-EC70-4D38-BC03-F230D9CD6B58"
},
{
cssPosition:"512,-1,-1,455,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"455",
right:"-1",
top:"512",
width:"60"
},
dataProviderID:"bcc_outbound_port",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.ftp_port",
visible:true
},
name:"bcc_outbound_port",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E3F40CAA-971C-435E-BF22-044FFB6034FC"
},
{
cssPosition:"323,-1,-1,152,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"152",
right:"-1",
top:"323",
width:"140"
},
dataProviderID:"bcc_permit",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.permit",
visible:true
},
name:"bcc_permit",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"E4540124-53E7-4CCA-BF44-837AA40F660F"
},
{
cssPosition:"37,-1,-1,150,498,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"37",
width:"498"
},
dataProviderID:"bcc_description",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:3,
visible:true
},
name:"fldDesc",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"EDA89064-3414-4EC9-B07E-310B94BCC1B4"
},
{
cssPosition:"87,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"87",
width:"140"
},
enabled:true,
labelFor:"fldWorkTypes",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.workTypes",
visible:true
},
name:"lblWorkTypes",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F9AE26CA-F9F9-4FB4-AEAB-E3DA7261B966"
},
{
cssPosition:"113,-1,-1,624,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"624",
right:"-1",
top:"113",
width:"24"
},
enabled:true,
onActionMethodID:"F11A1C2E-AE4C-461F-BAD1-2CC3CFD14D5D",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
visible:true
},
name:"btnWorkTemplate",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FE1B7036-904D-4201-946B-3EA1D96890FD"
}
],
name:"sys_bcc_workflow_dtl",
onShowMethodID:"88071A75-89E5-47C8-B9D5-84CC0F00075E",
paperPrintScale:100,
scrollbars:33,
size:"930,787",
typeid:3,
uuid:"FB789B20-ECAF-49C7-ABCD-0353DBF86B9D"