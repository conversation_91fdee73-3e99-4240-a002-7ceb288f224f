customProperties:"useCssPosition:true",
dataSource:"db:/avanti/prod_job_cost_adj",
extendsID:"69DF2DB8-D521-45F8-9886-27B5C716D3F2",
items:[
{
cssPosition:"65,-1,-1,910,140,22",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"910",
right:"-1",
top:"65",
width:"140"
},
enabled:true,
formIndex:27,
labelFor:"div_id",
styleClass:"text-text-right label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.div_id",
visible:true
},
name:"div_id_label",
styleClass:"text-text-right label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"068D2C53-4101-4B5C-A622-AD0B4F4B95EA"
},
{
cssPosition:"38,-1,-1,1055,140,22",
enabled:false,
formIndex:6,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"1055",
right:"-1",
top:"38",
width:"140"
},
dataProviderID:"jca_status",
editable:false,
enabled:false,
formIndex:6,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:-2,
valuelistID:"D2C3F28B-292B-4ACF-9415-13422FA62ABC",
visible:true
},
name:"jca_status",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"16C4FE9C-2DAE-4F54-B6F5-FFDD9C88DD1F"
},
{
cssPosition:"92,-1,-1,150,300,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"92",
width:"300"
},
dataProviderID:"jca_reference",
editable:true,
enabled:true,
formIndex:5,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:2,
visible:true
},
name:"jca_reference",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"23F4879A-D717-4C77-B559-137BBE22C676"
},
{
cssPosition:"155,-1,24,0,1200,374",
json:{
cssPosition:{
bottom:"24",
height:"374",
left:"0",
right:"-1",
top:"155",
width:"1200"
},
tabs:[
{
containedForm:"3A7F6368-48DD-46CA-99B2-AFD74E9285D8",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabAdjustmentDetails",
relationName:"prod_job_cost_adj_to_prod_job_cost_adj_detail",
svyUUID:"F9625663-4034-4E63-8520-8826849B9891",
text:"i18n:avanti.lbl.adjustmentDetails"
},
{
containedForm:"1EAA999E-334C-4FB9-B14F-FDF26E88196A",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"TabAdjustmentDistributions",
relationName:"prod_job_cost_adj_to_prod_job_cost_adj_dist",
svyUUID:"ED65224A-41F0-428C-B004-DA767D8C7A5B",
text:"i18n:avanti.lbl.distributions"
}
]
},
name:"tabDetails",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"3E5B3937-32CE-42DB-91DD-92E89B4468C8"
},
{
cssPosition:"38,-1,-1,5,140,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"38",
width:"140"
},
enabled:true,
formIndex:7,
labelFor:"jca_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.adjustmentNumber",
visible:true
},
name:"jca_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7F0FFE84-7B79-48C6-90AA-93B1F3698A66"
},
{
cssPosition:"92,1055,-1,5,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"1055",
top:"92",
width:"140"
},
enabled:true,
formIndex:1,
labelFor:"jca_reference",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.adjustmentReference",
visible:true
},
name:"jca_reference_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"87C4BA6C-**************-CD199B4FC946"
},
{
cssPosition:"0,-1,-1,0,1200,26",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"26",
left:"0",
right:"-1",
top:"0",
width:"1200"
},
enabled:true,
formIndex:2,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jobCostAdjustmentDetailView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"954E7F1C-639F-445A-A110-CBDC5156C73D"
},
{
cssPosition:"65,-1,-1,5,140,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"65",
width:"140"
},
enabled:true,
formIndex:8,
labelFor:"jca_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.adjustmentDate",
visible:true
},
name:"jca_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A27F9815-2EB9-4990-94EC-8ED9299EC738"
},
{
cssPosition:"38,-1,-1,910,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"910",
right:"-1",
top:"38",
width:"140"
},
enabled:true,
formIndex:2,
labelFor:"jca_status",
styleClass:"text-text-right label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"jca_status_label",
styleClass:"text-text-right label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A92BDD83-96E7-4E3C-B9A0-705C886AB834"
},
{
cssPosition:"89,-1,-1,910,140,22",
formIndex:28,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"910",
right:"-1",
top:"89",
width:"140"
},
enabled:true,
formIndex:28,
labelFor:"plant_id",
styleClass:"text-text-right label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"plant_id_label",
styleClass:"text-text-right label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B22C8A66-77AF-45CB-8978-987903F63162"
},
{
cssPosition:"65,-1,-1,150,140,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"65",
width:"140"
},
dataProviderID:"jca_date",
enabled:true,
formIndex:9,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:1,
visible:true
},
name:"jca_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"B3028547-91AE-4085-9EC8-ADFF846E7346"
},
{
cssPosition:"89,-1,-1,1055,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"1055",
right:"-1",
top:"89",
width:"140"
},
dataProviderID:"plant_id",
enabled:true,
onDataChangeMethodID:"90C14682-3097-4C88-8F81-4320257BE838",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"4B29436C-79B5-465A-A51B-77ABAD58B4C7",
visible:true
},
name:"plant_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"B3BF8360-EEB8-4A27-AC80-10D99BB3D1D0"
},
{
cssPosition:"65,-1,-1,1055,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"1055",
right:"-1",
top:"65",
width:"140"
},
dataProviderID:"div_id",
enabled:true,
onDataChangeMethodID:"30A587D6-EB7C-4582-B01C-B1C252795270",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"9F3857A8-AD20-4F0A-B139-AC6AF1EE1E59",
visible:true
},
name:"div_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BD782582-E0FD-4E4D-816E-1B2E3F0CAB9D"
},
{
cssPosition:"31,-1,-1,0,1200,119",
json:{
cssPosition:{
bottom:"-1",
height:"119",
left:"0",
right:"-1",
top:"31",
width:"1200"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_109F0745",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D23EA0F0-0DE3-4A40-9745-C2AD717B0132"
},
{
cssPosition:"38,-1,-1,150,140,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"38",
width:"140"
},
dataProviderID:"jca_number",
editable:false,
enabled:true,
formIndex:3,
format:"00000000",
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:-2,
visible:true
},
name:"jca_number",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DCF076EE-F3E8-497B-89D8-29464D22B5C6"
},
{
cssPosition:"119,-1,-1,1055,140,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"1055",
right:"-1",
top:"119",
width:"140"
},
enabled:true,
onActionMethodID:"8F467536-7BDC-4CAC-BA7A-D411673AC45C",
styleClass:"btn btn-default button_bts",
tabSeq:4,
text:"i18n:avanti.lbl.update",
visible:true
},
name:"btnUpdate",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"DD4C5E9B-597D-41A6-9FF3-93832E89D9A2"
},
{
height:553,
partType:5,
typeid:19,
uuid:"EDAB6446-4834-438D-A789-E38C3AE62E9B"
}
],
name:"prod_job_cost_adj_dtl",
scrollbars:33,
size:"1200,473",
typeid:3,
uuid:"719FBBB6-4F10-48FC-970A-77CCAEE41763"