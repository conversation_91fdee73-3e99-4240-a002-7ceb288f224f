customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_invoice",
encapsulation:4,
extendsID:"002BD0A7-8C1D-4970-B183-3BC4738F76F3",
initialSort:"inv_date asc",
items:[
{
height:33,
partType:1,
typeid:19,
uuid:"142BE5E3-4AE1-480B-A63F-0FC188489ADF"
},
{
cssPosition:"5,-1,-1,140,125,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"5",
width:"125"
},
enabled:true,
onActionMethodID:"6C7992F8-0AAB-4F71-A229-CF5B9C968D43",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.clearAll",
visible:true
},
name:"btn_clear_all",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"2EFEFCBA-DAF5-44C6-96D5-A3487A58F5FD"
},
{
height:100,
partType:5,
typeid:19,
uuid:"7EF7BB55-2D96-4288-9F2E-42325D4646AF"
},
{
cssPosition:"5,-1,-1,276,125,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"276",
right:"-1",
top:"5",
width:"125"
},
enabled:true,
onActionMethodID:"D0E23768-8EB3-4662-B6FF-77F11D42C197",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.payAll",
visible:false
},
name:"btn_pay_all",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"90322D66-B64E-492D-B43A-04325A9151C8",
visible:false
},
{
height:105,
partType:8,
typeid:19,
uuid:"91657AA8-5681-4CDC-9C11-F0ED306F6719"
},
{
cssPosition:"5,-1,-1,5,125,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"5",
width:"125"
},
enabled:true,
onActionMethodID:"1168EE93-303D-46B5-ABD8-A60C0979D20B",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.payEarliest",
visible:true
},
name:"btn_pay_earliest",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"96510E88-8547-4FA9-BEE5-5BE9BB246B40"
},
{
anchors:15,
cssPosition:"33px,-1,5px,0px,2229px,67px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:"inv_number",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.invoiceNumber",
id:"ap_inv_document_number",
maxWidth:110,
minWidth:110,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"DD14EEAB-56CC-49DF-8F92-A6F5152A9924",
valuelist:null,
visible:true,
width:110
},
{
autoResize:false,
dataprovider:"inv_date",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"DATE",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.invoiceDate",
id:"inv_date",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"BBE9799E-53FB-49A2-A7B9-F8AE55EDF6BE",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_total_amt",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.invoiceTotal",
id:"inv_total_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"7857B04A-FD2B-407E-961C-24690B2FEFFE",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_tot_posted_paym_credits",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.applied",
id:"inv_committed_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"12EFAD7E-DC0A-4CC4-A4C0-90D5F508ADAC",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_postage_amount",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:"¤#,###.00",
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.postage",
id:"postage_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"67912592-F10E-4BB9-8AC7-************",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_balance_amount",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.receiptBalance",
id:"inv_balance_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"8AE0A28D-A2F0-4ECB-A219-26184A30D194",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.payment",
id:"payment_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"BAF999EA-1D74-4BFF-B573-DEFF751D6077",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.overpayment_amount",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.Overpayment",
id:"overpayment_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"8AF4FC6C-E2DE-49AC-90E6-DA88599571EF",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_discount_amount",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.Discount",
id:"discount_amount",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"39A4889C-8CA2-4A04-8E47-E3CFFCB5361D",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.adjustment",
id:"adjustment_amt",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"5793A24B-F8F8-4484-8563-0596524D3D0B",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_total_amt_exchanged",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.invoiceTotal",
id:"inv_total_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D388FCE5-7437-4D15-B745-70756B83DE4C",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_tot_posted_paym_credits_cust_curr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.applied",
id:"inv_committed_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D17A2854-B89A-46A4-9C31-CF409117D544",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_postage_amount_exchanged",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:"¤#,###.00",
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.postage",
id:"postage_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"120BC628-BD49-47B7-98C1-E90D2CC038DF",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"inv_balance_amount_exchanged",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.receiptBalance",
id:"inv_balance_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"4A8851FF-8D60-4FDE-AA7E-1D630CC01B8E",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount_exchanged",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.payment",
id:"payment_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"A8231D1D-0ED8-4614-82A2-D7EE822849EC",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.overpayment_amount_exchanged",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.Overpayment",
id:"overpayment_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D9CAE57E-88B2-4535-BB05-5ED1605113EA",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_discount_amount_exchanged",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.Discount",
id:"discount_amount_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"903C28E0-2B9D-4905-8C44-0B3C12BFE99A",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_amount_exchanged",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"NUMBER",
format:null,
headerStyleClass:"text-right",
headerTitle:"i18n:avanti.lbl.adjustment",
id:"adjustment_amt_cust",
maxWidth:100,
minWidth:100,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"D16F6F78-6651-4DBA-9909-4BC580428CF0",
valuelist:null,
visible:true,
width:100
},
{
autoResize:false,
dataprovider:"sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_adjustment_glacct_id",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.adjustmentGL",
id:"adjustment_gl",
maxWidth:165,
minWidth:165,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"42BB43CA-19D1-47F5-A994-DBC2CDB7EA10",
valuelist:"12AC4EF7-D0A2-4B17-90C0-D77F89C00748",
visible:true,
width:165
},
{
autoResize:true,
dataprovider:"sa_invoice_to_sa_customer.cust_name",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.customerName",
id:"cust_name",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"91417F58-7374-4A4C-AB3D-216C06C38C27",
valuelist:null,
visible:true,
width:210
}
],
cssPosition:{
bottom:"5px",
height:"67px",
left:"0px",
right:"-1",
top:"33px",
width:"2229px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onColumnDataChange:"455D966E-2739-481C-BB79-DC776778E258",
onReady:"00F75671-8924-457C-8CF7-82A319119250",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"26FE0A71-1992-430B-AC8F-66AB8D4B6CCA"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"F216CBF1-F990-4D2B-9462-D4DFFDBEC603"
}
],
name:"sa_cash_receipt_invoices_dtl_tbl",
namedFoundSet:"separate",
navigatorID:"-1",
onShowMethodID:"1539F854-1B5F-49FA-A079-F582CF688005",
scrollbars:33,
size:"2229,100",
styleName:null,
typeid:3,
uuid:"AFD1F377-0BF7-4B22-9727-ECF187524852",
view:0