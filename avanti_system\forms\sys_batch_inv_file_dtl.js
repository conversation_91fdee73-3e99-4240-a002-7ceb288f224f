/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"A7D0F7B9-508B-48C8-BAF4-48216BAF212B"}
 */
function dc_new(_event, _triggerForm) {
	foundset.org_id = globals.org_id
	return _super.dc_new(_event, _triggerForm)
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"8DDF26C9-A3CF-4150-8624-D7C171BEB5E3"}
*/
function dc_save_new(_event) {
	return _super.dc_save_new(_event)
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"F3CBCD0A-1572-4513-A77D-5F261BF2B64E"}
*/
function dc_save(_event, _triggerForm) {
	if(!foundset.org_id){
		foundset.org_id = globals.org_id
	}
	return _super.dc_save(_event, _triggerForm)
}

/**
 *
 * @param {JSEvent} event
 *
 * @return
 * @properties={typeid:24,uuid:"0C884276-267D-430C-AB96-052A81E0520C"}
 */
function onLoad(event) {
//	scopes.globals.avUtilities_tabAdd(controller.getName(), "tabs", "sys_batch_inv_file_field_tbl", i18n.getI18NMessage("avanti.lbl.columns"), "sys_batch_inv_file_to_sys_batch_inv_file_field", null, null, 1, null);
//	scopes.globals.avUtilities_tabAdd(controller.getName(), "tabs", "sys_batch_inv_file_condition_tbl", i18n.getI18NMessage("avanti.lbl.columns"), "sys_batch_inv_file_to_sys_batch_inv_file_field", null, null, 1, null);
	popFilePlacementOptionsVL()
	return _super.onLoad(event)
}

/**
 * @properties={typeid:24,uuid:"CF21B85A-0CEC-4822-9E31-BBCC2185EAF5"}
 */
function updateBatchInvFieldBITableColNames(){
	/***@type {JSFoundset<db:/avanti/sys_batch_inv_file>} */
	var fs_files = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_batch_inv_file')
	fs_files.loadAllRecords()
	
	for(var i=1;i<=fs_files.getSize();i++){
		var rec_file = fs_files.getRecord(i)
		
		var sql = 'select max(biff_bi_data_table_field_num) ' +
		  		  'from sys_batch_inv_file_field sys ' +
				  'inner join app_avail_batch_inv_fields app on app.abif_id = sys.abif_id ' +
				  'where sys.bif_id = ? and (app.abif_data_type = ? or app.abif_data_type = ?)'
		
		var last_char_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.bif_id), 'char', 'char'])
		if(!last_char_field_num){
			last_char_field_num=0
		}
		
		var last_num_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.bif_id), 'number', 'currency'])
		if(!last_num_field_num){
			last_num_field_num=0
		}
		
		var last_date_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.bif_id), 'date', 'date'])
		if(!last_date_field_num){
			last_date_field_num=0
		}		
		
		if(utils.hasRecords(rec_file.sys_batch_inv_file_to_sys_batch_inv_file_field)){
			var numFields = rec_file.sys_batch_inv_file_to_sys_batch_inv_file_field.getSize()
			
			for(var j=1;j<=numFields;j++){
				var rec_field = rec_file.sys_batch_inv_file_to_sys_batch_inv_file_field.getRecord(j)
				
				if(!rec_field.biff_bi_data_table_field_num && utils.hasRecords(rec_field.sys_batch_inv_file_field_to_app_avail_batch_inv_fields)){
					var dataType = rec_field.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_data_type 
					
					if(dataType == 'char'){
						last_char_field_num++
						rec_field.biff_bi_data_table_field_num = last_char_field_num
						rec_field.biff_bi_data_table_field_name = 'char_' + last_char_field_num
					}
					else if(dataType == 'date'){
						last_date_field_num++
						rec_field.biff_bi_data_table_field_num = last_date_field_num
						rec_field.biff_bi_data_table_field_name = 'date_time_' + last_char_field_num
					}
					else if(dataType == 'number' || dataType == 'currency'){
						last_num_field_num++
						rec_field.biff_bi_data_table_field_num = last_num_field_num
						rec_field.biff_bi_data_table_field_name = 'number_' + last_char_field_num
					}
					
					databaseManager.saveData(rec_field)
				}
			}
		}
	}	
}

/**
 * @properties={typeid:24,uuid:"A97EA6D1-30C2-4761-B97E-6915841B78D7"}
 */
function popFilePlacementOptionsVL() {
	// hide not hotfolder options for now
//	application.setValueListItems('vl_filePlacementOptions', ['Hotfolder', 'Download', 'FTP'], [i18n.getI18NMessage('avanti.lbl.hotfolder'), i18n.getI18NMessage('avanti.lbl.download'), i18n.getI18NMessage('avanti.lbl.ftp')]);	
	application.setValueListItems('vl_filePlacementOptions', ['Hotfolder', 'Download'], [i18n.getI18NMessage('avanti.lbl.hotfolder'), i18n.getI18NMessage('avanti.lbl.download')]);	
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"EE0FEC2B-26EB-408E-A692-ED9F23BB5EDE"}
 */
function fileFormat_onDataChange(oldValue, newValue, event) {
	forms.sys_batch_inv_file_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_field_tbl.elements.grid.getColumnIndex("fldPosition")).visible  = newValue == 'Fixed Position';
	forms.sys_batch_inv_file_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_field_tbl.elements.grid.getColumnIndex("fldLength")).visible  = newValue == 'Fixed Position';
	forms.sys_batch_inv_file_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_field_tbl.elements.grid.getColumnIndex("btnClearPositions")).visible  = newValue == 'Fixed Position';

	forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumnIndex("fldPosition")).visible  = newValue == 'Fixed Position';
	forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumnIndex("fldPosition")).visible  = newValue == 'Fixed Position';
	forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumn(forms.sys_batch_inv_file_summary_field_tbl.elements.grid.getColumnIndex("btnClearPositions")).visible  = newValue == 'Fixed Position';
	
	if(newValue == 'Fixed Position'){
		forms.sys_batch_inv_file_field_tbl.foundset.sort('sequence_nr asc');
		forms.sys_batch_inv_file_summary_field_tbl.foundset.sort('sequence_nr asc');
		elements.bif_wrap_text_with_quotes.visible = false;
		elements.bif_wrap_text_with_quotes.enabled = false;
		elements.bif_use_for_inv_reg_export.visible = false;
		elements.bif_use_for_inv_reg_export.enabled = false;
		bif_wrap_text_with_quotes = 0;
		bif_use_for_inv_reg_export = 0;
	}
	else if (newValue == 'Comma Delimited') {
		elements.bif_use_for_inv_reg_export.visible = true;
		elements.bif_use_for_inv_reg_export.enabled = true;
		elements.bif_wrap_text_with_quotes.visible = true;
		elements.bif_wrap_text_with_quotes.enabled = true;
		bif_wrap_text_with_quotes = 1;
	}
	else {
		elements.bif_use_for_inv_reg_export.visible = false;
		elements.bif_use_for_inv_reg_export.enabled = false;
		elements.bif_wrap_text_with_quotes.visible = true;
		elements.bif_wrap_text_with_quotes.enabled = true;
		bif_use_for_inv_reg_export = 0;
		bif_wrap_text_with_quotes = 1;
	}
	
	return true
}

/**
 * @return
 * @properties={typeid:24,uuid:"842DFE0F-E466-4D34-87CE-F8364290DBEF"}
 */
function onSelectFilePath()
{
	var fileDir = plugins.file.showDirectorySelectDialog();

	if(fileDir!=null){
		return fileDir.getAbsolutePath();
	}else {
		return '';
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"277C364F-2F0D-43C5-A624-BF1F989E4E78"}
 */
function btnChooseFolder_onAction(event) {
	var filePath = onSelectFilePath()
	if(filePath != ''){
		foundset.bif_output_folder = filePath
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"4C6C4D5D-9BDA-4BF3-AB6F-CB1448C28D1C"}
 */
function onRecordSelection(_event, _form) {
	// onRecordSelection() wasnt firing on server - put loadAvantiFieldNamesVL() call somewhere else
//	loadAvantiFieldNamesVL();
	return _super.onRecordSelection(_event, _form);
}

/**
 * @properties={typeid:24,uuid:"ECFA6DC2-EEF4-4598-AB4C-12FB8BB0A9A2"}
 */
function loadAvantiFieldNamesVL(){
 	/***@type {JSFoundset<db:/avanti/app_avail_batch_inv_fields>} */
 	var fsField = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'app_avail_batch_inv_fields')
	fsField.loadAllRecords()
	fsField.sort('abif_field_name asc')
	var vlRealValues = new Array();
 	var vlDisplayValues = new Array();
	var vlRealValuesCond = new Array();
 	var vlDisplayValuesCond = new Array();
	var vlRealValuesNum = new Array();
 	var vlDisplayValuesNum = new Array();

	for (var i = 1; i <= fsField.getSize(); i++){
 		var rField = fsField.getRecord(i);
 		var bAddThisField = true;

 		// sl-6852 - filter avanti fields based on detail level
 		if(rField.abif_detail_level == 'LI' && bif_detail_level != 'sa_invoice_det'){
 			bAddThisField = false;
 		}
 		else if (rField.abif_detail_level == 'DI' && bif_detail_level != 'sa_invoice_distribution') {
 			bAddThisField = false;
 		}
 		

 		if(bAddThisField){
 	 		if(!rField.abif_summary_field){
 	 			vlDisplayValues.push(rField.abif_field_name)
 				vlRealValues.push(rField.abif_id)

		 		if(rField.abif_field_name != 'Batch Invoice Post Date'){ // doesnt make sense to use this field in a condition
	 	 			vlDisplayValuesCond.push(rField.abif_field_name)
	 				vlRealValuesCond.push(rField.abif_id)
		 		}
 	 		}
 			
 	 		if(rField.abif_data_type == 'number' || rField.abif_data_type == 'currency' || rField.abif_field_name == 'Batch Invoice Post Date' || rField.abif_summary_field){
 	 			vlDisplayValuesNum.push(rField.abif_field_name)
 				vlRealValuesNum.push(rField.abif_id)
 	 		}
 		}
 	}
	
 	application.setValueListItems('vl_batch_inv_field_names', vlDisplayValues, vlRealValues);
 	application.setValueListItems('vl_batch_inv_field_names_cond', vlDisplayValuesCond, vlRealValuesCond);
 	application.setValueListItems('vl_batch_inv_fields_numeric', vlDisplayValuesNum, vlRealValuesNum);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"15EB08E6-EC15-4A8A-BF33-639B3CD064D6"}
 */
function onDataChange_detailLevel(oldValue, newValue, event) {
	if (newValue == 'sa_invoice_distribution') {
		elements.bif_excl_accts_receivables.enabled = true;
		elements.bif_excl_accts_receivables.visible = true;
		elements.bif_make_debits_negative.enabled = true;
		elements.bif_make_debits_negative.visible = true;
		elements.bif_excl_sales_tax.enabled = true;
		elements.bif_excl_sales_tax.visible = true;
		elements.lbl_distribution_settings.visible = true;
	}
	else {
		elements.bif_excl_accts_receivables.enabled = false;
		elements.bif_excl_accts_receivables.visible = false;
		elements.bif_make_debits_negative.enabled = false;
		elements.bif_make_debits_negative.visible = false;
		elements.bif_excl_sales_tax.enabled = false;
		elements.bif_excl_sales_tax.visible = false;
		elements.lbl_distribution_settings.visible = false;
		bif_make_debits_negative = 0;
		bif_excl_accts_receivables = 0;
		bif_excl_sales_tax = 0;
	}
	if(anyFilteredFields(newValue)){
		if(scopes.avText.showYesNoQuestion('applyCbFieldFilter') == scopes.avText.no){
			bif_detail_level = oldValue;
			return false;
		}
		
		clearFilteredFields(newValue);
		clearFilteredConditions(newValue);
	}
	
	loadAvantiFieldNamesVL();
	
	return true;
}

/**
 * @param {String} sDetailLevel
 * 
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"502BCA30-0DC2-43D0-B6F2-26224F537443"}
 */
function anyFilteredFields(sDetailLevel){
	var fsField = sys_batch_inv_file_to_sys_batch_inv_file_field;
	
	if(utils.hasRecords(fsField)){
		var nMax = fsField.getSize();
		
	 	for (var i = 1; i <= nMax; i++){
	 		var rField = fsField.getRecord(i);
	 		
	 		if(utils.hasRecords(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields)){
		 		if(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_detail_level == 'LI' && sDetailLevel != 'sa_invoice_det'){
		 			return true;
		 		}
		 		else if(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_detail_level == 'DI' && sDetailLevel != 'sa_invoice_distribution'){
		 			return true;
		 		}
		 		else if(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_detail_level == 'IN' && sDetailLevel != 'sa_invoice'){
		 			return true;
		 		}
	 		}
	 	}
	}
	
	return false;
}

/**
 * @param {String} sDetailLevel
 *
 * @properties={typeid:24,uuid:"FD261AA3-4952-4E36-8602-BBC2DD2A6EF5"}
 */
function clearFilteredFields(sDetailLevel){
	var fsField = sys_batch_inv_file_to_sys_batch_inv_file_field;
	
	if(utils.hasRecords(fsField)){
		var nMax = fsField.getSize();
		
	 	for (var i = 1; i <= nMax; i++){
	 		var rField = fsField.getRecord(i);

	 		if(utils.hasRecords(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields)){
		 		if((rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_detail_level == 'LI' && sDetailLevel != 'sa_invoice_det') || 
		 				(rField.sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_detail_level == 'DI' && sDetailLevel != 'sa_invoice_distribution')){
			 		rField.abif_id = null;
			 		rField.biff_bi_data_table_field_num = null;
			 		rField.biff_bi_data_table_field_name = null;
		 		}
	 		}
	 	}
	}
}

/**
 * @param {String} sDetailLevel
 *
 * @properties={typeid:24,uuid:"CB5DFBEC-4F37-402C-B844-3485A8897084"}
 */
function clearFilteredConditions(sDetailLevel){
	var fsCond = sys_batch_inv_file_to_sys_batch_inv_file_condition;
	
	if(utils.hasRecords(fsCond)){
	 	for (var i = 1; i <= fsCond.getSize(); i++){
	 		var rCond = fsCond.getRecord(i);

	 		if(utils.hasRecords(rCond.sys_batch_inv_file_condition_to_app_avail_batch_inv_fields)){
		 		if((rCond.sys_batch_inv_file_condition_to_app_avail_batch_inv_fields.abif_detail_level == 'LI' && sDetailLevel != 'sa_invoice_det') || 
		 				(rCond.sys_batch_inv_file_condition_to_app_avail_batch_inv_fields.abif_detail_level == 'DI' && sDetailLevel != 'sa_invoice_distribution')){
		 			rCond.acbf_id = null;
		 		}
	 		}
	 	}
	}
}

/**
 *
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 *
 * @return
 * @properties={typeid:24,uuid:"95CE4783-C835-4F88-9733-6386355D08E5"}
 */
function onShowForm(_firstShow, _event) {
	refreshUI();
	loadAvantiFieldNamesVL();
	return _super.onShowForm(_firstShow, _event)
}

/**
 * @properties={typeid:24,uuid:"66C5B604-DEA2-4EEC-B7F1-C661C5476D00"}
 */
function refreshUI() {
	if (bif_detail_level == 'sa_invoice_distribution') {
		elements.bif_excl_accts_receivables.enabled = true;
		elements.bif_excl_accts_receivables.visible = true;
		elements.bif_make_debits_negative.enabled = true;
		elements.bif_make_debits_negative.visible = true;
		elements.bif_use_for_inv_reg_export.visible = true;
		elements.bif_use_for_inv_reg_export.enabled = true;
		elements.lbl_distribution_settings.visible = true;
		elements.bif_excl_sales_tax.visible = true;
		elements.bif_excl_sales_tax.enabled = true;
	}
	else {
		elements.bif_excl_accts_receivables.enabled = false;
		elements.bif_excl_accts_receivables.visible = false;
		elements.bif_make_debits_negative.enabled = false;
		elements.bif_make_debits_negative.visible = false;
		elements.bif_use_for_inv_reg_export.visible = false;
		elements.bif_use_for_inv_reg_export.enabled = false;
		elements.lbl_distribution_settings.visible = false;
		elements.bif_excl_sales_tax.visible = false;
		elements.bif_excl_sales_tax.enabled = false;
	}
	
	if (bif_file_format == 'Fixed Position') {
		elements.bif_wrap_text_with_quotes.visible = false;
		elements.bif_wrap_text_with_quotes.enabled = false;
	} 
	else {
		elements.bif_wrap_text_with_quotes.visible = true;
		elements.bif_wrap_text_with_quotes.enabled = true;
	}
}

/**
 *
 * @param {JSEvent} _event
 *
 * @return
 * @properties={typeid:24,uuid:"40771E01-AC5F-4CD9-9ABB-55C3AE585B82"}
 */
function dc_rec_first(_event) {
	var r = _super.dc_rec_first(_event);
	loadAvantiFieldNamesVL();
	refreshUI();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"1364B93D-93E4-431F-BD82-A8C46D9FFCF1"}
*/
function dc_rec_last(_event) {
	var r = _super.dc_rec_last(_event)
	loadAvantiFieldNamesVL();
	refreshUI();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"6E7C67D1-7913-425C-9D8A-BAB5DA66A525"}
*/
function dc_rec_next(_event) {
	var r = _super.dc_rec_next(_event)
	loadAvantiFieldNamesVL();
	refreshUI();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"CFA19E22-6627-423E-8C4C-385BE533E6A7"}
*/
function dc_rec_prev(_event) {
	var r = _super.dc_rec_prev(_event)
	loadAvantiFieldNamesVL();
	refreshUI();
	return r;
}

/**
 *
 * @param {JSFoundSet} _foundset
 * @param {String} _program
 *
 * @return
 * @properties={typeid:24,uuid:"0AA2D2CA-372B-46A0-8E80-15576C6DE099"}
 */
function dc_save_validate(_foundset, _program) {
	if(validConditions() && validPositions()){
		return _super.dc_save_validate(_foundset, _program);
	}
	else{
		return -1;
	}
}

/**
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"74AC7882-CBCE-47C5-8C1C-6CD11341342F"}
 */
function validConditions(){
	if(utils.hasRecords(sys_batch_inv_file_to_sys_batch_inv_file_condition)){
		var sCond = scopes.avText.getLblMsg('Condition');
		
		for(var i=1; i<=sys_batch_inv_file_to_sys_batch_inv_file_condition.getSize(); i++){
			var rCond = sys_batch_inv_file_to_sys_batch_inv_file_condition.getRecord(i);
			
			if(utils.hasRecords(rCond.sys_batch_inv_file_condition_to_app_avail_batch_inv_fields)){
				var sDataType = rCond.sys_batch_inv_file_condition_to_app_avail_batch_inv_fields.abif_data_type;
				
				if(sDataType == 'currency'){
					sDataType = 'number';
				}
			}
			
			if(rCond.sequence_nr > 1 && !rCond.bicond_and_or){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('AndOr')]);
				return false;
			}
			else if(!rCond.abif_id){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('avantiFieldName')]);
				return false;
			}
			else if(!rCond.bicond_condition){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, sCond]);
				return false;
			}

			// FROM's
			else if(sDataType == 'date' && !rCond.bicond_from_date){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			else if(sDataType == 'integer' && !rCond.bicond_from_int){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			else if(sDataType == 'number' && !rCond.bicond_from_num){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			else if(sDataType == 'char' && !rCond.bicond_from_text){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			else if(sDataType == 'uuid' && forms.sys_batch_inv_file_condition_tbl.isCharOnlyCondition(rCond.bicond_condition) && !rCond.bicond_from_text){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			else if(sDataType == 'uuid' && !forms.sys_batch_inv_file_condition_tbl.isCharOnlyCondition(rCond.bicond_condition) && !rCond.bicond_from_uuid){
				scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('FromOrOnly')]);
				return false;
			}
			
			// TO's
			else if(rCond.bicond_condition == 'BETWEEN' || rCond.bicond_condition == '!BETWEEN'){
				if(sDataType == 'date' && !rCond.bicond_to_date){
					scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('to')]);
					return false;
				}
				else if(sDataType == 'integer' && !rCond.bicond_to_int){
					scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('to')]);
					return false;
				}
				else if(sDataType == 'number' && !rCond.bicond_to_num){
					scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('to')]);
					return false;
				}
				else if(sDataType == 'char' && !rCond.bicond_to_text){
					scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('to')]);
					return false;
				}
				else if(sDataType == 'uuid' && !rCond.bicond_to_uuid){
					scopes.avText.showWarning('rowMissingValue', null, [sCond, i, scopes.avText.getLblMsg('to')]);
					return false;
				}
			}
		}
	}
	
	return true;
}

/**
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"CE5938CD-8573-4225-8F5D-978E007D0923"}
 */
function validPositions(){
	if(bif_file_format == 'Fixed Position' && utils.hasRecords(sys_batch_inv_file_to_sys_batch_inv_file_field)){
		var sField = i18n.getI18NMessage('svy.fr.lbl.field');
		
		for(var i=1; i<=sys_batch_inv_file_to_sys_batch_inv_file_field.getSize(); i++){
			var rField = sys_batch_inv_file_to_sys_batch_inv_file_field.getRecord(i);

			if(!rField.biff_position){
				scopes.avText.showWarning('rowMissingValue', null, [sField, i, scopes.avText.getLblMsg('position')]);
				return false;
			}
			else if(!rField.biff_length && rField.sequence_nr != sys_batch_inv_file_to_sys_batch_inv_file_field.getSize()){
				scopes.avText.showWarning('rowMissingValue', null, [sField, i, scopes.avText.getLblMsg('length2')]);
				return false;
			}
		}
	}
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2E8106B7-EAB2-4174-989C-E61851F8221C"}
 */
function onDataChange_currency_zoned(oldValue, newValue, event) {
	if(newValue == 1){
		bif_currency_k_separator = 0;
		bif_currency_round = 0;
		bif_currency_symbol = 0;
	}
	
	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"1925CF15-7781-4988-80BF-7531F90AFC89"}
 */
function onDataChange_currency_other(oldValue, newValue, event) {
	if(newValue == 1){
		bif_currency_zoned = 0;
	}

	return true
}
