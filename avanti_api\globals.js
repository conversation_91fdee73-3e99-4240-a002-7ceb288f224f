/**
 * @param {Object} body
 * @param {Object} [existingParametersJSON]
 * @return {Object}
 * @properties={typeid:24,uuid:"65731A98-C023-4B52-9237-F2B588BEB81C"}
 */
function parseBodyToJSON(body, existingParametersJSON){
    var localParametersJSON = (existingParametersJSON!=null) ? existingParametersJSON : new Object();
    for (var key in body) {
        //do not overwrite existing key
        if (!localParametersJSON.hasOwnProperty(key)){
            localParametersJSON[key] = body[key];
        }
    }
    return localParametersJSON;
}

/**
 * @param {Object} arguments
 * @param {JSON} [existingParametersJSON]
 * @return {JSON}
 * @properties={typeid:24,uuid:"345995BF-6730-4EBD-B522-D617EFCD2B1C"}
 */
function parseArgumentsToJSON(arguments, existingParametersJSON){
    /** @type {JSON} */
    var localParametersJSON = (existingParametersJSON!=null) ? existingParametersJSON : new Object();
    for (var i = 0; i < arguments.length; i++) {        
        if (typeof arguments[i] == 'string') { //The URL path additions are passed in as Strings
            //such type of parameters in URL are ignored in this solution
        } else {
            if(arguments[i] instanceof Array) {
                //application.output('Array arguments[i]: ' + arguments[i]);
            } else if(arguments[i] instanceof Object) {
                for (var key in arguments[i]) {
                    if(arguments[i][key] instanceof Array) {
                        localParametersJSON[key] = arguments[i][key][0];
                    } else {
                        localParametersJSON[key] = arguments[i][key];
                    }
                }
            }
        }       
    }
    return localParametersJSON;
}

/**
 * @param {JSON} parameters
 * @param {Object} bWireless
 * @return {Object}
 * @properties={typeid:24,uuid:"5EE89AAF-D3F8-4F3C-AB5B-4DD6563522FE"}
 */
function requestProcessor(parameters, bWireless){
    application.output("<<<< requestProcessor(), parameters: " + JSON.stringify(parameters), LOGGINGLEVEL.INFO);

    var response = new Object();
    var bSkipAuthenticate = false;
    
    //shop floor has different auth as jwt token's payload is different
	if (bWireless && typeof scopes.avShopFloorAPI.methods[parameters["method"]] === "function") {

		try {
			scopes.globals.bProcessingMobileAPI = true;
			scopes.avShopFloorAPI.shopfloorAuthToken(parameters);
			response = scopes.avShopFloorAPI.methods[parameters["method"]](parameters);
			scopes.globals.bProcessingMobileAPI = false;
		} catch (e) {
			throw e;
		}

		return response;
	}
	//shop floor request part end
	
    //authentication
    //some scopes variables are set by authenticateForAPI including: scopes.avUtils.getPrefs();
    try {
    	
    	var args = [];
    	if(parameters['Username'] !== undefined ){
    		args.push({'Username':[parameters['Username']]});
    	}
		if(parameters['Password'] !== undefined ){
			args.push({'Password':[parameters['Password']]});
    	}
		if(parameters['token'] !== undefined ){
			args.push({'token':[parameters['token']]});
    	}
        
        if (bWireless) {
			try {
				var aMethod = [{ 'method': [parameters['method']] }];
				if (aMethod && aMethod.length == 1 
						&& (aMethod[0]["method"][0] == "auth_AvantiMobileAppType" 
						|| aMethod[0]["method"][0] == "auth_AuthorizeDevice"
						|| aMethod[0]["method"][0] == "auth_RemoveMobileDeviceAuthorization"
						|| aMethod[0]["method"][0] == "auth_IsAvantiMobileDeviceAuthorized")) {
					bSkipAuthenticate = true;
					response = null;
				}
			} catch (e) {
				bSkipAuthenticate = false;
			}
        }
        			        
        //returns null if Ok'y, otherwise throws exception 
        if (!bSkipAuthenticate) {
        	response = scopes.avLogin.authenticateForAPI(args);
        }
        
    } catch (ex) {}
    
    if (response!=null){
        application.output("Failed Authentication." , LOGGINGLEVEL.ERROR);
        if (scopes.globals.avBase_AccountingAPI) {
            throw [plugins.http.HTTP_STATUS.SC_UNAUTHORIZED, 'Failed Authentication: Invalid web service code token and/or api_integration user missing.'];
        }
        else {
            throw [plugins.http.HTTP_STATUS.SC_UNAUTHORIZED, 'Failed Authentication.'];
        }
    }
    
    //for security reason, delete not needed anymore parameters
    delete parameters['Username'];
    delete parameters['Password'];
    
    response = new Object();
    
    try {
        if (bWireless) {
            if (typeof scopes.avWirelessInventory.methods[parameters["method"]] === "function") {
            	scopes.globals.bProcessingMobileAPI = true;
                response = scopes.avWirelessInventory.methods[parameters["method"]](parameters);
                scopes.globals.bProcessingMobileAPI = false;
            }
            else {
                throw [plugins.http.HTTP_STATUS.SC_BAD_REQUEST, "Incorrect method specified."];
            }
        }
        else {
            if (typeof scopes.avWebServices.methods[parameters["method"]] === "function") {
                response = scopes.avWebServices.methods[parameters["method"]](parameters);
            }
            else if (
            		typeof scopes.accounting.getMethods[parameters["method"]] === "function" ||
            		typeof scopes.accounting.postMethods[parameters["method"]] === "function" ||
            		typeof scopes.accounting.putMethods[parameters["method"]] === "function"
            ) {
            	var returnValue = null;
            	if(parameters["requestType"] == "get" && scopes.accounting.getMethods[parameters["method"]]) {
            		returnValue = scopes.accounting.getMethods[parameters["method"]](parameters);
            	} else if (parameters["requestType"] == "post" && scopes.accounting.postMethods[parameters["method"]]) {
            		returnValue = scopes.accounting.postMethods[parameters["method"]](parameters);
            	} else if (parameters["requestType"] == "put" && scopes.accounting.putMethods[parameters["method"]]) {
            		returnValue = scopes.accounting.putMethods[parameters["method"]](parameters);
                } else if (parameters["requestType"] == "delete" && scopes.accounting.deleteMethods[parameters["method"]]) {
                    returnValue = scopes.accounting.deleteMethods[parameters["method"]](parameters);
            	} else {
            		throw [plugins.http.HTTP_STATUS.SC_NOT_FOUND, "Incorrect method specified."];	
            	}
            	
            	if(returnValue) {
            		response = returnValue.response;
                	var statusCode = returnValue.statusCode;
    				
    				if (statusCode) {
    					var statusMessage = returnValue.statusMessage;
    					throw [statusCode, JSON.stringify(statusMessage)];
    				}	
            	}
            	
            } else {
            	throw [plugins.http.HTTP_STATUS.SC_NOT_FOUND, "Incorrect method specified."];
            }
        }
    }
    catch (ex) {
        application.output("Exception requestProcessor(): " + ( ex.message != null ? ex.message : ex ) + "\nStack: " + ex.stack);
        scopes.globals.bProcessingMobileAPI = false;
        throw ex;
    }
    
    application.output(">>>> requestProcessor(), response: " + JSON.stringify(response), LOGGINGLEVEL.INFO);
    
    return response;
}

/**
 * @param {String} item_id
 * @returns { {item_id:String, item_code:String, whse_id:String, whseloc_id:String, whseloc_bin_location:String, itemwhse_last_cost:Number, itemwhse_onpo_qty:Number, itemwhse_committed_qty:number
 *              , item_standard_uom_id:String} }
 *
 *
 *
 * @properties={typeid:24,uuid:"49F1AC30-8233-4ECF-8B92-F4C63A9AB7F2"}
 */
function getItemDefaultReceiptBinLocation(item_id) {
    /** @type { {item_id:String, item_code:String, whse_id:String, whseloc_id:String, whseloc_bin_location:String, itemwhse_last_cost:Number, itemwhse_onpo_qty:Number, itemwhse_committed_qty:number
     , item_standard_uom_id:String } }
     */
    var itemWarehouseDefaultBinLocation = null;

    if (item_id != null && item_id.length > 0) {

        var args = [item_id, scopes.globals.org_id];
        var ds_0 = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_item.item_code, in_item_warehouse.whse_id, in_item.item_standard_uom_id \
                FROM in_item \
                LEFT JOIN in_item_warehouse ON (in_item.item_id = in_item_warehouse.item_id) \
                WHERE in_item.item_id = ? AND in_item.org_id = ?", args, 10);

        //Comment below is from Tim's implementation
        //Logic as per Josh: Because the warehouse isn't passed in from the web service, we are using the following logic.
        //First check to see if the item belongs to one warehouse. If so, we will use that warehouse.
        //If the item belongs to multiple warehouses, get the employee default warehouse established when user authenticated.
        if (ds_0.getMaxRowIndex() > 0) {
            ds_0.rowIndex = 1;
            var item_standard_uom_id = ds_0['item_standard_uom_id'];
            var item_code = ds_0['item_code'];

            if (ds_0.getMaxRowIndex() > 1) {

                if (scopes.globals.avBase_employeeDefaultWarehouse) {
                    args = [item_id, scopes.globals.avBase_employeeDefaultWarehouse, scopes.globals.org_id];
                    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_item_warehouse.itemwhse_default_receipt_bin, in_warehouse_location.whseloc_bin_location \
                            , ISNULL(in_item_warehouse.itemwhse_last_cost,0) as itemwhse_last_cost, in_item_warehouse.itemwhse_onpo_qty, in_item_warehouse.itemwhse_committed_qty \
                            FROM in_item_warehouse \
                            INNER JOIN in_warehouse_location ON (in_item_warehouse.itemwhse_default_receipt_bin=in_warehouse_location.whseloc_id) \
                            WHERE in_item_warehouse.item_id = ? AND in_item_warehouse.whse_id = ? AND in_item_warehouse.org_id = ?", args, 10);

                    if (ds.getMaxRowIndex() > 0) {
                        ds.rowIndex = 1;

                        itemWarehouseDefaultBinLocation = {
                            item_id: item_id,
                            item_code: item_code,
                            whse_id: scopes.globals.avBase_employeeDefaultWarehouse,
                            whseloc_id: ds['itemwhse_default_receipt_bin'],
                            whseloc_bin_location: ds['whseloc_bin_location'],
                            itemwhse_last_cost: ds['itemwhse_last_cost'],
                            itemwhse_onpo_qty: ds['itemwhse_onpo_qty'],
                            itemwhse_committed_qty: ds['itemwhse_committed_qty'],
                            item_standard_uom_id: item_standard_uom_id
                        };
                    }
                }
            }//single whse only
            else {
                var whse_id = ds_0['whse_id'];

                args = [item_id, whse_id, scopes.globals.org_id];
                ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT in_item_warehouse.itemwhse_default_receipt_bin, in_warehouse_location.whseloc_bin_location \
                        , ISNULL(in_item_warehouse.itemwhse_last_cost,0) as itemwhse_last_cost, in_item_warehouse.itemwhse_onpo_qty, in_item_warehouse.itemwhse_committed_qty \
                        FROM in_item_warehouse \
                        INNER JOIN in_warehouse_location ON (in_item_warehouse.itemwhse_default_receipt_bin=in_warehouse_location.whseloc_id) \
                        WHERE in_item_warehouse.item_id = ? AND in_item_warehouse.whse_id = ? AND in_item_warehouse.org_id = ?", args, 10);

                if (ds.getMaxRowIndex() > 0) {
                    ds.rowIndex = 1;
                    itemWarehouseDefaultBinLocation = {
                        item_id: item_id,
                        item_code: item_code,
                        whse_id: scopes.globals.avBase_employeeDefaultWarehouse,
                        whseloc_id: ds['itemwhse_default_receipt_bin'],
                        whseloc_bin_location: ds['whseloc_bin_location'],
                        itemwhse_last_cost: ds['itemwhse_last_cost'],
                        itemwhse_onpo_qty: ds['itemwhse_onpo_qty'],
                        itemwhse_committed_qty: ds['itemwhse_committed_qty'],
                        item_standard_uom_id: item_standard_uom_id
                    };
                }

                //if default_receipt_bin is not specified, there is a chance that in_item_warehouse_location has a record with this item
                //let's use it as a default location
                if (itemWarehouseDefaultBinLocation == null) {

                    args = [item_id, whse_id, scopes.globals.org_id];
                    ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT ISNULL(in_item_warehouse.itemwhse_last_cost,0) \
                            as itemwhse_last_cost, in_item_warehouse_location.whseloc_id, in_warehouse_location.whseloc_bin_location, \
                            in_item_warehouse.itemwhse_onpo_qty, in_item_warehouse.itemwhse_committed_qty \
                            FROM in_item_warehouse \
                            INNER JOIN in_item_warehouse_location ON (in_item_warehouse.itemwhse_id=in_item_warehouse_location.itemwhse_id) \
                            INNER JOIN in_warehouse_location ON (in_warehouse_location.whseloc_id=in_item_warehouse_location.whseloc_id) \
                            WHERE in_item_warehouse.item_id = ? AND in_item_warehouse.whse_id = ? AND in_item_warehouse.org_id = ?", args, 10);

                    if (ds.getMaxRowIndex() > 0) {
                        ds.rowIndex = 1;
                        itemWarehouseDefaultBinLocation = {
                            item_id: item_id,
                            item_code: item_code,
                            whse_id: whse_id,
                            whseloc_id: ds['whseloc_id'],
                            whseloc_bin_location: ds['whseloc_bin_location'],
                            itemwhse_last_cost: ds['itemwhse_last_cost'],
                            itemwhse_onpo_qty: ds['itemwhse_onpo_qty'],
                            itemwhse_committed_qty: ds['itemwhse_committed_qty'],
                            item_standard_uom_id: item_standard_uom_id
                        };
                    }
                }
            }
        }
    }

    return itemWarehouseDefaultBinLocation;
}

/**
 * This will process order tasks after web to print completes. 
 * 
 * @param {{
 *                      sOrderID:String, 
 *                      sIntEstimateID: String,
 *                      sOrgID: String, 
 *                      sDBLogParentUUID:String, 
 *                      sXMLSenderID:String, 
 *                      oReturnObject:{message, status},
 *                      sStatus:String, 
 *                      bShowWarning:Boolean, 
 *                      sClientID: String
 *                      }} xmlOrderDetails
 *
 * @properties={typeid:24,uuid:"A87D103B-6991-478F-AACB-76BC3DF97814"}
 * @AllowToRunInFind
 */
function processPostOrderTasks(xmlOrderDetails) {
    try {
        if (!xmlOrderDetails) {
            application.output('Could not process order tasks as no object was passed in.', LOGGINGLEVEL.ERROR);
            return;
        }
        var sOrgID = xmlOrderDetails.sOrgID;

        var sSQL = 'select owner_id, org_name from sys_organization where org_id = ?';
        var dsOrg = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, [sOrgID.toString()], 1);

        globals.owner_id = dsOrg.getValue(1, 1);
        globals.org_id = sOrgID;
        globals.org_name = dsOrg.getValue(1, 2);
        
        // sl-27749 - have to set these 2 for org_id filtering to work
        globals.svy_sec_lgn_organization_id = globals.org_id;
        globals.svy_sec_owner_id = globals.owner_id;

        globals.svy_nav_filterOrganization();
        scopes.avUtils.getPrefs();
        
        forms["_docs_base"].init_avDocs_oDocs();

        /** @type {JSFoundSet<db:/avanti/sys_sender>} */
        var fsXMLSender = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sender');
        if (xmlOrderDetails.sXMLSenderID != null && fsXMLSender.find()) {
            fsXMLSender.sys_sender_id = xmlOrderDetails.sXMLSenderID;
            fsXMLSender.org_id = sOrgID;
            if (fsXMLSender.search() > 0) {
            }
            else {
                globals.dbLog('Could not find XML Sender', 'wtp_request', 'import', 'wtp', '', application.getUUID(sOrgID), 'logging', 'Detail', application.getUUID(xmlOrderDetails.sDBLogParentUUID));
                xmlOrderDetails.sStatus = 'Error';
                xmlOrderDetails.oReturnObject.message = 'Error: Could not find XML Sender';
                xmlOrderDetails.oReturnObject.status = xmlOrderDetails.sStatus;
                return;
            }
        }

        /**@type {{message:String, status:String}} */
        var oReturn = scopes.avWebToPrint.createJobFromTables(application.getUUID(xmlOrderDetails.sIntEstimateID), fsXMLSender, application.getUUID(xmlOrderDetails.sDBLogParentUUID), xmlOrderDetails.sOrderID, null);
        
        updateOrderBillingCodeDetailsForImport(xmlOrderDetails.sOrderID);
        
        if (oReturn && oReturn.status != 'Success') {
            scopes.avUtils.quickLog('createJobFromTables oReturn', 'status: ' + oReturn.status + ', message: ' + oReturn.message);
        }
    }
    catch (ex) {
        if (ex && ex.message) {
            var sErrMsg = "processPostOrderTasks error. msg: " + ex.message + ". stack: " + ex.stack;
            globals.dbLog(sErrMsg, 'wtp_request', 'import', 'wtp', '', application.getUUID(globals.org_id), 'logging', 
                'Detail', application.getUUID(xmlOrderDetails.sDBLogParentUUID), null, null, null);
        }
    }
    finally {
    	// Shutdown headless client
    	application.exit();
    }
}


/**
 * Updates Order BillingCodeDetails tab data for import in return response early mode
 * @param {String} sOrderId
 *
 * @properties={typeid:24,uuid:"A8C87633-8AE6-4132-880D-3748E0BC1B3B"}
 */
function updateOrderBillingCodeDetailsForImport (sOrderId) {
	if (sOrderId) {
		/**@type {JSRecord<db:/avanti/sa_order>} */
		var rOrder = scopes.avDB.getRec('sa_order', ['ordh_id'], [sOrderId]);
		if (rOrder) {
			globals.avBase_invoiceGPFeatureTokenEnabled = (_to_sys_organization.org_invoice_gp_feature_token == "EnableAvantiGPFeature515" ? true : false);
			scopes.avSales.updateOrderShipLocationTask(rOrder);
		}
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"78AE4845-C7F7-423E-B4DA-9B6D60CB171F"}
 */
function getWarehouseIdByWarehouseCode(whseCode){
    var querySql = "SELECT whse_id from in_warehouse \
     where org_id = ? AND whse_code = ?";
     var arguements = [scopes.globals.org_id, whseCode];
     
     var result = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, querySql, arguements, -1);
    
 	var nRowMax = result.getMaxRowIndex(); 
 	
 	if(nRowMax > 0) {
 		result.rowIndex = 1;
 		return result['whse_id'];
 	}
 	return '';
}

/**
 * @return
 * @properties={typeid:24,uuid:"1AC7EFAA-E212-418A-A73E-440CB577DA2B"}
 * @AllowToRunInFind
 */
function getItemWarehouseID(itemId, warehouseId) {
	/** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
	var fs_in_item_warehouse = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item_warehouse')
	if(fs_in_item_warehouse.find() || fs_in_item_warehouse.find()){
		fs_in_item_warehouse.item_id = itemId;
		fs_in_item_warehouse.whse_id = warehouseId;
		if(fs_in_item_warehouse.search()){
			return fs_in_item_warehouse.getRecord(1).itemwhse_id;
		}
	}
	
	return null
}

/**
 * @properties={typeid:24,uuid:"4C8AA0A9-3679-445C-BD07-38D8DF7DCA43"}
 * @param {JSRecord<db:/avanti/prod_job_cost_material_entry>} _rEntry
 * @param {String} current_jcl_id
 * @return {JSRecord<db:/avanti/prod_job_cost_material>}
 */
function createMatRecord(_rEntry, current_jcl_id) {
	//from entry to material
	var _rMat;
	if(_rEntry){
		if(utils.hasRecords(_rEntry.prod_job_cost_material_entry_to_prod_job_cost_material)){
			_rMat = _rEntry.prod_job_cost_material_entry_to_prod_job_cost_material.getRecord(1);
			return _rMat;
		}
	}
		// create mat rec if we dont have one
	if (!_rMat) {

		/** @type {JSFoundSet<db:/avanti/prod_job_cost_labour>} */
		var fsProdJobCostLabor = scopes.avDB.getFS('prod_job_cost_labour', ['jcl_id'], [current_jcl_id]);
		var rProdJobCostLabor = fsProdJobCostLabor.getRecord(1);
	    	
	        
		/** @type {JSFoundSet<db:/avanti/prod_job_cost>} */
		var fsProdJobCost = scopes.avDB.getFS('prod_job_cost', ['jc_id'], [rProdJobCostLabor.jc_id]);
		var rOpJC = fsProdJobCost.getRecord(1);
			
			
	    /**@type {JSRecord<db:/avanti/prod_job_cost_material>} */
	    _rMat = scopes.avDB.getNewRec('prod_job_cost_material');
	        
	        
	    /**@type {JSRecord<db:/avanti/prod_job_cost>} */
//	    var rMatJC = rOpJC.foundset.duplicateRecord(1, false, true);
	    var rMatJC = rOpJC.foundset.getRecord(rOpJC.foundset.duplicateRecord(1, false, true));
	    
	    rMatJC.jc_cost_type = scopes.avUtils.JOB_COST_TYPE.Material;
	    databaseManager.saveData(rMatJC);

	    _rMat.jc_id = rMatJC.jc_id;

	    _rEntry.jcm_id = _rMat.jcm_id;

	    _rMat.created_by_id = _rEntry.empl_id;
	    _rMat.created_date = application.getTimeStamp();
	    _rMat.sys_employee_shift_id = forms.sf_main_dtl.current_shift_id;
	    _rMat.job_id = _rEntry.job_id;
	    _rMat.item_id = _rEntry.item_id;
	    _rMat.whse_id = _rEntry.whse_id;
	    _rMat.whseloc_id = _rEntry.whseloc_id;
	    _rMat.initemroll_id = _rEntry.initemroll_id;
	    if (_rMat.whse_id) {
	    	/** @type {JSFoundSet<db:/avanti/in_item_warehouse>} */
			var fsInItemWarehouse =  scopes.avDB.getFS('in_item_warehouse', ['item_id','whse_id'], [_rEntry.item_id, _rEntry.whse_id]);
			var rInItemWarehouse = fsInItemWarehouse.getRecord(1);
	        _rMat.itemwhse_id = rInItemWarehouse.itemwhse_id;
	    }

	    //Need to assign the material cost centre based on rules
	    var rMaterialCostCentre = globals.getMaterialCostCentreFromLabourCostCentre(rOpJC.cc_id, _rMat.item_id);
	    if (rMaterialCostCentre) {
	        rMatJC.cc_id = rMaterialCostCentre.cc_id;
	        rMatJC.dept_id = rMaterialCostCentre.dept_id;
	        rMatJC.opcat_id = rMaterialCostCentre.opcat_id;
	        rMatJC.jc_op_code = rMaterialCostCentre.cc_op_code;
	    }
	    else {
	        rMatJC.cc_id = null;
	        rMatJC.dept_id = null;
	        rMatJC.opcat_id = null;
	        rMatJC.jc_op_code = null;
	    }
	        databaseManager.saveData(_rMat);
	    }
	    return _rMat;  
}


//from avant_shop_floor/forms/_sf_base.js

/**
 * @properties={typeid:24,uuid:"B9549016-26FF-4E64-A1F0-E3E2F8FFCAFB"}
 * 
 * @param {UUID} itemID
 * @param {UUID} whseID
 * @param {UUID} ordrevdID
 *
 * @returns {Number} Total Quantity Reserved for selected job
 */
function getQtyReservedAgainstSelectedJob(itemID, whseID, ordrevdID) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL ={};
	
	 /*** @type {JSFoundSet<db:/avanti/sa_order_revd_item>} */
    var fsReserved;
    
    var iReserved = 0.00;

    oSQL.sql = "SELECT dbo.sa_order_revd_item.ordrevditem_id\
    			FROM dbo.sa_order_revd_item INNER JOIN\
                         dbo.sa_order_revision_detail ON dbo.sa_order_revd_item.ordrevd_id = dbo.sa_order_revision_detail.ordrevd_id INNER JOIN\
                         dbo.sa_order_revision_header ON dbo.sa_order_revision_detail.ordrevh_id = dbo.sa_order_revision_header.ordrevh_id INNER JOIN\
                         dbo.sa_order ON sa_order.ordh_id = dbo.sa_order_revision_header.ordh_id \
                         WHERE (dbo.sa_order_revd_item.org_id = ?) AND (dbo.sa_order_revd_item.item_id = ?) AND (sa_order_revd_item.ordrevditem_reserved_qty > 0) AND (sa_order.ordh_document_type = 'ORD') AND (dbo.sa_order_revision_header.ordrevh_revision = 0)\
                         ";
    oSQL.sql += " AND dbo.sa_order_revision_detail.whse_id = '" + whseID + "'";
    oSQL.sql += " AND dbo.sa_order_revd_item.ordrevd_id = '" + ordrevdID + "'";
    
    oSQL.args = [globals.org_id, itemID.toString()];
    oSQL.table = "sa_order_revd_item";
    oSQL.server = globals.avBase_dbase_avanti;
    fsReserved = globals["avUtilities_sqlFoundset"](oSQL);
	
    for ( var i = 1; i <= fsReserved.getSize(); i++ ) {
    	var rReserved = fsReserved.getRecord(i);
    	iReserved += rReserved.ordrevditem_reservedqty_uomstk;
    }
	
    return iReserved;
}


/**
 * @properties={typeid:24,uuid:"F4AAF3F7-7321-4B1C-9050-0EE4F4B341B9"}
 * @param {UUID} itemID
 * @param {UUID} whseID
 * @param {UUID} ordrevdID
 *
 * @returns {Number} Total Quantity Reserved for selected job
 */
function getQtyReservedAgainstOtherJobs(itemID, whseID, ordrevdID) {
	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL ={};
	
	 /*** @type {JSFoundSet<db:/avanti/sa_order_revd_item>} */
    var fsReserved;
    
    var iReserved = 0.00;

    oSQL.sql = "SELECT dbo.sa_order_revd_item.ordrevditem_id\
    			FROM dbo.sa_order_revd_item INNER JOIN\
                         dbo.sa_order_revision_detail ON dbo.sa_order_revd_item.ordrevd_id = dbo.sa_order_revision_detail.ordrevd_id INNER JOIN\
                         dbo.sa_order_revision_header ON dbo.sa_order_revision_detail.ordrevh_id = dbo.sa_order_revision_header.ordrevh_id INNER JOIN\
                         dbo.sa_order ON sa_order.ordh_id = dbo.sa_order_revision_header.ordh_id \
                         WHERE (dbo.sa_order_revd_item.org_id = ?) AND (dbo.sa_order_revd_item.item_id = ?) AND (sa_order_revd_item.ordrevditem_reserved_qty > 0) AND (sa_order.ordh_document_type = 'ORD') AND (dbo.sa_order_revision_header.ordrevh_revision = 0)\
                         ";
    oSQL.sql += " AND dbo.sa_order_revision_detail.whse_id = '" + whseID + "'";
    oSQL.sql += " AND dbo.sa_order_revd_item.ordrevd_id != '" + ordrevdID + "'";
    
    oSQL.args = [globals.org_id, itemID.toString()];
    oSQL.table = "sa_order_revd_item";
    oSQL.server = globals.avBase_dbase_avanti;
    fsReserved = globals["avUtilities_sqlFoundset"](oSQL);
	
    for ( var i = 1; i <= fsReserved.getSize(); i++ ) {
    	var rReserved = fsReserved.getRecord(i);
    	iReserved += rReserved.ordrevditem_reservedqty_uomstk;
    }
	
    return iReserved;
}

/**
 * @properties={typeid:24,uuid:"04003DE7-1654-45B4-8519-E20E09C52226"}
 * @param {JSRecord<db:/avanti/prod_job_cost_material_entry>} _rEntry
 */
function updateTotQtyUsed(_rEntry){
	_rEntry.jcme_clc_total_qty_used = scopes.avDB.getSum('prod_job_cost_material', 'jcm_qty_used', ['job_id','prod_job_cost_material_to_prod_job_cost.ordrevds_id','item_id'], [_rEntry.job_id, _rEntry.ordrevds_id, _rEntry.item_id])
}

/**
 * @return
 * @properties={typeid:24,uuid:"9B748FE9-463E-47F9-8F93-C291846810A5"}
 */
function getJobCostIDFromMSGRPID(employeeId,selected_job_id, selected_ordrevds_id, selected_msgrp_id) {
	//from sf_main_dtl_material_main
	
    /***@type {JSFoundset<db:/avanti/prod_job_cost>} */
    var fsJobCost = databaseManager.getFoundSet(globals.avBase_dbase_avanti, "prod_job_cost");

    fsJobCost.newRecord();
    fsJobCost.job_id = selected_job_id;
    fsJobCost.ordrevds_id = selected_ordrevds_id;
    fsJobCost.created_date = application.getTimeStamp();
    fsJobCost.created_by_id = employeeId;
    fsJobCost.empl_id = employeeId;
    fsJobCost.jc_cost_type = scopes.avUtils.JOB_COST_TYPE.Material;
    fsJobCost.msgrp_id = selected_msgrp_id;

    databaseManager.saveData(fsJobCost);

    // need to create a prod_job_cost_transactions rec too
    if (!utils.hasRecords(fsJobCost.prod_job_cost_to_prod_job_cost_transactions)) {
        var new_cost_trans_rec = fsJobCost.prod_job_cost_to_prod_job_cost_transactions.getRecord(fsJobCost.prod_job_cost_to_prod_job_cost_transactions.newRecord());
        new_cost_trans_rec.jct_transaction_type = 'WIP';
        databaseManager.saveData(new_cost_trans_rec);
    }

    return fsJobCost.jc_id;
}
