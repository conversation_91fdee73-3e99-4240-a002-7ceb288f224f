/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"BECF98F5-5836-4082-81A8-2EDC35DE555E",variableType:-4}
 */
var aLateMilestones = new Array();

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"D452763A-8C90-4F4A-A6AC-5E4DB0EFF529",variableType:-4}
 */
var aLateJobs = new Array();

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"EABDCC7A-5487-42AB-A354-7A407134692A",variableType:-4}
 */
var bOptimizationCancelled = false;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"ABBAD5B5-A027-4A3F-B9A5-B38FDB3B36F4",variableType:93}
 */
var dOptimizeFrom = application.getTimeStamp();

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"59EF0C9F-6739-4CFD-8FEA-0B3F1567B337",variableType:93}
 */
var dScheduleFrom = application.getTimeStamp();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"17A5C94F-000B-4B11-B5B1-523206DF2764",variableType:4}
 */
var nJobCount = 0;

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"7EE78CE0-5AAE-4EE9-8439-EEF95E9E9DD6"}
 */
function btnCancel(event) {
    var sOptimizeSessionID = _to_sys_organization.org_sch_optimize_session_id;
    var nOptimizeInProgress = _to_sys_organization.org_sch_optimize_in_progress;
    
    bOptimizationCancelled = true;
    
    scopes.avScheduling.clearOrgScheduleOptimizeDetails();    
    
    if (nOptimizeInProgress == 1) {
        _to_sys_organization.org_sch_optimize_session_id = sOptimizeSessionID;
        _to_sys_organization.org_sch_optimize_cancel_req = 1;
        _to_sys_organization.org_sch_optimize_cancl_empuuid = null;
        databaseManager.saveData(_to_sys_organization);            
    }
    else {
        globals.DIALOGS.closeForm(event, "dlgOptimizeSchedule");
    }
    
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B0699AEF-89D6-42F5-95EA-8035EF3C9166"}
 */
function btnOK(event) {

    if (_to_sys_organization.org_sch_locked == 1) {
        scopes.avScheduling.showScheduleBoardLockedMessage();
    }
    else if (dScheduleFrom) {        
        var bFormClosed = false;
        
        // Save the current work
        databaseManager.saveData();
        forms.sch_agenda_dtl.saveAndDeleteTempRecords();
        
        var dOptimizationStart = application.getTimeStamp();
        var uOptimize_session_id = application.getUUID();
        
        forms.sch_agenda_dtl.dOptimizeClearFromDate = new Date(dOptimizeFrom);        
                
        scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);
        scopes.avScheduling.updateOrgScheduleOptimizeDetails(1, _to_sys_employee$avbase_employeeuuid.empl_id, application.getTimeStamp(), 1, null, uOptimize_session_id, 0, null, true);

        if (forms.sch_agenda_dtl.backupScheduleBoard(_to_sys_organization.org_sch_optimize_session_id, forms.sch_agenda_dtl.dOptimizeClearFromDate)) {
            if (forms.sch_agenda_dtl.clearScheduleBoard(_to_sys_organization.org_sch_optimize_session_id, forms.sch_agenda_dtl.dOptimizeClearFromDate)) {
                globals.bOptimizedSchedule = true;
                
                // sl-15019 - removed the call to optimizeSchedule_ByStoredProcedure() if ScheduleEmps off.
                // It wasnt working, all optimized milestones were disappearing from the schedule board.
                // we will have to create a ticket to investigate (15019 was about optimizeSchedule() not using
                // schedule ms from date). For now it will just use optimizeSchedule(), which works.
				if (optimizeSchedule()) {
					scopes.avScheduling.clearOrgScheduleOptimizeDetails();
					
					_to_sys_organization.org_sch_optimize_job_count = nJobCount;
	                _to_sys_organization.org_sch_optimize_strt_datetime = dOptimizationStart;                
	                _to_sys_organization.org_sch_optimize_cmpl_datetime = application.getTimeStamp();
	                _to_sys_organization.org_sch_optimize_in_progress = 0;
	                
	                databaseManager.saveData(_to_sys_organization);

	                scopes.avScheduling.notifyActiveSchedulers();
	                
	                if (globals.nav.mode == 'edit' && bOptimizationCancelled == false) {
	                    globals.DIALOGS.closeForm(event, "dlgOptimizeSchedule");
	                    bFormClosed = true;
	                    
	                    forms.sch_agenda_dtl.loadView();
	                    
	                    forms.sch_schedule_board_late_milestones_tbl.foundset.loadAllRecords();
	                    globals.DIALOGS.showFormInModalDialog(forms.sch_schedule_board_late_jobs_dlg, -1, -1, 1000, 690, i18n.getI18NMessage("avanti.dialog.lateJobs"), true, false, "dlgLateJobs", true);
	                } 
				} 
				else {
	                forms.sch_agenda_dtl.revertOptimizedSchedule(_to_sys_organization.org_sch_optimize_session_id);
	                forms.sch_agenda_dtl.cleanupScheduleBoardBackup(_to_sys_organization.org_sch_optimize_session_id);
	                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);

	                scopes.avText.showWarning("errorRunningOptimizeSchedule");
				}
                
            }
            else {
                // Issue with clearing schedule board, clear up tables that were backed up and unlock the board
                forms.sch_agenda_dtl.revertOptimizedSchedule(_to_sys_organization.org_sch_optimize_session_id);
                forms.sch_agenda_dtl.cleanupScheduleBoardBackup(_to_sys_organization.org_sch_optimize_session_id);
                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);

                plugins.dialogs.showErrorDialog('Error', 'Unable to clear the schedule board.', 'Ok');
            }
        }
        else {
            // Issue with backup, clear up tables that were backed up and unlock the board
            forms.sch_agenda_dtl.cleanupScheduleBoardBackup(_to_sys_organization.org_sch_optimize_session_id);
            scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);
        }
        
        if (bFormClosed == false) {
            globals.DIALOGS.closeForm(event, "dlgOptimizeSchedule");
            forms.sch_agenda_dtl.loadView();
        }
        
    }
}

/**
 * SL-14513
 * Optimizes the schedule for equipment only via a Stored Procedure *  
 *
 * @properties={typeid:24,uuid:"2454B393-D82A-4A0F-8706-2AD29BFD153D"}
 */
function optimizeSchedule_ByStoredProcedure() {
    /** @type{JSFoundSet<db:/avanti/sch_schedule>} */
    var fsSchedule = forms.sch_schedule_board_optimize_schedule_jobs_tbl.foundset;
    
    var dScheduleFromDate = new Date(forms.sch_schedule_board_optimize_schedule_dlg.dScheduleFrom);
 
    if (utils.hasRecords(fsSchedule)) {
        var tStartTime_Perf = new Date().getTime(); 
        
        bOptimizationCancelled = false;
        var sJobsToSchedule = '';
        
        for (var i = 1; i <= fsSchedule.getSize(); i++) {
            // A check to see if a request to cancel the optimization
            // alert the current user who the cancel is from then exit loop
            // The data will be reverted and schedule board will be unlocked when the other user clicks Revert and Unlock in dialog
            if (_to_sys_organization.org_sch_optimize_cancel_req == 1) {
                bOptimizationCancelled = true;

                break;
            }

            var rSchedule = fsSchedule.getRecord(i);
            if (utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone$notcompleted) && utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone$not_locked)) {                                
                sJobsToSchedule += rSchedule.sch_id + ','; 
            }          
        }
        
        elements.lblOpimizationProgress.text = 'Optimizing ' + nJobCount + ' jobs';
        globals.updateUI();
        
        // Call stored procedure
        // A dataset is returned, when no output-parameters defined, the last select-result in the procedure will be returned. 
        // When one or more output-parameters are defined, the dataset will contain 1 row with the output data.
        var maxReturnedRows = -1; //useful to limit number of rows 
        var procedure_declaration = '{call sp_ScheduleJobs(?,?,?,?,?,?)}'; 
        
        // define the direction, a 0 for input data, a 1 for output data 
        var typesArray = [0, 0, 0, 0, 0, 0]; 
        var sSessionID = application.getUUID().toString();             
        
        // define the types and values, a value for input data, a sql-type for output data 
        var args = [sSessionID.toString(), globals.org_id.toString(), globals.avBase_employeeUUID.toString(), dScheduleFromDate, sJobsToSchedule, 0];     
        
        var tStartTime = new Date().getTime();
        plugins.rawSQL.executeStoredProcedure(databaseManager.getDataSourceServerName(controller.getDataSource()), procedure_declaration, args, typesArray, maxReturnedRows);            
        scopes.avUtils.logDebugExecutionTime('optimizeSchdule - executeStoredProcedure', tStartTime);    
            
        tStartTime = new Date().getTime();                
        scopes.avUtils.broadcastDataChanges(sSessionID);
        
        scopes.avUtils.logDebugExecutionTime('optimizeSchdule - BroadcastDataChanges', tStartTime);        
        scopes.avUtils.logDebugExecutionTime('optimizeSchedule - Performance Stored Procedure - Jobs: ' + nJobCount, tStartTime_Perf);        
        
        // we have to get the jobs and the milestones which are late after the optimization has been completed.
        var sSQL = "SELECT DISTINCT m.sch_id \
                    FROM sch_milestone m \
                        INNER JOIN (SELECT sch_id, job_id \
                                    FROM sch_schedule sch \
                                    WHERE org_id = ? \
                                        AND EXISTS (SELECT NULL \
                                                    FROM dbo.fn_SplitStrings(?,',') schid \
                                                    WHERE schid.Item = sch.sch_id)) s ON m.sch_id = s.sch_id \
                        INNER JOIN prod_job pj ON s.job_id = pj.job_id \
                        INNER JOIN sa_order so ON pj.ordh_id = so.ordh_id \
                    WHERE m.org_id = ? \
                        AND pj.org_id = ? AND so.org_id = ? \
                        AND m.ms_date_due > so.ordh_exp_ship_date \
                        AND EXISTS (SELECT NULL \
                                    FROM sys_department dept \
                                    WHERE dept.dept_schedule_flag = 1 \
                                        AND dept.dept_id = m.dept_id \
                                        AND dept.org_id = ?)";
        
        var aArgs = [globals.org_id.toString(), sJobsToSchedule, globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString()];
        var dsLateJobs = databaseManager.getDataSetByQuery(scopes.globals.avBase_dbase_avanti, sSQL, aArgs, -1);
        
        if (dsLateJobs && dsLateJobs.getMaxRowIndex() > 0)
        {
            aLateJobs = dsLateJobs.getColumnAsArray(1);
        }
        
        sSQL = "SELECT m.ms_id \
                FROM sch_milestone m \
                    INNER JOIN (SELECT sch_id, job_id \
                                    FROM sch_schedule sch \
                                    WHERE org_id = ? \
                                        AND EXISTS (SELECT NULL \
                                                    FROM dbo.fn_SplitStrings(?,',') schid \
                                                    WHERE schid.Item = sch.sch_id)) s ON m.sch_id = s.sch_id \
                WHERE m.org_id = ? \
                    AND m.ms_date_due_bak > m.ms_date_scheduled \
                    AND EXISTS (SELECT NULL \
                                FROM sys_department dept \
                                WHERE dept.dept_schedule_flag = 1 \
                                    AND dept.dept_id = m.dept_id \
                                    AND dept.org_id = ?)";
        
        aArgs = [globals.org_id.toString(), sJobsToSchedule, globals.org_id.toString(), globals.org_id.toString()];
        var dsLateMilestones = databaseManager.getDataSetByQuery(scopes.globals.avBase_dbase_avanti, sSQL, aArgs, -1);
        
        if (dsLateMilestones && dsLateMilestones.getMaxRowIndex() > 0)
        {
            aLateMilestones = dsLateMilestones.getColumnAsArray(1);
        }
                
        if (!bOptimizationCancelled) {
            scopes.avScheduling.notifyActiveSchedulers();
        }
        else {

            // The revert also happens when another user cancels the optimization but because the optimization may still be in progress
            // and data being saved we have to revert again to ensure data is as it was prior to optimization.
            // The clearing of the backup tables will is done each time optimization begins
            forms['sch_agenda_dtl'].revertOptimizedSchedule(_to_sys_organization.org_sch_optimize_session_id);

            // If org_sch_optimize_cancl_empuuid is nothing then it was cancelled via Cancel button
            if (_to_sys_organization.org_sch_optimize_cancl_empuuid) {
                // Get the employee that locked the schedule board
                sSQL = "SELECT empl_full_name \
                        FROM sys_employee \
                        WHERE org_id = ? \
                            AND empl_id = ?";
                aArgs = [globals.org_id.toString(), _to_sys_organization.org_sch_optimize_cancl_empuuid];
                var sEmployeeFullName = scopes.avDB.SQLQuery(sSQL, false, aArgs);

                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);	                

                var sMessage = '';

                if (sEmployeeFullName && sEmployeeFullName.length > 0) {
                    sMessage = sEmployeeFullName;
                }
                else {
                    // In case no name is returned.
                    sMessage = 'someone with priviliges to override the Schedule Board lock during optimization';
                }

                globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_optimize_cancelled', [sMessage]), i18n.getI18NMessage('avanti.dialog.ok'));
            }
            else {
                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);	                
            }

        }
    }
}

/**
 * Optimizes the Schedule
 *
 * @return {Boolean} - false if an error occurs, otherwise true
 *
 * @properties={typeid:24,uuid:"CD5F306A-69FE-430A-9E59-12F46D2B9E0D"}
 * @AllowToRunInFind
 */
function optimizeSchedule() {
	scopes.avUtils.stopWatchStart("optimizeSchedule");
	
	try {
		/** @type{JSFoundSet<db:/avanti/sch_schedule>} */
	    var fsSchedule = forms.sch_schedule_board_optimize_schedule_jobs_tbl.foundset;
	    var rMilestone;
	    var rPredMilestone;
	    var dScheduleFromDate = new Date(forms.sch_schedule_board_optimize_schedule_dlg.dScheduleFrom);
	
	    if (utils.hasRecords(fsSchedule)) {
	        
	        bOptimizationCancelled = false;
	
	        for (var i = 1; i <= fsSchedule.getSize(); i++) {
	
	            // A check to see if a request to cancel the optimization
	            // alert the current user who the cancel is from then exit loop
	            // The data will be reverted and schedule board will be unlocked when the other user clicks Revert and Unlock in dialog
	            if (_to_sys_organization.org_sch_optimize_cancel_req == 1) {
	                bOptimizationCancelled = true;
	
	                break;
	            }
	            
	            var rSchedule = fsSchedule.getRecord(i);
	
	            if (utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone$notcompleted) && utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone$not_locked)) {                                
	                var fsMilestones = rSchedule.sch_schedule_to_sch_milestone$notcompleted;
	                var dExpectShipDate = rSchedule.sch_schedule_to_prod_job.prod_job_to_sa_order.sa_order_to_sa_order_revision_header$first_rev.ordrevh_expected_date;
	                var dMaxDueDate = null;
	                
	                fsMilestones.sort("sequence_nr asc");
	
	                elements.lblOpimizationProgress.text = 'Optimizing ' + i + ' of ' + nJobCount + ' jobs';
	
	                // this hangs when stepping thru code
					if (!application.isInDeveloper()) {
						globals.updateUI();
					}
	
	                for (var j = 1; j <= fsMilestones.getSize(); j++) {
	                    rMilestone = fsMilestones.getRecord(j);
	                    
						if (utils.hasRecords(rMilestone.sch_milestone_to_sys_department) && rMilestone.sch_milestone_to_sys_department.dept_schedule_flag) {
							processMilestone();
		                    
							if (rMilestone.ms_date_due > dMaxDueDate) {
								dMaxDueDate = rMilestone.ms_date_due;
							}
						}
	                }
	
	                databaseManager.saveData(fsMilestones);
	
	                if (dMaxDueDate > dExpectShipDate) {
	                    aLateJobs.push(rSchedule.job_id);
	                }
	            }
	
	            updateAllResourceSchedules(rSchedule);
	        }
	
	        if (!bOptimizationCancelled) {
	            databaseManager.saveData(fsSchedule);
	            scopes.avScheduling.notifyActiveSchedulers();
	        }
	        else {
	
	            // The revert also happens when another user cancels the optimization but because the optimization may still be in progress
	            // and data being saved we have to revert again to ensure data is as it was prior to optimization.
	            // The clearing of the backup tables will is done each time optimization begins
	            forms['sch_agenda_dtl'].revertOptimizedSchedule(_to_sys_organization.org_sch_optimize_session_id);
	
	            // If org_sch_optimize_cancl_empuuid is nothing then it was cancelled via Cancel button
	            if (_to_sys_organization.org_sch_optimize_cancl_empuuid) {
	                // Get the employee that locked the schedule board
	                var sSQL = "SELECT empl_full_name \
	                            FROM sys_employee \
	                            WHERE org_id = ? \
	                                AND empl_id = ?";
	                var aArgs = [globals.org_id.toString(), _to_sys_organization.org_sch_optimize_cancl_empuuid];
	                var sEmployeeFullName = scopes.avDB.SQLQuery(sSQL, false, aArgs);
	
	                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);	                
	
	                var sMessage = '';
	
	                if (sEmployeeFullName && sEmployeeFullName.length > 0) {
	                    sMessage = sEmployeeFullName;
	                }
	                else {
	                    // In case no name is returned.
	                    sMessage = 'someone with priviliges to override the Schedule Board lock during optimization';
	                }
	
	                globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_optimize_cancelled', [sMessage]), i18n.getI18NMessage('avanti.dialog.ok'));
	            }
	            else {
	                scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);	                
	            }
	
	        }
	
	    }

		scopes.avUtils.devOutputTime("optimizeSchedule");
 	    
	    return true;
	}
	catch (ex) {
		var sMSG = "An error has occurred in optimizeSchedule: ";
		var sCallStack = null;
		
		if (ex) {
			if (ex.message) {
				sMSG += ex.message;
			}
			if (ex.stack) {
				sCallStack = ex.stack; 
			}
		}
		
		
		scopes.avUtils.devLog("SL-22417", sMSG, null, null, null, sCallStack, null, true);
		
		return false;
	}

    function processMilestone() {

        // Check for ms_date_scheduled, if it is null means it hasn't been processed by LateMilestones
        if (utils.hasRecords(rMilestone.sch_milestone_to_sys_department)
        		&& scopes.avScheduling.isMilestoneSchedulable(rMilestone)
				&& rMilestone.ms_time_budget > 0
				&& !isMilestoneInProgress(rMilestone) 
                && rMilestone.ms_flg_locked != 1) {

            var dStart = dScheduleFromDate;
            var dDue = getBackedUpMSDueDate(rMilestone.ms_id);
            var rSch;
            
            rPredMilestone = scopes.avScheduling.getPredecessor(rMilestone);

            if (rPredMilestone) {
				var dMaxDateWithLag = scopes.avScheduling.getMilestoneMaxDateWithLag(rPredMilestone);
				
				if (dMaxDateWithLag) {
					dStart = dMaxDateWithLag;

					if (dStart < dScheduleFromDate) {
						dStart = dScheduleFromDate;
					}
				}
            }

            rMilestone.ms_date_scheduled = dStart;
            if (rMilestone.ms_date_scheduled && rMilestone.ms_time_budget) {
                rMilestone.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
            }
            else {
                rMilestone.ms_date_due = rMilestone.ms_date_scheduled;
            }

            var dNextAvailable = scopes.avScheduling.getNextFreeSpot(rMilestone, 'F');

            if (dNextAvailable) {
                if (dNextAvailable > dDue) {
                    aLateMilestones.push(rMilestone.ms_id);
                }

                if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
                    rSch = rMilestone.sch_milestone_to_sch_equip_schedule.getRecord(1);
                }

                if (rSch) {
                    rSch.equip_id = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.equip_id;
                    rSch.equipsch_start = rMilestone.ms_date_scheduled;
                    rSch.equipsch_end = rMilestone.ms_date_due;
                    databaseManager.saveData(rSch);
                }

                databaseManager.saveData(rMilestone);
            }
            else {
            	scopes.avUtils.devLog("SL-22417", "optimizeSchedule: couldnt find a freespot for ms: " + rMilestone.ms_id + ". Dates remain unchanged.")
            }
        }
    }
}

/**
 * This returns the due date for the milestone from the sch_milestone_optimize, where it is backed up before running optimization
 * 
 * @private 
 * 
 * @param {UUID} uMSID
 * 
 * @return {Date} 
 *
 * @properties={typeid:24,uuid:"233EFBEF-9400-4D62-B92D-1A38EB8F8B71"}
 */
function getBackedUpMSDueDate(uMSID) {
	var dDueDate = null;
	
	if (uMSID) {
		var sSQL = "SELECT ms_date_due \
					FROM sch_milestone_optimize \
					WHERE \
						org_id = ? \
						AND ms_id = ?";
		var aArgs = [globals.org_id, uMSID.toString()];
		
		dDueDate = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	
	return dDueDate;
}

/**
 *
 * @param {JSFoundSet<db:/avanti/sch_milestone>} fsMilestones
 * @param {Date} dClearFromDate
 * @param {Date} dScheduleFromDate
 *
 * @properties={typeid:24,uuid:"E3076D47-F4F6-4C7B-BF2D-65EA099B5C76"}
 */
function optimizeLateMilestones(fsMilestones, dClearFromDate, dScheduleFromDate) {
    var dCurDate = application.getTimeStamp();
    var rPredMilestone;
    var dStart;
    var rSch;

    if (fsMilestones) {        
        fsMilestones.sort("sequence_nr asc");
        
        for (var i = 1; i <= fsMilestones.getSize(); i++) {
            var rMilestone = fsMilestones.getRecord(i);
                
            //If the milestone isn't already optimized by the employee in the current edit session, then optimize the milestone
            if (rMilestone.ms_date_due < dCurDate && !isMilestoneInProgress(rMilestone) && rMilestone.dept_id 
                    && utils.hasRecords(rMilestone.sch_milestone_to_sys_department)
                    && rMilestone.sch_milestone_to_sys_department.dept_schedule_flag 
                    && rMilestone.ms_flg_locked != 1 && !rMilestone.ms_date_scheduled) {
                
                dStart = dScheduleFromDate;
                rPredMilestone = scopes.avScheduling.getPredecessor(rMilestone);

                if (rPredMilestone) {
    				var dMaxDateWithLag = scopes.avScheduling.getMilestoneMaxDateWithLag(rPredMilestone);
    				
					if (dMaxDateWithLag) {
						dStart = dMaxDateWithLag;

						if (dStart < dScheduleFromDate) {
							dStart = dScheduleFromDate;
						}
					}
                }

                if (!dStart) {
                    continue;
                }

                rMilestone.ms_date_scheduled = dStart;
                if (rMilestone.ms_date_scheduled && rMilestone.ms_time_budget) {
                    rMilestone.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
                }
                else {
                    rMilestone.ms_date_due = rMilestone.ms_date_scheduled;
                }

                var dDue = rMilestone.ms_date_due;
                rSch = null;
                
                /**@type {Date} */
                var dNextAvailable = scopes.avScheduling.getNextFreeSpot(rMilestone, 'F');

                if (dNextAvailable && ( !rPredMilestone || dNextAvailable >= rPredMilestone.ms_date_due )) {
                    if (dNextAvailable > dDue) {
                        aLateMilestones.push(rMilestone.ms_id);
                    }

                    rMilestone.ms_date_scheduled = dNextAvailable;

                    //Setting the due date correctly if the milestone lasts for more than one shift and if the milestone has to be scheduled at a different time due
                    //to resource not being available
                    rMilestone.ms_date_due = globals.addMinutes(dNextAvailable, rMilestone.ms_time_budget);

                    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
                        rSch = rMilestone.sch_milestone_to_sch_equip_schedule.getRecord(1);
                    }

                    if (rSch) {
                        rSch.equip_id = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.equip_id;
                        rSch.equipsch_start = rMilestone.ms_date_scheduled;
                        rSch.equipsch_end = rMilestone.ms_date_due;
                        databaseManager.saveData(rSch);
                    }
                    
                    databaseManager.saveData(rMilestone);

                }
            }
                        
        }
    }
}

/**
 * Filter the milestones in the schedule based on the department and operation category
 * available to the current employee
 * @param {JSFoundSet<db:/avanti/sch_milestone>} fsMilestones
 *
 * @properties={typeid:24,uuid:"4C4E0EB5-CD3E-47AD-B1FE-2362A002322D"}
 * @AllowToRunInFind
 */
function filterMilestonesBasedOnDept(fsMilestones) {
    if (fsMilestones && utils.hasRecords(fsMilestones) && fsMilestones.find()) {
        /** @type{JSFoundSet<db:/avanti/sys_empl_dept>} */
        var fsEmpDept = scopes.avDB.getFS('sys_empl_dept', ['empl_id'], [globals.avBase_employeeUUID]);

        for (var i = 1; i <= fsEmpDept.getSize(); i++) {
            var rDept = fsEmpDept.getRecord(i);

            if (i > 1) {
                fsMilestones.newRecord();
            }

            if (rDept.opcat_id) {
                fsMilestones.opcat_id = rDept.opcat_id;
            }
            else {
                fsMilestones.dept_id = rDept.dept_id;
            }
        }
        fsMilestones.search(true);
    }
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"3EF5B1E2-4FCA-4B5B-9C7A-1BB90AC52552"}
 */
function onShowForm(firstShow, event) {
    elements.lblOpimizationProgress.text = '';    
    
    dOptimizeFrom = application.getTimeStamp();
    dScheduleFrom = application.getTimeStamp();
        
    forms.sch_schedule_board_zone_department.foundset.loadAllRecords();
    filterScheduledJobs();
    _super.onShowForm(firstShow,event);
    
    // Setup the split
    aLateJobs = new Array();
    aLateMilestones = new Array();
    
    scopes.globals.avSchedule_selectedEmplUUID = globals.avBase_employeeUUID;
       
    // Don't want user to be able to delete depts from here.
    forms.sch_schedule_board_zone_department.elements.btnLookup.visible = false;
    forms.sch_schedule_board_zone_department.elements.btnLookupc.visible = false;

    return _super.onShowForm(firstShow, event)
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D69D4A9D-26A2-4578-8EBC-43F75B2636DD"}
 */
function onDataChange_ReferenceDate(oldValue, newValue, event) {
    if(newValue){
        dOptimizeFrom = new Date(newValue);
        filterScheduledJobs();
    }
    
    return true;
}

/**
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"7B5ADFF1-1736-4392-9AD8-7B5D9FB1775E"}
 */
function onDataChange_ScheduleFrom(oldValue, newValue, event) {
    if(newValue){
        dScheduleFrom = new Date(newValue);
    }
    
    return true;
}

/**
 * @properties={typeid:24,uuid:"52EAB871-F2C8-4B33-8070-D0FA9C98E84A"}
 */
function filterScheduledJobs() {
    
    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();
        
    // Get the number of jobs that will be optimized        
    oSQL.sql = "SELECT sch_id \
                FROM sch_schedule s \
                WHERE jobstat_id = 'Scheduled' \
                    AND EXISTS (SELECT NULL \
                                FROM sch_milestone m \
                                WHERE m.org_id = ? \
                                    AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                    AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                    AND m.ms_date_scheduled >= ? \
                                    AND m.sch_id = s.sch_id) \
                    AND s.org_id = ?";
    
    oSQL.args = [globals.org_id, scopes.avUtils.formatDateTime(dOptimizeFrom, true), globals.org_id];
    oSQL.table = 'sch_schedule';
        
    /** @type {JSDataSet} */
    var fsData = globals.avUtilities_sqlDataset(oSQL);
    nJobCount = fsData.getMaxRowIndex();
    application.output('nJobCount: ' + nJobCount);
    
    var fsSchedule = forms.sch_schedule_board_optimize_schedule_jobs_tbl.foundset;
    fsSchedule.loadRecords(fsData);       
    fsSchedule.sort("schedule_sequence asc");
    
    if (nJobCount == 0) {
        // Nothing to optimize disable button
        elements.btn_optimize.enabled = false;
    }
    else {
        elements.btn_optimize.enabled = true;
    }
        
}
