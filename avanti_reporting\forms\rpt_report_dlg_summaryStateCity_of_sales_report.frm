borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"325,-1,-1,550,26,22",
formIndex:28,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"550",
right:"-1",
top:"325",
width:"26"
},
enabled:true,
formIndex:28,
labelFor:"toDep",
styleClass:"text-text-center label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:false
},
name:"toDep_lbl",
styleClass:"text-text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"03484592-923C-4538-99DD-7B1E02C4A8A4",
visible:false
},
{
cssPosition:"182,-1,-1,11,144,20",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"11",
right:"-1",
top:"182",
width:"144"
},
enabled:true,
formIndex:22,
labelFor:"includeShipping",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.IncludeShipping",
visible:true
},
name:"includeIncludeShipping_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0FB9192F-D4C9-4C69-9038-F0FC5B5562B9"
},
{
cssPosition:"149,-1,-1,406,112,22",
formIndex:24,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"406",
right:"-1",
top:"149",
width:"112"
},
enabled:true,
formIndex:24,
labelFor:"toDate",
styleClass:"text-text-center label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toInvoiceDate",
visible:true
},
name:"toDate_label",
styleClass:"text-text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2B7755FC-E9E9-4BA8-B3B8-E260C0310A50"
},
{
cssPosition:"182,-1,-1,155,91,68",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"68",
left:"155",
right:"-1",
top:"182",
width:"91"
},
dataProviderID:"includeShipping",
enabled:true,
formIndex:26,
inputType:"radio",
styleClass:"choicegroup_bts vertical",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"includeShipping",
styleClass:"choicegroup_bts vertical",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"3B7C790C-27B5-4B81-966F-AB6EC2BC9483"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:347,
typeid:19,
uuid:"58F58402-44AD-4F97-B95A-B2DCE7316418"
},
{
cssPosition:"30,-1,-1,5,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"30",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"60BAF84B-DBC6-4893-B2A3-B349091CD77E"
},
{
cssPosition:"30,-1,-1,420,92,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"30",
width:"92"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"662C5B7D-2594-46FF-B54D-57687628919B"
},
{
cssPosition:"325,-1,-1,150,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"325",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDep",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromDept",
visible:false
},
name:"fromDep_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7389464B-5CAA-4A5E-9F1E-F3AF8C9507BC",
visible:false
},
{
cssPosition:"149,-1,-1,11,136,22",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"149",
width:"136"
},
enabled:true,
formIndex:22,
labelFor:"fromDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromInvoiceDate",
visible:true
},
name:"fromDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"84D87C31-9EDD-4967-8001-6C3A5F836290"
},
{
cssPosition:"149,-1,-1,517,244,22",
formIndex:25,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"517",
right:"-1",
top:"149",
width:"244"
},
dataProviderID:"toDate",
enabled:true,
formIndex:25,
onDataChangeMethodID:"DFEA7D6D-C5AD-42D8-BA21-D9F473DD458F",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"toDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"93324C55-898F-4067-9D18-F7EFB70D016B"
},
{
cssPosition:"27,-1,-1,517,244,112",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"517",
right:"-1",
top:"27",
width:"244"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"96BDDF84-F98D-4EEC-BA50-CE87AFCDA6A8"
},
{
height:200,
partType:5,
typeid:19,
uuid:"9CEB99D7-59F2-4DAF-AE0D-31CDD7627294"
},
{
cssPosition:"149,-1,-1,156,248,22",
formIndex:23,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"156",
right:"-1",
top:"149",
width:"248"
},
dataProviderID:"fromDate",
enabled:true,
formIndex:23,
onDataChangeMethodID:"6D0E01FE-6A87-4937-91FA-0EAEBBEC248B",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"fromDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"9F8465C8-A7C5-4DC2-815A-5EDDCC749FC9"
},
{
cssPosition:"325,-1,-1,580,250,22",
formIndex:29,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"580",
right:"-1",
top:"325",
width:"250"
},
dataProviderID:"toDep",
enabled:true,
formIndex:29,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"505481F3-428E-45A6-95B4-DC9E24CDC657",
visible:false
},
name:"toDep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A1BD9C5E-0D85-4B45-AD78-1E07673E7C5C",
visible:false
},
{
cssPosition:"325,-1,-1,295,250,22",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"295",
right:"-1",
top:"325",
width:"250"
},
dataProviderID:"fromDep",
editable:true,
enabled:true,
formIndex:27,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"505481F3-428E-45A6-95B4-DC9E24CDC657",
visible:false
},
name:"fromDep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"B10A805E-FB77-4AE6-8CEE-12AB536FE057",
visible:false
},
{
cssPosition:"30,-1,-1,155,250,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"155",
right:"-1",
top:"30",
width:"250"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"19D48E3B-9CE9-4AA0-8263-050843FC07A8",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"FB35C186-2A13-4899-85ED-C6C03AD16315"
}
],
name:"rpt_report_dlg_summaryStateCity_of_sales_report",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"7CE375AF-866D-4526-8FC3-91FEDC727E09"