/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B6C5A6BD-DBC7-4487-A424-CB9AF763CBEB",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"4EE2709F-C05F-401E-BD6B-2CFE1AD9E481"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"43FF4F8C-BA83-4D9E-A18C-ED2461A4B885"}
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
}
/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"AB8486A5-5241-4F53-8F4B-BC2671B9C80B"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnSelect" && col.styleClass && col.styleClass.search(' disabled') == -1) {
		btnSelect(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"955F8244-71F6-4826-BD33-655C5293CB81"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	btnSelect(event)
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"E947A913-1334-4716-8550-90F0D2491410"}
 * @AllowToRunInFind
 */
function onRecordSelection(event) {
	globals.avRpt_selectedProgram = program_name;

	var userID = scopes.globals.svy_sec_user_id;
	var orgID = scopes.globals.svy_sec_lgn_user_org_id;
	var resultDS = databaseManager.createEmptyDataSet();
	var fs = _to_rpt_report$avrpt_selectedprogram_ignorepks;
	var i = 0;
	var groups = [];
	
	for (var j = 1; j <= sec_user_in_group_to_sec_user_in_group.getSize(); j++) {
		var group = sec_user_in_group_to_sec_user_in_group.getRecord(j);
		if (group.group_id != null){
			groups.push(group.group_id);
		}
	}
	
	// lop thru reports in this program
	for (var index = 1; index <= fs.getSize(); index++) {
		var record = fs.getRecord(index);
		var sRptSecKeyID = scopes.avDB.getVal('sec_security_key', ['rpt_id'], [record.rpt_id.toString()], 'security_key_id', null, null, globals.avBase_dbase_framework);
		
		if(sRptSecKeyID){
			var bRptEnabled = false;
			/**@type {JSRecord<db:/svy_framework/sec_user_right>} */
			var rEmpSec = scopes.avDB.getRec('sec_user_right', ['user_id', 'user_org_id', 'security_key_id'], 
				[userID, orgID, sRptSecKeyID], null, null, true, globals.avBase_dbase_framework);

			// if emp sec_user_right exists have to use that 
			if(rEmpSec){
				if(rEmpSec.is_denied != 1){
					bRptEnabled = true;
				}
			}
			// else loop thru emp's groups and check check sec_user_right recs to see if 1 turned on 
			else{
				for(var k=0; k < groups.length; k++)
				{
					/**@type {JSRecord<db:/svy_framework/sec_user_right>} */
					var rGroupSec = scopes.avDB.getRec('sec_user_right', ['group_id', 'security_key_id'], 
						[groups[k], sRptSecKeyID], null, null, true, globals.avBase_dbase_framework);
					
					if(rGroupSec && rGroupSec.is_denied != 1){
						bRptEnabled = true;
						break;
					}
				}
				
			}
			
			if(!bRptEnabled){
				i++;
				resultDS.addRow(i, [record.rpt_id.toString()]);
			}
		}
	}
	
	forms.rpt_reportUI_dtl_tab_reports.foundset.loadRecords(resultDS);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B5798631-0D14-43CB-A328-F28B64BC84FD"}
 */
function btnSelect(event) {
	globals.avRpt_selectedProgram = program_name;
}
