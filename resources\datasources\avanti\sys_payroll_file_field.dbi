columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
creationOrderIndex:-1,
dataType:1,
flags:37,
length:36,
name:"prff_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"aprf_id"
},
{
allowNull:false,
autoEnterType:5,
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
lookupValue:"scopes.globals.org_id",
name:"org_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"prdd_length"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"prdd_position"
},
{
allowNull:false,
autoEnterType:2,
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
name:"prf_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:-9,
length:50,
name:"prff_col_header"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"prff_order_num"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:-9,
length:20,
name:"prff_pr_data_table_field_name"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"prff_pr_data_table_field_num"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:-9,
length:4,
name:"prff_sort_dir"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"prff_sort_num"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:-1,
dataType:4,
defaultValue:"0",
name:"prff_use_database_field_flag"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,250,0]]",
creationOrderIndex:-1,
dataType:12,
length:250,
name:"prff_user_defined_value"
},
{
allowNull:true,
autoEnterType:5,
compatibleColumnTypes:"[[-9,128,0]]",
creationOrderIndex:-1,
dataType:12,
length:128,
lookupValue:"scopes.globals.icon_shuffleGrey",
name:"sequence_icon"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"sequence_nr"
}
],
name:"sys_payroll_file_field",
tableType:0