/**
 * @properties={typeid:35,uuid:"E6A4DD00-FD32-4B24-8E02-52AFCBB06CD9",variableType:-4}
 */
var bHitOK = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"83A65DF7-E93F-46E7-94CF-0417C5A283BD",variableType:4}
 */
var _iLastCheckNumAdded = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"15ED77DB-122A-4FEE-86C5-E0CD81911545"}
 */
var _sRenderedItems = '';

/**
 * @properties={typeid:35,uuid:"B11DB73D-57E2-4580-ACB9-EBAB86EE93CB",variableType:-4}
 */
var _aCheckNames = [];

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D794F2AB-7DCA-4A96-8EAF-08F09472D80E"}
 */
function onActionOK(event) {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).qa_check_pos_1 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_2 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_3 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
	}
	resetPlaceHolder();
	bHitOK=true;
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"F187392D-215F-49F6-8CC5-DF369EEF46F9"}
 */
function onActionCancel(event) {
	resetPlaceHolder();
	bHitOK=false;
	globals.DIALOGS.closeForm(event);
	return false;
}

/**
 * @properties={typeid:24,uuid:"16D3004D-11BC-45D5-8F61-3C108D3CA19E"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0;
		
	}
}


/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @protected
 *
 * @properties={typeid:24,uuid:"AF94E4ED-8BE2-47FA-8B78-63E70B325D1C"}
 */
function onShow(firstShow, event) {
	elements.qa_check_1.visible = true;
	elements.qa_check_field_1.visible = true;
	elements.qa_check_2.visible = true;
	elements.qa_check_field_2.visible = true;
	elements.qa_check_field_3.visible = true;
	elements.qa_check_3.visible = true;
	
	if(!qa_check_pos_1){
		elements.qa_check_1.visible = false;
		elements.qa_check_field_1.visible = false;
	}
	if(!qa_check_pos_2){
		elements.qa_check_2.visible = false;
		elements.qa_check_field_2.visible = false;
	}
	if(!qa_check_pos_3){
		elements.qa_check_3.visible = false;
		elements.qa_check_field_3.visible = false;
	}
}