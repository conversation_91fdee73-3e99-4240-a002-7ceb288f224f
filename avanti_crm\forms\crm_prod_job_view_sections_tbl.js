/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"F4FBE41E-30C6-4E99-9373-D80A9946E444",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"AAC8A417-31CC-4397-8362-798F953ECD2A"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"7BE5DCA4-D978-4101-BD32-BDBF4ED73C27"}
 */
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	_super.onShowForm(_firstShow, _event);
	globals.avSecurity_toggleButtonsFromUserPermissions("CRM_Job_Sections");
	
	// sl-26896 - hide calculator btn as per Josh. may implement in future
	elements.grid.getColumn(elements.grid.getColumnIndex('calculate_icon')).visible = false; 
	
	return;
}

/**
 * @properties={typeid:35,uuid:"208EEB4B-DC3D-4B40-B83A-756E27D74510",variableType:-4}
 */
var isEditableDataprovider = false;

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E4D200DE-8CFE-411A-9441-71AED60E805D"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "calculate_icon" && col.styleClass.search(' disabled') == -1) {
		onAction_btnCalculator(event);
	}
	if (col.id == "info_icon" && col.styleClass.search(' disabled') == -1) {
		onAction_btnInfo(event);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"AB20FC9A-D4D9-4856-AC8E-2305B00BB073"}
 */
function onAction_btnCalculator(event) {
	//forms.sa_order_revision_detail_section_tbl.btnEstimateCalculator(event);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"7198E013-164D-490E-A199-2DBAC5858818"}
 */
function onAction_btnInfo(event) {
	forms.sa_order_revision_detail_section_tbl.controller.loadRecords(foundset)
	forms.sa_order_revision_detail_section_tbl.btnInfo(event);
}

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"B7C841E4-4DD8-4675-8401-270402F09EC9"}
 * @AllowToRunInFind
 */
function onRecordSelection(_event, _form)
{
//	reloadSection()

	globals.avCRM_selectedSectionID = ordrevds_id;
	
	controller.recreateUI();

	/*
	//Update Tab Description
	if (foundset.ordrevds_line_type == "C")
	{
		scopes.globals.avUtilities_tabSetText("sf_main_dtl_job_details", "tabsJobDetails", 2, 'Tasks')
	}
	else
	{
		scopes.globals.avUtilities_tabSetText("sf_main_dtl_job_details", "tabsJobDetails", 2, 'Tasks: ' + foundset.ordrevds_description)
	}
	*/
	 _super.onRecordSelection(_event, _form);
}

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"4A5E213A-4A4A-47F1-9B54-486CAA0F000F"}
 */
function reloadSection() {
	globals.avSales_selectedRevisionSectionID = ordrevds_id;
	
	globals.avSales_selectedRevisionDetailQtyUUID = foundset.sa_order_revision_detail_section_to_sa_order_revds_qty.ordrevdqty_id
		
	/** @type{JSFoundSet<db:/avanti/sa_order_revds_task>} */
	var task_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_order_revds_task')
	
	if(task_fs.find()) {
		task_fs.ordrevds_id = ordrevds_id
		if(task_fs.search() > 0) {
			forms.crm_sa_order_revision_detail_task_tbl.foundset.loadRecords(task_fs)
		} else {
			forms.crm_sa_order_revision_detail_task_tbl.foundset.loadRecords(null)
		}
	}	
}
