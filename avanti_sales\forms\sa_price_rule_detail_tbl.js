/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A53462CE-997F-4101-A30F-5B03332A4AC4",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"963E8042-7C71-4BF5-9867-DDE0D02F47E6"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"04106BDE-B667-46C1-A3CE-28C731451EE3"}
 */
function onShowForm(_firstShow, _event) {

	 if (_firstShow) {
	 	if (!_gridReady) {
	 		application.executeLater(onShowForm, 500, [true, _event]);
	 		return null;
	 	}
	 }

	 _super.onShowForm(_firstShow, _event)
	 
	  
	 //Get the standard markup
	 _standardMarkup = globals.avBase_getSystemPreference_Number(1);
	 globals.avUtilities_setFormEditMode(_event.getFormName());
	// controller.readOnly = false;
	 _AllowActualPrice = false;
	 
	 setDefaultSort();
	 clearAllFilters();
	 
	 forms.sa_price_rule_detail_dtl.setTabs();
	 
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"17A4C93A-A69F-4E8D-AAFA-5159430C97DD"}
 */
var btnDelete_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.deleteSelectedPriceRecord');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F0EB3EB9-E2DA-4B17-8BEE-BEDD57BB40C3"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnDelete") {
		scopes.globals.avUtilities_delete(event,false,true,false);
	}
	
	if (col.id.search('priceruledtl_repsonse_seg') > -1) {
		var iLoc = parseInt(col.id.split('priceruledtl_repsonse_seg')[1]) - 1;
		var sSegCode = forms.sa_price_rule_dtl.aSegmentCode[iLoc];
		switch(sSegCode) {
			case "A":
				onFocusGained_Customer(event);
				break;
			case "D":
				onFocusGained_Item(event);
				break;
			case "H":
				onFocusGained_Task(event);
				break;
			case "I":
				onFocusGained_Department(event);
				break;
			case "K":
				onFocusGained_CostCenter(event);
				break;
			case "M":
				onFocusGained_MachineVariable(event);
				break;
			case "N":
				onFocusGained_Division(event);
				break;
			case "O":
				onFocusGained_Plant(event);
				break;
		}
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 * @AllowToRunInFind
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"E07D7156-8A55-4D44-9CC8-0494BCA4F891"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);

	if (col.id.search('priceruledtl_repsonse_seg') > -1) {
		var iLoc = parseInt(col.id.split('priceruledtl_repsonse_seg')[1]) - 1;
		var sSegCode = forms.sa_price_rule_dtl.aSegmentCode[iLoc];
		switch(sSegCode) {
			case "N":
				onDataChange_Division(oldValue, newValue, event);
				break;
			case "O":
				onDataChange_Plant(oldValue, newValue, event);
				break;
			default:
				onDataChange_Segment(oldValue, newValue, event);
				break;
		}
	}	
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"695967B5-BEB5-4E84-B6AB-5EFA0928517A",variableType:8}
 */
var iSegIndex = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"62A886C2-17CC-40AC-8EDC-3773E25BD0E1"}
 */
var sSegmentCode = null;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"FFD59906-5CA3-4082-A437-89AE221BE8AA",variableType:-4}
 */
var _AllowActualPrice = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2AB68D07-245F-4D18-A25A-CC2841A6236B",variableType:8}
 */
var _standardMarkup = 0.00;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"13052E3D-5609-4277-B63F-5B9F71EDCDC1"}
 */
var _filter_seg6 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0000CE7E-ECA5-4E43-AD6C-F6E19FCC7DBF"}
 */
var _filter_seg5 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8A856EFF-2026-44F8-910E-D88B7EB7097A"}
 */
var _filter_seg4 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8DF35C57-5B6C-41EE-A260-09B3705D3AB1"}
 */
var _filter_seg3 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"44CD62FC-AA92-4C80-A19D-7D210478A99D"}
 */
var _filter_seg2 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"CA9DFB1F-7532-4EDA-A31C-6F86275597AF"}
 */
var _filter_seg1 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"EE486964-5CA2-4629-A9F3-38DC0AF289EE"}
 */
var _seg1_defaultValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"AD097689-22F6-47A2-8B38-512C67AE0684"}
 */
var _seg2_defaultValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"036C046A-31B2-44E6-B099-7355D54D8093"}
 */
var _seg3_defaultValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F10846CC-CCBB-4624-B3F9-E5185053FFC4"}
 */
var _seg4_defaultValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4B017019-5412-4F12-9B01-271766F34901"}
 */
var _seg5_defaultValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4816E617-8F5A-45A4-913B-ECB9759DB630"}
 */
var _seg6_defaultValue = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"EF1B5582-1889-4CBD-B8F5-2AAE4B41F2B8",variableType:-4}
 */
var aSegmentFlag;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"01BCEEFA-FE67-4889-B828-8B10AC5AC7F9"}
 */
var _ruleDetailMode = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"755C6EA4-E91B-4BD5-BE0A-0C63006E511A"}
 */
var _originalValue_Seg1 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"49891328-BE55-45BA-95C8-7C1FAEFDC5FB"}
 */
var _originalValue_Seg2 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"25A9A2BB-65DA-47D9-85C7-550ABDF22589"}
 */
var _originalValue_Seg3 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8AE8B737-273C-41D0-9D9B-0614D0EC26A4"}
 */
var _originalValue_Seg4 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"C678C072-8AB7-4AF2-9392-F7C77B722482"}
 */
var _originalValue_Seg5 = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"95AF9CCC-D688-44C8-9982-3AEE12719061"}
 */
var _originalValue_Seg6 = null;

/**
 * @properties={typeid:35,uuid:"D121A146-E8CF-477B-B4AC-41D5AC7D7811",variableType:-4}
 */
var _currentDivision = null;

/**
 * @properties={typeid:35,uuid:"85D93957-7332-42DE-A57B-23F5D1D8081F",variableType:-4}
 */
var _currentPlant = null;

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"830A1E99-0BCF-4035-A15F-265D9BCFF68A"}
 */
function onAction_btnAdd(event) {
	
	if (!scopes.avUtils.isNavModeReadOnly())
	{
		foundset.newRecord(true,true);
		_ruleDetailMode = 'Add';
		
		if (pricerule_id == null)
		{
			application.output("NULL Value")
		}

		//TODO: build value list based on price codes selections.
		foundset.priceruledtl_price_method = 'C';
		foundset.priceruledtl_break_method = scopes.avUtils.PRICERULE_BREAK_METHOD.QuantityBreak; // 'QB';
		
		if (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule) && sa_price_rule_detail_to_sa_price_rule.pricerule_type == 'TASK'){
			foundset.priceruledtl_task_price_uom = 'EACH';
		}
		
		if (foundset.priceruledtl_price_method == 'C') foundset.priceruledtl_base_pct = _standardMarkup;
			
		if (_seg1_defaultValue != null) priceruledtl_repsonse_seg1 = _seg1_defaultValue;
		if (_seg2_defaultValue != null) priceruledtl_repsonse_seg2 = _seg2_defaultValue;
		if (_seg3_defaultValue != null) priceruledtl_repsonse_seg3 = _seg3_defaultValue;
		if (_seg4_defaultValue != null) priceruledtl_repsonse_seg4 = _seg4_defaultValue;
		if (_seg5_defaultValue != null) priceruledtl_repsonse_seg5 = _seg5_defaultValue;
		if (_seg6_defaultValue != null) priceruledtl_repsonse_seg6 = _seg6_defaultValue;
	}
}

/**
 * @param {Object} oldValue
 * @param {Object} newValue
 * @param {JSEvent} event the event that triggered the action
 * 
 * @properties={typeid:24,uuid:"A4953849-4A7F-4C55-AA8A-89FE17BAF728"}
 */
function onDataChange_Segment(oldValue,newValue,event)
{		
	var _element = event.getElementName()
	
	if (_element == 'priceruledtl_repsonse_seg1')
	{
		globals.avBase_selectedPriceSegment = 1;
		priceruledtl_repsonse_seg1val = getDisplayValue(priceruledtl_repsonse_seg1);
	}
	else if (_element == 'priceruledtl_repsonse_seg2')
	{
		globals.avBase_selectedPriceSegment = 2;
		priceruledtl_repsonse_seg2val = getDisplayValue(priceruledtl_repsonse_seg2);
	}
	else if (_element == 'priceruledtl_repsonse_seg3')
	{
		globals.avBase_selectedPriceSegment = 3;
		priceruledtl_repsonse_seg3val = getDisplayValue(priceruledtl_repsonse_seg3);
	}
	else if (_element == 'priceruledtl_repsonse_seg4')
	{
		globals.avBase_selectedPriceSegment = 4;
		priceruledtl_repsonse_seg4val = getDisplayValue(priceruledtl_repsonse_seg4);
	}
	else if (_element == 'priceruledtl_repsonse_seg5')
	{
		globals.avBase_selectedPriceSegment = 5;
		priceruledtl_repsonse_seg5val = getDisplayValue(priceruledtl_repsonse_seg5);
	}
	else if (_element == 'priceruledtl_repsonse_seg6')
	{
		globals.avBase_selectedPriceSegment = 6;
		priceruledtl_repsonse_seg6val = getDisplayValue(priceruledtl_repsonse_seg6);
	}
}

/**
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"AF555716-1EDD-4106-8707-0FE3E1818994"}
 */
function onDataChange_Division(oldValue,newValue,event){
    onDataChange_Segment(oldValue,newValue,event);
    var iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (var i = 1; i <= iMax; i++) {
        // clearing all values of row except division upon division selection change
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] != "N" && foundset['priceruledtl_repsonse_seg' + i.toString()])
        {
            foundset['priceruledtl_repsonse_seg' + i.toString()] = '';
            foundset['priceruledtl_repsonse_seg' + i.toString()+'val'] = '';
        }
    }
}

/**
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"9C79BA91-DB18-452A-AE72-59B060A02848"}
 */
function onDataChange_Plant(oldValue,newValue,event){
    onDataChange_Segment(oldValue,newValue,event);
    var iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (var i = 1; i <= iMax; i++) {
     // clearing all values of row except division & Plant upon plant selection change
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] != "O" && forms.sa_price_rule_dtl.aSegmentCode[i - 1] != "N" && foundset['priceruledtl_repsonse_seg' + i.toString()])
        {
            foundset['priceruledtl_repsonse_seg' + i.toString()] = '';
            foundset['priceruledtl_repsonse_seg' + i.toString()+'val'] = '';
        }
    }
}

/**
 * Perform sort.
 *
 * @param {String} dataProviderID element data provider
 * @param {Boolean} asc sort ascending [true] or descending [false]
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C8F5B956-5D84-466B-A9CB-BCBDF8A8E947"}
 */
function onSort(dataProviderID, asc, event) {
	// GD - 2014-01-08: Sort failing because records may not be saved
	databaseManager.saveData();

	//Do the sort based on the description fields
	if (dataProviderID == null) // GD - 2014-01-08: SL-1949 - Was always doing the default sort for the first column, which is not correct; || dataProviderID == 'priceruledtl_repsonse_seg1')
	{
		setDefaultSort();
	}
	else
	{
		dataProviderID += 'val';
		foundset.sort(dataProviderID + (asc ? ' asc' : ' desc'), false)
	}
}

/**
 * setDefaultSort
 *
 * @properties={typeid:24,uuid:"519B44B6-0F34-4FD2-B934-CD6007594ECA"}
 */
function setDefaultSort() 
{
	 foundset.sort('priceruledtl_repsonse_seg1val asc, priceruledtl_repsonse_seg2val asc, priceruledtl_repsonse_seg3val asc, priceruledtl_repsonse_seg4val asc, priceruledtl_repsonse_seg5val asc, priceruledtl_repsonse_seg6val asc')
}

/**
 * getDisplayValue
 * @param {UUID} sUUID - The primary key for the response value
 * 
 * @return {String} Display Value
 *
 * @properties={typeid:24,uuid:"3DF63231-1901-4179-866A-E6F9D565E7DA"}
 */
function getDisplayValue(sUUID)
{

	sSegmentCode = sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type._to_sa_price_type_to_sa_price_type_segment$avbaseselectedpricesegment.pricetypeseg_code;

	if (sUUID == null || sSegmentCode == null) return null;
	
		switch(sSegmentCode)
		{
		case 'A':
			return getPriceSegmentDisplayValue('sa_customer','cust_name,cust_code','cust_id',sUUID);
		case 'B':
			return getPriceSegmentDisplayValue('sa_customer_category','custcat_desc,custcat_code','custcat_id',sUUID);
		case 'C':
			return getPriceSegmentDisplayValue('sa_customer_class','custclass_desc,custclass_code','custclass_id',sUUID);
		case 'D':
			return getPriceSegmentDisplayValue('in_item','item_desc1,item_code','item_id',sUUID);
		case 'E':
			return getPriceSegmentDisplayValue('in_item_class','itemclass_desc,itemclass_code','itemclass_id',sUUID);
		case 'F':
			return getPriceSegmentDisplayValue('in_group','ingroup_desc,ingroup_code','ingroup_id',sUUID);
		case 'G':
			return getPriceSegmentDisplayValue('app_task_type','tasktype_desc','tasktype_uuid',sUUID);
		case 'H':
			return getPriceSegmentDisplayValue('sa_task','task_description','task_id',sUUID);
		case 'I':
			return getPriceSegmentDisplayValue('sys_department','dept_desc,dept_code','dept_id',sUUID);
		case 'J':
			return getPriceSegmentTaskOperationDisplayValue(sUUID);
		case 'K':
			return scopes.avPriceRules.getPriceRuleSegmentDisplayValueFromID(sSegmentCode, sUUID);
		case 'L':
			return getPriceSegmentDisplayValue('in_ink_type','inktype_description,inktype_code','inktype_id',sUUID);
		case 'M':
			//Just testing
			//[TMD-TODO]
			return getPriceSegmentDisplayValue('sa_task_machine',' taskmachine_description','taskmachine_id',sUUID);
		case 'N':
		    return getPriceSegmentDisplayValue('sys_division','div_name,div_code','div_id',sUUID);
		case 'O':
		    return getPriceSegmentDisplayValue('sys_plant','plant_name,plant_code','plant_id',sUUID);
        
		default:
		
	}
	
	return null;
	
//	switch(sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type._to_sa_price_type_to_sa_price_type_segment$avbaseselectedpricesegment.pricetypeseg_code)
//	{
//	case 'A':
//		return getCustomerDisplayValue(sUUID);
//	case 'B':
//		return getCustomerCategoryDisplayValue(sUUID);
//	case 'C':
//		return getCustomerClassDisplayValue(sUUID);
//	case 'D':
//		return getItemDisplayValue(sUUID);
//	case 'E':
//		return getItemClassDisplayValue(sUUID);
//	case 'F':
//		return getItemGroupDisplayValue(sUUID);
//	case 'G':
//		return getTaskTypeDisplayValue(sUUID);
//	case 'H':
//		return getTaskDisplayValue(sUUID);
//	case 'I':
//		return getDepartmentDisplayValue(sUUID);
//	case 'J':
//		return getOperationCatergoryDisplayValue(sUUID);
//	case 'K':
//			
//	default:
//	}
}

/**
 * avBase_getCustomerDisplayValue
 * @param {String} sTableName
 * @param {String} sFields
 * @param {String} sKeyFieldName
 * @param {UUID} sUUID
 * 
 * @return {String} Display Value
 * 
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"5076CA87-E064-4BF8-B3AF-59C11283147B"}
 */
function getPriceSegmentDisplayValue(sTableName,sFields,sKeyFieldName,sUUID)
{

	//if (sUUID != null) sUUID = sUUID.toString();
		
	/***@type {String}*/
	var sReturnValue = null;
	
	/***@type {String}*/
	var sSQL;
	var oSQL = new Object();
	sSQL = globals.avUtilities_appSqlQueryGet('SELECT', null, 'getPriceSegmentDisplayValue', globals.avBase_dbase_version).query;

	// Replace the table name in the sql
	sSQL = sSQL.replace("<<table>>", sTableName);
	
	// Replace the fields
	sSQL = sSQL.replace("<<fields>>", sFields);
	
	// Replace the PK field name
	sSQL = sSQL.replace("<<keyFieldName>>", sKeyFieldName);
	
	oSQL.sql = sSQL;
	oSQL.args = [globals.UUIDtoStringNew(sUUID)];
	
	if (sTableName.indexOf("app_") == -1) {
	    oSQL.sql += " AND org_id = ?";
    	oSQL.args.push(globals.org_id);
	}

	/** @type {JSDataSet}*/
	var dsData = globals.avUtilities_sqlDataset(oSQL); 
	
	if (dsData && dsData.getMaxRowIndex() > 0)
	{
		for (var d = 1; d <= dsData.getMaxRowIndex(); d++)
		{
			for (var dr = 1; dr <= dsData.getMaxColumnIndex(); dr++)
			{
				if (sReturnValue != null) sReturnValue += ' - ';
				sReturnValue += dsData.getValue(d,dr);
			}
		}
	}
	
	if (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule) && sa_price_rule_detail_to_sa_price_rule.pricerule_type == 'TASK'){
		setPriceMethodValueList(new Array("C","A"));
	}
	
	else if (sSegmentCode == "D"){
		if (allowActualPrice(sUUID)){
			setPriceMethodValueList(new Array("C","A"));
		}
		else{
			setPriceMethodValueList(new Array("C"));
		}
	}
	else{
		setPriceMethodValueList(new Array("C"));
	}
	
	return sReturnValue;
	
}

/**
 * getPriceSegmentTaskOperationDisplayValue
 * @param {UUID} sUUID
 * 
 * @return {String} Display Value
 *
 * @properties={typeid:24,uuid:"82542BA1-D9B7-4F35-9318-ED148E384715"}
 */
function getPriceSegmentTaskOperationDisplayValue(sUUID)
{
	if (sUUID != null) sUUID = sUUID.toString();
	
	/***@type {String}*/
	var sReturnValue = null;
	
	/***@type {String}*/
	var sSQL;
	var oSQL = new Object();
	sSQL = globals.avUtilities_appSqlQueryGet('SELECT', null, 'getPriceSegmentTaskOperationDisplayValue', globals.avBase_dbase_version).query;

	oSQL.sql = sSQL;
	oSQL.args = [sUUID];

	/** @type {JSDataSet}*/
	var dsData = globals.avUtilities_sqlDataset(oSQL); 
	
	var sDesc = null;
	if (dsData && dsData.getMaxRowIndex() > 0)
	{
		for (var d = 1; d <= dsData.getMaxRowIndex(); d++)
		{
			for (var dr = 1; dr <= dsData.getMaxColumnIndex(); dr++)
			{
				if (sReturnValue != null) sReturnValue += ' - ';
				sDesc = dsData.getValue(d,dr);
				sDesc = i18n.getI18NMessage(sDesc);
				sReturnValue += sDesc;
			}
			
			
		}
	}
	
	return sReturnValue;
	
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"A64A4BA6-E0F6-448E-A902-827F89F17D9B"}
 */
function dc_cancel(_event, _triggerForm) {
	return _super.dc_cancel(_event, _triggerForm)
}

/**
 * refreshUI
 *
 * @properties={typeid:24,uuid:"BB0BA9F3-7A0C-4D68-BD71-CCCED1436A0C"}
 */
function refreshUI()
{
	getPriceRecords();
}

/**
 * getPriceRecords
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"BBBDAED6-997B-40F7-A4AC-43456F123AFF"}
 */
function getPriceRecords() 
{
//	/*** @type {JSFoundSet<db:/avanti/sa_price_rule_detail>} */
//	var fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sa_price_rule_detail');

	if (foundset.find() || foundset.find())
	{
		if (_filter_seg1 != null) priceruledtl_repsonse_seg1val = '%' + _filter_seg1 + '%';
		if (_filter_seg2 != null) priceruledtl_repsonse_seg2val = '%' + _filter_seg2 + '%';
		if (_filter_seg3 != null) priceruledtl_repsonse_seg3val = '%' + _filter_seg3+ '%';
		if (_filter_seg4 != null) priceruledtl_repsonse_seg4val = '%' + _filter_seg4+ '%';
		if (_filter_seg5 != null) priceruledtl_repsonse_seg5val = '%' + _filter_seg5+ '%';
		if (_filter_seg6 != null) priceruledtl_repsonse_seg6val = '%' + _filter_seg6+ '%';
		
		foundset.search();
		
		controller.loadRecords(foundset)
		setDefaultSort();
		
		//Save the defaults to be used on new record creation
		if (foundset.getSize() > 0)
		{
			if (_filter_seg1 != null) _seg1_defaultValue = priceruledtl_repsonse_seg1;
			if (_filter_seg2 != null) _seg2_defaultValue = priceruledtl_repsonse_seg2;
			if (_filter_seg3 != null) _seg3_defaultValue = priceruledtl_repsonse_seg3;
			if (_filter_seg4 != null) _seg4_defaultValue = priceruledtl_repsonse_seg4;
			if (_filter_seg5 != null) _seg5_defaultValue = priceruledtl_repsonse_seg5;
			if (_filter_seg6 != null) _seg6_defaultValue = priceruledtl_repsonse_seg6;
		}
		
	}
}

/**
 * clearAllFilters
 *
 * @properties={typeid:24,uuid:"EEA2C7C0-13F2-40F1-B86D-1CE963B3BA07"}
 */
function clearAllFilters()
{
	_filter_seg1 = null;
	_filter_seg2 = null;
	_filter_seg3 = null;
	_filter_seg4 = null;
	_filter_seg5 = null;
	_filter_seg6 = null;
	
	refreshUI();
	
}

/**
 * clearFilter
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"1F533FEC-8257-447F-95BB-55842D8050F9"}
 */
function clearFilter(event)
{
	var _eventName = event.getElementName();
	
	switch(_eventName)
	{
	case 'btnFilterSeg1':
		_filter_seg1 = null;
		break;
	case 'btnFilterSeg2':
		_filter_seg2 = null;
		break;
	case 'btnFilterSeg3':
		_filter_seg3 = null;
		break;
	case 'btnFilterSeg4':
		_filter_seg4 = null;
		break;
	case 'btnFilterSeg5':
		_filter_seg5 = null;
		break;
	case 'btnFilterSeg6':
		_filter_seg6 = null;
		break;

	}
	
	refreshUI();
	
}

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"B3F4CA82-CC5D-450C-BED9-CF5C4A659DA0"}
 */
function onRecordSelection(_event, _form) {
	 _super.onRecordSelection(_event, _form)
	 
	 _ruleDetailMode = '';
	 
	 _originalValue_Seg1 = priceruledtl_repsonse_seg1;
	 _originalValue_Seg2 = priceruledtl_repsonse_seg2;
	 _originalValue_Seg3 = priceruledtl_repsonse_seg3;
	 _originalValue_Seg4 = priceruledtl_repsonse_seg4;
	 _originalValue_Seg5 = priceruledtl_repsonse_seg5;
	 _originalValue_Seg6 = priceruledtl_repsonse_seg6;
	 	 
	 forms.sa_price_rule_detail_dtl.setTabs();
	 
	 var bAllowActualPrice1 = allowActualPrice(priceruledtl_repsonse_seg1)
	 var bAllowActualPrice2 = allowActualPrice(priceruledtl_repsonse_seg2)
	 var bAllowActualPrice3 = allowActualPrice(priceruledtl_repsonse_seg3)
	 var bAllowActualPrice4 = allowActualPrice(priceruledtl_repsonse_seg4)
	 var bAllowActualPrice5 = allowActualPrice(priceruledtl_repsonse_seg5)
	 var bAllowActualPrice6 = allowActualPrice(priceruledtl_repsonse_seg6) 
	 
	 if (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule) && sa_price_rule_detail_to_sa_price_rule.pricerule_type == 'TASK'){
		 setPriceMethodValueList(new Array("C","A"));
	 }
	 else if (bAllowActualPrice1 || bAllowActualPrice2 || bAllowActualPrice3 || bAllowActualPrice4 || bAllowActualPrice5 || bAllowActualPrice6) {
		 setPriceMethodValueList(new Array("C","A"));
	 }
	 else{
		 setPriceMethodValueList(new Array("C"));
	 }
	 
	 forms.sa_price_rule_breaks_tbl.refreshUI();
}

/**
 * Callback method form when editing is stopped.
 *
 * @param {JSRecord<db:/avanti/sa_price_rule_detail>} record record being saved
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9FFB2F76-442B-462D-8361-19114467410F"}
 * @AllowToRunInFind
 */
function onRecordEditStop(record, event) {
	
    // TODO:  Uncomment after Servoy bug is fixed when using global method converters
    	
    //Validate entry, Check required fields
    if (
        scopes.avUtils.isNavModeReadOnly() 
        || event.getElementName() == null && event.getElementName() == 'btnAdd' 
        || event.getElementName() == 'btnDelete'
    ) {
        return true;
    }

	var bDisplayDialog = false;
	
	if (aSegmentFlag != null)
	{
		for (var i = 0; i < aSegmentFlag.length; i++)
		{
		
		var iSeg = i + 1
		var sSeg = iSeg.toString();
		
		if (aSegmentFlag[i] == 1 && foundset['priceruledtl_repsonse_seg' + sSeg] == null || foundset['priceruledtl_repsonse_seg' + sSeg] != null && foundset['priceruledtl_repsonse_seg' + sSeg].length == 0)
			{
				bDisplayDialog = true;
				break;
			}
		}
	}

	
	if (bDisplayDialog == true)
	{
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.requiredFields_title"), 
			i18n.getI18NMessage('avanti.dialog.requiredFields_msg'), 
			i18n.getI18NMessage("avanti.dialog.ok"));
		return false;
	}
	
	//Check for duplicates
	var fsDuplicate = foundset.duplicateFoundSet();
	var iMax = 0;
	
	if (_ruleDetailMode != 'Add' && (_originalValue_Seg1 == priceruledtl_repsonse_seg1 && _originalValue_Seg2 == priceruledtl_repsonse_seg2
			&& _originalValue_Seg3 == priceruledtl_repsonse_seg3 && _originalValue_Seg4 == priceruledtl_repsonse_seg4
			&& _originalValue_Seg5 == priceruledtl_repsonse_seg5 && _originalValue_Seg6 == priceruledtl_repsonse_seg6))
	{
		iMax = 1;
	}
		
	if(fsDuplicate.find() || fsDuplicate.find())
	{
		fsDuplicate.priceruledtl_repsonse_seg1 = (record.priceruledtl_repsonse_seg1 != null) ? fsDuplicate.priceruledtl_repsonse_seg1 = record.priceruledtl_repsonse_seg1: '^'
		fsDuplicate.priceruledtl_repsonse_seg2 = (record.priceruledtl_repsonse_seg2 != null) ? fsDuplicate.priceruledtl_repsonse_seg2 = record.priceruledtl_repsonse_seg2: '^'
		fsDuplicate.priceruledtl_repsonse_seg3 = (record.priceruledtl_repsonse_seg3 != null) ? fsDuplicate.priceruledtl_repsonse_seg3 = record.priceruledtl_repsonse_seg3: '^'
		fsDuplicate.priceruledtl_repsonse_seg4 = (record.priceruledtl_repsonse_seg4 != null) ? fsDuplicate.priceruledtl_repsonse_seg4 = record.priceruledtl_repsonse_seg4: '^'
		fsDuplicate.priceruledtl_repsonse_seg5 = (record.priceruledtl_repsonse_seg5 != null) ? fsDuplicate.priceruledtl_repsonse_seg5 = record.priceruledtl_repsonse_seg5: '^'
		fsDuplicate.priceruledtl_repsonse_seg6 = (record.priceruledtl_repsonse_seg6 != null) ? fsDuplicate.priceruledtl_repsonse_seg6 = record.priceruledtl_repsonse_seg6: '^'
		
		fsDuplicate.search();
	}
	
	
	if (fsDuplicate.getSize() > iMax)
	{
		
		var bAllowDup = false;
		
		// Check if it's item based.  Can allow duplicates because of selling units.
		var bAllowActualPrice1 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg1))
		var bAllowActualPrice2 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg2))
		var bAllowActualPrice3 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg3))
		var bAllowActualPrice4 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg4))
		var bAllowActualPrice5 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg5))
		var bAllowActualPrice6 = allowActualPrice(globals.UUIDtoString(record.priceruledtl_repsonse_seg6))
		 
		if (bAllowActualPrice1 || bAllowActualPrice2 || bAllowActualPrice3 || bAllowActualPrice4 || bAllowActualPrice5 || bAllowActualPrice6) bAllowDup = true;
		 
		if (bAllowDup == false && (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule) && sa_price_rule_detail_to_sa_price_rule.pricerule_type == 'ITEM'))
		{
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.duplicatePriceRecord_title"), 
				i18n.getI18NMessage('avanti.dialog.duplicatePriceRecord_msg'), 
				i18n.getI18NMessage("avanti.dialog.ok"));
			return false;
		}
		
	}
	
	_ruleDetailMode = '';

	return true;
}

/**
 * Check if the segment uuid is a valid item based selection
 *
 * @param {UUID} sItemUUID - Item UUID
 *
 * @returns {Boolean} ReturnDesc
 *
 *
 * @properties={typeid:24,uuid:"7E1FF5F8-CA22-4540-B7F1-25F736AEBB2C"}
 */
function allowActualPrice(sItemUUID) {
    var bReturn = false;

    var vlRealValues = new Array();
    var vlDisplayValues = new Array();

    if (sItemUUID != null) {
        /*** @type {JSFoundSet<db:/avanti/in_item>} */
        var fsItem = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_item');
        fsItem.loadRecords(sItemUUID);

        if (utils.hasRecords(fsItem)) {
            bReturn = true;

            //Set Sell Price Units
            fsItem.in_item_to_in_item_selling_uom.sort("sequence_nr");
            forms.sa_price_rule_detail_dtl_actual_price._fsItemSellUOM = fsItem.in_item_to_in_item_selling_uom;
            
            for (var i = 1; i <= fsItem.in_item_to_in_item_selling_uom.getSize(); i++) {
                
                var rSellUOM = fsItem.in_item_to_in_item_selling_uom.getRecord(i);

                vlRealValues.push(rSellUOM.itemselluom_sell_uom_id);
                vlDisplayValues.push(rSellUOM.in_item_selling_uom_to_sys_unit_of_measure.uom_desc);

                if (i == 1 && priceruledtl_price_uom == null) {
                    priceruledtl_price_uom = rSellUOM.itemselluom_sell_uom_id;
                }
                forms.sa_price_rule_detail_dtl_actual_price.setSellPriceFormat();
            }
            application.setValueListItems('avItem_SellingUOM', vlDisplayValues, vlRealValues);
            
            if (vlRealValues.length > 0 
                    && vlRealValues.indexOf(priceruledtl_price_uom) == -1) {
                priceruledtl_price_uom = vlRealValues[0];
            }
            else if (vlRealValues.length == 0 ) {
                priceruledtl_price_uom = null;
            }
        }
    }
    else if (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule)
            && sa_price_rule_detail_to_sa_price_rule.pricerule_type == "INK") {
        // GD - Dec 2, 2018: SL-16064 - vl was not getting cleared all the time, so user was able to select a non-valid price uom
        // Now clear out the price rule uom if it is not item based
        priceruledtl_price_uom = null;
        application.setValueListItems('avItem_SellingUOM', vlDisplayValues, vlRealValues);
    }

    return bReturn;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DD10B2D2-B493-4F40-92D2-5910707C42B7"}
 */
function onAction_btnBulkChange(event)
{
	if (scopes.avUtils.isNavModeReadOnly()) return;
	
	//Only Available for customer column right now.
	var sElementName = event.getElementName();
	
	// Get the segment index from the button
	iSegIndex = utils.stringToNumber(utils.stringRight(sElementName,1));
	
	//Check if it's a customer
	if (forms.sa_price_rule_dtl.aSegmentCode.length == 0 || forms.sa_price_rule_dtl.aSegmentCode.length > 0 && forms.sa_price_rule_dtl.aSegmentCode[iSegIndex-1] != "A") return;
	
	// Show Bulk Change Customer Dialog.
	forms.sa_price_rule_bulkchange_customer_dlg._callbackForm = "sa_price_rule_detail_tbl"
	forms.sa_price_rule_bulkchange_customer_dlg._callbackMethod = "bulkUpdateSegments";
	
	//Prepopulate the from customer based on the current record
	forms.sa_price_rule_bulkchange_customer_dlg._fromCustomer = null;
	if (foundset.getSize()> 0)
	{
		forms.sa_price_rule_bulkchange_customer_dlg._fromCustomer = foundset.getSelectedRecord()["priceruledtl_repsonse_seg" + iSegIndex.toString()];
	}
	
	//Build the from valuelist based on unique values
	setBulkChangeSourceValueList();

	var dlgTitle = i18n.getI18NMessage("avanti.tooltip.bulkChange") + " " + forms.sa_price_rule_dtl.aSegmentDesc[iSegIndex -1];
	globals.DIALOGS.showFormInModalDialog(forms.sa_price_rule_bulkchange_customer_dlg, -1, -1, 525, 225,dlgTitle, true, false, "dlgPriceRuleBulkChangeCustomer", true);
}

/**
 * Bulk Update Segments
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-11-21
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} sFromUUID
 * @param {String} sToUUID
 *
 *
 * @properties={typeid:24,uuid:"CE988C32-42E4-4484-A231-2553E7824181"}
 */
function bulkUpdateSegments(event,sFromUUID,sToUUID)
{
	var iMax = foundset.getSize();
	for (var i = 1; i <= iMax; i++) 
	{
		var rRuleDetail = foundset.getRecord(i);
		
		if (rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString()] == sFromUUID)
		{
			rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString()] = sToUUID;
			
			globals.avBase_selectedPriceSegment = iSegIndex;
			rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString() + "val"] = getDisplayValue(rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString()]);
		}
	}
}

/**
 * Build the bulk change source valuelist
 *
 * @properties={typeid:24,uuid:"4A48AE93-0FB6-4B76-9750-7A233EDE18BB"}
 */
function setBulkChangeSourceValueList()
{

	var aDisplay = new Array();
	var aReturn = new Array();
	
	var iMax = foundset.getSize();
	for (var i = 1; i <= iMax; i++)
	{
		var rRuleDetail = foundset.getRecord(i);
		var sSeg = rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString()]
		var sSegDisplay = rRuleDetail["priceruledtl_repsonse_seg" + iSegIndex.toString() + "val"]
		
		if (aReturn.indexOf(sSeg) == -1)
		{
			aReturn.push(sSeg);
			
			if (forms.sa_price_rule_dtl.aSegmentCode[iSegIndex-1] == "A") // Get the customer code so the vl format matches
			{
				globals.avSales_selectedCustomerUUID = sSeg;
				if (utils.hasRecords(_to_sa_customer$avsales_selectedcustomeruuid)) sSegDisplay += " - " + _to_sa_customer$avsales_selectedcustomeruuid.cust_code;
			}
			
			aDisplay.push(sSegDisplay)
		}
	}
	
	application.setValueListItems("vl_PriceRuleBulkUpdateSource", aDisplay, aReturn);
	
}

/**
 * Handle focus gained event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"EE279156-906D-4BF8-A8F8-4E353BE107AA"}
 */
function onFocusGained_MachineVariable (event) {	
	// populate the machine variable value list based on the selected task.
	setTaskMachineValueList(null);	
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"017861D2-FEC1-4706-BC2D-D0C86087DAA2"}
 */
function onFocusGained_Division(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData;

    oSQL.sql = "SELECT div_name + ' - ' +  div_code, div_id FROM sys_division WHERE org_id = ? \
            AND div_id IN (SELECT div_id FROM sys_employee_div WHERE empl_id = ?) "
    oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];

    oSQL.sql += " ORDER BY sequence_nr ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 1));
        aReturn.push(dsData.getValue(i, 2));
    }
    application.setValueListItems("vl_PriceRules_Divisions", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"A409C9C4-489E-4D6A-955C-E2709FAF2FB6"}
 */
function onFocusGained_Plant(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData;

    oSQL.sql = "SELECT plant_name + ' - ' + plant_code, plant_id, sequence_nr FROM sys_plant WHERE org_id = ? \
    AND plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ?)";
    oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];

    iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (i = 1; i <= iMax; i++) {

        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Plant
        {
            oSQL.sql += " AND div_id = ? ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
    }
    oSQL.sql += " ORDER BY sequence_nr ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 1));
        aReturn.push(dsData.getValue(i, 2));
    }
    application.setValueListItems("vl_PriceRules_Plants", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"98C8FEF1-39B4-4C22-B2C9-3F8D3CAC6C78"}
 */
function onFocusGained_Task(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData,
        sDivPlantSQL = '';

    oSQL.sql = "SELECT task_description, task_id FROM sa_task WHERE org_id = ? \
                    AND task_active = 1 "
    oSQL.args = [globals["org_id"]];

    iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (i = 1; i <= iMax; i++) {
        if(!sDivPlantSQL && (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" || forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O"))
        {
            sDivPlantSQL = " AND (ISNULL(all_divs,0) = 1 OR task_id IN (SELECT object_id FROM sa_division_plant \
                WHERE object_type = 'TASK' AND org_id = ? ";
            oSQL.args.push(globals["org_id"]);
        }
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Division
        {
            sDivPlantSQL += " AND div_id = ? ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
        else if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Plant
        {
            sDivPlantSQL += " AND (ISNULL(all_plants,0) = 1 OR (plant_id = ?) ) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
    }
        
    oSQL.sql += sDivPlantSQL == '' ? '' : sDivPlantSQL + '))';
    oSQL.sql += " ORDER BY task_description ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 1));
        aReturn.push(dsData.getValue(i, 2));
    }
    application.setValueListItems("vl_PriceRules_Tasks", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"41CC34E9-F73F-4E9E-BEC1-3F47E4726CD3"}
 */
function onFocusGained_Department(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData,
        sDivPlantSQL = '';

    oSQL.sql = "SELECT dept_desc + ' - ' + dept_code, dept_id FROM sys_department WHERE org_id = ? \
                    AND dept_active = 1 "
    oSQL.args = [globals["org_id"]];

    iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (i = 1; i <= iMax; i++) {
        
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Division
        {
            sDivPlantSQL += " AND (div_id IS NULL OR div_id = ?) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
        else if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Plant
        {
            sDivPlantSQL += " AND (plant_id IS NULL OR plant_id = ?) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
    }
        
    oSQL.sql += sDivPlantSQL == '' ? '' : sDivPlantSQL ;
    oSQL.sql += " ORDER BY dept_desc ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 1));
        aReturn.push(dsData.getValue(i, 2));
    }
    application.setValueListItems("vl_PriceRules_Departments", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"B7E686BC-9E01-4229-A30F-146A7874FF5B"}
 */
function onFocusGained_Customer(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData,
        sDivPlantSQL = '';

    oSQL.sql = "SELECT cust_name + ' - ' + cust_code, cust_id FROM sa_customer WHERE org_id = ? "
    oSQL.args = [globals["org_id"]];

    iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (i = 1; i <= iMax; i++) {
        if(!sDivPlantSQL && (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" || forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O"))
        {
            sDivPlantSQL = " AND (cust_id IN (SELECT object_id FROM sa_division_plant \
                WHERE object_type = 'CUST' AND org_id = ? ";
            oSQL.args.push(globals["org_id"]);
        }
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Division
        {
            sDivPlantSQL += " AND div_id = ? ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
        else if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Plant
        {
            sDivPlantSQL += " AND (ISNULL(all_plants,0) = 1 OR (plant_id = ?) ) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
    }
        
    oSQL.sql += sDivPlantSQL == '' ? '' : sDivPlantSQL + '))';
    oSQL.sql += " ORDER BY cust_name ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 1));
        aReturn.push(dsData.getValue(i, 2));
    }
    application.setValueListItems("vl_PriceRules_Customers", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"B4C8214A-578C-4159-887A-8DC4B6C5302E"}
 */
function onFocusGained_CostCenter(event) {
	load_vl_PriceRules_CostCenter(true);
}

/**
 * @public 
 * 
 * @param {Boolean} [bForCurrentRecord]
 *
 * @properties={typeid:24,uuid:"84B0189B-536C-48C2-934F-9907776646D3"}
 */
function load_vl_PriceRules_CostCenter(bForCurrentRecord) {
    var aReturn = [],
	    aDisplay = [],
	    i = 0,
	    iMax = 0,
	    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	    oSQL = {},
	    /***@type {JSDataSet} ***/
	    dsData,
	    sDivPlantSQL = '';
	
	oSQL.sql = "SELECT c.cc_id, d.dept_desc  + '-' + o.opcat_desc + '-' + c.cc_desc + ' - ' + d.dept_code + '-' + o.opcat_code + '-' + c.cc_op_code \
				FROM sys_cost_centre c \
				INNER JOIN sys_operation_category o on c.opcat_id = o.opcat_id \
				INNER JOIN sys_department d on o.dept_id = d.dept_id \
				WHERE \
					c.org_id = ? \
	                AND c.cc_active = 1 "
	oSQL.args = [globals["org_id"]];
	
	if (bForCurrentRecord) {
		iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
		
		for (i = 1; i <= iMax; i++) {
			// Division
			if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) {
				sDivPlantSQL += " AND (c.div_id IS NULL OR c.div_id = ?) ";
				oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
			}
			// Plant
			else if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O" && foundset['priceruledtl_repsonse_seg' + i.toString()]) {
				sDivPlantSQL += " AND (c.plant_id IS NULL OR c.plant_id = ?) ";
				oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
			}
		}

		oSQL.sql += sDivPlantSQL == '' ? '' : sDivPlantSQL;
	}
	
	oSQL.sql += " ORDER BY c.cc_desc ASC";
	
	dsData = globals["avUtilities_sqlDataset"](oSQL);
	
	iMax = dsData.getMaxRowIndex();
	
	for (i = 1; i <= iMax; i++) {
	    aReturn.push(dsData.getValue(i, 1));
	    aDisplay.push(dsData.getValue(i, 2));
	}
	
	application.setValueListItems("vl_PriceRules_CostCenter", aDisplay, aReturn);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"1C4EA35A-EA34-4080-81B6-F22197847889"}
 */
function onFocusGained_Item(event) {
    var aReturn = [],
        aDisplay = [],
        i = 0,
        iMax = 0,
        /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
        oSQL = {},
        /***@type {JSDataSet} ***/
        dsData,
        sDivPlantSQL = '';

    oSQL.sql = "SELECT DISTINCT ini.item_id, item_desc1 + ' - ' + item_code \
    			FROM in_item ini \
                INNER JOIN in_item_warehouse iiw ON ini.item_id = iiw.item_id \
                INNER JOIN in_warehouse iw ON iiw.whse_id = iw.whse_id \
                WHERE ini.org_id = ?"
    oSQL.args = [globals["org_id"]];

    iMax = forms.sa_price_rule_dtl.aSegmentCode.length;
    for (i = 1; i <= iMax; i++) {
        
        if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "N" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Division
        {
            sDivPlantSQL += " AND (iw.div_id IS NULL OR iw.div_id = ?) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
        else if (forms.sa_price_rule_dtl.aSegmentCode[i - 1] == "O" && foundset['priceruledtl_repsonse_seg' + i.toString()]) // Plant
        {
            sDivPlantSQL += " AND (iw.plant_id IS NULL OR iw.plant_id = ?) ";
            oSQL.args.push(foundset['priceruledtl_repsonse_seg' + i.toString()].toString());
        }
    }
        
    oSQL.sql += sDivPlantSQL == '' ? '' : sDivPlantSQL ;
    oSQL.sql += " ORDER BY item_desc1 + ' - ' + item_code ASC";

    dsData = globals["avUtilities_sqlDataset"](oSQL);

    iMax = dsData.getMaxRowIndex();
    for (i = 1; i <= iMax; i++) {

        aDisplay.push(dsData.getValue(i, 2));
        aReturn.push(dsData.getValue(i, 1));
    }
    application.setValueListItems("vl_PriceRules_Items", aDisplay, aReturn);
}

/**
 * setTaskMachineValueList
 * 
 * @param {UUID} uTaskUUID
 * 
 * @properties={typeid:24,uuid:"A3F1B8ED-6830-4F38-9D46-BFF23D02EF57"}
 */
function setTaskMachineValueList (uTaskUUID) {

	// Changed from a custom valuelist to a related valuelist to fix the inconsistant functionality when there was a full screen of data.
	var nTaskSegment = 0;
	
	if (uTaskUUID == null && utils.hasRecords(foundset.sa_price_rule_detail_to_sa_price_rule) && 
			utils.hasRecords(foundset.sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type) &&
			utils.hasRecords(foundset.sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type.sa_price_type_to_sa_price_type_segment)){
				
		//Find out what segment is the task id
		for ( var i = 1; i <= foundset.sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type.sa_price_type_to_sa_price_type_segment.getSize(); i++ ) {
			var rPriceTypeSegment = foundset.sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type.sa_price_type_to_sa_price_type_segment.getRecord(i);
			
			if (rPriceTypeSegment.pricetypeseg_code == 'H'){
				nTaskSegment = rPriceTypeSegment.sequence_nr;
				break;
			}
		}
	}
	
	if (nTaskSegment > 0 || uTaskUUID != null){
		if (nTaskSegment > 0) uTaskUUID = foundset["priceruledtl_repsonse_seg" + nTaskSegment.toString()];
		scopes.globals.avBase_selectedPriceTask = uTaskUUID;
	}
	else{
		scopes.globals.avBase_selectedPriceTask = null;
	}
}

/**
 * Set's the price method value list
 *
 * @param {Array} aPriceMethods
 *
 *
 * @properties={typeid:24,uuid:"EFE9BA0F-F0A6-4ACD-9E7B-BF94984A3DAB"}
 */
function setPriceMethodValueList(aPriceMethods)
{
	/** @type  {JSDataSet} */
	var dataset = databaseManager.createEmptyDataSet(0,new Array('display_values','optional_real_values'));
	
	if (globals.avBase_selectedPriceMethods == null){
		dataset.addRow([i18n.getI18NMessage('avanti.lbl.costPlusMarkup%'),"C"]);
		dataset.addRow([i18n.getI18NMessage('avanti.lbl.priceMethod_actualPrice'),"A"]);
	}
	else{
		for ( var i = 0; i < globals.avBase_selectedPriceMethods.length; i++ ) {
			if (globals.avBase_selectedPriceMethods[i] == 'C') dataset.addRow([i18n.getI18NMessage('avanti.lbl.costPlusMarkup%'),"C"]);
			if (globals.avBase_selectedPriceMethods[i] == 'A') dataset.addRow([i18n.getI18NMessage('avanti.lbl.priceMethod_actualPrice'),"A"]);
		}
	}
	
	scopes.globals.avUtilities_setValuelistItems(forms.sa_price_rule_detail_dtl.controller.getName(), 'priceruledtl_price_method', dataset);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * 
 * @private 
 *
 * @properties={typeid:24,uuid:"5488E91D-C0E6-4037-8173-3DF86CD71CF7"}
 */
function onAction_btnDeleteAllRecords (event) {
	if (!scopes.avUtils.isNavModeReadOnly()){
		if (utils.hasRecords(sa_price_rule_detail_to_sa_price_rule)){
			var sPriceRuleName = sa_price_rule_detail_to_sa_price_rule.pricerule_desc;
			var sPriceRuleID = sa_price_rule_detail_to_sa_price_rule.pricerule_id.toString();
		}
		
		var bDeleted = globals.avUtilities_deleteAll(event);
		
		if (bDeleted){
	       var oAudit = {
	      		serverName: globals.avBase_dbase_avanti,
	      		tableName: "sa_price_rule_detail",
	      		fieldName: "",
	      		action: "3",
	      		source: "sa_price_rule_detail_tbl",
	      		primaryKey: sPriceRuleID,
	      		oldData: "",
	      		newData: "",
	      		documentType: "Price Rule",
	      		documentNumber: sPriceRuleName,
	      		org_id: globals.org_id.toString(),
	      		programName: globals.nav_program_name
	      		};
	       
		       scopes.avDB.writeAuditLog(oAudit);
		}
	}
}

/**
 * @properties={typeid:24,uuid:"08367E07-A664-42AE-83C1-07F5103534D6"}
 */
function updateAllSegmentVals() {
	var aCodeSegmentTypes = ['A', 'B', 'C', 'D', 'E', 'F', 'I', 'K', 'N', 'O'];
	
	for (var i = 1; i <= foundset.getSize(); i++) {
		var rRuleDetail = foundset.getRecord(i);

		for (var j = 0; j < forms.sa_price_rule_dtl.aSegmentCode.length; j++) {
			var iSegIdx = j+1;
			globals.avBase_selectedPriceSegment = iSegIdx;
			var sSegmentCode = sa_price_rule_detail_to_sa_price_rule.sa_price_rule_to_sa_price_type._to_sa_price_type_to_sa_price_type_segment$avbaseselectedpricesegment.pricetypeseg_code;
			
			if (aCodeSegmentTypes.indexOf(sSegmentCode) > -1) {
				rRuleDetail["priceruledtl_repsonse_seg" + iSegIdx.toString() + "val"] = getDisplayValue(rRuleDetail["priceruledtl_repsonse_seg" + iSegIdx.toString()]);
			}
		}
	}
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"62B10AAF-CE69-466C-B0D8-830AD25A55DA"}
 */
function applyFilter(oldValue, newValue, event) {
	// have to make sure all segXval cols use the new format before running filter. only run updateAllSegmentVals if havent been run before
	if (sa_price_rule_detail_to_sa_price_rule && !sa_price_rule_detail_to_sa_price_rule.segments_use_desc_code_format) {
		sa_price_rule_detail_to_sa_price_rule.segments_use_desc_code_format = 1;
		updateAllSegmentVals();
		databaseManager.saveData();
	}
	
	refreshUI();
	
	return true;
}
