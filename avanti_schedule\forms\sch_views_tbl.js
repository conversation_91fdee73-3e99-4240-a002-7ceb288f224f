/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"DCEC33EF-F10F-4D97-8D19-7BDA270027DA",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"247D6FE7-A0BA-4A14-AF51-F9A0FBDCD4F2"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B20C7A56-C805-47A7-905F-00C6E755D214"}
 */
function onShow(firstShow, event)
{

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	globals.avSchedule_selectedViewID = view_id;
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"94C0A0AC-1C30-499F-ABAF-1BEB524782DB"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnDelete") {
		deleteScheduleEvent(event);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"613C7D4D-8022-4C04-B6AD-EFC6861CBBEA"}
 */
function btnAdd(event)
{
	var rRec;
	
	if (foundset.getSize() == 0)
	{
		_super.dc_new(event,controller.getName());
		
		rRec = foundset.getSelectedRecord();
	}
	else
	{
		if (scopes.avUtils.isNavModeReadOnly()) return;
		
		rRec = foundset.getRecord(foundset.newRecord(false, true));
	}
	
	
	rRec.sequence_nr = foundset.getSize();
	
	globals.avSchedule_selectedViewID = rRec.view_id;
	
//	loadViews();
	
	elements.grid.requestFocus(elements.grid.getColumnIndex("viewName"));
}

/**
 * Loads the views
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-02-11
 *
 *
 * @properties={typeid:24,uuid:"6770192E-893A-49C7-83ED-2D95AFEC6F0F"}
 */
function loadViews()
{
	if (utils.hasRecords(_to_sch_view$employeeid))
	{
		foundset.loadRecords(_to_sch_view$employeeid);
		foundset.sort("view_name asc");
	}
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param _form
 *
 * @return
 * @properties={typeid:24,uuid:"15B8AD41-E094-45CD-B4D7-6201E48179FD"}
 */
function onRecordSelection(event, _form) {
	validateViewResources();
    loadTodaysViewResourcesCapRecs();
    
	if(globals.avBase_getSystemPreference_Number(79) === 1){
		scopes.avVL.load_vl_schedBoardViewDepts(div_id, plant_id);
	}
	else{
		scopes.avVL.load_vl_schedBoardViewDepts(null, null);
	}
	
	globals.avSchedule_selectedViewID = view_id;
	forms.sch_agenda_dtl._viewID = view_id;
	forms.sch_agenda_dtl.loadView();
	
	return _super.onRecordSelection(event, _form);
}

/**
 * @properties={typeid:24,uuid:"17C4B26B-C134-4160-8933-32C9DAB36EF3"}
 */
function validateViewResources() {
	if (sch_view_to_sch_view_resource) {
		for (var r = sch_view_to_sch_view_resource.getSize(); r >= 1 ; r--) {
			var rResource = sch_view_to_sch_view_resource.getRecord(r);
			var bValidEquip = utils.hasRecords(rResource.sch_view_resource_to_eq_equipment) && scopes.avScheduling.isEquipActive(rResource.sch_view_resource_to_eq_equipment.getRecord(1));
			var bValidEmp = utils.hasRecords(rResource.sch_view_resource_to_sys_employee) && rResource.sch_view_resource_to_sys_employee.empl_active;
			
			// this resource is not a valid equip or emp - delete it
			if (!bValidEquip && !bValidEmp) {
				sch_view_to_sch_view_resource.deleteRecord(r)
			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"320BC8BD-AB8B-4554-AE3C-11EBE040F996"}
 */
function loadTodaysViewResourcesCapRecs(){
    if(utils.hasRecords(sch_view_to_sch_view_resource)){
        var dCurDateTime = application.getTimeStamp();
        
        for(var i=1; i<sch_view_to_sch_view_resource.getSize(); i++){
            var rResource = sch_view_to_sch_view_resource.getRecord(i);
            
            if(utils.hasRecords(rResource.sch_view_resource_to_eq_equipment)){
                var rEquip = rResource.sch_view_resource_to_eq_equipment.getRecord(1);
                scopes.avScheduling.createEquipmentCapacityRecords(rEquip, null, dCurDateTime, 'F', true);
            }
            else if(utils.hasRecords(rResource.sch_view_resource_to_sys_employee)){
                var rEmp = rResource.sch_view_resource_to_sys_employee.getRecord(1);
                scopes.avScheduling.createEmployeeCapacityRecords(rEmp, null, dCurDateTime, null, 'F', true);
            }
        }
    }
}
