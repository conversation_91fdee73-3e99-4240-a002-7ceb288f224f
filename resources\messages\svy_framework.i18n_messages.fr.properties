=\=\=\=\=\=\=
<<<<<<<=HEAD
>>>>>>>=feature/reportSanneke
Job=(Detail)\=Dossier (D\u00E9tail)
OptionPane.cancelButtonMnemonic=A
OptionPane.cancelButtonText=Annuler
OptionPane.noButtonMnemonic=N
OptionPane.noButtonText=Non
OptionPane.okButtonMnemonic=O
OptionPane.okButtonText=Ok
OptionPane.yesButtonMnemonic=O
OptionPane.yesButtonText=Oui
Packing=Slip Detail\=D\u00E9tail du emballer
abanti.lbl.defaultPhysicalPackaging=Emballage physique par d\u00E9faut
abanti.lbl.physicalPackaging=Emballage physique
avanti.changeOrderSummary=Sommaire du changement
avanti.dialog.APInvoiceNumberExists=Le num\u00E9ro de facture a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9
avanti.dialog.CSVintegrationFileError=Le fichier d'int\u00E9gration CSV n'est pas d\u00E9fini
avanti.dialog.CantGetPackLock=Impossible de verrouiller tous les \u00E9l\u00E9ments de campagne du bordereau de livraison. Veuillez r\u00E9essayer plus tard.
avanti.dialog.CantMoveMilestoneIntoPast=Impossible de d\u00E9placer le jalon dans le pass\u00E9.
avanti.dialog.CarrierServiceTypeAlreadyUsed=Le type de transporteur/service est d\u00E9j\u00E0 utilis\u00E9 pour cette adresse de livraison
avanti.dialog.ChargebackPostingHasCompleted=Le traitement de la r\u00E9trofacturation est termin\u00E9
avanti.dialog.ChargebackedOrderCantBeEdited=Cette commande client a le statut "Chargeback enregistr\u00E9e" et ne peut pas \u00EAtre modifi\u00E9e.
avanti.dialog.CreditNote_doesnothave_line=Vous ne pouvez pas enregistrer une note de cr\u00E9dit qui n'a pas de ligne de commande client/facture.
avanti.dialog.DataFlushComplete=Rin\u00E7age des donn\u00E9es termin\u00E9. Veuillez vous d\u00E9connecter puis vous reconnecter pour voir les modifications des donn\u00E9es.
avanti.dialog.DialogTitleSprintPopup=Quoi de neuf
avanti.dialog.EquipShiftSetupChanges=Les modifications apport\u00E9es \u00E0 la configuration des \u00E9quipes d'\u00E9quipement n'affecteront pas les jours pour lesquels des t\u00E2ches sont planifi\u00E9es. Ces jours continueront d'utiliser l'ancienne configuration des \u00E9quipes. Pour qu'ils utilisent la nouvelle configuration des \u00E9quipes, vous devez ex\u00E9cuter \u00AB Optimiser la planification \u00BB \u00E0 partir du tableau de planification.
avanti.dialog.FreightShipmentMissingData_msg=Vous devez sp\u00E9cifier toutes les valeurs dans l'onglet palettes pour l'exp\u00E9dition de fret.
avanti.dialog.FreightShipmentMissingData_title=Erreur d'exp\u00E9dition de fret
avanti.dialog.IncorrectOrTooOldDate_msg=Vous avez entr\u00E9 une date non valide. Toutes les dates doivent \u00EAtre le ou apr\u00E8s le 1er janvier 1900.
avanti.dialog.InvalidBillToAddress=Les commandes suivantes avec des adresses de facturation non valides seront ignor\u00E9es de la facturation group\u00E9e
avanti.dialog.InvoiceExistsForOrder=Il existe des factures existantes (<<invoice>>) li\u00E9es \u00E0 cette commande client. Les modifications apport\u00E9es \u00E0 ce bon de commande ne seront pas r\u00E9percut\u00E9es sur les factures existantes. Veuillez supprimer les factures originales et refacturer ce bon de commande.
avanti.dialog.ItemAlreadyReplaced=Cet article a d\u00E9j\u00E0 \u00E9t\u00E9 remplac\u00E9 par l'article\: 
avanti.dialog.ItemClassDeletionError_msg=Impossible de supprimer ces classes d'articles. Il est attribu\u00E9 \u00E0 un ou plusieurs \u00E9l\u00E9ments.
avanti.dialog.ItemClassDeletionError_title=Erreur de classe d'article
avanti.dialog.ItemTypeCantBeChanged=Le type d'article ne peut pas \u00EAtre modifi\u00E9 car l'article comporte des transactions.
avanti.dialog.ItemWIthInactiveSupplier=L'article s\u00E9lectionn\u00E9 (<param1>) est associ\u00E9 \u00E0 un fournisseur inactif. Veuillez soit changer de fournisseur, soit activer le fournisseur existant avant de proc\u00E9der \u00E0 l'achat.
avanti.dialog.ItemWIthInactiveSupplierInPlannedPO=Un ou plusieurs articles sont associ\u00E9s \u00E0 un fournisseur inactif. Veuillez soit changer de fournisseur, soit activer le fournisseur existant avant de proc\u00E9der \u00E0 l'achat.
avanti.dialog.LineItemHasGangOnly1Qty=Cet \u00E9l\u00E9ment de campagne contient une section gang, de sorte qu'il ne peut prendre en charge qu'une seule quantit\u00E9 d'\u00E9l\u00E9ment de campagne
avanti.dialog.LineItemHasMultipleQtys=Cet \u00E9l\u00E9ment de campagne ne peut prendre en charge qu'une seule quantit\u00E9 d'\u00E9l\u00E9ment de campagne lorsqu'une section de gang est ajout\u00E9e
avanti.dialog.MaxQtyCanBeUsedForRolls=La quantit\u00E9 maximale autoris\u00E9e pour ce rouleau est de
avanti.dialog.NoGroupSelected=Aucun groupe s\u00E9lectionn\u00E9
avanti.dialog.NotPDFFileMessage=Seuls les documents PDF peuvent \u00EAtre envoy\u00E9s pour approbation.
avanti.dialog.OrigInvHasMinOrderChargeInBatchInvoice=Les commandes suivantes ont une facture avec des frais de commande minimum appliqu\u00E9s. Veuillez supprimer la facture d'origine ou cr\u00E9er une note de cr\u00E9dit pour refacturer les nouveaux frais avec des frais de commande minimum mis \u00E0 jour
avanti.dialog.OrigInvoiceForOrderHasMinOrderCharge=La facture originale <<invoice>> pour la commande client <<ordnum>> comporte des frais de commande minimum appliqu\u00E9s, veuillez supprimer l'original et refacturer avec des frais de commande minimum mis \u00E0 jour.
avanti.dialog.OrigInvoiceHasMinOrderCharge=La facture d'origine <<invnum>> a des frais de commande minimum appliqu\u00E9s, veuillez cr\u00E9er une note de cr\u00E9dit puis refacturer les nouveaux frais avec des frais de commande minimum mis \u00E0 jour.
avanti.dialog.PDFReviewCheckProofsConfirmation=Souhaitez-vous envoyer cette preuve au client pour approbation maintenant?
avanti.dialog.PDFReviewConfirmationTitle=Envoyer une confirmation de preuve
avanti.dialog.PDFReviewNoResponseReturned=Enfocus PDF Review Server ne r\u00E9pond pas et l'\u00E9preuve PDF n'a pas \u00E9t\u00E9 envoy\u00E9e pour approbation. Veuillez v\u00E9rifier l'\u00E9tat du serveur de r\u00E9vision PDF Enfocus et r\u00E9essayer.
avanti.dialog.PDFReviewNotEnabled=Vous n'avez pas configur\u00E9 la fonction "Enfocus Proofing" dans votre organisation. Ce document ne peut pas \u00EAtre envoy\u00E9 pour approbation.
avanti.dialog.PDFReviewNotOrderOrJob=L'envoi d'une \u00E9preuve PDF pour approbation n'est disponible que pour les documents li\u00E9s aux commandes ou aux travaux. Ce document ne peut pas \u00EAtre envoy\u00E9 pour approbation.
avanti.dialog.PDFReviewSendProofsConfirmation=Il existe plusieurs \u00E9preuves sur cette commande client. Souhaitez-vous les envoyer toutes\nau client pour approbation maintenant\u00A0?
avanti.dialog.PDFReviewSendProofsConfirmationWithProofChecking=Ce document n'est pas marqu\u00E9 comme \u00AB\u00A0Preuve\u00A0\u00BB. Souhaitez-vous marquer ce document comme \u00AB\u00A0Preuve\u00A0\u00BB et envoyer tous les autres documents marqu\u00E9s comme \u00AB\u00A0Preuve\u00A0\u00BB au client pour approbation maintenant\u00A0?
avanti.dialog.PDFReviewServerErrorTitle=Erreur d'envoi de preuve
avanti.dialog.PriceRulesExportSuccessful=L'exportation des r\u00E8gles de prix a r\u00E9ussi
avanti.dialog.Question=Question
avanti.dialog.RTScheduleHasCompleted=Pr\u00EAt \u00E0 planifier a termin\u00E9 le traitement
avanti.dialog.RateShoppingCompleted=\u00C9valuer l'achat termin\u00E9.
avanti.dialog.RateShoppingInputsChanged=Certaines entr\u00E9es Rate Shopping ont chang\u00E9. Veuillez valider le nombre de cartons et les tarifs.
avanti.dialog.RemoveLock=Supprimer le verrouillage
avanti.dialog.SheetTooLargeForPress=La taille de la feuille ou du rouleau est trop grande pour la presse s\u00E9lectionn\u00E9e
avanti.dialog.SheetTooSmallForPress=La taille de la feuille ou du rouleau est trop petite pour la presse s\u00E9lectionn\u00E9e
avanti.dialog.ShiftSetupChanges=Les modifications apport\u00E9es \u00E0 la configuration des \u00E9quipes n'affecteront pas les jours pour lesquels des t\u00E2ches sont planifi\u00E9es. Ces jours continueront d'utiliser l'ancienne configuration des \u00E9quipes. Pour qu'ils utilisent la nouvelle configuration des \u00E9quipes, vous devez ex\u00E9cuter \u00AB Optimiser la planification \u00BB \u00E0 partir du tableau de planification.
avanti.dialog.ShipMethodServiceTypeAlreadyUsed=La m\u00E9thode d'exp\u00E9dition/le type de service est d\u00E9j\u00E0 utilis\u00E9 pour cette adresse d'exp\u00E9dition
avanti.dialog.ShopFloorMatChange_msg=Les rouleaux ont chang\u00E9 par rapport \u00E0 la liste de s\u00E9lection d'origine. Souhaitez-vous mettre \u00E0 jour la liste de s\u00E9lection avec les r\u00F4les modifi\u00E9s ?
avanti.dialog.ShopFloorMatDoesNotMatchPickList_msg=Le mat\u00E9riau que vous avez s\u00E9lectionn\u00E9 ne correspond pas aux mat\u00E9riaux de la liste de s\u00E9lection. Voulez-vous continuer?
avanti.dialog.ShowThisMessageAgain=Afficher \u00E0 nouveau ce message
avanti.dialog.ShowThisMessageAgainNextVision=ne me montrez plus \u00E7a pour cette version
avanti.dialog.SourceandTargetUOMSame=La source et l'unit\u00E9 de destination sont identiques. Veuillez s\u00E9lectionner une unit\u00E9 diff\u00E9rente pour la conversion.
avanti.dialog.ThisAddressSelectedByOtherUser=Cette adresse de commande a \u00E9t\u00E9 s\u00E9lectionn\u00E9e par un autre utilisateur (<param1>). Si vous \u00EAtes s\u00FBr que cette adresse n'est pas s\u00E9lectionn\u00E9e pour cet utilisateur, vous pouvez annuler ce verrouillage.\r\n\r\nVoulez-vous annuler le verrouillage?
avanti.dialog.TotalShipQtyDoesntEqualOrderQty=La quantit\u00E9 totale d'exp\u00E9dition n'est pas \u00E9gale \u00E0 la quantit\u00E9 command\u00E9e (<param1>)
avanti.dialog.UnableToInvoiceDocumentStream=Impossible de facturer la commande client avec le flux de documents
avanti.dialog.UpsOauth2Warning=\u00C0 partir du 5 juin 2023, UPS n'\u00E9mettra plus de cl\u00E9s d'acc\u00E8s API. Plus important encore, le 3 juin 2024, ces cl\u00E9s d'acc\u00E8s ne seront plus prises en charge pour l'authentification aux API UPS. Au lieu des cl\u00E9s d'acc\u00E8s API, UPS passera \u00E0 un mod\u00E8le de s\u00E9curit\u00E9 OAuth 2.0. Vous devrez obtenir les informations d'identification de l'API OAuth aupr\u00E8s d'UPS pour utiliser OAuth 2.0.
avanti.dialog.VirtualItemHasTrans=L'\u00E9l\u00E9ment virtuel ne peut pas \u00EAtre modifi\u00E9 car l'\u00E9l\u00E9ment comporte des transactions.
avanti.dialog.YouMustEnterANote=Vous devez entrer une note
avanti.dialog.accountsPayableInvoiceReviewStatusReverted=Changer le statut de la facture de "R\u00E9vis\u00E9" la supprimera du registre assign\u00E9 et forcera le registre \u00E0 \u00EAtre revu \u00E0 nouveau avant la publication.
avanti.dialog.addOrderLines=Ajouter des lignes de commande
avanti.dialog.additionalPassRequired=En raison du nombre de couleurs, un ou plusieurs laissez-passer suppl\u00E9mentaires sont n\u00E9cessaires.
avanti.dialog.addresDoesntHaveShipQtys=Cette adresse n'a pas de qtys d'exp\u00E9dition.
avanti.dialog.addressCantBeDeleted=Cette adresse ne peut pas \u00EAtre supprim\u00E9e car elle est utilis\u00E9e par\: 
avanti.dialog.addressMissingTaxGroup=Cette adresse n'a pas de groupe fiscal, elle ne peut donc pas \u00EAtre utilis\u00E9e.
avanti.dialog.addressWithNoShipQtyPlantQty=Il y a des adresses d'exp\u00E9dition qui n'ont pas de quantit\u00E9 d'exp\u00E9dition ou qui ont une quantit\u00E9 d'exp\u00E9dition mais pas d'allocation de quantit\u00E9 d'usine. Veuillez corriger cela.
avanti.dialog.addressesCantBeDeleted=Certaines adresses ne peuvent pas \u00EAtre supprim\u00E9es car elles sont utilis\u00E9es par\: 
avanti.dialog.advChgOrdInvoice_msg=Le prix prolong\u00E9 est verrouill\u00E9 car la valeur de l\u2019ordre de modification a \u00E9t\u00E9 ventil\u00E9e.\r\nSupprimez cette facture et d\u00E9cochez l\u2019option de rupture ou, avec un ordre de modification, modifiez le prix, puis facturez.
avanti.dialog.advChgOrdInvoice_title=Facture de commande de modification avanc\u00E9e
avanti.dialog.alreadyAWTStream=Le flux <param1> <param2> a d\u00E9j\u00E0 \u00E9t\u00E9 marqu\u00E9 comme 'flux de mod\u00E8le de travail'. Vous ne pouvez pas en avoir plus d'un.
avanti.dialog.avalaraFailedToCalculate=AvaTax n'a pas r\u00E9ussi \u00E0 calculer. Veuillez consulter le journal d'int\u00E9gration pour plus de d\u00E9tails.
avanti.dialog.batchInvoicing.deleteUserOptionsProfile=\u00CAtes-vous s\u00FBr de vouloir supprimer ce profil?
avanti.dialog.batchInvoicing.deleteUserOptionsProfileUser=Ce profil a \u00E9t\u00E9 enregistr\u00E9 pour la derni\u00E8re fois par << >>. \u00CAtes-vous s\u00FBr de vouloir le supprimer?
avanti.dialog.batchInvoicing.newProfileNameRequired=Veuillez saisir un nouveau nom de profil lorsque vous utilisez "Enregistrer sous".
avanti.dialog.batchInvoicing.newProfileNameRequiredQuestion=Veuillez saisir un nouveau nom de profil.
avanti.dialog.batchInvoicingLock=Cette facture est actuellement verrouill\u00E9e par le programme Batch Invoice. Il sera d\u00E9bloqu\u00E9 une fois les distributions calcul\u00E9es.
avanti.dialog.batchShipRunAllQ=Voulez-vous cr\u00E9er et confirmer des exp\u00E9ditions pour toutes les adresses de commande client r\u00E9pertori\u00E9es\u00A0?
avanti.dialog.billingCodeRequired=Le code de facturation est requis
avanti.dialog.binMissing=L'emplacement du bac est absent du num\u00E9ro de ligne
avanti.dialog.calculatorOverridesDetected_msg=S\u2019il vous pla\u00EEt \u00EAtre inform\u00E9 que les d\u00E9rogations de prix seront autoris\u00E9 \u00E0 d\u00E9bloquer les prix pour cet ordre de changement factur\u00E9.
avanti.dialog.calculatorOverridesDetected_title=Calculatrice remplace d\u00E9tect\u00E9
avanti.dialog.cancel=Annuler
avanti.dialog.cancelBalance_msg=Annuler le solde ferme la ligne et ne peut pas \u00EAtre rouvert
avanti.dialog.cancelOrderWithAppliedDeposits_msg=Les d\u00E9p\u00F4ts appliqu\u00E9s ont \u00E9t\u00E9 supprim\u00E9s et r\u00E9affect\u00E9s au compte du client.
avanti.dialog.cancelOrderWithOpenPackingSlip_msg=Impossible d'annuler cette commande client car elle figure sur un bon de livraison ouvert. Supprimez le bon de livraison ou supprimez la commande du bon de livraison, puis la commande peut \u00EAtre annul\u00E9e.
avanti.dialog.cancelOrderWithPostedDepositsReceived_msg=Les acomptes re\u00E7us contre cette commande ont \u00E9t\u00E9 comptabilis\u00E9s. Veuillez contacter le service comptable afin qu'il puisse proc\u00E9der aux ajustements n\u00E9cessaires.
avanti.dialog.cancelOrderWithTransactions_msg=Cette commande comporte des transactions et ne peut \u00EAtre rouverte une fois annul\u00E9e. Voulez-vous toujours annuler cette commande\u00A0?
avanti.dialog.cancelOrderWithUnPostedPostedDepositsReceived_msg=Les acomptes re\u00E7us contre cette commande ont \u00E9t\u00E9 supprim\u00E9s car ils n'avaient pas encore \u00E9t\u00E9 publi\u00E9s.
avanti.dialog.cancelOrderWithUnpostedInvoice_msg=Impossible d'annuler cette commande client car elle figure sur une facture non valid\u00E9e. Validez la facture, supprimez la facture ou supprimez la commande de la facture, puis la commande peut \u00EAtre annul\u00E9e.
avanti.dialog.cancelledCashReceiptReviewStatusReverted=Changer le statut de la facture de "R\u00E9vis\u00E9" la supprimera du registre attribu\u00E9 et forcera le registre \u00E0 \u00EAtre revu \u00E0 nouveau avant la publication.
avanti.dialog.cannotCancelProjectPlanRelease_msg=Vous ne pouvez annuler le lancement d'un plan de projet que si tous les ordres de division productrice sont \u00E0 l'\u00E9tat ouvert.
avanti.dialog.cannotCreateJWTTokenMsg=Impossible de cr\u00E9er un jeton, le mot de passe secret n'a pas \u00E9t\u00E9 d\u00E9fini sur la page d'administration.
avanti.dialog.cannotCreateJWTTokenTitle=Impossible de cr\u00E9er un jeton
avanti.dialog.cannotCreateNewRegister=Le registre\: <regnum> doit \u00EAtre affich\u00E9 avant qu'un nouveau registre puisse \u00EAtre cr\u00E9\u00E9.
avanti.dialog.cannotCreatePickList=Vous ne pouvez pas cr\u00E9er de pr\u00E9l\u00E8vement de commande car vous ne disposez pas d'une licence pour le module de traitement des commandes. Vous pouvez cr\u00E9er des pr\u00E9l\u00E8vements de production via le bouton \u00AB Nouvelle liste de pr\u00E9l\u00E8vement de commande \u00BB.
avanti.dialog.cannotDeleteSystemWorkType_msg=Les types de travail syst\u00E8me ne peuvent pas \u00EAtre supprim\u00E9s.
avanti.dialog.cannotFindPDFFile=Le fichier PDF n'est pas accessible et ne peut pas \u00EAtre envoy\u00E9 pour approbation. Veuillez v\u00E9rifier le chemin du fichier ou rattacher le document et r\u00E9essayer.
avanti.dialog.cannotSaveGangAsWorkTemplate=Les devis ou les commandes client utilisant la fonction group\u00E9e ne peuvent pas \u00EAtre enregistr\u00E9s comme mod\u00E8le de travail.
avanti.dialog.cannotdeleteline=\r\nCet \u00E9l\u00E9ment de ligne ne peut pas \u00EAtre supprim\u00E9 car des co\u00FBts de travail ont \u00E9t\u00E9 saisis en production.
avanti.dialog.cannotdeletesection=Cette section ne peut pas \u00EAtre supprim\u00E9e car des co\u00FBts de travail ont \u00E9t\u00E9 saisis en production.
avanti.dialog.cantChangeShipDate=Il y a des commandes de vente sur cet envoi qui ont \u00E9t\u00E9 affich\u00E9s. Vous ne pouvez pas modifier la date du navire.
avanti.dialog.cantHavePageSetsAndSubstrates=Une t\u00E2che ne peut pas avoir \u00E0 la fois des ensembles de pages et des substrats s\u00E9lectionn\u00E9s.
avanti.dialog.cantLoadDPDServices=Impossible de charger les services DPD en raison d'une erreur\: 
avanti.dialog.cantRemoveAllEmployees=Il s'agit d'un jalon r\u00E9serv\u00E9 aux employ\u00E9s (il n'utilise pas d'\u00E9quipement). Vous ne pouvez pas supprimer tous les employ\u00E9s ou cela ne peut pas \u00EAtre planifi\u00E9.
avanti.dialog.cantSelectCBFile=Ce fichier de r\u00E9trofacturation a d\u00E9j\u00E0 \u00E9t\u00E9 s\u00E9lectionn\u00E9 par un autre travail d'enregistrement automatique, il ne peut donc pas \u00EAtre s\u00E9lectionn\u00E9 pour celui-ci.
avanti.dialog.cantUseBackwardSSLag=Une d\u00E9pendance start-start ne peut pas \u00EAtre utilis\u00E9e pour la t\u00E2che <param1> car cela entra\u00EEnerait la fin de la t\u00E2che avant son pr\u00E9d\u00E9cesseur, la t\u00E2che <param2>. Veuillez ajuster les d\u00E9pendances dans la t\u00E2che <param3>.
avanti.dialog.carrierSetupIncomplete=La configuration est incompl\u00E8te pour l'op\u00E9rateur 
avanti.dialog.cashReceipt_unpostedCashReceipts=Cette facture a <montant> dans Paiements/Cr\u00E9dits non comptabilis\u00E9s. Les paiements qui d\u00E9passent le solde d\u00FB ne seront pas affich\u00E9s.
avanti.dialog.categoryAlreadySelected=La cat\u00E9gorie est d\u00E9j\u00E0 s\u00E9lectionn\u00E9e. Vous ne pouvez pas utiliser deux fois la m\u00EAme cat\u00E9gorie pour la purge
avanti.dialog.changeCustomerAdvanceBilling_msg=Cette commande client comporte une facture de facturation anticip\u00E9e pour le client d'origine. Si la facture n'a pas \u00E9t\u00E9 valid\u00E9e, vous pouvez la supprimer, sinon \u00E9mettre une note de cr\u00E9dit et vous pourrez alors changer de client.
avanti.dialog.changeCustomerException_InProgressProjectPlan=Le client ne peut pas \u00EAtre chang\u00E9 une fois le plan de projet en cours.
avanti.dialog.changeCustomerException_hasInvoice=Cette commande client a \u00E9t\u00E9 factur\u00E9e. Vous pouvez changer de client une fois que toutes les factures ont \u00E9t\u00E9 supprim\u00E9es ou que des notes de cr\u00E9dit ont \u00E9t\u00E9 \u00E9mises et enregistr\u00E9es.
avanti.dialog.changeCustomerException_isChangeOrder=Vous ne pouvez changer le client que sur la commande principale.
avanti.dialog.changeCustomer_keepExistingShipLocations=Conserver les emplacements de navire existants
avanti.dialog.changeCustomer_resetShipLocations=R\u00E9initialiser les emplacements des navires
avanti.dialog.changeCustomer_title=Changer de client
avanti.dialog.changeOrderCancelRelease_msg=Cet ordre de modification a d\u00E9j\u00E0 \u00E9t\u00E9 publi\u00E9 et ne peut pas \u00EAtre annul\u00E9.
avanti.dialog.changePlant?=La modification de la division entra\u00EEnera l'indisponibilit\u00E9 de tous les mod\u00E8les de travail, \u00E9l\u00E9ments d'inventaire et t\u00E2ches non associ\u00E9s \u00E0 la nouvelle division sur cette <param1>.\r\n\r\nVoulez-vous continuer?
avanti.dialog.codeHasAlreadyBeenUsed=Ce code <param1> a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9. Veuillez en s\u00E9lectioner un autre.
avanti.dialog.colorsFormatWrong=Les couleurs doivent \u00EAtre au format '4/4'
avanti.dialog.confirmActionClearRegister=Voulez-vous vraiment effacer le registre s\u00E9lectionn\u00E9\u00A0?
avanti.dialog.confirmActionReleaseSelectedPurchaseOrdersToGP_msg=Souhaitez-vous transmettre les bons de commande s\u00E9lectionn\u00E9s \u00E0 GP\u00A0?
avanti.dialog.confirmActionSendReleaseSelectedPurchaseOrdersToGP_msg=Souhaitez-vous envoyer et valider les bons de commande s\u00E9lectionn\u00E9s \u00E0 GP\u00A0?
avanti.dialog.confirmActionSendSelectedPurchaseOrdersToGP_msg=Souhaitez-vous envoyer les bons de commande s\u00E9lectionn\u00E9s \u00E0 GP\u00A0?
avanti.dialog.confirmBatchShipments?=Voulez-vous confirmer toutes les exp\u00E9ditions?
avanti.dialog.confirmClearCustomerEnvironmentalReportingItemClasses_msg=\u00CAtes-vous s\u00FBr de vouloir effacer toutes les classes d'articles pour le client s\u00E9lectionn\u00E9 ?
avanti.dialog.confirmRegisterResetSage200_msg=Voulez-vous vraiment recr\u00E9er le fichier d'exportation Sage 200 pour ce registre.
avanti.dialog.confirmReopenProjectPlan=Voulez-vous vraiment rouvrir ce plan de projet?
avanti.dialog.controlAccount_InventoryAdjustments=Aucun compte de contr\u00F4le n'a \u00E9t\u00E9 trouv\u00E9 pour les ajustements d'inventaire pour la devise de l'organisation. Veuillez contacter le service comptable pour vous assurer que les comptes g / l applicables sont d\u00E9finis ainsi que le compte de contr\u00F4le.
avanti.dialog.copiedThingCSRIsInactive=Le repr\u00E9sentant du service sur l'enregistrement en cours de duplication est inactif. La valeur par d\u00E9faut applicable a \u00E9t\u00E9 appliqu\u00E9e au nouvel enregistrement. Si aucune valeur par d\u00E9faut n'est disponible, le champ sera vide.
avanti.dialog.copiedThingSalesRepIsInactive=Le repr\u00E9sentant des ventes sur l'enregistrement en cours de duplication est inactif. La valeur par d\u00E9faut applicable a \u00E9t\u00E9 appliqu\u00E9e au nouvel enregistrement. Si aucune valeur par d\u00E9faut n'est disponible, le champ sera vide.
avanti.dialog.copyDueAtCustomer?=Voulez-vous copier la date d'\u00E9ch\u00E9ance au client dans la nouvelle commande?
avanti.dialog.copyRow_msg=Souhaitez-vous cr\u00E9er une copie de cette ligne?
avanti.dialog.countBinLocation_msg=\u00CAtes-vous s\u00FBr que l'emplacement du bac s\u00E9lectionn\u00E9 est correct ? Il ne peut plus \u00EAtre modifi\u00E9 par la suite\:
avanti.dialog.countQtyBinCheck_msg=Vous devez s\u00E9lectionner l'emplacement du bac avant de saisir la quantit\u00E9 compt\u00E9e.
avanti.dialog.creditNoteChangeStatus=Voulez-vous vraiment modifier le statut de la note de cr\u00E9dit?
avanti.dialog.creditNoteDistributionMissingAccounts_msg=Cet avoir ne sera pas comptabilis\u00E9, car une ou plusieurs lignes de r\u00E9partition ne sont pas associ\u00E9es au compte g\u00E9n\u00E9ral. Veuillez consulter les d\u00E9tails dans l'onglet \u00AB\u00A0R\u00E9partitions\u00A0\u00BB.
avanti.dialog.creditNoteExceedsInvoiceBalance_msg=Le montant du cr\u00E9dit d\u00E9passe le montant d\u00FB {amount} et la note de cr\u00E9dit ne sera pas enregistr\u00E9e.
avanti.dialog.creditNoteJournalEntryOrDistributionTypeOutOfBalance_msg=Cette note de cr\u00E9dit ne sera pas enregistr\u00E9e dans le syst\u00E8me comptable car l'\u00E9criture de journal est d\u00E9s\u00E9quilibr\u00E9e.
avanti.dialog.custCSRIsInactive=Le repr\u00E9sentant de service par d\u00E9faut du client est inactif. Il ne sera pas utilis\u00E9.
avanti.dialog.custClassSalesRepIsInactive=Le repr\u00E9sentant commercial par d\u00E9faut de la classe client est inactif. Il ne sera pas utilis\u00E9.
avanti.dialog.custContactInactiveCheck=Ce contact est le contact principal de ce client. Vous devez le supprimer avant de le d\u00E9sactiver.
avanti.dialog.custSalesRepIsInactive=Le repr\u00E9sentant commercial par d\u00E9faut du client est inactif. Il ne sera pas utilis\u00E9.
avanti.dialog.customer.inactive.duplicateOrder_msg=Le client est inactif. La commande ne sera pas dupliqu\u00E9e.
avanti.dialog.customer.inactive.orderNotDeliver_msg=Le client est inactif. La commande ne sera pas lib\u00E9r\u00E9e.
avanti.dialog.cyberSourceInvalidFieldsData=Un ou plusieurs champs de la demande contiennent des donn\u00E9es non valides. Champs non valides \:
avanti.dialog.cyberSourceMissRequiredFields=Il manque un ou plusieurs champs obligatoires \u00E0 la demande. Champs non valides\:
avanti.dialog.deactivateWarehouseRemoveFromPlants=Cet entrep\u00F4t est d\u00E9fini comme entrep\u00F4t principal pour une ou plusieurs usines de l'organisation. Veuillez le supprimer avant de d\u00E9sactiver l'entrep\u00F4t.
avanti.dialog.deactivateWarehouseRemoveFromWorktype=La d\u00E9sactivation de cet entrep\u00F4t le supprimera de tous les types de travail s'il est d\u00E9fini comme entrep\u00F4t principal. Voulez-vous continuer ?
avanti.dialog.defaultCustomerNotConfiguredForWorkTemplate=Aucun client par d\u00E9faut n'est configur\u00E9 pour le mod\u00E8le de travail.
avanti.dialog.defaultPlantHasChanged=L'usine par d\u00E9faut a \u00E9t\u00E9 modifi\u00E9e. Vous devez vous d\u00E9connecter et vous reconnecter pour appliquer les modifications \u00E0 votre session.
avanti.dialog.defaultSheetUtilization_msg=L\u2019utilisateur doit entrer un pourcentage d\u2019utilisation de la feuille par d\u00E9faut pour les feuilles partielles avant que la fonctionnalit\u00E9 de feuille partielle ne fonctionne
avanti.dialog.defaultSheetUtilization_title=% d\u2019utilisation des feuilles par d\u00E9faut
avanti.dialog.deleteEstOrdError_msg=Une erreur s'est produite lors du processus de suppression.\r\nVeuillez vous d\u00E9connecter et vous reconnecter pour supprimer cet enregistrement.
avanti.dialog.deleteEstOrdError_title=Supprimer l'erreur
avanti.dialog.deleteExceptionCustomerToQuickBooksOnlineIntegration=Vous ne pouvez pas supprimer ce client car il a \u00E9t\u00E9 synchronis\u00E9 avec QuickBooks en ligne, veuillez le d\u00E9finir sur inactif.
avanti.dialog.deleteGLAccount_msg=Impossible de supprimer un compte g\u00E9n\u00E9ral utilis\u00E9 sur un ou plusieurs registres.
avanti.dialog.deleteLineItmWithJobTransaction_error=Impossible de supprimer une ligne Valid\u00E9 pour pr\u00E9paration si le travail qui lui est associ\u00E9 comporte des transactions.
avanti.dialog.deleteLineItmWithJobTransaction_title=Erreur lors de la suppression de la ligne de commande client
avanti.dialog.deleteMsg=\u00CAtes-vous s\u00FBr de vouloir supprimer l'enregistrement s\u00E9lectionn\u00E9?
avanti.dialog.deleteOrdEstWorkTemplateLinkedToItem=Ce mod\u00E8le de travail est li\u00E9 \u00E0 un article en stock. \u00CAtes-vous certain de vouloir supprimer cet enregistrement?
avanti.dialog.deletePlant_msg=Vous ne pouvez pas supprimer cette division car elle est actuellement r\u00E9f\u00E9renc\u00E9e dans l'organisation, l'entrep\u00F4t, le devis ou la commande client.
avanti.dialog.deleteSelectedMsg=\u00CAtes-vous s\u00FBr de vouloir supprimer l'enregistrement s\u00E9lectionn\u00E9?
avanti.dialog.deleteTitle=Effacer
avanti.dialog.deleteTransferOrder_msg=Vous ne pouvez pas supprimer une commande cr\u00E9\u00E9e \u00E0 partir d'un plan de projet interprofessionnel.
avanti.dialog.deleteUOMinUse_msg=L'unit\u00E9 de mesure est utilis\u00E9e. Vous ne pouvez pas supprimer une unit\u00E9 de mesure utilis\u00E9e.
avanti.dialog.deletingNotOpenSalesOrder=L'ordre d'\u00E9change a \u00E9t\u00E9 publi\u00E9 ; veuillez annuler la sortie.
avanti.dialog.deletingPostedCreditNote=**La note de cr\u00E9dit non appliqu\u00E9e cr\u00E9\u00E9e par le retour a \u00E9t\u00E9 publi\u00E9e et le retour n'est plus disponible pour annulation.**
avanti.dialog.depositsNotSupportedMultiCurrency_msg=La fonctionnalit\u00E9 d'ajout d'un acompte \u00E0 une commande client est d\u00E9sactiv\u00E9e pour les clients dans une devise diff\u00E9rente de celle de l'organisation. D\u00E9posez d'abord l'argent sur les re\u00E7us de caisse, puis appliquez le d\u00E9p\u00F4t.
avanti.dialog.descartesExportSuccessfully=Export\u00E9 avec succ\u00E8s
avanti.dialog.descartesIntegrationError_title=Descartes Route Planner erreur
avanti.dialog.digitalCustomInk_msg=Vous avez s\u00E9lectionn\u00E9 un type d\u2019encre sp\u00E9cial. \r\nVeuillez activer les encres sp\u00E9ciales, sp\u00E9cifier le nombre d\u2019encres que vous souhaitez utiliser, \r\net configurer les encres sur l\u2019onglet Encre.
avanti.dialog.digitalCustomInk_title=Encre sp\u00E9ciale utilis\u00E9e avec la presse num\u00E9rique
avanti.dialog.disableEnterDetailForEstOrdWorkTemplate=La saisie des d\u00E9tails n'est pas disponible pour les mod\u00E8les de travail copi\u00E9s \u00E0 partir des estimations ou des commandes clients. Toutes les modifications requises doivent \u00EAtre apport\u00E9es au mod\u00E8le de travail.
avanti.dialog.divPlantChangeException_batchInvoicesExist=L'utilisateur actuel dispose d'enregistrements de factures par lots non trait\u00E9s. Vous devez soit effacer le lot, soit cr\u00E9er les factures avant de changer le fitler de division ou d'usine.
avanti.dialog.doMayoDataFlush?=Cela supprimera tous les clients, contacts clients et articles en stock. Es-tu s\u00FBr de vouloir faire \u00E7a?
avanti.dialog.downloadFile_title=T\u00E9l\u00E9charger un fichier
avanti.dialog.duplicateProductingPlantRecord_msg=Cette usine de production existe d\u00E9j\u00E0.
avanti.dialog.dynamicsIntegration.accountSync=Synchronisation de compte
avanti.dialog.dynamicsIntegration.createCashReceipt=Les recettes mon\u00E9taires
avanti.dialog.dynamicsIntegration.createPayablesInvoice=Facture fournisseur
avanti.dialog.dynamicsIntegration.createPurchaseOrder=Bon de commande
avanti.dialog.dynamicsIntegration.createSalesInvoice=Facture de vente
avanti.dialog.dynamicsIntegration.customerSync=Synchronisation client
avanti.dialog.dynamicsIntegration.inventoryTransactions=Transactions d'inventaire
avanti.dialog.dynamicsIntegration.productionReceipts=Re\u00E7us de production
avanti.dialog.dynamicsIntegration.purchaseOrderReceipts=Re\u00E7us de bons de commande
avanti.dialog.dynamicsIntegration.supplierSync=Synchronisation des fournisseurs
avanti.dialog.dynamicsIntegration.wipRegister=Registre WIP & FG
avanti.dialog.employeeAlreadyHasOpenRegister=Registre\u00A0\: <regnum> doit \u00EAtre affich\u00E9 avant que le registre puisse \u00EAtre attribu\u00E9 \u00E0 cet employ\u00E9.
avanti.dialog.employeeNotPartOfTeam=L'employ\u00E9 s\u00E9lectionn\u00E9 ne fait pas partie de votre \u00E9quipe.
avanti.dialog.enterBatchID=Veuillez entrer l'ID du lot
avanti.dialog.enterCurProdQty=Veuillez entrer une quantit\u00E9 produite.
avanti.dialog.enterMessage=Veuillez entrer un message
avanti.dialog.enter_po_update_po_number_header=Bon de commande
avanti.dialog.errorRecalculatingTimeNeeded=Une erreur s'est produite lors de la tentative de recalcul du temps de jalon n\u00E9cessaire. Le jalon devra \u00EAtre r\u00E9tabli \u00E0 sa ressource d'origine.
avanti.dialog.errorRunningOptimizeSchedule=Une erreur s'est produite lors de l'ex\u00E9cution de la planification d'optimisation. La planification a \u00E9t\u00E9 r\u00E9tablie comme avant l'ex\u00E9cution d'Optimize.\r\n\r\nLes d\u00E9tails ont \u00E9t\u00E9 enregistr\u00E9s. Veuillez contacter le support Avanti.
avanti.dialog.excced60daysDateRange=Vous devez s\u00E9lectionner une plage de dates inf\u00E9rieure ou \u00E9gale \u00E0 60 jours. Veuillez ajuster votre plage de dates et r\u00E9ex\u00E9cuter le rapport
avanti.dialog.excced7daysDateRange=La plage de dates de d\u00E9but de travail ne peut pas d\u00E9passer une plage de 7 jours.
avanti.dialog.exchangeUOMMissing=L'UOM d'\u00E9change est manquant dans le num\u00E9ro de ligne
avanti.dialog.exportCompleted_msg=Exportation termin\u00E9e.
avanti.dialog.extendedLinePriceOverride_msg=Attention \: Les prix ont \u00E9t\u00E9 verrouill\u00E9s sur une ou plusieurs lignes en raison de d\u00E9rogations appliqu\u00E9es. Utilisez le bouton R\u00E9initialisation calculatrice pour d\u00E9bloquer les prix si vous souhaitez faire un ordre de modification factur\u00E9 sur cette ligne. Les remplacements peuvent \u00EAtre r\u00E9utilis\u00E9s apr\u00E8s avoir apport\u00E9 vos modifications, si d\u00E9sir\u00E9.
avanti.dialog.extendedLinePriceOverride_title=Remplacement du prix de la ligne d\u00E9tect\u00E9
avanti.dialog.fileExportNoData=Il n'y a pas de donn\u00E9es \u00E0 exporter
avanti.dialog.fileExportSuccess=Fichier export\u00E9 avec succ\u00E8s
avanti.dialog.fileExportUnsuccessful=\u00C9chec de l'exportation
avanti.dialog.foldPatternError_title=Erreur de mod\u00E8le de pli
avanti.dialog.folderTooSmall_msg=La taille des garnitures est trop petite pour tous les dossiers
avanti.dialog.folderTooSmall_title=Aucun dossier trouv\u00E9
avanti.dialog.groupMustHave2Sigs=Un groupe doit avoir au moins 2 signatures
avanti.dialog.headlessClientCouldntBeCreated=Vous n'avez pas assez de licences utilisateur disponibles pour d\u00E9marrer un processeur suppl\u00E9mentaire pour ce lot. Souhaitez-vous continuer malgr\u00E9 tout\u00A0? Vous ne pourrez pas quitter <param1> pendant l'ex\u00E9cution de ce processus.
avanti.dialog.importDynamicsGP_disable_billing_codes=Ne pas afficher l'onglet Codes de facturation dans les devis et les commandes
avanti.dialog.importDynamicsGP_disable_billing_codes_tooltip=Si coch\u00E9, l'onglet Codes de facturation et le calcul du code de facturation ne seront pas actifs
avanti.dialog.importDynamicsGP_purchaseOrderIntegration=Activer l'envoi de bons de commande au GP dans la gestion des bons de commande
avanti.dialog.importDynamicsGP_sync_terms=Synchroniser les termes en fonction de
avanti.dialog.inLinearFeet=(en pieds lin\u00E9aires)
avanti.dialog.inValidProducingPlant_msg=Plante productrice non valide d\u00E9tect\u00E9e.
avanti.dialog.inactiveSalesOrderLine=Cette commande client contient les articles de stock inactifs suivants, qui doivent \u00EAtre remplac\u00E9s ou r\u00E9activ\u00E9s avant que cette commande puisse \u00EAtre lanc\u00E9e \:\r\n<param1>
avanti.dialog.inactiveSubstrateWarning=L'\u00E9l\u00E9ment de substrat actuel est inactif et une nouvelle s\u00E9lection doit \u00EAtre effectu\u00E9e.
avanti.dialog.inactiveTasksInEstimate=Le devis contient les t\u00E2ches inactives suivantes, qui doivent \u00EAtre remplac\u00E9es ou r\u00E9activ\u00E9es\:
avanti.dialog.inactiveWorkTypeInItem=Cette commande client contient les \u00E9l\u00E9ments de ligne suivants avec des mod\u00E8les de travail inactifs, qui doivent \u00EAtre remplac\u00E9s ou r\u00E9activ\u00E9s avant que cette commande puisse \u00EAtre lanc\u00E9e \: <param1>
avanti.dialog.inventoryLabelExportFailed_msg=\u00C9chec de l'exportation automatique des \u00E9tiquettes d'inventaire\u00A0\:
avanti.dialog.invoiceCreationStillRunning_msg=Il semble que la cr\u00E9ation de facture soit toujours en cours pour cet utilisateur dans le client headelss. Voulez-vous commencer \u00E0 cr\u00E9er des factures group\u00E9es d\u00E8s maintenant\u00A0? (Il peut en r\u00E9sulter des factures en double pour les commandes)
avanti.dialog.invoiceDatevalidationErrorMessage=Vous avez entr\u00E9 une \u00ABdate de facturation\u00BB non valide. Toutes les dates doivent \u00EAtre le ou apr\u00E8s le 1er janvier 1900.
avanti.dialog.invoiceDepositsNotSupportedMultiCurrency_msg=Cette fonctionnalit\u00E9 est d\u00E9sactiv\u00E9e pour les clients dans une devise diff\u00E9rente de celle de l'organisation. Vous pouvez enregistrer le paiement dans les re\u00E7us de caisse.
avanti.dialog.invoiceDistributionMissingAccounts_msg=Cette facture ne sera pas comptabilis\u00E9e, car une ou plusieurs lignes de r\u00E9partition ne contiennent pas de compte g\u00E9n\u00E9ral. Veuillez consulter les d\u00E9tails dans l'onglet \u00AB\u00A0R\u00E9partitions\u00A0\u00BB.
avanti.dialog.invoiceDueDateValidationErrorMessage=Vous avez entr\u00E9 une \u00ABdate d'\u00E9ch\u00E9ance de la facture\u00BB non valide. Toutes les dates doivent \u00EAtre le ou apr\u00E8s le 1er janvier 1900.
avanti.dialog.invoiceJournalEntryOutOfBalance_msg=Cette facture ne sera pas enregistr\u00E9e dans le syst\u00E8me comptable car l'\u00E9criture au journal est d\u00E9s\u00E9quilibr\u00E9e. Veuillez consulter les d\u00E9tails dans l'onglet Entr\u00E9es de journal.
avanti.dialog.invoiceTransactionDateValidationErrorMessage=Vous avez entr\u00E9 une "Date de transaction" non valide. Toutes les dates doivent \u00EAtre le ou apr\u00E8s le 1er janvier 1900.
avanti.dialog.itemCodeHasAlreadyBeenUsed=Ce Code Article a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9. Veuillez en s\u00E9lectioner un autre.
avanti.dialog.itemLookup_title=Recherche d'article
avanti.dialog.jobCannotCompleteByBasedOnDate=Ce travail ne peut pas \u00EAtre termin\u00E9 avant la date Bas\u00E9 sur.
avanti.dialog.jobCannotCompleteByDueDate=Ce travail ne peut pas \u00EAtre termin\u00E9 \u00E0 la date pr\u00E9vue.
avanti.dialog.jobCantBeDoneByExpectedShipDate=Le travail ne peut pas \u00EAtre termin\u00E9 \u00E0 la date d'exp\u00E9dition pr\u00E9vue.
avanti.dialog.jobCostRegisterHasNewCosts=Des frais suppl\u00E9mentaires ont \u00E9t\u00E9 inscrits sur les emplois en cours de rapprochement sur ce registre. Veuillez revoir le registre \u00E0 nouveau pour r\u00E9cup\u00E9rer ces co\u00FBts, puis le registre pourra \u00EAtre affich\u00E9.
avanti.dialog.jobCostRegisterHasNewCosts_ProductionReceipts=Des co\u00FBts de r\u00E9ception de production suppl\u00E9mentaires ont \u00E9t\u00E9 saisis sur les travaux en cours de rapprochement sur ce registre. Veuillez afficher le registre des re\u00E7us de production, puis revoir le registre WIP & FG pour r\u00E9cup\u00E9rer ces co\u00FBts, puis le registre peut \u00EAtre affich\u00E9.
avanti.dialog.jobHasBeenPutOnHoldBy=Ce travail a \u00E9t\u00E9 suspendu par <param1>
avanti.dialog.laminatingSheetTooSmall_msg=La feuille de plastification est trop petite
avanti.dialog.laminatingSheetTooSmall_title=Feuille de plastification trop petite
avanti.dialog.lineGangProblem_msg=Vous devez supprimer la ou les lignes de gang vides avant d\u2019enregistrer.
avanti.dialog.lineGangProblem_title=Gang de ligne vide d\u00E9tect\u00E9
avanti.dialog.lineGang_addSectionTask_msg=Vous ne pouvez pas ajouter de t\u00E2ches \u00E0 une section qui fait partie d\u2019un gang.
avanti.dialog.lineGang_deleteLine_msg=Vous ne pouvez pas supprimer une ligne de gang contenant une section group\u00E9e.
avanti.dialog.lineGang_deleteSectionTask_msg=Vous ne pouvez pas supprimer des t\u00E2ches sur une section qui fait partie d\u2019un gang.
avanti.dialog.lineGang_duplicateSection_msg=Vous ne pouvez pas dupliquer une section de gang ou \r\ntoute section qui fait partie du gang.
avanti.dialog.lineGang_duplicateUpdateLine_msg=Vous ne pouvez pas dupliquer ou mettre \u00E0 jour une ligne gang ou \r\nune ligne de d\u00E9tail contenant une section group\u00E9e.
avanti.dialog.lineGang_multipleQtys_msg=Vous ne pouvez pas avoir plusieurs quantit\u00E9s sur une ligne Gang ou \r\nune ligne de d\u00E9tail contenant une section group\u00E9e.
avanti.dialog.lineGang_title=Gang de ligne
avanti.dialog.loadThumbnail_msg=Aper\u00E7u disponible pour gif, png, jpeg et PDF
avanti.dialog.logoutMsg=Vous avez \u00E9t\u00E9 d\u00E9connect\u00E9 d'Avanti Slingshot par l'administrateur syst\u00E8me
avanti.dialog.logoutOtherUsers=Veuillez demander \u00E0 tous les autres utilisateurs de se d\u00E9connecter du syst\u00E8me pour formater les registres.
avanti.dialog.logoutUser=Voulez-vous vraiment d\u00E9connecter cet utilisateur d'Avanti Slingshot?
avanti.dialog.logoutUser_title=D\u00E9connecter l'utilisateur
avanti.dialog.logoutUsers=Voulez-vous vraiment d\u00E9connecter tous les utilisateurs d'Avanti Slingshot?
avanti.dialog.logoutUsers_title=D\u00E9connecter les utilisateurs
avanti.dialog.longTimeEntryWarningShift=L\u2019entr\u00E9e \#<param1> a un temps \u00E9coul\u00E9 sup\u00E9rieur \u00E0 16 heures. \u00CAtes-vous s\u00FBr de vouloir confirmer cela?
avanti.dialog.maximumNumberofJobs=Le nombre maximum d'emplois pouvant \u00EAtre s\u00E9lectionn\u00E9s dans la plage de num\u00E9ros d'emploi est $$. Vous pouvez modifier ce param\u00E8tre dans les Pr\u00E9f\u00E9rences syst\u00E8me.
avanti.dialog.milestoneHasBlankDependencies=Le jalon '<param1>' a des d\u00E9pendances vides. Veuillez choisir 'Recr\u00E9er des jalons' pour corriger et contacter le support Avanti pour signaler le probl\u00E8me.
avanti.dialog.milestoneHasCosts=Le jalon s\u00E9lectionn\u00E9 a commenc\u00E9 ou co\u00FBte et ne peut pas \u00EAtre supprim\u00E9.
avanti.dialog.missingCommercialInvoiceInformation_proceed?=Il n'y a pas suffisamment d'informations pour g\u00E9n\u00E9rer une facture commerciale. Voulez-vous continuer?'
avanti.dialog.missingDetailLineItems_msg=Cette commande n'a pas d'\u00E9l\u00E9ments de ligne de d\u00E9tail et ne peut pas \u00EAtre lanc\u00E9e.\r\nCr\u00E9ez d'abord un \u00E9l\u00E9ment de ligne de d\u00E9tail, puis validez la commande.
avanti.dialog.missingDetailLineItems_title=\u00C9l\u00E9ments de ligne de d\u00E9tail manquants
avanti.dialog.missingFOBAddress=Une 'Adresse FOB' doit \u00EAtre s\u00E9lectionn\u00E9e lorsque 'FOB Client' est activ\u00E9.
avanti.dialog.missingFinishGoodItem=L'article bon fini doit \u00EAtre d\u00E9fini dans la ligne de commande client.
avanti.dialog.missingPackingSlipShipLocationReference=Impossible de d\u00E9terminer la r\u00E9f\u00E9rence de l'adresse d'exp\u00E9dition de destination sur le bon de livraison\: <packslip>, qui est requise par AvaTax. Veuillez v\u00E9rifier que le bon de livraison a une adresse de livraison valide.
avanti.dialog.missingPlant=Il manque une usine \u00E0 la ligne de revenus de l'usine.
avanti.dialog.missingSalesOrderShipLocationReference=Impossible de d\u00E9terminer la r\u00E9f\u00E9rence de l'adresse d'exp\u00E9dition de destination sur la commande client\: <sales_order>, qui est requise par AvaTax. Veuillez v\u00E9rifier tous les emplacements d'exp\u00E9dition pour cette commande client.
avanti.dialog.mobileDevice_ExceedLicense=Vous avez utilis\u00E9 toutes les autorisations d'appareils mobiles disponibles. Veuillez retirer les appareils inutilis\u00E9s ou vous pouvez en acheter d'autres par l'interm\u00E9diaire de votre repr\u00E9sentant commercial Avanti.
avanti.dialog.mobileDevice_NoLicense=Aucune licence d'appareil mobile trouv\u00E9e. Veuillez contacter votre repr\u00E9sentant commercial Avanti.
avanti.dialog.mountingSheetTooSmall_msg=La feuille de montage est trop petite
avanti.dialog.mountingSheetTooSmall_title=Feuille de montage trop petite
avanti.dialog.movableUnitNotFoundFromAddOrder=L'unit\u00E9 mobile n'a pas pu \u00EAtre mise en correspondance avec une liste de pr\u00E9l\u00E8vement d'ex\u00E9cution existante avec le statut \u00AB Pr\u00E9l\u00E8vement - Dans la zone de transit \u00BB, o\u00F9 ce pr\u00E9l\u00E8vement/commande utilise la m\u00EAme adresse d'exp\u00E9dition que cette exp\u00E9dition.
avanti.dialog.multiShipCheckPlantQuantities_msg=La quantit\u00E9 de plante a chang\u00E9 pour une ou plusieurs plantes. \r\nVeuillez vous assurer de v\u00E9rifier l'allocation de quantit\u00E9 de l'emplacement d'exp\u00E9dition de l'usine.
avanti.dialog.multiShipPlantError_msg=Les quantit\u00E9s d'usine \u00E0 exp\u00E9ditions multiples ne sont pas correctement r\u00E9parties. Veuillez corriger les lignes d'installation avec les soldes restants.
avanti.dialog.multipleBoxesWithDifferentPalletIDs=L'enregistrement de nombre de bo\u00EEtes correspondant contient d\u00E9sormais plusieurs enregistrements d'onglet de bo\u00EEtes, avec diff\u00E9rents ID de palette. Vous devrez modifier manuellement l'enregistrement du nombre de bo\u00EEtes.
avanti.dialog.negativeInvoiceSubTotal=Impossible d'enregistrer la facture avec un sous-total n\u00E9gatif d'une ligne de production n\u00E9gative.
avanti.dialog.negativeInvoiceTotal=Le total de la facture ne peut pas \u00EAtre n\u00E9gatif.
avanti.dialog.no=Non
avanti.dialog.noCustomer_warning=Aucun client n\u2019est associ\u00E9 \u00E0 cette commande.
avanti.dialog.noDefaultRefundDocumentStream=Vous n'avez pas de flux de documents de remboursement par d\u00E9faut. Veuillez le d\u00E9finir dans le programme de configuration des num\u00E9ros de document pour continuer.
avanti.dialog.noOutsourcedServicesTasks_msg=Vous ne pouvez pas supprimer les t\u00E2ches de production de cette section car il n'y a pas de t\u00E2che de service externalis\u00E9. Veuillez ajouter une t\u00E2che de service externalis\u00E9 (par exemple, rachat) pour continuer.
avanti.dialog.noPlantForSelectedWarehouse=Il n'y a pas de division pour l'entrep\u00F4t s\u00E9lectionn\u00E9.
avanti.dialog.noTaskCostLinkForMilestoneGroup_message=La norme d'estimation relative \u00E0 cette op\u00E9ration ne contient pas de liens de co\u00FBts et ne peut pas \u00EAtre utilis\u00E9e. Veuillez contacter votre administrateur Avanti.
avanti.dialog.notEnoughLeadTimeToProdAndShip=La date d'exp\u00E9dition pr\u00E9vue ne peut pas \u00EAtre inf\u00E9rieure ou \u00E9gale \u00E0 la date d'aujourd'hui.
avanti.dialog.nothingToShip=Il n'y a rien \u00E0 exp\u00E9dier
avanti.dialog.ok=D'accord
avanti.dialog.opAlreadyCompleted=d\u00E9j\u00E0 termin\u00E9 cette op\u00E9ration. \u00CAtes-vous s\u00FBr de vouloir recommencer?
avanti.dialog.opCatCodeHasAlreadyBeenUsed=Ce code de cat\u00E9gorie d'op\u00E9ration a d\u00E9j\u00E0 \u00E9t\u00E9 utilis\u00E9 pour ce d\u00E9partement. Veuillez en s\u00E9lectioner un autre.
avanti.dialog.opPredecessorsNotCompletedContinue=Les op\u00E9rations pr\u00E9c\u00E9dentes ne sont pas termin\u00E9es, voulez-vous continuer?
avanti.dialog.openReturnToDelete=Ce retour n'est pas en statut Ouvert. Changez le statut en Ouvert pour supprimer.
avanti.dialog.optimizeThisResource?=Souhaitez-vous optimiser toutes les t\u00E2ches pour cette ressource et son pool de travail en fonction de l'heure actuelle\u00A0? En cliquant sur "Non", les t\u00E2ches resteront \u00E0 leur place actuelle dans le planning.
avanti.dialog.orderHasChargebackedLines=Cette campagne comporte des \u00E9l\u00E9ments de campagne qui ont fait l'objet d'un rejet de d\u00E9bit. Il n'est pas conseill\u00E9 de les modifier, car les co\u00FBts ont d\u00E9j\u00E0 \u00E9t\u00E9 captur\u00E9s par la comptabilisation des r\u00E9trofacturations.
avanti.dialog.overwritePalletID=Il y a des enregistrements de bo\u00EEte dans l'onglet Bo\u00EEtes qui ont d\u00E9j\u00E0 un ID de palette. Voulez-vous les remplacer\u00A0?
avanti.dialog.paperLookup_title=Recherche de substrat
avanti.dialog.payroll_export_review_no_data_title=Examen de l'exportation de la paie
avanti.dialog.payroll_export_review_no_data_title_msg=Aucune donn\u00E9e disponible
avanti.dialog.plannedPurchasing.deleteUserOptionsProfile=\u00CAtes-vous s\u00FBr de vouloir supprimer ce profil?
avanti.dialog.pleaseEnterDeviceTag=Veuillez saisir une balise d'appareil
avanti.dialog.pressSheetOverrideExceedsParentSheet=La d\u00E9rogation \u00E0 la feuille de presse ne peut pas d\u00E9passer la taille de la feuille m\u00E8re.
avanti.dialog.pressSheetOverrideExceedsParentSheet_msg=La d\u00E9rogation \u00E0 la feuille de presse ne peut pas d\u00E9passer la taille de la feuille m\u00E8re.
avanti.dialog.pressSheetOverrideExceedsParentSheet_title=Erreur de remplacement de la feuille de presse
avanti.dialog.printShippingLabelsNotSupported=La fonction Imprimer les \u00E9tiquettes d'exp\u00E9dition n'est pas prise en charge par Postes Canada et Purolator.
avanti.dialog.problemSchedulingJob=Probl\u00E8me de planification du travail\: 
avanti.dialog.projectPlanCancelRelease=Plan de projet - Annuler le lancement
avanti.dialog.projectPlanHasBeenShippedCancel_msg=Vous ne pouvez pas annuler cet envoi car un plan de projet associ\u00E9 a \u00E9t\u00E9 exp\u00E9di\u00E9. Veuillez contacter l'usine d'origine pour conna\u00EEtre les options d'exp\u00E9dition.
avanti.dialog.projectPlanHasBeenShippedConfirm_msg=Vous ne pouvez pas confirmer ce bon de livraison car un plan de projet associ\u00E9 a \u00E9t\u00E9 exp\u00E9di\u00E9. Veuillez contacter l'usine d'origine pour conna\u00EEtre les options d'exp\u00E9dition.
avanti.dialog.purchaseReceiptFOBCostDoesNotMatchPurchaseOrder=Le co\u00FBt FOB indiqu\u00E9 sur le re\u00E7u d'achat ne correspond pas au co\u00FBt FOB indiqu\u00E9 sur le bon de commande.
avanti.dialog.purchaseReceiptLandedCostDoesNotMatchPurchaseOrder=Le co\u00FBt global indiqu\u00E9 sur le re\u00E7u d'achat ne correspond pas au co\u00FBt global indiqu\u00E9 sur le bon de commande.
avanti.dialog.purchaseReceiptMissingSupplier_msg=Le fournisseur de fret est requis lors de l'utilisation de la m\u00E9thode de facturation de fret par un tiers.
avanti.dialog.purchaseReceiptRollsReceived_msg=Veuillez saisir la quantit\u00E9 re\u00E7ue avant de saisir les rouleaux re\u00E7us.
avanti.dialog.pushUsedQtyFirst=Si ce fournisseur est pouss\u00E9 \u00E0 toutes les quantit\u00E9s, il en r\u00E9sulterait que certaines quantit\u00E9s (<param1>) n'auraient pas de fournisseur d'occasion. Veuillez d'abord pousser le fournisseur d'occasion pour cette quantit\u00E9. Souhaitez-vous pousser le fournisseur d'occasion maintenant?
avanti.dialog.qtyLessThanPickTransQty=Il existe une transaction Fulfillment Pick pour ce bac pour une quantit\u00E9 de <param1>. Vous ne pouvez pas r\u00E9duire la quantit\u00E9 en dessous.
avanti.dialog.quantityNotReservedToJob_msg=La quantit\u00E9 entr\u00E9e est sup\u00E9rieure au mat\u00E9riel affect\u00E9 \u00E0 ce travail.
avanti.dialog.quantityRejectedHigherThanReceived_msg=La quantit\u00E9 rejet\u00E9e ne peut pas \u00EAtre sup\u00E9rieure \u00E0 la quantit\u00E9 re\u00E7ue.
avanti.dialog.rateShopAllAddresses?=Voulez-vous vraiment \u00E9valuer toutes les adresses de magasin\u00A0?
avanti.dialog.receiptQuantityAdjustmentErrorMoreThanAllowed_msg=La quantit\u00E9 d'ajustement ne peut pas d\u00E9passer la quantit\u00E9 totale de r\u00E9ception de production.
avanti.dialog.receiptQuantityAdjustmentError_msg=Voulez-vous vraiment continuer si le solde apr\u00E8s r\u00E9ception d\u00E9passe la quantit\u00E9 command\u00E9e\u00A0?
avanti.dialog.recordEditLock_msg2=<<user>> a commenc\u00E9 \u00E0 modifier l'enregistrement actuel le <<date>>, \u00E0 <<time>>.\r\nSi vous \u00EAtes certain que <<user1>> ne modifie plus cet enregistrement,\r\nchoisissez 'Supprimer le verrouillage'.
avanti.dialog.refreshDistributionConfirm_msg=Souhaitez-vous actualiser la r\u00E9partition de la ou des factures en \u00E9chec ?
avanti.dialog.refreshDistributionSuccess_msg=Mise \u00E0 jour r\u00E9ussie de la distribution pour les factures qui avaient \u00E9chou\u00E9.\r\nUn examen du registre doit \u00EAtre effectu\u00E9 pour permettre une nouvelle ex\u00E9cution du registre \u00E0 publier sur le m\u00E9decin g\u00E9n\u00E9raliste.
avanti.dialog.refundExceedsCreditNoteBalance=Le remboursement ne peut pas d\u00E9passer le solde de la note de cr\u00E9dit.
avanti.dialog.refundExceedsDepositBalance=Le remboursement ne peut pas d\u00E9passer le solde du d\u00E9p\u00F4t.
avanti.dialog.registerNumFormatting=\u00CAtes-vous s\u00FBr de vouloir proc\u00E9der \u00E0 ce changement car la base de donn\u00E9es sera mise \u00E0 jour avec le nouveau format de registre\u00A0?
avanti.dialog.registerNumLength=La valeur par d\u00E9faut est de 4 chiffres, et vous ne pouvez pas descendre en dessous de 4 chiffres ou moins que le r\u00E9glage actuel une fois qu'un registre a \u00E9t\u00E9 actif pour l'entreprise, et vous ne pouvez pas d\u00E9passer 16 chiffres.
avanti.dialog.registerProcess_noAccessToDivPlant_msg=Vous n'\u00EAtes pas autoris\u00E9 \u00E0 modifier ce registre avec les param\u00E8tres actuels de la division/de l'usine.
avanti.dialog.removeInvoiceLockSelected_msg=\u00CAtes-vous s\u00FBr de vouloir supprimer le verrouillage des factures sur la ou les factures s\u00E9lectionn\u00E9es\u00A0?
avanti.dialog.removeInvoiceLock_msg=\u00CAtes-vous s\u00FBr de vouloir supprimer le verrouillage de la facture s\u00E9lectionn\u00E9e\u00A0?
avanti.dialog.reopenAppliedCreditNote=Voulez-vous vraiment rouvrir cette note de cr\u00E9dit appliqu\u00E9e\u00A0?
avanti.dialog.reservedFlushSkipped_msg={0} r\u00E9serv\u00E9 n'a pas pu \u00EAtre vid\u00E9 car la disponibilit\u00E9 de la quantit\u00E9 de l'article a chang\u00E9 avant qu'il puisse \u00EAtre vid\u00E9.
avanti.dialog.reservedFlush_msg={0} r\u00E9serv\u00E9s ont \u00E9t\u00E9 vid\u00E9s.
avanti.dialog.resetInvoiceToPostSelected_msg=\u00CAtes-vous s\u00FBr d'avoir r\u00E9solu les erreurs d'int\u00E9gration et souhaitez-vous r\u00E9initialiser la facture pour la comptabiliser dans le syst\u00E8me comptable\u00A0?
avanti.dialog.resetJWTTokenMsg=Voulez-vous remplacer le jeton existant ? Si vous remplacez le jeton de service Web, toutes les int\u00E9grations existantes utilisant le jeton pr\u00E9c\u00E9dent \u00E9choueront lors de l'authentification.
avanti.dialog.resetJWTTokenTitle=Remplacer le jeton de service
avanti.dialog.resetRateShoppingInfo?=\u00CAtes-vous s\u00FBr de vouloir r\u00E9initialiser toutes les informations d'achat de tarifs pour cette adresse \u00E0 son \u00E9tat par d\u00E9faut\u00A0?
avanti.dialog.retainCarrierPunchOutDetails=Voulez-vous conserver le tarif n\u00E9goci\u00E9, le tarif publi\u00E9, les revenus de fret et les frais de transport?
avanti.dialog.returnQtyOverOrdered=La quantit\u00E9 totale ne peut pas \u00EAtre sup\u00E9rieure \u00E0 la quantit\u00E9 command\u00E9e.
avanti.dialog.returnWorkFlowActionCancelReturn_msg=Confirmez l'action s\u00E9lectionn\u00E9e \: supprimez la note de cr\u00E9dit non appliqu\u00E9e, supprimez toutes les quantit\u00E9s r\u00E9approvisionn\u00E9es dans l'inventaire et supprimez la commande client pour tout \u00E9change.
avanti.dialog.returnWorkFlowCreateActionComplete_msg=Confirmez l'action s\u00E9lectionn\u00E9e \: cr\u00E9ez une note de cr\u00E9dit non appliqu\u00E9e, renvoyez toutes les quantit\u00E9s r\u00E9approvisionn\u00E9es \u00E0 l'inventaire et cr\u00E9ez une commande client pour tout \u00E9change.
avanti.dialog.rollisAlreadySelected=Le rouleau est d\u00E9j\u00E0 s\u00E9lectionn\u00E9
avanti.dialog.runAddressImport?=Voulez-vous vraiment ex\u00E9cuter l'importation d'adresse\u00A0?
avanti.dialog.salesOrderIsStillImporting=L'importation de cette commande client est en cours. Ses d\u00E9tails ne peuvent pas \u00EAtre affich\u00E9s ou modifi\u00E9s tant que l'importation n'est pas termin\u00E9e.
avanti.dialog.sectionGangProblem_msg=Vous devez supprimer la section vide gang(s) avant d\u2019enregistrer.
avanti.dialog.sectionGangProblem_title=Gang de section vide d\u00E9tect\u00E9
avanti.dialog.selectCrystalReportFileUpload=S\u00E9lectionnez un fichier de rapport Crystal \u00E0 t\u00E9l\u00E9charger
avanti.dialog.selectDivisionPlant_msg=Votre s\u00E9lection de filtre division/usine doit correspondre \u00E0 la division et \u00E0 l'usine du registre.
avanti.dialog.sf_selectJobBeforeAddingMore=Vous devez s\u00E9lectionner un dossier / une section / une op\u00E9ration avant de pouvoir ajouter d'autres dossiers / sections.
avanti.dialog.sheetUtiliz_msg=Feuilles de presse avec...
avanti.dialog.sheetsPerBed_title=Feuilles Par Lit
avanti.dialog.shiftTimes_msg=Vous \u00EAtes sur le point de confirmer les entr\u00E9es pour les heures de d\u00E9but et de fin d'\u00E9quipe.\r\nUne fois confirm\u00E9es, les entr\u00E9es ne peuvent plus \u00EAtre modifi\u00E9es.\r\n\r\nCliquez sur OK pour continuer ou sur Annuler pour annuler la confirmation.
avanti.dialog.shopFloorAddOther_msg=Veuillez s\u00E9lectionner un employ\u00E9, un travail et une section avant d'ajouter d'autres entr\u00E9es.
avanti.dialog.shopFloorAddOther_title=Champs obligatoires manquants
avanti.dialog.shopFloorConfirmQAChecks=Vous devez effectuer les contr\u00F4les d\u2019assurance qualit\u00E9 avant de terminer une op\u00E9ration.
avanti.dialog.shopFloorConfirmQAChecks_Resource=Vous devez effectuer les contr\u00F4les d\u2019assurance qualit\u00E9 avant de compl\u00E9ter une ressource.
avanti.dialog.shopFloorEntry_msg=Choisissez un centre de co\u00FBts pour ajouter des op\u00E9rations suppl\u00E9mentaires.
avanti.dialog.shopFloor_lineGangSectionsOnly=Les sections n'appara\u00EEtront pas si toutes les t\u00E2ches se produisent sur un groupe de lignes
avanti.dialog.splitBillUDFWarning=Vous ne pouvez avoir que 30 champs de facturation fractionn\u00E9s au maximum.
avanti.dialog.statusAlreadySelected=Le statut est d\u00E9j\u00E0 s\u00E9lectionn\u00E9. Vous ne pouvez pas utiliser le m\u00EAme statut plus d'une fois pour purger
avanti.dialog.substrateIncompatibleWithPress=Ce substrat est incompatible avec la presse actuelle. Il ne peut pas \u00EAtre utilis\u00E9.
avanti.dialog.substrateIncompatibleWithPress_useOtherPress=Ce substrat est incompatible avec la presse actuelle. Mais il y a une ou plusieurs autres presses dans le pool qui peuvent l'utiliser. Souhaitez-vous utiliser l'une de ces presses?
avanti.dialog.substrateIncompatibleWithPress_useOtherPressOrRoll2Sheet=Ce substrat est incompatible avec la presse actuelle. Mais il y a une autre presse dans le pool (<param1>) qui peut l'utiliser. Souhaitez-vous utiliser cette presse? (OUI) Ou souhaitez-vous conserver la presse actuelle et utiliser un convertisseur rouleau \u00E0 feuille? (NON)
avanti.dialog.supplierContactInactiveCheck=Ce contact est le contact principal de ce fournisseur. Vous devez le supprimer avant de le d\u00E9sactiver.
avanti.dialog.supplierQuantityBreakException=Veuillez saisir les d\u00E9tails du co\u00FBt au d\u00E9barquement avant d\u2019ajouter des interruptions de quantit\u00E9.
avanti.dialog.systempreferenceAuditLog_title=Journal d'audit des pr\u00E9f\u00E9rences syst\u00E8me
avanti.dialog.thatOrderCantBeFound=Ce num\u00E9ro de Commande est introuvable.
avanti.dialog.thisUserHasNoEmp=L'utilisateur connect\u00E9 n'est pas associ\u00E9 \u00E0 un employ\u00E9. Vous ne pouvez pas ex\u00E9cuter cette fonction sans employ\u00E9. Veuillez vous connecter avec un utilisateur qui a un employ\u00E9.
avanti.dialog.tooManyColorsForWebPress=Nombre de couleurs d\u00E9passe le nombre de tours d'impression \u00BB, veuillez ajuster le nombre de couleurs ou s\u00E9lectionner une presse diff\u00E9rente.
avanti.dialog.trimSizeTooSmall_msg=La t\u00E2che ne calculera pas, la taille de garniture utilis\u00E9e est plus petite que la taille du substrat Min. \r\nEstimation de la norme pour cette t\u00E2che. Vous devez s\u00E9lectionner une t\u00E2che diff\u00E9rente avec un plus petit Min. Taille du substrat d\u00E9finie sur la norme d\u2019estimation.
avanti.dialog.trimSizeTooSmall_title=Taille de garniture trop petite
avanti.dialog.unconfirmedLabor=Travail non confirm\u00E9
avanti.dialog.unconfirmedMaterial=Mat\u00E9riel non confirm\u00E9
avanti.dialog.unconfirmedTime=Horaires de Travail non Confirm\u00E9s
avanti.dialog.unpostedInvoicesAndCreditNotes_title=Factures / notes de cr\u00E9dit non post\u00E9es
avanti.dialog.updateDistribution_title=Distribution des mises \u00E0 jour
avanti.dialog.updateDistribution_tooltip=Rafra\u00EEchir la distribution des factures avec temps et mat\u00E9riel non confirm\u00E9s.
avanti.dialog.updateExchangeRate_msg=Le taux de change sur ce document ne correspond pas au taux de change actuel. Souhaitez-vous mettre \u00E0 jour le taux de change de ce document au taux de change actuel?
avanti.dialog.updateExchangeRate_title=Mettre \u00E0 jour le taux de change?
avanti.dialog.uploadPDFServerError=Le Enfocus PDF Review Server a renvoy\u00E9 une erreur\: {0}. Veuillez corriger le probl\u00E8me et r\u00E9essayer. Voir le journal d'int\u00E9gration pour plus de d\u00E9tails.
avanti.dialog.userManagement_title=Gestion des utilisateurs Slingshot dit
avanti.dialog.uspsNotSupported=La m\u00E9thode d'int\u00E9gration USPS n'est plus prise en charge. Veuillez utiliser USPS-3 \u00E0 la place.
avanti.dialog.validShiftNotFound=Les informations de quart de travail valides pour l'employ\u00E9 sont introuvables.
avanti.dialog.validationReplenishmentReport=Le rapport de r\u00E9approvisionnement n'est valide que pour le type de transaction 'Transfert de bac'.
avanti.dialog.validationRollPlacard=Le rapport Roll Placard n'est valide que pour le type de transaction 'Roll Bin Transfer'.
avanti.dialog.validationTransferSlip=Le rapport de bordereau de transfert n'est valable que pour le type de transaction \u00ABWhse Transfer Out\u00BB.
avanti.dialog.validationTransferSlipPostedDate=La transaction doit \u00EAtre "mise \u00E0 jour" avant d'imprimer ce rapport.
avanti.dialog.verifyDocumentsReceivedInDynamicsGP_title=V\u00E9rifier les documents re\u00E7us dans Dynamics GP
avanti.dialog.viewUnconfirmed_title=Afficher non confirm\u00E9
avanti.dialog.viewUnconfirmed_tooltip=Afficher les entr\u00E9es de main-d'\u0153uvre et de mat\u00E9riel non confirm\u00E9es.
avanti.dialog.vmRoundingWarning=Les utilisateurs de Validation Manager peuvent rencontrer des \u00E9checs dans la version 6.0.2023 en raison de changements dans l'arrondi. Veuillez examiner les estimations qui ont \u00E9chou\u00E9 et vous assurer que les calculs sont corrects. Pour mettre \u00E0 jour les estimations qui \u00E9chouent en raison d'un arrondi, acc\u00E9dez \u00E0 l'onglet Sections d'estimations et recalculez tout, puis actualisez le r\u00E9capitulatif des co\u00FBts dans l'onglet \u00C9l\u00E9ments de ligne, puis relancez Validation Manager.
avanti.dialog.voidCashReceipt=Impossible d'annuler un ticket de caisse qui a d\u00E9j\u00E0 \u00E9t\u00E9 annul\u00E9.
avanti.dialog.voidCashReceiptDeposits=Le re\u00E7u de caisse que vous annulez est un d\u00E9p\u00F4t dont les fonds sont appliqu\u00E9s au d\u00E9p\u00F4t. Vous ne pouvez pas annuler ce re\u00E7u de caisse.
avanti.dialog.warehouse_msg=Cet entrep\u00F4t ne peut pas \u00EAtre supprim\u00E9 car il est associ\u00E9 \u00E0 des transactions de stock historiques. L'entrep\u00F4t doit \u00E0 la place \u00EAtre d\u00E9sactiv\u00E9 s'il n'est plus utilis\u00E9.
avanti.dialog.warningFiscalPeriod=Veuillez s\u00E9lectionner les deux valeurs pour la p\u00E9riode fiscale.
avanti.dialog.warningPleaseEnsureThatYouSelectalleastOnePlant=Veuillez vous assurer de s\u00E9lectionner au moins une plante avant de continuer.
avanti.dialog.warningPleaseEnsureThatYouSelectalleastOneWarehouse=Veuillez vous assurer de s\u00E9lectionner au moins un entrep\u00F4t avant de continuer.
avanti.dialog.warning_cannotCancelPartialReceiptInGp=Impossible d'annuler un bon de commande dans GP qui a \u00E9t\u00E9 partiellement re\u00E7u. Doit annuler manuellement les soldes dans GP.
avanti.dialog.webHadItsAddMRSpoilsIncreased=Ce site Web a vu son Add MR Spoils augment\u00E9 pour correspondre au Web avec le plus grand Add MR Spoils.
avanti.dialog.webHadItsFirstMRSpoilsIncreased=Ce site Web a vu son First MR Spoils augment\u00E9 pour correspondre au Web avec le plus grand First MR Spoils.
avanti.dialog.webHadItsMRSpoilsIncreased=Ce site Web a vu son butin MR augment\u00E9 pour correspondre au Web avec le plus grand butin MR.
avanti.dialog.webHadItsRunSpoilsIncreased=Ce site Web a vu ses Run Spoils augment\u00E9s pour correspondre au Web avec les plus grands Run Spoils.
avanti.dialog.webHadItsSetupSpoilsIncreased=Ce site Web a vu ses spoils de configuration augment\u00E9s pour correspondre au Web avec les spoils de configuration les plus importants.
avanti.dialog.workFlowNotification_title=Notification de flux de travail
avanti.dialog.workTypeCodeRequired=Le code du mod\u00E8le de travail est requis
avanti.dialog.workTypeRequired=Le type de mod\u00E8le de travail est requis
avanti.dialog.yes=Oui
avanti.grid.asCompanyGrid=En tant que r\u00E9seau d'entreprise
avanti.grid.asDefaultCompanyGrid=Comme grille d'entreprise par d\u00E9faut
avanti.grid.asMyDefaultGrid=Comme ma grille par d\u00E9faut
avanti.grid.asNewGrid=Comme une nouvelle grille
avanti.grid.baseGridSaved=La grille de base a \u00E9t\u00E9 enregistr\u00E9e
avanti.grid.companyDefaultGridLoaded=La grille par d\u00E9faut de l'entreprise a \u00E9t\u00E9 charg\u00E9e
avanti.grid.companyDefaultGridNotSaved=La grille par d\u00E9faut de l'entreprise n'a pas \u00E9t\u00E9 enregistr\u00E9e
avanti.grid.companyDefaultGridSaved=La grille par d\u00E9faut de l'entreprise a \u00E9t\u00E9 enregistr\u00E9e
avanti.grid.companyDefaultLoaded=Grille par d\u00E9faut de l'entreprise charg\u00E9e
avanti.grid.companyGridNameExists=Une grille d'entreprise portant ce nom existe d\u00E9j\u00E0. Veuillez utiliser un nom de grille unique.
avanti.grid.companyGridNotSaved=Le r\u00E9seau de l'entreprise n'a pas \u00E9t\u00E9 enregistr\u00E9
avanti.grid.companyGridSaved=La grille de l'entreprise a \u00E9t\u00E9 enregistr\u00E9e
avanti.grid.companySavedGrids=Grilles enregistr\u00E9es par l'entreprise
avanti.grid.editDeleteGrids=Modifier/Supprimer des grilles
avanti.grid.emptyValue=Valeur vide
avanti.grid.enterGridName=Entrez un nom pour la grille\u00A0\:
avanti.grid.enterGridNameOrDefault=Entrez un nom pour la grille ou \u00AB\u00A0par d\u00E9faut\u00A0\u00BB\u00A0\:
avanti.grid.error=Erreur
avanti.grid.gridHasBeenReset=R\u00E9initialisation du r\u00E9seau
avanti.grid.gridLoadError=Erreur de charge du r\u00E9seau
avanti.grid.gridLoadFailed=\u00C9chec du chargement du r\u00E9seau
avanti.grid.gridLoaded=Grille charg\u00E9e
avanti.grid.gridName=Nom de la grille
avanti.grid.gridNameAlreadyExists=Une grille portant ce nom existe d\u00E9j\u00E0. Veuillez utiliser un nom de grille unique ou modifier/supprimer la grille existante.
avanti.grid.gridNameCannotBeEmpty=Le nom de la grille ne peut pas \u00EAtre vide
avanti.grid.gridNameError=Erreur de nom de grille
avanti.grid.gridNameLoaded="{0}" charg\u00E9
avanti.grid.gridNameSaved="{0}" enregistr\u00E9
avanti.grid.gridNotSaved=La grille n'a pas \u00E9t\u00E9 enregistr\u00E9e
avanti.grid.gridReset=R\u00E9initialisation du r\u00E9seau
avanti.grid.gridSaved=Grille enregistr\u00E9e
avanti.grid.isCompanyGrid=Grille d'entreprise
avanti.grid.isSystemGrid=Grille syst\u00E8me
avanti.grid.mySavedGrids=Mes grilles enregistr\u00E9es
avanti.grid.namesUpdatedError=La mise \u00E0 jour des noms de grille a \u00E9chou\u00E9
avanti.grid.namesUpdatedSuccess=Noms de grille mis \u00E0 jour avec succ\u00E8s
avanti.grid.noBaseGrid=Pas de grille de base
avanti.grid.noDefaultGridFound=Aucune grille par d\u00E9faut trouv\u00E9e
avanti.grid.reservedValues=Valeurs r\u00E9serv\u00E9es
avanti.grid.resetGrid=R\u00E9initialiser la grille
avanti.grid.resetToCompanyDefault=R\u00E9initialiser le r\u00E9seau aux valeurs par d\u00E9faut de l'entreprise
avanti.grid.resetToMyDefault=R\u00E9initialiser la grille \u00E0 ma valeur par d\u00E9faut
avanti.grid.saveCurrentGrid=Enregistrer la grille actuelle
avanti.grid.selectToDelete=S\u00E9lectionner pour supprimer
avanti.grid.selectedNameReserved=Le nom s\u00E9lectionn\u00E9 "{0}" est r\u00E9serv\u00E9
avanti.grid.success=Succ\u00E8s
avanti.grid.userDefaultGridLoaded=Votre grille par d\u00E9faut a \u00E9t\u00E9 charg\u00E9e avec succ\u00E8s.
avanti.grid.userDefaultLoaded=Grille par d\u00E9faut de l'utilisateur charg\u00E9e
avanti.group.CostCenter=Op\u00E9ration
avanti.group.Customer=Client
avanti.group.CustomerStock=Stock client
avanti.group.Department=D\u00E9partement
avanti.group.Employee=Employ\u00E9
avanti.group.Equipment=\u00C9quipement
avanti.group.ItemInformation=Informations sur l'\u00E9l\u00E9ment
avanti.group.ItemSalesSummary=R\u00E9sum\u00E9 des ventes
avanti.group.ItemStatistics=Statistiques sur les articles
avanti.group.ItemTaxInformation=Informations fiscales
avanti.group.Operation=Op\u00E9ration
avanti.group.OrderRushCode=Code urgent
avanti.group.PaperBrands=Poids/Finition
avanti.group.PaperDetails=Substrat
avanti.group.PaperProperties=Propri\u00E9t\u00E9s du substrat
avanti.group.PaperStandards=Normes de substrat
avanti.group.SupplierItemDetails=D\u00E9tails du fournisseur
avanti.group.SupplierItemStatistics=Statistiques
avanti.group.SupplierLandedCost=Prix \u200B\u200Bau d\u00E9barquement
avanti.group.SupplierPurchaseDefaults=Valeurs d'achat par d\u00E9faut
avanti.group.Tasks=Les t\u00E2ches
avanti.group.UOM=Unit\u00E9 de mesure
avanti.group.WarehouseLocations=Emplacements des bacs
avanti.group.generalLedgerAccountsProducingPlants=Comptes g\u00E9n\u00E9raux - Usines de production
avanti.group.mobileDeviceManagement=Gestion des appareils mobiles
avanti.label.excludeStatesFromAvatax=Exclure les \u00E9tats s\u00E9lectionn\u00E9s de l\u2019int\u00E9gration d\u2019Avatax
avanti.label.includePostageInTask=Incluez chaque t\u00E2che d'affranchissement en tant que ligne de facture distincte.
avanti.label.productionBillingReport=Rapport de facturation de production
avanti.lbl.=Co\u00FBt s\u00E9par\u00E9 pour chaque \u00E9l\u00E9ment de la nomenclature
avanti.lbl.\#Around=\# Environ
avanti.lbl.\#Cuts=\# Coupes
avanti.lbl.\#DiffInks=\# Diff\u00E9rentes encres
avanti.lbl.\#Forms=\# Formes
avanti.lbl.\#InksOnBack=\# Encres au dos
avanti.lbl.\#InksOnFront=\# Encres sur le devant
avanti.lbl.\#Items2Insert=\# d'\u00E9l\u00E9ments \u00E0 ins\u00E9rer
avanti.lbl.\#Out=\# Up
avanti.lbl.\#Passes=\# Passes
avanti.lbl.\#Pieces2Insert=\# de pi\u00E8ces \u00E0 ins\u00E9rer
avanti.lbl.\#RollsRequired=\# de rouleaux requis
avanti.lbl.\#Up=\# Up
avanti.lbl.\#VersionPlates=\# Plaques de version
avanti.lbl.\#across=\# \u00C0 travers
avanti.lbl.\#fromParent_2=\# de la feuille parent
avanti.lbl.\#helpers=\# Aides
avanti.lbl.\#originalsColor=\# Originals / Pages Couleur
avanti.lbl.\#other1=\# Autre 1
avanti.lbl.\#other2=\# Autre 2
avanti.lbl.\#other3=\# Autre 3
avanti.lbl.\#pages=\# Pages
avanti.lbl.\#rolls=\# Rouleaux
avanti.lbl.\#sheets=\# Feuilles
avanti.lbl.\#skids=\# Palettes
avanti.lbl.%Coverage=% Couverture
avanti.lbl.%Discount=% Remise
avanti.lbl.ACHReferenceNumber=Num\u00E9ro de r\u00E9f\u00E9rence ACH
avanti.lbl.AESITN=AES/ITN  Code d'Exon\u00E9ration
avanti.lbl.ARControlAccount=Compte de contr\u00F4le des comptes clients
avanti.lbl.AddMilestoneProgress=Ajouter une entr\u00E9e de progression des \u00E9tapes
avanti.lbl.AddOrderedItems=Ajouter des articles command\u00E9s
avanti.lbl.AddToGroup=Ajouter au groupe
avanti.lbl.AdditionalInsuranceCost=Co\u00FBt d'assurance suppl\u00E9mentaire
avanti.lbl.AdditionalMaterial=Mat\u00E9riels suppl\u00E9mentaires
avanti.lbl.AddressImport=Importation d'adresse
avanti.lbl.AddressesToImport=\ Adresses \u00E0 importer
avanti.lbl.AdjustedMarkup=Marge ajust\u00E9e de <param1>% \u00E0 <param2>% apr\u00E8s l'application de la remise client de <param3>%
avanti.lbl.All=Tout
avanti.lbl.AllowProjectToControlTheTax=Autoriser Project \u00E0 contr\u00F4ler la taxe
avanti.lbl.AmountRefunded=Montant rembours\u00E9
avanti.lbl.AppliesOnRegister=S'applique au registre
avanti.lbl.ApplyBoxItemDetails=Appliquer les d\u00E9tails de l'article de bo\u00EEte \u00E0 toutes les exp\u00E9ditions du lot contenant le m\u00EAme article et la m\u00EAme quantit\u00E9.
avanti.lbl.ApplyShipMethodsToAllAddresses=Appliquer ces m\u00E9thodes d'exp\u00E9dition et types de services \u00E0 toutes les adresses
avanti.lbl.ArchiveFolderTableColumn=Dossier d'archives
avanti.lbl.ArchiveTableColumn=Archive
avanti.lbl.AroundSize=Autour de la taille
avanti.lbl.AutoAllocateBoxConfiguration=Nombre de bo\u00EEtes d'allocation automatique
avanti.lbl.AutoAllocateBoxItem=Article de bo\u00EEte d'allocation automatique
avanti.lbl.AutoBatch=Lot Auto
avanti.lbl.AutoCreated=Cr\u00E9\u00E9 automatiquement
avanti.lbl.AutoReviewed=Examen automatique
avanti.lbl.AvaTaxWarningEditingTax=Remarque\: les montants de taxe seront redistribu\u00E9s par AvaTax
avanti.lbl.BTOKitsAssemblyKits=Kits BTO/D'assemblage
avanti.lbl.BatchInvoicingHasCompleted=Le traitement de la facturation par lots est termin\u00E9. Total des factures cr\u00E9\u00E9es\: 
avanti.lbl.BatchShippingHasCompleted=L'exp\u00E9dition par lots a termin\u00E9 le traitement
avanti.lbl.Billable=facturable
avanti.lbl.BinLocation=Emplacement
avanti.lbl.BookingConfirmationNumber=Num\u00E9ro de confirmation de r\u00E9servation
avanti.lbl.Box\#=\# Boite
avanti.lbl.BoxConfigComesFrom=Nombre de bo\u00EEtes r\u00E9cup\u00E9r\u00E9 \u00E0 partir du num\u00E9ro de bordereau d'exp\u00E9dition
avanti.lbl.BoxItemDetailsComeFrom=D\u00E9tails de l'article de la bo\u00EEte extraits du num\u00E9ro du bordereau d'exp\u00E9dition 
avanti.lbl.BoxLabels=\u00C9tiquettes de bo\u00EEte
avanti.lbl.BrokerageFees=Frais de courtage
avanti.lbl.BundleMaterialQty=Paquet/quantit\u00E9 de mat\u00E9riel
avanti.lbl.CRMCustomerEstimates=Estimations client CRM
avanti.lbl.CRMopportunities=Opportunit\u00E9s CRM
avanti.lbl.CalculatedCoilSize=Taille de bobine calcul\u00E9e
avanti.lbl.CalculatedSpineThickness=\u00C9paisseur calcul\u00E9e de la colonne vert\u00E9brale
avanti.lbl.CalculatingLineItem\#=Calcul du num\u00E9ro d'article 
avanti.lbl.CanadaPost=Postes Canada
avanti.lbl.CancelFulfillmentPick=Annuler la s\u00E9lection d'ex\u00E9cution
avanti.lbl.CancelReceipts=Annuler les re\u00E7us
avanti.lbl.CarrierRates=Tarifs des Transporteurs
avanti.lbl.CashReceipt=Ticket de caisse
avanti.lbl.CashReceiptCancel_DetailView=Annulation d'encaissement - Vue d\u00E9taill\u00E9e
avanti.lbl.CashReceiptCancel_TableView=Annulation d'encaissement - Affichage du tableau
avanti.lbl.CashReceiptNumber=Num\u00E9ro de re\u00E7u de caisse
avanti.lbl.CashReceiptUnavailableInvoice=Vous essayez d'appliquer un encaissement sur une facture (<invoice_num>) qui n'est pas disponible.
avanti.lbl.ChargebackBatch=Lot de r\u00E9trofacturation
avanti.lbl.ChildCustomers=Clients Enfants
avanti.lbl.ClearFoldeGroups=Effacer les groupes de dossiers
avanti.lbl.ClientSecret=Cl\u00E9 secr\u00E8te du client
avanti.lbl.CoilMaterial=Mat\u00E9riau de la bobine
avanti.lbl.ColorGate=ColorGate
avanti.lbl.ColumnDescription=Description de la Colonne
avanti.lbl.CommercialInvoice=Facture commerciale
avanti.lbl.CommodityTaxesAndFees=Taxes \u00E0 la consommation et frais
avanti.lbl.CompletedCreateNewOrderAndPost=Termin\u00E9 Cr\u00E9er une nouvelle commande et publier
avanti.lbl.CompletedHold=Maintien termin\u00E9
avanti.lbl.CompletedReadyToPost=Termin\u00E9 Pr\u00EAt \u00E0 publier
avanti.lbl.Confirming=Confirmation
avanti.lbl.ConfirmingShipments=Confirmation des Envois
avanti.lbl.CoreDiameter_in=Diam\u00E8tre de base (in)
avanti.lbl.CornerRadius=Rayon de coin
avanti.lbl.CostUomFactor=Unit\u00E9s de co\u00FBt / Facteur
avanti.lbl.CouldntGetGeoSession=Impossible d'obtenir geoSession. Veuillez confirmer que le nom d'utilisateur et le mot de passe sont corrects.
avanti.lbl.CouldntGetToken.=Impossible d'obtenir le jeton.
avanti.lbl.CountPerRoll=Nombre par rouleau
avanti.lbl.Create&Confirm=Cr\u00E9er & Confirmer
avanti.lbl.CreateDetails=Cr\u00E9er des D\u00E9tails
avanti.lbl.CreateFolderGroup=Cr\u00E9er un groupe de dossiers
avanti.lbl.CreateShipmentDetails=Cr\u00E9er les D\u00E9tails de l'Exp\u00E9dition
avanti.lbl.CreatingInvoices=Cr\u00E9ation de factures
avanti.lbl.CreatingShipments=Cr\u00E9er des Exp\u00E9ditions
avanti.lbl.Curve=Courbe
avanti.lbl.CustomerPartsSelected=Pi\u00E8ces client s\u00E9lectionn\u00E9es
avanti.lbl.CustomsCurrency=Monnaie douani\u00E8re
avanti.lbl.CylinderSize=Taille de cylindre
avanti.lbl.DSFPasswordLabel=Mot de passe
avanti.lbl.DSFReceiverIdLabel=Identifiant du destinataire
avanti.lbl.DSFUserLabel=Utilisateur
avanti.lbl.DSFWorkbenchNameLabel=Nom de l'atelier
avanti.lbl.DateBatchPosted=Date d'envoi du lot
avanti.lbl.DateTime=Date/heure
avanti.lbl.DeclarationStatement=D\u00E9claration
avanti.lbl.DeclaredValue=Valeur D\u00E9clar\u00E9e
avanti.lbl.DeclaredValueType=Type de valeur d\u00E9clar\u00E9e
avanti.lbl.DefWorkTemplateCust=Client du mod\u00E8le de travail par d\u00E9faut
avanti.lbl.Descartes=Descartes
avanti.lbl.Discount=Remise
avanti.lbl.DueTime=D\u00E9lai d'ex\u00E9cution
avanti.lbl.Duties=Devoirs
avanti.lbl.DutyCost=Co\u00FBt en douane
avanti.lbl.EarnedTime=Temps gagn\u00E9 %
avanti.lbl.EmbossRelief=Relief en relief
avanti.lbl.EnvironmentalReport=Rapport environnemental
avanti.lbl.EstimatingUOM=Unit\u00E9s d'estimation
avanti.lbl.ExchangeCost=Co\u00FBt d'\u00E9change
avanti.lbl.ExportData=Exporter des donn\u00E9es
avanti.lbl.ExportReason=Motif d'exportation
avanti.lbl.ExportReasonType=Type de motif d'exportation
avanti.lbl.ExportReference=R\u00E9f\u00E9rence d'exportation
avanti.lbl.FGItem=Bon Article Fini
avanti.lbl.FIFOPurchaseOrder=Bon de commande
avanti.lbl.FOBAddress=Adresse FOB
avanti.lbl.FOBCustomer=Client FOB
avanti.lbl.FailedCarrierRequests=\u00C9chec des demandes de transporteur
avanti.lbl.Fedex=Fedex
avanti.lbl.FindReserved=Trouver r\u00E9serv\u00E9
avanti.lbl.FindingReserved=Trouver R\u00E9serv\u00E9...
avanti.lbl.FinishedCarrier=Transporteur fini
avanti.lbl.FlushReserved=Chasse d'eau r\u00E9serv\u00E9e
avanti.lbl.FobCost=Co\u00FBt FOB
avanti.lbl.FoldPattern=Mod\u00E8le pli\u00E9
avanti.lbl.FoldingBinding=Plier/Relier le bord
avanti.lbl.FootagePerRoll=Images par rouleau
avanti.lbl.FreightCost=Co\u00FBt du fret
avanti.lbl.FreightInMethod=M\u00E9thode de fret
avanti.lbl.FreightLTL=Fret LTL
avanti.lbl.FromOrderCloseDate=\u00C0 partir de la date de cl\u00F4ture de la commande
avanti.lbl.GPDistributions=- Les valeurs sont %s par %f puis %s par &f.
avanti.lbl.GPDistributionsDivided=divis\u00E9es
avanti.lbl.GPDistributionsMultiplied=multipli\u00E9es
avanti.lbl.GPRoundingNote=Remarque \: GP peut inclure l'arrondi comme une distribution distincte.
avanti.lbl.GetNextJob=Mon prochain dossier
avanti.lbl.GetShippingRates=Obtenez les tarifs d'exp\u00E9dition
avanti.lbl.GrainDir=Sens du grain
avanti.lbl.HideZeroBins=Masquer les emplacements des bacs avec une quantit\u00E9 disponible nulle 
avanti.lbl.ImportFile=Importer le fichier
avanti.lbl.ImpositionModel=Mod\u00E8le d'imposition
avanti.lbl.ImpositionRestrictedToNarrowestMaterial=Imposition limit\u00E9e \u00E0 l'utilisation du mat\u00E9riau le plus...
avanti.lbl.ImpositionRestrictedToNarrowestMaterial_tooltip=Imposition limit\u00E9e \u00E0 l'utilisation du mat\u00E9riau le plus \u00E9troit
avanti.lbl.IncludeSelectedCustomerPartNo=Inclure le num\u00E9ro de pi\u00E8ce du client s\u00E9lectionn\u00E9
avanti.lbl.IncludeVirtualItems=Inclure les \u00E9l\u00E9ments virtuels
avanti.lbl.IncludingPostageMarkup=(Y compris le suppl\u00E9ment d'affranchissement)
avanti.lbl.IntegrationDetails=D\u00E9tails de l'int\u00E9gration
avanti.lbl.InterBranch_AllRevenueToOriginatingPlant=Tous les revenus vont \u00E0 l'usine d'origine.
avanti.lbl.InterBranch_TaskDoesNotTransfer=La t\u00E2che n'est pas transf\u00E9r\u00E9e.
avanti.lbl.InterBranch_TaskIsSystemTask=La t\u00E2che est une t\u00E2che syst\u00E8me.
avanti.lbl.InterBranch_taskSetToAllovateToOriginatingPlant=Ensemble de t\u00E2ches \u00E0 allouer \u00E0 l'usine d'origine.
avanti.lbl.InternationalFreight=Fret international
avanti.lbl.InvoiceExportReason=Motif d'exportation de la facture
avanti.lbl.InvoiceReference=R\u00E9f\u00E9rence de la facture
avanti.lbl.InvoiceSignatureName=Signature de la facture Nom
avanti.lbl.InvoiceSignatureTitle=Signature de la facture Titre
avanti.lbl.InvoiceTermsOfDelivery=Conditions de livraison de la facture
avanti.lbl.ItemID=Article
avanti.lbl.ItemStockingUOM=UdM de stock d'articles
avanti.lbl.JobCompleteOptions_fullyshipped_or_marked_complete=Une fois enti\u00E8rement exp\u00E9di\u00E9 ou termin\u00E9 manuellement
avanti.lbl.JobDateClosedFrom=Poste ferm\u00E9 depuis
avanti.lbl.LagTimeBasedOn=Temps de latence bas\u00E9 sur
avanti.lbl.LandedCost=Prix \u200B\u200Bau d\u00E9barquement
avanti.lbl.LastModified=Derni\u00E8re modification
avanti.lbl.LastStagingLocation=Dernier lieu de rassemblement
avanti.lbl.LayoutAndFolds=Disposition et plis
avanti.lbl.LeadEdge=Bord d'attaque
avanti.lbl.LeadTime=D\u00E9lai
avanti.lbl.LeadTimeControlMethod=Type de d\u00E9lai
avanti.lbl.LeadTimeDays=D\u00E9lai d'ex\u00E9cution (jours)
avanti.lbl.LeadTimeDays_tooltip=D\u00E9lai (jours) ne sont modifiables que lorsque le type de d\u00E9lai est d\u00E9fini sur Manuel.
avanti.lbl.Length=Longueur (in)
avanti.lbl.LineItemCode=Code de ligne d'article
avanti.lbl.LineQty=Qt\u00E9 de ligne
avanti.lbl.LinearFeet=Pieds lin\u00E9aires
avanti.lbl.MRSpoils=D\u00E9chets de pr\u00E9paration
avanti.lbl.MRSpoilsLinearFeet=MR Spoils (pieds lin\u00E9aires)
avanti.lbl.MRSpoilsUOM=MR g\u00E2che l'UdM\:
avanti.lbl.MailInnovationsOptions=Options d'innovations de messagerie
avanti.lbl.ManuallySelectRolls=Autoriser l'utilisateur \u00E0 s\u00E9lectionner manuellement les rouleaux qu'il utilisera
avanti.lbl.ManufacturerCountry=Pays du fabricant
avanti.lbl.MaterialMultiplier=Multiplicateur de mat\u00E9riau
avanti.lbl.MilestoneProgress=Progr\u00E8s des \u00E9tapes du travail
avanti.lbl.MilestoneProgressComments=Ommentaires sur la progression des \u00E9tapes
avanti.lbl.MinModelSize=Taille minimale du mod\u00E8le
avanti.lbl.MinimumOrderQty=Quantit\u00E9 minimum de commande
avanti.lbl.Mirrored/Reverse=Miroir / Inverser
avanti.lbl.MultipleCaps=PLUSIEURS
avanti.lbl.MultipleSignatures=Plusieurs signatures sur le formulaire?
avanti.lbl.MyJobs=Mes dossiers
avanti.lbl.NMFCitemNo=Num\u00E9ro d'article NMFC
avanti.lbl.NegotiatedRate=Taux n\u00E9goci\u00E9
avanti.lbl.NetRemaining=Net restant
avanti.lbl.NewItem=Nouvel article
avanti.lbl.NoRateForMailInnovations=Aucun tarif ne sera fourni par UPS pour les types de services Mail Innovations
avanti.lbl.NumberAcross=Nombre \u00E0 travers
avanti.lbl.NumberPerSet=Nombre par set
avanti.lbl.NumberUp=Nombre en hausse
avanti.lbl.Obsolete&Replace=Obsol\u00E8te et Remplacer
avanti.lbl.Ocean=Oc\u00E9an
avanti.lbl.OnlyInvoicedandPostedShipments=Uniquement les exp\u00E9ditions factur\u00E9es et valid\u00E9es
avanti.lbl.OverriddenCoilSize=Taille de bobine annul\u00E9e
avanti.lbl.OverrideEstimate=Remplacer l'estimation
avanti.lbl.PDFPreviewNotPossible=Impossible de pr\u00E9visualiser le fichier
avanti.lbl.PDFReviewCloudToken=Jeton Cloud Enfocus
avanti.lbl.PDFReviewExpirationDays=Expiration de l'approbation (jours)
avanti.lbl.PDFReviewRepoId=Identifiant du d\u00E9p\u00F4t
avanti.lbl.PDFReviewSiteName=Nom du site
avanti.lbl.PDFReviewTabName=Param\u00E8tres Enfocus
avanti.lbl.PDFReviewUrl=URL de r\u00E9vision d'Enfocus
avanti.lbl.PackageSize=Taille du paquet
avanti.lbl.PageCount=Nombre de pages
avanti.lbl.PageSet=Ensemble de pages
avanti.lbl.Paging=Pagination
avanti.lbl.PalletID=ID Palette
avanti.lbl.ParentSheetSpoils=D\u00E9chets de feuille de parent
avanti.lbl.ParentSheetsWithSpoils=Feuilles m\u00E8res et d\u00E9chets
avanti.lbl.PassageOfTime=Passage du Temps
avanti.lbl.PickupTime=Heure de ramassage
avanti.lbl.PleaseEnterAProject=Veuillez saisir un projet
avanti.lbl.PostageAndPostageTaskTotal=Total des frais d'affranchissement...
avanti.lbl.PostageCancelDeposit=D\u00E9p\u00F4t d'annulation d'affranchissement
avanti.lbl.PostageItems=Articles d'affranchissement
avanti.lbl.PressSheets/LinearFeet=Feuilles de presse/pieds lin\u00E9aires
avanti.lbl.PreviousJob\#=Emploi pr\u00E9c\u00E9dent \#
avanti.lbl.PriceCategory=Cat\u00E9gorie de prix
avanti.lbl.PriceOfPostage=Prix \u200B\u200Bde l'affran...
avanti.lbl.PriceRules=R\u00E8gles de prix
avanti.lbl.PrintAll=Tout imprimer
avanti.lbl.PrintBoxLabels=Imprimer des \u00E9tiquettes de bo\u00EEte
avanti.lbl.ProcessingBatch=Lot de Traitement
avanti.lbl.ProcessingFile=Fichier de traitement\:
avanti.lbl.ProcessingLineItem\#Address\#=\u00E9l\u00E9ment de ligne\u00A0\#<param1>, <param2> sur <param3>\u00A0adresses trait\u00E9es\u2026
avanti.lbl.ProducingPlantDocumentStream=Production d'un flux de documents d'usine
avanti.lbl.ProducingPlantRevenue=Production des revenus de l'usine%
avanti.lbl.ProducingPlants=Production de plantes
avanti.lbl.ProductionQtyOfOrder=Quantit\u00E9 de production de la commande
avanti.lbl.PurchaseOrderReceiptsRegisterBuyoutsOnly=Registre des re\u00E7us de bons de commande (achats uniquement)
avanti.lbl.PurgeCompletedDate=Date de fin
avanti.lbl.PurgeInvoicedDate=Date de facturation
avanti.lbl.PurgeShippedDate=Date d'envoi
avanti.lbl.PurgeTableColumn=Purge
avanti.lbl.PurgingArchive=Purger / Archiver
avanti.lbl.Purolator=Purolator
avanti.lbl.QtyExceedsShipQty=La quantit\u00E9 emball\u00E9e d\u00E9passe la quantit\u00E9 exp\u00E9di\u00E9e pour cette adresse.
avanti.lbl.QtyPerCore/Roll=Quantit\u00E9 par noyau/rouleau
avanti.lbl.QtyRemainingToShip=Quantit\u00E9 restante \u00E0 exp\u00E9dier
avanti.lbl.QtyUsedToDate=Quantit\u00E9 utilis\u00E9e \u00E0 ce jour
avanti.lbl.QuantityByBinLocation=Quantit\u00E9 par emplacement de bac
avanti.lbl.QuantityProgress=Progression Travaux
avanti.lbl.Rail=Rail
avanti.lbl.RateShopAllAddresses=Taux Magasinez toutes les adresses
avanti.lbl.RateShopThisAddress=\u00C9valuer Acheter cette adresse
avanti.lbl.RateShopping=\u00C9valuer les Achats
avanti.lbl.ReasonForExport=Raison pour l'export
avanti.lbl.RecalculateAll=Recalculer tout
avanti.lbl.RecalculateSection=Recalculer la section
avanti.lbl.ReceivableCreditNote=Note de cr\u00E9dit \u00E0 recevoir
avanti.lbl.ReceivableInvoice=Facture \u00E0 recevoir
avanti.lbl.RefreshAllocation=Actualiser l'allocation
avanti.lbl.Refunds=Remboursements
avanti.lbl.Refunds_DetailView=Remboursements - Vue d\u00E9taill\u00E9e
avanti.lbl.Refunds_TableView=Remboursements - Vue tableau
avanti.lbl.RemoveFromGroup=Supprimer du groupe
avanti.lbl.ReorderMultiple=R\u00E9organiser plusieurs
avanti.lbl.Replace=Remplacer
avanti.lbl.ReportColumnPosition=Position de la colonne du rapport
avanti.lbl.ReportingUOM=UdM de d\u00E9claration
avanti.lbl.ResequenceBoxNumbers=Num\u00E9ros de bo\u00EEte de res\u00E9quence
avanti.lbl.ResequenceBoxNumbersTT=Res\u00E9quencez les num\u00E9ros de case pour tous les \u00E9l\u00E9ments de ligne apr\u00E8s celui-ci.
avanti.lbl.ResetBoxDetailsAndBoxes=R\u00E9initialiser les d\u00E9tails de la bo\u00EEte et les bo\u00EEtes
avanti.lbl.ResetBoxes=R\u00E9initialiser les bo\u00EEtes aux valeurs par d\u00E9faut
avanti.lbl.ResetPassword=R\u00E9initialiser le mot de passe
avanti.lbl.ResetShippingMethods=R\u00E9initialiser les m\u00E9thodes d'exp\u00E9dition aux valeurs par d\u00E9faut
avanti.lbl.ResetToDefaults=R\u00E9initialiser les param\u00E8tres par d\u00E9faut
avanti.lbl.RestoreDefaultSort=Restaurer le tri par d\u00E9faut
avanti.lbl.ResumeOperation=Reprendre l'op\u00E9ration
avanti.lbl.ResumingPausedOp=Reprise de l'op\u00E9ration en pause
avanti.lbl.RevenuePct=Revenu%
avanti.lbl.RunAll=Tout Ex\u00E9cuter
avanti.lbl.Running=Ex\u00E9cution
avanti.lbl.SQIn/min=po\u00B2/min
avanti.lbl.SQmm/min=mm\u00B2/min
avanti.lbl.SalesTaxType=Groupe de taxe de vente
avanti.lbl.ScrapQuantity=Quantit\u00E9 de rebut
avanti.lbl.ScrapQuantityLF=Quantit\u00E9 de ferraille (LF)
avanti.lbl.SelectOperation=S\u00E9lectionnez l'op\u00E9ration
avanti.lbl.SellingUomFromSalesOrder=UdM de vente \u00E0 partir de la commande client (par d\u00E9faut)
avanti.lbl.ShiftTime=Heure de Changement
avanti.lbl.ShipmentTaxesAndFees=Taxes et frais d'exp\u00E9dition
avanti.lbl.ShippersDestinationTaxID=ID fiscal de destination des exp\u00E9diteurs
avanti.lbl.ShippersLoadAndCount=Les exp\u00E9diteurs chargent et comptent
avanti.lbl.ShippingCost=Frais de port
avanti.lbl.ShippingRateShopping=Frais de Livraison
avanti.lbl.ShopFloorShowExitMsg=Masquer le message de sortie
avanti.lbl.ShowBoxDetails=Afficher les d\u00E9tails de la bo\u00EEte
avanti.lbl.ShowCurrentProjectOnly=Afficher le projet actuel uniquement
avanti.lbl.ShowQtyByBin=Afficher la quantit\u00E9 par emplacement de bac
avanti.lbl.SideGuide=Guide lat\u00E9ral
avanti.lbl.SignaturesNotValidated=Signatures non valid\u00E9es
avanti.lbl.SignaturesValidated=Signatures valid\u00E9es
avanti.lbl.SizeAcross=Taille sur
avanti.lbl.SizeAround=Taille autour
avanti.lbl.SpaceAcross=L'espace \u00E0 travers
avanti.lbl.SpaceAround=Espace autour
avanti.lbl.SpacingAcross=Espacement Traversant
avanti.lbl.SpacingAround=Espacement Autour
avanti.lbl.SquareFootRate=Tarif au pied carr\u00E9
avanti.lbl.SquareMeterRate=Tarif au m\u00E8tre carr\u00E9
avanti.lbl.SubstrateFirstDim=Substrat First Dim.
avanti.lbl.Substrates=Des substrats
avanti.lbl.SupplierItem\#=Num\u00E9ro de fournisseur
avanti.lbl.SupplierPart=Num\u00E9ro d'article
avanti.lbl.TariffCode=Code Tarifaire
avanti.lbl.TaxApplied=Taxe appliqu\u00E9e
avanti.lbl.TaxExemptionReason=Raison de l'exon\u00E9ration fiscale
avanti.lbl.TaxesAndFees=Taxes et frais
avanti.lbl.ThirdPartyAddress=Adresse de tiers
avanti.lbl.ThirdPartyCountry=Pays tiers
avanti.lbl.ThirdPartyPostalCode=Code postal / code postal d'un tiers
avanti.lbl.ThisIsAGlobalProject=Un ast\u00E9risque indique qu'il s'agit d'un projet global non li\u00E9 \u00E0 un client sp\u00E9cifique.
avanti.lbl.ToOrderCloseDate=Date de cl\u00F4ture de la commande
avanti.lbl.TotalAvailableQty=Quantit\u00E9 totale disponible
avanti.lbl.TotalClicks=Nombre total de clics
avanti.lbl.TotalCustomerPrice=Prix \u200B\u200Btotal client
avanti.lbl.TotalDutiesTaxesFees=Total des droits/taxes/frais
avanti.lbl.TotalNegotiatedPrice=Prix \u200B\u200Bn\u00E9goci\u00E9 total
avanti.lbl.TotalPostageTask=T\u00E2che d'affranchissement totale
avanti.lbl.TotalRemainingQty=Quantit\u00E9 restante totale
avanti.lbl.TotalRepeat=R\u00E9p\u00E9tition totale
avanti.lbl.TotalSetupAmountToDate=Montant total de la configuration
avanti.lbl.TotalSqInches=Sq. Total Pouces
avanti.lbl.TotalWebSetup=Total co\u00FBt d'installation / Web
avanti.lbl.TowerConfiguration=Configuration de la Tour
avanti.lbl.TrackRolls=Rouleaux de piste
avanti.lbl.TransportationCost=Co\u00FBt de transport
avanti.lbl.TransportationMode=Mode de transport
avanti.lbl.UPS=UPS
avanti.lbl.USPS=USPS
avanti.lbl.UnconfirmedLaborAndMaterial=Main-d'\u0153uvre et mat\u00E9riel non confirm\u00E9s
avanti.lbl.Unlock=Ouvrir
avanti.lbl.UomIsMissing=UdM est manquante
avanti.lbl.UseSelectedShipMethod=Utiliser la m\u00E9thode d'exp\u00E9dition s\u00E9lectionn\u00E9e
avanti.lbl.UseTimeZone=Utiliser le fuseau horaire
avanti.lbl.UseTimeZoneTT=Convertissez l'heure dans le fuseau horaire de l'organisation si un d\u00E9calage de fuseau horaire est pr\u00E9sent dans la cha\u00EEne de date.
avanti.lbl.VATPaid=TVA pay\u00E9e
avanti.lbl.ValidValuesAre=Les valeurs valides sont\: 
avanti.lbl.VersionLabel=\u00C9tiquette de version
avanti.lbl.View=Other Side\=Voir l'autre c\u00F4t\u00E9
avanti.lbl.View\ Other\ Side=Voir l'autre c\u00F4t\u00E9
avanti.lbl.VirtualItem=Objet Virtuel
avanti.lbl.WIPrecordsOnly=Enregistrements WIP uniquement
avanti.lbl.WebServiceResponse=R\u00E9ponse de service Web
avanti.lbl.WebWidth=Largeur Web
avanti.lbl.WhyIsntOrderShowingInBatch=Pourquoi ne puis-je pas trouver ma Commande dans ce lot?
avanti.lbl.WorkTemplateStream=Flux de mod\u00E8les de travail
avanti.lbl.Workato=Comptabilit\u00E9 int\u00E9gr\u00E9e
avanti.lbl.accountExpenseLink=Lien de d\u00E9penses du compte
avanti.lbl.accountInternalCode=Code interne du compte
avanti.lbl.accountingDate=Date comptable
avanti.lbl.acrossRoll=\# \u00C0 travers le rouleau
avanti.lbl.activity=Activit\u00E9
avanti.lbl.activityDueDate_all=Tout
avanti.lbl.activityRegarding_customer=Client
avanti.lbl.activityRegarding_job=Dossier
avanti.lbl.actual=R\u00E9elle
avanti.lbl.actualTime=Heure R\u00E9elle
avanti.lbl.add=Ajouter
avanti.lbl.addAddress=ajouter une adresse
avanti.lbl.addBox=ajouter une bo\u00EEte
avanti.lbl.addFile_fileFolder=Fichiers\:
avanti.lbl.addFile_fileFolder_tooltip=S\u00E9lectionnez un fichier / dossier ou entrez une URL ou un chemin UNC \u00E0 attacher
avanti.lbl.addItem=Ajouter un item
avanti.lbl.addMRTime/Cost=Ajouter du temps...
avanti.lbl.addMRTime/Cost_tooltip=Ajouter du temps de pr\u00E9paration
avanti.lbl.addNewMobileDevice=Ajouter un nouvel appareil mobile
avanti.lbl.addOtherOperation=Ajouter une operation
avanti.lbl.addOtherOperations=Ajouter une autre op\u00E9ration
avanti.lbl.addQtyOrderedTT=Ajouter des articles command\u00E9s
avanti.lbl.addToReturn=Ajouter au retour
avanti.lbl.additionalCharge=Frais suppl\u00E9mentaires
avanti.lbl.additionalMaterialPerBundle=Mat\u00E9riel suppl\u00E9mentaire \r\n par lot
avanti.lbl.additionalMaterialPerBundle_note=(in) (s'applique uniquement lorsque le mat\u00E9riau est estim\u00E9 en pieds lin\u00E9aires)
avanti.lbl.additionalMaterialPerBundle_noteMetric=(mm) (s'applique uniquement lorsque le mat\u00E9riau est estim\u00E9 en m\u00E8tres lin\u00E9aires)
avanti.lbl.additionalPostageTaskCharges=Frais d'affranchissement suppl\u00E9mentaires
avanti.lbl.additionalSpoilage=Addo Spoilage (%)
avanti.lbl.addressHasShipQtyLessThanBoxes=L'adresse d'exp\u00E9dition <param1> a maintenant une quantit\u00E9 d'exp\u00E9dition inf\u00E9rieure aux \u00E9tiquettes de bo\u00EEte cr\u00E9\u00E9es, veuillez l'ajuster.
avanti.lbl.adhesive=Adh\u00E9sive
avanti.lbl.adhesiveOtherDie1=Adh\u00E9sif Autre Matrice \#1
avanti.lbl.adhesiveOtherDie2=Adh\u00E9sif Autre Matrice \#2
avanti.lbl.adjustedPrice=Prix \u200B\u200Bajust\u00E9
avanti.lbl.adjustmentAmount=Montant de l'ajustement
avanti.lbl.administrator=Administrateur/Administrateur
avanti.lbl.advanceBillingAppliedInvoices=Facturation anticip\u00E9e appliqu\u00E9e aux factures / notes de cr\u00E9dit
avanti.lbl.all=Tout
avanti.lbl.allFlag=Tout
avanti.lbl.allProducingPlants=Toutes les usines de production
avanti.lbl.allRevenueToOrigPlant=Tous les revenus \u00E0 l'usine d'origine
avanti.lbl.allocateShippingToProducingPlants=Allouer les exp\u00E9ditions aux usines de production.
avanti.lbl.allowStaggeredLayouts=Autoriser les mises en page d\u00E9cal\u00E9es
avanti.lbl.allowTiling=Autoriser le carrelage
avanti.lbl.alongRoll=\# Long rouleau
avanti.lbl.appliedCreditNote_reopen=Rouvrir
avanti.lbl.applyPriceRules=Appliquer les r\u00E8gles de prix
avanti.lbl.applyToTiledPanels=Appliquer aux panneaux carrel\u00E9s
avanti.lbl.approved=Approuv\u00E9
avanti.lbl.approvedBy=Approuv\u00E9 par
avanti.lbl.approvedDate=Date d'approbation
avanti.lbl.approverComment=Commentaires de l'approbateur
avanti.lbl.appySquareMeterRoundingGrid=Appliquer une grille d'arrondi au m\u00E8tre carr\u00E9
avanti.lbl.april=Avril
avanti.lbl.are_taxes_paid=Les imp\u00F4ts sont-ils pay\u00E9s?
avanti.lbl.attach=Attacher
avanti.lbl.attachFile=Pi\u00E8ce jointe
avanti.lbl.august=Ao\u00FBt
avanti.lbl.autoPost=Publication automatique
avanti.lbl.autoPosted=Publi\u00E9 automatiquement
avanti.lbl.autoRounding=Arrondi Automatique
avanti.lbl.autoSelectRollsUsingFIFO=Syst\u00E8me de s\u00E9lection automatique des rouleaux \u00E0 l'aide de la fonctionnalit\u00E9 FIFO existante
avanti.lbl.avail=Profiter
avanti.lbl.available=Disponible
avanti.lbl.availableFilters=Filtres disponibles
avanti.lbl.avalaraAutoAcceptAvaTaxRecalc=Accepter automatiquement le recalcul d'AvaTax
avanti.lbl.avalaraInvoiceCreditNotePosted=Facture / note de cr\u00E9dit publi\u00E9e
avanti.lbl.averageLeadTime=D\u00E9lai moyen
avanti.lbl.bLMarkup=B / L Markup
avanti.lbl.backerMaterial=Mat\u00E9riel de soutien
avanti.lbl.batchInvoiceFilesSelected=Fichiers de factures group\u00E9es s\u00E9lectionn\u00E9s
avanti.lbl.batchInvoicing_checkingInvoiceCompleteOrders=V\u00E9rification de la facturation des commandes compl\u00E8tes
avanti.lbl.batchInvoicing_loadingResults=Chargement des r\u00E9sultats
avanti.lbl.batchInvoicing_processingInvoiceCompleteOrders=Traitement des commandes compl\u00E8tes sur facture
avanti.lbl.batchInvoicing_processingPackingSlips=Traitement des bordereaux d'exp\u00E9dition
avanti.lbl.batchInvoicing_processingServiceItems=Traitement des articles de service
avanti.lbl.batchInvoicing_processingShipmentDetails=Traitement des d\u00E9tails de l'envoi
avanti.lbl.batchInvoicing_selectingShipmentsToProcess=S\u00E9lection des envois \u00E0 traiter
avanti.lbl.best=Meilleur
avanti.lbl.billOfLading=Connaissement
avanti.lbl.billTo=Facturer
avanti.lbl.billingCode=Code de facturation
avanti.lbl.billingCodeDetails=D\u00E9tails du code de facturation
avanti.lbl.billingPeriod=P\u00E9riode de facturation
avanti.lbl.binderSpoils=Reliure des d\u00E9chets
avanti.lbl.bindingEdge=Bord de reliure
avanti.lbl.binlocations=Emplacements des poubelles
avanti.lbl.blackWhite=Noir Blanc
avanti.lbl.bleedHLong=\# Marges perdues hauteur
avanti.lbl.bleedL=Marges perdues longeur
avanti.lbl.bleedLLong=\# Marges perdues longeur
avanti.lbl.bleedSizeLength=Marges perdues longeur
avanti.lbl.bleedSizeLong=Marges perdues
avanti.lbl.bleedSizeWidth=Marges perdues largeur
avanti.lbl.bleedW=Marges perdues largeur
avanti.lbl.bleedWLong=\# Marges perdues largeur
avanti.lbl.bleeds=Marges
avanti.lbl.bleedsForHeight=Marges perdues hauteur
avanti.lbl.bleedsForLength=Marges perdues longeur
avanti.lbl.bleedsForWidth=Marges perdues larg...
avanti.lbl.boxCountApplyToAllAddressesTT=Utilisez ce bouton pour copier la quantit\u00E9 par bo\u00EEte vers toutes les adresses qui exp\u00E9dient cet article.
avanti.lbl.boxTable=Tableau de la bo\u00EEte
avanti.lbl.boxesTabApplyToAllAddressesTT=Utilisez ce bouton pour copier l'article de la bo\u00EEte, la quantit\u00E9 par bo\u00EEte, les dimensions de la bo\u00EEte, la longueur, la largeur et la hauteur et les d\u00E9tails du poids vers toutes les adresses qui exp\u00E9dient cet article.
avanti.lbl.brandName=Marque
avanti.lbl.bundleQty=Qt\u00E9 de paquet
avanti.lbl.bwClicks=Nombre de clics N & B
avanti.lbl.calc_qtyAvailable=Quantit\u00E9 disponible
avanti.lbl.calculationMethod=Calculation Method
avanti.lbl.caliperAdjustment%=R\u00E9glage de l'\u00E9trier (%)
avanti.lbl.caliperCarbonlessSet=Pied \u00E0 coulisse
avanti.lbl.cancel=Annuler
avanti.lbl.cancelReceipt=Annuler le re\u00E7u
avanti.lbl.cancelReceiptCount=Annuler le comptage des re\u00E7us
avanti.lbl.cancelReceiptNumber=Annuler le num\u00E9ro de re\u00E7u
avanti.lbl.cancelReceiptStatus=Annuler le statut du re\u00E7u
avanti.lbl.cantLoadServices=N\u00ED f\u00E9idir seirbh\u00EDs\u00ED a l\u00F3d\u00E1il mar gheall ar earr\u00E1id\: 
avanti.lbl.cantUsePriceRules=Vous ne pouvez pas utiliser de r\u00E8gles de prix lorsqu'il existe des d\u00E9passements de prix/de marge
avanti.lbl.carbonlessSet=Ensemble en plusieurs parties (par ex. Sans carbone)
avanti.lbl.carrierDescartesDepotKey=D\u00E9p\u00F4t
avanti.lbl.carrierDescartesOrganizationKey=Cl\u00E9 d'organisation
avanti.lbl.carrierDescartesPasswordHash=Hachage de mot de passe
avanti.lbl.carrierDescartesUsername=Nom d'utilisateur de Descartes
avanti.lbl.carrierFreightOptions=Options de fret
avanti.lbl.cashReceiptCancelDate=Date de r\u00E9ception
avanti.lbl.cashReceiptDetails=D\u00E9tails du re\u00E7u en esp\u00E8ces
avanti.lbl.category=Cat\u00E9gorie
avanti.lbl.cbPostExplanation=Run Query charge les commandes dans le lot. Ce sont les commandes qui s'afficheront dans le rapport de r\u00E9vision et le fichier d'exportation, et seront publi\u00E9es lorsque vous cliquerez sur Publier. Les options Review et Post n'ex\u00E9cuteront pas de nouvelle requ\u00EAte pour voir si les nouvelles commandes entrent dans la plage de date d'exp\u00E9dition. Par cons\u00E9quent, si de nouvelles commandes ont \u00E9t\u00E9 exp\u00E9di\u00E9es depuis la derni\u00E8re ex\u00E9cution de Run Query et que vous souhaitez qu'elles soient r\u00E9cup\u00E9r\u00E9es dans ce lot, Run Query devra \u00EAtre ex\u00E9cut\u00E9 \u00E0 nouveau.
avanti.lbl.changeOrder=Demande de changement
avanti.lbl.changePasswordOnLogin=L'utilisateur doit changer de mot de passe lors de la premi\u00E8re connexion
avanti.lbl.chargeFullSheets=Charge pour les feuilles compl\u00E8tes
avanti.lbl.chargePartialSheets=Frais pour les feuilles partielles dans l\u2019estimation et les commandes client
avanti.lbl.chargePartialSheetsAsFullSheets_checked=\u00AB\u00A0Charger les feuilles partielles en tant que feuilles compl\u00E8tes\u00A0\u00BB coch\u00E9
avanti.lbl.chargePartialSheetsAsFullSheets_unchecked=\u00AB\u00A0Charger les feuilles partielles en tant que feuilles compl\u00E8tes\u00A0\u00BB d\u00E9coch\u00E9
avanti.lbl.chargebackCode=Code de r\u00E9trofacturation
avanti.lbl.chartLine=Ligne
avanti.lbl.city=Ville
avanti.lbl.clearAllFilters=Effacer tous les filtres
avanti.lbl.clearFilters=Effacer le filtre
avanti.lbl.clearLog=Effacer le journal
avanti.lbl.clearSearch=Effacer la recherche
avanti.lbl.clickCalcBasedOn=Cliquez sur Calcul bas\u00E9 sur
avanti.lbl.clickCharge=Charge par clic
avanti.lbl.clicks=Clics
avanti.lbl.clicksBW=Clics N&B
avanti.lbl.clicksColor=Couleur Clics
avanti.lbl.clicksTT=La quantit\u00E9 de clics est bas\u00E9e sur le nombre d'impressions et peut \u00E9galement prendre en compte le nombre de couleurs et/ou un multiplicateur pour la taille de la feuille, selon la configuration de la presse.
avanti.lbl.close=Fermer
avanti.lbl.collate=Collationner
avanti.lbl.color=Couleur
avanti.lbl.colorClicks=Nombre de clics de couleur
avanti.lbl.colourBar=Barre de couleur
avanti.lbl.colourBarLong=Barre de couleur
avanti.lbl.comment=Commentaire
avanti.lbl.comments=Commentaires
avanti.lbl.commission=Commission
avanti.lbl.companyName=Nom de la compagnie
avanti.lbl.complete=Achev\u00E9e
avanti.lbl.completeOrder=Compl\u00E9tez la commande
avanti.lbl.completedBy=Termin\u00E9 par
avanti.lbl.completedJobCostReconciliation=Rapprochement des co\u00FBts du travail termin\u00E9
avanti.lbl.completionDate=Date d'ach\u00E8vement
avanti.lbl.confirmationCancel=Message de confirmation
avanti.lbl.confirmationQtyproduced=Cette op\u00E9ration n\u00E9cessite une quantit\u00E9 produite.
avanti.lbl.confirmationRequired=Confirmation requise
avanti.lbl.confirmedCostOnly=Co\u00FBts confirm\u00E9s uniquement
avanti.lbl.connected=Li\u00E9
avanti.lbl.consumptionRate=Taux de consommation
avanti.lbl.contact=Contact
avanti.lbl.contactFullName=Nom complet du contact
avanti.lbl.contact_full_name=Nom complet
avanti.lbl.contractsSelected=Contrats S\u00E9lectionn\u00E9s
avanti.lbl.copiedFromEstimate=Copi\u00E9 \u00E0 partir de l'estimation
avanti.lbl.copiedFromOrder=Copi\u00E9 depuis la commande client
avanti.lbl.copies=Copies
avanti.lbl.copy=Copie
avanti.lbl.copyComments=Copier les commentaires
avanti.lbl.copyEstimateWorkTemplate=Copier l'estimation vers le mod\u00E8le de travail
avanti.lbl.copyOrderWorkTemplate=Copier la commande client dans le mod\u00E8le de travail
avanti.lbl.core=Coeur
avanti.lbl.coreDiameter=Diam\u00E8tre de noyau
avanti.lbl.cost=Co\u00FBt
avanti.lbl.costCenter=Op\u00E9ration
avanti.lbl.costQuantity=Co\u00FBt Quantit\u00E9
avanti.lbl.costassociation=Entr\u00E9e de temps associ\u00E9e
avanti.lbl.countPerRollAfterSlitter=Nombre par rouleau apr\u00E8s d\u00E9coupe
avanti.lbl.country=Pays
avanti.lbl.cover=couvrir
avanti.lbl.coverMaterial=Mat\u00E9riau de couverture
avanti.lbl.createDate=Cr\u00E9er une date
avanti.lbl.createSeparateOrders=Cr\u00E9er des commandes s\u00E9par\u00E9es
avanti.lbl.createdBy=Cr\u00E9\u00E9 par
avanti.lbl.createdByProgram=Cr\u00E9\u00E9 par programme
avanti.lbl.creditNoteTax=Taxe sur les notes de cr\u00E9dit
avanti.lbl.creditType_AdvanceBilling=Facturation pr\u00E9alable
avanti.lbl.creditType_Rebill=refacturation
avanti.lbl.cur=Cabot
avanti.lbl.curr_current_rate=Taux actuel
avanti.lbl.currency=Devise
avanti.lbl.currencyDetails=D\u00E9tails de la devise
avanti.lbl.currencyRate=Cours des devises
avanti.lbl.currencyTotalCust=Devise totale du client
avanti.lbl.currencyTotalOrg=Devise totale de l'organisation
avanti.lbl.currentCost_short=C. Co\u00FBt
avanti.lbl.currentEmployee=Employ\u00E9 actuel\:
avanti.lbl.currentPartialSheetsFunctionality=Fonctionnalit\u00E9 actuelle des feuilles partielles
avanti.lbl.custName=Nom du client
avanti.lbl.custPartNumber=Num\u00E9ro de client \#
avanti.lbl.cust_code=Code Client
avanti.lbl.cust_curr_id=Devise du client
avanti.lbl.cust_id=Client
avanti.lbl.cust_name_2=Non du Client
avanti.lbl.cust_type=Type de Client
avanti.lbl.customInvoice=Facture personnalis\u00E9e\:
avanti.lbl.customer=Client
avanti.lbl.customerCancelDeposit=Le client annule le d\u00E9p\u00F4t
avanti.lbl.customerCode=Code client
avanti.lbl.customerDepositReceived=D\u00E9p\u00F4ts clients re\u00E7us
avanti.lbl.customerName=Non du Client
avanti.lbl.customerPostage=Frais de port client
avanti.lbl.customerPrice=Prix \u200B\u200Bclient
avanti.lbl.customerType=Type de Client
avanti.lbl.customsValue=Valeur en douane
avanti.lbl.cutoffCylinder=Cylindre de coupure
avanti.lbl.cutoffDie=Matrice de coupure
avanti.lbl.cutoffSize2=Taille coup\u00E9e (in)
avanti.lbl.cutoffSize2_mm=Taille coup\u00E9e (mm)
avanti.lbl.cuts\#=\# Coupes
avanti.lbl.cutterSpoils=Butin de coupe
avanti.lbl.cutting=Coupe
avanti.lbl.dateAndTime=Date et l'heure
avanti.lbl.dateFrom=Dater de
avanti.lbl.dateOnly=Rendez-vous uniquement
avanti.lbl.dateRanges=Plages de dates
avanti.lbl.dateTo=Date \u00E0
avanti.lbl.datesource=Source de date
avanti.lbl.december=D\u00E9cembre
avanti.lbl.defaultFreightClass=Classe de fret par d\u00E9faut
avanti.lbl.defaultNMFCitemNo=Num\u00E9ro d'article NMFC par d\u00E9faut
avanti.lbl.defaultProducingPlantRevenue=Revenu d'usine de production par d\u00E9faut%
avanti.lbl.defaultSheetUtilizationForPartialSheets=% d\u2019utilisation des feuilles par d\u00E9faut pour les feuilles partielles
avanti.lbl.deleteImportingOrdersLabel=Supprimer les commandes \u00AB Importer \u00BB
avanti.lbl.deletingCurrency=Cette devise ne peut pas \u00EAtre supprim\u00E9e car elle est associ\u00E9e \u00E0 la transaction client.
avanti.lbl.deliveryMode=Mode de livraison
avanti.lbl.deliveryTime=Heure de livraison
avanti.lbl.department=D\u00E9partement
avanti.lbl.departmentGroup=Groupe de d\u00E9partements
avanti.lbl.departments=D\u00E9partements
avanti.lbl.depositApplyReferenceType_CancelCashReceipt=Annuler le d\u00E9p\u00F4t de re\u00E7u en esp\u00E8ces
avanti.lbl.depositApplyReferenceType_Refund=Rembourser
avanti.lbl.dept=D\u00E9partement
avanti.lbl.dept_id=D\u00E9partement
avanti.lbl.destinationEntryFacilityType=Type d'installation d'entr\u00E9e de destination
avanti.lbl.details=D\u00E9tails
avanti.lbl.deviceName=Nom de l'appareil
avanti.lbl.deviceStatus=Statut du p\u00E9riph\u00E9rique
avanti.lbl.deviceType=Type d'appareil
avanti.lbl.die=Mourir
avanti.lbl.dieCylinderInfo=Infos sur la matrice / cylindre
avanti.lbl.difficulty=Difficult\u00E9
avanti.lbl.dimension_l_gf=longueur
avanti.lbl.dimension_l_gf_1=largeur
avanti.lbl.dimension_w_gf=largeur
avanti.lbl.dimension_w_gf_1=hauteur
avanti.lbl.dimensions=Dimensions
avanti.lbl.discountTypeShort=Type de remise
avanti.lbl.dismiss=Rejeter
avanti.lbl.dismissed=Rejet\u00E9
avanti.lbl.displayDelivery=Afficher la livraison?
avanti.lbl.displayWarningMsg=Afficher le message d'avertissement
avanti.lbl.displayWarningMsgAndMinOrdCharge=Afficher un message d'avertissement et facturer sur la facture des frais de commande minimum
avanti.lbl.distributeQuantityWarning=L'ic\u00F4ne "Distribuer la quantit\u00E9" ne peut \u00EAtre utilis\u00E9e que lorsqu'il y a 2 t\u00E2ches/sections ou plus sur l'op\u00E9ration.
avanti.lbl.distributionDetails=D\u00E9tails de la distribution
avanti.lbl.distributionOutOfBalance=La distribution est d\u00E9s\u00E9quilibr\u00E9e.
avanti.lbl.distributionsExchange=\u00C9change de distributions GP
avanti.lbl.divide=Diviser
avanti.lbl.dmi_impressionCountBy_color=Couleur
avanti.lbl.doNotSendDistribution=N'envoyez pas la distribution de la facture \u00E0 GP.
avanti.lbl.doNotShowAgain=Ne plus jamais afficher ce message
avanti.lbl.doNotShowAgainforAllFutureReleases=Ne plus me montrer cela pour toutes les versions futures
avanti.lbl.docsMgmPurgeCategory=<html><head></head><body><b>Cat\u00E9gorie</b> - Purger ou archiver les fichiers plus anciens que</body></html>
avanti.lbl.docsMgmPurgeCriteria=<html><head></head><body><b>Crit\u00E8res de purge ou d'archivage</b></body></html>
avanti.lbl.docsMgmPurgeDateOlder=<html><head></head><body><b>Date</b> - Purger les fichiers plus anciens que</body></html>
avanti.lbl.docsMgmPurgeStatus=<html><head></head><body><b>Statut</b> - Purger ou archiver les fichiers plus anciens que</body></html>
avanti.lbl.documentManagement_addSalesOrderFolder=Ajouter \u00AB\u00A0Dossiers de commandes\u00A0\u00BB au dossier de commandes syst\u00E8me
avanti.lbl.documentManagement_basedOnCategory=Cat\u00E9gorie
avanti.lbl.documentManagement_basedOnType=Cr\u00E9er un sous-dossier en fonction du type de fichier
avanti.lbl.documentManagement_category=Cat\u00E9gorie
avanti.lbl.documentManagement_createSubFolderBasedOn=Cr\u00E9er un sous-dossier bas\u00E9 sur la cat\u00E9gorie
avanti.lbl.documentManagement_filesOlderThan=Fichiers plus anciens que
avanti.lbl.documentMissingAssociation=Aucun document
avanti.lbl.documentsStoragePath_jobs=Des dossiers
avanti.lbl.documents_replaceExistingFile=Fichier du m\u00EAme nom d\u00E9j\u00E0 t\u00E9l\u00E9charg\u00E9 pour cette commande. Voulez-vous remplacer un fichier existant par la nouvelle version ?
avanti.lbl.done=Termin\u00E9
avanti.lbl.doubleRound=Double tour
avanti.lbl.downloadRFQ=T\u00E9l\u00E9charger la demande de devis
avanti.lbl.dpdCollectionDate=Date et heure de collecte
avanti.lbl.dpdCollectionOnDelivery=Collecte \u00E0 la livraison
avanti.lbl.dpdLiability=Responsabilit\u00E9
avanti.lbl.dpdLiabilityValue=Valeur de responsabilit\u00E9
avanti.lbl.dpdParcelDescription=Description du colis
avanti.lbl.dueAtCustomer=\u00C0 payer au client
avanti.lbl.dueDate=Date d'\u00E9ch\u00E9ance
avanti.lbl.dynamicsGP_applyAdvBillSkipped=Ignor\u00E9 appliquer la facture avanc\u00E9e {0}.
avanti.lbl.dynamicsGP_applyCashReceiptSkipped=Ignor\u00E9 appliquer le re\u00E7u de caisse {0}.
avanti.lbl.dynamicsGP_applyCreditNoteSkipped=Ignor\u00E9 appliquer la note de cr\u00E9dit {0}.
avanti.lbl.dynamicsGP_creditNoteNotReceived=La note de cr\u00E9dit a \u00E9t\u00E9 envoy\u00E9e mais n'a pas \u00E9t\u00E9 re\u00E7ue.
avanti.lbl.dynamicsGP_invoiceJournalEntryAccountInvalid=\u00C9critures de journal de facture pour la facture\: {0} il manque un ou plusieurs comptes g\u00E9n\u00E9raux. Il n'a pas \u00E9t\u00E9 envoy\u00E9 \u00E0 GP.
avanti.lbl.dynamicsGP_invoiceNotReceived=La facture a \u00E9t\u00E9 envoy\u00E9e mais n'a pas \u00E9t\u00E9 re\u00E7ue.
avanti.lbl.dynamicsGP_invoicesWithForceRefreshFlag=La ou les factures suivantes ont au moins un travail avec des co\u00FBts suppl\u00E9mentaires confirm\u00E9s apr\u00E8s la cr\u00E9ation de la facture.\r\nVeuillez actualiser la distribution pour r\u00E9cup\u00E9rer les co\u00FBts suppl\u00E9mentaires et relancer l'inscription pour publier\u00A0\:
avanti.lbl.dynamicsGP_invoicesWithUnConfirmedCosts=La ou les factures suivantes ont au moins un travail avec des co\u00FBts suppl\u00E9mentaires \u00AB\u00A0non confirm\u00E9s\u00A0\u00BB. Veuillez confirmer les co\u00FBts dans Saisie des feuilles de temps, puis actualiser la r\u00E9partition des factures pour r\u00E9cup\u00E9rer les co\u00FBts suppl\u00E9mentaires, puis relancer l'inscription pour publier\u00A0\:
avanti.lbl.edgesForHeight=P\u00E9rim\u00E8tre Marges perdues largeur
avanti.lbl.edgesForWidth=P\u00E9rim\u00E8tre Marges perdues longeur
avanti.lbl.editItem=Modifier l'article
avanti.lbl.email=Email
avanti.lbl.emailAllSuppliers=Email tous les fournisseurs
avanti.lbl.emailPostageDeposit=Envoyer la demande de d\u00E9p\u00F4t \r\nd'affranchissement au contact client
avanti.lbl.employee=Employ\u00E9
avanti.lbl.employeeCodeCannotBeEmpty=Le code d'employ\u00E9 ne peut pas \u00EAtre vide\!
avanti.lbl.employeeFullName=Nom complet de l'employ\u00E9
avanti.lbl.employeeId=identifiant de l'employ\u00E9
avanti.lbl.employee_code=Code d'employ\u00E9
avanti.lbl.employees=Employ\u00E9s
avanti.lbl.employeesSinglePlural=Employ\u00E9s
avanti.lbl.endShiftReply=travaille activement dans l'atelier et ne peut pas \u00EAtre d\u00E9connect\u00E9.
avanti.lbl.endTime=Heure de fin
avanti.lbl.endTimes=Heures de Fin
avanti.lbl.equals=\u00C9quivaut \u00E0
avanti.lbl.equipment=\u00C9quipement
avanti.lbl.errorList=Liste des erreurs
avanti.lbl.estimate=Estimation
avanti.lbl.estimateCalculator=Calculateur d'estimation
avanti.lbl.estimateCost=Co\u00FBt estim\u00E9
avanti.lbl.estimateUOM=Estimation UOM
avanti.lbl.exchangeGainLoss=\u00C9change de gains / perte
avanti.lbl.exchangeMethod=M\u00E9thode d'\u00E9change *
avanti.lbl.exclAcctReceivables=Exclure les comptes clients
avanti.lbl.exclSalesTax=Exclure la taxe de vente
avanti.lbl.excludeDocStreamFromInvoice=Exclure ce flux de documents de la facturation ou appara\u00EEtre dans le pr\u00EAt \u00E0 facturer
avanti.lbl.excludeNegOnHand=Exclure le n\u00E9gatif en main
avanti.lbl.exclueFromAvaTax=Exclure de la transaction AvaTax
avanti.lbl.existingUOM=Utiliser l'UdM existante
avanti.lbl.exitOnLogoutLabel=Quitter Slingshot \u00E0 la d\u00E9connexion
avanti.lbl.expected=Attendu
avanti.lbl.expectedDate=Date de livraison pr\u00E9vue
avanti.lbl.expectedInTransType_All=Tout
avanti.lbl.expectedInTransType_PurchaseOrder=Bon de commande
avanti.lbl.exportOrder_exportDetailLevel=Niveau de d\u00E9tail de l'exportation
avanti.lbl.exportOrder_lineItemXml=T\u00E9l\u00E9charger le fichier XML de l'\u00E9l\u00E9ment de campagne
avanti.lbl.exportOrder_sectionXml=T\u00E9l\u00E9charger le XML de la section/t\u00E2che
avanti.lbl.extPriceExchange=\u00C9change de prix \u00E9tendu
avanti.lbl.extendedCost=Co\u00FBt \u00E9tendu
avanti.lbl.extendedPrice=Ext.\u00A0Prix
avanti.lbl.extendedPrice_full=Prix \u200B\u200Bprolong\u00E9e
avanti.lbl.extraCode=Code suppl\u00E9mentaire
avanti.lbl.extraPlateMRTime/Cost=Temps de pr\u00E9para...
avanti.lbl.extraPlateMRTime/Cost_tooltip=Temps de pr\u00E9paration de la plaque suppl\u00E9mentaire\r\n
avanti.lbl.extraPlates=Plaques suppl\u00E9mentaires
avanti.lbl.faceTrim=Garniture de visage
avanti.lbl.fanfoldAt=Fan Fold At (in)
avanti.lbl.february=F\u00E9vrier
avanti.lbl.fedEx_DirectSignatureReq=Signature directe requise
avanti.lbl.fedEx_IndirectSignatureReq=Signature indirecte requise
avanti.lbl.fedEx_NoSignatureReq=Aucune signature requise
avanti.lbl.feedLength=Longueur d'aliment
avanti.lbl.feetPerHour_short=Ft/Hr
avanti.lbl.feetPerMin_short=Ft/Min
avanti.lbl.fifoCost=Co\u00FBt
avanti.lbl.filterTable=Filtrer le tableau
avanti.lbl.filters=Les filtres
avanti.lbl.finalTrim=Garniture finale
avanti.lbl.finish=Finition
avanti.lbl.finishSize=Taille de finition
avanti.lbl.finishSizeFull_gf=Taille de finition
avanti.lbl.finishSizeFull_gf_1=Zone de vie
avanti.lbl.finishSizeHLong=Taille de finition
avanti.lbl.finishSizeL=Longeur fini
avanti.lbl.finishSizeLLong=Longeur fini
avanti.lbl.finishSizeW=Largeur\r\n fini
avanti.lbl.finishSizeWLong=Largeur fini
avanti.lbl.finishingSpoils=Butin de finition
avanti.lbl.firsMRTime/Cost=Premier temps de...
avanti.lbl.firsMRTime/Cost_tooltip=Premier temps de pr\u00E9paration
avanti.lbl.flatSize=Taille plate (W x L)
avanti.lbl.flatSizeHLong=Taille \u00E0 plat (HxW)
avanti.lbl.flatSize_gf=Taille \u00E0 plat
avanti.lbl.flatSize_mm=Taille \u00E0 plat (WxL mm)
avanti.lbl.flexoWorkType_envelope=Enveloppe
avanti.lbl.flexoWorkType_webCutoff=Coupure Web
avanti.lbl.folder=Dossier
avanti.lbl.folderSpoils=Dossier g\u00E2te
avanti.lbl.folding=Pliant
avanti.lbl.folio=Folio
avanti.lbl.footTrim=Garniture de pied
avanti.lbl.footagePerRollAfterSlitter=Nombre de s\u00E9quences par rouleau apr\u00E8s d\u00E9coupe
avanti.lbl.freightAccountNumber=Num\u00E9ro de compte de fret
avanti.lbl.freightAddress=Adresse de fret
avanti.lbl.freightClass=Classe de fret
avanti.lbl.freightExceededExpected_msg=Le fret r\u00E9el a d\u00E9pass\u00E9 le montant du fret pr\u00E9vu\!
avanti.lbl.freightIn=Fret dans
avanti.lbl.freightOut=Fret
avanti.lbl.freightReimbursement=Remboursement du fret
avanti.lbl.freightTaxExempt=Exon\u00E9ration de la taxe de transport
avanti.lbl.from.shipped.date=\u00C0 partir de la date d'exp\u00E9dition
avanti.lbl.fromCategory=De la cat\u00E9gorie
avanti.lbl.fromCustomerPartNo=Du num\u00E9ro de pi\u00E8ce du client
avanti.lbl.fromDate=Partir de la date
avanti.lbl.fromJobStartDate=\u00C0 partir de la date de d\u00E9but de l'emploi
avanti.lbl.fromProducingOrder=De l'ordre de production
avanti.lbl.fromSalesOrderDate=\u00C0 partir de la date de la commande client
avanti.lbl.fromStatus=Du statut
avanti.lbl.fromTransactionDate=De la date de transaction
avanti.lbl.fromTransferOrder=De l'ordre de transfert
avanti.lbl.fscItem=Item FSC
avanti.lbl.ft/min=pi/min
avanti.lbl.ftplabelstyle1=Style d'\u00E9tiquette d'inventaire 1
avanti.lbl.ftplabelstyle2=Style d'\u00E9tiquette d'inventaire 2
avanti.lbl.ftppallettag=\u00C9tiquette de palette
avanti.lbl.fullSheets=Full Sheets
avanti.lbl.full_name=Nom complet
avanti.lbl.functions=Fonctions
avanti.lbl.gangLayout=Disposition et groupe
avanti.lbl.gangLithoWebFolder=Les sections Web Litho utilisant la m\u00E9thode de remise du dossier ne peuvent pas \u00EAtre ajout\u00E9es \u00E0 une section Gang
avanti.lbl.gangRun=Course de groupe
avanti.lbl.generateRawMaterialReport=G\u00E9n\u00E9rer le rapport de mati\u00E8res premi\u00E8res
avanti.lbl.generateReturn=G\u00E9n\u00E9rer un retour
avanti.lbl.getShippingRates?=\u00CAtes-vous s\u00FBr de vouloir envoyer la demande de tarif\u00A0?
avanti.lbl.glControlAccount_APInvoiceSalesTax=A/P Taxe de vente sur facture
avanti.lbl.glControlAccount_GainOrLossOnCurrencyExchange=Devise - Discuter des d\u00E9p\u00F4ts / S'applique
avanti.lbl.glControlAccount_InventoryAdjustments=Ajustements d'inventaire
avanti.lbl.glControlAccount_SalesTax=Taxe de vente
avanti.lbl.gpIntegration=GP Integration
avanti.lbl.gpSOPDistType1=Ventes (SALE)
avanti.lbl.gpSOPDistType11=Frais de commission (COMMEXP)
avanti.lbl.gpSOPDistType12=Commission \u00E0 payer (COMMPAY)
avanti.lbl.gpSOPDistType13=Autre (OTHER)
avanti.lbl.gpSOPDistType14=CMV (COGS)
avanti.lbl.gpSOPDistType15=Inventaire (INV)
avanti.lbl.gpSOPDistType16=Retourner (RETURN)
avanti.lbl.gpSOPDistType2=Comptes d\u00E9biteurs (RECV)
avanti.lbl.gpSOPDistType3=EN ESP\u00C8CES (CASH)
avanti.lbl.gpSOPDistType4=Conditions prises (TAKEN)
avanti.lbl.gpSOPDistType5=Conditions disponibles (AVAIL)
avanti.lbl.gpSOPDistType6=\u00C9changer (TRADE)
avanti.lbl.gpSOPDistType7=Cargaison (FREIGHT)
avanti.lbl.gpSOPDistType8=Divers (MISC)
avanti.lbl.gpSOPDistType9=Imp\u00F4ts (TAXES)
avanti.lbl.grid=la grille
avanti.lbl.grindOff=Taille de mouture
avanti.lbl.gripper=Pince
avanti.lbl.group2=Groupe
avanti.lbl.groupingOptionsJdfFold=Mod\u00E8le pli\u00E9
avanti.lbl.groupingOptionsSubstrate=Substrat
avanti.lbl.gutterSize=Taille de goutti\u00E8re
avanti.lbl.gutters=Goutti\u00E8res
avanti.lbl.headTrim=Garniture de t\u00EAte
avanti.lbl.helper_titleCase=Assistant
avanti.lbl.holdForAllQtys=Tenir pour toutes qt\u00E9s
avanti.lbl.holdForAllQtys_2=Tenir pour toutes \r\nles quantit\u00E9s
avanti.lbl.howManyCopies_msg=Combien de copies de cette section souhaitez-vous cr\u00E9er\u00A0?
avanti.lbl.httpExport_OutboundUrl=URL sortante
avanti.lbl.iItemInKits=Cet article fait partie des kits \u00E0 construire sur commande ou des kits assembl\u00E9s suivants
avanti.lbl.idleSince=Inactif depuis
avanti.lbl.imageArea=Image Area
avanti.lbl.imageSize=Taille de l'image (WxL)
avanti.lbl.imageSizeH=Taille de l'image (Hauteur)
avanti.lbl.imageSizeL=Taille de l'image (longueur)
avanti.lbl.imageSizeL_gf=Taille de l'image (<<dimension>>)
avanti.lbl.imageSizeW=Taille de l'image (largeur)
avanti.lbl.imageSizeW_gf=Taille de l'image (<<dimension>>)
avanti.lbl.imageSize_in=Taille de l'image (WxL in)
avanti.lbl.imageSize_mm=Taille de l'image (WxL mm)
avanti.lbl.images=Images
avanti.lbl.imagesDetailView=Images - Vue d\u00E9taill\u00E9e
avanti.lbl.impHr=Imp/hr.
avanti.lbl.importBadFOBAddress=Impossible de trouver l'adresse du client \u00E0 partir du code d'adresse FOB et du code client de l'adresse FOB.
avanti.lbl.importShipTo_clearAllExistingAddresses=Effacez TOUTES les adresses existantes avant l'importation
avanti.lbl.import_customs_duty_value=Valeur des droits de douane \u00E0 l'importation
avanti.lbl.import_taxes_value=Valeur des taxes \u00E0 l'importation
avanti.lbl.imposition=Imposition
avanti.lbl.impositionModels=Mod\u00E8les d'imposition
avanti.lbl.impressions=Impressions
avanti.lbl.impressionsTT=La quantit\u00E9 d'impressions est bas\u00E9e sur le nombre d'images produites dans un tirage. Impression d'une seule feuille 1 face \= 1 impression, impression d'une seule feuille 2 faces \= 2 impressions.
avanti.lbl.in=(in.)
avanti.lbl.inPlant=Dans l'usine
avanti.lbl.inReview=En revue
avanti.lbl.includeCreditNotes=Inclure les notes de cr\u00E9dit
avanti.lbl.includePostageMarkupInSales=Inclure les frais d'affranchissement dans les ventes
avanti.lbl.includeSelectedBatchInvoiceFiles=Inclure les fichiers de factures group\u00E9es s\u00E9lectionn\u00E9s
avanti.lbl.includeSelectedContracts=Inclure les contrats s\u00E9lectionn\u00E9s
avanti.lbl.includeSelectedCustomers=Inclure les clients s\u00E9lectionn\u00E9s
avanti.lbl.includeSelectedTransferOrders=Inclure les ordres de transfert s\u00E9lectionn\u00E9s
avanti.lbl.includesalesofchildcust=Inclure les ventes du client Enfant dans Parent
avanti.lbl.ink=Encre
avanti.lbl.inkCost=Co\u00FBt d'encre
avanti.lbl.inkCoverage=Couverture d'encre totale \u00E0 partir de
avanti.lbl.inkMixTime/Cost=Temps de m\u00E9lange d'encre
avanti.lbl.ink_setup=Configuration de l'encre
avanti.lbl.inkjetting=Jet d'encre
avanti.lbl.inlineOperations=Op\u00E9rations en ligne
avanti.lbl.integrationLog=Journal d'int\u00E9gration
avanti.lbl.integrationProblem=Un \u00AB\u00A0\!\u00BB indique que cette exp\u00E9dition utilise une int\u00E9gration de transporteur, mais qu'aucun appel de transporteur n'a \u00E9t\u00E9 effectu\u00E9 avec succ\u00E8s. Cela peut \u00EAtre d\u00FB \u00E0 des entr\u00E9es manquantes ou non valides.
avanti.lbl.interBranchProjectPlanValidation_incorrectPlantQuantityAllocation=a un pourcentage de revenu mais n'a pas de quantit\u00E9s de production
avanti.lbl.interBranchProjectPlanValidation_invalid=Ce n'est pas un plan de projet interprofessionnel valide. Veuillez v\u00E9rifier les revenus de l'usine et les allocations de quantit\u00E9.
avanti.lbl.interBranchProjectPlanValidation_missingPlantRevenue=L'allocation totale des revenus de l'usine doit \u00EAtre \u00E9gale \u00E0 100%.
avanti.lbl.interBranchProjectPlanValidation_missingPlantRevenueRecord=Le plan de projet ne contient pas l'enregistrement des recettes de l'usine d'origine.
avanti.lbl.interBranchProjectPlanValidation_missingProducingPlantRevenueAllocation=Il n'y a pas d'allocation des revenus des usines de production.
avanti.lbl.interBranchProjectPlanValidation_missingProductionQuantities=a un pourcentage de revenu mais n'a pas de quantit\u00E9s de production
avanti.lbl.interBranchQtyAllocation_NoPressAssignedForm=La quantit\u00E9 ne peut \u00EAtre affect\u00E9e que si le formulaire de la section a la presse affect\u00E9e \u00E0 l'usine.
avanti.lbl.interBranchQtyAllocation_NoPressAssignedSection=La quantit\u00E9 ne peut \u00EAtre attribu\u00E9e que si la section d\u2019impression de la ligne est affect\u00E9e \u00E0 la presse.
avanti.lbl.interBranchQtyAllocation_NoTaskAssignedPlant=La quantit\u00E9 ne peut \u00EAtre affect\u00E9e que si la t\u00E2che est affect\u00E9e \u00E0 la division.
avanti.lbl.interBranch_ReceivableJournalEntry=Entr\u00E9e du journal des comptes clients inter-succursales
avanti.lbl.interBranch_SalesOrderJournalEntry=\u00C9criture au journal des commandes client interservices
avanti.lbl.interbranchSettings=Param\u00E8tres inter-succursales
avanti.lbl.internalOnly=Interne seulement
avanti.lbl.internalTime=Temps interne (min.)
avanti.lbl.inventoryWebhook=webhook d'inventaire
avanti.lbl.invoice=Facture
avanti.lbl.invoiceAccount=Compte Facture
avanti.lbl.invoiceBalanceAmount=Montant du solde de la facture
avanti.lbl.invoiceCommittedAmount=Montant engag\u00E9 sur la facture
avanti.lbl.invoiceCompleteTT=Facturer les commandes client termin\u00E9es. \r\n\r\nCette option fait r\u00E9f\u00E9rence \u00E0 la facturation des commandes dans leur int\u00E9gralit\u00E9 pour \u00E9viter de facturer plusieurs fois des exp\u00E9ditions partielles. \r\nSi la pr\u00E9f\u00E9rence syst\u00E8me 203 (Quantit\u00E9 par d\u00E9faut pour la facture) est d\u00E9finie sur Quantit\u00E9 command\u00E9e, alors toute exp\u00E9dition (partielle ou compl\u00E8te) rendra la commande disponible pour la facturation. \r\nSi la pr\u00E9f\u00E9rence syst\u00E8me 203 est d\u00E9finie sur Quantit\u00E9 exp\u00E9di\u00E9e, la commande doit \u00EAtre enti\u00E8rement exp\u00E9di\u00E9e pour \u00EAtre disponible pour la facturation.
avanti.lbl.invoiceDate=Date de facturation
avanti.lbl.invoiceDetail=D\u00E9tail de la facture
avanti.lbl.invoiceDistribution=R\u00E9partition des factures
avanti.lbl.invoiceLocked=* Cette facture est actuellement verrouill\u00E9e par Batch Invoicing.
avanti.lbl.invoiceRedistribution_bothCostAndRevenue=Co\u00FBt et revenus
avanti.lbl.invoiceRedistribution_costOnly=Co\u00FBt seulement
avanti.lbl.invoiceRedistribution_notApplicable=Sans objet
avanti.lbl.invoiceRedistribution_revenueOnly=Revenus uniquement
avanti.lbl.invoiceType=Type de facture
avanti.lbl.invoicesBasedOn=Factures bas\u00E9es sur
avanti.lbl.item=Article
avanti.lbl.itemDescription=Description de l'article
avanti.lbl.itemDescription2=Article - Description 2
avanti.lbl.itemDetailView=Article - Vue d\u00E9taill\u00E9e
avanti.lbl.itemDetails=D\u00E9tails de l'article
avanti.lbl.itemGroup=Groupe d'articles
avanti.lbl.itemReservedQuantities=Quantit\u00E9 r\u00E9serv\u00E9e pour l'item
avanti.lbl.itemSupplier=Fournisseurs
avanti.lbl.itemType=Type d'\u00E9l\u00E9ment
avanti.lbl.itemUPC=Code UPC
avanti.lbl.item_allow_backorders=Autoriser les commandes en retard
avanti.lbl.item_allow_commissions=Autoriser les commissions
avanti.lbl.item_allow_discounts=Autoriser les r\u00E9ductions
avanti.lbl.item_avg_lead_time=D\u00E9lai moyen
avanti.lbl.item_code=Code de l'article
avanti.lbl.item_color=Couleur
avanti.lbl.item_creation_date=Date de cr\u00E9ation de l'article
avanti.lbl.item_decimal_places=D\u00E9cimales Unit\u00E9s
avanti.lbl.item_desc=Description de l'article
avanti.lbl.item_dimensions=Dim. (LxWxH in)
avanti.lbl.item_dimensionsLengthWidth=Dimensions (LxW in)
avanti.lbl.item_dimensionsLengthWidthMetric=Dimensions (LxW mm)
avanti.lbl.item_dimensionsMetric=Dim. (LxWxH mm)
avanti.lbl.item_expiry_date=Date d'expiration de l'article
avanti.lbl.item_max_weight=Poids Max. (lbs)
avanti.lbl.item_max_weight2=Poids Max.
avanti.lbl.item_max_weightMetric=Poids Max. (kgs)
avanti.lbl.item_purchasetax_option=Option de taxe d'achat
avanti.lbl.item_salestax_option=Option de taxe de vente
avanti.lbl.item_standard_uom_id=Unit\u00E9s de stockage
avanti.lbl.item_status=Statut
avanti.lbl.item_type=Type d'\u00E9l\u00E9ment
avanti.lbl.itemclass=Classe
avanti.lbl.itemclass_id=Classe d'objet
avanti.lbl.itemqty_backord_qty=Quantit\u00E9 en rupture de stock
avanti.lbl.itemqty_expin_qty=Attendu dans
avanti.lbl.itemqty_onhand_qty=Quantit\u00E9 en main
avanti.lbl.items=Articles
avanti.lbl.itemselluom_list_price=Liste des prix
avanti.lbl.itemselluom_min_order_qty=Quantit\u00E9 minimum de commande
avanti.lbl.itemselluom_sell_uom_id=Unit\u00E9s de vente
avanti.lbl.january=Janvier
avanti.lbl.jdfBudget=Budget
avanti.lbl.jdfSchedule=Calendrier
avanti.lbl.jdfType_DownloadToCentralStorage=T\u00E9l\u00E9charger vers l'emplacement de ... \:
avanti.lbl.jdfType_ExcludeLayoutDetails=Exclure les d\u00E9tails de mise en page
avanti.lbl.jdfType_FileName=Nom de fichier
avanti.lbl.jdfType_PopulateFileName=Utiliser le nom de fichier comme nom de t\u00E2che
avanti.lbl.jdfType_PopulateFileName_tooltip=Remplissez le nom du fichier PDF en tant que nom du travail (JDF@DescriptiveName) dans le JDF.
avanti.lbl.job=Dossier
avanti.lbl.job\#=Dossier \#
avanti.lbl.jobCompletedDate=Date de fin du travail
avanti.lbl.jobCostTransactionStatus_posted=Publi\u00E9
avanti.lbl.jobCostTransactionStatus_unposted=non publi\u00E9
avanti.lbl.jobCostTransactions=Transactions de co\u00FBt du travail
avanti.lbl.jobDate=Date du dossier
avanti.lbl.jobDescription=Description du dossier
avanti.lbl.jobDetail=Dossier (D\u00E9tail)
avanti.lbl.jobDetails=D\u00E9tails du travail
avanti.lbl.jobNumber=Num\u00E9ro du dossier
avanti.lbl.jobNumbers=Num\u00E9ro(s) de dossier
avanti.lbl.jobStatus=Statut du dossier
avanti.lbl.jobTicket=Dossier
avanti.lbl.jobs=Des dossiers
avanti.lbl.journalEntries=Entr\u00E9es de journal
avanti.lbl.july=Juillet
avanti.lbl.june=Juin
avanti.lbl.labour=Travail
avanti.lbl.lastDate=Dernier rendez-vous
avanti.lbl.lastLeadTime=Dernier d\u00E9lai
avanti.lbl.lastLoginDate=Date de la derni\u00E8re connexion
avanti.lbl.lastPriceChange=Dernier changement de prix
avanti.lbl.layout=Disposition
avanti.lbl.leadOrder=Avance
avanti.lbl.legend=L\u00E9gende
avanti.lbl.length=Longueur (in)
avanti.lbl.length2=Longueur
avanti.lbl.lengthMetric=Longueur (mm)
avanti.lbl.lessDepositsPaymentsStacked=D\u00E9p\u00F4t
avanti.lbl.line=Ligne
avanti.lbl.lineItem=\u00C9l\u00E9ment
avanti.lbl.lineItemWon=\u00C9l\u00E9ment de campagne gagn\u00E9
avanti.lbl.lineNumber=Ligne \#
avanti.lbl.linearFeet=Pieds lin\u00E9aires
avanti.lbl.linearFt=Pieds lin\u00E9aires.
avanti.lbl.linearMetres=M\u00E8tres Lin\u00E9aires
avanti.lbl.link=Lien
avanti.lbl.listPrice=Liste des prix
avanti.lbl.location=Emplacement
avanti.lbl.lockedInvoices=Factures verrouill\u00E9es
avanti.lbl.longTimeEntryLabelShift=Temps \u00E9coul\u00E9 sup\u00E9rieur \u00E0 16 heures
avanti.lbl.m/min=m/min
avanti.lbl.m/u=M / U%
avanti.lbl.m/u_amount=Montant M/U
avanti.lbl.mWeight=Poids M / GSM
avanti.lbl.machineVariable=Variable
avanti.lbl.makeDebitsNegative=Rendre les d\u00E9bits n\u00E9gatifs
avanti.lbl.makeReadySheetsIncluded=Pr\u00E9parez des feuilles incluses dans cette valeur
avanti.lbl.makeReadySpoilage=Pr\u00E9parer et g\u00E2ter
avanti.lbl.manualAdjustmentsDetected=R\u00E9glages manuels d\u00E9tect\u00E9s.
avanti.lbl.manufactureItem=ID d'article du fabricant
avanti.lbl.manufactureName=Nom du Fabricant
avanti.lbl.march=Mars
avanti.lbl.margin=Marge
avanti.lbl.markAllMilestonesDone=Marquer tous les jalons comme termin\u00E9s
avanti.lbl.markup%=% De Profit
avanti.lbl.markupOverride%=Annulation<br />du marquage %
avanti.lbl.material=Mat\u00E9riel
avanti.lbl.materialEntries=Entr\u00E9es mat\u00E9rielles
avanti.lbl.materialForBack=Mat\u00E9riel pour le dos
avanti.lbl.materialForFront=Mat\u00E9riel pour l'avant
avanti.lbl.materialGL=Mat\u00E9riau GL
avanti.lbl.materialQty=Quantit\u00E9 de mat\u00E9riel
avanti.lbl.materials=Mat\u00E9riaux
avanti.lbl.maxHelpers=Max. Aides
avanti.lbl.maxRunSpoils=Max. Ex\u00E9cutez le butin
avanti.lbl.maxSlowDown=Max. Ralentir
avanti.lbl.may=Mai
avanti.lbl.message=Message
avanti.lbl.messages=Messages
avanti.lbl.meterFrom=De (m\u00E8tre)
avanti.lbl.meterPerHour_short=M/Hr
avanti.lbl.meterPerMin_short=M/Min
avanti.lbl.meterTo=\u00C0 (m\u00E8tre)
avanti.lbl.metersPerHour_short=M/Hr
avanti.lbl.metersPerMin_short=M/Min
avanti.lbl.method=M\u00E9thode
avanti.lbl.milestoneInfo=Informations importantes
avanti.lbl.milestones=\u00C9tapes
avanti.lbl.minimumOrderQtys=Quantit\u00E9 minimum de commande
avanti.lbl.minimumQtys=Quant. minimales
avanti.lbl.missingShipOrder=* Si vous ne voyez pas votre commande/adresse, saisissez la commande dans le filtre Commande client pour actualiser l'\u00E9cran.
avanti.lbl.mixedGrainLayout=Disposition de grain mixte - non prise en charge pour le moment.
avanti.lbl.mobileDeviceAuthenticationDate=Date d'authentification
avanti.lbl.mobileDeviceAuthenticationPasscode=Code d'authentification
avanti.lbl.mobileDevicePasscodeExpiryDate=Date d'expiration de l'appareil
avanti.lbl.mobileDeviceStatus_1=En attente d'authentification
avanti.lbl.mobileDeviceTag=\u00C9tiquette d'appareil mobile
avanti.lbl.modelMinSize=Mod\u00E8le Min.\u00A0Taille
avanti.lbl.mrFeet=D\u00E9chets de pr\u00E9paration (feet)
avanti.lbl.mrFeet_metric=D\u00E9chets de pr\u00E9paration (m\u00E8tres)
avanti.lbl.multipleSFJobs=Plusieurs travaux / sections ont \u00E9t\u00E9 s\u00E9lectionn\u00E9s pour cette op\u00E9ration.
avanti.lbl.multiply=Multiplier
avanti.lbl.multiship.warning=Vous devez s\u00E9lectionner un ou plusieurs \u00E9l\u00E9ments de ligne pour appliquer les adresses multi-exp\u00E9ditions import\u00E9es.
avanti.lbl.namesPerForm=Noms par forme
avanti.lbl.namesPerForm_tooltip=Multiplicateur pour \#Out;\u00A0\#Out \= \#Out x Noms / Forme
avanti.lbl.netWeight=Poids net
avanti.lbl.netsuiteAccountType=Type de compte NetSuite
avanti.lbl.newBalance=Nouvel \u00E9quilibre
avanti.lbl.newRecord=Nouvel enregistrement
avanti.lbl.newValue=Nouvelle valeur
avanti.lbl.no=Non
avanti.lbl.noApiResponseText=Supprimer la r\u00E9ponse de l'API
avanti.lbl.noBinLocationTracking=Suivi de l'emplacement sans bac
avanti.lbl.noExceptInvoiceStatus=Non, sauf \u00E9tat de la facture / note de cr\u00E9dit
avanti.lbl.noPackSizeAdjustment=Aucun ajustement de la taille de l'emballage
avanti.lbl.noPressAssociatedTask=Aucune valeur de colonne T\u00E2che associ\u00E9e n'est fournie pour l'onglet T\u00E2che associ\u00E9e. S\u00E9lectionnez un onglet T\u00E2che associ\u00E9e et indiquez la valeur de la colonne T\u00E2che associ\u00E9e ou supprimez la ligne enti\u00E8re.
avanti.lbl.noRevenue=Aucun revenu
avanti.lbl.non-inventory=non-inventaire
avanti.lbl.noneAvailalble=Aucun disponible
avanti.lbl.notBillable=non facturable
avanti.lbl.notTransferable=Non transf\u00E9rable
avanti.lbl.note=Remarque
avanti.lbl.note\:=Remarque\:
avanti.lbl.noteRelatedTo=Relier \u00E0
avanti.lbl.noteRelatedToReference=Relier \u00E0
avanti.lbl.noteType=Type de note
avanti.lbl.notes=Remarques
avanti.lbl.november=Novembre
avanti.lbl.nrExtraPlates/Cost=\# Plaques suppl\u00E9ment...
avanti.lbl.nrPagesPerForm=\# Pages par formulaire
avanti.lbl.nrPlates/Cost=\# Plaques
avanti.lbl.numberOfCuts=Nombre de coupes
avanti.lbl.numberOfRewindRolls=\# de rouleaux de rembobinage
avanti.lbl.numberOfTabs=Nombre d'onglets
avanti.lbl.numberWashups=\# Lavages
avanti.lbl.numbering=Num\u00E9rotage
avanti.lbl.october=Octobre
avanti.lbl.oldValue=Ancienne valeur
avanti.lbl.opCat=Cat\u00E9gorie
avanti.lbl.op_id=Op\u00E9ration
avanti.lbl.openInNewTab=Ouvrir dans un nouvel onglet
avanti.lbl.operation=Op\u00E9ration
avanti.lbl.operationProofLabel=Preuve
avanti.lbl.option1=Option-1
avanti.lbl.option2=Option-2
avanti.lbl.order=Ordre
avanti.lbl.orderCustomerCode=Commander le code client
avanti.lbl.orderDate=Date de commande
avanti.lbl.orderDocumentNumber=Num\u00E9ro du document de commande
avanti.lbl.orderEstimateStatus=Statut de l'estimation de la commande
avanti.lbl.orderEstimatorEmployeeId=ID d'employ\u00E9 de l'estimateur de commande
avanti.lbl.orderExportDetailLevel=Niveau de d\u00E9tail de l'exportation
avanti.lbl.orderExportLevel_lineItem=\u00C9l\u00E9ment de campagne (standard)
avanti.lbl.orderExportLevel_sectionAndTask=D\u00E9tails de la session et de la t\u00E2che
avanti.lbl.orderShipStatus_allReleasedProjectPlans=Tous les plans de projet publi\u00E9s
avanti.lbl.orderStatus_tableView=\u00C9tat de la commande \u2013 Affichage du tableau
avanti.lbl.orderWorkflowAction=Action de flux de travail de commande
avanti.lbl.ordh_customer_po=Bon de commande client
avanti.lbl.ordrevd_line_num=Ligne
avanti.lbl.ordrevh_promise_date=\u00C0 payer au client
avanti.lbl.orgWebServiceToken=Jeton de service Web
avanti.lbl.originatingAmountApplied=Montant d'origine appliqu\u00E9
avanti.lbl.originatingBalance=Solde d'origine
avanti.lbl.originatingDistributions=Distributions d'origine
avanti.lbl.other1=Autre 1
avanti.lbl.other2=Autre 2
avanti.lbl.other3=Autre 3
avanti.lbl.otherDie1=Autre matrice \#1
avanti.lbl.otherDie2=Autre matrice \#2
avanti.lbl.otherSpoils=Autres d\u00E9chets
avanti.lbl.outputFolderEdit=Le dossier de sortie doit \u00EAtre modifi\u00E9 manuellement.
avanti.lbl.outputFormat=Format d'impression
avanti.lbl.outsideDiameter=Diam\u00E8tre Ext\u00E9rieur (in)
avanti.lbl.outsideDiameter_mm=Diam\u00E8tre Ext\u00E9rieur (mm)
avanti.lbl.outsourcedServices=Services ext\u00E9rieurs
avanti.lbl.overlap=Chevauchement
avanti.lbl.overrideCost_short=O. Co\u00FBt
avanti.lbl.overrideForms=Remplacer les form...
avanti.lbl.overrideForms_tooltip=Remplacer les formulaires
avanti.lbl.p/o=Bon de commande
avanti.lbl.pack=Emballer
avanti.lbl.packQty=Pack Qt\u00E9
avanti.lbl.packingSlipDetail=D\u00E9tail du emballer
avanti.lbl.pageSets=Ensembles de pages
avanti.lbl.pagesetSheetTooBig=Substrat trop grand pour la presse, le pr\u00E9-coupage doit \u00EAtre activ\u00E9 ou utiliser une feuille de presse plus petite.
avanti.lbl.palletNo=Palette \#
avanti.lbl.palletTable=Table de palette
avanti.lbl.pallets=Palettes
avanti.lbl.panelBleedsForHeight=Panneau perdu des marges longeur
avanti.lbl.panelBleedsForWidth=panneau perdu des marges largeur (G / D)
avanti.lbl.panels=Panneaux
avanti.lbl.paper=Substrat
avanti.lbl.paperBrand=Poids / Fini
avanti.lbl.paperBrandName=Poids/Finition
avanti.lbl.paperCost=Co\u00FBt du substrat
avanti.lbl.paperGrade=Type de mat\u00E9riel
avanti.lbl.paperGradeBrand=Poids/Fini
avanti.lbl.paperGradeBrand_g=Poids (g) / Finition
avanti.lbl.paperGradeBrand_oz=Poids (oz) / Finition
avanti.lbl.paperGradeLength=Longueur
avanti.lbl.paperMinSize=Substrat Min.\u00A0Taille
avanti.lbl.paperSelected=Substrat s\u00E9lectionn\u00E9
avanti.lbl.paperSizeCaliper=\u00C9trier
avanti.lbl.paperSizeFirstDim=Premi\u00E8re dimension (in)
avanti.lbl.paperSizeFirstDimShort2=Premi\u00E8re Dim.
avanti.lbl.paperSizeSecondDim2=Grain
avanti.lbl.paperStandards=Normes de substrat
avanti.lbl.paperWeight=Poids du substrat
avanti.lbl.paperWeight_kgs=Poids du substrat (kgs)
avanti.lbl.paperWeight_lbs=Poids du substrat (lbs)
avanti.lbl.paper_setup=Configuration du substrat
avanti.lbl.partial=Partiel
avanti.lbl.passesPerForm=\# Passes par formulaire
avanti.lbl.password=Mot de passe
avanti.lbl.patch=Correctif
avanti.lbl.patch\#=Correctif \#
avanti.lbl.patchAdhesiveQty=Quantit\u00E9 d'adh\u00E9sif pour patch
avanti.lbl.patchDescription=Description du correctif
avanti.lbl.patchLength=Longueur du patch
avanti.lbl.patchMaterialQty=Quantit\u00E9 de mat\u00E9riau de patch
avanti.lbl.patchMaterialSummary=R\u00E9sum\u00E9 du mat\u00E9riau du patch
avanti.lbl.patchMaterialWidth=Largeur du mat\u00E9riau de patch
avanti.lbl.patchSide=Patch c\u00F4t\u00E9 1 ou 2
avanti.lbl.patchTable=Tableau des patchs
avanti.lbl.patchWidth=Largeur de patch
avanti.lbl.path=Chemin
avanti.lbl.pause=Pause
avanti.lbl.paymentsBasedOn=Paiements bas\u00E9s sur
avanti.lbl.paymentsOnRegister=Paiements sur registre
avanti.lbl.paymethod_checkbook_id=\r\nID de ch\u00E9quier
avanti.lbl.paymethod_checkbook_id_tooltip=Cet ID doit correspondre \u00E0 l'ID du ch\u00E9quier dans Dynamics GP.
avanti.lbl.per=Par
avanti.lbl.perAddlThousand=Par Addl.\u00A0Mille
avanti.lbl.perLinearFoot=par pied lin\u00E9aire
avanti.lbl.perLinearMeter=par m\u00E8tre lin\u00E9aire
avanti.lbl.perPiece=(par pi\u00E8ce)
avanti.lbl.percent_tooltip=Vous devez entrer un signe% \u00E0 la fin.
avanti.lbl.perf=Perforer
avanti.lbl.perfScore=Perforer/Marquer
avanti.lbl.perfScoreMRTime/Cost=Perforer/marquer le...
avanti.lbl.perfScoreMRTime/Cost_tooltip=Perforer/marquer le temps de pr\u00E9paration
avanti.lbl.perfecting=Perfection
avanti.lbl.phone=T\u00E9l\u00E9phone
avanti.lbl.pick=Choisir
avanti.lbl.pickOrderCustomerCodes=Choisir les codes client de la commande
avanti.lbl.pickSlipNumber=Num\u00E9ro de liste de s\u00E9lection
avanti.lbl.pickStatus=Choisir le statut
avanti.lbl.pickingSlipDetail=D\u00E9tail de liste de s\u00E9lection
avanti.lbl.pieces=Pi\u00E8ces
avanti.lbl.pinFeed_left=Alimentation par broches \u00E0 gauche
avanti.lbl.pinFeed_right=Alimentation des broches \u00E0 droite
avanti.lbl.plantGlobalAddress=Adresse globale de l'usine
avanti.lbl.plantOrder=Ordre de l'usine
avanti.lbl.plantRevenueSplit=R\u00E9partition des revenus de l'usine
avanti.lbl.plate=Plaque
avanti.lbl.plateMRTime/Cost=Temps de plaque
avanti.lbl.poReqNumber=Num\u00E9ro de bon de commande
avanti.lbl.poStatus=Statut du bon de commande
avanti.lbl.poTotalAmount=Montant total du bon de commande
avanti.lbl.postMaterialConfirmation=\u00CAtes-vous s\u00FBr de vouloir enregistrer du mat\u00E9riel?
avanti.lbl.postPressSpoils_metric=Butin post-presse (m\u00E8tres)
avanti.lbl.postage=Frais de poste
avanti.lbl.postageBillingCode=Code de facturation d'affranchissement
avanti.lbl.postageMarkupIncludedInSales=La majoration d'affranchissement est incluse dans les ventes.
avanti.lbl.posteddate=Date de publication
avanti.lbl.pr_ContractNumber=Num\u00E9ro de contrat
avanti.lbl.pr_IndiciaNumber=Poids Max. (lbs)
avanti.lbl.preTrim=Pr\u00E9-garniture
avanti.lbl.pred=Pr\u00E9c\u00E9dant
avanti.lbl.pref347_opt1=La transaction de sortie de stock pour les commandes de pr\u00E9l\u00E8vement (ex\u00E9cution) se produira lors de l'exp\u00E9dition (par d\u00E9faut).
avanti.lbl.pref347_opt2=La transaction de sortie de stock pour les commandes de pr\u00E9l\u00E8vement (ex\u00E9cution) se produira lors du pr\u00E9l\u00E8vement lorsque le pr\u00E9l\u00E8vement est accept\u00E9. Tous les autres types de commande continueront d'effectuer la transaction de sortie d'inventaire lors de l'exp\u00E9dition.
avanti.lbl.press=Presse
avanti.lbl.pressMode=Appuyez sur Mode
avanti.lbl.pressPool=Pool de presse
avanti.lbl.pressRunSpeed=Presse vitesse d'ex\u00E9cution
avanti.lbl.pressRunSpoilage=Pressage g\u00E2cher
avanti.lbl.pressSetup=Appuyez sur Setup
avanti.lbl.pressSheetSize=Remplacer la feuille de presse(WxL)
avanti.lbl.pressSheetSize_196=Remplacer la feuille de presse (HxW)
avanti.lbl.pressSheetSize_in=Remplacer la feuille de presse (WxL in)
avanti.lbl.pressSheetSize_in_196=Remplacer la feuille de presse (HxW in)
avanti.lbl.pressSheetSize_mm=Remplacer la feuille de presse (WxL mm)
avanti.lbl.pressSheetSize_mm_196=Remplacer la feuille de presse (HxW mm)
avanti.lbl.pressSheetSize_warning=* Sp\u00E9cifiez le format de la feuille de presse lorsque vous remplacez \#up et \#out
avanti.lbl.pressSheets=Feuilles de presse
avanti.lbl.pressSheetsFeet=Feuilles de presse (feet)
avanti.lbl.pressSheetsFeet_metric=Feuilles de presse (m\u00E8tres)
avanti.lbl.pressSheetsSpoils=Feuilles de presse avec...
avanti.lbl.pressSheetsWithSpoilsFeet=Feuilles de presse avec butin (feet)
avanti.lbl.pressSheetsWithSpoilsFeet_metric=Feuilles de presse avec butin (m\u00E8tres)
avanti.lbl.price=Prix
avanti.lbl.priceCostFullPartialSheets=Calculer le co\u00FBt et le prix des feuilles de presse GF compl\u00E8tes et partielles
avanti.lbl.priceLockedByChangeOrder=*Les prix sont bloqu\u00E9s en raison d'un ordre de modification non facturable
avanti.lbl.priceLockedOnLineGang=* Les prix sont bloqu\u00E9s sur la ligne Gang. Modifier les prix sur les lignes sources si n\u00E9cessaire.
avanti.lbl.pricePer=Prix \u200B\u200Bpar
avanti.lbl.pricingFactor=Par quantit\u00E9 de
avanti.lbl.pricingOption=Options de tarification
avanti.lbl.pricingUnit=Unit\u00E9s de tarification
avanti.lbl.printListofEmployees=Imprimer la liste des employ\u00E9s
avanti.lbl.printSalesOrderNumber=Imprimer le num\u00E9ro de commande client
avanti.lbl.priority=Priorit\u00E9
avanti.lbl.proceed=Proc\u00E9der
avanti.lbl.processUnconfirmedTimeAndMaterial=Traiter les saisies de temps et de mat\u00E9riel non confirm\u00E9es.
avanti.lbl.processingCategory=Cat\u00E9gorie de Traitement
avanti.lbl.productionBillingCode=Code de facturation de la production
avanti.lbl.progress=Le progr\u00E8s
avanti.lbl.projectPlan=Plan de projet
avanti.lbl.projectPlanSelection=S\u00E9lection du plan de projet
avanti.lbl.projectPlanType=Type de plan de projet
avanti.lbl.projectPlanType_interbranch=Interbranch
avanti.lbl.projectPlanType_standard=Standard
avanti.lbl.projectPlanUnappliedInvoices=Factures de plan de projet / notes de cr\u00E9dit non appliqu\u00E9es \u00E0 la facturation anticip\u00E9e
avanti.lbl.projectPlanWorkFlow_CancelRelease=Annuler la lib\u00E9ration
avanti.lbl.projectPlanWorkFlow_Close=Fermer
avanti.lbl.projectPlanWorkFlow_Release=communiqu\u00E9s
avanti.lbl.projectPlanWorkFlow_Reopen=Rouvrir
avanti.lbl.proof=\u00C9preuve
avanti.lbl.prorateLaborTime=Temps proportionnel pour
avanti.lbl.pullLap=Tirer le tour
avanti.lbl.purchaseGL=Acheter GL
avanti.lbl.purchaseHistory=Historique d'achat
avanti.lbl.purchaseIntegrationAction_Cancel=Annuler
avanti.lbl.purchaseIntegrationAction_Close=Fermer
avanti.lbl.purchaseIntegrationAction_Create=Cr\u00E9er
avanti.lbl.purchaseIntegrationAction_ReOpen=Rouvrir
avanti.lbl.purchaseIntegrationAction_Release=Sortie
avanti.lbl.purchaseIntegrationAction_Update=r\u00E9viser
avanti.lbl.purchaseIntegrationAction_Void=Vide
avanti.lbl.purchaseOrder=Bon de commande
avanti.lbl.purchaseOrderDate=Date P / O
avanti.lbl.purchaseOrderNumber=Num\u00E9ro de P / O
avanti.lbl.purchaseOrderReceiptStatus_ReadyToPost=Pr\u00EAt \u00E0 publier
avanti.lbl.purchaseOrdersOnRegister=Bons de commande sur registre
avanti.lbl.purchaseTaxGroup=Groupe de taxe d'achat
avanti.lbl.purchases=Achats
avanti.lbl.pushTaskValues=Transf\u00E9rer les valeurs de t\u00E2ches vers d'autres Qt\u00E9
avanti.lbl.putJobOnHold=Mettre le travail en attente
avanti.lbl.qty=Quantit\u00E9
avanti.lbl.qtyBackordered=Remis en commande
avanti.lbl.qtyBreak=Qt\u00E9 Pause
avanti.lbl.qtyCommitted=Engag\u00E9
avanti.lbl.qtyCost=Qt\u00E9 Co\u00FBt
avanti.lbl.qtyCredited=Qt\u00E9 cr\u00E9dit\u00E9e
avanti.lbl.qtyExchanged=Qt\u00E9 \u00E9chang\u00E9e
avanti.lbl.qtyNewOrder=Nouvelle qt\u00E9 de commande
avanti.lbl.qtyOrdered=Quantit\u00E9 command\u00E9e
avanti.lbl.qtyPerCore=Qt\u00E9 par noyau
avanti.lbl.qtyReceivedShort=Quantit\u00E9 re\u00E7ue
avanti.lbl.qtyReserved=Qt\u00E9 r\u00E9serv\u00E9e
avanti.lbl.qtyRestock=Qt\u00E9 de r\u00E9approvisionnement
avanti.lbl.qtyReturned=Qt\u00E9 retourn\u00E9e
avanti.lbl.qtyScrapped=Qt\u00E9 mis au rebut
avanti.lbl.qtyUsed=Quantit\u00E9 utilis\u00E9e
avanti.lbl.quantity=Quantit\u00E9
avanti.lbl.quantityDetails=D\u00E9tails de la quantit\u00E9
avanti.lbl.quantityReserved=Quantit\u00E9 r\u00E9serv\u00E9e
avanti.lbl.quickEntry_items=Entr\u00E9e rapide - Articles
avanti.lbl.quote=Citation
avanti.lbl.quote\#=Citation \#
avanti.lbl.quotedPrice=Prix \u200B\u200Bindiqu\u00E9
avanti.lbl.rate=Taux
avanti.lbl.ready=Pr\u00EAt
avanti.lbl.readyToPost=Pr\u00EAt \u00E0 publier
avanti.lbl.rebuildFIFO=Reconstruire FIFO
avanti.lbl.recalculate=Recalculer
avanti.lbl.recalculating=Re-calculer
avanti.lbl.recalculatingInvoices=Calculer les factures
avanti.lbl.receiptCount=Nombre de re\u00E7us
avanti.lbl.receiptDate=Date de r\u00E9ception
avanti.lbl.receiptDateRange=Plage de dates de r\u00E9ception
avanti.lbl.receivedToDate=Re\u00E7u \u00E0 ce jour
avanti.lbl.recordsProcessed=Dossiers trait\u00E9s
avanti.lbl.recordsSelected=Enregistrements s\u00E9lectionn\u00E9s
avanti.lbl.refreshInkColors=Actualiser les couleurs d'encre
avanti.lbl.refundAmount=Montant du remboursement
avanti.lbl.refundComments=Commentaires sur le remboursement
avanti.lbl.refundDate=date de remboursement
avanti.lbl.refundStatus=Statut de remboursement
avanti.lbl.refundType=Type de remboursement
avanti.lbl.registerPasswordFailToPassStatus=Mot de passe - \u00C9chec de la r\u00E9ussite du statut
avanti.lbl.registerProcess_Clear=D\u00E9gager
avanti.lbl.registerProcess_Create=Cr\u00E9er
avanti.lbl.registerProcess_CreateRegister=Cr\u00E9er un registre
avanti.lbl.registerProcess_Edit=\u00C9diter
avanti.lbl.registerProcess_Post=Poste
avanti.lbl.registerProcess_applyChanges=Appliquer les modifications
avanti.lbl.registerProcess_review=Examen
avanti.lbl.registerProcess_reviewRegister=R\u00E9viser le registre
avanti.lbl.registerProcessingFilters=Enregistrer les filtres de traitement
avanti.lbl.registerProcessingOptions=Enregistrer les options de traitement
avanti.lbl.registerType_AccountsPayableInvoiceRegister=Registre des factures des comptes cr\u00E9diteurs
avanti.lbl.registerType_CashReceiptsRegister=Registre des encaissements
avanti.lbl.registerType_JobCostAdjustmentRegister=Registre d'ajustement des co\u00FBts de travail
avanti.lbl.regularPrice=Prix \u200B\u200Bhabituel
avanti.lbl.releaseInterbranchProjectPlanValidationErrors=Erreurs de validation du plan de projet
avanti.lbl.removeInvoiceLock=Supprimer le verrouillage de la facture
avanti.lbl.reportFilters=Filtres de rapport
avanti.lbl.reportTitle=Titre du rapport
avanti.lbl.reprintSummaryDesc=R\u00E9impression Description
avanti.lbl.required=Champs obligatoires
avanti.lbl.requiredFieldsWarning=Il manque des champs obligatoires, souhaitez-vous corriger les adresses ou quitter.
avanti.lbl.reset=R\u00E9initialiser
avanti.lbl.resource=Ressource
avanti.lbl.resourceFilter=Filtre de ressources
avanti.lbl.responseErrorMessage=Message d'erreur de r\u00E9ponse
avanti.lbl.returnDate=Date de retour
avanti.lbl.returnSOCreated=Commande client de retour cr\u00E9\u00E9e
avanti.lbl.returnToMenu=Retour au menu
avanti.lbl.returnUnappliedCN=Retourner la note de cr\u00E9dit non appliqu\u00E9e
avanti.lbl.revenue=Revenu
avanti.lbl.reviewed=R\u00E9vis\u00E9
avanti.lbl.rightJustifyColumns.flag=Colonnes de devise justifi\u00E9es \u00E0 droite
avanti.lbl.roll=Rouleau
avanti.lbl.rollChangeMaterial=Mat\u00E9riau de changement de rouleau (pieds)
avanti.lbl.rollChangeMaterial_m=Mat\u00E9riau de changement de rouleau (M\u00E8tres)
avanti.lbl.rollChangeTime=Temps de changement de rouleau (min)
avanti.lbl.rollLength=Longueur (ft)
avanti.lbl.rollLengthMetric=Longueur (m)
avanti.lbl.rollOrientation=Orientation du rouleau
avanti.lbl.rollOutput=Sortie de rouleau
avanti.lbl.rollSetupMaterial=Mat\u00E9riau d\u2019installation du rouleau (pieds)
avanti.lbl.rollSetupMaterial_m=Mat\u00E9riau d\u2019installation du rouleau (M\u00E8tres)
avanti.lbl.rollSetupTime=Temps de configuration du rouleau (min)
avanti.lbl.rollWeigthUsed=Poids total du rouleau utilis\u00E9 (lb)
avanti.lbl.rollWeigthUsedMetric=Poids total du rouleau utilis\u00E9 (kg)
avanti.lbl.rollWidth=Largeur du rouleau (in)
avanti.lbl.rollplacard=Roll Placard
avanti.lbl.rolls/Skid=Rouleaux par palette
avanti.lbl.rpt.ShippingCAPS=TRANSPORT
avanti.lbl.rpt_tracking_number=Suivi \#
avanti.lbl.run=Courir
avanti.lbl.runFeet=D\u00E9chets de presse (feet)
avanti.lbl.runFeet_metric=D\u00E9chets de presse (m\u00E8tres)
avanti.lbl.runReport=Ex\u00E9cuter le rapport
avanti.lbl.runSheetAcross=Courir \u00E0 travers
avanti.lbl.runSpeed=Vitesse d'ex\u00E9cution
avanti.lbl.runSpoils=D\u00E9chets de presse
avanti.lbl.runSpoils%=D\u00E9chets de presse -%
avanti.lbl.runTime/Cost=Temps d'ex\u00E9cution
avanti.lbl.runType=Type de parcours
avanti.lbl.runningAs=Courir en tant que
avanti.lbl.rushCode=Code urgent
avanti.lbl.rush_code=Code urgent
avanti.lbl.salesOrder=Commande client
avanti.lbl.salesOrderExport_httpPostTab=D\u00E9tails de la publication HTTP
avanti.lbl.salesOrderWorkFlow_CompleteOrder=Compl\u00E9tez la commande
avanti.lbl.salesOrderWorkFlow_MoveToProjectPlan=Passer au plan de projet
avanti.lbl.salesTaxAccount=Compte de taxe de vente
avanti.lbl.salesTaxGroup=Groupe de taxe de vente
avanti.lbl.save=Sauver
avanti.lbl.scheduledDate=Date pr\u00E9vue
avanti.lbl.score=Marquer
avanti.lbl.search=Chercher
avanti.lbl.section=Section
avanti.lbl.sectionDescription=Description de la section
avanti.lbl.sectionDetails=D\u00E9tails de la section
avanti.lbl.sectionQuantity=Quantit\u00E9 de section
avanti.lbl.sections=Sections
avanti.lbl.select=S\u00E9lectionner
avanti.lbl.selectCustomers=S\u00E9lectionnez le(s) client(s)
avanti.lbl.selectItem=S\u00E9lectionner un article
avanti.lbl.selected=Choisi
avanti.lbl.selectedPress=Presse s\u00E9lectionn\u00E9e
avanti.lbl.selectedforEnvironmentalReporting=S\u00E9lectionnez pour afficher sur le rapport environnemental.
avanti.lbl.sellingUnitPrice=Prix \u200B\u200Bunitaire de vente
avanti.lbl.sendMsgToLoggedInUsers=Envoyer un message \u00E0 tous les utilisateurs connect\u00E9s
avanti.lbl.sendProof=Envoyer une preuve
avanti.lbl.sendProofAutoAttachments=Envoyer une preuve
avanti.lbl.sender_dateFormatToolTip=y est l'ann\u00E9e, M le mois, d le jour, HH l'heure, m les minutes, T ou un espace vide peuvent \u00EAtre utilis\u00E9s pour s\u00E9parer les parties de date et d'heure. Il n'est pas n\u00E9cessaire de sp\u00E9cifier les secondes, les millisecondes ou le d\u00E9calage horaire, m\u00EAme si ces \u00E9l\u00E9ments figurent dans votre cha\u00EEne de date. Par exemple \: \u00AB yyyy-MM-ddTHH\:mm \u00BB.
avanti.lbl.separateJournalEntriesInPOReg=Entr\u00E9es JE s\u00E9par\u00E9es sur le registre des re\u00E7us d'achat
avanti.lbl.separateMaterialPricingForDetailLines=Prix des mat\u00E9riaux distincts / Achat sur les lignes de commande
avanti.lbl.september=Septembre
avanti.lbl.setEstimateCost=D\u00E9finir le co\u00FBt estim\u00E9
avanti.lbl.setup=Installer
avanti.lbl.setupFeetRollMode=Pieds de configuration (Mode Rouleau)
avanti.lbl.setupFeetRollMode_m=Compteurs de configuration (Mode Rouleau)
avanti.lbl.setupMin=Configuration (min.)
avanti.lbl.setupSheets=Feuilles d'installation
avanti.lbl.setupSpoils=D\u00E9chets de pr\u00E9paration
avanti.lbl.setupTime=Temps d'install. (min)
avanti.lbl.sfClicks=Nombre de clics
avanti.lbl.sheetSize=Taille de la feuille
avanti.lbl.sheetUtilization=% d\u2019utilisation des feuilles
avanti.lbl.sheetUtilization2=Utilisation de la feuille
avanti.lbl.sheets=Feuilles
avanti.lbl.sheetsAcrossBed=\u00C0 travers le lit
avanti.lbl.sheetsAcrossRoll=\# de feuilles sur le rouleau
avanti.lbl.sheetsPerBed=Feuilles par lit
avanti.lbl.sheetwise=Sheetwise
avanti.lbl.shiftTimes=Horaires des quarts de travail
avanti.lbl.shipPickslipStatus=\u00C9tat du bordereau d'exp\u00E9dition
avanti.lbl.shipToAddressContact2=Adresse de livraison
avanti.lbl.shipToSellingPlant=Usine de vente \u00E0 la livraison
avanti.lbl.shipments=exp\u00E9ditions
avanti.lbl.shippingMethod_FedEx_DeliverySignatureOptions=Options de signature de livraison
avanti.lbl.shopFloorAddMaterialQuestion=\u00CAtes-vous s\u00FBr de vouloir ajouter le mat\u00E9riel \u00E0 la liste?
avanti.lbl.shopFloorAddOtherMaterial=Ajouter un autre mat\u00E9riau
avanti.lbl.shopFloorAssociateToCostEntry=Associer \u00E0 la saisie du temps
avanti.lbl.shopFloorAssociateToSection=Associer \u00E0 la section
avanti.lbl.shopFloorAssociationCostEntryError=Aucune entr\u00E9e de co\u00FBt s\u00E9lectionn\u00E9e
avanti.lbl.shopFloorAttachMessageToJob=Joindre un message au travail
avanti.lbl.shopFloorAttachMessageToJobAndSection=Joindre un message \u00E0 la t\u00E2che et \u00E0 la section
avanti.lbl.shopFloorClicks=Clics d'atelier
avanti.lbl.shopFloorCostEntries=Entr\u00E9es de co\u00FBt
avanti.lbl.shopFloorCostEntry=Entr\u00E9e de co\u00FBt
avanti.lbl.shopFloorCostEntryError=Aucune entr\u00E9e de co\u00FBt.
avanti.lbl.shopFloorEndShift=Fin du poste
avanti.lbl.shopFloorEndShiftQuestion=\u00CAtes-vous s\u00FBr de vouloir mettre fin \u00E0 votre quart de travail?
avanti.lbl.shopFloorExitMsg=Vous avez une ou plusieurs op\u00E9rations en cours, qui resteront en cours lorsque  vous quitterez Shop Floor.
avanti.lbl.shopFloorExtraCode=Shop Floor Code suppl\u00E9mentaire
avanti.lbl.shopFloorExtraCodeDetailView=Extra Code - Vue d\u00E9taill\u00E9e
avanti.lbl.shopFloorExtraCodeTableView=Code Extra - Vue tableau
avanti.lbl.shopFloorExtraMaterialDeleteCommitted=Vous ne pouvez pas supprimer un mat\u00E9riau engag\u00E9.
avanti.lbl.shopFloorHideMaterials=Masquer les mat\u00E9riaux
avanti.lbl.shopFloorHistoricalCostEntries=Entr\u00E9es de co\u00FBts historiques
avanti.lbl.shopFloorInProgress=En cours
avanti.lbl.shopFloorJobDetailsButton=<body><div align\="center"><table><tr><td><img src\="media\:///Job_Details_32x32.png"></td><td style\="vertical-align\:middle">D\u00E9tails du travail</td></tr></table></div></body>
avanti.lbl.shopFloorJobTicketButton=<body><div align\="center"><table><tr><td><img src\="media\:///Job_Ticket_32x32.png"></td><td style\="vertical-align\:middle">Dossier</td></tr></table></div></body>
avanti.lbl.shopFloorLastQuantityUsed=Derni\u00E8re quantit\u00E9 utilis\u00E9e
avanti.lbl.shopFloorLoadExpectedOperation=Charger les op\u00E9ration en attentes
avanti.lbl.shopFloorLoadExpectedOperation_tip=Op\u00E9ration de chargement bas\u00E9e sur la date de planification des \u00E9tapes.
avanti.lbl.shopFloorLoadOperation=Op\u00E9ration de chargement
avanti.lbl.shopFloorLoginAssociationError=Votre utilisateur n'est pas associ\u00E9 \u00E0 Shop Floor.
avanti.lbl.shopFloorLoginEmployeeCodeError=Votre code d'employ\u00E9 est introuvable. Veuillez contacter un administrateur.
avanti.lbl.shopFloorMaterialEntry=Entr\u00E9e de mat\u00E9riel
avanti.lbl.shopFloorMaterialEntryButton=<body><div align\="center"><table><tr><td><img src\="media\:///Database_32x32.png"></td><td style\="vertical-align\:middle">Entr\u00E9e de mat\u00E9riel</td></tr></table></div></body>
avanti.lbl.shopFloorMaterialUsed=Mat\u00E9riaux utilis\u00E9s
avanti.lbl.shopFloorMessagesButton=<body><div align\="center"><table><tr><td><img src\="media\:///Message_32x32.png"></td><td style\="vertical-align\:middle">Messages</td></tr></table></div></body>
avanti.lbl.shopFloorMileStoneCompleteQuestion=Souhaitez-vous effectuer toutes les op\u00E9rations incompl\u00E8tes pour cette \u00C9tape et d\u00E9finir le temps n\u00E9cessaire pour chaque op\u00E9ration \u00E0 partir du temps \u00E9coul\u00E9 de votre entr\u00E9e par rapport au temps budg\u00E9tis\u00E9 des \u00E9tapes?
avanti.lbl.shopFloorMilestoneError=Il n'y a pas d'\u00E9tape connexe \u00E0 compl\u00E9ter.
avanti.lbl.shopFloorMilestoneSelectionError=Veuillez s\u00E9lectionner une op\u00E9ration.
avanti.lbl.shopFloorMilestoneSelectionErrorMsg=Vous avez s\u00E9lectionn\u00E9 un jalon. Veuillez s\u00E9lectionner une op\u00E9ration \u00E0 la place.
avanti.lbl.shopFloorNoEmployeeSelected=S\u00E9lectionnez un employ\u00E9 auquel envoyer le message direct ou joignez-le \u00E0 un dossier ou \u00E0 une section.
avanti.lbl.shopFloorNoExpectedOperation=Aucune op\u00E9ration n'est pr\u00EAte pour cette section
avanti.lbl.shopFloorNoJobTicket=Un dossier de travail n'a pas \u00E9t\u00E9 trouv\u00E9 pour ce travail
avanti.lbl.shopFloorNoShiftStarted=Aucun quart de travail n'a \u00E9t\u00E9 commenc\u00E9.
avanti.lbl.shopFloorOperationAssociationError=Aucune op\u00E9ration s\u00E9lectionn\u00E9e
avanti.lbl.shopFloorOperationAssociationError_msg=Aucune op\u00E9ration n'a \u00E9t\u00E9 s\u00E9lectionn\u00E9e dans l'\u00E9cran de saisie des co\u00FBts. S\u00E9lectionnez une op\u00E9ration ou d\u00E9cochez la case "Associer \u00E0 la saisie des co\u00FBts".
avanti.lbl.shopFloorOperationComplete=Op\u00E9ration termin\u00E9e
avanti.lbl.shopFloorOperationsInProgress=Op\u00E9rations en cours
avanti.lbl.shopFloorPauseJob=Vous avez suspendu l'op\u00E9ration <param1> dans la section <param2> du travail <param3>
avanti.lbl.shopFloorPleaseStartOperation=S'il vous pla\u00EEt commencer l'op\u00E9ration
avanti.lbl.shopFloorPostMaterialsButton=Mat\u00E9riel enregistr\u00E9
avanti.lbl.shopFloorProductionWorkflow=Flux de production
avanti.lbl.shopFloorQuantityAvailable=Quantit\u00E9 disponible
avanti.lbl.shopFloorQuantityExpected=Quantit\u00E9 attendue
avanti.lbl.shopFloorQuantityProduced=Quantit\u00E9 produite
avanti.lbl.shopFloorQuantityUsed=Quantit\u00E9 actuelle utilis\u00E9e
avanti.lbl.shopFloorQuantityUsedDate=Date de la quantit\u00E9 utilis\u00E9e
avanti.lbl.shopFloorQuantityUsedToDate=Quantit\u00E9 utilis\u00E9e \u00E0 ce jour
avanti.lbl.shopFloorRemoveUnpostedButton=Supprimer les documents non enregistr\u00E9s
avanti.lbl.shopFloorResourceComplete=Ressource compl\u00E8te
avanti.lbl.shopFloorResourceCompleteQuestion=Souhaitez-vous effectuer toutes les op\u00E9rations incompl\u00E8tes pour cette cat\u00E9gorie d'op\u00E9rations et d\u00E9finir le temps n\u00E9cessaire pour chaque op\u00E9ration \u00E0 partir du temps \u00E9coul\u00E9 de votre entr\u00E9e par rapport au temps budg\u00E9tis\u00E9 des \u00E9tapes?
avanti.lbl.shopFloorResourceError=Aucune ressource connexe \u00E0 compl\u00E9ter.
avanti.lbl.shopFloorResumeQuestion=Cette op\u00E9ration a \u00E9t\u00E9 mis en pause, souhaitez-vous la reprendre?
avanti.lbl.shopFloorResumeQuestion2=\u00E9tait en pause, souhaitez-vous reprendre l'op\u00E9ration?
avanti.lbl.shopFloorReturnToMenu=\u00CAtes-vous s\u00FBr de vouloir quitter Shop Floor?
avanti.lbl.shopFloorRushCode=Shop Floor Code urgent
avanti.lbl.shopFloorRushCodeDetailView=Code urgent - Vue d\u00E9taill\u00E9e
avanti.lbl.shopFloorRushCodeTableView=Code urgent - Vue tableau
avanti.lbl.shopFloorSectionComplete=Section compl\u00E8te
avanti.lbl.shopFloorSectionCompleteQuestion=Souhaitez-vous effectuer toutes les op\u00E9rations de cette section et r\u00E9duire la dur\u00E9e de chaque op\u00E9ration en fonction de la dur\u00E9e \u00E9coul\u00E9e du d\u00E9but \u00E0 la fin et de la dur\u00E9e estim\u00E9e sp\u00E9cifi\u00E9e dans les \u00E9tapes?
avanti.lbl.shopFloorSectionDocuments=Section des documents
avanti.lbl.shopFloorSectionDocumentsButton=<body><div align\="center"><table><tr><td><img src\="media\:///Paperclip_32x32.png"></td><td style\="vertical-align\:middle">Section des documents</td></tr></table></div></body>
avanti.lbl.shopFloorSectionMilestones=Section des \u00E9tapes
avanti.lbl.shopFloorSectionMilestonesButton=<body><div align\="center"><table><tr><td><img src\="media\:///Milestone_32x32.png"></td><td style\="vertical-align\:middle">Section des \u00E9tapes</td></tr></table></div></body>
avanti.lbl.shopFloorSelectionError=Veuillez s\u00E9lectionner un travail, une section et une op\u00E9ration sur lesquels travailler.
avanti.lbl.shopFloorSendToEmployee=Envoyer \u00E0 l'employ\u00E9
avanti.lbl.shopFloorShiftEnded=Votre quart de travail est termin\u00E9.
avanti.lbl.shopFloorShiftEnded_info=Votre quart de travail s'est termin\u00E9 \u00E0\:
avanti.lbl.shopFloorShiftInProgress=Vous avez d\u00E9j\u00E0 commenc\u00E9 votre quart de travail.
avanti.lbl.shopFloorShiftStarted=Vous avez commenc\u00E9 votre quart de travail.
avanti.lbl.shopFloorShiftStarted_info=Vous avez commenc\u00E9 votre quart de travail \u00E0\:
avanti.lbl.shopFloorShowAllMaterialsForSection=Afficher tous les mat\u00E9riaux pour la section
avanti.lbl.shopFloorShowMaterials=Afficher les mat\u00E9riaux
avanti.lbl.shopFloorStartError=Vous devez commencer l'op\u00E9ration en premier.
avanti.lbl.shopFloorStartOperation=Commencer l'op\u00E9ration
avanti.lbl.shopFloorStartOperationAndExit=Commencer l'op\u00E9ration et sortir
avanti.lbl.shopFloorStartShift=Commencer le quart
avanti.lbl.shopFloorStartShiftAfterLogin=Voulez-vous commencer votre quart de travail?
avanti.lbl.shopFloorTimeEntry=Entr\u00E9e de temps
avanti.lbl.shopFloorTimeEntryButton=<body><div align\="center"><table><tr><td><img \r\nsrc\="media\:///Clock_32x32.png"></td><td style\="vertical-align\:middle">Entr\u00E9e de temps</td></tr></table></div></body>
avanti.lbl.shopFloorUnfinishedOperations=Op\u00E9rations inachev\u00E9es
avanti.lbl.shopFloorUnfinishedOperationsMsg=Vous avez des op\u00E9rations en cours. Elles seront automatiquement compl\u00E9t\u00E9es.
avanti.lbl.shopFloorView=Vue de Shop floor
avanti.lbl.shopSchedulingButton=<body><div align\="center"><table><tr><td><img src\="media\:///Scheduling_32x32.png"></td><td style\="vertical-align\:middle">Mes \r\ndossiers</td></tr></table></div></body>
avanti.lbl.shopfloorAddJobsTT=Ajoutez des travaux / sections \u00E0 cette op\u00E9ration. Le co\u00FBt en temps et en mat\u00E9riel de l\u2019op\u00E9ration sera r\u00E9parti \u00E9galement entre les travaux s\u00E9lectionn\u00E9s.
avanti.lbl.shopfloorAddJobsTT2=Ajoutez des travaux / sections \u00E0 cette op\u00E9ration. Le co\u00FBt en temps et en mat\u00E9riel de l'op\u00E9ration sera r\u00E9parti proportionnellement, en fonction du temps budg\u00E9tis\u00E9, pour cette op\u00E9ration parmi les travaux s\u00E9lectionn\u00E9s.
avanti.lbl.shopfloorAddJobsTT3=Ajoutez des travaux / sections \u00E0 cette op\u00E9ration. Le co\u00FBt en temps et en mat\u00E9riel de l'op\u00E9ration sera r\u00E9parti proportionnellement, en fonction du volume (impressions budg\u00E9t\u00E9es), pour cette op\u00E9ration parmi les travaux s\u00E9lectionn\u00E9s.
avanti.lbl.shopfloorAddJobsTT4=Ajoutez des t\u00E2ches / sections \u00E0 cette op\u00E9ration. Le temps et le co\u00FBt du mat\u00E9riel pour l'op\u00E9ration seront r\u00E9partis proportionnellement, en fonction de la quantit\u00E9 command\u00E9e par article, pour cette op\u00E9ration parmi les travaux s\u00E9lectionn\u00E9s.
avanti.lbl.shopfloorQtyProducedDistMethodPrefOption1=Taux au prorata bas\u00E9 sur la quantit\u00E9 de sections (par d\u00E9faut)
avanti.lbl.shopfloorQtyProducedDistMethodPrefOption2=Taux au prorata bas\u00E9 sur la quantit\u00E9 command\u00E9e par article de campagne
avanti.lbl.shortDescription=Courte description
avanti.lbl.showChangeOrderDetails=Afficher les d\u00E9tails de la demande de changement
avanti.lbl.showInReport=Afficher dans le rapport
avanti.lbl.showLog=Afficher le journal
avanti.lbl.showOnInvoice=Afficher sur la facture
avanti.lbl.showRecord=Afficher les donn\u00E9es
avanti.lbl.showRollDetails=Afficher les d\u00E9tails du rouleau
avanti.lbl.showTotals=Afficher les totaux
avanti.lbl.shutdownAllUsers=Arr\u00EAter tous les utilisateurs connect\u00E9s
avanti.lbl.side=C\u00F4t\u00E9
avanti.lbl.size=Taille
avanti.lbl.sizeIn=Taille (in)
avanti.lbl.sizeOffOfSheeter=Taille hors Sheeter
avanti.lbl.skid=Palette
avanti.lbl.slitters=D\u00E9coupeuses
avanti.lbl.smartPostDefaultEndorsmentType=Type d'approbation par d\u00E9faut
avanti.lbl.smartPostDefaultIndicia=Indices par d\u00E9faut
avanti.lbl.smartPostEndorsmentType=Type d'approbation
avanti.lbl.smartPostHubIdLabel=Identifiant du hub
avanti.lbl.smartPostIndicia=Indice
avanti.lbl.soinvoice\#=Facture SO
avanti.lbl.sort=La sorte
avanti.lbl.specifications=Caract\u00E9ristiques
avanti.lbl.speedAdjustment%=R\u00E9glage de la vit (%)
avanti.lbl.speedCalculatedIn=Vitesse calcul\u00E9e en
avanti.lbl.speedInMin_narrow=Vitesse (in/min)
avanti.lbl.speedInMin_narrow_metric=Vitesse (mm/min)
avanti.lbl.speedMMMin=Vitesse (mm/min)
avanti.lbl.speedUnit3_mm=Vitesse (mm/min)
avanti.lbl.spine=Dos
avanti.lbl.spineGlue=Colle pour la colonne vert\u00E9brale
avanti.lbl.spineThickness=\u00C9paisseur de la colonne vert\u00E9brale
avanti.lbl.spineThicknessCalc=Calcul de l'\u00E9paisseur de la colonne vert\u00E9brale
avanti.lbl.spineThicknessRoundingFactor=Facteur d'arrondi du calcul de la colonne vert\u00E9brale
avanti.lbl.spineThicknessStandardAmount=Montant standard ajout\u00E9 \u00E0 l'\u00E9paisseur du dos
avanti.lbl.splitter=Splitter
avanti.lbl.spoils=D\u00E9chets
avanti.lbl.sqft/min=pi\u00B2/min
avanti.lbl.sqm/min=m\u00B2/min
avanti.lbl.squareFootMeterRoundingTable=Table d'arrondi pied carr\u00E9/m\u00E8tre
avanti.lbl.squareMeterRoundingTable=Table d'arrondi au m\u00E8tre carr\u00E9
avanti.lbl.standardPrice=Prix \u200B\u200Bstandard
avanti.lbl.startTime=Heure de d\u00E9but
avanti.lbl.startTimes=Heures de D\u00E9but
avanti.lbl.startedAt=Commenc\u00E9 \u00E0\:
avanti.lbl.stdMarkup=Std Marquage %
avanti.lbl.subTotal=Total partiel
avanti.lbl.substrate=Substrat
avanti.lbl.substrateColor=Couleur du substrat
avanti.lbl.substrateFinish=Finition du substrat
avanti.lbl.substrateLength=Longueur du substrat
avanti.lbl.substrateType=Type de substrat
avanti.lbl.substrateWeight=Poids du substrat
avanti.lbl.supplier=Fournisseur
avanti.lbl.supplierName=Nom du fournisseur
avanti.lbl.supplierSetupCost=Co\u00FBt d'installation du fournisseur
avanti.lbl.supplier_name_2=Nom du fournisseur
avanti.lbl.suppliers=Fournisseurs
avanti.lbl.systemPreference=Pr\u00E9f\u00E9rence syst\u00E8me
avanti.lbl.systemTasks=T\u00E2ches g\u00E9n\u00E9r\u00E9es par le syst\u00E8me
avanti.lbl.task=T\u00E2che
avanti.lbl.taskDetails=D\u00E9tails de la t\u00E2che
avanti.lbl.taskPriceRuleApplied=* La r\u00E8gle du prix de la t\u00E2che a \u00E9t\u00E9 appliqu\u00E9e
avanti.lbl.taskPriceTypeShort_FinishSizeAreaMetric=sqm
avanti.lbl.taskPriceTypeShort_ImageAreaMetric=sqm
avanti.lbl.taskPriceTypeShort_LinearMeters=lm
avanti.lbl.taskPriceTypeShort_LinearMillimeters=lmm
avanti.lbl.taskPriceType_Clicks=Clics
avanti.lbl.taskPriceType_FinishSizeArea=Surface de finition (sq. ft.)
avanti.lbl.taskPriceType_FinishSizeAreaXPages_Metric=Surface de taille de finition (M\u00E8tres carr\u00E9s x \# feuilles / pages)
avanti.lbl.taskPriceType_FinishSizeArea_Metric=Surface de taille de finition (M\u00E8tres carr\u00E9s)
avanti.lbl.taskPriceType_ImageAreaXPages_Metric=Surface de l\u2019image (M\u00E8tres carr\u00E9s x \# feuilles/pages)
avanti.lbl.taskPriceType_ImageArea_Metric=Superficie de l\u2019image (M\u00E8tres carr\u00E9s)
avanti.lbl.taskPriceType_LinearMeters=M\u00E8tres lin\u00E9aires (longueur d\u2019alimentation)
avanti.lbl.taskPriceType_LinearMetersTask=Compteurs lin\u00E9aires (t\u00E2che)
avanti.lbl.taskPriceType_LinearMillimeters=Millim\u00E8tres lin\u00E9aires
avanti.lbl.taskPriceType_PrintedImages=Images imprim\u00E9es (\# feuilles x \# transversalement x \# longitudinalement x \# pages)
avanti.lbl.taskQuantityandCostbyCustomerPO=Rapport sur la quantit\u00E9 et le co\u00FBt des t\u00E2ches par bon de commande client\:
avanti.lbl.taskTotals=Totaux de t\u00E2ches
avanti.lbl.tasks=Les t\u00E2ches
avanti.lbl.tasksSelected=T\u00E2ches s\u00E9lectionn\u00E9es
avanti.lbl.taxAdditionalCharges=Frais suppl\u00E9mentaires fiscaux
avanti.lbl.taxMaterialOnly=Mat\u00E9riel fiscal uniquement
avanti.lbl.taxMaterialOnly_tooltip=Seuls les mat\u00E9riaux seront tax\u00E9 - le travail ne sera pas impos\u00E9
avanti.lbl.theFollowingPlantOrdersWereCreated=Les ordres d'usine suivants ont \u00E9t\u00E9 cr\u00E9\u00E9s.
avanti.lbl.theFollowingPlantOrdersWereDeleted=Les commandes d'usine suivantes ont \u00E9t\u00E9 supprim\u00E9es.
avanti.lbl.thickness=\u00C9paisseur
avanti.lbl.time=Temps
avanti.lbl.timeFormat=Toutes les heures en(HR\:Min)
avanti.lbl.timeNeeded=Temps n\u00E9cessaire
avanti.lbl.timeUsed=Temps Utilis\u00E9
avanti.lbl.title=Titre
avanti.lbl.to.shipped.date=\u00C0 la date d'exp\u00E9dition
avanti.lbl.toDate=\u00C0 ce jour
avanti.lbl.toJobStartDate=Date de d\u00E9but du travail
avanti.lbl.toTransactionDate=\u00C0 la date de transaction
avanti.lbl.tooBigForPress=Trop gros pour la presse
avanti.lbl.tooltip.itemDimensions=Dimensions en pouces
avanti.lbl.tooltip.itemDimensions_mm=Dimensions en mm
avanti.lbl.tooltip.recalculateAllSections=Recalculer toutes les sections
avanti.lbl.tooltip.recalculateSelectedSection=Recalculer la section s\u00E9lectionn\u00E9e
avanti.lbl.tooltip.rightclick_showBackorderDetails=Clic droit pour afficher les d\u00E9tails des commandes en attente
avanti.lbl.tooltip.rightclick_showCommitmentDetails=Clic droit pour afficher les d\u00E9tails de l'engagement
avanti.lbl.tooltip.rightclick_showOnPODetails=Cliquez avec le bouton droit pour afficher les d\u00E9tails des commandes d'achat, des r\u00E9ceptions en attente et des ordres de fabrication en attente
avanti.lbl.tooltip.rightclick_showReservedDetails=Clic droit pour afficher les d\u00E9tails r\u00E9serv\u00E9s
avanti.lbl.total%=(Total %)
avanti.lbl.totalCost=Co\u00FBt total
avanti.lbl.totalCosts=Co\u00FBt total
avanti.lbl.totalElapsedTimeMinute=Temps total \u00E9coul\u00E9 (min.)
avanti.lbl.totalInksRequired=Total d'encres requis
avanti.lbl.totalInvCreated=Total des factures cr\u00E9\u00E9es
avanti.lbl.totalOutsourcedCost=Total des co\u00FBts externalis\u00E9s
avanti.lbl.totalPrice=Prix total
avanti.lbl.totalQty=Qt\u00E9 Totale
avanti.lbl.totalQtyAtPlantsExceeds=Le total des quantit\u00E9s d\u00E9finies pour toutes les usines doit \u00EAtre exactement \u00E9gal \u00E0 la quantit\u00E9 command\u00E9e.
avanti.lbl.totalQtyAtPlantsExceedsSection=Le total des quantit\u00E9s d\u00E9finies pour toutes les usines doit \u00EAtre exactement \u00E9gal \u00E0 la quantit\u00E9 de section s\u00E9lectionn\u00E9e.
avanti.lbl.totalQtyAtPlantsExceedsTask=Le total des quantit\u00E9s d\u00E9finies pour toutes les usines doit \u00EAtre exactement \u00E9gal \u00E0 la quantit\u00E9 de t\u00E2che s\u00E9lectionn\u00E9e.
avanti.lbl.totalRevExceeds=Le% du revenu total doit \u00EAtre de 100%
avanti.lbl.totalSpoils=Total du butin
avanti.lbl.totalSpoilsFeet=Total du butin (feet)
avanti.lbl.totalSpoilsFeet_metric=Total du butin (m\u00E8tres)
avanti.lbl.totalUnApplied=Total non appliqu\u00E9
avanti.lbl.total_goods_value=Valeur totale des marchandises
avanti.lbl.total_invoice_value=Valeur totale de la facture
avanti.lbl.total_with_duties_taxes=Total avec droits et taxes
avanti.lbl.transaction=Transaction
avanti.lbl.transactionUsageDate=Date de transaction/d'utilisation
avanti.lbl.transferOrder=Ordre de transfert
avanti.lbl.transferOrdersSelected=Ordres de transfert s\u00E9lectionn\u00E9s
avanti.lbl.trimmerKnives=Couteaux Trimmer
avanti.lbl.trims=Garnitures
avanti.lbl.turningBar=Barre de retournement
avanti.lbl.type=Type
avanti.lbl.type*=*Type
avanti.lbl.unappliedFifo=Fifo non appliqu\u00E9
avanti.lbl.unavailable=Indisponible
avanti.lbl.unfinished=Inachev\u00E9
avanti.lbl.unitImperial_in=(in)
avanti.lbl.unitMetricSpeedPerHour=Compteurs par heure
avanti.lbl.unitMetric_mm=(mm)
avanti.lbl.unitOfMeasurement=Unit\u00E9 de mesure
avanti.lbl.unmarkAllMilestonesDone=Annuler le marquage de tous les jalons termin\u00E9s
avanti.lbl.unusable=Inutilisable
avanti.lbl.uom_code=UOM
avanti.lbl.updateRefund=Mettre \u00E0 jour le remboursement
avanti.lbl.updatedByProgram=Mis \u00E0 jour par programme
avanti.lbl.ups_usps_endorsement_asr=Service d'adresse demand\u00E9
avanti.lbl.ups_usps_endorsement_csr=Service de modification demand\u00E9
avanti.lbl.ups_usps_endorsement_fsr=Service de transfert demand\u00E9
avanti.lbl.ups_usps_endorsement_lnr=Aucun service s\u00E9lectionn\u00E9
avanti.lbl.ups_usps_endorsement_rsr=Service de retour demand\u00E9
avanti.lbl.useCylinderForPressCutoff=Permettez que ce cylindre soit utilis\u00E9 pour calculer la coupure de presse.
avanti.lbl.useDieForPressCutoff=Permettez que cette matrice soit utilis\u00E9e pour calculer la coupure de la presse.
avanti.lbl.useFileForInvoiceExport=Utiliser le fichier pour l'exportation des factures
avanti.lbl.useForModelling=Utiliser pour la mod\u00E9lisation
avanti.lbl.useInvoiceBillingCodeSummaryForGPIntegration=Utiliser le r\u00E9sum\u00E9 du code de facturation de la facture lors de la cr\u00E9ation de la facture G/P
avanti.lbl.useItemCodeAsSectionDescription=Utiliser le code d'\u00E9l\u00E9ment de t\u00E2che mat\u00E9rielle comme description de section
avanti.lbl.useNumericalValuesOnly=Utilisez uniquement des valeurs num\u00E9riques
avanti.lbl.useOnePlatePerColor=Utilisez une assiette par couleur / c\u00F4t\u00E9
avanti.lbl.useProductPreviousSection=Utiliser le produit de la section pr\u00E9c\u00E9dente
avanti.lbl.useSupplierForQty=Utiliser le fournisseur pour la quantit\u00E9
avanti.lbl.used=Utilis\u00E9
avanti.lbl.useinSalesRevenueReport=Rapport sur les revenus des ventes\: 
avanti.lbl.useinVariousFeesReport=Rapport sur les divers frais \:
avanti.lbl.user=Utilisateur
avanti.lbl.userName=Nom d'utilisateur
avanti.lbl.usps3_crid=ID d'Enregistrement Client
avanti.lbl.usps3_mid=Identifiant de l'Exp\u00E9diteur
avanti.lbl.usps3_permitZIPCode=Permettre le Code ZIP
avanti.lbl.utilitiesAndSettings=Utilitaires et param\u00E8tres
avanti.lbl.validate=Valider
avanti.lbl.valueAdded=Valeur ajout\u00E9e
avanti.lbl.valueCredited=\r\nValeur cr\u00E9dit\u00E9e
avanti.lbl.valueRestock=Valeur de r\u00E9approvisionnement
avanti.lbl.valueReturned=Valeur renvoy\u00E9e
avanti.lbl.valueScrapped=Valeur mise au rebut
avanti.lbl.variableData=Donn\u00E9es variables
avanti.lbl.variableData?=Donn\u00E9es variables?
avanti.lbl.viewFailedRateRequests=Afficher les demandes de tarif op\u00E9rateur \u00E9chou\u00E9es
avanti.lbl.viewStockAllocation=Voir la r\u00E9partition du mat\u00E9riel
avanti.lbl.virtual=Virtuel
avanti.lbl.waiting=Waiting
avanti.lbl.warehouse=Entrep\u00F4t
avanti.lbl.warehouseCode=Code d'entrep\u00F4t
avanti.lbl.warehouseDecartesDepotLabel=D\u00E9p\u00F4t Descartes
avanti.lbl.warning=Attention\!
avanti.lbl.washTime/Cost=Temps de lavage
avanti.lbl.webServiceMethod=M\u00E9thode de service Web
avanti.lbl.webTowerConfigTT=Le nombre de tours d\u00E9termine le nombre de couleurs que la presse peut imprimer.\r\n\r\nPerfectionnement \: une tour peut imprimer 1 couleur haut et bas en m\u00EAme temps donc un travail d'impression 4/4 n\u00E9cessiterait 4 tours.\r\n\r\nPar feuille\u00A0\: une tour ne peut imprimer qu'une seule couleur sur un c\u00F4t\u00E9 \u00E0 la fois, donc un travail 4/4 n\u00E9cessiterait 8\u00A0tours. Tours 1 \u00E0 4 pour imprimer 4 couleurs sur le devant et tours 5 \u00E0 8 pour imprimer 4 couleurs sur le c\u00F4t\u00E9 2.
avanti.lbl.weight=Poids
avanti.lbl.weight2=Poids (lbs)
avanti.lbl.weight2Metric=Poids (kgs)
avanti.lbl.whse_id=Entrep\u00F4t
avanti.lbl.width=Largeur (in)
avanti.lbl.width2=Largeur
avanti.lbl.widthMetric_cm=Largeur (cm)
avanti.lbl.widthMetric_mm=Largeur (mm)
avanti.lbl.workTemplate=Mod\u00E8le de travail
avanti.lbl.workTumble=Travail et culbute
avanti.lbl.workTurn=Travail et tour
avanti.lbl.workType=Type de travail
avanti.lbl.workatoRegisterJournalEntrySuccessful=\u00C9criture de journal cr\u00E9\u00E9e avec succ\u00E8s dans le syst\u00E8me comptable.
avanti.lbl.workatoRegisterWaitingToBeProcessed=Le registre est actuellement en attente d'\u00EAtre trait\u00E9. Une r\u00E9ponse appara\u00EEtra apr\u00E8s son traitement par le syst\u00E8me comptable.
avanti.lbl.worktype_setProdQuantityNumberPressSheets=D\u00E9finir la quantit\u00E9 de production en fonction du nombre de feuilles d'impression dans JDF Export
avanti.lbl.wrapTextWithQuotes=Envelopper le texte avec des guillemets
avanti.lbl.wtMRTime/Cost=WT temps de pr\u00E9p...
avanti.lbl.wtMRTime/Cost_tooltip=WT temps de pr\u00E9paration
avanti.lbl.xmlDeposit_amountIsGreaterThanDepositBalance=Le solde du compte de d\u00E9p\u00F4t client \u00E9tait inf\u00E9rieur au montant du paiement du XML.
avanti.lbl.xmlDeposit_amountIsGreaterThanTotalPrice=Le montant du paiement du XML est sup\u00E9rieur au prix total de la commande.
avanti.lbl.xmlDeposit_customerDoesntHaveADepositBalance=Ce client n'a pas de solde de d\u00E9p\u00F4t, donc aucun d\u00E9p\u00F4t n'a \u00E9t\u00E9 cr\u00E9\u00E9.
avanti.lbl.xmlDeposit_orderDoesntHavePrice=Cette commande n'a pas de prix total, donc aucun d\u00E9p\u00F4t n'a \u00E9t\u00E9 cr\u00E9\u00E9.
avanti.lbl.xmlError_FlaggedForDeletionCust=ERREUR\: le client <param1> a le statut 'Signal\u00E9 pour Suppression'. Une commande ne peut pas \u00EAtre cr\u00E9\u00E9e pour ce client.
avanti.lbl.xmlError_InactiveCustomer=ERREUR\: le client <param1> a le statut 'Inactif'. Une commande ne peut pas \u00EAtre cr\u00E9\u00E9e pour ce client.
avanti.lbl.yes=Oui
avanti.lbl.zoom=Zoom
avanti.lblAllCaps=TOUT
avanti.lblincludeAllCustomerPartNo=Inclure tous les num\u00E9ros de pi\u00E8ce client
avanti.menusubgroup.BinLocations=Emplacements des bacs
avanti.program.BinLocations=Emplacements des bacs
avanti.program.JobView=Vue du dossier
avanti.program.PDFReviewPasswordLabel=\u00C9preuvage Enfocus
avanti.program.cancelCashReceipt=R\u00E9trospective de sprint
avanti.program.employees=Employ\u00E9s
avanti.program.equipment=\u00C9quipement
avanti.program.imageLibrary=Biblioth\u00E8que d'images
avanti.program.items=Articles
avanti.program.paperStandards=Normes de substrat
avanti.program.refunds=Remboursements
avanti.program.sectionDetails=D\u00E9tails de la section
avanti.program.tasks=Les t\u00E2ches
avanti.program.trackCurrencyExchange=Suivre l'\u00E9change de devises
avanti.program.userManagement=Gestion des utilisateurs
avanti.receiptFreightInCostMethodInvalidOutsourced_msg=La m\u00E9thode Work-in-Progress (WIP) est la seule m\u00E9thode de calcul du co\u00FBt de transport des \u00E9l\u00E9ments de service externalis\u00E9s.
avanti.refundStatus$Open=Ouvert
avanti.refundStatus$Updated=Mise \u00E0 jour
avanti.refundType$CustomerDeposit=D\u00E9p\u00F4t client
avanti.refundType$PostageDeposit=D\u00E9p\u00F4t d'affranchissement
avanti.refundType$UnappliedCreditNote=Note de cr\u00E9dit non appliqu\u00E9e
avanti.registerDetailGP=D\u00E9tails de l'inscription
avanti.rpt.lbl.ChangeOrderNo=No. de demande de changement
avanti.rpt.lbl.OrderNo=No. de commande
avanti.rpt.lbl.PONo=No. de B/C
avanti.rpt.lbl.approvalDate=Date d'approbation
avanti.rpt.lbl.authorization=Autorisation
avanti.rpt.lbl.producingPlantOrderNum=Num\u00E9ro de commande de l'usine de production
avanti.rpt.lbl.requestDate=Date de la demande
avanti.rpt.lbl.revSplitPct=Fractionnement calcul\u00E9 %
avanti.rpt.lbl.revSplitdollar=Division des r\u00E9v.
avanti.rpt.lbl.taxCAPS=TAXE
avanti.rpt.openAdvanceBilling=FACTURE D'AVANCE non trait\u00E9e
avanti.rpt.openInvoice=FACTURE non trait\u00E9e
avanti.rpt.proFormaAdvancedBilling=FACTURE D'AVANCE proforma
avanti.rpt.proFormaInvoice=FACTURE proforma
avanti.rpt.title.interBranchProducingReport=Rapport de production intersectoriel
avanti.rpt.title.interBranchReport=Rapport de transfert inter-succursales
avanti.tab.ItemPaperStandards=Normes de substrat
avanti.tab.item_dieInfo=Die Info
avanti.tab.item_warehouses=Info Entrep\u00F4t
avanti.tab.items=Articles
avanti.tab.supplierQtyBreaks=Quantit\u00E9 Breaks
avanti.tab_close_all=Fermez tous les onglets
avanti.tab_close_current=Fermer l'onglet actuel
avanti.tab_close_left=Fermer tous les onglets \u00E0 gauche
avanti.tab_close_right=Fermer tous les onglets \u00E0 droite
avanti.tooltip.\#Items2Insert=\# d'\u00E9l\u00E9ments \u00E0 ins\u00E9rer
avanti.tooltip.AddNewJournalEntryLine=Ajouter une nouvelle ligne d'\u00E9criture de journal
avanti.tooltip.Backorders=La quantit\u00E9 de ce code d'article qui est en rupture de stock (en stock moins les r\u00E9sultats engag\u00E9s donnent une valeur n\u00E9gative) filtr\u00E9e par division/usine et entrep\u00F4t mais non filtr\u00E9e par projet.
avanti.tooltip.CalculatedCoilSize=Taille de bobine calcul\u00E9e
avanti.tooltip.CalculatedSpineThickness=\u00C9paisseur calcul\u00E9e de la colonne vert\u00E9brale
avanti.tooltip.CoilMaterial=Mat\u00E9riau de la bobine
avanti.tooltip.CountHistory=Une liste des ajustements de comptage pour ce code article non filtr\u00E9s par entrep\u00F4t ou projet.
avanti.tooltip.CustomerARControlAccount=Remplir ce champ remplacera le compte de contr\u00F4le A/R pour toutes les devises
avanti.tooltip.ExpectedIn=Le montant de ce code d'article qui figure actuellement sur les bons de commande actifs filtr\u00E9s par division/usine et entrep\u00F4t mais non filtr\u00E9s par projet.
avanti.tooltip.FifoCostingItem=Une liste de toutes les transactions FIFO pour ce code d'article filtr\u00E9es par division/usine et entrep\u00F4t mais non filtr\u00E9es par projet.
avanti.tooltip.GenericSectionModelMinSize=C\u2019est la zone consomm\u00E9e par l\u2019imposition de toutes les pages plus les saignements et garnitures de reliure sur la feuille de presse
avanti.tooltip.ItemFifo_ProjectFilter=Filtre les d\u00E9tails du lot FIFO des articles pour afficher les transactions li\u00E9es \u00E0 tous les projets ou \u00E0 un projet sp\u00E9cifique.
avanti.tooltip.ItemFifo_WarehouseFilter=Filtre les d\u00E9tails du lot FIFO des articles pour afficher les transactions li\u00E9es \u00E0 tous les entrep\u00F4ts ou \u00E0 un entrep\u00F4t sp\u00E9cifique en fonction des privil\u00E8ges des utilisateurs et filtr\u00E9es par division et usine.
avanti.tooltip.ItemFifo_totalAvailableQty=Quantit\u00E9 FIFO d'origine moins toute quantit\u00E9 d\u00E9duite de l'enregistrement FIFO en raison de toute transaction de sortie de stock moins toute quantit\u00E9 r\u00E9serv\u00E9e et filtr\u00E9e par division/usine/entrep\u00F4t et par projet.
avanti.tooltip.ItemFifo_totalRemainingQty=Qt\u00E9 FIFO d'origine moins FIFO non appliqu\u00E9 et toute quantit\u00E9 d\u00E9duite de l'enregistrement FIFO en raison de toute transaction de sortie de stock et filtr\u00E9e par division/usine/entrep\u00F4t et par projet.
avanti.tooltip.ItemFifo_totalUnappliedFIFO=La quantit\u00E9 qui n'a \u00E9t\u00E9 appliqu\u00E9e \u00E0 aucun enregistrement FIFO.
avanti.tooltip.ItemTransaction_onHandQty=Somme de l'inventaire physique actuel disponible en fonction des param\u00E8tres de filtre de division, d'usine, d'entrep\u00F4t et de projet.
avanti.tooltip.ItemwhseAvailableQty=Les codes d'articles (quantit\u00E9 en stock - quantit\u00E9 engag\u00E9e - quantit\u00E9 non disponible - quantit\u00E9 inutilisable) filtr\u00E9s par division/usine et entrep\u00F4t mais non filtr\u00E9s par projet.
avanti.tooltip.ItemwhseBackordQty=La quantit\u00E9 de ce code d'article qui est en rupture de stock (en stock - engag\u00E9) qui se traduit par une valeur n\u00E9gative filtr\u00E9e par division/usine et entrep\u00F4t mais non filtr\u00E9e par projet. \r\n\r\nCliquez avec le bouton droit pour afficher les d\u00E9tails de la commande en attente.
avanti.tooltip.ItemwhseCommittedQty=Le montant de ce code d'article engag\u00E9 dans les travaux/commande client filtr\u00E9 par division/usine et entrep\u00F4t mais non filtr\u00E9 par projet.\r\n\r\nCliquez avec le bouton droit pour afficher les d\u00E9tails de l'engagement.
avanti.tooltip.ItemwhseOnhandQty=L'inventaire physique actuel disponible filtr\u00E9 par division/usine et entrep\u00F4t, mais non filtr\u00E9 par projet.
avanti.tooltip.ItemwhseOnpoQty=Le montant de ce code d'article qui figure actuellement sur les bons de commande actifs filtr\u00E9s par division/usine et entrep\u00F4t mais non filtr\u00E9s par projet. \r\n\r\nCliquez avec le bouton droit pour afficher les d\u00E9tails des bons de commande en attente, des r\u00E9ceptions en attente et des ordres de production.
avanti.tooltip.ItemwhseReservedQty=Le sous-ensemble de cet article code les montants engag\u00E9s actuellement disponibles filtr\u00E9s par division/usine et entrep\u00F4t mais non filtr\u00E9s par projet. \r\n\r\nCliquez avec le bouton droit pour afficher les d\u00E9tails r\u00E9serv\u00E9s.
avanti.tooltip.ItemwhseUnavailibleQty=La quantit\u00E9 en stock de ce code d'article qui se trouve dans un emplacement d\u00E9sign\u00E9 comme indisponible, filtr\u00E9 Division/Usine et entrep\u00F4t mais non filtr\u00E9 par projet. Indisponible, impossible de s'engager ou de r\u00E9server \u00E0 partir de cet emplacement, c'est-\u00E0-dire une ferraille.
avanti.tooltip.ItemwhseUnusableQty=Le montant de cet article code la quantit\u00E9 en stock qui se trouve dans un emplacement d\u00E9sign\u00E9 comme inutilisable, filtr\u00E9 par division/usine et entrep\u00F4t mais non filtr\u00E9 par projet. Inutilisable, impossible de choisir ou d'utiliser dans Shopfloor \u00E0 partir de cet emplacement.
avanti.tooltip.MinimumOrderQty=Quantit\u00E9 minimum de commande
avanti.tooltip.OrderHistory=Une liste des commandes client pour ce code d'article filtr\u00E9e par filtre de division/usine mais non filtr\u00E9e par projet.
avanti.tooltip.OverriddenCoilSize=Taille de bobine annul\u00E9e
avanti.tooltip.PostageAndPostageTaskTotal=Total des frais d'affranchissement et d'affranchissement
avanti.tooltip.PriceOfPostage=Prix \u200B\u200Bde l'affranchissement
avanti.tooltip.Projectfilter=Filtre les transactions d'articles pour afficher les transactions li\u00E9es \u00E0 tous les projets ou \u00E0 un projet sp\u00E9cifique et filtr\u00E9es par division et usine.
avanti.tooltip.PurchaseHistory=Historique des achats pour ce code d'article filtr\u00E9 par division/usine et entrep\u00F4t mais non filtr\u00E9 par projet.
avanti.tooltip.SendAndReleaseAllSelectedPurchaseOrdersInGp=Envoyez tous les bons de commande s\u00E9lectionn\u00E9s au GP et d\u00E9finissez le statut \u00E0 envoyer au fournisseur.
avanti.tooltip.SendAndReleasePurchaseInGp=Envoyez le bon de commande au g\u00E9n\u00E9raliste et d\u00E9finissez le statut \u00E0 envoyer au fournisseur.
avanti.tooltip.SendPurchaseOrderToGp=Envoyez le bon de commande au g\u00E9n\u00E9raliste.
avanti.tooltip.SetPurchaseOrderStatusToReleasedInGP=D\u00E9finissez le statut du bon de commande sur Lanc\u00E9 au fournisseur.
avanti.tooltip.TheMinimumOrderQtyIs=La quantit\u00E9 minimum de commande pour cet article est
avanti.tooltip.Transactions=Une liste de toutes les transactions pour ce code d'article filtr\u00E9es par division/usine et entrep\u00F4t mais non filtr\u00E9es par projet.
avanti.tooltip.UDF=Champs d\u00E9finis par l'utilisateur, non filtr\u00E9s.
avanti.tooltip.WarehouseInfo=Les entrep\u00F4ts o\u00F9 r\u00E9side ce code d'article sont filtr\u00E9s par le filtre Division/Usine mais non filtr\u00E9s par Projet.
avanti.tooltip.Warehousefilter=Filtre les transactions d'articles pour afficher les transactions li\u00E9es \u00E0 tous les entrep\u00F4ts ou \u00E0 un entrep\u00F4t sp\u00E9cifique en fonction des privil\u00E8ges des utilisateurs et filtr\u00E9es par division et usine.
avanti.tooltip.addPostageItem=Ajouter un article d'affranchissement
avanti.tooltip.addRecord=Ajouter un enregistrement
avanti.tooltip.addSupplier=Ajouter un article d'affranchissement
avanti.tooltip.allowTiling=Autoriser le carrelage
avanti.tooltip.applyProducingPlantAndRevenueSplit=Appliquer une usine de production et une r\u00E9partition des revenus
avanti.tooltip.avalaraTax=Taxe Avalara
avanti.tooltip.bleedLLong=\# Marges perdues longeur
avanti.tooltip.bleedWLong=\# Marges perdues largeur
avanti.tooltip.bleedsForWidth=Marges perdues larg. (G/D)
avanti.tooltip.boxSelecttheAddress=Cette case s\u00E9lectionne l'adresse qui sera imprim\u00E9e sur la facture.
avanti.tooltip.clearAllEnvironmentItemClasses=Effacer toutes les classes d'\u00E9l\u00E9ments de rapport environnemental
avanti.tooltip.clearAllFilters=Effacer tous les filtres
avanti.tooltip.clearFilter=R\u00E9initialiser le filtre
avanti.tooltip.commentflag_invoice=Imprimer le commentaire sur la facture de vente
avanti.tooltip.commentflag_jobticket=Imprimer le commentaire sur le dossier de travail
avanti.tooltip.commentflag_orderack=Imprimer le commentaire sur l'accus\u00E9 de r\u00E9ception de commande client
avanti.tooltip.commentflag_packingslip=Imprimer le commentaire sur le bordereau
avanti.tooltip.commentflag_pickingslip=Imprimer le commentaire sur la liste de s\u00E9lection
avanti.tooltip.commentflag_po=Transf\u00E9rer le commentaire dans le bon de commande
avanti.tooltip.commentflag_quoteletter=Imprimer le commentaire sur la lettre de citation
avanti.tooltip.commentflag_salesorder=Transf\u00E9rer le commentaire dans la commande client
avanti.tooltip.commitments=Le montant de ce code d'article engag\u00E9 dans les travaux/commande client filtr\u00E9 par division/usine et entrep\u00F4t, mais non filtr\u00E9 par projet.
avanti.tooltip.coverMaterial=Mat\u00E9riau de couverture
avanti.tooltip.currencyExchangeMethodNode=* La m\u00E9thode d'\u00E9change ne peut pas \u00EAtre modifi\u00E9e apr\u00E8s avoir \u00E9t\u00E9 utilis\u00E9e sur un document de vente (devis, commande client ou plan de projet)
avanti.tooltip.currencyExchangeMethodNote2=\r\n* Multiply sera ajout\u00E9 dans une prochaine version.
avanti.tooltip.currencyGPNote=La m\u00E9thode d'\u00E9change doit \u00EAtre oppos\u00E9e \u00E0 ce qui est dans GP pour fonctionner tandis que le taux de change doit \u00EAtre le m\u00EAme.
avanti.tooltip.currentCost_estUOM=Co\u00FBt actuel dans l'estimation de UOM
avanti.tooltip.distributions_cashReceiptCancel=Les distributions ne sont disponibles qu'apr\u00E8s la mise \u00E0 jour de l'annulation du re\u00E7u de caisse dans le registre.
avanti.tooltip.downloadFile=T\u00E9l\u00E9charger un fichier.
avanti.tooltip.dynamicsIncludeJobWithPurchaseRegisterJournalEntry=Seuls les registres de re\u00E7us d'achat ex\u00E9cut\u00E9s une fois cette case coch\u00E9e seront \u00E9clat\u00E9s. Les registres de r\u00E9ex\u00E9cution cr\u00E9\u00E9s avant cette case \u00E0 cocher seront sous la forme dans laquelle ils ont \u00E9t\u00E9 cr\u00E9\u00E9s.
avanti.tooltip.dynamicsPasswordResetFailedRegisters=Mot de passe pour r\u00E9initialiser un registre ayant \u00E9chou\u00E9 comme pass\u00E9.
avanti.tooltip.excludeDocStreamFromInvoice=Apr\u00E8s le changement, les utilisateurs doivent se d\u00E9connecter du syst\u00E8me et se reconnecter pour que cette option prenne effet
avanti.tooltip.export=Exporter
avanti.tooltip.filterOptionsContains=Ce filtre utilise "Commence par" par d\u00E9faut. Pour utiliser "Contient", pr\u00E9fixez le texte avec un "%".
avanti.tooltip.freightExpensedNotDisplayed=Le fret est pass\u00E9 en charges et ne sera pas affich\u00E9.
avanti.tooltip.getsRevenue=Cr\u00E9e une entr\u00E9e de journal sur la facture lorsqu'elle est coch\u00E9e.
avanti.tooltip.grindOff=Taille de mouture
avanti.tooltip.inkMixTime/Cost=Temps de m\u00E9lange d'encre
avanti.tooltip.interBranch_ReceivableJournalEntry=Lorsqu'elle est coch\u00E9e, une \u00E9criture de journal pour l'int\u00E9gration de Receivable sera cr\u00E9\u00E9e en redistribuant les revenus aux usines de production
avanti.tooltip.interBranch_SalesOrderJournalEntry=Lorsqu'elle est coch\u00E9e, une entr\u00E9e de journal pour l'int\u00E9gration de la commande client sera cr\u00E9\u00E9e, redistribuant les revenus aux usines de production.
avanti.tooltip.item_creation_date=Date de cr\u00E9ation de l'article
avanti.tooltip.item_salestax_option=Option de taxe de vente
avanti.tooltip.itemqty_backord_qty=Quantit\u00E9 en rupture de stock
avanti.tooltip.jdfType_DownloadToCentralStorage=T\u00E9l\u00E9charger vers l'emplacement de stockage central\:
avanti.tooltip.journalEntryOutOfBalanceBy=L'\u00E9criture de journal est d\u00E9s\u00E9quilibr\u00E9e par\:
avanti.tooltip.journalEntryOutOfBalanceByType=L'\u00E9criture de journal est d\u00E9s\u00E9quilibr\u00E9e par type de distribution
avanti.tooltip.lastPriceChange=Dernier changement de prix
avanti.tooltip.leadTime=Le d\u00E9lai est en jours
avanti.tooltip.logoutUser=D\u00E9connectez cet utilisateur d'Avanti Slingshot
avanti.tooltip.lookup=Rechercher, modifier ou cr\u00E9er de nouveaux enregistrements
avanti.tooltip.nEndingBalance=Solde d'ouverture + somme du solde de transaction de la quantit\u00E9 en stock bas\u00E9e sur le filtre Division, Usine, Entrep\u00F4t et Projet.
avanti.tooltip.nOpeningBalance=\u00C0 l'origine, les importations de stocks initiales cr\u00E9aient un solde d'ouverture. Nous avons modifi\u00E9 le flux de travail afin que les importations de stock initiales cr\u00E9ent une transaction et non un solde d'ouverture.
avanti.tooltip.nTransactionBalance=Somme de toutes les transactions r\u00E9pertori\u00E9es en fonction des filtres de division, d'usine, d'entrep\u00F4t et de projet s\u00E9lectionn\u00E9s.
avanti.tooltip.noPackSizeAdjustment=Cela ignorera l'ajustement de la taille du paquet lors de l'achat et autorisera des quantit\u00E9s d\u00E9cimales.
avanti.tooltip.nrExtraPlates/Cost=\# Plaques suppl\u00E9mentaires
avanti.tooltip.numberOfRewindRolls=Nombre de rouleaux de rembobinage
avanti.tooltip.overrideCost_est=Surcharge des co\u00FBts pour toutes les qtys dans l'estimation de l'UOM
avanti.tooltip.overrideCost_qty=Remplacez le co\u00FBt de cette quantit\u00E9 lors de l'estimation de l'UOM.\r\nAffecte le co\u00FBt du substrat pour cette quantit\u00E9, mais n'affecte pas C.Cost.
avanti.tooltip.pressRunSpeed=Presse vitesse d'ex\u00E9cution
avanti.tooltip.pressSheetsSpoils=Feuilles de presse avec butin
avanti.tooltip.processUnconfirmedTimeAndMaterial=Lorsque cette case est coch\u00E9e, le syst\u00E8me permettra \u00E0 l'utilisateur de traiter les entr\u00E9es de temps et de mat\u00E9riel non confirm\u00E9es dans le registre des factures.
avanti.tooltip.producingPlantOrderDocumentStream=Production d'un flux de documents de commande d'usine
avanti.tooltip.purchaseFreightVarianceMsgForDebitCredit=Le compte de d\u00E9bit et de cr\u00E9dit ne s'applique pas aux \u00E9critures d'\u00E9cart d'achat ou de fret.
avanti.tooltip.purchaseOrderSentToGP=Le bon de commande a \u00E9t\u00E9 envoy\u00E9 \u00E0 GP Dynamics
avanti.tooltip.purchasingUOM=UOM d'achat
avanti.tooltip.registerProcess_ClearRegister=Effacer les donn\u00E9es des registres
avanti.tooltip.registerProcess_CreateRegister=Cr\u00E9er un registre
avanti.tooltip.registerProcess_EditRegister=Modifier les options de filtre de registre
avanti.tooltip.registerProcess_reviewRegister=Examiner les donn\u00E9es du registre en fonction des filtres
avanti.tooltip.reserved=Le sous-ensemble de cet article code les montants engag\u00E9s actuellement disponibles, filtr\u00E9s par division/usine et entrep\u00F4t, mais non filtr\u00E9s par projet.
avanti.tooltip.resetFilters=R\u00E9initialiser les filtres
avanti.tooltip.rightClick_RerunFailedIntegration=Cliquez avec le bouton droit pour relancer l'int\u00E9gration ayant \u00E9chou\u00E9.
avanti.tooltip.rightClick_ShowRefundDetails=Cliquez avec le bouton droit pour afficher les d\u00E9tails du remboursement
avanti.tooltip.rightClick_setUserDefault=Clic droit pour d\u00E9finir comme utilisateur par d\u00E9faut
avanti.tooltip.roundingApplied=L'arrondi sera ajout\u00E9 sur une ligne distincte dans GP.
avanti.tooltip.runSpoils%=D\u00E9chets de presse -%
avanti.tooltip.salesTaxGroup=Groupe de taxe de vente
avanti.tooltip.selectPlantToAssignRevenue=S\u00E9lectionnez l'usine \u00E0 laquelle vous souhaitez affecter des revenus
avanti.tooltip.sendAllSelectedPoToGp=Envoyez tous les bons de commande s\u00E9lectionn\u00E9s \u00E0 GP.
avanti.tooltip.sendMessageToUser=Envoyer un message \u00E0 cet utilisateur
avanti.tooltip.setAllSelectedPoToReleased=D\u00E9finissez tous les bons de commande s\u00E9lectionn\u00E9s sur lanc\u00E9s au fournisseur.
avanti.tooltip.setEstimateCost=Le co\u00FBt estim\u00E9 sera mis \u00E0 jour avec le co\u00FBt d\u00E9barqu\u00E9 du fournisseur s\u00E9lectionn\u00E9
avanti.tooltip.setupSpoils=D\u00E9chets de pr\u00E9paration
avanti.tooltip.setupTime=Temps d'install. (min)
avanti.tooltip.showContractCharges=Afficher les frais de contrat
avanti.tooltip.showDistributionDetails=Afficher les d\u00E9tails de la distribution
avanti.tooltip.showInvoiceCost=Afficher le co\u00FBt de la facture
avanti.tooltip.showSystemPreferenceChangeLog=Afficher le journal des modifications des pr\u00E9f\u00E9rences syst\u00E8me
avanti.tooltip.showUnappliedFIFO=Afficher FIFO non appliqu\u00E9
avanti.tooltip.showWIPrecordOnly=Ce filtre Case \u00E0 cocher collectera tous les enregistrements WIP cr\u00E9\u00E9s dans la plage de dates du filtre. La date de fin des enregistrements sera ignor\u00E9e.
avanti.tooltip.soShowOnInvoice=Valeur de facture par d\u00E9faut
avanti.tooltip.taxAdditionalCharges=Ce champ contr\u00F4le le statut fiscal des frais suppl\u00E9mentaires et des majorations ind\u00E9pendamment du statut fiscal standard
avanti.tooltip.taxItem_frieghtTaxExempt=Cochez cette case pour appliquer une exon\u00E9ration de taxe sur les recettes de fret
avanti.tooltip.toggleRecordCheckBox=Cocher tout / D\u00E9cocher tous les drapeaux d'enregistrement
avanti.tooltip.viewStockAllocation=Voir la r\u00E9partition du mat\u00E9riel
avanti.tooltip_exportDataDictionary=Exporter le dictionnaire de donn\u00E9es pour l'\u00E9cran actuel
avanti.tootlip.runSpoils=D\u00E9chets de presse
avanti.tt.FirstAddress=Premi\u00E8re Page
avanti.tt.LastAddress=Derni\u00E8re Page
avanti.tt.NextAddress=Page Suivante
avanti.tt.PreviousAddress=Page Pr\u00E9c\u00E9dente
avanti.tt.pushSupplierValuesToAllOtherQuantities=Pousser les valeurs des fournisseurs vers toutes les autres quantit\u00E9s
avanti.tt.workPool_scheduleForward=Ceci avance le nombre sp\u00E9cifi\u00E9 de d\u00E9calages. Ensuite, il parcourt les machines du pool de travail dans l'ordre (en commen\u00E7ant par la premi\u00E8re), en essayant de planifier chacune d'entre elles pendant ce quart de travail. Le premier qui peut \u00EAtre programm\u00E9 est utilis\u00E9.\r\n\r\nCeci est diff\u00E9rent de l'\u00E9quilibrage de charge en ce sens que nous ne consid\u00E9rons pas quelle machine a la plus grande capacit\u00E9, c'est juste bas\u00E9 sur l'ordre des machines.\r\n\r\nCela ne s'applique pas lors de l'utilisation de l'option Planifier en arri\u00E8re.
avanti.utils.dataImport.vl_dataImportType.Vendors=Fournisseurs
avvanti.lbl.UseOAuth2.0Model=Utiliser le mod\u00E8le OAuth 2.0
i18n\:avanti.lbl.employeeId=identifiant de l'employ\u00E9
i18n\:avanti.lbl.invoiceBalanceAmount=Montant du solde de la facture
i18n\:avanti.lbl.invoiceNumberCredit=Cr\u00E9dit de num\u00E9ro de facture
i18n\:svy.fr.lbl.favicon=Favoris
lbl.avanti.FieldRequired=Champ requis
lbl.avanti.appliedPostageDeposit=Appliqu\u00E9 - D\u00E9p\u00F4ts postaux
lbl.avanti.appliedSalesOrderDeposit=Appliqu\u00E9 - D\u00E9p\u00F4ts de commandes client
lbl.avanti.customerPostageReceived=Affranchissement client re\u00E7u
lbl.avanti.soPostageDepositReceived=D\u00E9p\u00F4ts d'affranchissement de commande client re\u00E7us
mydate=dd/MM/yyyy
rpt.AccountNumber=N \u00B0 de compte
rpt.AdditionalQuantities=Quantit\u00E9s Suppl\u00E9mentaires
rpt.AdvanceBilling_caps=FACTURE D'AVANCE
rpt.Amount=Montant
rpt.AmountStacked=Prix
rpt.AmountStacked_allCAPS=PRIX
rpt.ApprovedBy=Approuv\u00E9 par
rpt.ApprovedByStacked=Approuv\u00E9 par
rpt.Backordered=Livraison partielle
rpt.BackorderedStacked=Diff\u00E9r\u00E9s
rpt.BillTo=Factur\u00E9 \u00E0
rpt.BinLocation=Bin Lieu
rpt.Box(es)=Boite(s)
rpt.Box(s)=Bo\u00EEte(s)
rpt.Boxes=Bo\u00EEtes
rpt.Buyer=Acheteur
rpt.BuyerStacked=Acheteur
rpt.Chargeback=R\u00E9trofacturation
rpt.ChargebackStacked=R\u00E9trofacturation
rpt.Contact=Contacte
rpt.ContactStacked=Contact
rpt.CreatedBy=Cr\u00E9e par
rpt.CreditNoteNumber=Num\u00E9ro
rpt.CreditNote_caps=NOTE DE CR\u00C9DIT
rpt.CustomerPO=Bon de commande client
rpt.CustomerPOStacked=No De Commande
rpt.DateIssued=Date
rpt.DateRequired=Date requise
rpt.DeliverTo=Exp\u00E9di\u00E9 \u00E0
rpt.DeliveryInstructions=Instructions de livraison
rpt.Disc=Remise
rpt.Email=e
rpt.Estimate=Estimation
rpt.EstimateCAPS=\# ESTIMATION
rpt.EstimateStacked=Estimation
rpt.Estimate_caps=ESTIMATION
rpt.EstimatorAllCaps=ESTIMATEUR
rpt.Expected=Date pr\u00E9vu
rpt.ExpectedStacked=Attendu
rpt.Fax=f
rpt.Flat_Size=Taille \u00E0 plat
rpt.FreightStacked=Fret
rpt.Invoice=facture / Invoice
rpt.InvoiceNumber=Num\u00E9ro
rpt.Invoice_caps=FACTURE
rpt.Item/Description=Article/Description
rpt.LessAdvanceBillStacked=Facture moins \r\navance
rpt.Line=Ligne
rpt.Number=R\u00E9f\u00E9rence
rpt.Ordered=Command\u00E9e
rpt.OrderedFrom=Fournisseur
rpt.OrderedStacked=Command\u00E9s
rpt.PONumber=Numero
rpt.PackSlip=Bon de Livraison
rpt.PackSlipNumber=No Bon de Livraison
rpt.Per=Par
rpt.PerStacked=Par
rpt.Phone=t.
rpt.PurchaseOrder=Bon de Commande
rpt.Qty=Qt\u00E9
rpt.Quantity=Quantit\u00E9
rpt.QuantityStacked=Quantit\u00E9
rpt.Receiver=Re\u00E7u
rpt.Reprint=R\u00E9impression
rpt.Rev=R\u00E9v
rpt.SalesOrder=No De Dossier
rpt.SalesOrderStacked=No De Dossier
rpt.SalesRep=Vendeur
rpt.SalesTax=Taxes de vente
rpt.SalesTaxDetails=D\u00E9tails de la taxe de vente
rpt.SalesTaxStacked=Taxes  de vente
rpt.ServicerepAllCaps=REP DE SERVICE
rpt.SetupStacked=Installation
rpt.ShipDate=Date d'exp\u00E9dition
rpt.ShipTo=Exp\u00E9di\u00E9 \u00E0
rpt.ShipVia=Exp\u00E9di\u00E9 par
rpt.ShipViaStacked=Exp\u00E9di\u00E9 par
rpt.ShipmentQuantity=Navire Quantit\u00E9
rpt.Shipped=Exp\u00E9di\u00E9s
rpt.ShippedToDateStacked=Livr\u00E9s \u00E0 ce jour
rpt.Shipper=Exp\u00E9diteur
rpt.Shipping=Transport
rpt.ShippingCharge=Charge d'exp\u00E9dition
rpt.ShippingDetails=Description
rpt.ShippingStacked=Transport
rpt.Subtotal=Sous-Total
rpt.Subtotal.CAPS=SOUS-TOTAL
rpt.SubtotalStacked=Sous-Total
rpt.Tasks=Les t\u00E2ches
rpt.Terms=Termes
rpt.TermsAllCaps=TERMES
rpt.TermsStacked=Termes
rpt.TimeDelivered=Heure de livraison
rpt.TimeShipped=Heure d'exp\u00E9dition
rpt.Title=Titre
rpt.TotalDueAmount=Montant total d\u00FB
rpt.Total_Cost=Co\u00FBt total
rpt.UOM=Unit\u00E9
rpt.UOMStacked=Unit\u00E9
rpt.UnitPrice=Prix \u200B\u200Bunitaire
rpt.UnitPriceStacked=Prix unitaire
rpt.backColor_allCaps=COULEURS DU DOS
rpt.bleeds_allCaps=MARGES PERDUES
rpt.boxesof=Boites de
rpt.boxof=Boite de
rpt.caliper_allCaps=\u00C9TRIER
rpt.descriptionAllCaps=DESCRIPTION
rpt.frontColor_allCaps=COULEURS DEVANT
rpt.invdtl.lbl.exntendedPrice=Prix Total
rpt.lbl.CashReceiptNo=Num\u00E9ro de ticket de caisse
rpt.lbl.TransferOrderTotal=Total de l'ordre de transfert
rpt.lbl.TransferPlantCode=Transfert du code de l'installation
rpt.lbl.address1=Adresse1
rpt.lbl.address2=Adresse2
rpt.lbl.address3=Adresse3
rpt.lbl.filelocation=Emplacement du fichier
rpt.lbl.inv.postage=Frais de poste
rpt.lbl.inv.postageStack=Frais de poste
rpt.lbl.invlindet.sumtotal=R\u00E9sum\u00E9 total
rpt.lbl.invlindet.totfreight=Exp\u00E9dition Totale
rpt.lbl.invlindet.totpostage=Affranchissement Total
rpt.lbl.invtl.jobNumber=Num\u00E9ro du dossier
rpt.lbl.lineDetailReport=RAPPORT DE D\u00C9TAIL DE LIGNE
rpt.lbl.lineInvoiceno=No. de Facture
rpt.lbl.lineTotal=Total de la ligne
rpt.lbl.multipleLocations=Plusieurs emplacements
rpt.lbl.name=Nom
rpt.lbl.ouritem=Notre article
rpt.lbl.packingSlips=Bon(s) de Livraison
rpt.lbl.po.FreightStacked=Fret
rpt.lbl.po.amount=Prix
rpt.lbl.ps.Received=Re\u00E7u
rpt.lbl.setupCost=Co\u00FBt d'installation
rpt.lbl.stackedSalesOrd=Commande client
rpt.lbl.state=Etat
rpt.lbl.zip=Code postal
rpt.method_allCaps=METHODE
rpt.noSectionTypeDefined=<<Aucun type de section d\u00E9fini>>
rpt.no_allCaps=NON
rpt.of=de
rpt.p=t.
rpt.packingslip.SalesOrder=No De Dossier
rpt.po.lbl.freight=Transport
rpt.po.lbl.ponumber=Num\u00E9ro
rpt.postage=Frais de poste
rpt.priceperThosand_allCaps=Prix pour mille
rpt.qtyprice_allcaps=QUANTIT\u00C9/PRIX
rpt.quantity_allCaps=QUANTIT\u00C9
rpt.quantitystack_allCaps=QUANTIT\u00C9
rpt.reference=R\u00E9f\u00E9rence
rpt.sheets=feuille(s)
rpt.size_allCaps=TAILLE
rpt.sizepages_allCaps=FORMAT
rpt.sizesheets_allCaps=FORMAT
rpt.sort_allCaps=TRIER
rpt.stockcolor_allCaps=STOCK COULEUR
rpt.title.multicurrencyapplyrecords=Enregistrements d'application manuelle multidevises - Notes de cr\u00E9dit
rpt.title.summaryReport=RAPPORT SOMMAIRE
rpt.titleAllCaps=TITRE
rpt.to=\u00E0
rpt.unitPrice_allCaps=PRIX UNITAIRE
rpt.yes_allCaps=OUI
servoy.aboutDialog.credits=Remerciements
servoy.aboutDialog.title=A Propos
servoy.applicationException.connectionLost=La connexion avec le serveur a \u00E9t\u00E9 perdue, red\u00E9marrer l'application
servoy.applicationException.errorCode=Code erreur Inconnu {0}
servoy.applicationException.execureProgramFailed=L'ex\u00E9cution du programme externe a echou\u00E9e
servoy.applicationException.incorrectLogin=Login Incorrect
servoy.applicationException.invalidInput=Saisie Invalide
servoy.applicationException.invalidInputFormat=La saisie de '{0}', est incompatible avec le format '{2}'
servoy.applicationException.javascriptError=Erreur dans le JavaScript
servoy.applicationException.maintenanceMode=Le serveur est en mode Maintenance
servoy.applicationException.noLicense=Plus de Licences Disponibles
servoy.button.apply=Appliquer
servoy.button.browse=Parcourir
servoy.button.cancel=Annuler
servoy.button.close=Fermer
servoy.button.copy=Copier
servoy.button.default=Par D\u00E9faut
servoy.button.details=D\u00E9tails >>
servoy.button.finish=Terminer
servoy.button.hide=<< R\u00E9duire
servoy.button.moveDown=Haut
servoy.button.moveUp=Haut
servoy.button.next=suivant
servoy.button.ok=OK
servoy.button.prev=pr\u00E9c
servoy.button.quit=Quitter
servoy.client.datachangelabel.tooltip=Donn\u00E9es modifi\u00E9es par un autre utilisateur
servoy.client.editlabel.tooltip=Modifier l'enregistrement
servoy.client.error.executing.method=Erreur pendant l'ex\u00E9cution de la m\u00E9thode de fermeture de la solution m\u00E9thode\: {0}
servoy.client.error.finding.dataservice=Impossible de trouver la source de donn\u00E9es, le serveur n'est peut-\u00EAtre pas d\u00E9marr\u00E9
servoy.client.error.finding.repository=Impossible de trouver le repository, le serveur n'est peut-\u00EAtre pas d\u00E9marr\u00E9
servoy.client.error.loadingsolution=Impossible de charger la solution {0}
servoy.client.error.opensolution=Impossible d'ouvrir la\u00A0solution
servoy.client.findModeHelp.betweenGeneralCondition=(Entre les  valeurs x et y, incluses)
servoy.client.findModeHelp.caseInsensitiveCondition=(Insensible \u00E0 la casse)
servoy.client.findModeHelp.containsPercentCondition=(contient le caract\u00E8re '%')
servoy.client.findModeHelp.containsTextCondition=(contient 'x')
servoy.client.findModeHelp.containsUnderscoreCondition=(contient le caract\u00E8re '_')
servoy.client.findModeHelp.containsXCharYCondition=(contient 'x' suivie par n'importe quel caract\u00E8re et 'y')
servoy.client.findModeHelp.dateFields=Champs Date
servoy.client.findModeHelp.equalsDateCondition=(equals value x ,entire day)
servoy.client.findModeHelp.equalsGeneralCondCol1=x
servoy.client.findModeHelp.equalsGeneralCondition=(\u00E9gal \u00E0 x)
servoy.client.findModeHelp.equalsNumberCondition=(\u00E9gal \u00E0 x)
servoy.client.findModeHelp.equalsSpaceXCondition=(\u00E9gal \u00E0 un espace et 'x')
servoy.client.findModeHelp.formatDateCol1=c|format
servoy.client.findModeHelp.formatDateCol2=(applique le format au crit\u00E8re 'x|dd-MM-yyyy')
servoy.client.findModeHelp.generalCol1=G\u00E9n\u00E9ral
servoy.client.findModeHelp.generalCol2=-aide pour la recherche -
servoy.client.findModeHelp.gtGeneralCondition=(sup\u00E9rieur \u00E0 x)
servoy.client.findModeHelp.gteGeneralCondition=(sup\u00E9rieur ou \u00E9gal \u00E0 x)
servoy.client.findModeHelp.ltGeneralCondition=(inf\u00E9rieur \u00E0 x)
servoy.client.findModeHelp.lteGeneralCondition=(inf\u00E9rieur ou \u00E9gal \u00E0 x)
servoy.client.findModeHelp.modifiedCondition=(modifier le crit\u00E8re, d\u00E9pends du type du champ)
servoy.client.findModeHelp.notGeneralCondition=(crit\u00E8re Diff\u00E9rent de)
servoy.client.findModeHelp.nowDateCondition=(\u00E9gal \u00E0 maintenant, date et ou heure)
servoy.client.findModeHelp.nullGeneralCondition=(est nul)
servoy.client.findModeHelp.nullTextCondition=(est nul ou vide)
servoy.client.findModeHelp.nullZeroCondition=(est nul ou z\u00E9ro)
servoy.client.findModeHelp.numberFields=Champs Num\u00E9rique
servoy.client.findModeHelp.orGeneralCondition=(condition1 ou condition2)
servoy.client.findModeHelp.textFieldsCol1=Champs Texte
servoy.client.findModeHelp.todayCondition=(\u00E9gal \u00E0 aujourd'hui)
servoy.client.locklabel.tooltip=Donn\u00E9es verrouill\u00E9es
servoy.client.message.activetransaction=Il y a des transactions en cours. Voulez-vous les valider?
servoy.client.message.activetransaction.title=Transaction en cours
servoy.client.message.clientregister=Inscription du client
servoy.client.message.closeopensolution=Une solution est active, la fermer et continuer?
servoy.client.message.error.registerclient=Inscription du client impossible
servoy.client.message.loginfailed=Echec d'authentification
servoy.client.message.loginfailed.title=Erreur d'authentification
servoy.client.message.remotesolutionclose=Solution ferm\u00E9e par l'administrateur
servoy.client.message.remotesolutionclose.title=Solution ferm\u00E9e
servoy.client.message.unsupportedBrowser=Navigateur non support\u00E9 '{0}', veuillez le mettre \u00E0 jour vers une version plus r\u00E9cente
servoy.client.module.notfound=Impossible de trouver le module  '{0}'
servoy.client.serverdisconnect.dialog.label=Vous \u00EAtes d\u00E9connect\u00E9 du serveur. Tentative de reconnexion
servoy.client.serverdisconnect.dialog.title=Vous \u00EAtes d\u00E9connect\u00E9 du serveur
servoy.client.serverdisconnect.optionpane.question=Vous \u00EAtes toujours d\u00E9connect\u00E9 du serveur\!\r\nFermer le client?
servoy.client.serverdisconnect.optionpane.title=Vous \u00EAtes toujours d\u00E9connect\u00E9 du serveur..
servoy.client.serverdisconnect.restarting.solution=Anciennes transactions ou verrouillage d\u00E9tect\u00E9s, veuillez red\u00E9marrer la solution
servoy.client.serverdisconnect.restarting.solution.title=Echec de reconnexion
servoy.client.ssllabel.tooltip=Utilisation de SSL
servoy.client.status.application.init=Initialisation de l'application
servoy.client.status.application.setup=Configuration de l'application
servoy.client.status.closingsolution=Fermeture de la Solution
servoy.client.status.connecting.dataservice=Connexion \u00E0 la source de donn\u00E9es
servoy.client.status.initializing=Initialisation
servoy.client.status.initializing.solution=Initialisation de la Solution
servoy.client.status.load.plugins=Chargement des Plugins
servoy.client.status.loading.preferencepanels=Chargement des pr\u00E9f\u00E9rences
servoy.client.status.loadingsolution=Chargement de la solution {0}
servoy.client.status.start.repository=D\u00E9marrage du repository
servoy.client.transactionlabel.tooltip=Transaction active
servoy.colorchooser.title=Choisir une couleur
servoy.conversion.error.date=Impossible de convertir\: {0} en date
servoy.conversion.error.global=Le param\u00E8tre global\: nom '{0}' / type '{1}' a une valeur du mauvais type {2}
servoy.databasemanager.error.inOutParamsShouldBeEqual=Les Arguments d'entr\u00E9e et sortie doivent \u00EAtre de la m\u00EAme taille et de type array
servoy.dateChooser.selectDate=Choisir une date
servoy.datechooser.label.friday=Ven
servoy.datechooser.label.monday=Lun
servoy.datechooser.label.monthoverview=Affichage Mois
servoy.datechooser.label.saturday=Sam
servoy.datechooser.label.sunday=Dim
servoy.datechooser.label.thursday=Jeu
servoy.datechooser.label.tuesday=Mar
servoy.datechooser.label.wednesday=Mer
servoy.datechooser.message.timeinvalid=Valeur de date invalide
servoy.datechooser.nextmonth=Mois suivant
servoy.datechooser.nextyear=Ann\u00E9e suivante
servoy.datechooser.previousmonth=Mois Pr\u00E9c\u00E9dent
servoy.datechooser.previousyear=Ann\u00E9e pr\u00E9c\u00E9dente
servoy.datechooser.title=Choisir une date (s\u00E9lection\: {0})
servoy.datechooser.today=Aujourd'hui
servoy.edittoolbar.label=Editer
servoy.error.cannotConnectToDataServer=Impossible de se connecter au serveur de BDD
servoy.error.cannotLoadDBDefinitions=Echec d'initialisation, impossible de charger la d\u00E9finition du serveur de BDD depuis\:
servoy.error.cannotSetupDataServer=Impossible de configurer le serveur de BDD
servoy.error.cantFindDatabase=BDD non trouv\u00E9e, elle n'est peut \u00EAtre pas d\u00E9marr\u00E9e
servoy.error.converterNotFound=Convertisseur {0} manquant
servoy.error.cycleDetected=Cycle d\u00E9tect\u00E9 dans le calcul\: {0}  table {1}, pile\: {2}
servoy.error.executing.program=Erreur lors de la tentative d'ex\u00E9cution du programme\: 
servoy.error.executingCalculation=Exception en ex\u00E9cutant le calcul\: {0}  table {1}, erreur\: {2}
servoy.error.sql.noConnection=Le pilote n'a pas renvoy\u00E9 de connexion (URL non reconnue par le pilote)
servoy.error.transaction.allreadyInProgress=Transaction d\u00E9j\u00E0 d\u00E9marr\u00E9e, impossible d'en d\u00E9marrer une autre.
servoy.error.transaction.closedOrInProgress=connexion d\u00E9j\u00E0 ferm\u00E9e ou transaction en cours
servoy.error.transaction.mustRollback=rollback n\u00E9cessaire
servoy.error.transaction.noTransaction=pas de transaction en cours
servoy.error.validatorNotFound=Validator {0} manquant
servoy.exception.connectionInvalid=Connexion non valide
servoy.exception.creating.transactionid=Echec lors de la cr\u00E9ation d'un id unique pour la transaction
servoy.exception.invalidServerConnection=Connexion au serveur Servoy invalide
servoy.exception.primaryKeyNeeded=impossible de travailler sans cl\u00E9 primaire sur la table '{0}'
servoy.exception.serverAndTableNotFound=Serveur {0} avec la table {1} non trouv\u00E9
servoy.exception.serverNotFound=Serveur {0} non trouv\u00E9
servoy.exception.solutionNotFound=Solution {0} non trouv\u00E9e
servoy.exception.tableNotFound=Table non trouv\u00E9e\:
servoy.exception.transaction.already.ended=La transaction id {0} demand\u00E9e apr\u00E8s la fin de celle-ci
servoy.filechooser.button.title=S\u00E9lection
servoy.filechooser.button.upload=T\u00E9l\u00E9charger
servoy.filechooser.title=S\u00E9lection du r\u00E9pertoire
servoy.filechooser.upload.title=Formulaire de t\u00E9l\u00E9chargement
servoy.fontchooser.title=Police
servoy.formManager.error.ExecutingOpenSolutionMethod=Erreur pendant l'ex\u00E9cution de la m\u00E9thode de d\u00E9marrage \:{0}
servoy.formManager.error.LoadingForms=impossible de charger les formulaires de la solution
servoy.formManager.error.LoadingSkin=impossible de charger les d\u00E9corations de la solution
servoy.formManager.error.MakingGlobalScriptsMenu=erreur lors du chargement des m\u00E9thodes globalscript dans le menu des scripts
servoy.formManager.error.PrintPreview=impossible de cr\u00E9er l'aper\u00E7u avant impression
servoy.formManager.error.SettingVoidForm=Un formulaire d\u00E9fini comme formulaire principal ne peut \u00EAtre nul
servoy.formManager.loadingForm=Chargement/Cr\u00E9ation des formulaires
servoy.formManager.menuGlobalMethods=M\u00E9thodes globales
servoy.formManager.showingForm=Affichage du formulaire
servoy.formManager.warningAccessForm=Vous n'avez pas les droits pour afficher ce formulaire
servoy.formPanel.deleteall.text=Effacer tous les enregistrements
servoy.formPanel.deleteall.warning=Etes-vous sur de vouloir supprimer tous les enregistrements?
servoy.formPanel.error.cannotShowForm=Ce formulaire ne peut \u00EAtre affich\u00E9
servoy.formPanel.error.compilingScripts=Impossible de r\u00E9cup\u00E9rer les scriptmethods pour compilation
servoy.formPanel.error.deleteRecord=Suppression de l'enregistrement impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.deleteRecords=Suppression des enregistrement impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.duplicateRecord=Duplication de l'enregistrement impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.evalString=Impossible d'\u00E9valuer la cha\u00EEne '
servoy.formPanel.error.executeMethod=Impossible d'ex\u00E9cuter la m\u00E9thode du formulaire '
servoy.formPanel.error.executingMethod=Erreur pendant l'ex\u00E9cution de la m\u00E9thode nomm\u00E9e \:{0}
servoy.formPanel.error.formData=Impossible de r\u00E9cup\u00E9rer les donn\u00E9es du formulaire
servoy.formPanel.error.formNotInitialized=Acc\u00E8s au Foundset alors qu'il n'est pas encore initialis\u00E9 (peut-\u00EAtre dans la m\u00E9thode form.onLoad)
servoy.formPanel.error.initFormScope=Impossible d'initialiser le scope du formulaire ou de cr\u00E9er JSForm
servoy.formPanel.error.initScriptEngine=Impossible d'initialiser le moteur de script
servoy.formPanel.error.invertRecords=Inversion des enregistrements impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.loadingPKData=Erreur lors du chargement des donn\u00E9es de cl\u00E9 primaire
servoy.formPanel.error.newRecord=Cr\u00E9ation de l'enregistrement impossible, essayez d'afficher tous les enregistrements
servoy.formPanel.error.noRecordsToPrint=Il n'y a aucun enregistrement \u00E0 imprimer, continuer ?
servoy.formPanel.error.omitNotPossible=Ignorer impossible sur plus de 200 enregistrements
servoy.formPanel.error.omitRecord=Ignorer l'enregistrements impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.omittedRecords=Afficher les enregistrements ignor\u00E9s impossible, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.printDocument=Impression du document impossible
servoy.formPanel.error.printPreview=Aper\u00E7u avant Impression du document impossible
servoy.formPanel.error.saveFormData=Impossible de sauvegarder les donn\u00E9es du formulaire
servoy.formPanel.error.searchFailed=Erreur lors de la recherche, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.settingFoundset=Erreur de d\u00E9finition du foundset du formulaire
servoy.formPanel.error.setupForm=Impossible de d\u00E9finir le formulaire
servoy.formPanel.error.showFormData=Impossible d'afficher les donn\u00E9es du formulaire
servoy.formPanel.error.sortRecords=Impossible de trier les enregistrements, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.sortRecordsDialog=Impossible d'afficher la boite de dialogue de tri, essayez d'afficher tous les enregistrements 
servoy.formPanel.error.wrongFoundsetTable=Impossible de d\u00E9finir le foundset depuis la table {0} avec un formulaire utilisant la table {1}
servoy.formPanel.message.largeResultset=Votre jeu de r\u00E9sultat est tr\u00E8s grand (> {0} enregistrements) \u00EAtes-vous s\u00FBr de vouloir continuer ?
servoy.formPanel.printCurrentRecord=Imprimer l'enregistrement en cours
servoy.formPanel.search.invalidRange=Les champs suivants on des plage de donn\u00E9es invalides \:
servoy.formPanel.search.noResults=Aucun r\u00E9sultat pour la recherche, modifier les crit\u00E8res ?
servoy.formPanel.status.printProgress=Impression en cours
servoy.formPanel.status.xmlPrinting=Impression XML en cours
servoy.foundSet.error.aggregate=Il y un probl\u00E8me lors de l'agrregation
servoy.foundSet.error.editNullRecord=Impossible de passer en modification un enregistrement nul
servoy.foundSet.error.loadingRecord=Impossible de charger l'enregistrement
servoy.foundSet.error.noAccess=Vous n'avez pas les droits pour afficher ces donn\u00E9es
servoy.foundSet.error.noCreateAccess=Vous n'avez pas les droits pour cr\u00E9er ces donn\u00E9es
servoy.foundSet.error.noDeleteAccess=Vous n'avez pas les droits pour effacer ces donn\u00E9es
servoy.foundSet.error.noModifyAccess=Vous n'avez pas les droits pour modifier ces donn\u00E9es
servoy.foundSet.error.retrievingMoreData=Impossible de r\u00E9cup\u00E9rer plus de donn\u00E9es du formulaire
servoy.foundSet.error.sqlsheet=La page SQL ne peut \u00EAtre nulle
servoy.foundSet.noRecord=Aucun enregistrement, veuillez d'abord en cr\u00E9er un
servoy.foundSet.query.error.firstTable=Le nom de la table du foundset n'est pas la premi\u00E8re table de la requ\u00E8te
servoy.foundSet.query.error.fromNotFound=FROM' non trouv\u00E9 dans la requ\u00EAte
servoy.foundSet.query.error.fullyQualified=Toutes les colonnes SQL doivent \u00EAtre d\u00E9finies avec leurs noms pleinement qualifi\u00E9s ex\: 'orders.order_id'
servoy.foundSet.query.error.groupByNotAllowed=GROUP BY' non permis dans une requ\u00EAte
servoy.foundSet.query.error.havingNotAllowed=HAVING' non permis dans une requ\u00EAte
servoy.foundSet.query.error.incorrectNumberOfPKS=La requ\u00EAte n'a pas le nombre correct de cl\u00E9 primaire
servoy.foundSet.query.error.orderByNotFound=ORDER BY' non permis dans une requ\u00EAte
servoy.foundSet.query.error.startWithSelect=Requ\u00EAte devant commencer par 'SELECT'
servoy.foundSet.query.error.wrongTable=Le nom de la table du foundset non trouv\u00E9 dans la requ\u00EAte
servoy.foundSet.recordLocked=Enregistrement verrouill\u00E9
servoy.foundset.error.createRelatedRecordsNotAllowed=La relation '{0}' ne vous permet pas de cr\u00E9er des enregistrements li\u00E9s
servoy.foundset.error.deleteNotGranted=Suppression non autoris\u00E9e
servoy.foundset.error.noParentDeleteWithRelatedrecords=La relation '{0}' ne vous permet pas de suppprimer l'enregistrement parent lorsqu'il existe des enregistrements reli\u00E9s.
servoy.foundset.error.recordValidationFailed=Erreur de validation de l'enregistrement\: {0}
servoy.foundsetManager.error.flushingRelatedFoundset=erreur de nettoyage du relatedfoundset avec l'id
servoy.foundsetManager.error.missingTable=Impossible de trouver la table {0} (non d\u00E9finie ou manquante) dans le serveur {1}
servoy.foundsetManager.error.savingData=Erreur lors de l'enregistrement des donn\u00E9es
servoy.foundsetupdater.error.setColumnValue=Impossible de d\u00E9finir la valeur de la colonne
servoy.foundsetupdater.updateFailed=Erreur de mise \u00E0 jour des donn\u00E9es
servoy.general.cancel.title=Annuler
servoy.general.clickOk=Cliquer sur OK pour Continuer
servoy.general.confirm=Confirmation
servoy.general.error=Erreur
servoy.general.info=Info
servoy.general.loading=Chargement...
servoy.general.preview=Aper\u00E7u
servoy.general.status.ready=Pr\u00EAt
servoy.general.warning=Attention
servoy.i18nDialog.title=Editeur I18N
servoy.i18nPanel.default=D\u00E9faut
servoy.i18nPanel.filter=Filtre
servoy.i18nPanel.key=Cl\u00E9
servoy.i18nPanel.locale=R\u00E9gional
servoy.i18nPanel.localeText=Texte R\u00E9gional
servoy.i18nPanel.referenceText=Texte de r\u00E9f\u00E9rence
servoy.imageMedia.error.loading=Erreur lors du chargement du m\u00E9dia
servoy.imageMedia.loadingImage=Chargement du m\u00E9dia...
servoy.imageMedia.popup.menuitem.copy=Copier un media
servoy.imageMedia.popup.menuitem.load=Charger un fichier
servoy.imageMedia.popup.menuitem.paste=Coller un Media
servoy.imageMedia.popup.menuitem.remove=Supprimer le Media
servoy.imageMedia.popup.menuitem.save=Sauver le Media
servoy.imagefilter.description=Images Seulement
servoy.javascript.error.lockedForDeleteIndex=Impossible de supprimer dans une \u00E9tendue verrouill\u00E9e, index\:{0}
servoy.javascript.error.lockedForDeleteName=Impossible de supprimer dans une \u00E9tendue verrouill\u00E9e, nom\:{0}
servoy.javascript.error.lockedForIndex=Impossible d'ajouter dans une \u00E9tendue verrouill\u00E9e, index\:{0}, valeur\:{1}
servoy.javascript.error.lockedForName=Impossible d'ajouter dans une \u00E9tendue verrouill\u00E9e, om\:{0}, valeur\:{1}
servoy.license.aspLicense=Licence asp
servoy.license.label=Licence
servoy.license.notrialleft=Licences (ou licences d'\u00E9valuation ) non disponibles. Fermeture
servoy.license.plural=Licences
servoy.license.registered=Enregistr\u00E9 pour {0} avec {1} {2}
servoy.license.single=licence
servoy.license.siteLicense=licence site
servoy.license.switchingtotrial=Nombre maximum de licences atteint, passage en mode \u00E9valuation
servoy.license.text=Vous utilisez des licences d'\u00E9valuation, achetez des licences ou red\u00E9marrer l'application
servoy.license.unregistered=Non enregistr\u00E9
servoy.logindialog.label.name=Nom (Utilisateur)
servoy.logindialog.password=Mot de Passe
servoy.logindialog.remember=M\u00E9moriser
servoy.logindialog.specify.password=Veuillez indiquer le mot de passe
servoy.logindialog.specify.username.password=Veuillez indiquer l'utilisateur et le mot de passe
servoy.logindialog.title=Connexion
servoy.maintenance.clientRegisterForbidden=Le serveur est en mode Maintenance. Les connexions ne sont pas autoris\u00E9es. Veuillez r\u00E9essayer plus tard.
servoy.maintenance.label=Maintenance
servoy.menuitem.about=A propos...
servoy.menuitem.about.mnemonic=A
servoy.menuitem.back=Retour
servoy.menuitem.back.mnemonic=R
servoy.menuitem.close=Fermer
servoy.menuitem.close.mnemonic=F
servoy.menuitem.close.status.text=Fermeture de la Solution
servoy.menuitem.copy=Copier
servoy.menuitem.copy.mnemonic=C
servoy.menuitem.cut=Couper
servoy.menuitem.cut.mnemonic=t
servoy.menuitem.deleteAll=Effacer Tout
servoy.menuitem.deleteAll.mnemonic=A
servoy.menuitem.deleteAll.status.text=Effacement des Enregistrements
servoy.menuitem.deleteRecord=Effacer Enregistrement
servoy.menuitem.deleteRecord.mnemonic=e
servoy.menuitem.deleteRecord.status.text=Effacement de l'Enreg. En cours
servoy.menuitem.duplicateRecord=Copier Enregistrement
servoy.menuitem.duplicateRecord.mnemonic=D
servoy.menuitem.duplicateRecord.status.text=Dupliquer l'enregistrement
servoy.menuitem.edit=Edition
servoy.menuitem.edit.mnemonic=E
servoy.menuitem.exit=Quitter
servoy.menuitem.exit.mnemonic=Q
servoy.menuitem.export=Export
servoy.menuitem.extendSearch=Recherche \u00E9tendue
servoy.menuitem.extendSearch.mnemonic=\u0000
servoy.menuitem.extendSearch.status.text=recherche des donn\u00E9es
servoy.menuitem.file=Fichier
servoy.menuitem.file.mnemonic=F
servoy.menuitem.find=Recherche
servoy.menuitem.find.mnemonic=F
servoy.menuitem.forward=Suivant
servoy.menuitem.forward.mnemonic=F
servoy.menuitem.help=Aide
servoy.menuitem.help.mnemonic=H
servoy.menuitem.import=Import
servoy.menuitem.invertRecords=Inverser la s\u00E9lection
servoy.menuitem.invertRecords.mnemonic=R
servoy.menuitem.invertRecords.status.text=Inversion de la s\u00E9lection
servoy.menuitem.logout=D\u00E9connexion
servoy.menuitem.logout.mnemonic=l
servoy.menuitem.methods=M\u00E9thodes
servoy.menuitem.methods.mnemonic=M
servoy.menuitem.newRecord=Nouvel Enregistrement
servoy.menuitem.newRecord.mnemonic=N
servoy.menuitem.newRecord.status.text=Ajout d'un Enregistrement
servoy.menuitem.nextRecord=Enr. Suivant
servoy.menuitem.nextRecord.mnemonic=x
servoy.menuitem.omitRecord=Ignorer l'Enregistrement
servoy.menuitem.omitRecord.mnemonic=O
servoy.menuitem.omitRecord.status.text=Enregistrement ignor\u00E9
servoy.menuitem.openSolution=Ouvrir une Solution
servoy.menuitem.openSolution.mnemonic=O
servoy.menuitem.openSolution.status.text=Ouverture de la Solution
servoy.menuitem.pageSetup=Mise en Page
servoy.menuitem.pageSetup.error=Impossible d'afficher les param\u00E8tres de Mise en Page
servoy.menuitem.pageSetup.mnemonic=u
servoy.menuitem.paste=Coller
servoy.menuitem.paste.mnemonic=V
servoy.menuitem.preferences=Pr\u00E9f\u00E9rences
servoy.menuitem.preferences.mnemonic=f
servoy.menuitem.previousRecord=Enr. Pr\u00E9c\u00E9dent
servoy.menuitem.previousRecord.mnemonic=p
servoy.menuitem.print=Imprimer
servoy.menuitem.print.mnemonic=p
servoy.menuitem.printPreview=Aper\u00E7u avant Impression
servoy.menuitem.printPreview.mnemonic=P
servoy.menuitem.redo=R\u00E9tablir
servoy.menuitem.redo.mnemonic=R
servoy.menuitem.reduceSearch=R\u00E9duire la Recherche
servoy.menuitem.reduceSearch.status.text=recherche des donn\u00E9es
servoy.menuitem.relookup=Recharger
servoy.menuitem.replace=Remplacer
servoy.menuitem.replace.mnemonic=e
servoy.menuitem.saveData=Sauver
servoy.menuitem.search=Recherche
servoy.menuitem.search.mnemonic=f
servoy.menuitem.select=S\u00E9lectionner
servoy.menuitem.select.mnemonic=S
servoy.menuitem.selectAll=Tout s\u00E9lectionner
servoy.menuitem.selectAll.mnemonic=A
servoy.menuitem.showAll=Afficher Tout
servoy.menuitem.showAll.mnemonic=A
servoy.menuitem.showAll.status.text=recherche des donn\u00E9es
servoy.menuitem.showOmitted=Afficher les Enreg. Ignor\u00E9s
servoy.menuitem.showOmitted.mnemonic=t
servoy.menuitem.showOmitted.status.text=Affichage des Enreg. Ignor\u00E9s
servoy.menuitem.sort=Trier
servoy.menuitem.sort.mnemonic=T
servoy.menuitem.sort.status.text=Tri des enregistrements
servoy.menuitem.spell=Correction Orthographique
servoy.menuitem.spell.mnemonic=S
servoy.menuitem.undo=Annuler
servoy.menuitem.undo.mnemonic=A
servoy.menuitem.view=Affichage
servoy.menuitem.view.mnemonic=V
servoy.menuitem.viewAsList=Mode Liste
servoy.menuitem.viewAsList.mnemonic=s
servoy.menuitem.viewAsRecord=Mode Enregistrement
servoy.menuitem.viewAsRecord.mnemonic=R
servoy.menuitem.viewAsTable=Mode Grille
servoy.menuitem.viewAsTable.mnemonic=e
servoy.menuitem.window=Fen\u00EAtre
servoy.menuitem.window.mnemonic=W
servoy.modeManager.status.design=Concepteur
servoy.modeManager.status.findText=Recherche (Appuyer sur entr\u00E9e ou F3 pour d\u00E9marrer la recherche ou utiliser les menus), survoler ici pour une aide rapide
servoy.modeManager.status.preview=Aper\u00E7u
servoy.pagesetup.button.landscape=Paysage
servoy.pagesetup.button.portrait=Portrait
servoy.pagesetup.button.reversedlandscape=Paysage Retourn\u00E9
servoy.pagesetup.button.reversedportrait=Portrait Retourn\u00E9
servoy.pagesetup.label.bottommargin=Bas
servoy.pagesetup.label.leftmargin=Gauche
servoy.pagesetup.label.pageheight=Hauteur de Page
servoy.pagesetup.label.pagewidth=Largeur de Page
servoy.pagesetup.label.rigthmargin=Droite
servoy.pagesetup.label.size=Taille\:
servoy.pagesetup.label.topmargin=Haut
servoy.pagesetup.list.size.custom=Taille personnalis\u00E9e\:
servoy.pagesetup.margins.title=Marges
servoy.pagesetup.orientation.title=Orientation
servoy.pagesetup.paper.title=Feuille
servoy.pagesetup.title=Config. Page
servoy.pagesetup.unit.inches=Pouces
servoy.pagesetup.unit.millimetres=Millimetres
servoy.plugin.agent.displayname=Agent Plugin
servoy.plugin.agent.menu.animate=Animer\!
servoy.plugin.agent.menu.hide=Masquer
servoy.plugin.agent.preference.showOnStartup=Afficher l'Agent au d\u00E9marrage
servoy.plugin.agent.preference.tabname=Agent
servoy.plugin.agent.text.animate=Animation
servoy.plugin.agent.text.hello=Bonjour \!
servoy.plugin.dialog.displayname=Dialog Plugin
servoy.plugin.excel.displayname=Export/Import Excel Plugin
servoy.plugin.excel.fromExcel=Depuis un fichier Excel
servoy.plugin.excel.toExcel=Vers un fichier Excel
servoy.plugin.export.cancelExport=Etes-vous s\u00FBr de vouloir annuler l'export?
servoy.plugin.export.exception=Exception pendant l'export
servoy.plugin.export.exportEncoding=Encodage
servoy.plugin.export.exportHeader=Ent\u00EAte de l'Export
servoy.plugin.export.fileOptions.title=Options de Fichier
servoy.plugin.export.status.buildingUI=Cr\u00E9ation de l'interface utilisateur
servoy.plugin.export.status.loadingData=Chargement des donn\u00E9es depuis la BDD
servoy.plugin.export.success=Export de {0} enregistrement effectu\u00E9 avec succ\u00E9s
servoy.plugin.export.title=Export
servoy.plugin.export.toFile=Export vers le fichier\:
servoy.plugin.exportImport.fileSelect.exception=Exception pendant la s\u00E9lection du fichier
servoy.plugin.exportImport.specifyFileLabel=Indiquer le fichier\:
servoy.plugin.exportImport.specifyFileTitle=Indiquer un fichier
servoy.plugin.file.displayname=File Plugin
servoy.plugin.file.folderDelete.title=Supprimer le dossier
servoy.plugin.file.folderDelete.warning=Etes-vous sur de vouloir supprimer le dossier?
servoy.plugin.fmp.displayname=FMP Plugin
servoy.plugin.http.displayname=HTTP Plugin
servoy.plugin.import.cancelImport=Etes-vous s\u00FBr de vouloir annuler l'import?
servoy.plugin.import.chooseFile=Choisir un fichier
servoy.plugin.import.chooseSheet=Choisir une feuille\:
servoy.plugin.import.columnTable.tooltip=For fixed value import use 'fixed\:xxxxx' on the left For column selection, click in the database field rows
servoy.plugin.import.columnname.databaseField=Champ de la Base de donn\u00E9es
servoy.plugin.import.columnname.importField=Champ \u00E0 importer
servoy.plugin.import.exception=Exception pendant l'import
servoy.plugin.import.fileLoad.exception=Exception pendant le chargement du fichier
servoy.plugin.import.importing.label=Import  en cours
servoy.plugin.import.importingDone.label=Import  Termin\u00E9e
servoy.plugin.import.rowContainsFieldnames=Noms de champs dans la premi\u00E8re ligne
servoy.plugin.import.selectFile=S\u00E9lection du fichier\:
servoy.plugin.import.specifyDateFormat.title=Indiquer le format de date (voir le manuel pour les sp\u00E9cifications)
servoy.plugin.import.specifyMapping.title=Indiquer la correspondance des champs (cliquer sur le champ de la BDD)
servoy.plugin.import.status.doneImporting=Import effectu\u00E9\: {0} Lignes import\u00E9es
servoy.plugin.import.status.loadingData=Chargement des donn\u00E9es dans la BDD
servoy.plugin.import.status.organizingData=Organisation des donn\u00E9es depuis le fichier
servoy.plugin.import.status.organizingDataIntoColumns=Organisation des donn\u00E9es depuis le fichier vers les champs
servoy.plugin.import.status.rowsImported=Import des lignes\: {0} Lignes import\u00E9es
servoy.plugin.import.title=Import
servoy.plugin.mail.displayname=Mail Plugin
servoy.plugin.mail.incoming.mailserver.label=Server de courrier entrant (pop)\: 
servoy.plugin.mail.outgoing.mailserver.label=Server de courrier sortant (smtp)\:
servoy.plugin.mail.tabname=Mail
servoy.plugin.mailserver.defaultsubject=<no subject>
servoy.plugin.mailserver.displayname=Mail Server Plugin
servoy.plugin.pdfoutput.displayname=PDF Output Plugin
servoy.plugin.runtimebuilder.displayname=Runtime Builder Plugin
servoy.plugin.runtimebuilder.error.multiplyServers=La solution utilise plus d'un seul serveur ( {0} )
servoy.plugin.runtimebuilder.error.serverNotFound=serveur '{0}' non trouv\u00E9
servoy.plugin.runtimebuilder.error.serverNotValid=serveur '{0}' non valide, v\u00E9rifier ses param\u00E8tres
servoy.plugin.runtimebuilder.error.solutionHasNoServer=La solution n'utilise aucun serveur?
servoy.plugin.runtimebuilder.error.wrongpath=Le chemin sp\u00E9cifi\u00E9 n'est pas un r\u00E9pertoire
servoy.plugin.runtimebuilder.menuitem=Servoy Runtime Builder
servoy.plugin.runtimebuilder.status.settingUp=configuration du runtime...
servoy.plugin.scheduler.cannotScheduleJob=Impossible de planifier le job {0} erreur\: {1}
servoy.plugin.scheduler.cannotStart=Impossible de d\u00E9marrer le scheduler\! 
servoy.plugin.scheduler.invalidTimings=Expression (Chaine) de planification  {0} invalide\: {1}
servoy.plugin.tabxport.choose.separator=Choisir le s\u00E9parateur\:
servoy.plugin.tabxport.displayname=Export/Import Plugin
servoy.plugin.tabxport.format=Choisir le format appropri\u00E9\:
servoy.plugin.tabxport.linesFromFile=Lignes du fichier de donn\u00E9es
servoy.plugin.tabxport.menuitem.fromTextFile=Depuis un fichier Texte
servoy.plugin.tabxport.menuitem.toTextFile=Vers fichier texte 'valeurs s\u00E9par\u00E9es'  *.tab/*.csv
servoy.plugin.tabxport.separator.comma=Virgule
servoy.plugin.tabxport.separator.other=autre
servoy.plugin.tabxport.separator.semicolon=Point-virgule
servoy.plugin.tabxport.separator.space=Espace
servoy.plugin.tabxport.separator.tab=Tabulation
servoy.plugin.tabxport.textQualifier=Identificateur de texte
servoy.plugin.tabxport.useFixedWidth=Largeur de Colonne Fixe
servoy.plugin.tabxport.useSeparator=Utiliser le s\u00E9parateur
servoy.preference.general.tabName=G\u00E9n\u00E9ral
servoy.preference.general.useSystemPrintDialog=Utiliser le boite de dialogue d'impression syst\u00E8me
servoy.preference.locale.dateFormat=Format de Date
servoy.preference.locale.defaultLocale=Locale par d\u00E9faut
servoy.preference.locale.defaultTimezone=Fuseau horaire par d\u00E9faut
servoy.preference.locale.integerFormat=Format entier
servoy.preference.locale.numberFormat=Format num\u00E9rique
servoy.preference.locale.tabName=Donn\u00E9es Locales
servoy.preference.lookandfeel.chooseFont=Choisir la police
servoy.preference.lookandfeel.fontsize=Taille\:
servoy.preference.lookandfeel.label.defaultFont=Police par d\u00E9faut\:
servoy.preference.lookandfeel.label.lnf=Look and feel\: 
servoy.preference.lookandfeel.label.theme=Th\u00E8me\:
servoy.preference.lookandfeel.msg.undefined=ind\u00E9fini
servoy.preference.lookandfeel.tabName=Look And Feel
servoy.preference.service.tabName=Services
servoy.preference.service.twoWaySocket=D\u00E9marrer avec TwoWaySocket pour les clients derri\u00E8res un firewall
servoy.preferencedialog.title=Pr\u00E9f\u00E9rences de l'Application
servoy.preferencedialog.warning.reloadsolution=Changements effectifs apr\u00E8s rechargement de la solution
servoy.preferencedialog.warning.restartapplication=Changements effectifs apr\u00E8s red\u00E9marrage de la solution
servoy.print.error.cannotCreatePreview=Impossible de cr\u00E9er l'aper\u00E7u pour cette page
servoy.print.error.cannotPrintDocument=Impossible d'imprimer le document
servoy.print.error.retrievingAllData=Impossible de r\u00E9cup\u00E9rer les donn\u00E9es n\u00E9cessaire \u00E0 l'aper\u00E7u
servoy.print.msg.noPrintersFound=Imprimante (n\u00E9cessaire) non trouv\u00E9e, installer une imprimante SVP
servoy.print.printing.title=Impression
servoy.print.status.generatePages=G\u00E9n\u00E9ration des pages de l'aper\u00E7u
servoy.print.zoom=zoom
servoy.record.error.columnSizeTooSmall=Colonne {0} trop petite
servoy.record.error.gettingDataprovider=Obtention des donn\u00E9es du fournisseur '{0}', type '{1}'
servoy.record.error.nullRow=L'enregistrement ne peut \u00EAtre nul
servoy.record.error.settingDataprovider=D\u00E9finition des donn\u00E9es du fournisseur '{0}', type '{1}'
servoy.record.error.validation=Echec de Validation pour '{0}', avec la valeur\: {1}
servoy.relatedfoundset.error.noFK=Un argument where (foreignkey) doit \u00EAtre sp\u00E9cifi\u00E9 pour un related foundset, relationid servoy.relation.error\=Erreur dans la relation
servoy.relation.error=Erreur dans la relation
servoy.relation.error.dataproviderDoesntExist=Le fournisseur de donn\u00E9e {0} ou la colonne {1} n'existe pas tel que d\u00E9fini dans la relation {2}
servoy.relation.error.tableDoesntExist=La table {0} ou {1} n'existe pas tel que d\u00E9fini dans la relation {2}
servoy.relation.error.typeDoesntMatch=le type de cl\u00E9 de {0} ne correspond pas au type de {1}
servoy.runtime.title=Servoy Runtime
servoy.selectSolutionDialog.errorLoadingSolution=Erreur de chargement de la solution
servoy.selectSolutionDialog.selectSolution=Choisir la solution
servoy.sequence.exception.illegalCall=arguments incorrects dans getSequence\: client_id {0} object_type_id {1}
servoy.sequence.exception.illegalClient=impossible d'obtenir la s\u00E9quence suivante pour un client null
servoy.sequence.exception.noDefaults=Le repository ne contient pas les parametres par d\u00E9faut n\u00E9cessaires, manque\: 
servoy.sequence.locked=Impossible de mettre \u00E0 jour la s\u00E9quence, \u00E0 cause d'un verrouillage
servoy.sequence.missingColumnInfo=Le repository ne contient pas les parametres par d\u00E9faut n\u00E9cessaires, manque\: 
servoy.sequence.noAutoEnter=La s\u00E9quence de ColumnInfo n'est pas d\u00E9fini correctement
servoy.servoy.readingfile.msg=Lecture du fichier...
servoy.sliding.error.overlap=Superposition de composants glissants\!
servoy.sliding.error.overlap.components=les Composants {0} et {1} posent un probl\u00E8me de coulissage
servoy.sortDialog.sortTitle=Trier
servoy.sqlengine.error.columnMissing=Colonne {0} manquante dans la table {1}
servoy.sqlengine.error.noServer=Serveur {0} non trouv\u00E9...
servoy.sqlengine.error.noTransactionActive=Pas de transaction active pour l'id
servoy.sqlengine.error.noTransactionID=Pas de transaction avec l'id
servoy.sqlengine.error.notRegistered=Client non enregistr\u00E9 dans le serveur.
servoy.status.databaseInit0=Initialisation de la Base de donn\u00E9es
servoy.texttoolbar.label=Texte
servoy.texttoolbar.menuitem.horizontalline=Ligne Horizontale
servoy.texttoolbar.menuitem.orderedlist=Liste Tri\u00E9e
servoy.texttoolbar.menuitem.preformatted=Pr\u00E9format\u00E9
servoy.texttoolbar.menuitem.subscript=Subscript
servoy.texttoolbar.menuitem.superscript=Superscript
servoy.texttoolbar.menuitem.unorderedlist=Liste non tri\u00E9e
servoy.texttoolbar.tooltip.aligncenter=Centr\u00E9
servoy.texttoolbar.tooltip.alignleft=Alignement Gauche
servoy.texttoolbar.tooltip.alignright=Alignement Droit
servoy.texttoolbar.tooltip.bold=Gras
servoy.texttoolbar.tooltip.fontcolor=Couleur de la police
servoy.texttoolbar.tooltip.italic=Italique
servoy.texttoolbar.tooltip.more=Plus...
servoy.texttoolbar.tooltip.underline=Souligner
servoy.toolbar.print.button.nextPage.tooltip=Page Suivante
servoy.toolbar.print.button.pageSetup=Mise en Page
servoy.toolbar.print.button.prevPage.tooltip=Page Pr\u00E9c\u00E9dente
servoy.toolbar.print.button.print=Impression...
servoy.toolbar.print.title=Imprimer
servoy.toolbars.text=Barre d'Outils
servoy.userClient.message.fromServer=Message du serveur
servoy.windowMenuDialog.chooseForm=Choisir le formulaire
servoy.windowMenuDialog.more=Plus...
servoy.wizardwindow.quiting.message=Etes-vous s\u00FBre de vouloir Fermer
servoy.wizardwindow.quiting.title=Fermeture
svy.dlg.delete_record=Etes-vous sur de vouloir supprimer cet enregistrement ?
svy.fr.dlg.I18n_key_selector=Selecteur de cl\u00E9 i18n non disponible dans le Client Web, veuillez utiliser le mode smart.
svy.fr.dlg.autofill=Remplissage auto \u00E0 partir de l'enegistrement li\u00E9
svy.fr.dlg.baseformfirst=Renseigner le formulaire de base en premier
svy.fr.dlg.baseformfirst1=Renseigner le formulaire de base en premier
svy.fr.dlg.cancel_record=Etes-vous sur de vouloir annuler ?
svy.fr.dlg.delete=Etes-vous sur de vouloir supprimer cet enregistrement ?
svy.fr.dlg.delete_canceled=Impossible de supprimer cet enregistrement
svy.fr.dlg.deletes=Etes-vous sur de vouloir supprimer ces enregistrements ?
svy.fr.dlg.enter_name=Renseignez le champ Nom
svy.fr.dlg.enter_table_server=Renseigner le serveur et la table \u00E0 utiliser pour ce programme.
svy.fr.dlg.export_failed=L'export a \u00E9chou\u00E9
svy.fr.dlg.export_successful=Export termin\u00E9
svy.fr.dlg.filter_foundset=Renseignez le filtre du foundset en premier
svy.fr.dlg.help=Pas d'aide disponible pour ce formulaire
svy.fr.dlg.loginfailed=Erreur d autentification veuillez reessayer.
svy.fr.dlg.mandatory_fields=Renseigner les champs obligatoires
svy.fr.dlg.method_popmenu=The method you want this pop-menu to invoke.  \r\nSample global method\: globals.methodname  \r\nSample form method\: forms.yourform.methodname
svy.fr.dlg.new_password=Nouveau Mot de passe
svy.fr.dlg.newmenu=Est-ce que vous voulez cr\u00E9er un nouvel enregestrement racine ou un enfant ?
svy.fr.dlg.noMenuWarning=Vous n'avez pas les autorisations n\u00E9c\u00E9ssaires pour lancer ce menu
svy.fr.dlg.no_form_selected=Aucun Formulaire S\u00E9lectionn\u00E9
svy.fr.dlg.password_10_times=Atraduire
svy.fr.dlg.password_contain_letters_numbers=Le mot de passe doit contenir lettres et chiffres
svy.fr.dlg.password_length=Le mot de passe doit \u00EAtre d'une longueur d'au moins {0} caract\u00E8res
svy.fr.dlg.password_not_equal=Les mot de passe ne correspondent pas
svy.fr.dlg.password_renewed=Le mot de passe doit \u00EAtre chang\u00E9 tous les 10 jours
svy.fr.dlg.password_reserved_word=Le mot de passe  contient le mot r\u00E9serv\u00E9 {0} qui est interdit
svy.fr.dlg.password_same_begin=Le mot de passe  ne doit pas commencer comme le nom utilisateur
svy.fr.dlg.password_wrong_times=Nombre d'essais infructueux avant le bloquage du compte utilisateur
svy.fr.dlg.program_image=Le nom de l'image dans media library. Exemple \: 16_16_customers.png.
svy.fr.dlg.readColumns=Lecture automatique des colonnes depuis la table
svy.fr.dlg.record_lock=Enreg. Deja utilise par un autre utilisateur
svy.fr.dlg.search_again=Aucun enreg. Trouve, nouvelle recherche ?
svy.fr.dlg.selected_syspref197_no.deleteCreditNotes=\u00CAtes-vous s\u00FBr de vouloir supprimer les notes de cr\u00E9dit s\u00E9lectionn\u00E9es\u00A0?\r\nRemarque\u00A0\: les notes de cr\u00E9dit dont le statut est "Post\u00E9" ou "Imprim\u00E9" ne seront pas supprim\u00E9es.
svy.fr.dlg.selected_syspref197_yes.deleteCreditNotes=\u00CAtes-vous s\u00FBr de vouloir supprimer les notes de cr\u00E9dit s\u00E9lectionn\u00E9es\u00A0?\r\nRemarque\u00A0\: les notes de cr\u00E9dit dont le statut est "Publi\u00E9" ne seront pas supprim\u00E9es.
svy.fr.dlg.server_table=Le nom du serveur et de la table doit etre renseignes en premier
svy.fr.dlg.username_password_entered=veuillez rentrer le nom et le mot de passe utilisateur
svy.fr.dlg.warning_programname=Changer le nom du programme peut avoir des consequences si il est utilise dasn des relations
svy.fr.lbl.I18n_key_selector=Selecteur de cl\u00E9 i18n
svy.fr.lbl.active=Actif
svy.fr.lbl.add=Ajouter
svy.fr.lbl.addmode=Mode Ajout
svy.fr.lbl.admin=Admin
svy.fr.lbl.all_tables=Toutes les tables
svy.fr.lbl.allow_null=Autoriser valeur nulle
svy.fr.lbl.alt=Alt
svy.fr.lbl.available=Disponible
svy.fr.lbl.base_form_name=Form. De base
svy.fr.lbl.bkmrkadd=Epingler
svy.fr.lbl.bkmrkicon=Epingles
svy.fr.lbl.browse=parcourir
svy.fr.lbl.buttons=Boutons
svy.fr.lbl.cancel=Annuler
svy.fr.lbl.child=Enfant
svy.fr.lbl.choose_lang=Choisir votre langue
svy.fr.lbl.control=Contr\u00F4le
svy.fr.lbl.create_new_tableview=Cr\u00E9er une nouvelle vue Table
svy.fr.lbl.custom_sql=SQL personnalis\u00E9
svy.fr.lbl.default=Defaut
svy.fr.lbl.default_value=Valeur par defaut
svy.fr.lbl.defaultaccess=Acces par defaut
svy.fr.lbl.delete=Supprimer
svy.fr.lbl.delete_search=Supprimer la recherche
svy.fr.lbl.deletemode=Mode Suppression
svy.fr.lbl.description=Description
svy.fr.lbl.detaillist=Detail/Liste
svy.fr.lbl.display_field_header=Afficher entetes de champs
svy.fr.lbl.display_type=Afficher le type
svy.fr.lbl.display_value=Valeur affichee
svy.fr.lbl.divider_height=Hauteur du separateur
svy.fr.lbl.divider_locked=S\u00E9parateur verrouill\u00E9
svy.fr.lbl.dividerheight=Hauteur du separateur
svy.fr.lbl.dividerlocked=Separateur verrouille
svy.fr.lbl.edit=Modifier
svy.fr.lbl.edit_aloud=Mdification autorisee
svy.fr.lbl.edit_delete_view=Editer/Supprimer la vue
svy.fr.lbl.edit_in_table_mode=Modifier en mode table
svy.fr.lbl.edit_on_tab=Modifier dans les onglets
svy.fr.lbl.edit_tableview=Editer la vue table
svy.fr.lbl.editable=Modifiable
svy.fr.lbl.editallowed=Modification autorisee
svy.fr.lbl.editintablemode=Modifer en mode table
svy.fr.lbl.element=Element
svy.fr.lbl.email=Email
svy.fr.lbl.end=Fin
svy.fr.lbl.exit=Sortie
svy.fr.lbl.exit_application=Sortie de l application
svy.fr.lbl.export=Export
svy.fr.lbl.extend_search=Etendre la recherhce
svy.fr.lbl.favicon=Favoris
svy.fr.lbl.favorites=Favoris
svy.fr.lbl.favoritesadd=Ajouter un Favoris
svy.fr.lbl.fieldname=Nom du champ
svy.fr.lbl.fields=Champs
svy.fr.lbl.filename=Nom du Fichier
svy.fr.lbl.first_name=Pr\u00E9nom
svy.fr.lbl.form=Formulaire
svy.fr.lbl.form_name=Nom du Form.
svy.fr.lbl.form_type=Type de Form.
svy.fr.lbl.formproperties=Propri\u00E9t\u00E9 du Form.
svy.fr.lbl.foundset=Foundset
svy.fr.lbl.foundset_filter=Filtre du Foundset
svy.fr.lbl.function=Fonction
svy.fr.lbl.functions=Fonctions
svy.fr.lbl.group=Groupe
svy.fr.lbl.groups=Groupes
svy.fr.lbl.help=Aide
svy.fr.lbl.histbackward=Historique Pr\u00E9c.
svy.fr.lbl.histforward=Historique Suiv.
svy.fr.lbl.historyicon=Historique
svy.fr.lbl.id=Id
svy.fr.lbl.image=Image
svy.fr.lbl.initials=Initiales
svy.fr.lbl.insert=Inserer
svy.fr.lbl.key=Cle
svy.fr.lbl.keys=Cles
svy.fr.lbl.label=Etiquette
svy.fr.lbl.language=Langue
svy.fr.lbl.last_name=Nom de famille
svy.fr.lbl.ldap_enable_encrypted=Activer le cryptage ( SSL )
svy.fr.lbl.loading...=Chargement...
svy.fr.lbl.loading\u00C3\u00A2\u00E2\u0082\u00AC\u00C2\u00A6=Chargement...
svy.fr.lbl.locked=Verrouille
svy.fr.lbl.login=S'identifier
svy.fr.lbl.logout=D\u00E9connexion
svy.fr.lbl.lookup_window=Fen\u00EAtre de recherche
svy.fr.lbl.lookup_window\:=Fen\u00EAtre de recherche\:
svy.fr.lbl.mainreports=Etat principal
svy.fr.lbl.mandatory_fields=Champs obligatoires
svy.fr.lbl.maximum_bookmarks=Maximum d epingles atteint
svy.fr.lbl.maximum_shortcuts=Maximum de raccourcis atteint
svy.fr.lbl.menu=Menu
svy.fr.lbl.menu_image=Image du Menu
svy.fr.lbl.menu_type=Type de Menu
svy.fr.lbl.menuitems=Items du Menu
svy.fr.lbl.method=methode
svy.fr.lbl.modes=Modes
svy.fr.lbl.modules=Modules
svy.fr.lbl.mutiple_delete=Suppression multiple
svy.fr.lbl.name=Nom
svy.fr.lbl.name?=Nom ?
svy.fr.lbl.navigation=Navigation
svy.fr.lbl.new_password=Nouveau mot de passe
svy.fr.lbl.new_tableview=Nouvelle Vue Table
svy.fr.lbl.no=Non
svy.fr.lbl.no_form=Pas de formulaire
svy.fr.lbl.noreadonly=Pas en lecture seule
svy.fr.lbl.not_selected=Non selectionne
svy.fr.lbl.nr=Numero
svy.fr.lbl.ok=Ok
svy.fr.lbl.open_navigation=Ouvrir Module Navigation
svy.fr.lbl.open_security=Ouvrir Module S\u00E9curit\u00E9
svy.fr.lbl.open_valuelist=Ouvrir Module ValueLists
svy.fr.lbl.operator=Operateur
svy.fr.lbl.order=Ordre
svy.fr.lbl.owner=Proprietaire
svy.fr.lbl.owner_id=Id du proprietaire
svy.fr.lbl.owner_module=Proprietaire/Module
svy.fr.lbl.owners=Proprietaires
svy.fr.lbl.parent=Parent
svy.fr.lbl.parent_menu=Menu parent
svy.fr.lbl.password=Mot de passe
svy.fr.lbl.phone=T\u00E9l\u00E9phone
svy.fr.lbl.picture=Photo
svy.fr.lbl.popmenu=Popmenu
svy.fr.lbl.popmenus=Popmenus
svy.fr.lbl.prefix=Prefixe
svy.fr.lbl.print=Imprimer
svy.fr.lbl.program=Programme
svy.fr.lbl.program_name=Nom du Programme
svy.fr.lbl.properties=Propri\u00E9t\u00E9s
svy.fr.lbl.propertyname=Nom de la Propriete
svy.fr.lbl.propertyvalue=Propriete
svy.fr.lbl.read=Lecture
svy.fr.lbl.rec_all=Tous les enregistrements
svy.fr.lbl.rec_first=<< Premier. Enreg
svy.fr.lbl.rec_last=Dernier. Enreg >>
svy.fr.lbl.rec_next=Enreg. Suivant >
svy.fr.lbl.rec_prev=< Enreg. Precedent
svy.fr.lbl.record_delete=Effacer
svy.fr.lbl.record_duplicate=Dupliquer
svy.fr.lbl.record_edit=Modifier
svy.fr.lbl.record_history=Historique des enregistrements
svy.fr.lbl.record_information=Info Enreg.
svy.fr.lbl.record_locking=Verrouillage enregistrement
svy.fr.lbl.record_new=Nouveau
svy.fr.lbl.recordlocking=Verrouillage enregistrement
svy.fr.lbl.records_in_foundset=Enreg. Trouves
svy.fr.lbl.records_in_table=Enreg. Total
svy.fr.lbl.reduce_search=Reduire la recherche
svy.fr.lbl.related=Li\u00E9e\:
svy.fr.lbl.relation=Relation
svy.fr.lbl.reload=Recharger
svy.fr.lbl.return_to_detaul_table=Revenir \u00E0 la table par d\u00E9faut
svy.fr.lbl.return_type=Type de retour
svy.fr.lbl.return_value=Valeur de retour
svy.fr.lbl.retype_new_password=retapper le nouveau mot de passe
svy.fr.lbl.root=Racine
svy.fr.lbl.save=Sauver
svy.fr.lbl.save_record=Sauver l enregistrement
svy.fr.lbl.save_search=Sauver le recherche
svy.fr.lbl.search=Recherche
svy.fr.lbl.search_options=Options de recherche
svy.fr.lbl.securitykey=Cle de securite
svy.fr.lbl.selected=Selectionne
svy.fr.lbl.selected_record=Enreg. Selectionne
svy.fr.lbl.sequence=Sequence
svy.fr.lbl.server_name=Nom du serveur
svy.fr.lbl.server_table=Serveur/Table
svy.fr.lbl.servername=Nom du serveur
svy.fr.lbl.short_name=Non court
svy.fr.lbl.shortkey=Raccourci clavier
svy.fr.lbl.shortkeys=Raccourcis clavier
svy.fr.lbl.sort_order=Ordre de tri
svy.fr.lbl.sort_type=Type de tri
svy.fr.lbl.sort_value=Valeur de tri
svy.fr.lbl.start=Demarrer
svy.fr.lbl.startupview=Vue au demarrage
svy.fr.lbl.system=Syst\u00E8me
svy.fr.lbl.system_list=Liste syst\u00E8me
svy.fr.lbl.system_value=Valeur syst\u00E8me
svy.fr.lbl.tab=Onglet
svy.fr.lbl.tab_specific_description=Onglet\: Description sp\u00E9cifique
svy.fr.lbl.table=Table
svy.fr.lbl.table_filter=Table \: Filtre
svy.fr.lbl.table_name=Table \: Nom
svy.fr.lbl.tabs=Onglets
svy.fr.lbl.tabsequence=Ordre de tabulation
svy.fr.lbl.targetprogram=Programme cible
svy.fr.lbl.template_name=Modele \: Nom
svy.fr.lbl.templateproperties=Mod\u00E8le \: propriete
svy.fr.lbl.test_query=Tester la requete
svy.fr.lbl.title=Titre
svy.fr.lbl.tracking=Suivi
svy.fr.lbl.translation=Traduction
svy.fr.lbl.translations=Traductions
svy.fr.lbl.type=Type
svy.fr.lbl.unlock_user=deverrouiller l utilisateur
svy.fr.lbl.update=Mise \u00E0 jour
svy.fr.lbl.updatemode=Mode Modification
svy.fr.lbl.user=Utilisateur
svy.fr.lbl.user_group=Utilisateur/Groupe
svy.fr.lbl.username=Utilisateur
svy.fr.lbl.userpassword=Mot de passe
svy.fr.lbl.users=Utilisateurs
svy.fr.lbl.value=Valeur
svy.fr.lbl.valuelist=Liste de valeurs
svy.fr.lbl.valuelists=Listes de valeurs
svy.fr.lbl.values=Valeurs
svy.fr.lbl.view_details=Vue Detail
svy.fr.lbl.view_list=Vue Liste
svy.fr.lbl.view_list_tabs=Vue liste avec Onglets
svy.fr.lbl.visible=Visible
svy.fr.lbl.warning=Attention\!
svy.fr.lbl.warning_no_program=Vous n'avez pas les autorisations n\u00E9c\u00E9ssaires, veuillez contacter votre administrateur
svy.fr.lbl.week=Semaine
svy.fr.lbl.window_splitter=Separateur de fenetre
svy.fr.lbl.windowsplitter=Separateur de fenetre
svy.fr.lbl.wrong_login_attemt=Mauvaise tentative de connexion
svy.fr.lbl.yes=Oui
svy.fr.reset_table_headers=Reinitialiser les entetes de colonnes
svy.lb.creation_date=Cr\u00E9\u00E9 le (Date)
svy.lb.creation_user=Cr\u00E9\u00E9 par (Utilisateur)
svy.lb.modification_date=Modification - Date
svy.lb.modification_user=Modification - Utilisateur
svy.lb.signature=Signature
svy.lbl.add_argument=Ajouter un argument
svy.lbl.add_contact=Ajouter un Contact
svy.lbl.add_mailing=Ajouter un Mailing
svy.lbl.add_query=Ajouter une Requ\u00E8te
svy.lbl.add_template=Ajouter un Mod\u00E8le
svy.lbl.argument=Argument
svy.lbl.birthdate=Date de naissance
svy.lbl.contacts=Contacts
svy.lbl.delete_contact=Supprimer contact
svy.lbl.delete_mailing=Supprimer Mailing
svy.lbl.delete_query=Supprimer Requ\u00E8te
svy.lbl.delete_record=Supprimer Enregistrement
svy.lbl.delete_template=Supprimer Mod\u00E8le
svy.lbl.email=Email
svy.lbl.email_sender=Exp\u00E9diteur
svy.lbl.email_template=Mod\u00E8le d'email
svy.lbl.fax=T\u00E9l\u00E9copie
svy.lbl.first_column_id=La premi\u00E8re colonne doit \u00EAtre l'id
svy.lbl.first_name=Pr\u00E9nom
svy.lbl.foundset_query=Requ\u00E8te du Foundset
svy.lbl.gender=Genre
svy.lbl.html_body=Corps Html
svy.lbl.html_body_display=Corps Html Affich\u00E9
svy.lbl.last_name=Nom de famille
svy.lbl.mailings=Mailings
svy.lbl.name=Nom
svy.lbl.name_sender=Nom de l'exp\u00E9diteur
svy.lbl.no=Non
svy.lbl.notes=Notes
svy.lbl.output_query=R\u00E9sultat de la requ\u00E8te
svy.lbl.prefix=Pr\u00E9fixe
svy.lbl.properties=Propri\u00E9t\u00E9s
svy.lbl.queries=Requ\u00E8tes
svy.lbl.second_column_email=La seconde clonne doit \u00EAtre l'adresse Email
svy.lbl.send_mail=Envoyer le Mailing
svy.lbl.send_test_mail=Envoyer un Email de test
svy.lbl.sequence=S\u00E9quence
svy.lbl.subject=Object
svy.lbl.tags=Tags
svy.lbl.tel=T\u00E9l\u00E9phone
svy.lbl.templates=Mod\u00E8les
svy.lbl.yes=Oui
test.dummy=Dummy
