customProperties:"useCssPosition:true",
dataSource:"db:/avanti/in_landed_cost_udf",
extendsID:"38F0AEAC-C193-4066-A18A-466209D794B9",
items:[
{
cssPosition:"62,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"62",
width:"140"
},
enabled:true,
labelFor:"lcudf_label",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.name",
visible:true
},
name:"lcudf_label_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0D6D7E04-2E7E-4F8F-8D9D-3C698C1540D0"
},
{
cssPosition:"89,-1,-1,150,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"89",
width:"140"
},
dataProviderID:"lcudf_short_label",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"lcudf_short_label",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2C61C72E-B40F-4E72-BF26-6736F39C677A"
},
{
cssPosition:"170,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"170",
width:"140"
},
enabled:true,
labelFor:"lcudf_is_salestax",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.salesTaxField",
visible:false
},
name:"lcudf_is_salestax_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3A023A17-80B3-45B4-B7BB-548ED6FA0281",
visible:false
},
{
cssPosition:"170,-1,-1,150,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"170",
width:"140"
},
dataProviderID:"lcudf_is_salestax",
enabled:true,
onDataChangeMethodID:"B9123658-C012-402E-AA5B-2E136FC48A7B",
styleClass:"checkbox_bts",
tabSeq:0,
text:"",
visible:false
},
name:"lcudf_is_salestax",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"41B6A862-B6E6-4993-9A9E-7A6496C0FB28",
visible:false
},
{
cssPosition:"89,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"89",
width:"140"
},
enabled:true,
labelFor:"lcudf_short_label",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shortName",
visible:true
},
name:"lcudf_short_label_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5C588509-DBDC-456C-A37A-C6DFA29A047A"
},
{
height:480,
partType:5,
typeid:19,
uuid:"6D33A85E-65B4-4BDD-8AEE-2781C2482A44"
},
{
cssPosition:"116,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"116",
width:"140"
},
enabled:true,
labelFor:"lcudf_cost_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.DefaultCostType",
visible:true
},
name:"lcudf_cost_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"76929AF1-017D-40C5-97E8-B9C719B4B74A"
},
{
cssPosition:"5,0,-1,0,1000,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"5",
width:"1000"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.landedCostUDF_DetailView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AD40867B-ACED-4F01-9415-699D74411F10"
},
{
cssPosition:"35,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"35",
width:"140"
},
enabled:true,
labelFor:"lcudf_line",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.UDFNumber",
visible:true
},
name:"lcudf_line_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B3288328-F93D-49FF-87B7-3323196F045B"
},
{
cssPosition:"143,-1,-1,150,220,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"143",
width:"220"
},
dataProviderID:"lcudf_expense_glacct_id",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EFA2D647-9064-4FD4-B4CE-3E7633AE661D",
visible:true
},
name:"lcudf_expense_glacct_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D3384F46-CEAE-455E-84CE-DFDE4619470D"
},
{
cssPosition:"62,-1,-1,150,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"62",
width:"140"
},
dataProviderID:"lcudf_label",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"lcudf_label",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D7CDD872-6A1B-4BB8-8D0E-99CE83BA9BAB"
},
{
cssPosition:"143,-1,-1,5,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"5",
right:"-1",
top:"143",
width:"140"
},
enabled:true,
labelFor:"lcudf_expense_glacct_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.expenseAccount",
visible:true
},
name:"lcudf_expense_glacct_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DCA69B38-4E04-479F-9BFC-579FC3FD09F2"
},
{
cssPosition:"35,-1,-1,150,50,22",
enabled:false,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"35",
width:"50"
},
dataProviderID:"lcudf_line",
editable:false,
enabled:false,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"lcudf_line",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DF8B8F53-7F16-42D5-87EF-12319E3ED0E1"
},
{
cssPosition:"116,-1,-1,150,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"116",
width:"140"
},
dataProviderID:"lcudf_cost_type",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"833B3AF3-B116-4BEC-8C41-87C2442390A3",
visible:true
},
name:"lcudf_cost_type",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F0C576C8-2F49-4D43-B79D-703FB5440862"
}
],
name:"in_landed_cost_udf_dtl",
scrollbars:33,
size:"1000,480",
styleName:"Avanti",
typeid:3,
uuid:"4E18E55B-AD58-4A72-BCFD-A4FA96043F2D"