/**
 * @properties={typeid:35,uuid:"30F2EAD7-7A32-4D65-A17D-5567D5EE2F14",variableType:-4}
 */
var bHitOK = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"41E72BB3-581F-4F48-9E40-B3219156854A",variableType:4}
 */
var _iLastCheckNumAdded = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"713E100E-A2CB-4136-BDFE-C4BB0F02FAD8"}
 */
var _sRenderedItems = '';

/**
 * @properties={typeid:35,uuid:"9D144CEF-939E-44C2-99F6-984968129CF5",variableType:-4}
 */
var _aCheckNames = [];

/**
 * @properties={typeid:35,uuid:"98C2319D-5CE1-4A95-BC89-DC239030EF21",variableType:-4}
 */
var aRenderedJobs = [];

/**
 * @properties={typeid:35,uuid:"6AB913EF-B464-4AB5-B255-7E80B5AC5094",variableType:-4}
 */
var aRenderedSections = [];

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"70B6911D-31E2-4D2A-8D38-B5CA7A623F42"}
 */
function onActionOK(event) {
//	var continueResource = false
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			resetPlaceHolder()
			return false
		}
		
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			resetPlaceHolder()
			return false
		}
		
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			resetPlaceHolder()
			return false
		}
	}
	resetPlaceHolder()
	bHitOK=true
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
//	forms.sf_main_dtl_labour.continueResourceComplete()
//	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"*************-4845-972C-966A40FFDFCC"}
 */
function onActionCancel(event) {
	resetPlaceHolder()
	bHitOK=false
	globals.DIALOGS.closeForm(event);
	return false
}

/**
 * @properties={typeid:24,uuid:"9A0461D1-73BF-40CD-8185-937CC0272FA4"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0
		
	}
}

/**
 * @return {String}
 *
 * @param {JSRecord<db:/avanti/sch_milestone_group>} currentRowRecord
 *
 * @properties={typeid:24,uuid:"CB0B0EC1-3BC4-4DF4-B99D-FA90A52256BA"}
 */
function getNextQACheck(currentRowRecord){
	_iLastCheckNumAdded = 0
	if(currentRowRecord && utils.hasRecords(currentRowRecord.sch_milestone_group_to_sys_cost_centre)) {
		if(_iLastCheckNumAdded  < 1 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled) {
			_iLastCheckNumAdded = 1
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1
		} 
		else if(_iLastCheckNumAdded  < 2 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled) {
			_iLastCheckNumAdded = 2
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2
		} 
		else if(_iLastCheckNumAdded  < 3 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled) {
			_iLastCheckNumAdded = 3
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3
		}		
	} 
	
	return ''
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"FC8051C3-B552-4B85-AE00-0049F319B68C"}
 */
function onRenderCC(event) {
	_iLastCheckNumAdded = 0
	_sRenderedItems = ''
	_aCheckNames = []
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"FF5BEF9E-8254-4D37-BB9E-B6644D909B9A"}
 */
function onRenderCheck1(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true
	
	if(currentRowRecord && !currentRowRecord.qa_check_pos_1){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"8A415495-F51A-47B2-8F39-91DB0800F5DF"}
 */
function onRenderCheck2(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true

	if(currentRowRecord && !currentRowRecord.qa_check_pos_2){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"DDF15FC0-ED0B-45AB-8725-AF14A25ED79B"}
 */
function onRenderCheck3(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()

	check_field.visible = true
	if(currentRowRecord && !currentRowRecord.qa_check_pos_3){
		check_field.visible = false
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @properties={typeid:24,uuid:"D2723704-069C-436C-8BAD-907662DF8001"}
 */
function onRecordSelection(_event, _form) {
	var ret = _super.onRecordSelection(_event, _form)
	application.output(sch_milestone_group_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.ordrevds_description)
	return ret	
}
///**

// * Called before the form component is rendered.

// *

// * @param {JSRenderEvent} event the render event

// *

// * @properties={typeid:24,uuid:"AFA9AE4E-0EF2-4350-A228-7505548CD399"}

// */

//function onRenderJob(event) {

//	if (event.isRecordSelected()) {

//		/***@type {JSRecord<db:/avanti/sch_milestone_group>} */

//		var currentRowRecord = event.getRecord()

//		if(aRenderedJobs.indexOf(currentRowRecord.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_prod_job.job_number) == -1){

//			aRenderedJobs.push(currentRowRecord.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_prod_job.job_number)

//		}

//		else{

//			event.getRenderable().visible = false

//		}

//	}

//}

//

///**

// * Called before the form component is rendered.

// *

// * @param {JSRenderEvent} event the render event

// *

// * @properties={typeid:24,uuid:"5DF90EA1-AA82-4ECB-93E8-11FFB9E0B59D"}

// */

//function onRenderSection(event) {

//	if (event.isRecordSelected()) {

//		/***@type {JSRecord<db:/avanti/sch_milestone_group>} */

//		var currentRowRecord = event.getRecord()

//		var sJob = currentRowRecord.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_prod_job.job_number

//		var sSection = currentRowRecord.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.ordrevds_description

//		

//		if(aRenderedSections.indexOf(sJob + '-' + sSection) == -1){

//			aRenderedSections.push(sJob + '-' + sSection)

//		}

//		else{

//			event.getRenderable().visible = false

//		}

//	}

//}

