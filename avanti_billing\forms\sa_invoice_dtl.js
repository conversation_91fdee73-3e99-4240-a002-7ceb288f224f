/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"579DC2DA-C943-4878-80FC-5F21622571C2",variableType:4}
 */
var _formOnRenderFired = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E78F296A-0B58-4C59-8C47-B59CAAB64B15"}
 */
var _sNameOfTabWIthFocus = "none";

/**
 * @type {Array}
 *
 *
 * @properties={typeid:35,uuid:"0B53993C-8276-4678-80B3-E99BB6B8A4F1",variableType:-4}
 */
var _tabMap = [i18n.getI18NMessage("i18n:avanti.lbl.details"),
    i18n.getI18NMessage("avanti.lbl.comments"),
    i18n.getI18NMessage("avanti.lbl.distributions"),
    i18n.getI18NMessage("avanti.lbl.commissions"),
    i18n.getI18NMessage("avanti.lbl.costCenterAnalysis"),
    i18n.getI18NMessage("avanti.lbl.documents"),
    i18n.getI18NMessage("avanti.lbl.invoices"),
    i18n.getI18NMessage("avanti.lbl.journalEntries"),
    i18n.getI18NMessage("avanti.lbl.billingCodeDetails"),
    i18n.getI18NMessage("avanti.lbl.IntegrationDetails")
    ];

/**
 * Overrides svy_nav_base.js:dc_resetTableViewPersistance() to reset table 
 * column widths and order on the sub-table: sa_invoice_det_tbl too.
 * 
 * <AUTHOR> Meixner
 * @since 2019-05-28
 * 
 * @param {JSEvent} [_event] the event that triggered the action
 * @param {String}  [_triggerForm] (svy_nav_fr_buttonbar_browser/svy_nav_fr_buttonbar_viewer)
 * @global {String} _sNameOfTabWIthFocus - the name of the tab that currently has focus
 * 
 * @return  none
 *
 * @properties={typeid:24,uuid:"24539BFD-B89C-42A0-BB5C-FC6550240742"}
 */
function dc_resetTableViewPersistance(_event, _triggerForm) {
    
    _super.dc_resetTableViewPersistance(_event, _triggerForm);
    
    var sNameOfTabWIthFocus = _sNameOfTabWIthFocus;
    
    switch (sNameOfTabWIthFocus) {
        
        case 'sa_invoice_det_tbl':
            
            // Complex case, extra steps or icons disappear:
            var aInvoiceDetTblSettings = forms.sa_invoice_det_tbl.getIconVisibilityStates();
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_det_tbl");
            forms["sa_invoice_det_tbl"].controller.recreateUI();    
            forms.sa_invoice_det_tbl.refreshUI();
            forms.sa_invoice_det_tbl.setIconVisibilityStates(aInvoiceDetTblSettings);
            break;        
        
        case 'sa_invoice_distribution_dtl':
            
            // Complex case, extra steps or icons disappear:
            var aInvoiceDistributionDtlSettings = forms.sa_invoice_distribution_dtl.getIconVisibilityStates();
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_distribution_dtl");
            forms["sa_invoice_distribution_dtl"].controller.recreateUI();    
            forms.sa_invoice_distribution_dtl.refreshUI();        
            forms.sa_invoice_distribution_dtl.setIconVisibilityStates(aInvoiceDistributionDtlSettings);
            break;
            
        case 'sa_invoice_cost_centre_tbl':
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_cost_centre_tbl");
            forms["sa_invoice_cost_centre_tbl"].controller.recreateUI();
            break;
            
        case 'sa_invoice_commission_tabs':
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_commission_summary_by_order");
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_commission_summary_by_rep");
            dc_clearUserTableViewPersistenceFromDB("sa_invoice_commission_dtl");
            forms["sa_invoice_commission_summary_by_order"].controller.recreateUI();
            forms["sa_invoice_commission_summary_by_rep"].controller.recreateUI();
            forms["sa_invoice_commission_dtl"].controller.recreateUI();
            break;
            
        case 'sys_comment_invoice':
            dc_clearUserTableViewPersistenceFromDB("sys_comment_invoice");
            forms["sys_comment_invoice"].controller.recreateUI();
            break;
            
        default:
            application.output('sa_invoice_dtl.js:dc_resetTableViewPersistance(): ERROR: Received unhandled tab name: '+sNameOfTabWIthFocus);
    }
    
    return;
}

/**
 * Description
 *
 * <AUTHOR> Dol
 * @since Jan 22, 2018
 * @param {JSRecord<db:/avanti/sa_order>} rOrder
 * @public
 *
 * @properties={typeid:24,uuid:"E43EE481-E2EF-4B4D-B787-CD839E073E53"}
 */
function Callback_LoadInvoiceByOrder(rOrder) {
    /**@type {JSRecord<db:/avanti/sa_invoice>} */
    var rInvoice = foundset.getSelectedRecord();
    globals.avInvoice_generateAdvancedBillingInvoice(rOrder, rInvoice);
    databaseManager.saveData();

    if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)) {
        rInvoice.sa_invoice_to_sa_invoice_det.setSelectedIndex(1);
        forms.sa_invoice_det_tbl.getTotals();
    }
    try {
        loadBillingContactValuelist();
        scopes.globals.avInv_selectedInvStatus = inv_status;
        setInvoiceStatusChoicesValueList();
        setTabVisibility();
        refreshUI();
    }
    catch (ex) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.generatingInvoice_error2') + rInvoice.inv_number + ': ' + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
    }

    databaseManager.recalculate(rInvoice); // need this here for total pay/cred to be populated
}

/**
 * Callback_LoadInvoice
 * @param {JSFoundSet<db:/avanti/sa_ship_billing>} fsShipBill The Ship Billing Foundset to copy
 * @param {Boolean} [bDepositSlip]
 * @param {Boolean} [bCompleteInvoice]
 * @param {String} [uDivisionId]
 * @param {String} [uPlantId]
 * 
 * @properties={typeid:24,uuid:"2EF906AE-4D2F-4004-B368-39BB40415650"}
 */
function Callback_LoadInvoice(fsShipBill, bDepositSlip, bCompleteInvoice, uDivisionId, uPlantId) {
    
    var aOrders = [],
        i = 0;

    /**@type {JSRecord<db:/avanti/sa_invoice>} */
    var rInvoice;
    if (bDepositSlip) {
        rInvoice = scopes.avDB.newRecord('sa_invoice');
    }
    else {
        rInvoice = foundset.getSelectedRecord();
    }
    
    if (uDivisionId && uPlantId) {
        rInvoice.div_id = uDivisionId;
        rInvoice.plant_id = uPlantId;
    }

    globals.aProcessedShipDetails = new Array();
    
    for (i = 1; i <= fsShipBill.getSize(); i++) {
        
        var rShipBill = fsShipBill.getRecord(i);

		if (rShipBill.shipbill_selected == 1 || bCompleteInvoice) {
            
            var rShip = rShipBill.sa_ship_billing_to_sa_ship.getRecord(1);
            
            // Reset the B/L markup on tasks depending on pref
            if (aOrders.indexOf(rShip.ordh_id) == -1) {
                scopes.avDetail.setTaskBlMarkupForDetailLines(rShip.sa_ship_to_sa_order.getRecord(1), null, null, true);
            }
            
            globals.avInvoice_generateInvoice(rShipBill, rInvoice);
            databaseManager.saveData();
            
            scopes.avChangeOrders.addChgOrdsForInvoice(rInvoice);
            scopes.avChangeOrders.aChgOrdsDeletedFromInvoice = [];

            if (aOrders.indexOf(rShip.ordh_id) == -1) {
                aOrders.push(rShip.ordh_id);
                copyCommentsFromUpstream(rShip, rInvoice)
            }
           
        }
    }
    
    if (scopes.avAccounting.bGpSendInvoices) {

        scopes.avBilling.updateInvoiceShipLocationTask(rInvoice);

        for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {

            var rInvoiceDet = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);

            if (rInvoiceDet.invd_is_chgord == 1) {
                continue;
            }

            globals.avCalcs_salesTax_setInvoiceDetail(rInvoiceDet);
        }
        globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true);
    }

    if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)) {
        rInvoice.sa_invoice_to_sa_invoice_det.setSelectedIndex(1);
        forms.sa_invoice_det_tbl.getTotals();
    }
    
    try {
        loadBillingContactValuelist();
        scopes.globals.avInv_selectedInvStatus = inv_status;
        setInvoiceStatusChoicesValueList();
        if (globals.nav_program_name) {
        	refreshUI();
            setTabVisibility();
    	}
    }
    catch (ex) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.generatingInvoice_error2') + rInvoice.inv_number + ': ' + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
    }

    globals.aProcessedShipDetails = new Array();
    databaseManager.recalculate(rInvoice); // need this here for total pay/cred to be populated
    
    addMinimumOrderChargeLine(rInvoice);
    //Recalculate Invoice
    globals.avInvoice_recalculateInvoiceLines(rInvoice);
    globals.calculateCommissions(rInvoice);
    if (scopes.avAccounting.bGpSendInvoices) {
        forms["sa_invoice_det_ship_task_tbl"].getTotal(rInvoice);
    }
    avInvoice_createInvoiceDocRecords(rInvoice);
}

/**
 * This function will add the minimum order charge line to the invoice.
 * It will be added for customers that does not have contract invoicing.
 * @param {JSRecord<db:/avanti/sa_invoice>} invoiceRec
 *
 * @properties={typeid:24,uuid:"0828C22F-9269-4AB1-8ED4-42570A38CBEE"}
 */
function addMinimumOrderChargeLine(invoiceRec) {

	if (!invoiceRec || 
			invoiceRec.inv_type != scopes.avUtils.INVOICE_TYPE.Standard ||
			globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.MinOrdChargeBehaviour) != "MINORD" || 
			(utils.hasRecords(invoiceRec.sa_invoice_to_sa_customer) && 
					utils.hasRecords(invoiceRec.sa_invoice_to_sa_customer.sa_customer_to_sa_contracts$active)) || 
			(utils.hasRecords(invoiceRec.sa_invoice_to_sa_customer.sa_customer_to_sa_customer$parent) && 
					utils.hasRecords(invoiceRec.sa_invoice_to_sa_customer.sa_customer_to_sa_customer$parent.sa_customer_to_sa_contracts$active))) {
		return;
	}

	if (utils.hasRecords(invoiceRec.sa_invoice_to_sa_invoice_det)) {
		invoiceRec.sa_invoice_to_sa_invoice_det.sort("sequence_nr asc");

		// If the sales order is being billed as well, then we need to add the minimum order charge on the invoice
		var minimumOrderChargeItemId = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.ContractMinimumChargeServiceItem);

		var iSeq = 0;

		/**@type {JSRecord<db:/avanti/sa_order_revision_header>}*/
		var rOrderRev;

		/**@type {JSRecord<db:/avanti/sa_order>}*/
		var rOrder;

		/***@type {{aOrders:Array,
		 * 		    aSequence:Array}}*/
		var oOrders = getListOfOrders(invoiceRec);

		/**@type {Array}*/
		var aOrders = oOrders.aOrders;

		/**@type {Array} */
		var aSequence = oOrders.aSequence;

		for (var i = aOrders.length - 1; i >= 0; i--) {

			rOrder = aOrders[i];
			iSeq = aSequence[i];

			if (utils.hasRecords(rOrder.sa_order_to_sa_order_revision_header$first_rev)) {
				rOrderRev = rOrder.sa_order_to_sa_order_revision_header$first_rev.getRecord(1);
				var reversedInvoiceDetIds = [];
				var invDetIds = [];
				//Check if minimum order charge line for the order has already been added to the invoice
				for (var c = 1; c <= rOrder.sa_order_to_sa_invoice_det$invd_is_min_order_charge.getSize(); c++) {
					var rInvDet = rOrder.sa_order_to_sa_invoice_det$invd_is_min_order_charge.getRecord(c);
					
					//If a credit note for reversed invoice is found then ignore it and add invd_id of the reversed invoice
					if (rInvDet.sa_invoice_det_to_sa_invoice.inv_record_type == scopes.avUtils.INVOICE_RECORD_TYPE.CreditNote) {
						reversedInvoiceDetIds.push(rInvDet.credit_invd_id);
						continue;
					}
					
					invDetIds.push(rInvDet.invd_id);
				}
				
				//Go through each invoice ids and remove it from the list of invoice detail ids so both credit note and the reversed invoice detail can be ignored.
				for (var j = 0; j < reversedInvoiceDetIds.length; j++) {
					var index = invDetIds.indexOf(reversedInvoiceDetIds[j]);
					if (index > -1) {
						invDetIds.splice(index,1);
					}
				}

				//If no outstanding invoice detail ids left then create the minimum order charge line. 
				// else we know that the an invoice already has the minimum order charge applied. we don't want to double add the charge line.
				if (invDetIds.length == 0 && minimumOrderChargeItemId && rOrderRev.ordrevh_min_ord_charge > 0) {
					forms.sa_contract_batch_selections.CreateAdditionalChargeLine(invoiceRec, null, minimumOrderChargeItemId, rOrderRev.ordrevh_min_ord_charge, rOrder.ordh_id, iSeq, true);
				}
			}
		}
	}

	//re-sequence line numbers
	/**@type {JSFoundSet<db:/avanti/sa_invoice_det>}*/
	var fsInvoiceDetail = invoiceRec.sa_invoice_to_sa_invoice_det;
	fsInvoiceDetail.sort("sequence_nr asc");
	iSeq = 1;
	for (j = 1; j <= fsInvoiceDetail.getSize(); j++) {
		var rInvoiceDetail = fsInvoiceDetail.getRecord(j);
		rInvoiceDetail.sequence_nr = iSeq;
		iSeq += 1;
	}
}

/**
 * TODO generated, please specify type and doc for the params
 * @param rInvoice
 *
 * @return
 * @properties={typeid:24,uuid:"9FE3E05E-E683-4F59-BACD-82600A6E4FC9"}
 */
function getListOfOrders(rInvoice) {

	/***@type {{aOrders:Array,
	 * 		    aSequence:Array}}*/
	var oObject = new Object();

	var aOrders = new Array();
	var aSequence = new Array();

	if (rInvoice) {
		/***@type {{sql:String,
		 * 		 args:Array, server:String,
		 * 		 maxRows:Number,
		 * 		 table:String}}*/
		var oSQL = { };

		/**@type {JSDataSet} */
		var dsData;

		oSQL.sql = "SELECT sa_order.ordh_document_num, MAX(sa_invoice_det.sequence_nr) AS sequence_nr \
	     			 FROM sa_order \
	     			 INNER JOIN sa_invoice_det ON dbo.sa_order.ordh_id = dbo.sa_invoice_det.ordh_id \
	     			 WHERE (sa_invoice_det.org_id = ?) AND (sa_invoice_det.inv_id = ?) AND (sa_order.ordh_document_type = 'ORD')\
	     			 GROUP BY sa_order.ordh_document_num";
		oSQL.args = [rInvoice.org_id.toString(), rInvoice.inv_id.toString()];

		dsData = globals["avUtilities_sqlDataset"](oSQL);

		if (dsData && dsData.getMaxRowIndex() > 0) {
			for (var i = 1; i <= dsData.getMaxRowIndex(); i++) {
				var sOrder = dsData.getValue(i, 1);
				var iSeq = dsData.getValue(i, 2);
				var rOrder = scopes.avDB.getRec("sa_order", ["ordh_document_num", "ordh_document_type"], [sOrder]);

				if (rOrder) {
					aOrders.push(rOrder);
					aSequence.push(iSeq);
				}
			}
		}
	}

	oObject.aOrders = aOrders;
	oObject.aSequence = aSequence;

	return oObject;
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_header>} rOrdRev
 *
 * @return
 * @properties={typeid:24,uuid:"E3D3649F-6B09-4111-B3A5-39880CAEF3F2"}
 */
function createDepositSlip(rOrdRev){
	/**@type {JSRecord<db:/avanti/sa_invoice>} */
	var rInvoice = scopes.avDB.newRecord('sa_invoice');
	var nMax = rOrdRev.sa_order_revision_header_to_sa_order_revh_deposit.getSize();
	rOrdRev.sa_order_revision_header_to_sa_order_revh_deposit.sort('ordrevhdep_date_added asc');
	var sRevID = globals.UUIDtoStringNew(rOrdRev.ordh_id);
	var rOrd = rOrdRev.sa_order_revision_header_to_sa_order.getRecord(1);
	
	rInvoice.inv_number = globals.GetNextDocumentNumber("INV","0");
	rInvoice.inv_docstream_type = "INV";
	rInvoice.inv_status = 'O';
	rInvoice.inv_date = application.getTimeStamp();
	rInvoice.inv_is_deposit_slip = 1;
	rInvoice.created_date = inv_date;
	rInvoice.created_by_id = globals.avBase_employeeUUID;
	rInvoice.curr_id = rOrdRev.sa_order_revision_header_to_sa_order.curr_id;
	rInvoice.inv_cust_id = rOrdRev.sa_order_revision_header_to_sa_order.cust_id;
	rInvoice.inv_billto_custaddr_id = rOrd.ordh_billto_custaddr_id;
	rInvoice.inv_shipto_custaddr_id = rOrd.ordh_shipto_custaddr_id;
	rInvoice.inv_shipto_ordaddr_id = rOrd.sa_order_to_sa_order_address_shipto.ordaddr_id;
	rInvoice.div_id = rOrd.div_id;
	rInvoice.plant_id = rOrd.plant_id;
	rInvoice.inv_type = scopes.avUtils.INVOICE_TYPE.Standard;
	rInvoice.inv_record_type = 'I';
	
	if (rOrdRev.ordrevh_exchange_rate) {
		rInvoice.inv_exchange_rate = rOrdRev.ordrevh_exchange_rate;
	}
	else {
		rInvoice.inv_exchange_rate = rInvoice.sa_invoice_to_sys_currency.sys_currency_to_sys_currency_rate.currrate_rate;
	}
	
	rInvoice.inv_exchange_rate_original = rInvoice.sa_invoice_to_sys_currency.sys_currency_to_sys_currency_rate.currrate_rate;
	rInvoice.inv_form_detail_level = rInvoice.sa_invoice_to_sa_customer.cust_invoice_detail_level;
	if (rInvoice.inv_form_detail_level == null || rInvoice.inv_form_detail_level == "") rInvoice.inv_form_detail_level = 'L';
	
	//Create new address record
	rInvoice.inv_addr_id = globals.getNewAddressID();
	rInvoice.inv_address_name = rOrd.sa_order_to_sa_order_address_billto.ordaddr_name;
	rInvoice.sa_invoice_to_sys_address.addr_address1 = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.addr_address1;
	rInvoice.sa_invoice_to_sys_address.addr_address2 = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.addr_address2;
	rInvoice.sa_invoice_to_sys_address.addr_address3 = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.addr_address3;
	rInvoice.sa_invoice_to_sys_address.addr_city = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.addr_city;
	rInvoice.sa_invoice_to_sys_address.stateprov_id = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.stateprov_id
	rInvoice.sa_invoice_to_sys_address.addr_postal = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.addr_postal;
	rInvoice.sa_invoice_to_sys_address.country_id = rOrd.sa_order_to_sa_order_address_billto.sa_order_address_to_sys_address.country_id;
	
	if (rInvoice.custcontact_id == null){ // Set the invoice contact
		   if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order) &&
		   utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order.sa_order_to_sa_order_address_billto)){
			   rInvoice.custcontact_id = rInvoice.sa_invoice_to_sa_invoice_det.sa_invoice_det_to_sa_order.sa_order_to_sa_order_address_billto.custcontact_id;
		   }
	}
	
	databaseManager.saveData(rInvoice);	
	foundset.removeFoundSetFilterParam('InvoiceStatusFilter');
	foundset.loadRecords(rInvoice.inv_id);
	rOrdRev.sa_order_revision_header_to_sa_order_revh_deposit.sort('ordrevhdep_date_added asc');
	
	for(var i=1; i<=nMax; i++){
		var rDep = rOrdRev.sa_order_revision_header_to_sa_order_revh_deposit.getRecord(i);

		if(!rDep.inv_id && rDep.ordrevhdep_hide_invoice_msg != 1){
			forms.sa_invoice_det_tbl.createDepositLine(sRevID, rDep.ordrevhdep_amount);
			rDep.sa_order_revh_deposit_to_sa_invoice_tender_trans.inv_id = rInvoice.inv_id;
			databaseManager.saveData(rDep.sa_order_revh_deposit_to_sa_invoice_tender_trans);
		}
	}
	
	if (utils.hasRecords(rInvoice.sa_invoice_to_sa_invoice_det)){
		rInvoice.sa_invoice_to_sa_invoice_det.setSelectedIndex(1);
		forms.sa_invoice_det_tbl.getTotals();
	}

	loadBillingContactValuelist();
	refreshUI();
	
	return rInvoice.inv_id;
}

/**
 * @param {JSRecord<db:/avanti/sa_ship>} rShip
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 *
 * @properties={typeid:24,uuid:"FFFB24A7-9E2D-4223-ACE4-7B6EC4FE0525"}
 */
function copyCommentsFromUpstream(rShip, rInvoice) {
    /***@type {JSRecord<db:/avanti/sys_comment>} */
    var rComment;
    /***@type {JSRecord<db:/avanti/sys_comment>} */
    var rInvComment;
    var c = 0;
    var nSeqNum = 0;
    /***@type {JSRecord<db:/avanti/sa_order>} */
    var rOrder = rShip.sa_ship_to_sa_order.getRecord(1);
    var rOrderRev = rOrder.sa_order_to_sa_order_revision_header$first_rev.getRecord(1);

    // order comments
    if (utils.hasRecords(rOrder.sa_order_to_sys_comment$invoice)) {
        rOrder.sa_order_to_sys_comment$invoice.sort('sequence_nr asc');
        
        for (c = 1; c <= rOrder.sa_order_to_sys_comment$invoice.getSize(); c++) {
            rComment = rOrder.sa_order_to_sys_comment$invoice.getRecord(c);
            addComment();
        }
    }

    // line items
    if (utils.hasRecords(rOrderRev.sa_order_revision_header_to_sa_order_revision_detail$is_deleted)) {
        rOrderRev.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.sort('sequence_nr asc');
        
        for (var d = 1; d <= rOrderRev.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getSize(); d++) {
            var rLineItem = rOrderRev.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getRecord(d);
            
            if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted)) {
                rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.sort('sequence_nr asc');
                
                for (var s = 1; s <= rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.getSize(); s++) {
                    var rSection = rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.getRecord(s);
                    
                    // section comments
                    if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sys_comment$invoice)) {
                        rSection.sa_order_revision_detail_section_to_sys_comment$invoice.sort('sequence_nr asc');
                        
                        for (c = 1; c <= rSection.sa_order_revision_detail_section_to_sys_comment$invoice.getSize(); c++) {
                            rComment = rSection.sa_order_revision_detail_section_to_sys_comment$invoice.getRecord(c);
                            addComment();
                        }
                    }
                    
                    if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted)) {
                        rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.sort('sort_key asc');
                        
                        for (var t = 1; t <= rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getSize(); t++) {
                            var rTask = rSection.sa_order_revision_detail_section_to_sa_order_revds_task$is_deleted.getRecord(t);
                            
                            // task comments
                            if (utils.hasRecords(rTask.sa_order_revds_task_to_sys_comment$invoice)) {
                                rTask.sa_order_revds_task_to_sys_comment$invoice.sort('sequence_nr asc');
                                
                                for (c = 1; c <= rTask.sa_order_revds_task_to_sys_comment$invoice.getSize(); c++) {
                                    rComment = rTask.sa_order_revds_task_to_sys_comment$invoice.getRecord(c);
                                    addComment();
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    function addComment() {
        rInvComment = rInvoice.sa_invoice_to_sys_comment.getRecord(rInvoice.sa_invoice_to_sys_comment.newRecord(false, true));
        rInvComment.comment_desc = rComment.comment_desc;
        rInvComment.inv_id = rInvoice.inv_id;
        rInvComment.sequence_nr = ++nSeqNum;
    }
}

/** *
 * @param _firstShow
 * @param {JSEvent} _event
 *
 * @properties={typeid:24,uuid:"746DFA26-403E-4BDF-AF1C-90770FB15201"}
 */
function onShowForm(_firstShow, _event) {
	scopes.avBilling.createMissingShipBillingRecs();
	
    _super.onShowForm(_firstShow, _event);

    //Form Logo
    if (scopes.avLogo.orgHasPlantLogos(null)) {
        elements.btnSetPlantLogo.visible = true;
    }
    else {
        elements.btnSetPlantLogo.visible = false;
    }
    
    if (_uDepositInvID != null) {
        try {
            foundset.removeFoundSetFilterParam('InvoiceStatusFilter');
            foundset.removeFoundSetFilterParam('InvoicePaidFilter');
            foundset.loadRecords(_uDepositInvID);
            _nOnShowFormHits = 0;
            _uDepositInvID = null;
            dc_edit(null, "svy_nav_fr_buttonbar_browser");
        }
        catch (e) {
            application.output('Could not load Invoice', LOGGINGLEVEL.ERROR);
        }
    }

    scopes.avTax.sAvalaraAPIKey = _to_sys_organization.org_avalara_api_key;
    if (scopes.avTax.sAvalaraAPIKey) {
        elements.btnRefreshTax.visible = true;
    }
    else {
        elements.btnRefreshTax.visible = false;
    }
    
    _bHasRightToEditPrintedFinalInvoiceToOpen = globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_open_invoice', globals.avBase_employeeUserID);
    _bHasRightToEditPrintedFinalInvoiceToProForma = globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_pro_forma_invoice', globals.avBase_employeeUserID);
    
    _bWorkatoIntegration = globals.isWorkatoIntegration();
    _bWorkatoUseInvoiceRegister = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.WorkatoUseInvoiceRegister);
    
    refreshUI();
    setToolBarOptions();
    setTabVisibility();
    setInvoiceStatusChoicesValueList();
    loadBillingContactValuelist();
    if (globals.nav.mode == "browse") {
        forms.sa_invoice_det_tbl.foundset.sort("sequence_nr asc");
    }

    databaseManager.recalculate(foundset.getSelectedRecord()); // to recalc inv_total_payments_credits
}

/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"BF285D14-B8D6-4A1B-B729-472716FC49EE"}
 */
function onRecordSelection(_event, _form) {
	_formOnRenderFired = 0;
	
	 _super.onRecordSelection(_event, _form)

	if (globals.nav.mode == "browse")
	{
		forms.sa_invoice_det_tbl.foundset.sort("sequence_nr asc");
	}
	
 	setToolBarOptions();
 	setTabVisibility();
	loadBillingContactValuelist();
	scopes.globals.avInv_selectedInvStatus = inv_status;
	setInvoiceStatusChoicesValueList();
	
	// RG 2014-05-09 SL-2467 updated to refresh UI on record select to allow change in currency format
	refreshUI() ;
	forms.sa_invoice_distribution_dtl.verifyDistribution();
	forms.sa_invoice_det_ship_task_tbl.getTotal(foundset.getSelectedRecord());
}

/**
 * Find commissions tab in the system
 * @return {Number} Returns tabCommission tab index
 * @properties={typeid:24,uuid:"91EF1383-6410-4EF7-B00A-6E5BE9FEF3D7"}
 */
function findCommissionsTabIndex () {
	var iMaxTab = scopes.globals.avUtilities_tabGetMaxTabIndex(controller.getName(), "tabs_235");
    for (var i = 1; i <= iMaxTab; i++) {
        if (scopes.globals.avUtilities_tabGetName(controller.getName(), "tabs_235", i) == "tabCommission") {
            return i;
        }
    }
    return null;
}

/**
 * Refreshes the UI
 *
 * <AUTHOR> Galibov
 * @since 2014-05-09
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"4C5FE82A-4136-4C00-9E43-869F3C68E5DB"}
 */
function refreshUI()
{
	setEditingStateSaInvoiceDtl();
	elements.inv_date.format = globals.avBase_dateFormat;
	elements.inv_due_date.format = globals.avBase_dateFormat;
	
	elements.inv_subtotal_amt.format = globals.avBase_currencyFormat_invoice;
	elements.inv_postage_amount.format = globals.avBase_currencyFormat_invoice;
	elements.inv_discount_amt.format = globals.avBase_currencyFormat_invoice;
	elements.inv_freight_amt.format = globals.avBase_currencyFormat_invoice;
	elements.inv_salestax_amt.format = globals.avBase_currencyFormat_invoice;
	elements.inv_total_amt.format = globals.avBase_currencyFormat_invoice;
	elements.deposit_and_payment.format = globals.avBase_currencyFormat_invoice;
	elements.inv_balance_owing.format = globals.avBase_currencyFormat_invoice;
	elements.inv_total_advance_billing.format = globals.avBase_currencyFormat_invoice;
	
	//Advance Billing
    elements.inv_total_advance_billing.visible = false;
    elements.btnAdvancedBillings.visible = false;
    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.EnableAdvancedBilling) == 1) {
        elements.inv_total_advance_billing.visible = inv_type != scopes.avUtils.INVOICE_TYPE.AdvanceBilling;
        elements.btnAdvancedBillings.visible = inv_type != scopes.avUtils.INVOICE_TYPE.AdvanceBilling;
    }
	
	if(globals.nav && globals.nav.form_view_01 && (globals.nav.form_view_01 == "sa_customer_crm_tbl" ||
	   globals.nav.form_view_01 == "crm_customer_address_tbl" ||
	   globals.nav.form_view_01 == "crm_job_view_tbl")){
		elements.lblHeader.text = i18n.getI18NMessage('avanti.lbl.invoiceDetailView');
	} else {
		elements.lblHeader.text = i18n.getI18NMessage('avanti.lbl.invoiceEntryDetailView');
	}
	
	 // RG 2014-05-08 SL-2467 updated to refresh UI in sub forms on record select to allow change in currency format
	forms.sa_invoice_distribution_dtl.refreshUI();
	forms.sa_invoice_det_tbl.refreshUI();
	forms.sa_invoice_distribution_dtl.refreshUI();
	
	var nCommissionsTabIndex = findCommissionsTabIndex();
	var bCanShowCommissionDetail = globals.avSecurity_checkForUserRight('Invoice_Entry', 'invoiceShowCommissionDetails', globals.avBase_employeeUserID);
	if (!bCanShowCommissionDetail) {
		if (nCommissionsTabIndex) {
			scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "tabs_235", nCommissionsTabIndex);
		}
	}
	else {
		// if no commissions tab shown
		if (!nCommissionsTabIndex) {
			scopes.globals.avUtilities_tabAdd(controller.getName(), "tabs_235", forms.sa_invoice_commission_tabs, i18n.getI18NMessage("i18n:avanti.lbl.commissions"), _to_sa_invoice, null, "tabCommission", 3, null);
		}
		forms.sa_invoice_commission_tabs.refreshUI();
	}
	
    forms.sa_invoice_docs_tbl.refreshUI();
	
	if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration) == "QWC") {
		elements.taxtype_code.visible = true;
	} else {
		elements.taxtype_code.visible = false;
	}
	
	if ((inv_status == "U" || inv_status == "P") && utils.hasRecords(sa_invoice_to_sa_invoice_register)) {
		elements.invreg_number.visible = true;
	}
	else{
		elements.invreg_number.visible = false;
	}
	
	if (globals.avSecurity_checkForUserRight("Invoice_Entry", "set_invoice_ready_in_detail_view", globals.avBase_employeeUserID)) {
		elements.inv_ready.enabled = true;
	} else {
		elements.inv_ready.enabled = false;
	}
	
	//Advance Billing Invoice for a project plan
    if (inv_type == scopes.avUtils.INVOICE_TYPE.AdvanceBilling 
            && utils.hasRecords(sa_invoice_to_sa_order$advancedbilling) 
            && sa_invoice_to_sa_order$advancedbilling.ordh_document_type == scopes.avUtils.DOCUMENT_TYPE.ProjectPlan) {
        forms.sa_invoice_det_tbl._sOrderNumberLabel = i18n.getI18NMessage("avanti.lbl.projectPlan");
        forms.sa_invoice_det_tbl.elements.grid.getColumn(forms.sa_invoice_det_tbl.elements.grid.getColumnIndex("job_number")).visible = false;
        forms.sa_invoice_det_tbl.elements.grid.getColumn(forms.sa_invoice_det_tbl.elements.grid.getColumnIndex("projectOrderNumber")).visible = true;
        
    }
    else {
        forms.sa_invoice_det_tbl._sOrderNumberLabel = i18n.getI18NMessage("avanti.lbl.salesOrder");
        forms.sa_invoice_det_tbl.elements.grid.getColumn(forms.sa_invoice_det_tbl.elements.grid.getColumnIndex("job_number")).visible = true;
        forms.sa_invoice_det_tbl.elements.grid.getColumn(forms.sa_invoice_det_tbl.elements.grid.getColumnIndex("projectOrderNumber")).visible = false;
    }
    
    elements.lblLockedMessage.visible = (inv_batch_invoice_lock ? true: false);
	elements.lblShield.visible = false;
}

/**
 *
 * @param {JSEvent} _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"38C98823-8852-4D32-8DFF-481EF0536346"}
 */
function dc_save(_event, _triggerForm) {
	
	var rInv;
	
	// This function can be called from headless client (Example: cybersource call for import order from XML)
	if (globals.nav_program_name) {
		_super.dc_save(_event, _triggerForm);
		
		rInv = foundset.getSelectedRecord();
	}
	else {
		
		rInv = foundset.getSelectedRecord();
		if (rInv) {
			if (rInv.inv_record_type == 'I') {
				forms.sa_invoice_dtl.recalculateDistributions();
			} else if (rInv.inv_record_type == 'C') {
				forms.sa_credit_invoice_dtl.recalculateDistributions();
			}
		}
		databaseManager.saveData(foundset);
	}
	
	if (rInv) {
		rInv.inv_committed_amount = inv_deposits_amt + inv_payments_amt;
		databaseManager.saveData(rInv);
		if(rInv.inv_record_type === "I"){
		    globals.sendMessageToTriggersAlerts('invoicing', rInv.inv_id.toString(), null);
		}
	}
	
	scopes.avBilling.bInvoiceEdit = false;
	
	forms.sa_invoice_docs_tbl.saveDocuments();
	
	scopes.avChangeOrders.aChgOrdsDeletedFromInvoice = [];
	scopes.avChangeOrders.markChgOrdsAsInvoiced(rInv);
	
	// SL-12020 need to update edit/delete buttons
	setToolBarOptions();
	setEditingStateSaInvoiceDtl()
	
	// only for servoy web client and not for headless client (import order from xml)
	if (globals.nav_program_name) {
		refreshDetailView();
	}
}

/**
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @properties={typeid:24,uuid:"5C496EBA-A7C1-4B1E-9628-F10DFC4FB5B2"}
 */
function dc_edit(_event, _triggerForm) {
	if (inv_historical) {
		scopes.avText.showWarning('cantEditHistoricalInvoice');
		return;
	}
	
	if (inv_batch_invoice_lock) {
		scopes.avText.showWarning("batchInvoicingLock");
		return;
	}
	
	// This function can be called from headless client (Example: cybersource call for import order from XML)
	if (globals.nav_program_name) {
		_super.dc_edit(_event, _triggerForm);
	}
	
	setCustIdAccess();
	
	setEditingStateSaInvoiceDtl()
	
	// only for servoy web client and not for headless client (import order from xml)
	if (globals.nav_program_name) {
		refreshDetailView();
	}
	
	if (scopes.globals.avUtilities_tabGetName(controller.getName(), "tabs_235", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_235")) == "tabBillingCodeDetails") {
		scopes.globals.avUtilities_tabSelect(controller.getName(),'tabs_235', 'tabDetails');
	}
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"29F1DAAF-35C0-4D14-BB1D-76C6AD6F46E4"}
 */
function onDataChange_customer(oldValue, newValue, event) {

    if (utils.hasRecords(foundset.sa_invoice_to_sa_customer)) {
        inv_billto_custaddr_id = sa_invoice_to_sa_customer.cust_billto_custaddr_id;
        onDataChange_invBillToCustAddr(null, inv_billto_custaddr_id, event);
        curr_id = sa_invoice_to_sa_customer.curr_id;
        terms_id = sa_invoice_to_sa_customer.terms_id;
        inv_form_detail_level = sa_invoice_to_sa_customer.cust_invoice_detail_level;
        
        refreshUI();
    }
    else {
        inv_billto_custaddr_id = null;
    }
    
    return true;
}

/**
 * Set the valuelist avInvoice_InvoiceStatusChoices
 * 
 * <AUTHOR>
 * @properties={typeid:24,uuid:"61B0A57C-B28D-4686-9363-496C3747BC84"}
 */
function setInvoiceStatusChoicesValueList() {
    var vlRealValues = new Array();
    var vlDisplayValues = new Array();
    
    switch (inv_status) {
        case "O":
            vlRealValues.push("O");
            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.open"));
            if (globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_pro_forma_invoice', globals.avBase_employeeUserID)) {
                vlRealValues.push("PR");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedProForma"));
            }

            if (globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_printed_final_invoice', globals.avBase_employeeUserID)) {
                vlRealValues.push("P");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedFinal"));
            }
            
            if (!_bWorkatoUseInvoiceRegister && _bWorkatoIntegration) {
            	vlRealValues.push("RP");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.readyToPost"));
            }
            
            break;
        case "P":
            vlRealValues.push("P");
            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedFinal"));
            
            if (_bHasRightToEditPrintedFinalInvoiceToOpen) {
                vlRealValues.push("O");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.open"));
            }
            
            if (_bHasRightToEditPrintedFinalInvoiceToProForma) {
                vlRealValues.push("PR");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedProForma"));
            }
            
            if (!_bWorkatoUseInvoiceRegister && _bWorkatoIntegration) {
            	vlRealValues.push("RP");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.readyToPost"));
            }

            break;
        case "PR":
            if (globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_open_invoice', globals.avBase_employeeUserID)) {
                vlRealValues.push("O");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.open"));
            }
            
            vlRealValues.push("PR");
            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedProForma"));
            
            if (globals.avSecurity_checkForUserRight(globals.nav_program_name, 'set_invoice_status_printed_final_invoice', globals.avBase_employeeUserID)) {
                vlRealValues.push("P");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedFinal"));
            }
            
            if (!_bWorkatoUseInvoiceRegister && _bWorkatoIntegration) {
            	vlRealValues.push("RP");
                vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.readyToPost"));
            }
            break;
        case "U":
            vlRealValues.push("U")
            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.Posted"));
            break;
        case "RP":
        	
        	vlRealValues.push("RP");
    		vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.readyToPost"));
        
	        vlRealValues.push("P");
	        vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedFinal"));
        
	        if (_bHasRightToEditPrintedFinalInvoiceToOpen) {
	            vlRealValues.push("O");
	            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.open"));
	        }
	        
	        if (_bHasRightToEditPrintedFinalInvoiceToProForma) {
	            vlRealValues.push("PR");
	            vlDisplayValues.push(i18n.getI18NMessage("avanti.lbl.printedProForma"));
	        }

        break;
    }
    
    if (vlRealValues.length > 0) {
        application.setValueListItems('avInvoice_InvoiceStatusChoices', vlDisplayValues, vlRealValues);
    }
    else {
        application.setValueListItems('avInvoice_InvoiceStatusChoices', [], []);
    }
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"E4BA7C24-1964-4675-8B31-30302E9CC2BD"}
 */
function onDataChange_inv_status(oldValue, newValue, event) {
    
    if (globals.nav.mode == "required_fields") {
        return true;
    }
    
    if (inv_subtotal_amt < 0 && hasNegativeProductionLine(foundset.getSelectedRecord())) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.notification"), i18n.getI18NMessage("avanti.dialog.negativeInvoiceSubTotal"), i18n.getI18NMessage("avanti.dialog.ok"));
        inv_status = oldValue;
        return false;
    }
    else if (inv_total_amt < 0) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.notification"), i18n.getI18NMessage("avanti.dialog.negativeInvoiceTotal"), i18n.getI18NMessage("avanti.dialog.ok"));
        inv_status = oldValue;
        return false;
    }
    else if (_bWorkatoIntegration 
            && (inv_status == scopes.avUtils.INVOICE_STATUS.ReadyToPost || inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal)
            && !forms.sa_invoice_distribution_dtl.verifyDistribution()) {
        globals.DIALOGS["showErrorDialog"](
            i18n.getI18NMessage("avanti.dialog.notification"), 
            i18n.getI18NMessage("avanti.dialog.invoiceDistributionOutOfBalance_msg"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        inv_status = oldValue;
        forms.sa_invoice_dtl.elements.tabs_235.tabIndex = 'tabDistributions';
        return false;
    }
    else if (_bWorkatoIntegration
            && (inv_status == scopes.avUtils.INVOICE_STATUS.ReadyToPost || inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal)
            && !scopes.avAccounting.hasAllDistributionAccounts(foundset.getSelectedRecord())) {
        globals.DIALOGS["showErrorDialog"](
            i18n.getI18NMessage("avanti.dialog.notification"), 
            i18n.getI18NMessage("avanti.dialog.invoiceDistributionMissingAccounts_msg"), 
            i18n.getI18NMessage("avanti.dialog.ok"));
        inv_status = oldValue;
        forms.sa_invoice_dtl.elements.tabs_235.tabIndex = 'tabDistributions';
        return false;
    }
    else {
        setInvoiceStatusChoicesValueList();
        
		if (!_bWorkatoUseInvoiceRegister 
				&& _bWorkatoIntegration 
				&& inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal && oldValue !=scopes.avUtils.INVOICE_STATUS.ReadyToPost) {
			var sResponse = globals.DIALOGS["showQuestionDialog"](i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.invoicePrintedFinalToReadyToPost_msg'),
				i18n.getI18NMessage('avanti.dialog.yes'),
				i18n.getI18NMessage('avanti.dialog.no'));

			if (sResponse == i18n.getI18NMessage('avanti.dialog.yes')) {
				inv_status = scopes.avUtils.INVOICE_STATUS.ReadyToPost;
			}
		}
        
        if ((inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal) || (inv_status == scopes.avUtils.INVOICE_STATUS.PrintProForma) || (inv_status == scopes.avUtils.INVOICE_STATUS.ReadyToPost)) {
        	if (!utils.hasRecords(sa_invoice_to_sa_invoice_distribution)) {
        		globals.bRecalculateDistributions = true;
        	}
        	
            recalculateDistributions();
    	}
        
    	// Clear the integration fields if changing the status
        if (oldValue == scopes.avUtils.INVOICE_STATUS.ReadyToPost || oldValue == scopes.avUtils.INVOICE_STATUS.PrintFinal ) {
            inv_ws_posted = null;
            inv_ws_message = null;
        }
        
        if (newValue == scopes.avUtils.INVOICE_STATUS.PrintFinal 
                && foundset.getSelectedRecord().inv_type != scopes.avUtils.INVOICE_TYPE.AdvanceBilling 
                && _to_sys_organization.org_avalara_calc_tax_inv_final || _to_sys_organization.org_avalara_calc_tax_inv_posted) {
            var returnObject = globals.sendInvoiceToAvaTax(foundset.getSelectedRecord(), oldValue);
            if (returnObject.refreshStatus) {
                setInvoiceStatusChoicesValueList();
            }
        }
    }
    
    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit || (scopes.avUtils.isNavModeReadOnly() && (_bHasRightToEditPrintedFinalInvoiceToOpen || _bHasRightToEditPrintedFinalInvoiceToProForma))) {

        //Confirm Changes
        sResponse = globals.DIALOGS.showQuestionDialog(
            i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
            i18n.getI18NMessage('avanti.dialog.invoiceChangeStatus'),
            i18n.getI18NMessage('avanti.dialog.yes'),
            i18n.getI18NMessage('avanti.dialog.no'));
            
        if (sResponse == i18n.getI18NMessage('avanti.dialog.yes')) {
            
            //If moving an invoice from printed final, make sure we remove it from any register
            //and if the register has been reviewed, reset to force register to be rerun again prior to posting.
            if (oldValue == scopes.avUtils.INVOICE_STATUS.PrintFinal && (newValue == scopes.avUtils.INVOICE_STATUS.Open || newValue == scopes.avUtils.INVOICE_STATUS.PrintProForma)) {
                inv_sent_to_avatax = 0;
            	globals.updateJobInvoiceStatus(sa_invoice_to_sa_invoice_det);
                if (utils.hasRecords(sa_invoice_to_sa_invoice_register) && sa_invoice_to_sa_invoice_register.invreg_date_updated == null) {
                    sa_invoice_to_sa_invoice_register.invreg_date_reviewed = null;
                    sa_invoice_to_sa_invoice_register.invreg_reviewed_by_id = null;
                    databaseManager.saveData(sa_invoice_to_sa_invoice_register);
                    
                    invreg_id = null;
                }
            }
            
            databaseManager.saveData(foundset.getSelectedRecord());
            setToolBarOptions();
            setTabVisibility();
        }
        else {
            inv_status = oldValue;
        }
    }
    
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"24849429-D6E1-477E-8559-3808FE1196A7"}
 */
function onAction_refreshAvalaraTax(event) {
    
    var rInvoice = foundset.getSelectedRecord(),
        nOriginalTax = rInvoice.inv_salestax_amt;
        
    scopes.avTax.sAvalaraAPIKey = _to_sys_organization.org_avalara_api_key;
    
    if (application.isInDeveloper() 
            && scopes.avTax.sAvalaraAPIKey) {
        scopes.avTax.calculateTaxForInvoiceAvalara(rInvoice, false);
    }
    else if (globals.nav.mode != 'edit' && globals.nav.mode != 'add') {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
    }
    else if (inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal && !Boolean(_to_sys_organization.org_avalara_calc_tax_inv_posted)) {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.statusMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
    }
    else {
        var sResponse = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.recalculateAvalara'), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
        if (sResponse == i18n.getI18NMessage('avanti.dialog.yes')
                && scopes.avTax.sAvalaraAPIKey) {
            scopes.avTax.calculateTaxForInvoiceAvalara(rInvoice, false);
        }
    }

	if (nOriginalTax != rInvoice.inv_salestax_amt) {
        scopes.avTax.initTaxRateCache();
        globals.avInvoice_recalulateInvoiceTotal(rInvoice, null, null, null, null, true);
    }
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
* @param {String} _answer
*
 * @return
* @properties={typeid:24,uuid:"BCED4B28-3C33-4668-B591-334C35EE8FBA"}
*/
function dc_cancel(_event, _triggerForm, _answer) {
	var bReturn;
	
	if ((inv_status == scopes.avUtils.INVOICE_STATUS.PrintFinal) || (inv_status == scopes.avUtils.INVOICE_STATUS.PrintProForma)) {
		bReturn = dc_save(_event, _triggerForm);
	}
	else {
		bReturn = _super.dc_cancel(_event, _triggerForm, _answer);
		scopes.avBilling.bInvoiceEdit = false;
		setInvoiceStatusChoicesValueList();
		setToolBarOptions();
		
		scopes.avChangeOrders.aChgOrdsDeletedFromInvoice = [];
	}
	
	// only for servoy web client and not for headless client (import order from xml)
	if (globals.nav_program_name) {
		refreshDetailView();
	}
	
	setEditingStateSaInvoiceDtl()

	return bReturn;
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"8AB81E9C-7E89-4EC9-A35C-654B4AD8A15A"}
 */
function onDataChange_inv_date(oldValue, newValue, event) {
	var bResult = true;
	
	//Check if the new date is within a closed period.
	if(scopes.avAccounting.isInvoicingPeriodClosedForDate(newValue,org_id)){
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'),i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'),i18n.getI18NMessage('avanti.dialog.ok'));
		inv_date = oldValue;
		bResult = false;
	}

	return bResult;
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"C3C0B1D1-BC2F-45F2-AC2B-F599DA455B70"}
 */
function onRender(event) {
	
	if (!_formOnRenderFired ) {
		
		_formOnRenderFired = 1;
		
		scopes.globals.avInv_selectedInvStatus = inv_status;
		setToolBarOptions();
	}
	
}

/**
 * Create Multiple Invoices from sales order
 *
 * @param {JSFoundSet<db:/avanti/sa_ship_billing>} fsShipBill The Ship Billing;
 * 
 * @return {JSRecord<db:/avanti/sa_invoice>} rInvoice
 *
 * @properties={typeid:24,uuid:"D15ABCFA-B765-46A0-9835-D2411FF99427"}
 */
function generateInvoicesFromSalesOrder(fsShipBill) {
	
	var aOrders = new Array();
	
	/**@type {JSRecord<db:/avanti/sa_invoice>} */
	var rInvoice = scopes.avDB.newRecord('sa_invoice');
	rInvoice.inv_number = globals.GetNextDocumentNumber("INV","0");
	rInvoice.inv_date = new Date();
	rInvoice.inv_type = scopes.avUtils.INVOICE_TYPE.Standard;
	
	for (var i = 1; i <= fsShipBill.getSize(); i++) 
	{
		var rShipBill = fsShipBill.getRecord(i);

		if (rShipBill.shipbill_selected == 1)
		{
		    globals.avInvoice_generateInvoice(rShipBill, rInvoice);
			databaseManager.saveData();
			
			var rShip = rShipBill.sa_ship_billing_to_sa_ship.getRecord(1);
			if (aOrders.indexOf(rShip.ordh_id) == -1){
				aOrders.push(rShip.ordh_id);
				copyCommentsFromUpstream(rShip,rInvoice);
				databaseManager.saveData();
			}
		}
	}
	globals.avInvoice_recalculateInvoiceLines(rInvoice,true);
	globals.calculateCommissions(rInvoice, true);
	
	return rInvoice;
	
}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"06F5CBB4-DAF0-42D7-9EC1-3F72972C50CB"}
 */
function onHide(event) {
	
	_formOnRenderFired = 0;
	
	return _super.onHide(event)
}

/**
 * Pro-Rate edited sales tax amount downstream
 *
 * <AUTHOR> Dol
 * @since Oct 18, 2017
 * 
 * @param {JSRecord<db:/avanti/sa_invoice_tax_detail>} rinvoiceTaxDetailEdited - Edited Invoice Tax Detail Record
 * 
 * @public
 *
 *
 * @properties={typeid:24,uuid:"6558791E-CCB1-4253-A2E3-FE5295409FC2"}
 */
function proRateInvoiceSalesTax(rinvoiceTaxDetailEdited) {
	
	if (!rinvoiceTaxDetailEdited){
		return;
	}
	
	/**@type {JSRecord<db:/avanti/sa_invoice>} */
	var rInvoice = rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice.getRecord(1);
	
	var rInvoiceDetail,
	rInvoiceTaxDetail,
	rInvoiceDetailShip,
	rInvoiceFreight,
	rInvoiceFreightTaxDetail;
	
	/**@type {Number} */
	var i,j,k,l
	
	if (rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig == null){ //Make sure all overrides are cleared and then recalculate tax
		
		for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++ ) {
			rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);
			
			for (j = 1; j <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getSize(); j++ ) {
				rInvoiceTaxDetail = rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getRecord(j);
				
				if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetail.taxitem_id){
					rInvoiceTaxDetail.invtaxdet_tax_amt_orig = null;
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = null;
				}
			}
				
			for (k = 1; k <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); k++ ) {
				rInvoiceDetailShip = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(k);
				
				for (l = 1; l <= rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getSize(); l++ ) {
					var rInvoiceTaxDetailShip = rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getRecord(l);
					
					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetailShip.taxitem_id){
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig = null;
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_over = null;
					}
				}
			}
		}
		
		//Freight
		for ( i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++ ) {
			rInvoiceFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);
			
			for (j = 1; j <= rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getSize(); j++ ) {
				rInvoiceFreightTaxDetail = rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getRecord(j);
				
				if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceFreightTaxDetail.taxitem_id){
					rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig = null;
					rInvoiceFreightTaxDetail.invtaxdet_tax_amt_over = null;
				}
			}
		}
		
		globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true);
	}
	else{ //pro-rate the tax
	
		var nAllocatedToDetails = 0.00, nAllocatedToShipment = 0.00, nAllocatedToFreight = 0.00;
		var rHighestInvoiceTaxDetail, rHighestInvoiceTaxDetailShip, rHighestInvoiceFreightTaxDetail;
		
		for ( i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++ ) {
			rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);
			
			for ( j = 1; j <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getSize(); j++ ) {
				rInvoiceTaxDetail = rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getRecord(j);
				
				if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetail.taxitem_id){
					if (rInvoiceTaxDetail.invtaxdet_tax_amt_orig == null){
						rInvoiceTaxDetail.invtaxdet_tax_amt_orig = rInvoiceTaxDetail.invtaxdet_tax_amt;
					}
					
					rInvoiceTaxDetail.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoiceTaxDetail.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt,2); 
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = rInvoiceTaxDetail.invtaxdet_tax_amt;
					nAllocatedToDetails += rInvoiceTaxDetail.invtaxdet_tax_amt;
					
					if (rHighestInvoiceTaxDetail == null || rInvoiceTaxDetail.invtaxdet_tax_amt > rHighestInvoiceTaxDetail.invtaxdet_tax_amt){
						rHighestInvoiceTaxDetail = rInvoiceTaxDetail;
					}
				}
			}
					
			for (k = 1; k <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); k++ ) {
				rInvoiceDetailShip = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(k);
				
				for (l = 1; l <= rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getSize(); l++ ) {
					rInvoiceTaxDetailShip = rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getRecord(l);
					
					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetailShip.taxitem_id){
						if (rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig == null){
							rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig = rInvoiceTaxDetailShip.invtaxdet_tax_amt;
						}
						
						rInvoiceTaxDetailShip.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt, 2); 
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_over = rInvoiceTaxDetailShip.invtaxdet_tax_amt;
						nAllocatedToShipment += rInvoiceTaxDetailShip.invtaxdet_tax_amt;
						
						if (rHighestInvoiceTaxDetailShip == null || rInvoiceTaxDetailShip.invtaxdet_tax_amt > rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt){
							rHighestInvoiceTaxDetailShip = rInvoiceTaxDetailShip;
						}
					}
				}
			}
		}
		
		//Freight Tax
		for ( i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++ ) {
			rInvoiceFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);
			
			for (j = 1; j <= rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getSize(); j++ ) {
				rInvoiceFreightTaxDetail = rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getRecord(j);
				
				if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceFreightTaxDetail.taxitem_id){
					if (rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig == null){
						rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig = rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
					}
					
					rInvoiceFreightTaxDetail.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt,2); 
					rInvoiceFreightTaxDetail.invtaxdet_tax_amt_over = rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
					nAllocatedToFreight += rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
					
					if (rHighestInvoiceFreightTaxDetail == null || rInvoiceFreightTaxDetail.invtaxdet_tax_amt > rHighestInvoiceFreightTaxDetail.invtaxdet_tax_amt){
						rHighestInvoiceFreightTaxDetail = rInvoiceFreightTaxDetail;
					}
				}
			}
		}
		
		//Handle Rounding Issues
		var nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - (nAllocatedToDetails + nAllocatedToFreight);
		if (nDiff != 0 && rHighestInvoiceTaxDetail){
			rHighestInvoiceTaxDetail.invtaxdet_tax_amt += nDiff;
			rHighestInvoiceTaxDetail.invtaxdet_tax_amt_over = rHighestInvoiceTaxDetail.invtaxdet_tax_amt;
		}
		
		nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - (nAllocatedToShipment + nAllocatedToFreight);
		if (nDiff != 0 && rHighestInvoiceTaxDetailShip){
			rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt += nDiff;
			rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt_over = rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt;
		}
		
		// GD - Jun 1, 2022: SL-23854: Changes to tax or freight not updating distributions/total correctly with billing codes on
        if (scopes.avAccounting.bGpSendInvoices) {
          globals.avInvoice_recalulateInvoiceTotal(rInvoice);
          globals.bRecalculateDistributions = false;
        }
        else {
            globals.avInvoice_recalulateInvoiceTax(rInvoice);
        }
		
	}
}

/**
 * Pro-Rate the detail line sales taxes
 * @param {JSRecord<db:/avanti/sa_invoice_tax_detail>} rinvoiceTaxDetailEdited - Edited Invoice Tax Detail Record
 * @public 
 *
 * @properties={typeid:24,uuid:"84870D4E-7BB5-46B9-93B0-4094320F1CA2"}
 */
function proRateInvoiceSalesTaxLineDetails(rinvoiceTaxDetailEdited) {
	if (!rinvoiceTaxDetailEdited) {
		return;
	}
	
	if (utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_det) 
			&& utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_det.sa_invoice_det_to_sa_invoice)) {
		
		/**@type {JSRecord<db:/avanti/sa_invoice>} */
		var rInvoice = rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_det.sa_invoice_det_to_sa_invoice.getRecord(1);
		
		/**@type {JSRecord<db:/avanti/sa_invoice_det>} */
		var rInvoiceDetail = rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_det.getRecord(1);
		
		var rInvoiceDetailShip;
		
		/**@type {Number} */
		var i,
			k,
			l,
			nDiff;
		
		var nAllocatedToDetails = 0.00,
		nAllocatedToShipment = 0.00;
	
	
		/**@type {JSRecord<db:/avanti/sa_invoice_tax_detail>} */
		var rInvoiceTaxDetail,
		rHighestInvoiceTaxDetail, 
		rHighestInvoiceTaxDetailShip;
		
		if (rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig == null) { //Make sure all overrides are cleared and then recalculate tax

			for (k = 1; k <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); k++) {
				rInvoiceDetailShip = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(k);

				for (l = 1; l <= rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getSize(); l++) {
					var rInvoiceTaxDetailShip = rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getRecord(l);

					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetailShip.taxitem_id) {
						rInvoiceTaxDetailShip.invtaxdet_tax_amt = rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig;
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig = null;
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_over = null;
					}
				}
			}

			//Invoice
			for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); i++) {
				rInvoiceTaxDetail = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(i);
				if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetail.taxitem_id) {
					rInvoiceTaxDetail.invtaxdet_tax_amt = rInvoiceTaxDetail.invtaxdet_tax_amt_orig;
					rInvoiceTaxDetail.invtaxdet_tax_amt_orig = null;
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = null;
				}
			}

			globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true);
			
		}
		else { //pro-rate the tax
		
			for (k = 1; k <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getSize(); k++) {
				rInvoiceDetailShip = rInvoiceDetail.sa_invoice_det_to_sa_invoice_det_ship.getRecord(k);

				for (l = 1; l <= rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getSize(); l++) {
					rInvoiceTaxDetailShip = rInvoiceDetailShip.sa_invoice_det_ship_to_sa_invoice_tax_detail.getRecord(l);

					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceTaxDetailShip.taxitem_id) {
						if (rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig == null) {
							rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig = rInvoiceTaxDetailShip.invtaxdet_tax_amt;
						}

						rInvoiceTaxDetailShip.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoiceTaxDetailShip.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt, 2);
						rInvoiceTaxDetailShip.invtaxdet_tax_amt_over = rInvoiceTaxDetailShip.invtaxdet_tax_amt;
						nAllocatedToShipment += rInvoiceTaxDetailShip.invtaxdet_tax_amt;

						if (rHighestInvoiceTaxDetailShip == null || rInvoiceTaxDetailShip.invtaxdet_tax_amt > rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt) {
							rHighestInvoiceTaxDetailShip = rInvoiceTaxDetailShip;
						}
					}
				}
			}
			
						
			//Handle Rounding Issues
			nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - nAllocatedToDetails;
			if (nDiff != 0 && rHighestInvoiceTaxDetail) {
				rHighestInvoiceTaxDetail.invtaxdet_tax_amt += nDiff;
				rHighestInvoiceTaxDetail.invtaxdet_tax_amt_over = rHighestInvoiceTaxDetail.invtaxdet_tax_amt;
			}

			nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - nAllocatedToShipment;
			if (nDiff != 0 && rHighestInvoiceTaxDetailShip) {
				rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt += nDiff;
				rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt_over = rHighestInvoiceTaxDetailShip.invtaxdet_tax_amt;
			}
			
			recalculateInvoiceTaxDetails(rInvoice);
			
			// GD - Jun 1, 2022: SL-23854: Changes to tax or freight not updating distributions/total correctly with billing codes on
            if (scopes.avAccounting.bGpSendInvoices) {
              globals.avInvoice_recalulateInvoiceTotal(rInvoice);
              globals.bRecalculateDistributions = false;
            }
            else {
                globals.avInvoice_recalulateInvoiceTax(rInvoice);
            }
			
		}
	}
}

/**
 * ProRate Invoice Freight Tax
 * 
  * @param {JSRecord<db:/avanti/sa_invoice_tax_detail>} rinvoiceTaxDetailEdited - Edited Freight Tax Detail Record
 *
 * @properties={typeid:24,uuid:"A5499CA5-3A2A-44A6-80D2-6AD0F9DC080E"}
 */
function proRateInvoiceSalesTaxFreight(rinvoiceTaxDetailEdited) {
	if (!rinvoiceTaxDetailEdited) {
		return;
	}
	
	if (utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_freight) 
			&& utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_freight.sa_invoice_freight_to_sa_invoice)) {
		
		/**@type {JSRecord<db:/avanti/sa_invoice>} */
		var rInvoice = rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_freight.sa_invoice_freight_to_sa_invoice.getRecord(1);
		
		var rInvoiceFreight,
		rInvoiceFreightTaxDetail;
		
		/**@type {Number} */
		var i,
			j,
			nDiff;
		
		var nAllocatedToFreight = 0.00;
	
		/**@type {JSRecord<db:/avanti/sa_invoice_tax_detail>} */
		var	rHighestInvoiceFreightTaxDetail;
		
		if (rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig == null) { //Make sure all overrides are cleared and then recalculate tax

			//Freight
			for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++) {
				rInvoiceFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);

				for (j = 1; j <= rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getSize(); j++) {
					rInvoiceFreightTaxDetail = rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getRecord(j);

					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceFreightTaxDetail.taxitem_id) {
						rInvoiceFreightTaxDetail.invtaxdet_tax_amt = rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig;
						rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig = null;
						rInvoiceFreightTaxDetail.invtaxdet_tax_amt_over = null;
					}
				}
			}

			globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true);
		}
		else { //pro-rate the tax
		
			//Freight Tax
			for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++) {
				rInvoiceFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);

				for (j = 1; j <= rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getSize(); j++) {
					rInvoiceFreightTaxDetail = rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getRecord(j);

					if (rinvoiceTaxDetailEdited.taxitem_id == rInvoiceFreightTaxDetail.taxitem_id) {
						if (rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig == null) {
							rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig = rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
						}

						rInvoiceFreightTaxDetail.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt, 2);
						rInvoiceFreightTaxDetail.invtaxdet_tax_amt_over = rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
						nAllocatedToFreight += rInvoiceFreightTaxDetail.invtaxdet_tax_amt;

						if (rHighestInvoiceFreightTaxDetail == null || rInvoiceFreightTaxDetail.invtaxdet_tax_amt > rHighestInvoiceFreightTaxDetail.invtaxdet_tax_amt) {
							rHighestInvoiceFreightTaxDetail = rInvoiceFreightTaxDetail;
						}
					}
				}
			}
			
			//Handle Rounding Issues
			nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - nAllocatedToFreight;
			if (nDiff != 0 && rHighestInvoiceFreightTaxDetail) {
				rHighestInvoiceFreightTaxDetail.invtaxdet_tax_amt += nDiff;
				rHighestInvoiceFreightTaxDetail.invtaxdet_tax_amt_over = rHighestInvoiceFreightTaxDetail.invtaxdet_tax_amt;
			}

			recalculateInvoiceTaxDetails(rInvoice);
			
			// GD - Jun 1, 2022: SL-23854: Changes to tax or freight not updating distributions/total correctly with billing codes on
		    if (scopes.avAccounting.bGpSendInvoices) {
		      globals.avInvoice_recalulateInvoiceTotal(rInvoice);
		      globals.bRecalculateDistributions = false;
		    }
		    else {
		        globals.avInvoice_recalulateInvoiceTax(rInvoice);
		    }
		}
	}
}

/**
 * ProRate Invoice Postage Tax
 * 
 * @param {JSRecord<db:/avanti/sa_invoice_tax_detail>} rinvoiceTaxDetailEdited - Edited Freight Tax Detail Record
 *
 * @properties={typeid:24,uuid:"3F52D1F5-359E-45CD-AD86-195C1366E3A0"}
 */
function proRateInvoiceSalesTaxPostage(rinvoiceTaxDetailEdited) {
	if (!rinvoiceTaxDetailEdited) {
		return;
	}
	
	if (utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_postage) 
			&& utils.hasRecords(rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_postage.sa_invoice_postage_to_sa_invoice)) {
		
		/**@type {JSRecord<db:/avanti/sa_invoice>} */
		var rInvoice = rinvoiceTaxDetailEdited.sa_invoice_tax_detail_to_sa_invoice_postage.sa_invoice_postage_to_sa_invoice.getRecord(1);
		
		var rInvoicePostage,
		rInvoicePostageTaxDetail;
		
		/**@type {Number} */
		var i,
			j,
			nDiff;
		
		var nAllocatedToPostage = 0.00;
	
		/**@type {JSRecord<db:/avanti/sa_invoice_tax_detail>} */
		var	rHighestInvoicePostageTaxDetail;
		
		if (rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig == null) { //Make sure all overrides are cleared and then recalculate tax

			//Postage
			for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_postage.getSize(); i++) {
				rInvoicePostage = rInvoice.sa_invoice_to_sa_invoice_postage.getRecord(i);

				for (j = 1; j <= rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getSize(); j++) {
					rInvoicePostageTaxDetail = rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getRecord(j);

					if (rinvoiceTaxDetailEdited.invpost_id == rInvoicePostageTaxDetail.invpost_id 
							&& rinvoiceTaxDetailEdited.taxitem_id == rInvoicePostageTaxDetail.taxitem_id) {
						rInvoicePostageTaxDetail.invtaxdet_tax_amt = rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig;
						rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig = null;
						rInvoicePostageTaxDetail.invtaxdet_tax_amt_over = null;
					}
				}
			}

			globals.avInvoice_recalculateInvoiceLines(rInvoice, null, null, true);
		}
		else { //pro-rate the tax
		
			//Postage Tax
			for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_postage.getSize(); i++) {
				rInvoicePostage = rInvoice.sa_invoice_to_sa_invoice_postage.getRecord(i);

				for (j = 1; j <= rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getSize(); j++) {
					rInvoicePostageTaxDetail = rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getRecord(j);

					if (rinvoiceTaxDetailEdited == rInvoicePostageTaxDetail.invpost_id && rinvoiceTaxDetailEdited.taxitem_id == rInvoicePostageTaxDetail.taxitem_id) {
						if (rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig == null) {
							rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig = rInvoicePostageTaxDetail.invtaxdet_tax_amt;
						}

						rInvoicePostageTaxDetail.invtaxdet_tax_amt = scopes.avUtils.roundNumber(rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig / rinvoiceTaxDetailEdited.invtaxdet_tax_amt_orig * rinvoiceTaxDetailEdited.invtaxdet_tax_amt, 2);
						rInvoicePostageTaxDetail.invtaxdet_tax_amt_over = rInvoicePostageTaxDetail.invtaxdet_tax_amt;
						nAllocatedToPostage += rInvoicePostageTaxDetail.invtaxdet_tax_amt;

						if (rHighestInvoicePostageTaxDetail == null || rHighestInvoicePostageTaxDetail.invtaxdet_tax_amt > rHighestInvoicePostageTaxDetail.invtaxdet_tax_amt) {
							rHighestInvoicePostageTaxDetail = rInvoicePostageTaxDetail;
						}
					}
				}
			}
			
			//Handle Rounding Issues
			nDiff = rinvoiceTaxDetailEdited.invtaxdet_tax_amt - nAllocatedToPostage;
			if (nDiff != 0 && rHighestInvoicePostageTaxDetail) {
				rHighestInvoicePostageTaxDetail.invtaxdet_tax_amt += nDiff;
				rHighestInvoicePostageTaxDetail.invtaxdet_tax_amt_over = rHighestInvoicePostageTaxDetail.invtaxdet_tax_amt;
			}
		}
		
		recalculateInvoiceTaxDetails(rInvoice);
		globals.avInvoice_recalulateInvoiceTax(rInvoice);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"321F2599-28C0-4AA0-82C6-39223D6D220B"}
 */
function onAction_btnAdvancedBillings(event) {
    forms.sa_invoice_advance_billing_dialog.showAdvancedBillings(foundset.getSelectedRecord());
}

/**
 * Hide/show tabs
 *
 * <AUTHOR> Dol
 * @since Feb 22, 2018
 * @private
 *
 * @properties={typeid:24,uuid:"974D5DF4-B4ED-4B1A-9DD5-4636880B08C0"}
 */
function setTabVisibility() {
    if (inv_type != scopes.avUtils.INVOICE_TYPE.AdvanceBilling && inv_credit_type != scopes.avUtils.CREDIT_NOTE_TYPE.AdvanceBilling) {
        globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_cost_centre_tbl", "avanti.lbl.costCenterAnalysis", "sa_invoice_to_sa_invoice_cost_centre_summary", _tabMap);
    }
    else {
        globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.costCenterAnalysis");
    }
    
    globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.invoices");
    if (utils.hasRecords(sa_invoice_to_sa_customer_project$project_plan)) {
        var fsUnlinkedProjectPlanInvoices = scopes.avBilling.GetInvoicesLinkedToProjectPlan(ordh_id);

        if (fsUnlinkedProjectPlanInvoices.getSize() > 0) {
            forms.sa_invoice_project_plan_invoices_tbl.controller.loadRecords(fsUnlinkedProjectPlanInvoices);
            globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_project_plan_invoices_tbl", "avanti.lbl.invoices", null, _tabMap);
        }
    }
    
    var gpSettings = scopes.avAccounting.getGPSettings(globals.org_id);
    if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.RedistributeRevenueAndCostsUsingJournaEntry) != scopes.avUtils.ENUM_INVOICE_REALLOCATION_METHOD.NotApplicable || (gpSettings.dynamics_gp_thomas_journal_entry || gpSettings.dynamics_gp_tension_journal_entry)) {
        globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_journal_entry_tbl", "avanti.lbl.journalEntries", "sa_invoice_to_sa_invoice_journal_entry", _tabMap);
    }
    else {
        globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.journalEntries");
    }
    
    if (scopes.avUtils.trackCurrencyExchange(curr_id)
            && globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration) == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.MicrosoftDynamicsGP) {
    	globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_distribution_exchange_dtl", "avanti.lbl.distributionsExchange", "sa_invoice_to_sa_invoice_distribution", _tabMap);
    	globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_distribution_originating_dtl", "avanti.lbl.originatingDistributions", "sa_invoice_to_sa_invoice_distribution", _tabMap);
    }
    else {
    	globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.distributionsExchange");
    	globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.originatingDistributions");
    }
    
    if (Boolean(globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseBillingCodesInvoiceCreditNotes)) 
            || (Boolean(gpSettings.dynamics_gp_send_invoices) && globals.avBase_invoiceGPFeatureTokenEnabled)) {
        globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_det_ship_task_tbl", "avanti.lbl.billingCodeDetails", "sa_invoice_to_sa_invoice_det_ship_task", _tabMap);
    }
    else {
        globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.billingCodeDetails");
    }
    
    if (!_bWorkatoUseInvoiceRegister 
    		&& _bWorkatoIntegration
    		&& (inv_status == scopes.avUtils.INVOICE_STATUS.ReadyToPost || inv_status == scopes.avUtils.INVOICE_STATUS.Updated)) {
        globals.avUtilities_tabAdd("sa_invoice_dtl", "tabs_235", "sa_invoice_integration_dtl", "avanti.lbl.IntegrationDetails", "_to_sa_invoice", _tabMap);
    }
    else {
        globals.avUtilities_tabRemove("sa_invoice_dtl", "tabs_235", "avanti.lbl.IntegrationDetails");
    }
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0FB1B329-1920-4FE5-A781-6F917C30511C"}
 */
function onDataChange_invType(oldValue, newValue, event) {
    
    if (isValidInvoiceType(oldValue, newValue)) {
        //Must rewrite sa_invoice_det_ship
        globals.avInvoice_resetInvoiceShipmentDetails(foundset.getSelectedRecord());
        globals.avInvoice_updateAdvanceBillings(foundset.getSelectedRecord());
        globals.avInvoice_recalulateInvoiceTotal(foundset.getSelectedRecord());
        globals.avInvoice_recalculateInvoiceLines(foundset.getSelectedRecord());
        setTabVisibility();
        refreshUI();
        return true;
    }
    else {
        inv_type = oldValue;
        return false;
    }
}

/**
 * validate invoice type
 *
 * <AUTHOR> Dol
 * @since Mar 7, 2018
 * 
 * @param {String} oldValue
 * @param {String} newValue

 * @private
 *
 * @return
 * @properties={typeid:24,uuid:"90532C83-2021-4A49-834F-CFBCF981C70E"}
 */
function isValidInvoiceType(oldValue, newValue) {
    var bIsValid = true;
    var bHasAdditionalCharges = false;
    var aOrders = new Array();

    for (var i = 1; i <= sa_invoice_to_sa_invoice_det.getSize(); i++) {
        var rInvoiceDetail = sa_invoice_to_sa_invoice_det.getRecord(i);

        if (rInvoiceDetail.ordh_id && aOrders.indexOf(rInvoiceDetail.ordh_id) == -1) {
            aOrders.push(rInvoiceDetail.ordh_id);
            ordh_id = rInvoiceDetail.ordh_id;
        }
        
        //Any additional charge lines
        if (rInvoiceDetail.ship_id == null || rInvoiceDetail.ordrevd_id == null){
            bHasAdditionalCharges = true;
        }
    }

    if (inv_type == scopes.avUtils.INVOICE_TYPE.AdvanceBilling) {
        //If changing to an advance bill type, there can only be one sales order referenced.
        if (aOrders.length > 1) {
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.notification'),
                i18n.getI18NMessage('avanti.dialog.invalidInvoiceType_AdvanceBilling_msg'),
                i18n.getI18NMessage("avanti.dialog.ok"));
            bIsValid = false;
            ordh_id = null;
        }
        else if (bHasAdditionalCharges) {
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.notification'),
                i18n.getI18NMessage('avanti.dialog.invalidInvoiceType_AdvanceBillingAdditionalCharges_msg'),
                i18n.getI18NMessage("avanti.dialog.ok"));
            bIsValid = false;
            ordh_id = null;
        }
    }
    else {
        if (oldValue == scopes.avUtils.INVOICE_TYPE.AdvanceBilling && newValue == scopes.avUtils.INVOICE_TYPE.Standard && inv_created_from_open_order == 1) {
            globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.notification'),
                i18n.getI18NMessage('avanti.dialog.invalidInvoiceType_StandardInvoice_msg'),
                i18n.getI18NMessage("avanti.dialog.ok"));
            bIsValid = false;
        }
        else{
            ordh_id = null; // This is only for advance billings
        }
    }

    return bIsValid;
}

/**
 * Handle focus gained event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"705C8CCB-C203-4B43-AD3E-AC1C14CF14A1"}
 */
function onFocusGained_invDate(event) {
    application.output("Focus is on date field", LOGGINGLEVEL.DEBUG);
}

/**
 * call back when order selection dialog is closed without selecting an order to invoice
 *
 * @param {JSEvent} event
 * 
 * @public
 *
 * @properties={typeid:24,uuid:"98178133-C3AF-4B5B-89D8-1610CF2F743D"}
 */
function Callback_ClosedDialog(event) {
    if (!utils.hasRecords(foundset.sa_invoice_to_sa_invoice_det)) {
        globals.svy_nav_dc_onClick(event, 'dc_cancel', 'svy_nav_fr_buttonbar_browser');
    }
}

/**
 * Recalculate Invoice Tax Details
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice - The Invoice Record
 *
 * @properties={typeid:24,uuid:"50178BDE-1061-4B06-B5BA-B00055496AFE"}
 */
function recalculateInvoiceTaxDetails(rInvoice) {
	
	if (!rInvoice){
		return;
	}
	
	/**@type {Number} */
	var i,
		j,
		k;
	
	//Reset Invoice Tax to zero
	for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); i++) {
		var rInvoiceTaxDetail = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(i);
		rInvoiceTaxDetail.invtaxdet_tax_amt = 0;
		rInvoiceTaxDetail.invtaxdet_tax_amt_orig = null;
		rInvoiceTaxDetail.invtaxdet_tax_amt_over = null;
	}
	
	//Total Invoice Tax
	for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_det.getSize(); i++) {
		var rInvoiceDetail = rInvoice.sa_invoice_to_sa_invoice_det.getRecord(i);

		for (j = 1; j <= rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getSize(); j++) {
			var rInvoiceDetailTaxDetail = rInvoiceDetail.sa_invoice_det_to_sa_invoice_tax_detail.getRecord(j);

			for (k = 1; k <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); k++) {
				rInvoiceTaxDetail = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(k);

				if (rInvoiceDetailTaxDetail.taxitem_id == rInvoiceTaxDetail.taxitem_id) {
					rInvoiceTaxDetail.invtaxdet_tax_amt += rInvoiceDetailTaxDetail.invtaxdet_tax_amt;
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = rInvoiceTaxDetail.invtaxdet_tax_amt;

					if (rInvoiceDetailTaxDetail.invtaxdet_tax_amt_orig != null) {
						rInvoiceTaxDetail.invtaxdet_tax_amt_orig += rInvoiceDetailTaxDetail.invtaxdet_tax_amt_orig;
					}

					break;
				}
			}
		}
	}
	
	//Add Postage Tax
	for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_postage.getSize(); i++) {
		var rInvoicePostage = rInvoice.sa_invoice_to_sa_invoice_postage.getRecord(i);

		for (j = 1; j <= rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getSize(); j++) {
			var rInvoicePostageTaxDetail = rInvoicePostage.sa_invoice_postage_to_sa_invoice_tax_detail.getRecord(j);

			for (k = 1; k <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); k++) {
				rInvoiceTaxDetail = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(k);

				if (rInvoicePostageTaxDetail.taxitem_id == rInvoiceTaxDetail.taxitem_id) {
					rInvoiceTaxDetail.invtaxdet_tax_amt += rInvoicePostageTaxDetail.invtaxdet_tax_amt;
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = rInvoiceTaxDetail.invtaxdet_tax_amt;

					if (rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig != null) {
						rInvoiceTaxDetail.invtaxdet_tax_amt_orig += rInvoicePostageTaxDetail.invtaxdet_tax_amt_orig;
					}

					break;
				}
			}
		}
	}
	
	//Add Freight Tax
	for (i = 1; i <= rInvoice.sa_invoice_to_sa_invoice_freight.getSize(); i++) {
		var rInvoiceFreight = rInvoice.sa_invoice_to_sa_invoice_freight.getRecord(i);

		for (j = 1; j <= rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getSize(); j++) {
			var rInvoiceFreightTaxDetail = rInvoiceFreight.sa_invoice_freight_to_sa_invoice_tax_detail.getRecord(j);

			for (k = 1; k <= rInvoice.sa_invoice_to_sa_invoice_tax_detail.getSize(); k++) {
				rInvoiceTaxDetail = rInvoice.sa_invoice_to_sa_invoice_tax_detail.getRecord(k);

				if (rInvoiceFreightTaxDetail.taxitem_id == rInvoiceTaxDetail.taxitem_id) {
					rInvoiceTaxDetail.invtaxdet_tax_amt += rInvoiceFreightTaxDetail.invtaxdet_tax_amt;
					rInvoiceTaxDetail.invtaxdet_tax_amt_over = rInvoiceTaxDetail.invtaxdet_tax_amt;

					if (rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig != null) {
						rInvoiceTaxDetail.invtaxdet_tax_amt_orig += rInvoiceFreightTaxDetail.invtaxdet_tax_amt_orig;
					}

					break;
				}
			}
		}
	}
}

/**
 * Callback method when the user changes tab in a tab panel or divider position in split pane.
 *
 * @param {Number} previousIndex index of tab shown before the change
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"6C12143B-DBDD-4409-B1C1-7838D73669BE"}
 */
function onTabChange(previousIndex, event) {
    switch (scopes.globals.avUtilities_tabGetName(controller.getName(), "tabs_235", scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_235"))) {
        case 'tabCostCenterAnalysis':
            recalculateDistributions();
            break;
        case 'tabDistributions':
            recalculateDistributions();
            break;
        case 'tabBillingCodeDetails':
            forms.sa_invoice_det_ship_task_tbl.showDialog(foundset.getSelectedRecord());
            break;
        default:
            break;
    }
}

/**
 * @properties={typeid:24,uuid:"2FBB94BA-F152-4A94-A38E-7DC939A7B57D"}
 */
function recalculateDistributions() {
    
    var rInvoice = foundset.getSelectedRecord();
    
    // GD - Dec 14, 2019: SL-17572 - If the invoice has manual distribution adjustments, then do not recalc
    // User must go to the distributions and refresh them to remove them and recalc
    if (!scopes.avBilling.checkManualDistributionOverrides(rInvoice) 
            && globals.bRecalculateDistributions) {
                
        globals.avBase_bInvoiceUpdateRunning = true;
        elements.lblShield.visible = true;
        elements.lblShield.text = i18n.getI18NMessage("avanti.lbl.recalculatingDistributionsAndCostCenters");
        globals.updateUI();
        globals.avInvoice_recalculateInvoiceLines(rInvoice);
        globals.calculateCommissions(rInvoice);
        globals.avBase_bInvoiceUpdateRunning = false;
        forms.sa_invoice_distribution_dtl.verifyDistribution();
        if (globals.nav_program_name) {
        	refreshUI();
    	}
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D30154F5-0F01-4AD3-910E-F0C60815F33A"}
 */
function onAction_SetPlantLogo(event) {

	if (scopes.avUtils.isNavModeReadOnly()) {
	    return;
	}

    forms.sys_plant_logo_dialog.showPlantLogoSelection(globals.svy_nav_form_name, null, inv_logo_plant_id, inv_logo_type);
}

/**
 * Callback to set the plant logo
 *
 * <AUTHOR> Dol
 * @since Nov 7, 2019
 * @param {String} sPlant_id
 * @private
 *
 * @properties={typeid:24,uuid:"40FFC92B-3A9A-4B97-AE67-CDDCDD7EB409"}
 */
function Callback_SelectPlantLogo(sPlant_id) {

    if (sPlant_id) {
        inv_logo_plant_id = sPlant_id;
    }
    else {
        inv_logo_plant_id = null;
    }

}

/**
 * 
 * @param {String} sLogoType
 *
 * 
 *
 * @properties={typeid:24,uuid:"900A8289-6A97-4987-A4C9-BE0D08F61F3C"}
 */
function Callback_SetLogoType(sLogoType) {

    if (sLogoType) {
        inv_logo_type = sLogoType;
    }
    else {
        inv_logo_type = null;
    }

}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D48582D3-4264-49B2-8EA0-8F8E213B1EFF"}
 */
function onAction_btnCurrencyDetails(event) {
	if (scopes.avUtils.trackCurrencyExchange(foundset.curr_id)) {
		forms.exchange_dtl_dlg._callbackForm = event.getFormName();
		forms.exchange_dtl_dlg._callbackMethod = "returnConversionRate";
		forms.exchange_dtl_dlg._cust_curr_id = foundset.curr_id;
		forms.exchange_dtl_dlg._exchangeRate = foundset.inv_exchange_rate;
		forms.exchange_dtl_dlg._totalAmount = foundset.clc_balance_owing_rounded;
		globals.DIALOGS.showFormInModalDialog(forms.exchange_dtl_dlg, -1, -1, 330, 180, i18n.getI18NMessage("avanti.lbl.currencyDetails"), false, false, "dlgCurrencyDetails", true);
	}
}

/**
 * 
 * @param {Number} conversionRate new conversion rate
 *
 * @properties={typeid:24,uuid:"ADA6FDEA-A8AA-4438-B7F7-17DA781F8A8E"}
 */
function returnConversionRate(conversionRate) {
	foundset.inv_exchange_rate = conversionRate;
	setTabVisibility();
}

/**

 * @private
 *
 * @properties={typeid:24,uuid:"96A24D1E-8833-4775-A382-6BF10DA1D5FB"}
 */
function refreshDetailView() {
    forms.sa_invoice_det_tbl.controller.recreateUI();
    globals.updateUI();
}

/**
 * 
 * @public
 *  
 * @param {JSRecord<db:/avanti/sa_invoice>} rInvoice
 * 
 * @properties={typeid:24,uuid:"5F5CF092-FD82-454E-B5F7-746C9C97B1D0"}
 */
function avInvoice_createInvoiceDocRecords(rInvoice) {
    if (!rInvoice || !rInvoice.inv_cust_id || scopes.avUtils.isNavModeReadOnly()) {
        return;
    }
    
    globals.avInvoice_cleanInvoiceDocRecords(rInvoice.inv_id);
    
    scopes.globals.avInv_selectedInvID = rInvoice.inv_id.toString();
    
    /*** @type {JSDataSet} */
    var dsDocs;
    var oSQL ={};
    
    oSQL.args = [];
    oSQL.server = globals.avBase_dbase_avanti;
    oSQL.table = 'sys_document';
    oSQL.sql = 'SELECT docs.doc_id FROM _v_invoice_sys_document AS docs \
                LEFT OUTER JOIN sa_order_revh_doc order_docs ON order_docs.doc_id = docs.doc_id \
                LEFT OUTER JOIN sa_order_revision_header ordrevh ON ordrevh.ordrevh_id = order_docs.ordrevh_id \
                WHERE docs.org_id = ? AND (ISNULL(docs.doc_is_purged,0) = 0) ';
    
    oSQL.args.push(globals.org_id.toString());
    
    oSQL.sql += ' AND (docs.cust_id = ?';
    oSQL.args.push(rInvoice.inv_cust_id.toString());
    
    if(rInvoice.custcontact_id) {
        oSQL.sql += ' OR docs.custcontact_id = ?';
        oSQL.args.push(rInvoice.custcontact_id.toString());
    } else {
        oSQL.sql += ' AND docs.custcontact_id = ?';
        oSQL.args.push(scopes.avUtils.ENUM_DEFAULT_GUID.Default);
    }
    
    oSQL.sql += ' OR docs.inv_id = ?)';
    oSQL.args.push(rInvoice.inv_id.toString());
    
    dsDocs =  globals.avUtilities_sqlDataset(oSQL);
    
    if (dsDocs && dsDocs.getMaxRowIndex() > 0) {
        for (var i = 1; i <= dsDocs.getMaxRowIndex(); i++) {
            /**@type {JSRecord<db:/avanti/sys_document>} */
            var rDoc = scopes.avDB.getRec("sys_document", ["doc_id"], [dsDocs.getValue(i, 1)]);
            
            /**@type {JSRecord<db:/avanti/sa_invoice_doc>} */
            var rInvDoc = scopes.avDB.getRec("sa_invoice_doc",["doc_id", "inv_id"],[rDoc.doc_id, scopes.globals.avInv_selectedInvID]);
            
            if (!rInvDoc) {
                /**@type {JSRecord<db:/avanti/sa_invoice_doc>} */
                rInvDoc = rDoc.sys_document_to_sa_invoice_doc$avinv_selectedinvid.getRecord(rDoc.sys_document_to_sa_invoice_doc$avinv_selectedinvid.newRecord(false, false));
                rInvDoc.inv_doc_show_on_inv = rDoc.doc_show_on_inv;
            }
        }
        
        databaseManager.saveData();
    }
}


/**
 *
 * @properties={typeid:24,uuid:"ADEEB06B-CB29-4B05-859C-9151C8AB08EF"}
 */
function setEditingStateSaInvoiceDtl() {
	forms.sa_invoice_distribution_dtl.setEditingState();
	forms.sa_invoice_journal_entry_tbl.setEditingState();
	forms.sa_invoice_det_tbl.setEditingState();
	forms.sa_invoice_docs_tbl.refreshUI();
	var bool = !scopes.avUtils.isNavModeReadOnly()
	
	elements.inv_status.editable = bool;

	elements.inv_cust_id.editable = bool;
	elements.inv_billto_custaddr_id.editable = bool;
	elements.custcontact_id.editable = bool;
	elements.inv_date.enabled = bool;
	if(!bool) {
		elements.inv_date.addStyleClass("textBkgnd_White-Override")
	} else {
		elements.inv_date.removeStyleClass("textBkgnd_White-Override")
		scopes.globals.avUtilities_setStyleClass(elements.inv_date, "textBkgnd_LightYellow")
	}
	
	elements.inv_batch_id.editable = bool;
	elements.inv_type.editable = bool;
	elements.terms_id.editable = bool;
	elements.inv_form_detail_level.editable = bool;
}
