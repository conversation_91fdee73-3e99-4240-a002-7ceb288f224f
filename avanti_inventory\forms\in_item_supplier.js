/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FAEFC090-2087-48AC-B835-98705C1566A9"}
 */
var _lookupItem = '';

/** *
  * @param _firstShow
  * @param _event
  *
  * @properties={typeid:24,uuid:"72E7EFB9-86DB-4284-8276-0C0476ACADC8"}
  * @AllowToRunInFind
  */
function onShowForm(_firstShow, _event) 
 {
	var _context = controller.getFormContext();
	/**@type {String}*/
	var _formName = _context.getValue(_context.getMaxRowIndex(),2)
	if (utils.stringLeft(_formName,20) == 'in_item_supplier_dtl')
	{
		//in lookupwindows the form names have unique numbers appended to the names
		if (_formName.startsWith('in_item_supplier_dtl'))
		{
			forms.in_item_supplier_tbl.isSupplierIdEditable = true;
		}
		else
		{
			if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(_event.getFormName()) ) )
			{
				forms.in_item_supplier_tbl.isSupplierIdEditable = true;
				forms[_formName].setNewRecordDefaults(item_id,_event);
				forms.in_item_supplier_tbl.onDataChange_Supplier(null,supplier_id,_event)
			}

		}
	}
	// GD - Oct 26, 2014: SL-2137: Lock down all fields until a supplier record is present
	if (scopes.avUtils.isNavModeReadOnly() || forms.in_item_supplier_tbl.foundset.getSize() == 0 ){
		scopes.globals.avUtilities_setFormEditMode(controller.getName(), "browse");
	} else {
		scopes.globals.avUtilities_setFormEditMode(controller.getName(), "edit");
	}
}

/**
 * Refresh the UDF fields
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"B410FC1D-0DD3-4EF9-9F4B-0079169DAD1E"}
 */
function setUDFFields()
{
	
	// Handle Landed Cost UDF's
	/*** @type {JSFoundSet<db:/avanti/in_landed_cost_udf>} */
	var fsUDF = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_landed_cost_udf');
	
	if (fsUDF.find() || fsUDF.find())
	{
		 fsUDF.search();
		 fsUDF.sort('lcudf_line asc');
	
		 // Get the first four records as we only support 4 UDF's
		 if (fsUDF.getSize() > 0 )
		 {
			 /** @type {JSRecord<db:/avanti/in_landed_cost_udf>}*/
			 var _jsRecord;
					 
			 for (var i = 1; i < 5; i++)
			 {
			 _jsRecord = fsUDF.getRecord(i);
				 
		  		
			 		switch (_jsRecord.lcudf_line)
			 		{
			 			case 1:
			 			
						if (_jsRecord.lcudf_label == '' || _jsRecord.lcudf_label == null)
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf1.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_type.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_pct.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_cost.visible = false;
			 			}
			 			
			 			else
			 				
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf1.text = _jsRecord.lcudf_label	
							forms.in_item_supplier_landedcost.elements.lblUdf1.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_type.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_pct.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_cost.visible = true;
			 				
			 				if (_jsRecord.lcudf_is_salestax == 1)
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf1_type.enabled = false;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf1_pct.enabled = false;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_cost.enabled = false;
			 				}
			 				else
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf1_type.enabled = true;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf1_pct.enabled = true;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf1_cost.enabled = true;
			 				}
			 				
			 			}
			 			
			 			break;
						
			 			case 2:
			 			
			 			if (_jsRecord.lcudf_label == '' || _jsRecord.lcudf_label == null)
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf2.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_type.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_pct.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_cost.visible = false;
			 			}
			 			
			 			else
			 				
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf2.text = _jsRecord.lcudf_label	
							forms.in_item_supplier_landedcost.elements.lblUdf2.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_type.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_pct.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_cost.visible = true;
			 				
			 				if (_jsRecord.lcudf_is_salestax == 1)
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf2_type.enabled = false;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf2_pct.enabled = false;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_cost.enabled = false;
			 				}
			 				else
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf2_type.enabled = true;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf2_pct.enabled = true;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf2_cost.enabled = true;
			 				}
			 			}
			 			
						break;
						
						case 3:
			 			
			 			if (_jsRecord.lcudf_label == '' || _jsRecord.lcudf_label == null)
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf3.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_type.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_pct.visible = false;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_cost.visible = false;
			 			}
			 			
			 			else
			 				
			 			{
			 				forms.in_item_supplier_landedcost.elements.lblUdf3.text = _jsRecord.lcudf_label
			 				forms.in_item_supplier_landedcost.elements.lblUdf3.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_type.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_pct.visible = true;
			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_cost.visible = true;
			 				
			 				if (_jsRecord.lcudf_is_salestax == 1)
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf3_type.enabled = false;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf3_pct.enabled = false;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_cost.enabled = false;
			 				}
			 				else
			 				{
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf3_type.enabled = true;
			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf3_pct.enabled = true;
				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf3_cost.enabled = true;
			 				}
			 			}
			 			
			 			break;
						
						case 4:
						forms.in_item_supplier_landedcost.elements.lblUdf4.visible = false;
		 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_type.visible = false;
		 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_pct.visible = false;
		 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_cost.visible = false;
		 				
		 				//Reserved for outsourced setup cost
//			 			if (_jsRecord.lcudf_label == '' || _jsRecord.lcudf_label == null)
//			 			{
//			 				forms.in_item_supplier_landedcost.elements.lblUdf4.visible = false;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_type.visible = false;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_pct.visible = false;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_cost.visible = false;
//			 			}
//			 			
//			 			else
//			 				
//			 			{
//			 				forms.in_item_supplier_landedcost.elements.lblUdf4.text = _jsRecord.lcudf_label	
//			 				forms.in_item_supplier_landedcost.elements.lblUdf4.visible = true;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_type.visible = true;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_pct.visible = true;
//			 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_cost.visible = true;
//			 				
//			 				if (_jsRecord.lcudf_is_salestax == 1)
//			 				{
//			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf4_type.readOnly = true;
//			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf4_pct.enabled = false;
//				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_cost.enabled = false;
//			 				}
//			 				else
//			 				{
//			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf4_type.readOnly = false;
//			 					forms.in_item_supplier_landedcost.elements.itemsupp_udf4_pct.enabled = true;
//				 				forms.in_item_supplier_landedcost.elements.itemsupp_udf4_cost.enabled = true;
//			 				}
//			 			}
			 			
			 			break;
			 		}
			 }
		}
	}
}
