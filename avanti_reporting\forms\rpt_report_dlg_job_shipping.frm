borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"144,-1,-1,416,38,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"144",
width:"38"
},
enabled:true,
labelFor:"_toJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0768F4A0-1BED-4629-A0FA-E241F5858E69"
},
{
cssPosition:"171,-1,-1,464,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"464",
right:"-1",
top:"171",
width:"212"
},
dataProviderID:"_dateTo",
enabled:true,
onDataChangeMethodID:"07EC83D5-156E-41CD-973A-3A33CA2DDBF2",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"0F450636-246D-4BB5-B102-A388E5EF112A"
},
{
cssPosition:"198,-1,-1,416,38,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"198",
width:"38"
},
enabled:true,
labelFor:"dToExpected",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"component_47072B20",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"10FDA6C1-E515-41CD-BCDB-C0178C1AC906"
},
{
cssPosition:"4,-1,-1,466,212,80",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"80",
left:"466",
right:"-1",
top:"4",
width:"212"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"1AEE81E5-9FB7-494A-87BE-264896C0B37A"
},
{
cssPosition:"7,-1,-1,416,44,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"7",
width:"44"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1B167AFF-79B4-4211-A42F-CB4AAD2A9A12"
},
{
cssPosition:"200,-1,-1,146,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"200",
width:"212"
},
dataProviderID:"_dateFromExpected",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFromExpected",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"1C1C15C0-AD76-4BEB-AEFA-D2222478CB6F"
},
{
cssPosition:"225,-1,-1,464,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"464",
right:"-1",
top:"225",
width:"212"
},
dataProviderID:"_dateToShipped",
enabled:true,
onDataChangeMethodID:"EEC473FD-52F1-443A-BF0C-0FEEEE702777",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dToShipped",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"26EC8118-61AF-4E4E-AD7A-4EA772CC4979"
},
{
cssPosition:"90,-1,-1,416,40,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"90",
width:"40"
},
enabled:true,
labelFor:"_toCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toCustomerCode_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"38ECD3FF-DAB1-4986-9FF1-920F795AA053"
},
{
cssPosition:"227,-1,-1,146,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"227",
width:"212"
},
dataProviderID:"_dateFromShipped",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFromShipped",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"39795D86-A5AB-4B68-9B1C-80E47EB4B209"
},
{
cssPosition:"144,-1,-1,464,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"464",
right:"-1",
top:"144",
width:"212"
},
dataProviderID:"_toJobNr",
enabled:true,
onDataChangeMethodID:"D156BF21-5ECD-4D84-B2AD-018D836EE51D",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_toJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"4106CA50-9EDB-4231-9886-D6262E95A90E"
},
{
cssPosition:"89,-1,-1,466,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"466",
right:"-1",
top:"89",
width:"212"
},
dataProviderID:"_toCustomer",
enabled:true,
onDataChangeMethodID:"6290E0C0-1EC0-4393-908C-CB47C9A53FBA",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_toCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"5568760D-E6A2-4FA0-A677-6A874E4F7D96"
},
{
cssPosition:"227,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"227",
width:"140"
},
enabled:true,
labelFor:"dFromShipped",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.from.shipped.date",
visible:true
},
name:"component_6FF8AD98",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5915DD3A-0131-4B62-801D-13F0C39B61E4"
},
{
cssPosition:"254,-1,-1,146,212,22",
formIndex:6,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"254",
width:"212"
},
dataProviderID:"_JobStatus",
enabled:true,
formIndex:6,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"C3CC57C6-**************-AA6FA14FE769",
visible:true
},
name:"_JobStatus",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"61B38A73-C745-465D-A623-4E24075E02DA"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:451,
typeid:19,
uuid:"737284AA-E7EF-4806-BB98-3736906924CB"
},
{
cssPosition:"118,-1,-1,146,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"118",
width:"212"
},
dataProviderID:"_fromSalesOrder",
enabled:true,
onDataChangeMethodID:"D3C9181D-F9D7-482D-9DC8-2591A90191D7",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_fromSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7908A4DB-4162-4EC2-9295-AB5FF337E0A5"
},
{
height:200,
partType:5,
typeid:19,
uuid:"7AD14665-5EA8-4C89-8B4F-BCF3130CCA83"
},
{
cssPosition:"7,-1,-1,5,131,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"7",
width:"131"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7F411279-657E-4A03-A91D-E9BD9EB52914"
},
{
cssPosition:"117,-1,-1,465,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"465",
right:"-1",
top:"117",
width:"212"
},
dataProviderID:"_toSalesOrder",
enabled:true,
onDataChangeMethodID:"3CC94128-0FCD-4246-AF51-451088BED655",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_toSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"80A27D94-C395-4247-B2AD-1976086018BA"
},
{
cssPosition:"92,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"92",
width:"140"
},
enabled:true,
labelFor:"_fromCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromCustomer",
visible:true
},
name:"_customer_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"88C26CF8-622E-4C87-9093-B2703A581F40"
},
{
cssPosition:"225,-1,-1,416,38,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"225",
width:"38"
},
enabled:true,
labelFor:"dToShipped",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"component_94E5F3B3",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8BA59862-3E78-4D7E-BBE3-99A67AC05385"
},
{
cssPosition:"146,-1,-1,146,212,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"146",
width:"212"
},
dataProviderID:"_fromJobNr",
enabled:true,
onDataChangeMethodID:"F47B8B81-6AA5-431E-BCDC-B8089E72A726",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_fromJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"93E34B75-7EE6-4EA1-979E-BFF449713647"
},
{
cssPosition:"116,-1,-1,416,40,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"116",
width:"40"
},
enabled:true,
labelFor:"_toSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9C4DC42D-8793-43D0-A368-987B74A6D807"
},
{
cssPosition:"173,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"173",
width:"140"
},
enabled:true,
labelFor:"dFrom",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobDate",
visible:true
},
name:"component_BC988466",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A660E109-6550-46F1-838C-F754531BC463"
},
{
cssPosition:"146,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"146",
width:"140"
},
enabled:true,
labelFor:"_fromJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A981BC43-D82D-4587-890B-7274207FED92"
},
{
cssPosition:"198,-1,-1,464,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"464",
right:"-1",
top:"198",
width:"212"
},
dataProviderID:"_dateToExpected",
enabled:true,
onDataChangeMethodID:"DFAC9FB4-6982-4F37-8C8A-6B95DA4D1CB0",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dToExpected",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"B2F61AD0-B13E-4D20-8494-C32D28606EEE"
},
{
cssPosition:"200,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"200",
width:"140"
},
enabled:true,
labelFor:"dFromExpected",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.from.po.expected.date",
visible:true
},
name:"component_99FADEC7",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BDE54B50-C836-4013-9AFC-EFCC7E6D6019"
},
{
cssPosition:"173,-1,-1,146,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"173",
width:"212"
},
dataProviderID:"_dateFrom",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"C2F9D626-6F26-42B7-B9DE-623552E14BF2"
},
{
cssPosition:"171,-1,-1,416,38,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"171",
width:"38"
},
enabled:true,
labelFor:"dTo",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"component_90A00EBA",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DD9A8097-68C6-45CE-B625-98D4618AC6CE"
},
{
cssPosition:"7,-1,-1,146,212,80",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"80",
left:"146",
right:"-1",
top:"7",
width:"212"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"F644E105-65C7-4F6B-9B17-4A5969F72E82",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E317BF74-93D4-4599-BCF5-49F1391E9B32"
},
{
cssPosition:"92,-1,-1,146,212,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"92",
width:"212"
},
dataProviderID:"_fromCustomer",
editable:true,
enabled:true,
onDataChangeMethodID:"A160540B-08F1-4F22-94C5-52D9B7597DE8",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_fromCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F76F11F5-F98D-4232-B6AF-92C0FB571A3A"
},
{
cssPosition:"255,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"255",
width:"140"
},
enabled:true,
labelFor:"_JobStatus",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jobStatus",
visible:true
},
name:"_JobStatus_Label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FA3383FA-D20F-47F3-9A71-C2B8512AA842"
},
{
cssPosition:"117,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"117",
width:"140"
},
enabled:true,
labelFor:"_fromSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesOrder",
visible:true
},
name:"_fromSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FC611943-F229-4966-810F-C40E116C9FF7"
}
],
name:"rpt_report_dlg_job_shipping",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"832,200",
styleName:null,
typeid:3,
uuid:"070AAF29-E1AE-498F-8367-2CD89D4D677C"