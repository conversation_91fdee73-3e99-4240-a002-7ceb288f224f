borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
height:200,
partType:5,
typeid:19,
uuid:"13E7F6A9-FB01-4E29-8DB7-92921B22630A"
},
{
cssPosition:"150,-1,-1,11,131,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"150",
width:"131"
},
enabled:true,
formIndex:2,
labelFor:"_fromJob",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJob_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3BE643C6-9B49-4D37-A7EB-BFD4620BC9D1"
},
{
cssPosition:"25,298,-1,-1,92,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"298",
top:"25",
width:"92"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3DF412C5-DCF7-42EA-AFAA-7C77B49A1F1B"
},
{
cssPosition:"187,-1,-1,12,354,20",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"12",
right:"-1",
top:"187",
width:"354"
},
dataProviderID:"includeInvoice",
enabled:true,
formIndex:8,
onDataChangeMethodID:"77843178-DA38-4985-949E-00FE0AAE9450",
styleClass:"checkbox_bts",
tabSeq:0,
text:"Exclude Partially Invoiced Shipments",
toolTipText:"When Checked, partially and fully invoiced shipments will be excluded. When unchecked, only fully invoiced shipments will be excluded.",
visible:true
},
name:"_excludeInvoices",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"4D8222D7-A23B-410E-A159-5AA3650AE151"
},
{
cssPosition:"150,298,-1,-1,92,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"298",
top:"150",
width:"92"
},
enabled:true,
formIndex:4,
labelFor:"_toJob",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toJob_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6424E8ED-851B-4235-906D-081D12EFD519"
},
{
cssPosition:"25,-1,-1,143,237,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"143",
right:"-1",
top:"25",
width:"237"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"9F6104FD-9880-4460-8C36-4437F6B37858",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"81B53A2F-7081-4441-984D-1A6C6E34D564"
},
{
cssPosition:"150,-1,-1,484,244,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"484",
right:"-1",
top:"150",
width:"244"
},
dataProviderID:"_toJob",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"9686F69D-A8E9-4F9C-84A5-A05F2C59AD2B",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"_toJob",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"84030FB6-9B29-497F-B1FA-3633D5C39090"
},
{
cssPosition:"150,-1,-1,143,237,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"143",
right:"-1",
top:"150",
width:"237"
},
dataProviderID:"_fromJob",
editable:true,
enabled:true,
formIndex:3,
onDataChangeMethodID:"6ED0E4AB-DD07-43C2-97FA-4E7DD01D8B6F",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"_fromJob",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"92D56CCE-8103-41C3-9297-854A66FFF60A"
},
{
cssPosition:"22,-1,-1,484,244,112",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"484",
right:"-1",
top:"22",
width:"244"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"A3B43AE8-B772-4822-AB4A-CF27B844B9C0"
},
{
cssPosition:"25,-1,-1,12,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"12",
right:"-1",
top:"25",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D1224CB0-6B12-4FEF-B9BC-AE0086B1E1D8"
}
],
name:"rpt_report_dlg_jobsShippedButNotInvoicedReport",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"777,200",
styleName:null,
typeid:3,
uuid:"31D609E5-0EF6-499F-8D2D-FC01EE62A687"