customProperties:"useCssPosition:true",
dataSource:"db:/avanti/prod_job_cost_labour",
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
cssPosition:"157,-1,-1,637,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'jcl_extra_code_id'\",\
\"'Shop Floor Extra Code'\"\
],\
parameters:[]\
}\
}",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"637",
right:"-1",
top:"157",
width:"24"
},
enabled:true,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:-2,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btn_extra_code_lookup",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0196EDC8-F327-464C-AA61-F0BE61ED57FB"
},
{
cssPosition:"103,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"103",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.startTime",
visible:true
},
name:"startdate_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1ADCA379-CEE8-4142-B8C3-C76A36328D43"
},
{
cssPosition:"4,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"4",
width:"140"
},
enabled:true,
styleClass:"label_bts bold",
tabSeq:-1,
text:"i18n:avanti.lbl.labour",
visible:true
},
name:"labour_lbl",
styleClass:"label_bts bold",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2BB04705-DA26-4942-872B-668147406F68"
},
{
cssPosition:"103,-1,-1,669,156,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"669",
right:"-1",
top:"103",
width:"156"
},
enabled:true,
labelFor:"totalelapsedtime",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.totalElapsedTimeMinute",
visible:true
},
name:"total_elapsedtime_lbl",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2E28D008-8BAB-4954-A081-56CFA93A8909"
},
{
cssPosition:"130,-1,-1,331,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"331",
right:"-1",
top:"130",
width:"25"
},
enabled:true,
onActionMethodID:"05555BB6-2C43-43B8-A136-1FB908D1C21D",
styleClass:"listview_noborder label_bts text-center",
tabSeq:-2,
text:"%%globals.icon_Notes%%",
toolTipText:"i18n:avanti.lbl.MilestoneProgressNote",
visible:true
},
name:"btnProgressComments",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2EE6FE9E-BB09-4690-A2D5-7CCFCA8BFFE3"
},
{
cssPosition:"184,-1,-1,146,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"184",
width:"205"
},
dataProviderID:"jcl_qa_field_1_complete",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:8,
text:"",
visible:true
},
name:"qa_check_field_1",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"347C62F5-C8E5-472D-920D-A299CE1EBC55"
},
{
cssPosition:"130,-1,-1,834,123,75",
json:{
cssPosition:{
bottom:"-1",
height:"75",
left:"834",
right:"-1",
top:"130",
width:"123"
},
dataProviderID:"clc_last_staging_location",
editable:false,
styleClass:"textarea_bts",
tabSeq:0,
visible:false
},
name:"clc_last_staging_location",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"3D95EA32-B897-4345-B2FA-17C775A989F1",
visible:false
},
{
cssPosition:"130,-1,-1,508,150,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"508",
right:"-1",
top:"130",
width:"150"
},
dataProviderID:"jcl_qty_spoilage",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:5,
visible:true
},
name:"fldScrapQty",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"40051D93-8DBC-472A-8CCE-051858743D0D"
},
{
cssPosition:"184,-1,-1,358,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"358",
right:"-1",
top:"184",
width:"205"
},
dataProviderID:"jcl_qa_field_2_complete",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:9,
text:"",
visible:true
},
name:"qa_check_field_2",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"49901058-1A36-4499-9299-39DE5CB8478B"
},
{
cssPosition:"157,-1,-1,363,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"363",
right:"-1",
top:"157",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.extraCode",
visible:true
},
name:"extracode_lbl",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4CF6378A-88FF-4BDD-80B8-4A5CA4F3D482"
},
{
cssPosition:"184,-1,-1,570,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"570",
right:"-1",
top:"184",
width:"205"
},
dataProviderID:"jcl_qa_field_3_complete",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:10,
text:"",
visible:true
},
name:"qa_check_field_3",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"509443FF-274E-4C22-B8A7-B61A7CCE54F8"
},
{
cssPosition:"157,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"157",
width:"140"
},
enabled:true,
styleClass:"label_bts text-left",
tabSeq:-1,
text:"i18n:avanti.lbl.rushCode",
visible:true
},
name:"rushcode_lbl",
styleClass:"label_bts text-left",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"545F88D0-0A4C-4541-B45C-B3351D826531"
},
{
cssPosition:"130,-1,-1,360,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"360",
right:"-1",
top:"130",
width:"25"
},
enabled:true,
onActionMethodID:"BA77E934-5B11-4AD0-A547-4D2901620F64",
styleClass:"listview_noborder label_bts text-center",
tabSeq:-2,
text:"%%globals.icon_add%%",
toolTipText:"i18n:avanti.lbl.AddMilestoneProgress",
visible:true
},
name:"btnAddJobProgressEntry",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"59662EEE-5A9E-4B06-985D-8C044A8D7AA5"
},
{
cssPosition:"130,-1,-1,145,123,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"130",
width:"123"
},
dataProviderID:"jcl_qty_produced",
editable:true,
enabled:true,
onDataChangeMethodID:"0EA3FD5C-12C9-4DC3-A459-321E396700F3",
onFocusLostMethodID:"B294EA46-2AFF-4856-A740-99E902413307",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:4,
toolTipText:"i18n:avanti.tooltip.quantityProduced",
visible:true
},
name:"quantity_produced",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5B3FC9B3-22D9-489C-AF26-F37E1255DFB9"
},
{
cssPosition:"103,-1,-1,834,123,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"834",
right:"-1",
top:"103",
width:"123"
},
dataProviderID:"total_elapsed_in_minutes",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:3,
visible:true
},
name:"totalelapsedtime",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"746B4FAF-B89C-4D21-9445-23AC2D6CB160"
},
{
cssPosition:"130,-1,-1,392,110,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"392",
right:"-1",
top:"130",
width:"110"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.ScrapQuantity",
visible:true
},
name:"lblScrapQty",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"762CC411-746C-4B14-85A7-1E4132532C93"
},
{
cssPosition:"130,-1,-1,272,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"272",
right:"-1",
top:"130",
width:"25"
},
enabled:true,
onActionMethodID:"37CDD27A-2289-4F7B-8E2E-961CD3755CB5",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_TreeView%%",
toolTipText:"i18n:avanti.lbl.distributeQuantity",
visible:true
},
name:"btnDistributeQuantity",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7DAE44B5-305D-4B88-A38D-AD5FC43E1DD4"
},
{
cssPosition:"157,-1,-1,145,123,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"157",
width:"123"
},
dataProviderID:"jcl_rush_code_id",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:6,
valuelistID:"BC963E65-00D2-43EE-BFEC-6710C03C6C2C",
visible:true
},
name:"rushcode",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7EDCFD12-F98B-4281-93C9-0022A6E937AF"
},
{
cssPosition:"184,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"184",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"QA Checks",
visible:true
},
name:"qa_check_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"831AE293-5331-42F6-8D5D-2980B1FC9477"
},
{
cssPosition:"130,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"130",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shopFloorQuantityProduced",
visible:true
},
name:"quantity_produced_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"88C8C04D-5792-413C-A970-009CB05BAABC"
},
{
cssPosition:"31,-1,-1,1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"-1",
top:"31",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.documents",
visible:true
},
name:"documents_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8E3A3500-2592-4DF8-9C22-C7BE8035A8AA"
},
{
cssPosition:"157,-1,-1,508,123,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"508",
right:"-1",
top:"157",
width:"123"
},
dataProviderID:"jcl_extra_code_id",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:7,
valuelistID:"5BCFB235-8B72-4140-B227-A7444C0FB5BA",
visible:true
},
name:"extracode",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A57FED96-AA48-44A8-B5DC-E86E2866302B"
},
{
cssPosition:"103,-1,-1,363,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"363",
right:"-1",
top:"103",
width:"140"
},
enabled:true,
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.endTime",
visible:true
},
name:"enddate_lbl",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AA27471A-C44F-4B28-8A9D-0DB7DB6C2BBA"
},
{
cssPosition:"130,-1,-1,302,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"302",
right:"-1",
top:"130",
width:"25"
},
enabled:true,
onActionMethodID:"DCA3BA0A-A8CE-4FBF-926D-10466D57837F",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_sfClicksInfo%%",
toolTipText:"i18n:avanti.lbl.shopFloorClicks",
visible:true
},
name:"btnImpressions",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AC75E21C-43CB-4752-9308-E4C5C50C21F8"
},
{
cssPosition:"211,0,-1,1,995,322",
json:{
cssPosition:{
bottom:"-1",
height:"322",
left:"1",
right:"0",
top:"211",
width:"995"
},
tabs:[
{
containedForm:"697360C5-0E00-48F4-ACCC-9F9A41A73705",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:null,
svyUUID:"EAB4C64B-24F6-4B86-A7F8-36435F637E73",
text:"i18n:avanti.lbl.materials"
},
{
containedForm:"BED440CC-72E5-4FCF-B528-863EBEA190FA",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:"prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sch_milestone.sch_milestone_to_prod_job_milestone_progress",
svyUUID:"6699F400-C8C7-4D75-81A2-60948966C1F6",
text:"i18n:avanti.lbl.MilestoneProgress"
},
{
containedForm:"90B519B4-BD18-41DB-A4C5-1744CE9B5F30",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:null,
relationName:null,
svyUUID:"BB34F413-3A42-495D-9FDF-4D40B2F6F0EE",
text:"i18n:avanti.lbl.BoxLabels"
}
]
},
name:"material_tab",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"B3D7C8E6-A128-4360-82CF-830B8EE45AC6"
},
{
cssPosition:"157,-1,-1,275,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'jcl_rush_code_id'\",\
\"'Shop Floor Rush Code'\"\
],\
parameters:[]\
}\
}",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"275",
right:"-1",
top:"157",
width:"24"
},
enabled:true,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:-2,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btn_rush_code_lookup",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B9D76E77-86DD-4BC6-B76D-1880F82CCCC8"
},
{
height:535,
partType:5,
typeid:19,
uuid:"BC9C0BC3-F619-495E-ADC0-C30AE2EE074E"
},
{
cssPosition:"103,-1,-1,508,150,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"508",
right:"-1",
top:"103",
width:"150"
},
dataProviderID:"end_date_to_locale",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:2,
visible:true
},
name:"enddate",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C6A544E2-B6DD-4EF5-B4EF-226C47F418AB"
},
{
cssPosition:"103,-1,-1,145,150,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"103",
width:"150"
},
dataProviderID:"start_date_to_locale",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:1,
visible:true
},
name:"startdate",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C81CA3AC-3935-4784-9DAE-581E55E056EF"
},
{
cssPosition:"130,-1,-1,666,156,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"666",
right:"-1",
top:"130",
width:"156"
},
enabled:true,
labelFor:"clc_last_staging_location",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.LastStagingLocation",
visible:true
},
name:"lbl_clc_last_staging_location",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D50476EB-EBBD-4C2F-BCE0-2566D1EFC991"
},
{
cssPosition:"103,-1,-1,963,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"963",
right:"-1",
top:"103",
width:"24"
},
enabled:true,
onActionMethodID:"AD25650D-5682-44F3-9121-3AF02C5921D5",
styleClass:"label_bts",
tabSeq:-2,
text:"%%globals.icon_tableViewInfo%%",
visible:true
},
name:"info_icon",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F912C332-065B-4AFF-A25A-7BC1EFF81548"
},
{
cssPosition:"31,-1,500,145,837,59",
json:{
cssPosition:{
bottom:"500",
height:"59",
left:"145",
right:"-1",
top:"31",
width:"837"
},
dataProviderID:"selected_documents",
enabled:true,
onDataChangeMethodID:"DD77ACC5-513D-4EB9-8501-31B8246B9C48",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:-2,
valuelistID:"*************-4747-8A2B-31FFBC41E1AA",
visible:true
},
name:"documents",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"FCAB94BC-B1D7-43F9-9FDE-0789B0C1BFEB"
}
],
name:"sf_main_dtl_labour",
onShowMethodID:"63C3943B-A5EB-489E-BC4F-835E2290C46B",
scrollbars:33,
size:"995,420",
styleName:"Avanti",
typeid:3,
uuid:"EDD73B32-0F0D-41DF-805F-B30B9F53A7D7"