/**
 * @properties={typeid:35,uuid:"0D4D0A49-5589-4089-A674-8FE0EF2CFFC3",variableType:-4}
 */
var aClonedForms = [];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"47B93CEC-A4A2-4CF4-88C1-F1BE3F49C86B",variableType:-4}
 */
var aSegmentDesc = new Array();

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"363CFB9E-AA49-4038-AC91-8292AC205B5D",variableType:-4}
 */
var aSegmentFlag = new Array();

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"81B7A16C-C0D6-4E4A-BDE1-87260A282E28",variableType:-4}
 */
var aSegmentCode = new Array();

/**
 * <PERSON>le changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B8A56232-136E-4D11-A4AD-B945D0645801"}
 */
function onDataChange_DateTo(oldValue, newValue, event)
{
	pricerule_end_date = globals.avUtilities_dateSetTimeEOD(pricerule_end_date)
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"0EF230C3-C3E4-4D16-A54E-C5FFABC193B1"}
 */
function onDataChange_PriceType(oldValue, newValue, event) {
	
if (sa_price_rule_to_sa_price_type) pricerule_type = sa_price_rule_to_sa_price_type.pricetype_type;
    
	var sBaseForm = 'sa_price_rule_detail_tbl',
		sCloneForm = sBaseForm + application.getUUID().toString().slice(1,8);
	
	aClonedForms.push(sCloneForm);
	forms.sa_price_rule_detail_dtl.elements.tab1.containedForm = null;
	
	var jsForm = solutionModel.getForm(sBaseForm);
	var jsClone = solutionModel.cloneForm(sCloneForm,jsForm);	
	
    	/** @type {JSWebComponent} */ 
	var jsGrid = jsClone.getWebComponent('grid');
		/** @type {Array<CustomType<aggrid-groupingtable.column>>} */
    var aColumns = jsGrid.getJSONProperty('columns');

    if (newValue != null) {
        
        if (foundset.sa_price_rule_to_sa_price_type && foundset.sa_price_rule_to_sa_price_type.sa_price_type_to_sa_price_type_segment) {
            var fsSegments = foundset.sa_price_rule_to_sa_price_type.sa_price_type_to_sa_price_type_segment;
            fsSegments.sort('sequence_nr asc');
            var iMax = fsSegments.getSize();
            aSegmentDesc = [];
            aSegmentFlag = [];
            aSegmentCode = [];
            
            for (var i = 1; i <= iMax; i++) {
                var rSegments = fsSegments.getRecord(i);
                
                if (rSegments.pricetypeseg_code && rSegments.pricetypeseg_code.length > 0) {
                    var lblFlag = rSegments.pricetypeseq_flag == '1' ? '*' : '';
                    var lblSegment = i18n.getI18NMessage('avanti.lbl.priceSegmentCode_' + rSegments.pricetypeseg_code) + lblFlag;
                    
                    aSegmentDesc.push(lblSegment);
                    aSegmentFlag.push(rSegments.pricetypeseq_flag);
                    aSegmentCode.push(rSegments.pricetypeseg_code);
                    
                    
                    /** @type {CustomType<aggrid-groupingtable.column>} */
            		var column = {
            			rowGroupIndex: -1,
            			enableRowGroup: false,
            			enableSort: false,
            			enableToolPanel: false,
            			autoResize: true
            		};
                    
					 // Add columns to the grid
					column.dataprovider = 'priceruledtl_repsonse_seg' + i.toString();
                    column.id = 'priceruledtl_repsonse_seg' + i.toString();
					column.headerTitle = lblSegment;
                    column.editType = 'TYPEAHEAD';
                    column.filterType = 'NONE';
                    var sVlName = setSegmentValueList(rSegments.pricetypeseg_code, rSegments.pricetypeseq_flag);
                    application.output(sVlName);
                    column.valuelist = solutionModel.getValueList(sVlName).getUUID().toString();
                    aColumns.push(column);
                }
            }
            if (aColumns.length > 0) {
            	aColumns = scopes['ng_utils'].reorderArrayByStyleClass(aColumns);
            	jsGrid.setJSONProperty('columns',aColumns);
            }
        }
    }

    forms.sa_price_rule_detail_dtl.elements.tab1.containedForm = sCloneForm;
    
    forms[sCloneForm].aSegmentFlag = aSegmentFlag;
    
    // Update filter visibility
    var iMaxSegments = aSegmentDesc.length;
    for (var j = 1; j <= iMaxSegments; j++) {
        forms[sCloneForm].elements['_filter_seg' + j.toString() + '_label'].visible = true;
        forms[sCloneForm].elements['_filter_seg' + j.toString()].visible = true;
        forms[sCloneForm].elements['btnFilterSeg' + j.toString()].visible = true;
        if (aSegmentCode[j-1] == "A") forms[sCloneForm].elements['btnBulkChange' + j.toString()].visible = true;
        forms[sCloneForm].elements['_filter_seg' + j.toString() + '_label']["text"] = aSegmentDesc[j-1];

    }
    
    return true;
}

/**
 * setSegmentValueList
 * @param {String} sSegmentType
 * @param {Number} iFlag
 * 
 * @return {String} sValueList
 *
 * @properties={typeid:24,uuid:"2BE4563E-646F-4645-8A39-E5FE20AE4E95"}
 */
function setSegmentValueList(sSegmentType,iFlag) 
{
	switch(sSegmentType)
	{
	//Customer
	case 'A':
		return 'vl_PriceRules_Customers';
		break;
	
	//Customer Category
	case 'B':
		if (iFlag == 1) {
			return 'vl_CustomerCategory_desc_and_code';
		}
		else {
			return 'vl_CustomerCategory_desc_and_code_aev';
		}
		break;
	
	//Customer Class
	case 'C':
		if (iFlag == 1) {
			return 'vl_CustomerClass_desc_and_code';
		}
		else {
			return 'vl_CustomerClass_desc_and_code_aev';
		}
		break;
		
	//Item
	case 'D':	
		return 'vl_PriceRules_Items';	
		break;
		
	//Item Class
	case 'E':
		if (iFlag == 1) {
			return 'vl_itemClass_desc_and_code';
		}
		else {
			return 'vl_itemClass_desc_and_code_aev';
		}
		break;
			
	//Item Group
	case 'F':
		if (iFlag == 1) {
			return 'vl_ItemGroup_desc_and_code';
		}
		else {
			return 'vl_ItemGroup_desc_and_code_aev';
		}
		break;
	
	//Task Type
	case 'G':
		if (iFlag == 1) {
			return 'vl_TaskTypesWithUUID_NoEmptyValue';
		}
		else {
			return 'vl_TaskTypesWithUUID_WithEmptyValue';
		}
		break;
		
	//Task
	case 'H':
		return 'vl_PriceRules_Tasks';
		break;
		
	//Department
	case 'I':
		return 'vl_PriceRules_Departments';		
		break;
		
	//Task Operation
	case 'J':
	    return 'vl_TaskOperations_i18n_pricing';
		break;		
		
	//Cost Centre
	case 'K':
		return 'vl_PriceRules_CostCenter';
		break;
	
	//Inks
	case 'L':
		return 'avInkTypes_desc_and_code';
		break;
	
	//Variable
	case 'M':
		return 'avSales_priceSegmentMachineVariable';
		break;
			
	case 'N':          
        return  'vl_PriceRules_Divisions';
	    break;
    
	case 'O': 
        return 'vl_PriceRules_Plants';
	    break;
    
	default:
		
	return null;
	}

}

/**
 * duplicate Price Rule
 *
 * @properties={typeid:24,uuid:"AF220012-340B-4BF5-9B3E-E46D8325413D"}
 */
function duplicatePriceRule()
{
	// Show dialog to verify duplicate method
	var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.duplicateRecord_title"), 
		i18n.getI18NMessage("avanti.dialog.duplicatePriceRule_title") + ' ' + foundset.pricerule_desc, 
		i18n.getI18NMessage("avanti.dialog.ok"), 
		i18n.getI18NMessage("avanti.dialog.cancel"));
	if (sAns != i18n.getI18NMessage("avanti.dialog.ok"))
	{
		return;
	}
	
	var rCurRule = foundset.getSelectedRecord();
//	var sCurRulePK = rCurRule.pricerule_id;
	
	// Create a new record and get its UUID
	foundset.duplicateRecord(foundset.getSelectedIndex(), false, true);
	var rNewRule = foundset.getSelectedRecord();
	var sNewRulePK = rNewRule.pricerule_id;
	rNewRule.pricerule_desc += i18n.getI18NMessage("avanti.lbl.duplicated");
	
	//Duplicate each rule detail record
	var i,j,k;
	var iMax,kMax;
	var aClone = ['sa_price_rule_to_sa_price_rule_detail'];
	
	for (i = 0; i <= aClone.length - 1; i++)
	{
		if (utils.hasRecords(rCurRule[aClone[i]]))
		{
			iMax = rCurRule[aClone[i]].getSize();
			
			for ( j = 1; j <= iMax; j++)
			{
				/** @type {JSRecord<db:/avanti/sa_price_rule_detail>}*/
				var rCurPriceRuleDetail = rCurRule[aClone[i]].getRecord(j);
//				var sCurPriceRuleDetailPK = rCurPriceRuleDetail.pricerule_id;
				
				rCurRule[aClone[i]].duplicateRecord(j, false, true);
				rCurRule[aClone[i]].pricerule_id = sNewRulePK;
				var sNewRuleDetailPK = rCurRule[aClone[i]].priceruledtl_id;
				
				kMax = rCurPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.getSize();
				for (k = 1; k <= kMax; k++)
				{
					rCurPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.duplicateRecord(k,false,true);
					rCurPriceRuleDetail.sa_price_rule_detail_to_sa_price_rule_breaks.priceruledtl_id = sNewRuleDetailPK;
				}
			}
		}
	}
	
	databaseManager.saveData();
	
	dc_edit(null, globals.nav.browser_buttonbar)
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"C9AC209B-E67B-41B4-9493-************"}
 */
function dc_duplicate(_event, _triggerForm) {
	duplicatePriceRule();
}

/**
 * @param _foundset
 *
 * @properties={typeid:24,uuid:"9313DFFC-B6A5-44C7-A787-74C1695F2125"}
 */
function dc_save_pre(_foundset) {    
    _super.dc_save_pre(_foundset)    ;
    _foundset.pricerule_costbreakflag = '';
    for (var i = 1; i <= foundset.sa_price_rule_to_sa_price_rule_detail.getSize(); i++ ) {
        var rRec =  foundset.sa_price_rule_to_sa_price_rule_detail.getRecord(i);
        if (rRec.priceruledtl_break_method == 'CB' || rRec.priceruledtl_break_method == 'CP') {
            _foundset.pricerule_costbreakflag = '*';
            break;
        }
    } 
}

/**
 * @param {JSEvent} _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"940CA0D6-5999-4D0F-9189-DB9243FC69B9"}
 */
function dc_save(_event, _triggerForm) {
    
    _to_sys_organization.org_price_rule_edit_datetime = new Date();
    
    return _super.dc_save(_event, _triggerForm);
}

/**
 * @param {JSEvent} event the event that triggered the action
 * @param _form
 *
 * @private
 * @override
 *
 * @return
 * @properties={typeid:24,uuid:"8539E571-BFD0-42D3-9136-DBBB73AE9E0E"}
 */
function onRecordSelection(event, _form) {
    
    forms.sa_price_rule_detail_dtl.setSellingUom();
    
    return _super.onRecordSelection(event, _form);
}

/**
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @return
 * @properties={typeid:24,uuid:"25EB5AD4-0671-45EE-AC1B-B905E179E771"}
 */
function onShow(firstShow, event) {
    forms.sa_price_rule_detail_dtl.setSellingUom();
    forms.sa_price_rule_detail_tbl.load_vl_PriceRules_CostCenter();
    
    return _super.onShowForm(firstShow, event);
}

/**
 * @param {JSEvent} event
 *
 * @protected
 * @override
 *
 * @properties={typeid:24,uuid:"F792A743-0D4B-4779-B126-2852015646EC"}
 */
function onHide(event) {
	removeClonedForms();
	return _super.onHide(event);
}

/**
 * removeClonedForms
 *
 * @return
 * @properties={typeid:24,uuid:"3DF0EC4A-7C71-4D35-9C75-802EA8BBF276"}
 */
function removeClonedForms() {
	
	var bSuccess = false;
	try {

		for (var i = 0; i < aClonedForms.length; i++) {
			var existingForm = solutionModel.getForm(aClonedForms[i]);
			if (existingForm) {
			
				// Remove from history
				bSuccess = history.removeForm(aClonedForms[i]);
			
				// Remove from solution model
				if (bSuccess) {
					bSuccess = solutionModel.removeForm(aClonedForms[i]);
	//				application.output("Clone: " + aClones[i] + " removed?" + bSuccess);
					if (bSuccess && aClonedForms.length > 0) {
						aClonedForms.pop();
					}
				}
			}
		}
		aClonedForms = [];
	}
	catch (err) {
		application.output('Error in sa_price_rule_dtl.removeClonedForms():' + err.message);
	}

	return bSuccess;
}