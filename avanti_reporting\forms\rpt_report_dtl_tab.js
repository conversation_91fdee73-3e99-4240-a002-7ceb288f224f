/**
 * @type {Array}
 * @properties={typeid:35,uuid:"5F3238D7-F009-43CA-BD5F-AF97AF7C9896",variableType:-4}
 */
var _availablePrograms = null;

/**
 * Checks to see if the program is on the list of available programs for the report
 *
 * <AUTHOR>
 * @since 2011-05-30
 * 
 * @param {String} sProgramName - The program name to check
 * 
 * @properties={typeid:24,uuid:"8C1D1982-72B9-453A-9889-AD7DCE540123"}
 * 
 *  @returns {boolean} TRUE or FALSE
 */
function checkAvailableReportPrograms(sProgramName)
{
	// If there is no available programs for this report, then throw an error
	if (!forms.rpt_report_dtl_tab._availablePrograms)
	{
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportProgramError_title"), 
			i18n.getI18NMessage("avanti.dialog.reportProgramError_msg"), 
			i18n.getI18NMessage("avanti.dialog.ok"));
		
		return false;
	}
	
	// If this menu is not in the list of available programs, then throw error
	if (forms.rpt_report_dtl_tab._availablePrograms.indexOf(sProgramName, 0) == -1)
	{
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.reportProgramMenu_title"), 
			i18n.getI18NMessage("avanti.dialog.reportProgramMenu_msg"), 
			i18n.getI18NMessage("avanti.dialog.ok"));
		
		return false;
	}
	
	return true;
}


/**
 * TODO generated, please specify type and doc for the params
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"9736E4ED-CE74-41AF-A354-90FCDA147A38"}
 */
function onShowForm(firstShow, event) {
	
}
