/**
 * @enum
 * @properties={typeid:35,uuid:"98FF60F8-73B2-468C-BE36-4DDD977DF60B",variableType:-4}
 */
var weekdays = {
    0: "Sunday",
    1: "Monday",
    2: "Tuesday",
    3: "Wednesday",
    4: "Thursday",
    5: "Friday",
    6: "Saturday"
}; 

/**
 * @properties={typeid:35,uuid:"2E1A87FD-5C31-4A3C-9B53-4782AE6EE7A7",variableType:-4}
 */
var DST_CHANGE = {
    spring_forward: "spring_forward",
    fall_back: "fall_back",
    none: "none"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"F9D34B85-6A39-4806-B144-B86E63F0AD4A",variableType:-4}
 */
var ENUM_TIME_FORMAT = {
    hhmmss: "hhmmss"
};

/**
 * @enum 
 * @public 
 *
 *
 * @properties={typeid:35,uuid:"F19AD5A1-8C82-48A4-ADC6-8AE77F072CB9",variableType:-4}
 */
var PURGE_PERIOD = {
	one_month: "1month",
	three_months: "3months",
	six_months: "6months",
	one_year: "1year",
	never: "never"
};

/**
 * @public 
 * 
 * @param {Date} dStartDate
 * @param {Date} dStopDate
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"75D43950-C419-4AA2-AE14-0CB7A07E323C"}
 */
function getDiffInMinutes(dStartDate, dStopDate){
	var nMillisecondsDiff = (dStopDate - dStartDate)
	if(nMillisecondsDiff==0){
		return 0
	}
	else{
		return nMillisecondsDiff / 1000 / 60
	}
}

/**
 * @public 
 * 
 * @param {Date} dStartDate
 * @param {Date} dStopDate
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"1510CA27-68AD-4B83-8DCE-DE51D9133DDF"}
 */
function getDiffInSeconds(dStartDate, dStopDate){
	var nMillisecondsDiff = (dStopDate - dStartDate);
	if(nMillisecondsDiff==0){
		return 0;
	}
	else{
		return nMillisecondsDiff / 1000;
	}
}

/**
 * @param {Date} dDate1
 * @param {Date} dDate2
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"CE08A031-37EA-4349-84B1-72C964574C23"}
 */
function isSameDay(dDate1, dDate2){
	return dDate1.getFullYear() == dDate2.getFullYear()
		&& dDate1.getMonth() == dDate2.getMonth()
		&& dDate1.getDate() == dDate2.getDate();
}

/**
 * @param {Date} dDate1
 * @param {Date} dDate2
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"6F12F02D-A523-4D9D-8826-C14971263385"}
 */
function greaterOf(dDate1, dDate2){
	if(dDate1 > dDate2){
		return dDate1;
	}
	else{
		return dDate2;
	}
}

/**
 * return time string from date in the format '14:25'
 * 
 * @public 
 * 
 * @param {Date} dDate
 * @param {String} [sFormat]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"311E441D-BD52-4956-B05D-ACA66AB9DF5E"}
 */
function getTimeFromDate(dDate, sFormat){
	if(dDate instanceof Date){
		var nHours = dDate.getHours();
		var nMins = dDate.getMinutes();
        var nSec = dDate.getSeconds();
		var sHours = nHours < 10 ? '0' + nHours.toString() : nHours;
		var sMins = nMins < 10 ? '0' + nMins.toString() : nMins;
        var sSec = nSec < 10 ? '0' + nSec.toString() : nSec;
        
        switch (sFormat) {
            case ENUM_TIME_FORMAT.hhmmss:
                return sHours.toString() + sMins.toString() + sSec.toString();
            default:
                return sHours + ':' + sMins;
        }
	}
	else{
		return null;
	}
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {Boolean} [bConvertToServerTime]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"E4488979-DE30-4CE9-855F-395C14BE8B41"}
 */
function getDateSQl(dDate, bConvertToServerTime){
	if (bConvertToServerTime) {
		dDate = convertClientTimeToServerTime(dDate);
	}
	
	var y = dDate.getFullYear().toString();
	var m = (dDate.getMonth() + 1).toString();
	var d = dDate.getDate().toString();
	
	if(m.length == 1){
	    m = "0" + m;
	}

	if(d.length == 1){
        d = "0" + d;
    }
	
	var sDateString = y + m + d;
	
	// 112 - ISO - yyyymmdd
	return "CONVERT ( datetime , '" + sDateString + "' , 112)";
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {Boolean} [bIncludeTime] - YYYYMMDD.HHNN
 * @param {Boolean} [bIncludeTimeToMS] - YYYYMMDD.HHNNSSMMM
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"F0DF8D43-008F-45EB-B0F8-C09257661A5F"}
 */
function getDateAsNum(dDate, bIncludeTime, bIncludeTimeToMS) {
	var nDate = null;
	
	if (dDate) {
		var sDate = null;
		
		if (bIncludeTimeToMS) {
			sDate = getDateTimeString(dDate, null, true);
		}
		else if (bIncludeTime) {
			sDate = getDateTimeString(dDate, true);
		}
		else {
			sDate = getDateString(dDate);
		}
		
		if (sDate) {
			nDate = (bIncludeTime || bIncludeTimeToMS) ? parseFloat(sDate) : parseInt(sDate); 
		}
	}
	
	return nDate;
}

/**
 * @param {String} period
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"844720B5-E8F1-4D3D-BA30-97378EF3FD05"}
 */
function getDateFromPeriod(period){
	var date;
	switch (period) {
	case PURGE_PERIOD.one_month:
		date = plugins.DateUtils.addMonths(new Date(), -1)
		break;
	case PURGE_PERIOD.three_months:
		date = plugins.DateUtils.addMonths(new Date(), -3)
		break;
	case PURGE_PERIOD.six_months:
		date = plugins.DateUtils.addMonths(new Date(), -6)
		break;
	case PURGE_PERIOD.one_year:
		date = plugins.DateUtils.addYears(new Date(), -1)
		break;
	case PURGE_PERIOD.never:
		break;
	default:
		break;
	}
	
	return date;
}


/**
 * @param {Date} dDateTime
 * @param {String} sGranularity - (M)inutes, (S)econds, (MS) - milliseonds
 * @param {Boolean} [bConvertToServerTime]
 *
 * @return
 * @properties={typeid:24,uuid:"7DD43454-B59C-4816-B60E-08961E133D78"}
 */
function getDateTimeSQl(dDateTime, sGranularity, bConvertToServerTime) {
	if (bConvertToServerTime) {
		dDateTime = convertClientTimeToServerTime(dDateTime);
	}
	
    var y = dDateTime.getFullYear().toString();
    var m = (dDateTime.getMonth() + 1).toString();
    var d = dDateTime.getDate().toString();
    var h = dDateTime.getHours().toString();
    var n = dDateTime.getMinutes().toString();
    var s = dDateTime.getSeconds().toString();
    var ms = dDateTime.getMilliseconds().toString();

    if (m.length == 1) {
        m = "0" + m;
    }
    if (d.length == 1) {
        d = "0" + d;
    }
    if (h.length == 1) {
        h = "0" + h;
    }
    if (n.length == 1) {
        n = "0" + n;
    }
    if (s.length == 1) {
        s = "0" + s;
    }

    if (ms.length == 1) {
        ms = "00" + ms;
    }
    else if (ms.length == 2) {
        ms = "0" + ms;
    }

    // default to mins unless otherwise specified
    var sDateString = y + '-' + m + '-' + d + ' ' + h + ':' + n;

    if (sGranularity == 'S') {
        sDateString += ':' + s;
    }
    else if (sGranularity == 'MS') {
        sDateString += ':' + s + '.' + ms;
    }

    // 120 - ODBC canonical - yyyy-mm-dd hh:mi:ss.mmm
    return "CONVERT ( datetime , '" + sDateString + "' , 120)";
}

/**
 * @public 
 * 
 * @param {String} sAmPmHour
 *
 * @return
 * @properties={typeid:24,uuid:"557A82A3-A2C6-4D5B-A998-D11530320A4C"}
 */
function getNumMinutes(sAmPmHour){
    if(sAmPmHour){
        var nColonPos = sAmPmHour.indexOf(':');
        var sAmOrPm='';
        if(sAmPmHour.toLowerCase().indexOf('am')>-1){
            sAmOrPm = 'am';
        }
        else if(sAmPmHour.toLowerCase().indexOf('pm')>-1){
            sAmOrPm = 'pm';
        }

        if(nColonPos > 0 && sAmOrPm != ''){
            var nHour = parseInt(sAmPmHour.slice(0, nColonPos)); // hour
            var nMinutes = parseInt(sAmPmHour.slice(nColonPos+1, nColonPos+3)); // minutes
            
            if(nHour > 0 && nHour < 13 && nMinutes >= 0 && nMinutes < 60){ // hours + minutes are valid
                if(sAmOrPm == 'am' && nHour == 12){
                    nHour = 0;
                }
                else if(sAmOrPm == 'pm' && nHour != 12){
                    nHour = nHour + 12;
                }
                
                if(nHour >= 0 && nHour < 24 && nMinutes >= 0 && nMinutes < 60){
                    return (nHour * 60) + nMinutes;
                }
            }
        }
    }

    return null;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"6886CC41-1395-4ACB-9636-3D01164400DE"}
 */
function getNumMinutesFromDate(dDate){
    if(dDate){
        return (dDate.getHours() * 60) + dDate.getMinutes();
    }
    else{
        return null; 
    }
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {Boolean} [bZeroTime]
 * 
 * @return {Date}
 * 
 * @properties={typeid:24,uuid:"336851E1-2F87-41A5-BD22-9290A3DEEDB0"}
 */
function copyDate(dDate, bZeroTime){
    var dDate2 = new Date();
    var sYear = dDate.getFullYear();
    var sMonth = dDate.getMonth();
    var sDay = dDate.getDate();
    
    dDate2.setFullYear(sYear, sMonth, sDay);
    
    if(bZeroTime){
        dDate2.setHours(0, 0, 0, 0);
    }
    else{
        var sHours = dDate.getHours();
        var sMin = dDate.getMinutes();
        var sSec = dDate.getSeconds();
        var sMS = dDate.getMilliseconds();
        
        dDate2.setHours(sHours, sMin, sSec, sMS);    
    }
    
    return dDate2;
}

/**
 * @param {String} dDateCol
 *
 * @return
 * @properties={typeid:24,uuid:"576A212B-ED50-4AE2-8B26-B51F1C426633"}
 */
function getDateNoTimeSQL(dDateCol){
    return " DATEADD(DAY, 0, DATEDIFF(DAY, 0, " + dDateCol + ")) ";
}

/**
 * Returns the date name 
 * @param {Date} dDate
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"870E3563-707A-4F2D-8C18-5F19A1F1B2EF"}
 */
function getDateName(dDate){
    
    var sDayName = weekdays[dDate.getDay()];    
    return sDayName;
    
}

/**
 * @param {String|Number|Date} dDate
 * @param {String} sFormat
 * @param {Number} [nTimeFormat] - 24 or 12
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"2A7BBAB9-42B2-4C13-987A-32CFF3E7B2DB"}
 */
function formatDate(dDate, sFormat, nTimeFormat) {
    if (!dDate) {
        return '';
    }

    var retval = dDate.toString();

    switch (sFormat) {
        // 4 digit date
        case 'MM/DD/YYYY':
            retval = formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate()) + '/' + dDate.getFullYear().toString();
            break;
        case 'DD/MM/YYYY':
            retval = formatDay(dDate.getDate()) + '/' + formatMonth(dDate.getMonth()) + '/' + dDate.getFullYear().toString();
            break;
        case 'YYYY/MM/DD':
            retval = dDate.getFullYear().toString() + '/' + formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate());
            break;

        case 'MM-DD-YYYY':
            retval = formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate()) + '-' + dDate.getFullYear().toString();
            break;
        case 'DD-MM-YYYY':
            retval = formatDay(dDate.getDate()) + '-' + formatMonth(dDate.getMonth()) + '-' + dDate.getFullYear().toString();
            break;
        case 'YYYY-MM-DD':
            retval = dDate.getFullYear().toString() + '-' + formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate());
            break;

        case 'MMDDYYYY':
            retval = formatMonth(dDate.getMonth()) + formatDay(dDate.getDate()) + dDate.getFullYear().toString();
            break;
        case 'DDMMYYYY':
            retval = formatDay(dDate.getDate()) + formatMonth(dDate.getMonth()) + dDate.getFullYear().toString();
            break;
        case 'YYYYMMDD':
            retval = dDate.getFullYear().toString() + formatMonth(dDate.getMonth()) + formatDay(dDate.getDate());
            break;

        // 2 digit date
        case 'MM/DD/YY':
            retval = formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate()) + '/' + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'DD/MM/YY':
            retval = formatDay(dDate.getDate()) + '/' + formatMonth(dDate.getMonth()) + '/' + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'YY/MM/DD':
            retval = utils.stringRight(dDate.getFullYear().toString(), 2) + '/' + formatMonth(dDate.getMonth()) + '/' + formatDay(dDate.getDate());
            break;

        case 'MM-DD-YY':
            retval = formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate()) + '-' + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'DD-MM-YY':
            retval = formatDay(dDate.getDate()) + '-' + formatMonth(dDate.getMonth()) + '-' + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'YY-MM-DD':
            retval = utils.stringRight(dDate.getFullYear().toString(), 2) + '-' + formatMonth(dDate.getMonth()) + '-' + formatDay(dDate.getDate());
            break;

        case 'MMDDYY':
            retval = formatMonth(dDate.getMonth()) + formatDay(dDate.getDate()) + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'DDMMYY':
            retval = formatDay(dDate.getDate()) + formatMonth(dDate.getMonth()) + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'YYMMDD':
            retval = utils.stringRight(dDate.getFullYear().toString(), 2) + formatMonth(dDate.getMonth()) + formatDay(dDate.getDate());
            break;

        case 'DD-MMM-YY':
            retval = formatDay(dDate.getDate()) + '-' + getMonthShortName(dDate.getMonth()) + '-' + utils.stringRight(dDate.getFullYear().toString(), 2);
            break;
        case 'DD-MMM-YYYY':
            retval = formatDay(dDate.getDate()) + '-' + getMonthShortName(dDate.getMonth()) + '-' + dDate.getFullYear().toString();
            break;
    }
    
    if (nTimeFormat) {
        var nHours = dDate.getHours();
        var nMins = dDate.getMinutes();
        var sHours;
        var sMins;
        var sAMPM = '';

        if (nMins < 10) {
            sMins = '0' + nMins.toString();
        }
        else {
            sMins = nMins.toString();
        }

        if (nTimeFormat == 24) {
            if (nHours < 10) {
                sHours = '0' + nHours.toString();
            }
            else {
                sHours = nHours.toString();
            }
        }
        else if (nTimeFormat == 12) {
            if (nHours < 12) {
                sAMPM = ' AM';
            }
            else {
                sAMPM = ' PM';
            }
            
            if (nHours == 0) {
                nHours = 12;
            }
            else if (nHours > 12) {
                nHours = nHours % 12;
            }
            
            sHours = nHours.toString();
        }

        retval += ' ' + sHours + ':' + sMins + sAMPM;
    }

    return retval;
}

/**
 * @param {Number} month
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C5DFFB2E-511F-42F4-8739-20C0EE5C6441"}
 */
function formatMonth(month) {
    var retval = '';

    month++;
    if (month < 10) {
        retval = '0' + month.toString();
    }
    else {
        retval = month.toString();
    }

    return retval;
}

/**
 * @param {Number} day
 * @return {String}
 *
 * @properties={typeid:24,uuid:"06203795-E3CC-4484-95D8-15558971E63E"}
 */
function formatDay(day) {
    var retval = '';

    if (day < 10) {
        retval = '0' + day.toString();
    }
    else {
        retval = day.toString();
    }

    return retval;
}

/**
 * @public 
 * 
 * @param {Number} nMonthNum
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"155F5119-DF91-488C-B735-CC887285F051"}
 */
function getMonthShortName(nMonthNum) {
    switch (nMonthNum) {
        case 0: return "Jan";
        case 1: return "Feb";
        case 2: return "Mar";
        case 3: return "Apr";
        case 4: return "May";
        case 5: return "Jun";
        case 6: return "Jul";
        case 7: return "Aug";
        case 8: return "Sep";
        case 9: return "Oct";
        case 10: return "Nov";
        case 11: return "Dec";
        default: return null;
    }
}

/**
 * @public 
 * 
 * @param {Number} nMonthNum
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"6033808B-42AF-46C1-A11C-553EAB720A58"}
 */
function getMonthLongName(nMonthNum) {
    switch (nMonthNum) {
        case 0: return i18n.getI18NMessage("avanti.lbl.january");
        case 1: return i18n.getI18NMessage("avanti.lbl.february");
        case 2: return i18n.getI18NMessage("avanti.lbl.march");
        case 3: return i18n.getI18NMessage("avanti.lbl.april");
        case 4: return i18n.getI18NMessage("avanti.lbl.may");
        case 5: return i18n.getI18NMessage("avanti.lbl.june");
        case 6: return i18n.getI18NMessage("avanti.lbl.july");
        case 7: return i18n.getI18NMessage("avanti.lbl.august");
        case 8: return i18n.getI18NMessage("avanti.lbl.september");
        case 9: return i18n.getI18NMessage("avanti.lbl.october");
        case 10: return i18n.getI18NMessage("avanti.lbl.november");
        case 11: return i18n.getI18NMessage("avanti.lbl.december");
        default: return null;
    }
}

/**
 * adds nMinutes to dDate and adjusts if DST change has caused new date to be different from what we expected.
 * sometimes if we add 24h to nov 4 00:00 we need the result to be nov 5 00:00, even tho really 24h later
 * is nov 23:00 because we fell back an hour at 2am 
 * 
 * @public 
 * 
 * @param {Date} dDate
 * @param {Number} nMinutes
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"6C99A33A-0D4F-498C-9458-AF4F733EDD44"}
 */
function addMinutesNoDSTChange(dDate, nMinutes) {
	var dNewDate = dDate;
	
	if (dDate && nMinutes && scopes.avMath.isNumber(nMinutes)) {
		dNewDate = plugins.DateUtils.addMinutes(dDate, nMinutes);
		var sDSTChange = getClockChangeForDST(dDate, dNewDate);

		// DST - we went ahead an hour - subtract the hour
		if (sDSTChange == DST_CHANGE.spring_forward) {
			dNewDate = plugins.DateUtils.addMinutes(dNewDate, -60);
		}
		// DST - we went back an hour - add the hour back
		else if (sDSTChange == DST_CHANGE.fall_back) {
			dNewDate = plugins.DateUtils.addMinutes(dNewDate, 60);
		}
	}
	
    return dNewDate;
}

/**
 * returns DST_CHANGE.spring_forward if the clock has moved forwards an hour between dDate1 and dDate2 because of DST
 * returns DST_CHANGE.fall_back if the clock has moved back an hour between dDate1 and dDate2 because of DST
 * returns DST_CHANGE.none if neither of the above is true
 * 
 * @public 
 * 
 * @param {Date} dDate1
 * @param {Date} dDate2
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"52FD3A89-6DD2-4B33-AE83-802474F73DD2"}
 */
function getClockChangeForDST(dDate1, dDate2) {
    // if you're west of london (GMT), then offsets are are positive.
    // if you're east of london (GMT), then offsets are are negative
    // if you're in london (GMT), then DST date offset is negative and non-DST date offset is zero
    var nOffsetDate1 = dDate1.getTimezoneOffset();
    var nOffsetDate2 = dDate2.getTimezoneOffset();
    
    // eg. offset moves from 300 to 240 in toronto - minus 60
    // eg. offset moves from -120 to -180 in athens - minus 60
    // eg. offset moves from 0 to -60 in london - minus 60
    if (nOffsetDate1 == nOffsetDate2 + 60) {
        return DST_CHANGE.spring_forward;
    }
    // eg. offset moves from 240 to 300 in toronto - plus 60
    // eg. offset moves from -180 to -120 in athens - plus 60
    // eg. offset moves from -60 to 0 in london - plus 60
    else if (nOffsetDate2 == nOffsetDate1 + 60) {
        return DST_CHANGE.fall_back;
    }
    else {
        return DST_CHANGE.none;
    }
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {Number} nDays
 * @param {Boolean} [bSetTimeToStartOfDay]
 * @param {Boolean} [bSetTimeToEndOfDay]
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"44A62BB7-8FE0-4651-B1DA-B325FD2486FD"}
 */
function addDaysNoDSTChange(dDate, nDays, bSetTimeToStartOfDay, bSetTimeToEndOfDay) {
    var dNewDate = addMinutesNoDSTChange(dDate, nDays * 24 * 60);
    
    if (bSetTimeToStartOfDay) {
        dNewDate.setHours(0, 0, 0, 0);
    }
    else if (bSetTimeToEndOfDay) {
        dNewDate.setHours(23, 59, 59, 999)
    }
    
    return dNewDate;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"76E4A2E0-127F-471A-AA2F-9A243D088A51"}
 */
function getNumDaysInMonth(dDate) {
	var nMonth = dDate.getMonth();
	var nYear = dDate.getFullYear();
	
	switch (nMonth) {
	    case 0: return 31;
	    case 1: 
			if (isLeapYear(nYear)) {
				return 29;
			}
			else {
				return 28;
			}
	    case 2: return 31;
	    case 3: return 30;
	    case 4: return 31;
	    case 5: return 30;
	    case 6: return 31;
	    case 7: return 31;
	    case 8: return 30;
	    case 9: return 31;
	    case 10: return 30;
	    case 11: return 31;
	    default: return null;
	}
}

/**
 * @public
 *  
 * @param {Number} tiYear
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"CF6F25C7-F35B-455B-A4CB-980AC8373439"}
 */
function isLeapYear(tiYear){
	return (tiYear % 4 == 0 && tiYear % 100 != 0) || tiYear % 400 == 0
}

/**
 * returns a date converted to string (YYYYMMDD format)
 * 
 * @public 
 * 
 * @param {Date} dDate
 * @param {Boolean} [bConvertToServerTime]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"26349DEE-6A0A-4B5D-BDF1-6ECEE97F90CD"}
 */
function getDateString(dDate, bConvertToServerTime){
	if (bConvertToServerTime) {
		dDate = convertClientTimeToServerTime(dDate);
	}
	
	var y = dDate.getFullYear().toString();
	var m = (dDate.getMonth() + 1).toString();
	var d = dDate.getDate().toString();
	
	if(m.length == 1){
	    m = "0" + m;
	}

	if(d.length == 1){
        d = "0" + d;
    }
	
	var sDateString = y + m + d;
	
	return sDateString;
}

/**
 * @param {Date} dDate
 * @param {Boolean} [bScheduleFormat] - "YYYYMMDD.HHNN"
 * @param {Boolean} [bScheduleFormatToMS] - "YYYYMMDD.HHNNSSMMM"
 *
 * @return
 * @properties={typeid:24,uuid:"DEBA02FA-3199-4159-9B7D-71186B52DCA1"}
 */
function getDateTimeString(dDate, bScheduleFormat, bScheduleFormatToMS) {
	var sDateTime = "";
	
	if (dDate) {
		var sYear = dDate.getFullYear().toString();
		var sMonth = (dDate.getMonth() + 1).toString(); 
		var sDay = dDate.getDate().toString();
		var sHour = dDate.getHours().toString();
		var sMinute = dDate.getMinutes().toString();
		var sSecond = dDate.getSeconds().toString();
		var sMilliSecond = dDate.getMilliseconds().toString();
		
	    if (sMonth.length == 1) {
	    	sMonth = "0" + sMonth;
	    }
	    if (sDay.length == 1) {
	    	sDay = "0" + sDay;
	    }
	    if (sHour.length == 1) {
	    	sHour = "0" + sHour;
	    }
	    if (sMinute.length == 1) {
	    	sMinute = "0" + sMinute;
	    }
	    if (sSecond.length == 1) {
	    	sSecond = "0" + sSecond;
	    }
	    if (sMilliSecond.length == 1) {
	    	sMilliSecond = "00" + sMilliSecond;
	    }
	    else if (sMilliSecond.length == 2) {
	    	sMilliSecond = "0" + sMilliSecond;
	    }
	    
		if (bScheduleFormatToMS) {
			sDateTime = sYear + sMonth + sDay + "." + sHour + sMinute + sSecond + sMilliSecond;
		}
		else if (bScheduleFormat) {
			sDateTime = sYear + sMonth + sDay + "." + sHour + sMinute;     
		}
		else {
			sDateTime = sYear + sMonth + sDay + sHour + sMinute + sSecond + sMilliSecond;     
		}
	}
	
	return sDateTime;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {String} - returns a datetime string in this format: 2021-10-27T12:43:51.321Z
 *
 * @properties={typeid:24,uuid:"C30ECAF1-2E50-4C39-BC21-FBF6349F4BC4"}
 */
function getFedexDateTimeFormat(dDate) {
	var sDateTime = null;
	
	if (dDate) {
		var sYear = dDate.getFullYear().toString();
		var sMonth = (dDate.getMonth() + 1).toString(); 
		var sDay = dDate.getDate().toString();
		var sHour = dDate.getHours().toString();
		var sMinute = dDate.getMinutes().toString();
		var sSecond = dDate.getSeconds().toString();
		var sMilliSecond = dDate.getMilliseconds().toString();
		
	    if (sMonth.length == 1) {
	    	sMonth = "0" + sMonth;
	    }
	    if (sDay.length == 1) {
	    	sDay = "0" + sDay;
	    }
	    if (sHour.length == 1) {
	    	sHour = "0" + sHour;
	    }
	    if (sMinute.length == 1) {
	    	sMinute = "0" + sMinute;
	    }
	    if (sSecond.length == 1) {
	    	sSecond = "0" + sSecond;
	    }

	    if (sMilliSecond.length == 1) {
	    	sMilliSecond = "00" + sMilliSecond;
	    }
	    else if (sMilliSecond.length == 2) {
	    	sMilliSecond = "0" + sMilliSecond;
	    }
		
		// 2021-10-27T12:43:51.321Z
		sDateTime = sYear + "-" + sMonth + "-" + sDay + "T" + sHour + ":" + sMinute + ":" + sSecond + "." + sMilliSecond;     
	}
	
	return sDateTime;
}

/**
 * @public 
 * 
 * @return {Number} - the number of minutes server time is behind UTC/GMT 
 * 
 * @properties={typeid:24,uuid:"BA3FB24A-6BE5-476E-B82B-F65416D4867A"}
 */
function getServerTimeZoneOffset() {
	return (Packages.java.util.Calendar.getInstance().getTimeZone().getRawOffset() / 1000 / 60) * -1;
}


/**
 * 
 * @param offsetInMinutes
 *
 * @return
 * @properties={typeid:24,uuid:"64CBEBC5-3453-4AC8-B51C-1F275C1D8CD4"}
 */
function getTimeZoneFromOffset(offsetInMinutes) {
	var timeZones = {
        '-720': 'Etc/GMT+12',
        '-570': 'Pacific/Marquesas',
        '-210': 'America/St_Johns',
        '-180': 'America/Argentina/Buenos_Aires',
        '-120': 'America/Noronha',
        '-60': 'Atlantic/Azores',
        '210': 'Asia/Tehran',
        '270': 'Asia/Kabul',
        '300': 'Canada/Eastern',
        '330': 'Asia/Kolkata',
        '345': 'Asia/Kathmandu',
        '360': 'Canada/Central',
        '390': 'Asia/Yangon',
        '420': 'Asia/Bangkok',
        '480': 'Asia/Shanghai',
        '540': 'Asia/Tokyo',
        '570': 'Australia/Darwin',
        '600': 'Australia/Sydney',
        '630': 'Australia/Adelaide',
        '660': 'Pacific/Noumea',
        '720': 'Pacific/Auckland',
        '780': 'Pacific/Chatham',
        '840': 'Pacific/Kiritimati',
        '-240': 'Canada/Atlantic',
        '-360': 'Canada/Central',
        '-300': 'Canada/Eastern',
        '-420': 'Canada/Mountain',
        '-150': 'Canada/Newfoundland',
        '-480': 'Canada/Pacific',
          '60': 'Europe/Central European Time',
         '120': 'Europe/Eastern European Time',
           '0': 'Europe/Greenwich',
         '180': 'Europe/Moscow',
         '240': 'Europe/Samara',
        '-540': 'US/Alaska',
        '-600': 'US/Hawaii',
        '-660': 'US/Samoa'
    };

    return timeZones[offsetInMinutes] || 'Canada/Central';
}

/**
 * @public 
 * 
 * @return {Number} - the number of minutes client time is behind UTC/GMT 
 * 
 * @properties={typeid:24,uuid:"F647B1B0-45EC-40F1-89C1-B64C9C338CD2"}
 */
function getClientTimeZoneOffset() {
	// sl-27929 - this is set when user logs in, and in createNewOrder() for xml order, but added this check to make sure 
	if (!globals.avUserTimeZone) {
		globals.avUserTimeZone = Packages.java.util.TimeZone.getTimeZone(i18n.getCurrentTimeZone());
	}
	
	// sl-27599 - i had been using application.getTimeStamp().getTimezoneOffset(), but it wasnt working. i found that application.getTimeStamp() 
	// was returning the server time, despite the documentation clearly saying it should return client time. globals.avUserTimeZone.getRawOffset()
	// does return actual client time offset
	return (globals.avUserTimeZone.getRawOffset() / 1000 / 60) * -1;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"622403CF-6007-42C1-81B3-D8CCFC212C00"}
 */
function convertClientTimeToServerTime(dDate) {
	if (dDate) {
		var nServerTimeZoneOffset = getServerTimeZoneOffset();
		var nClientTimeZoneOffset = getClientTimeZoneOffset();
		var nServerClientTimeZoneOffsetDiff = nServerTimeZoneOffset - nClientTimeZoneOffset;
		
		if (nServerClientTimeZoneOffsetDiff != 0) {
			dDate = plugins.DateUtils.addMinutes(dDate, nServerClientTimeZoneOffsetDiff * -1);
		}
	}
	
	return dDate;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"3F760DC5-DBD0-4F86-AAB2-4C20DF2D5900"}
 */
function convertServerTimeToClientTime(dDate) {
	if (dDate) {
		var nServerTimeZoneOffset = getServerTimeZoneOffset();
		var nClientTimeZoneOffset = getClientTimeZoneOffset();
		var nServerClientTimeZoneOffsetDiff = nServerTimeZoneOffset - nClientTimeZoneOffset;
		
		if (nServerClientTimeZoneOffsetDiff != 0) {
			dDate = plugins.DateUtils.addMinutes(dDate, nServerClientTimeZoneOffsetDiff);
		}
	}
	
	return dDate;
}

/**
 * @private 
 * 
 * @param {String} sFunction
 * @param {Date} dOldDate
 * @param {Date} dNewDate
 * @param {Number} nServerTimeZoneOffset
 * @param {Number} nClientTimeZoneOffset
 *
 * @properties={typeid:24,uuid:"734E26BF-130A-46EE-B538-FEFD8C022B8D"}
 */
function logTZConversion(sFunction, dOldDate, dNewDate, nServerTimeZoneOffset, nClientTimeZoneOffset) {
	var sInfo = sFunction + ": " +
				"dOldDate: " + dOldDate + ", " +
				"dNewDate: " + dNewDate + ", " +
				"nServerTimeZoneOffset: " + nServerTimeZoneOffset + ", " +
				"nClientTimeZoneOffset: " + nClientTimeZoneOffset; 
				
	if (nServerTimeZoneOffset != nClientTimeZoneOffset) {
		sInfo += ". CONVERSION!"
	}
	
	scopes.avUtils.devLog("SL-27850", sInfo, true);
}

/**
 * This function will set the time to a date
 * @param {Date} dDate
 * @param {Number} nHours
 * @param {Number} nMinutes
 * @param {Number} nSeconds
 * @param {number} [nMilliseconds]
 *
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"CF94B218-8D6E-40EE-B3BD-04E7825CBDDD"}
 */
function setTimeToDate(dDate, nHours, nMinutes, nSeconds, nMilliseconds) {
	dDate.setHours(nHours);
	dDate.setMinutes(nMinutes);
	dDate.setSeconds(nSeconds);
	
	if (nMilliseconds > 0) {
		dDate.setMilliseconds(nMilliseconds);
	}
	else {
		dDate.setMilliseconds(0);
	}
	
	return dDate;
}

/**
 * Add minutes to existing datetime
 * @param {Date} dDate
 * @param {Number} nMinutes
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"B78776EB-1C6B-44C4-A62F-8AE378BC0E1F"}
 */
function addMinutesToDateTime(dDate, nMinutes) {
	var newDate = new Date(dDate.getTime());
	newDate.setMinutes(newDate.getMinutes() + nMinutes);
	
	return newDate;
}

/**
 * Subtract minutes from existing datetime
 * @param {Date} dDate
 * @param {Number} nMinutes
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"F0142AAB-691B-425D-9CF1-805555C7BC1B"}
 */
function subtractMinutesFromDateTime(dDate, nMinutes) {
	var newDate = new Date(dDate.getTime());
	newDate.setMinutes(newDate.getMinutes() - nMinutes);
	
	return newDate;
}


/**
 * Get number of minutes from date time.
 * @param dDate
 *
 * @return
 * @properties={typeid:24,uuid:"A66453C2-061D-49B0-9EFE-F945BB78B922"}
 */
function getMinutes(dDate){
	return dDate.getHours() * 60 + dDate.getMinutes();
}

/**
 * @public 
 * 
 * @param {String} sDate
 * @param {String} sDateFormat
 * @param {Number} [nDoTimeZoneConversion]
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"89787F97-F1C1-44F4-B426-D808554DC5FD"}
 */
function getDateFromString(sDate, sDateFormat, nDoTimeZoneConversion) {
	/**@type {Date} */
	var dDate = null;
	
	if (sDate && sDateFormat) {
		// if an error occurs in date formatting just return null for date
		try {
			sDateFormat = scopes.avText.trim(sDateFormat);
			
			// EG. yyyy-MM-ddTHH:mm:ss, utils.parseDate() expects single quotes around the T
			if (sDateFormat.includes("T") && !sDateFormat.includes("'T'")) {
				sDateFormat = sDateFormat.replace("T", "'T'")
			}
			
			var nTimeZoneOffsetInFormat = getTimeZoneOffsetFromDateString(sDateFormat);
			
			// utils.parseDate() doesnt support use of timezone offset (EG: '+05:00') in format string so remove it
			if (nTimeZoneOffsetInFormat != null) {
				sDateFormat = sDateFormat.substr(0, sDateFormat.length - 6);
			}
			
			dDate = utils.parseDate(sDate, sDateFormat);
			
			// we only go down to the minute
			if (dDate) {
				dDate.setSeconds(0, 0);
			}
			
			if (nDoTimeZoneConversion) {
				var nXMLTimeZoneOffset = getTimeZoneOffsetFromDateString(sDate);
				
				if (nXMLTimeZoneOffset != null) {
					var nClientTimeZoneOffset = getClientTimeZoneOffset() * -1;
					var nDiff = nClientTimeZoneOffset - nXMLTimeZoneOffset; 

					if (nDiff != 0) {
						dDate = plugins.DateUtils.addMinutes(dDate, nDiff);
					}
				}
			}
		}
		catch (ex) {}
	}

	return dDate;
}

/**
 * @public 
 * 
 * @param {String} sDate
 * 
 * @return {Number} -  the timezone offset in minutes or null if no offset present (zero is a valid offset)
 * 
 * @properties={typeid:24,uuid:"067BCB62-CADD-4217-90F4-331B5183D9C3"}
 */
function getTimeZoneOffsetFromDateString(sDate) {
	var nOffset = null;
	
	if (sDate) {
		sDate = scopes.avText.trim(sDate);
		
		var sTimeZoneOffset = scopes.avText.getLastNumChars(sDate, 6);
		var nSign = sTimeZoneOffset.charAt(0);
		
		if ((nSign == "+" || nSign == "-") && sTimeZoneOffset.includes(":")) {
			sTimeZoneOffset = sTimeZoneOffset.substr(1); // remove +/-
			
			var aHHNN = sTimeZoneOffset.split(":");
			
			if (aHHNN.length == 2) {
				var nHours = parseInt(aHHNN[0]);
				var nMins = parseInt(aHHNN[1]);

				nOffset = (nHours * 60) + nMins;
				
				if (nSign == "-") {
					nOffset = nOffset * -1;
				}
			}
		}
	}
	
	return nOffset;
}

/**
 * @public 
 * 
 * @param {String} sTimeZone
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"ECFC998E-3E6A-4914-8F12-747D64B52F3D"}
 */
function getTimeZoneOffset(sTimeZone) {
	var nOffset = null;
	
	if (sTimeZone) {
		var oTimeZone = Packages.java.util.TimeZone.getTimeZone(sTimeZone);
		
		if (oTimeZone) {
			nOffset = oTimeZone.getRawOffset(); 
			
			if (nOffset) {
				nOffset = nOffset / 60000;
			}
		}
	}
	
	return nOffset;
}

/**
 * @public 
 * 
 * @return {Number}
 * 
 * @properties={typeid:24,uuid:"948928F7-5628-484F-877B-72D1FD5717B9"}
 */
function getOrgTimeZoneOffset() {
	var nOffset = null;
	
	if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id) {
		nOffset = getTimeZoneOffset(_to_sys_organization.org_timezone_id);
	}
	
	return nOffset;
}

/**
 * Convert the date to server time zone before saving so that it maintains the client time zone
 *
 * @param databaseValue The database value.
 * @param {String} dbType The type of the database column. Can be one of "TEXT", "INTEGER", "NUMBER", "DATETIME" or "MEDIA".
 *
 * @return {Object} the displayed value.
 *
 * @properties={typeid:24,uuid:"745E643B-F53F-45AE-AA4C-46D44E43AC93"}
 */
function convertDBDateToOrgTime(databaseValue, dbType) {
	if (databaseValue) {
		var nClientTimeZoneOffset = getClientTimeZoneOffset() * -1;
		var nOrgTimeZoneOffset = getOrgTimeZoneOffset();
		var nDiff = nOrgTimeZoneOffset - nClientTimeZoneOffset;

		if (nDiff != 0) {
			databaseValue = plugins.DateUtils.addMinutes(databaseValue, nDiff);
		}
	}
	
	return databaseValue;
}

/**
 * Convert the date to client time zone before display so that it maintains the client time zone.
 *
 * @param displayedValue The displayed value.
 * @param {String} dbType The type of the database column. Can be one of "TEXT", "INTEGER", "NUMBER", "DATETIME" or "MEDIA".
 *
 * @return {Object} the database value.
 *
 * @properties={typeid:24,uuid:"A1E7AD29-FE13-4BCE-94B8-0B93FC67D65C"}
 */
function convertOrgDateToDBTime(displayedValue, dbType) {
	if (displayedValue) {
		var nClientTimeZoneOffset = getClientTimeZoneOffset() * -1;
		var nOrgTimeZoneOffset = getOrgTimeZoneOffset();
		var nDiff = nOrgTimeZoneOffset - nClientTimeZoneOffset;

		if (nDiff != 0) {
			displayedValue = plugins.DateUtils.addMinutes(displayedValue, nDiff * -1);
		}
	}
	
	return displayedValue;
}
