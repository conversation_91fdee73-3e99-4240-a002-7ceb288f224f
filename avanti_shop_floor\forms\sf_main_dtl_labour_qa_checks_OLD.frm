customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sch_milestone_group",
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
cssPosition:"30,329,-1,229,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"229",
right:"329",
top:"30",
width:"200"
},
dataProviderID:"qa_check_pos_2",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:6,
visible:true
},
name:"qa_check_field_2",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"09B144AA-DC98-4B1B-99FB-************"
},
{
partType:2,
typeid:19,
uuid:"0D515F38-2894-4CAB-BC6C-D3BCB91F3D37"
},
{
cssPosition:"5,529,-1,202,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"202",
right:"529",
top:"5",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_1",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"76715ABC-5F5C-417E-9365-D629DAFA0BD3"
},
{
cssPosition:"5,329,-1,229,199,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"229",
right:"329",
top:"5",
width:"199"
},
dataProviderID:"qa_check_pos_1",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:5,
visible:true
},
name:"qa_check_field_1",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"807E8720-0EDB-4F79-A835-FB21CDEC81AB"
},
{
height:135,
partType:8,
typeid:19,
uuid:"93626013-12BD-48AE-98EC-F6BA38BD57C0"
},
{
height:81,
partType:5,
typeid:19,
uuid:"BC2E9ED5-8737-44EC-82A2-014C8D8C3885"
},
{
cssPosition:"104,-1,10,583,80,22",
json:{
cssPosition:{
bottom:"10",
height:"22",
left:"583",
right:"-1",
top:"104",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"5A5ED3B0-8A3C-4F73-B02C-9553B3A86DD9",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_77401B9F",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"BD76B067-A90B-4513-AA72-EE2E21CBAAE9"
},
{
cssPosition:"30,529,-1,202,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"202",
right:"529",
top:"30",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
enabled:true,
styleClass:"checkbox_bts text-center",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_2",
styleClass:"checkbox_bts text-center",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"C2AF799F-9429-4485-9FC3-286190489D07"
},
{
cssPosition:"55,529,-1,202,26,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"202",
right:"529",
top:"55",
width:"26"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
enabled:true,
styleClass:"text-center checkbox_bts",
tabSeq:0,
text:"",
visible:true
},
name:"qa_check_3",
styleClass:"text-center checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"DB36F9B1-7DB6-4D2B-9909-4CA00B4C4B90"
},
{
cssPosition:"55,329,-1,229,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"229",
right:"329",
top:"55",
width:"200"
},
dataProviderID:"qa_check_pos_3",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:7,
visible:true
},
name:"qa_check_field_3",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E6EDF35D-87B1-4B8C-B85F-0C5503853417"
},
{
cssPosition:"104,-1,10,668,80,22",
json:{
cssPosition:{
bottom:"10",
height:"22",
left:"668",
right:"-1",
top:"104",
width:"80"
},
enabled:true,
formIndex:0,
onActionMethodID:"37EEF227-32AB-4C88-9028-A8091B75EE23",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_B3929A10",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"EB0BB7DA-8991-4E71-A51A-02F27F4B2612"
},
{
cssPosition:"5,565,-1,5,187,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"565",
top:"5",
width:"187"
},
dataProviderID:"sch_milestone_group_to_sys_cost_centre.cc_desc",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"cost_centre",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F4301A1B-D10D-4C66-95AA-52A8B717804A"
}
],
name:"sf_main_dtl_labour_qa_checks_OLD",
onShowMethodID:"E768B7E5-4F39-4A09-8D91-DE95FEC00941",
size:"757,136",
styleName:"Avanti",
typeid:3,
uuid:"62DFE3E3-23CE-4C50-AD21-8C6BA0D9FE23",
view:1