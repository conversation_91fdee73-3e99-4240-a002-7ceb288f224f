customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_task_worktype_section",
extendsID:"67CFC51B-FCB5-406A-8E36-162B08C9F1AA",
items:[
{
cssPosition:"10,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
labelFor:"fldSheetSize",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sheetSize2",
visible:true
},
name:"component_EDECD4F9",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"00178161-3635-45B0-9447-123344351FFC"
},
{
cssPosition:"106,-1,-1,353,142,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"353",
right:"-1",
top:"106",
width:"142"
},
enabled:true,
labelFor:"fldBleedL",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedLLong",
visible:true
},
name:"component_B058733A",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0657B652-FAE0-4E18-81D0-E38A65F8E9F1"
},
{
cssPosition:"226,-1,-1,395,114,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"395",
right:"-1",
top:"226",
width:"114"
},
enabled:true,
labelFor:"fldForms",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.#Forms",
visible:true
},
name:"lblForms",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"086D99FA-7F5A-4139-90DF-00DD6DA0D132"
},
{
cssPosition:"354,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"354",
width:"22"
},
enabled:true,
onActionMethodID:"AD98F74E-9DE5-4D9E-9FC7-DFF2352DED00",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_papergrade_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"11295E0D-55F5-4F77-BF25-81384BD3BFA2"
},
{
cssPosition:"202,-1,-1,10,191,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"202",
width:"191"
},
enabled:true,
labelFor:"worktypesection_roll_to_sheet",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.convertRollToSheet",
visible:true
},
name:"component_95C0E305",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"25BC7A7B-8182-4E93-8647-DF3B604151E5"
},
{
cssPosition:"82,-1,-1,231,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"231",
right:"-1",
top:"82",
width:"60"
},
dataProviderID:"worktypesection_over_model_min_l",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldModelMinL",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"26FDEA5C-BE8B-4041-B7A2-937430B86FF5"
},
{
cssPosition:"130,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"130",
width:"140"
},
enabled:true,
labelFor:"fldGridX",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#AcrossSheet",
visible:true
},
name:"lblAcrossSheet",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"28E695D2-D7BA-472F-94A9-CFDF4ADCD0E4"
},
{
cssPosition:"404,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"404",
width:"192"
},
enabled:true,
labelFor:"fldSelectedPaper",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperSelected",
visible:true
},
name:"lblSubstrateSelected",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"29FC5DA1-BECB-4589-B476-08F9D2FB32D3"
},
{
cssPosition:"202,-1,-1,394,115,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"394",
right:"-1",
top:"202",
width:"115"
},
enabled:true,
labelFor:"fldPaperWeight",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.netWeight",
visible:true
},
name:"lblPaperWeight",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2EC73DE6-1F13-435B-A29C-8CBFB29F8D68"
},
{
cssPosition:"379,-1,-1,606,82,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"606",
right:"-1",
top:"379",
width:"82"
},
dataProviderID:"paper_color",
editable:true,
enabled:true,
onDataChangeMethodID:"EC711BC2-202C-4CB6-8443-7475BE9638F7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2BD1EAC1-E527-4981-A0CC-40CBE54F25D0",
visible:true
},
name:"paper_color",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2ED88079-AFCB-4ED9-AB13-F4A1ECAF296E"
},
{
cssPosition:"130,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"130",
width:"60"
},
dataProviderID:"_iGridX",
editable:true,
enabled:true,
onDataChangeMethodID:"68CFBF34-756B-41DC-8F22-4935260A21DE",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldGridX",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3145CC74-8EEA-46CF-9B68-45ADC70A0BD4"
},
{
cssPosition:"329,-1,-1,605,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"605",
right:"-1",
top:"329",
width:"60"
},
dataProviderID:"sa_task_worktype_section_to_in_item_paper.paper_m_weight",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"component_50015356",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3213498A-8621-43E7-A9A4-342C01CF2097"
},
{
cssPosition:"154,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"154",
width:"140"
},
enabled:true,
labelFor:"fldGridY",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#AlongSheet",
visible:true
},
name:"lblAlongSheet",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3779AB88-1A55-45EA-94D3-94C209B35991"
},
{
cssPosition:"404,-1,-1,206,228,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"404",
width:"228"
},
dataProviderID:"item_id",
enabled:true,
onDataChangeMethodID:"D4D48823-2F68-4528-AC82-8DF5A6AD4BF0",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"FB35F7AF-285F-44A5-B1D4-CDCBF8C21C89",
visible:true
},
name:"fldSelectedPaper",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3AFA7DDF-CF5F-47F2-BB2C-C877D0B4E2C8"
},
{
cssPosition:"379,-1,-1,10,166,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"379",
width:"166"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperGradeBrand",
visible:true
},
name:"lblPaperBrand",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"45F35A1A-C67F-4B3D-9752-01547B490E43"
},
{
cssPosition:"34,-1,-1,216,15,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"216",
right:"-1",
top:"34",
width:"15"
},
enabled:true,
labelFor:"fldFinishSizeL",
styleClass:"text-center label_bts",
tabSeq:-1,
text:"X",
visible:true
},
name:"x1",
styleClass:"text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4B6B943A-918D-49AA-B02D-6CD9E42C70C2"
},
{
cssPosition:"251,-1,-1,10,191,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"251",
width:"191"
},
enabled:true,
labelFor:"worktypesection_across_roll",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sheetsAcrossRoll",
visible:true
},
name:"component_987C35A4",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"52F2194F-EE16-411C-AEB1-E9BE5C825B66"
},
{
cssPosition:"329,-1,-1,470,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"329",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.mWeight",
visible:true
},
name:"paper_m_weight_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"549F20FB-AF9D-4926-B2E7-923FD3076610"
},
{
cssPosition:"58,-1,-1,231,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"231",
right:"-1",
top:"58",
width:"60"
},
dataProviderID:"worktypesection_flat_size_l",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldFlatSizeL",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"559E5A66-7B8D-45FB-877C-894EA1CCA42C"
},
{
cssPosition:"10,-1,-1,155,102,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"10",
width:"102"
},
dataProviderID:"worktypesection_paper_size",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldSheetSize",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"5617DC5B-41B2-4BF4-907D-E51760C2D75C"
},
{
cssPosition:"178,570,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"570",
top:"178",
width:"140"
},
enabled:true,
labelFor:"fldPages",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#pages",
visible:true
},
name:"lblPages",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"58E2F0C6-8284-487B-A883-2A84EB093E60"
},
{
cssPosition:"329,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"329",
width:"22"
},
enabled:true,
onActionMethodID:"AD98F74E-9DE5-4D9E-9FC7-DFF2352DED00",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_brand_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5C254CC9-303A-47DF-8716-B7A20040186B"
},
{
cssPosition:"250,-1,-1,206,71,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"250",
width:"71"
},
dataProviderID:"worktypesection_across_roll",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"worktypesection_across_roll",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"6136834F-4E9C-4214-B46A-F0102728AF54"
},
{
cssPosition:"379,-1,-1,470,130,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"379",
width:"130"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.item_color",
visible:true
},
name:"lblPaperColour",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"64072187-FF07-4B32-8278-8F393FDA7E42"
},
{
cssPosition:"379,-1,-1,271,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"271",
right:"-1",
top:"379",
width:"140"
},
dataProviderID:"paperbrand_name",
editable:true,
enabled:true,
onDataChangeMethodID:"EC711BC2-202C-4CB6-8443-7475BE9638F7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F108CB21-21BE-4008-825A-FFD7BA9906D3",
visible:true
},
name:"paperbrand_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"66BEBF19-E5FF-42D3-840B-5245BA721F11"
},
{
cssPosition:"250,-1,-1,395,114,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"395",
right:"-1",
top:"250",
width:"114"
},
enabled:true,
labelFor:"fldSheets",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.pressSheets",
visible:true
},
name:"lblSheets",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6D301EFF-06FC-4526-88E8-0A92414DE205"
},
{
cssPosition:"58,570,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"570",
top:"58",
width:"140"
},
enabled:true,
labelFor:"fldFlatSizeW",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.flatSize",
visible:true
},
name:"lblFlatSize",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"71019A7C-194A-4219-8A8C-8FFA0D2D7E02"
},
{
cssPosition:"379,-1,-1,206,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"379",
width:"60"
},
dataProviderID:"paper_weight",
editable:true,
enabled:true,
onDataChangeMethodID:"EC711BC2-202C-4CB6-8443-7475BE9638F7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"5FAFEDF3-ECAB-4C10-BBE8-88AA4F872913",
visible:true
},
name:"paper_weight",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"73750004-4C2C-4536-A721-4E707E5D95AE"
},
{
cssPosition:"58,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"58",
width:"60"
},
dataProviderID:"worktypesection_flat_size_w",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldFlatSizeW",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"74FD88F7-D7B4-4FC5-AD49-CF12DE6AB791"
},
{
cssPosition:"354,-1,-1,590,13,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"590",
right:"-1",
top:"354",
width:"13"
},
enabled:true,
styleClass:"over_warning label_bts text-center",
tabSeq:-1,
text:"!",
visible:true
},
name:"lblCaliperOverride",
styleClass:"over_warning label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7727F924-306B-4B4E-ACCB-6261BEE19DE3"
},
{
cssPosition:"31,-1,-1,522,65,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"522",
right:"-1",
top:"31",
width:"65"
},
dataProviderID:"worktypesection_fin_area",
editable:true,
enabled:true,
format:"#.####",
onDataChangeMethodID:"E5B5F94F-5746-485E-8368-77141F53F34A",
selectOnEnter:true,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:false
},
name:"fldFinishedSizeAreaCalc",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"78D11095-4E00-4116-8213-07723591A69A",
visible:false
},
{
cssPosition:"354,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"354",
width:"192"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperGrade",
visible:true
},
name:"papergrade_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7CB4F8EA-09D6-4115-AA7F-1590F3933B64"
},
{
cssPosition:"354,-1,-1,605,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"605",
right:"-1",
top:"354",
width:"60"
},
dataProviderID:"_nCaliper",
editable:true,
enabled:true,
format:"0.####|0.####",
onDataChangeMethodID:"B47D6623-73EB-4BAF-92D8-9085BAD4D6EA",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"ordrevds_generic_caliper",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7D1C85BE-6DB0-4585-9CF5-4371C62B3DD6"
},
{
cssPosition:"34,-1,-1,231,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"231",
right:"-1",
top:"34",
width:"60"
},
dataProviderID:"worktypesection_trim_size_l",
editable:true,
enabled:true,
onDataChangeMethodID:"C9D5DE6F-E64C-4594-94AB-A96E518EC8FC",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldFinishSizeL",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7D6AB73E-2595-4E83-9A4C-EB44F386D079"
},
{
cssPosition:"82,570,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"570",
top:"82",
width:"140"
},
enabled:true,
labelFor:"fldModelMinW",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.modelMinSize",
visible:true
},
name:"lblModelMinSize",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"85F03E0C-442D-48CD-9A5B-2EDD0F067DB9"
},
{
cssPosition:"58,225,-1,353,142,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"353",
right:"225",
top:"58",
width:"142"
},
enabled:true,
labelFor:"fldBleedSize",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedSizeLong",
visible:true
},
name:"component_F48F583A",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"864A4F47-5BEC-4E30-961B-0BD135AC0E35"
},
{
cssPosition:"82,-1,-1,522,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"82",
width:"50"
},
dataProviderID:"worktypesection_bleed_w",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F76DF013-FB68-40C3-896F-7B5F75E29301",
visible:true
},
name:"fldBleedW",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"898138DB-804E-4941-83C7-EA81ADA3F3C7"
},
{
cssPosition:"202,-1,-1,206,174,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"202",
width:"174"
},
dataProviderID:"worktypesection_roll_to_sheet",
enabled:true,
onDataChangeMethodID:"07F00027-A335-4F13-99F9-0655DEC17BE4",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"F79EF22E-0E6F-4943-97BE-F441F524768F",
visible:true
},
name:"worktypesection_roll_to_sheet",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"89DDFE06-1B09-4738-AA84-C1A4D4FEDE45"
},
{
cssPosition:"329,-1,-1,181,24,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"181",
right:"-1",
top:"329",
width:"24"
},
enabled:true,
onActionMethodID:"7156F03B-D566-4D30-8884-F18E78F6E790",
styleClass:"label_bts text-center",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupPaper",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8A8D3466-05FF-4447-8540-854F637407DB"
},
{
cssPosition:"58,-1,-1,216,15,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"216",
right:"-1",
top:"58",
width:"15"
},
enabled:true,
labelFor:"fldFlatSizeL",
styleClass:"text-center label_bts",
tabSeq:-1,
text:"X",
visible:true
},
name:"x2",
styleClass:"text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"907EC271-80C6-460F-A2D7-9DB781986237"
},
{
cssPosition:"106,-1,-1,522,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"106",
width:"50"
},
dataProviderID:"worktypesection_bleed_l",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F76DF013-FB68-40C3-896F-7B5F75E29301",
visible:true
},
name:"fldBleedL",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"94C441DD-F077-447D-899F-647888DE48E2"
},
{
cssPosition:"31,-1,-1,508,13,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"508",
right:"-1",
top:"31",
width:"13"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"over_warning label_bts text-center",
tabSeq:-1,
text:"%%clc_over_finished_area%%",
visible:true
},
name:"modFinishedSizeAreaCalc",
styleClass:"over_warning label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9953A9E4-75CD-4671-B4B4-3504B912CAA3"
},
{
cssPosition:"154,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"154",
width:"60"
},
dataProviderID:"_iGridY",
editable:true,
enabled:true,
onDataChangeMethodID:"68CFBF34-756B-41DC-8F22-4935260A21DE",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldGridY",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A032F79B-B2E5-4CB3-B963-0C75D3034F14"
},
{
cssPosition:"329,-1,-1,206,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"329",
width:"205"
},
dataProviderID:"paper_brand_name",
editable:true,
enabled:true,
onDataChangeMethodID:"EC711BC2-202C-4CB6-8443-7475BE9638F7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7309F79E-BFE5-4609-82E1-A5E02D41F8F4",
visible:true
},
name:"paper_brand_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A6C7FA6E-DB53-4AA0-9FA0-5DA063173F5D"
},
{
cssPosition:"329,-1,-1,10,166,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"329",
width:"166"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.brandName",
visible:true
},
name:"paper_brand_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A94C0C9D-5922-4C6F-B84A-EF2A79C5EC0A"
},
{
cssPosition:"226,-1,-1,206,110,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"226",
width:"110"
},
dataProviderID:"worktypesection_press_sheet",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"worktypesection_press_sheet",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"ADC3DCC1-4052-40D6-8191-3594A579517B"
},
{
cssPosition:"202,-1,-1,514,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"514",
right:"-1",
top:"202",
width:"100"
},
dataProviderID:"worktypesection_paper_weight",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldPaperWeight",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B0773C95-16E0-4D3B-BCAE-B9B57209AAEC"
},
{
cssPosition:"178,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"178",
width:"60"
},
dataProviderID:"worktypesection_pages",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldPages",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B1401EFB-96AB-4F17-862D-4EED8B547082"
},
{
cssPosition:"82,-1,-1,216,15,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"216",
right:"-1",
top:"82",
width:"15"
},
enabled:true,
labelFor:"fldModelMinL",
styleClass:"text-center label_bts",
tabSeq:-1,
text:"X",
visible:true
},
name:"x2c",
styleClass:"text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B400C268-497A-4335-9033-36A3B43E9A34"
},
{
cssPosition:"379,-1,-1,690,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"690",
right:"-1",
top:"379",
width:"22"
},
enabled:true,
onActionMethodID:"AD98F74E-9DE5-4D9E-9FC7-DFF2352DED00",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_color",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B48326A6-1AA2-444C-8E03-6376837A40C4"
},
{
cssPosition:"34,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"34",
width:"60"
},
dataProviderID:"worktypesection_trim_size_w",
editable:true,
enabled:true,
onDataChangeMethodID:"C9D5DE6F-E64C-4594-94AB-A96E518EC8FC",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldFinishSizeW",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B996D6C4-A3B9-4CA1-BE33-0DBCCF51E939"
},
{
cssPosition:"82,225,-1,353,142,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"353",
right:"225",
top:"82",
width:"142"
},
enabled:true,
labelFor:"fldBleedW",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bleedWLong",
toolTipText:"i18n:avanti.tooltip.bleedWLong",
visible:true
},
name:"component_1BAA4DF9",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD63385F-3B7E-429D-90F8-55C843F2A4C4"
},
{
cssPosition:"304,-1,-1,10,192,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"304",
width:"192"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paper",
visible:true
},
name:"component_84369857",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C3EE3283-7A68-4613-B10E-FDD65572A5C4"
},
{
cssPosition:"106,570,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"570",
top:"106",
width:"140"
},
enabled:true,
labelFor:"fldColors",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.color",
visible:true
},
name:"lblColors",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C5411CC7-D81A-4AC9-B370-37C0F246BE5F"
},
{
cssPosition:"354,-1,-1,206,205,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"206",
right:"-1",
top:"354",
width:"205"
},
dataProviderID:"papergrade_name",
editable:true,
enabled:true,
onDataChangeMethodID:"EC711BC2-202C-4CB6-8443-7475BE9638F7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"ADEDCDDF-2B32-486A-B06B-7577E9AB0414",
visible:true
},
name:"papergrade_name",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"CA3BC978-3E84-476D-84EE-C3DCA81AA779"
},
{
cssPosition:"379,-1,-1,412,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"412",
right:"-1",
top:"379",
width:"22"
},
enabled:true,
onActionMethodID:"AD98F74E-9DE5-4D9E-9FC7-DFF2352DED00",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paperbrand_name",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CDCC7AE3-EC2A-4608-AB40-349D4F2CFD71"
},
{
cssPosition:"250,-1,-1,514,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"514",
right:"-1",
top:"250",
width:"100"
},
dataProviderID:"worktypesection_press_sheets",
editable:true,
enabled:true,
format:"#,###.##",
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"fldSheets",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CDF9C3AA-607A-4D31-B794-31EB00C34463"
},
{
cssPosition:"82,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"82",
width:"60"
},
dataProviderID:"worktypesection_over_model_min_w",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldModelMinW",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CE7FBF67-B354-45CE-886E-D2B87359EA17"
},
{
cssPosition:"226,-1,-1,10,191,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"226",
width:"191"
},
enabled:true,
labelFor:"worktypesection_press_sheet",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.sizeOffOfSheeter",
visible:true
},
name:"component_9CC158C6",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CEC23781-F7AD-4E44-BB7F-E7464C75F4E7"
},
{
cssPosition:"106,-1,-1,155,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"106",
width:"60"
},
dataProviderID:"worktypesection_colours",
editable:true,
enabled:true,
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldColors",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D0751C39-E9DF-449A-8844-7DF812125B8F"
},
{
cssPosition:"34,569,-1,10,141,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"569",
top:"34",
width:"141"
},
enabled:true,
labelFor:"fldFinishSizeW",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.finishSize",
visible:true
},
name:"lblFinishSize",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D48B236B-7C63-4070-A79D-AB212179864A"
},
{
cssPosition:"31,-1,-1,585,44,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"585",
right:"-1",
top:"31",
width:"44"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"text-center label_bts",
tabSeq:-1,
text:"Sq.Ft.",
visible:true
},
name:"lblSquareFeet",
styleClass:"text-center label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D9028AE5-3A1A-4D0A-95D5-49FCB551FCAB"
},
{
height:655,
partType:5,
typeid:19,
uuid:"DAB43C6C-1C4F-4518-8F3B-9E8749C79306"
},
{
cssPosition:"379,-1,-1,181,27,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"181",
right:"-1",
top:"379",
width:"27"
},
enabled:true,
onActionMethodID:"AD98F74E-9DE5-4D9E-9FC7-DFF2352DED00",
styleClass:"listview_noborder label_bts text-center",
tabSeq:0,
text:"%%globals.icon_clearFilter%%",
toolTipText:"i18n:avanti.lbl.clear",
visible:true
},
name:"btnClear_paper_weight",
styleClass:"listview_noborder label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E0398C1C-078C-47CB-B7AC-1B29C17D6EFA"
},
{
cssPosition:"354,-1,-1,470,111,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"470",
right:"-1",
top:"354",
width:"111"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paperSizeCaliper",
visible:true
},
name:"lblPaperCaliper",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E26A0EFC-100D-4738-8C38-618FD3307AE7"
},
{
cssPosition:"226,-1,-1,514,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"514",
right:"-1",
top:"226",
width:"50"
},
dataProviderID:"worktypesection_over_nr_forms",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldForms",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E55A3530-FD2E-4ECD-B117-3C833360083C"
},
{
cssPosition:"31,-1,-1,353,156,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"353",
right:"-1",
top:"31",
width:"156"
},
enabled:true,
labelFor:"fldFinishedSizeAreaCalc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.flatSizeAreaCalculation",
visible:true
},
name:"lblFlatSizeAreaCalculation",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E7424093-6386-4AE4-A203-D03632FEB86C"
},
{
cssPosition:"58,-1,-1,522,50,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"58",
width:"50"
},
dataProviderID:"worktypesection_bleed_size",
editable:true,
enabled:true,
format:"#.000",
selectOnEnter:true,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldBleedSize",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"FFDCAEEE-DB72-41ED-9129-A4FCB6A9A03E"
}
],
name:"sa_task_worktype_section_dlg_generic",
onRecordSelectionMethodID:"205580FB-A9C6-4862-8D97-26D60FA4F7B7",
onShowMethodID:"DF208578-1F39-4483-8CEA-EDF1730C40D4",
scrollbars:33,
showInMenu:false,
size:"720,600",
styleName:"Avanti",
typeid:3,
uuid:"21D3B217-B730-441C-86FA-9CA90E4955AD"