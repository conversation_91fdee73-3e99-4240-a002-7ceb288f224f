/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"1FD58C15-9898-4E7A-B3D8-4BF251249F28",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"7A35AB3E-B230-4ABF-94C0-930117D8B27B"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * @properties={typeid:24,uuid:"E3B66E75-4DC7-4F13-85EF-EF28E6516A84"}
 * @override
 */
function onShowForm(firstShow, event) {
    if (firstShow) {
       if (!_gridReady) {
            application.executeLater(onShowForm, 500, [true, event]);
            return null;
       }
    }
 return _super.onShowForm(firstShow, event);
}
/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"D033CE57-15CE-4BCD-83AD-BA7BEED6E4AE"}
 * @AllowToRunInFind
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "divCheck" && col.styleClass.search(' disabled') == -1) {
		divSelected_onDataChange(oldValue, newValue, event);
	}
	if (col.id == "chkDefault" && col.styleClass.search(' disabled') == -1) {
		defDiv_onDataChange(oldValue, newValue, event);
	}
}

/**
*
* @param {JSEvent} event
*
* @properties={typeid:24,uuid:"886E2012-8E45-4264-A86C-8F93861CDC8B"}
*/
function onHide(event) {
	_super.onHide(event)
//	//sl-2154 - restore div and plant filters that were removed in onShowForm()
//	globals.avBase_setTableFilter()
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9541B17E-5238-4BC8-8EE0-A33CE8B35F8F"}
 * @AllowToRunInFind
 */
function divSelected_onDataChange(oldValue, newValue, event) {
    if (!foundset.div_id) {
        return false;
    }
    /***@type {JSFoundset<db:/avanti/sys_employee_div>} */
    var fs_sys_employee_div = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_div');

    if (newValue) {
        fs_sys_employee_div.newRecord();
        fs_sys_employee_div.empl_id = globals.avEmpSetup_curEmployeeID;
        fs_sys_employee_div.div_id = foundset.div_id;
        fs_sys_employee_div.all_plants = 1;
    }
    else{
        var sSQL = "SELECT empdiv_id FROM sys_employee_div WHERE empl_id = ? AND org_id = ? AND div_id = ?";
        var args = [globals.avEmpSetup_curEmployeeID, globals.org_id, foundset.div_id.toString()];

        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, 1);
        if (ds.getMaxRowIndex() > 0) {
            fs_sys_employee_div.loadRecords(ds);
            fs_sys_employee_div.deleteAllRecords();
        }
    }

    forms.empl_employee_plant_dtl.allPlants_onDataChange(oldValue, newValue, event);
    forms.empl_employee_plant_dtl.elements.chkSelectAll.enabled = newValue? true:false;
    forms.empl_employee_plant_tbl.elements.grid.setReadOnly(!newValue,["chkPlantSelected", "chkDefPlant"])

    if (!newValue) {
        forms.empl_employee_div_dtl.foundset.all_divs = 0;
    }

    databaseManager.saveData();
    if (newValue && areAllDivsSelected()) {
        forms.empl_employee_div_dtl.foundset.all_divs = 1;
    }
    return true;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"269D508F-F2EA-493C-BBB8-F6EB31E0815A"}
 */
function onRecordSelection(_event, _form) {
	var retval = _super.onRecordSelection(_event, _form);
	globals.avEmpSetup_curDivID = foundset.div_id;
	forms.empl_employee_plant_dtl.elements.chkSelectAll.enabled = (foundset.div_selected_for_this_emp == 1);
	forms.empl_employee_plant_tbl.elements.grid.setReadOnly(!(foundset.div_selected_for_this_emp == 1),["chkPlantSelected", "chkDefPlant"]);
	return retval;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5A70DA6F-E48F-431A-9D8D-28AD4E3D3933"}
 * @AllowToRunInFind
 */
function defDiv_onDataChange(oldValue, newValue, event) {
    if (newValue && !foundset.div_selected_for_this_emp) {
        divSelected_onDataChange(oldValue, newValue, event);
    }

    if (newValue) {
        clearDefaultFlagForAllDivs();
        clearDefaultFlagForAllPlants();
    }
    else {
        forms.empl_employee_plant_tbl.clearDefaultFlagForAllPlantsThisDiv();
    }

    foundset.sys_division_to_sys_employee_div$cur_setup_emp.default_div = newValue;
    databaseManager.saveData(foundset);
    return true;
}

/**
 * @properties={typeid:24,uuid:"491C0979-B0A5-4322-9CF2-764D713B9C80"}
 */
function clearDefaultFlagForAllDivs(){
    for (var i = 1; i <= foundset.getSize(); i++) {
        var rDiv = foundset.getRecord(i);

		if (utils.hasRecords(rDiv.sys_division_to_sys_employee_div$cur_setup_emp) && rDiv.sys_division_to_sys_employee_div$cur_setup_emp.default_div) {
			rDiv.sys_division_to_sys_employee_div$cur_setup_emp.default_div = 0;
		}
    }
}

/**
 * @properties={typeid:24,uuid:"B9ACE717-4415-4172-A2BC-F353049F2A65"}
 */
function clearDefaultFlagForAllPlants() {
    for (var d = 1; d <= foundset.getSize(); d++) {
        var rDiv = foundset.getRecord(d);
        
    	for (var p = 1; p <= rDiv.sys_division_to_sys_plant.getSize(); p++) {
            var rPlant = rDiv.sys_division_to_sys_plant.getRecord(p);
            
    		if (utils.hasRecords(rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp) && rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary) {
            	rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary = 0;
            }
        }
    }
}

/**
 * @public 
 * 
 * @return {UUID}
 * 
 * @properties={typeid:24,uuid:"ED5BC441-F033-43FA-AEA8-44E33978374B"}
 */
function getDefaultPlant() {
	if (!utils.hasRecords(foundset)) {
		foundset.loadAllRecords();
	}
	
    for (var d = 1; d <= foundset.getSize(); d++) {
        var rDiv = foundset.getRecord(d);
        
    	for (var p = 1; p <= rDiv.sys_division_to_sys_plant.getSize(); p++) {
            var rPlant = rDiv.sys_division_to_sys_plant.getRecord(p);
            
    		if (utils.hasRecords(rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp) && rPlant.sys_plant_to_sys_employee_plant$cur_setup_emp.emplplant_is_primary) {
            	return rPlant.plant_id;
            }
        }
    }
    
   return null;
}

/**
 * @param {JSRecord<db:/avanti/sys_employee>} empRec
 *
 * @properties={typeid:24,uuid:"7C65DE8D-**************-767DEF7E79DD"}
 */
function pullInDivsFromClass(empRec){
	globals.RunSQL("delete from sys_employee_div where empl_id = '" + empRec.empl_id + "'", true)
	
	if(utils.hasRecords(empRec, 'sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_div')){
		application.output('div has recs')
		
		/***@type {JSFoundset<db:/avanti/sys_employee_div>} */
		var fs_sys_employee_div = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_div')
		var iNumRecs = empRec.sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_div.getSize()
		for(var i=1;i<=iNumRecs;i++){
			/***@type {JSRecord<db:/avanti/sys_employee_class_div>} */
			var rec_sys_employee_class_div = empRec.sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_div.getRecord(i)
			
			fs_sys_employee_div.newRecord()
			fs_sys_employee_div.div_id = rec_sys_employee_class_div.div_id
			fs_sys_employee_div.all_plants = rec_sys_employee_class_div.all_plants
			fs_sys_employee_div.default_div = rec_sys_employee_class_div.default_div
			fs_sys_employee_div.empl_id = empRec.empl_id
			
			databaseManager.saveData(fs_sys_employee_div)
		}
		
		foundset.loadAllRecords()
	}
}

/**
 * @param {JSRecord<db:/avanti/sys_employee>} empRec
 *
 * @properties={typeid:24,uuid:"D443A028-1D9D-4F55-A6DF-B8850F1FD0DB"}
 */
function pullInPlantsFromClass(empRec){
	globals.RunSQL("delete from sys_employee_plant where empl_id = '" + empRec.empl_id + "'", true)
	
	if(utils.hasRecords(empRec, 'sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_plant')){
		application.output('plant has recs')
		
		/***@type {JSFoundset<db:/avanti/sys_employee_plant>} */
		var fs_sys_employee_plant = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee_plant')
		var iNumRecs = empRec.sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_plant.getSize()
		for(var i=1;i<=iNumRecs;i++){
			/***@type {JSRecord<db:/avanti/sys_employee_class_plant>} */
			var rec_sys_employee_class_plant = empRec.sys_employee_to_sys_employee_class.sys_employee_class_to_sys_employee_class_plant.getRecord(i)
			fs_sys_employee_plant.newRecord()
			
			fs_sys_employee_plant.plant_id = rec_sys_employee_class_plant.plant_id
			fs_sys_employee_plant.emplplant_is_primary = rec_sys_employee_class_plant.emplplant_is_primary
			fs_sys_employee_plant.empl_id = empRec.empl_id
			
			databaseManager.saveData(fs_sys_employee_plant)
		}
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"FB47B0D8-4D06-4F9A-BE4F-45C4B95A409C"}
 */
function areAllDivsSelected(){
     var sSQL = "SELECT CASE WHEN \
                (SELECT count(*) FROM sys_division \
                    WHERE org_id = ?)\
                =(SELECT count(*) FROM sys_division sd\
                    INNER JOIN sys_employee_div sed\
                    ON sed.div_id = sd.div_id \
                    AND sed.org_id = sd.org_id\
                    WHERE sed.empl_id = ? \
                    AND sd.org_id = ?)\
                THEN 1\
                ELSE 0\
                END AS ALL_SELECTED";
     var args = [globals.org_id, globals.avEmpSetup_curEmployeeID, globals.org_id];

     var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSQL, args, 1);

     if (ds.getMaxRowIndex() > 0) {
         return ds.getValue(1, 1);
     }
     
     return 0;
}
