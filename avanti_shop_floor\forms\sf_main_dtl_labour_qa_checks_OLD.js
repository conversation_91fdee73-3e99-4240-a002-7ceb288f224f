/**
 * @properties={typeid:35,uuid:"96031279-0C0E-4571-857F-236412B9F220",variableType:-4}
 */
var bHitOK = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"6E9F219F-9461-4645-BA94-41B64533AE90",variableType:4}
 */
var _iLastCheckNumAdded = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0E17BD55-F01A-447C-99CB-F560CD6D57D1"}
 */
var _sRenderedItems = '';

/**
 * @properties={typeid:35,uuid:"7BDED2C4-3C4A-4737-9ADD-0616D01519B4",variableType:-4}
 */
var _aCheckNames = [];

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"37EEF227-32AB-4C88-9028-A8091B75EE23"}
 */
function onActionOK(event) {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).qa_check_pos_1 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_2 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_3 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			return false;
		}
	}
	resetPlaceHolder();
	bHitOK=true;
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5A5ED3B0-8A3C-4F73-B02C-9553B3A86DD9"}
 */
function onActionCancel(event) {
	resetPlaceHolder();
	bHitOK=false;
	globals.DIALOGS.closeForm(event);
	return false;
}

/**
 * @properties={typeid:24,uuid:"B2E174D2-585F-4634-A313-265E6CF4D254"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0;
		
	}
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"2207DE17-4999-4F3B-A58D-F2B710936193"}
 */
function onRenderCheckOld(event) {
//	var check_field = event.getRenderable()
//	var sElementName = check_field.getName()
//	
//	if(_sRenderedItems.indexOf(sElementName) > -1){
//		return // only need to process onRenderCheck once - setting check_field.visible = true below causes onRenderCheck() to fire again
//	}
//	else{
//		_sRenderedItems += sElementName
//
//		if(sElementName.indexOf('qa_check_field') == -1){ // check box, not label, get name and it will be asigned when label turns comes around
//			/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
//			var currentRowRecord = event.getRecord()
//			_aCheckNames.push(getNextQACheck(currentRowRecord))
//		}
//		
//		var iCheckNum = sElementName.substr(sElementName.length-1, 1) 
//		
//		if(_aCheckNames[iCheckNum]){
//			if(sElementName.indexOf('qa_check_field') > -1){ // label - get the name
//				check_field.putClientProperty('text', _aCheckNames[iCheckNum])
//			}
//			check_field.visible = true		
//		}
//		else{
//			check_field.visible = false	
//		}
//	}
}

/**
 * @return {String}
 *
 * @param {JSRecord<db:/avanti/sch_milestone_group>} currentRowRecord
 *
 * @properties={typeid:24,uuid:"141DF66B-50C9-4E65-AC6B-D24862E586D8"}
 */
function getNextQACheck(currentRowRecord){
	_iLastCheckNumAdded = 0;
	if(currentRowRecord && utils.hasRecords(currentRowRecord.sch_milestone_group_to_sys_cost_centre)) {
		if(_iLastCheckNumAdded  < 1 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled) {
			_iLastCheckNumAdded = 1;
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1;
		} 
		else if(_iLastCheckNumAdded  < 2 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled) {
			_iLastCheckNumAdded = 2;
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2;
		} 
		else if(_iLastCheckNumAdded  < 3 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled) {
			_iLastCheckNumAdded = 3;
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3;
		}		
	} 
	
	return '';
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"AED56C12-DBA8-4848-A621-7450E2A321EB"}
 */
function onRenderCC(event) {
	_iLastCheckNumAdded = 0;
	_sRenderedItems = '';
	_aCheckNames = [];
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"C972A95D-BAE0-46B3-A384-BDD103B04BA0"}
 */
function onRenderCheck1(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true
	
	if(currentRowRecord && !currentRowRecord.qa_check_pos_1){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"0C78265A-25E3-4E98-BAE3-A71FD9162BEC"}
 */
function onRenderCheck2(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true

	if(currentRowRecord && !currentRowRecord.qa_check_pos_2){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"B8401055-CB6F-49B4-84A9-35DDA2E9F709"}
 */
function onRenderCheck3(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	
	check_field.visible = true
	if(currentRowRecord && !currentRowRecord.qa_check_pos_3){
		check_field.visible = false
	}
	
	

//	check_field.visible = false
//	if(currentRowRecord && currentRowRecord.qa_check_pos_3){
//		application.output('currentRowRecord.qa_check_pos_3: ' + currentRowRecord.qa_check_pos_3)
//		check_field.visible = true
//	}
	
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @protected
 *
 * @properties={typeid:24,uuid:"E768B7E5-4F39-4A09-8D91-DE95FEC00941"}
 */
function onShow(firstShow, event) {
	elements.qa_check_1.visible = true;
	elements.qa_check_field_1.visible = true;
	elements.qa_check_2.visible = true;
	elements.qa_check_field_2.visible = true;
	elements.qa_check_field_3.visible = true;
	elements.qa_check_3.visible = true;
	
	if(qa_check_pos_1){
		elements.qa_check_1.visible = false;
		elements.qa_check_field_1.visible = false;
	}
	if(qa_check_pos_2){
		elements.qa_check_2.visible = false;
		elements.qa_check_field_2.visible = false;
	}
	if(qa_check_pos_3){
		elements.qa_check_3.visible = false;
		elements.qa_check_field_3.visible = false;
	}
}
