customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_cash_receipt",
encapsulation:0,
extendsID:"F0A8155B-2930-49F3-849B-6183994C0B90",
items:[
{
cssPosition:"225,-1,-1,10,125,22",
formIndex:16,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"225",
width:"125"
},
enabled:true,
formIndex:16,
labelFor:"bank_account_glacct_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.bankAccount",
visible:true
},
name:"bank_account_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0E052338-D92E-4121-868A-65EAA0A11E46"
},
{
cssPosition:"117,-1,-1,330,125,22",
formIndex:28,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"117",
width:"125"
},
enabled:true,
formIndex:28,
labelFor:"sa_cust_postage_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.PostageDeposit",
visible:true
},
name:"sa_cust_postage_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"10A9420E-EE11-4668-909B-8E411BC2362E"
},
{
cssPosition:"117,-1,-1,637,22,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"637",
right:"-1",
top:"117",
width:"22"
},
enabled:true,
formIndex:3,
onActionMethodID:"CFC17FA9-F835-4569-8E78-90CDD197B52C",
styleClass:"listview_noborder label_bts",
tabSeq:10,
text:"%%globals.icon_tableViewInfo%%",
toolTipText:"i18n:avanti.tooltip.selectDepositToApply",
visible:true
},
name:"btnApplyDeposit",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"10DEB8CF-3795-4738-9BB7-2C44946ECC0C"
},
{
cssPosition:"171,-1,-1,810,22,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"171",
width:"22"
},
dataProviderID:"sa_cash_receipt_auto_apply",
enabled:true,
formIndex:8,
styleClass:"checkbox_bts",
tabSeq:19,
text:"",
visible:true
},
name:"sa_cash_receipt_auto_apply",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"13ED6E7E-2723-4101-A05A-8B0945DB1068"
},
{
cssPosition:"284,5,4,5,990,151",
formIndex:16,
json:{
cssPosition:{
bottom:"4",
height:"151",
left:"5",
right:"5",
top:"284",
width:"990"
},
formIndex:16,
onTabClickedMethodID:"F053FA59-341C-4507-B14B-54F048CECD72",
tabs:[
{
containedForm:"A9979870-2BD9-4164-9A1D-F674DC5457F6",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabInvoiceDetail",
relationName:"_to_sa_cash_receipt",
svyUUID:"E14F08F4-92B6-4CFA-906B-8DD0C61ABE53",
text:"i18n:avanti.dialog.InvoiceDetails_title"
},
{
containedForm:"46332935-76CF-442D-8BA8-C67FA89E9A0A",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabNotes",
relationName:null,
svyUUID:"B91F854A-9288-4EC8-89FE-1116F79E082C",
text:"i18n:avanti.lbl.notes"
},
{
containedForm:"4ABFA5D3-CDF5-4EDD-AA6C-0E0A4F93B4DF",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabDistribution",
relationName:"sa_cash_receipt_to_sa_cash_receipt_dist",
svyUUID:"5F3E0B9F-45E8-4C95-9CC3-44B75E25CB77",
text:"i18n:avanti.lbl.distributions"
},
{
containedForm:"807A60B9-FB16-4B40-9D2A-A4710FA3E965",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabOrders",
relationName:null,
svyUUID:"4B2480EE-CB30-44ED-BB32-2CFB9EB367C5",
text:"i18n:avanti.tab.OrderDetails"
},
{
containedForm:"FC1EE775-8C5B-4C93-8C14-A18FF5C0C97D",
disabled:false,
hideCloseIcon:false,
iconStyleClass:null,
imageMedia:null,
name:"tabIntegrationDetails",
relationName:"_to_sa_cash_receipt",
svyUUID:"9F4C9C29-33D2-4649-B0DD-2071C6712C15",
text:"i18n:avanti.lbl.IntegrationDetails"
}
]
},
name:"tabs",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"167821D4-3D4D-4105-92B2-8EF111B87406"
},
{
cssPosition:"252,-1,-1,140,492,22",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"252",
width:"492"
},
dataProviderID:"apply_to_account_glacct_id",
enabled:true,
formIndex:22,
styleClass:"datalabel_bts",
tabSeq:13,
valuelistID:"51C9602D-57FC-4665-BE42-10C0B2CC894C",
visible:true
},
name:"apply_to_account_account",
styleClass:"datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"168CB270-D4AE-4394-8852-40DD49708E1E"
},
{
cssPosition:"36,-1,-1,810,180,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"36",
width:"180"
},
dataProviderID:"sa_cash_receipt_document_num",
editable:false,
enabled:true,
formIndex:10,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:20,
visible:true
},
name:"sa_cash_receipt_document_num",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"239A0579-7028-4301-8D24-A578C825BDE0"
},
{
cssPosition:"36,-1,-1,10,125,22",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"36",
width:"125"
},
enabled:true,
formIndex:14,
labelFor:"cust_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customer",
visible:true
},
name:"supplier_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2633EA02-18CF-43BC-BD53-17238309830F"
},
{
cssPosition:"252,-1,-1,10,125,22",
formIndex:17,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"252",
width:"125"
},
enabled:true,
formIndex:17,
labelFor:"apply_to_account_account",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.applyToAccount",
visible:true
},
name:"apply_to_account_glacct_id_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2CE34AC9-6DA8-4AB0-B666-356680DDFC39"
},
{
cssPosition:"117,-1,-1,810,180,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"117",
width:"180"
},
enabled:true,
formIndex:6,
onActionMethodID:"2CA81415-BDE4-4078-B825-49F3FD5A3AE3",
styleClass:"btn btn-default button_bts",
tabSeq:11,
text:"i18n:avanti.lbl.activityStatus_onHold",
visible:true
},
name:"btn_onHold",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"43CDA3B7-6933-4780-AACF-940B16A7A563"
},
{
cssPosition:"117,-1,-1,140,180,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"117",
width:"180"
},
dataProviderID:"sa_cash_receipt_trans_date",
enabled:true,
formIndex:9,
onDataChangeMethodID:"68160D32-1814-4581-9A22-E7F78D4789C4",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:4,
visible:true
},
name:"sa_cash_receipt_trans_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"47774B8B-38C1-4CF0-A160-8B1B4675E877"
},
{
cssPosition:"90,-1,-1,460,180,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"90",
width:"180"
},
dataProviderID:"sa_cash_receipt_source",
enabled:true,
formIndex:5,
onDataChangeMethodID:"DB11B4C7-9C5C-414A-A4B5-1F5F2B5D232F",
styleClass:"combobox_bts",
tabSeq:8,
valuelistID:"1A72E7F1-CC44-4DDA-837C-6D7939939D81",
visible:true
},
name:"sa_cash_receipt_source",
styleClass:"combobox_bts",
typeName:"bootstrapcomponents-combobox",
typeid:47,
uuid:"4CEB9023-23C8-4722-98B6-058CA42B32B9"
},
{
cssPosition:"90,-1,-1,330,125,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"90",
width:"125"
},
enabled:true,
formIndex:4,
labelFor:"sa_cash_receipt_source",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.source",
visible:true
},
name:"sa_cash_receipt_source_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4DC3DB0C-5935-47A2-BE66-5E888551C3DE"
},
{
cssPosition:"90,-1,-1,809,181,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"809",
right:"-1",
top:"90",
width:"181"
},
dataProviderID:"created_by_id",
enabled:true,
formIndex:1,
styleClass:"not_editable datalabel_bts",
tabSeq:-2,
valuelistID:"9262D790-20F4-48B1-BD97-862E3F6EAFD6",
visible:true
},
name:"created_by_id",
styleClass:"not_editable datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"4F6B11F5-CFA0-4389-BEF0-B73818FF6429"
},
{
cssPosition:"171,-1,-1,460,199,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"171",
width:"199"
},
dataProviderID:"sa_cash_receipt_remaining_amt",
editable:false,
enabled:true,
formIndex:7,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:-2,
visible:true
},
name:"invoice_total_amount_remaining",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"594EC1C5-3F04-4FF2-B539-E14441CA2D9F"
},
{
cssPosition:"225,-1,-1,140,519,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"225",
width:"519"
},
dataProviderID:"bank_account_glacct_id",
enabled:true,
formIndex:21,
styleClass:"datalabel_bts",
tabSeq:7,
valuelistID:"12AC4EF7-D0A2-4B17-90C0-D77F89C00748",
visible:true
},
name:"bank_account_glacct_id",
styleClass:"datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"5D9401CB-A8A6-4F71-8BD3-02410F8C4C38"
},
{
cssPosition:"252,-1,-1,810,180,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"252",
width:"180"
},
enabled:true,
formIndex:5,
onActionMethodID:"EDE32CF2-41CF-4B25-AB54-A116B8FF99D4",
styleClass:"btn btn-default button_bts",
tabSeq:18,
text:"i18n:servoy.menuitem.import",
visible:true
},
name:"btnImport",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"63CC94CA-47AA-4F0B-9155-8279C323B233"
},
{
cssPosition:"171,-1,-1,679,125,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"171",
width:"125"
},
enabled:true,
formIndex:7,
labelFor:"sa_cash_receipt_auto_apply",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.autoApplyDeposit",
visible:true
},
name:"sa_cash_receipt_auto_apply_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"653BD0DA-916F-4EFE-B705-A2A52501E233"
},
{
cssPosition:"90,-1,-1,10,125,22",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"90",
width:"125"
},
enabled:true,
formIndex:10,
labelFor:"sa_cash_receipt_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentDate",
visible:true
},
name:"sa_cash_receipt_date_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"66F38DB1-83F1-4DD2-9EFD-AFB2DFD0FEA2"
},
{
cssPosition:"171,-1,-1,140,180,22",
formIndex:37,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"171",
width:"180"
},
dataProviderID:"sa_cash_receipt_funds_deposited",
enabled:true,
formIndex:37,
styleClass:"combobox_bts",
tabSeq:6,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"sa_cash_receipt_funds_deposited",
styleClass:"combobox_bts",
typeName:"bootstrapcomponents-combobox",
typeid:47,
uuid:"697838F3-A312-4BA1-A7D3-2AFDEB026713"
},
{
cssPosition:"63,-1,-1,679,125,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"63",
width:"125"
},
enabled:true,
formIndex:2,
labelFor:"status_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.status",
visible:true
},
name:"status_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6DC988F0-4982-434D-8A1E-C4C2447D0C91"
},
{
cssPosition:"0,-1,-1,0,1000,26",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"26",
left:"0",
right:"-1",
top:"0",
width:"1000"
},
enabled:true,
formIndex:2,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.CashReceipt_DetailView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6E7B4241-1A29-4BD6-A2B8-A6AFE9FE8002"
},
{
cssPosition:"171,-1,-1,330,125,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"171",
width:"125"
},
enabled:true,
formIndex:12,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.remainingAmount",
visible:true
},
name:"sa_cash_receipt_amount_remaining_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7FD0F669-C922-4AB9-B1A8-3DDB534343E5"
},
{
cssPosition:"31,-1,-1,674,321,248",
json:{
cssPosition:{
bottom:"-1",
height:"248",
left:"674",
right:"-1",
top:"31",
width:"321"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_62AB55F9",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"81A680DB-99EF-493B-A70E-00559CB61D35"
},
{
cssPosition:"117,-1,-1,460,172,22",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"117",
width:"172"
},
dataProviderID:"sa_cust_postage_id",
enabled:true,
formIndex:31,
styleClass:"not_editable datalabel_bts",
tabSeq:-2,
valuelistID:"2E8F04CE-68D5-4D9E-8D6E-0803EE0B8A93",
visible:true
},
name:"sa_cust_postage_id",
styleClass:"not_editable datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"8A03D854-501A-4211-BD7A-144403545C99"
},
{
cssPosition:"63,-1,-1,10,125,22",
formIndex:18,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"63",
width:"125"
},
enabled:true,
formIndex:18,
labelFor:"sa_cash_receipt_num",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.checkPaymentNo",
visible:true
},
name:"sa_cash_receipt_num_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8B5F167F-F579-4605-AB84-05CE5DF8002F"
},
{
cssPosition:"144,-1,-1,140,180,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"144",
width:"180"
},
dataProviderID:"sa_cash_receipt_amount_exchanged",
editable:true,
enabled:true,
formIndex:8,
onDataChangeMethodID:"08642580-85C0-4319-BFC9-1736B0EDEC85",
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:22,
visible:true
},
name:"sa_cash_receipt_amount_exchanged",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8D31ED02-8476-4DDA-A885-59223C2B536A"
},
{
cssPosition:"171,-1,-1,460,199,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"171",
width:"199"
},
dataProviderID:"sa_cash_receipt_remaining_amt_exchanged",
editable:false,
enabled:true,
formIndex:7,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:24,
visible:true
},
name:"invoice_total_amount_remaining_exchanged",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"8DAD16E0-6113-41D5-932E-CBB4F9540759"
},
{
cssPosition:"90,-1,-1,140,180,22",
formIndex:24,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"90",
width:"180"
},
dataProviderID:"sa_cash_receipt_date",
enabled:true,
formIndex:24,
onDataChangeMethodID:"5339350C-4B3D-464B-8E94-D24B13D043AB",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:3,
visible:true
},
name:"sa_cash_receipt_date",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"98176D67-15E6-4AF5-97A6-853BD989191F"
},
{
cssPosition:"36,-1,-1,679,125,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"36",
width:"125"
},
enabled:true,
formIndex:9,
labelFor:"sa_cash_receipt_document_num",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.receiptNumber",
visible:true
},
name:"sa_cash_receipt_document_num_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9A53839E-08BE-43A8-B52D-DCC495C93131"
},
{
cssPosition:"90,-1,-1,679,125,22",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"90",
width:"125"
},
enabled:true,
formIndex:11,
labelFor:"created_by_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.employee",
visible:true
},
name:"created_by_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9DFBD11D-474D-49D5-B0BA-695075B9C715"
},
{
cssPosition:"31,-1,-1,5,660,248",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"248",
left:"5",
right:"-1",
top:"31",
width:"660"
},
enabled:true,
formIndex:1,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_1799C736",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A7F169EA-3481-4E5C-867E-FDA9F84B705B"
},
{
cssPosition:"190,-1,-1,330,127,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"190",
width:"127"
},
dataProviderID:"overpayment",
enabled:true,
onDataChangeMethodID:"255D510F-D4A9-4296-B12A-4FA3C0819C8B",
styleClass:"checkbox_bts",
tabSeq:16,
text:"i18n:avanti.lbl.Overpayment",
visible:true
},
name:"overpayment",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"AC671D6C-1F9D-488E-BD73-31F62516E878"
},
{
cssPosition:"63,-1,-1,140,180,22",
formIndex:23,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"63",
width:"180"
},
dataProviderID:"sa_cash_receipt_num",
editable:true,
enabled:true,
formIndex:23,
onDataChangeMethodID:"9AC26626-23CD-4C75-A491-BD8740F881BE",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:2,
visible:true
},
name:"sa_cash_receipt_num",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B07FF461-BDB2-4721-8B39-A03E943FA6A2"
},
{
cssPosition:"117,-1,-1,330,125,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"117",
width:"125"
},
enabled:true,
formIndex:30,
labelFor:"sa_cust_deposit_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customerDeposit",
visible:true
},
name:"sa_cust_deposit_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B11AA1E3-8CC2-45E5-BDCD-D8B75C31FD8B"
},
{
cssPosition:"63,-1,-1,330,125,22",
formIndex:25,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"63",
width:"125"
},
enabled:true,
formIndex:25,
labelFor:"curr_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.curr_id",
visible:true
},
name:"curr_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD11E934-EBB7-4FBB-9C6B-63FB55F7910C"
},
{
cssPosition:"144,-1,-1,140,180,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"144",
width:"180"
},
dataProviderID:"sa_cash_receipt_amount",
editable:true,
enabled:true,
formIndex:8,
onDataChangeMethodID:"2D8FCF49-F3B5-4201-9DF2-38908185D9BF",
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:5,
visible:true
},
name:"sa_cash_receipt_amount",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BD402C1B-C8EE-4ABB-985A-318EBC5A1706"
},
{
cssPosition:"90,-1,-1,330,125,22",
formIndex:19,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"90",
width:"125"
},
enabled:true,
formIndex:19,
labelFor:"paymethod_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymethod_id",
visible:true
},
name:"paymethod_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD9F175E-D9A7-41F0-9A4C-771AF7501324"
},
{
cssPosition:"2,-1,-1,679,125,22",
formIndex:34,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"2",
width:"125"
},
enabled:true,
formIndex:34,
labelFor:"sacr_reg_number",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.registerNumber",
visible:true
},
name:"sacr_reg_number_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BE463AA7-62BA-4D8F-908D-7AA6E8E94D33"
},
{
cssPosition:"171,-1,-1,10,125,22",
formIndex:36,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"171",
width:"125"
},
enabled:true,
formIndex:36,
labelFor:"sa_cash_receipt_funds_deposited",
styleClass:"label_bts",
tabSeq:-1,
text:"Funds Deposited",
toolTipText:"Have the funds already been deposited?",
visible:true
},
name:"sa_cash_receipt_funds_deposited_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C068C7EB-48D5-4C19-A95B-D88587F4160F"
},
{
cssPosition:"36,-1,-1,140,519,22",
formIndex:15,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"140",
right:"-1",
top:"36",
width:"519"
},
dataProviderID:"cust_id",
editable:true,
enabled:true,
formIndex:15,
onDataChangeMethodID:"1E79B0BD-00C3-4554-B30B-EDFC23728860",
styleClass:"typeahead_bts",
tabSeq:1,
valuelistID:"0C50969F-CE7F-44D2-9AAA-6F446496C044",
visible:true
},
name:"cust_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C9D619F5-BCEB-40EF-A1A9-C759967F2901"
},
{
cssPosition:"117,-1,-1,10,125,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"117",
width:"125"
},
enabled:true,
formIndex:2,
labelFor:"sa_cash_receipt_trans_date",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.transactionDate",
visible:true
},
name:"transaction_date_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CC05FC61-6B73-4AB7-A751-E63DDBCF12F5"
},
{
cssPosition:"144,-1,-1,330,125,22",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"144",
width:"125"
},
enabled:true,
formIndex:11,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.amountApplied",
visible:true
},
name:"sa_cash_receipt_amount_matched_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CCB34750-2D6A-4696-A76F-CD7D7D077A9E"
},
{
cssPosition:"63,-1,-1,460,199,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"63",
width:"199"
},
dataProviderID:"curr_id",
enabled:true,
formIndex:26,
styleClass:"not_editable datalabel_bts",
tabSeq:-2,
valuelistID:"9E25AAB4-5C24-4FBA-B0E4-FA1523FA6721",
visible:true
},
name:"curr_id",
styleClass:"not_editable datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"D32CDF4B-426A-400C-B3CA-6B6D0BD2B92C"
},
{
cssPosition:"117,-1,-1,460,172,22",
formIndex:29,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"117",
width:"172"
},
dataProviderID:"sa_cust_deposit_id",
enabled:true,
formIndex:29,
styleClass:"not_editable datalabel_bts",
tabSeq:-2,
valuelistID:"FCC7971F-3468-4202-97BB-4850023DCDCB",
visible:true
},
name:"sa_cust_deposit_id",
styleClass:"not_editable datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"D58F9DEF-1319-4F13-A3C7-3AC26D57E533"
},
{
cssPosition:"63,-1,-1,810,180,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"63",
width:"180"
},
dataProviderID:"sa_cash_receipt_status",
enabled:true,
formIndex:3,
styleClass:"not_editable datalabel_bts",
tabSeq:-2,
valuelistID:"8896FC1B-8AF4-4AFB-BF56-50B848927893",
visible:true
},
name:"status_id",
styleClass:"not_editable datalabel_bts",
typeName:"bootstrapcomponents-datalabel",
typeid:47,
uuid:"D6C43223-F6BE-4432-A832-625B23B99FDA"
},
{
cssPosition:"198,-1,-1,460,199,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"198",
width:"199"
},
dataProviderID:"overpayment_amount",
editable:false,
enabled:true,
formIndex:6,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:17,
visible:true
},
name:"overpayment_amount",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DD65F75E-96A4-44A2-A1F0-B736EC041F77"
},
{
cssPosition:"144,-1,-1,460,199,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"144",
width:"199"
},
dataProviderID:"sa_cash_receipt_payment_amt_exchanged",
editable:false,
enabled:true,
formIndex:9,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:23,
visible:true
},
name:"invoice_total_amount_exchanged",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E0B3718C-8CC6-41A2-B762-2A01B948862B"
},
{
cssPosition:"252,-1,-1,637,22,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"637",
right:"-1",
top:"252",
width:"22"
},
enabled:true,
formIndex:2,
onActionMethodID:"78E9842C-1C58-4463-B977-057125D12295",
styleClass:"listview_noborder label_bts",
tabSeq:14,
text:"%%globals.icon_tableViewInfo%%",
visible:true
},
name:"applyToAccountInfo",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E1172029-A015-4869-92E5-AA08E7E483E7"
},
{
cssPosition:"90,-1,-1,460,199,22",
formIndex:20,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"90",
width:"199"
},
dataProviderID:"paymethod_id",
editable:true,
enabled:true,
formIndex:20,
onDataChangeMethodID:"67220D28-6EEF-4766-8A85-30612B5368F1",
styleClass:"typeahead_bts",
tabSeq:9,
valuelistID:"********-F42A-4EC9-AA03-B70AB14E0D26",
visible:true
},
name:"paymethod_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"E875FD4A-E130-4E49-AC8E-17001CCD03B6"
},
{
cssPosition:"2,-1,-1,810,180,22",
formIndex:35,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"2",
width:"180"
},
dataProviderID:"sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_number",
editable:false,
enabled:true,
formIndex:35,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:21,
visible:true
},
name:"sacr_reg_number",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"ED85A953-FFA7-46EB-8C51-9AB5C0D397C7"
},
{
cssPosition:"144,-1,-1,460,199,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"144",
width:"199"
},
dataProviderID:"sa_cash_receipt_payment_amt",
editable:false,
enabled:true,
formIndex:9,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:-2,
visible:true
},
name:"invoice_total_amount",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"EFEB0206-AADB-45D1-8980-977CC3C9CE8C"
},
{
cssPosition:"225,-1,-1,679,316,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"679",
right:"-1",
top:"225",
width:"316"
},
enabled:true,
formIndex:12,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"lblCancelledReceipt",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F1B87F4C-3E05-4626-B979-A9769920B448"
},
{
cssPosition:"144,-1,-1,10,125,22",
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"144",
width:"125"
},
enabled:true,
formIndex:13,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentAmount",
visible:true
},
name:"sa_cash_receipt_amount_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F1C73DEC-D00B-469F-B568-6AB08F06142E"
},
{
cssPosition:"144,-1,-1,810,180,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"810",
right:"-1",
top:"144",
width:"180"
},
enabled:true,
formIndex:4,
onActionMethodID:"914BD96A-A54B-47F3-BF71-8739A06E53C3",
styleClass:"btn btn-default button_bts",
tabSeq:12,
text:"i18n:avanti.lbl.readyToBePosted",
visible:true
},
name:"btn_ready_to_be_posted",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"F3323947-CDE2-4138-851B-D8CC8A863767"
},
{
cssPosition:"198,-1,-1,460,199,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"198",
width:"199"
},
dataProviderID:"overpayment_amount_exchanged",
editable:false,
enabled:true,
formIndex:6,
selectOnEnter:false,
styleClass:"not_editable_text-right textbox_bts text-right",
tabSeq:25,
visible:true
},
name:"overpayment_amount_exchanged",
styleClass:"not_editable_text-right textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"F55F86C1-3144-4360-BFEA-25D3E6C039E1"
},
{
height:439,
partType:5,
typeid:19,
uuid:"F8A3E525-8B0A-460C-8DE9-C2934B590910"
}
],
name:"sa_cash_receipt_dtl",
onRecordSelectionMethodID:"8ECBF868-960F-4E8E-8C6C-2B1A54E2F293",
onShowMethodID:"5D1F8C48-9878-42B1-9898-801A3BA7A7A7",
scrollbars:33,
size:"1000,439",
styleName:null,
typeid:3,
uuid:"74441DD4-7366-4C9F-A68E-0A7CDECEFC27"