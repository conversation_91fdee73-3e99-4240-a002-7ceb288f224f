/**
 * @return {MEDIA}
 *
 * @properties={typeid:36,uuid:"BC363437-22B5-4825-9B10-D17AFCD6DCE5"}
 */
function clc_avantiFieldBatchInvSummary()
{
	if(bifsf_use_udv == 1){
		 return false;
	}else if(bifsf_use_udv == 0){
		return true;
	}else{
		 return false;
	}
}

/**
 * @return {MEDIA}
 *
 * @properties={typeid:36,uuid:"76E4B232-DD9F-4B64-A135-6AB3BAF0E1CE"}
 */
function clc_UDVFieldBatchInvSummary()
{
	if(bifsf_use_udv == 1){
		 return true;
	}else if(bifsf_use_udv == 0){
		return false;
	}else{
		 return false;
	}
}
