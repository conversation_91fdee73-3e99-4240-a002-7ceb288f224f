/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3B5D496C-84D9-4D5C-8D86-FFDB54B5CA94",variableType:4}
 */
var _gridReady = 0;

// changed to xxx_local because it was hiding global

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"699101F7-50FA-4812-9FFF-69429C75E70F"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"0C7D50D6-B84D-431D-B05B-CAD3F5C2C889"}
 */
function onShow(firstShow, event)
{

    if (firstShow) {
    	if (!_gridReady) {
    		application.executeLater(onShow, 500, [true, event]);
    		return null;
    	}
    }

    _bScheduleEmps_local = scopes.avUtils.getSysPrefNum('ScheduleEmployees') == 1;
    
    elements.grid.getColumn(elements.grid.getColumnIndex("emps")).visible = _bScheduleEmps_local;
    elements.grid.getColumn(elements.grid.getColumnIndex("btnEmps")).visible = _bScheduleEmps_local;
    elements.grid.getColumn(elements.grid.getColumnIndex("split")).visible = _bScheduleEmps_local;
    elements.grid.getColumn(elements.grid.getColumnIndex("completionDate")).format = globals.avBase_dateFormatWithTime;
    elements.grid.getColumn(elements.grid.getColumnIndex("dueDate")).format = globals.avBase_dateFormatWithTime;
    elements.grid.getColumn(elements.grid.getColumnIndex("scheduledDate")).format = globals.avBase_dateFormatWithTime;
    
	if(scopes.avUtils.isFormShowing('sa_order_schedule_reservation')){
		elements.grid.setReadOnly(true,["ready", "done"]);
	}
	else{
		elements.grid.setReadOnly(false,["ready", "done"]);
	}
	
	foundset.loadAllRecords();
	foundset.sort("sequence_nr asc");

    setValueLists();
	databaseManager.recalculate(foundset);
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4CE478D6-C7C8-40F5-AD4A-9B4501B32896"}
 */
var split_tooltip = i18n.getI18NMessage('i18n:avanti.tt.splitMilestone');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C9F72245-8E21-47B2-800B-259AD66690C1"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btnEmps") {
		onAction_btnEmps(event);
	}
	if (col.id == "split") {
		onAction_split(event);
	}
	if (col.id == "sort_icon") {
		onAction_btnShuffle(event);
	}
	if (col.id == "btnDelete" && col.styleClass.search(' disabled') == -1) {
		onAction_btnDelete(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"DD5AED81-FD85-4A12-880A-5FA3E5FA1FC5"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "sequenceNr") {
		onDataChange_taskNum(oldValue, newValue, event);
	}
	if (col.id == "section") {
		onDataChange_sect(oldValue, newValue, event);
	}
	if (col.id == "dept") {
		onDataChange_dept(oldValue, newValue, event);
	}
	if (col.id == "opcat") {
		onDataChange_opCat(oldValue, newValue, event);
	}
	if (col.id == "oper") {
		onDataChange_op(oldValue, newValue, event);
	}
	if (col.id == "pred") {
		onDataChange_pred(oldValue, newValue, event);
	}
	if (col.id == "ready") {
		onDataChange_ready(oldValue, newValue, event);
	}
	if (col.id == "done") {
		onDataChange_markMilestoneDone(oldValue, newValue, event);
	}
	if (col.id == "ms_utilization_rate") {
		onDataChange_UR(oldValue, newValue, event);
	}
	if (col.id == "timeNeeded") {
		onDataChange_timeNeeded(oldValue, newValue, event);
	}
	if (col.id == "scheduledDate") {
		onDataChange_SchedDate(oldValue, newValue, event);
	}
	if (col.id == "dueDate") {
		onDataChange_dateDue(oldValue, newValue, event);
	}
	if (col.id == "completionDate") {
		onDataChange_compDate(oldValue, newValue, event);
	}
}

/**
 * @properties={typeid:35,uuid:"C727BDAD-CB36-4E41-967A-ABCCAA19F987",variableType:-4}
 */
var bProcessingDateChange = false;

/**
 * @properties={typeid:35,uuid:"987DEA69-F25A-40D0-A7DA-C97CB783917B",variableType:-4}
 */
var _bScheduleEmps_local = false;

/**
 * @properties={typeid:35,uuid:"D3CF00D2-CD7F-4607-AF2A-2FDE3B8D56E2",variableType:-4}
 */
var _dirty = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"EE9175CC-FF81-450F-852A-AA2BD6EBCF3A",variableType:4}
 */
var _shuffleSource = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"798D296F-9B3B-47ED-901D-D90F494A0FBD",variableType:4}
 */
var _shuffleDest = 0;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"D9E3ABCE-5988-4164-96DD-7A14240F22CF",variableType:93}
 */
var oldDueDate;

/**
 * This function sets several valuelists used by this form
 * @properties={typeid:24,uuid:"8C3A0E2E-6663-440E-B049-DE2E08B0705E"}
 */
function setValueLists() {
    if (utils.hasRecords(foundset.sch_milestone_to_sch_schedule) 
            && utils.hasRecords(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job)) {
        scopes.avVL.load_vl_jobSections(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_id);
        scopes.avVL.load_vl_MilestoneCostCentersBak(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.getRecord(1));
    }
    else if (utils.hasRecords(foundset.sch_milestone_to_sch_schedule) 
            && utils.hasRecords(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail) 
            && utils.hasRecords(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section)) {

        scopes.avVL.set_vl_MilestoneCostCentersBak(foundset.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section);
    }
    else {
        application.setValueListItems('vl_jobSections', [], []);
        application.setValueListItems('vl_MilestoneCostCentersBak', [], []);
    }

}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param _form
 *
 * @return
 * @properties={typeid:24,uuid:"C3981AC8-60EE-4B9C-A6A5-7054C83FAE1A"}
 */
function onRecordSelection(event, _form)
{
	var ret = _super.onRecordSelection(event, _form);
	
	globals.avSchedule_selectedMilestoneUUID = ms_id;
	scopes.globals.avBase_selectedDeptID = dept_id;
	scopes.globals.avBase_selectedOpCatID = opcat_id;
	scopes.avVL.load_vl_MilestoneCostCenters(foundset.getSelectedRecord());
	
	return ret;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F2587D94-ED98-4EC4-8329-AE16A4AE93CD"}
 */
function onDataChange_dept(oldValue, newValue, event) {
	if(ms_flg_completed){
		dept_id = oldValue
		globals.showWarning('cantModifyMS')
		return true
	}
	scopes.globals.avBase_selectedDeptID = newValue
	scopes.avVL.clearVL('vl_MilestoneCostCenters')
	opcat_id=null
	ms_oper_name=null
	updateMSInfo()
	_dirty=true
	return true
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DEE139A2-2F47-4E1A-98B3-03FFA8FA7F41"}
 */
function onDataChange_opCat(oldValue, newValue, event) {
	if (ms_flg_completed) {
		dept_id = oldValue
		globals.showWarning('cantModifyMS')
		return true
	}
	
	//Checks if the milestone can be moved to a different operation category
	/***@type {JSDataSet} ***/
	var dsMilestone = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti,"select opcat_id, dept_id, ms_oper_name from sch_milestone where ms_id = '" + 
		foundset.getSelectedRecord().ms_id + "'",null,-1);
	
	/***@type {JSRecord<db:/avanti/eq_equipment>} ***/
	var rOldEquip = scopes.avScheduling.getEquipmentFromOpCat(dsMilestone.getValue(1,1));
	/***@type {JSRecord<db:/avanti/eq_equipment>} ***/
	var rNewEquip = scopes.avScheduling.getEquipmentFromOpCat(newValue);
	
	var oldEquipId;
	var newEquipId;
	if(!rOldEquip){
		oldEquipId = null;
	}
	else{
		oldEquipId = rOldEquip.equip_id;
	}
	
	if(!rNewEquip){
		newEquipId = null;
	}
	else{
		newEquipId = rNewEquip.equip_id;
	}

	// if we dont have an oldEquipId or newEquipId then we dont need to call checkIfMilestoneCanBeMovedToEquipment
	if (oldEquipId && newEquipId && oldEquipId != newEquipId) {
		checkIfMilestoneCanBeMovedToEquipment(oldEquipId, newEquipId);
	}
	else {
		milestoneMove = true;
	}
	
	if (milestoneMove) {
		scopes.globals.avBase_selectedOpCatID = newValue
		ms_opcat_modified = 1;
        ms_opcat_modified_by = "onDataChange_opCat";
		ms_oper_name = null;
		
		var bMSGrouped = (sch_milestone_to_sch_milestone_group && sch_milestone_to_sch_milestone_group.getSize() > 1);
		
		// check to see if new opcat supports group
		if (bMSGrouped) {
			updateGroup(rNewEquip)
		}
		else {
			updateMSInfo();
		}

		databaseManager.saveData(foundset.getSelectedRecord());
		scopes.avVL.load_vl_MilestoneCostCenters(foundset.getSelectedRecord());
		
		if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RecalcMilestoneTimeNeeded)) {
			scopes.avScheduling.setTimeNeededForNewEquip(foundset.getSelectedRecord(), rNewEquip);
		}

		globals.gAddingToSchedule = true;
		_dirty = true
	}
	else {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'),
			i18n.getI18NMessage('avanti.dialog.cannotMoveOutsideWorkPoolFamily'), i18n.getI18NMessage('avanti.dialog.ok'));
		foundset.getSelectedRecord().opcat_id = dsMilestone.getValue(1, 1);
		foundset.getSelectedRecord().dept_id = dsMilestone.getValue(1, 2);
		foundset.getSelectedRecord().ms_oper_name = dsMilestone.getValue(1, 3);
	}
	
	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"155AB727-7B39-4112-9671-E769DF580BA2"}
 */
function onAction_btnAdd(event) {
    
	if(!scopes.avUtils.isNavModeReadOnly()){
		var lastRecNum = foundset.getSize();
		var prev_rec = foundset.getRecord(lastRecNum) 
		var rec = foundset.getRecord(foundset.newRecord())
		
		rec.sequence_nr = lastRecNum + 1
		rec.ms_date_scheduled = null;
		rec.ms_date_due = null;
		rec.ms_date_completed = null;
		rec.ms_flg_ready = 0;
		rec.ms_flg_completed = 0;
		rec.ms_oper_name = null;
		rec.ms_time_budget = 0;
		rec.ms_cost_budget = 0;
		
		if (prev_rec) {
			rec.sch_id = prev_rec.sch_id
			rec.ordrevds_id = prev_rec.ordrevds_id
			rec.ms_pred_ms_id = prev_rec.ms_id
		}
		
		databaseManager.saveData(rec)	
		databaseManager.saveData(foundset)	
		
		foundset.sort("sequence_nr asc");
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"********-6ACC-4861-B8B6-1D07E38BA21F"}
 */
function onAction_btnDelete(event) {
    if (validateDeleteMilestone()) {
    	if(globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('servoy.general.warning'), globals.getDlgMsg('deleteMilestone?'), globals.yes, globals.no) == globals.yes){
    		if(foundset.sequence_nr < foundset.getSize()){ // if not the last rec then decrement task num of all tasks after
    			decrementTaskNums(foundset.sequence_nr)
    			if(foundset.sequence_nr > 1){ // if also not the fst rec then link predecessor and successor together to close gap
    				linkPredecessorAndSuccessor(foundset.getSelectedRecord())
    			}
    			else{
    				var numSuccs = foundset.sch_milestone_to_sch_milestone$successors.getSize();
    				for(var i=1;i<=numSuccs;i++){
    					var recSucc = foundset.sch_milestone_to_sch_milestone$successors.getRecord(i);
    					recSucc.ms_pred_ms_id = foundset.getSelectedRecord().ms_pred_ms_id;
    					//Deleting the dependencies for the successor milestone
    					recSucc = globals.avCalcs_schedule_deleteMilestoneDependency(recSucc);
    				}
    			}
    		}
    		
    		scopes.avScheduling.clearMilestoneCapacity(foundset.getSelectedRecord(), true);
    		clearMilestonesInProdJobCost(foundset.getSelectedRecord());
    		foundset.deleteRecord()
    		
    		databaseManager.saveData(foundset)
    	}
    }
}

/**
 * @return
 * @properties={typeid:24,uuid:"D5C04BCA-6EC9-46F1-9706-FDA141A1BE2B"}
 */
function validateDeleteMilestone() {
    var bValid = true;
    
    var rMilestone = foundset.getSelectedRecord();
    
    if (utils.hasRecords(rMilestone.sch_milestone_to_prod_job_cost)) {
        /***@type {JSFoundSet<db:/avanti/prod_job_cost>} ***/
        var fsProdJobCost = rMilestone.sch_milestone_to_prod_job_cost;
        
        for (var i = 1; i <= fsProdJobCost.getSize(); i++) {
            /***@type {JSRecord<db:/avanti/prod_job_cost>} ***/
            var rProdJobCost = fsProdJobCost.getRecord(i);
            
            if (utils.hasRecords(rProdJobCost.prod_job_cost_to_prod_job_cost_labour)) {
                /***@type {JSFoundSet<db:/avanti/prod_job_cost_labour>} ***/
                var fsProdJobCostLabour = rProdJobCost.prod_job_cost_to_prod_job_cost_labour;
                
                for (var j = 1; j <= fsProdJobCostLabour.getSize(); j++) {
                    /***@type {JSRecord<db:/avanti/prod_job_cost_labour>} ***/
                    var rProdJobCostLabour = fsProdJobCostLabour.getRecord(j);
                    
                    if ((rProdJobCostLabour.jcl_start_datetime) || (rProdJobCostLabour.jcl_total_cost)) {
                        bValid = false;
                    }
                }
            }
            
            if (utils.hasRecords(rProdJobCost.prod_job_cost_to_prod_job_cost_material)) {
                /***@type {JSFoundSet<db:/avanti/prod_job_cost_material>} ***/
                var fsProdJobCostMaterial = rProdJobCost.prod_job_cost_to_prod_job_cost_material;
                
                for (j = 1; j <= fsProdJobCostMaterial.getSize(); j++) {
                    /***@type {JSRecord<db:/avanti/prod_job_cost_material>} ***/
                    var rProdJobCostMaterial = fsProdJobCostMaterial.getRecord(j);
                    
                    if (rProdJobCostMaterial.jcm_total_cost != 0) {
                        bValid = false;
                    }
                }
            }
            
            if (utils.hasRecords(rProdJobCost.prod_job_cost_to_prod_job_cost_purchases)) {
                /***@type {JSFoundSet<db:/avanti/prod_job_cost_purchases>} ***/
                var fsProdJobCostPurchases = rProdJobCost.prod_job_cost_to_prod_job_cost_purchases;
                
                for (j = 1; j <= fsProdJobCostPurchases.getSize(); j++) {
                    /***@type {JSRecord<db:/avanti/prod_job_cost_purchases>} ***/
                    var rProdJobCostPurchases = fsProdJobCostPurchases.getRecord(j);
                    
                    if (rProdJobCostPurchases.jobp_total_cost != 0) {
                        bValid = false;
                    }
                }
            }
        }
    }
    
    if (!bValid) {
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), globals.getDlgMsg('milestoneHasCosts'), i18n.getI18NMessage('avanti.lbl.close'));
    }
    
    return bValid;
}

/**
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"3996E943-DE9A-4049-A7C3-B2E0D48A7A4E"}
 */
function clearMilestonesInProdJobCost(rMilestone) {
    if (utils.hasRecords(rMilestone.sch_milestone_to_prod_job_cost)) {
        /***@type {JSFoundSet<db:/avanti/prod_job_cost>} ***/
        var fsProdJobCost = rMilestone.sch_milestone_to_prod_job_cost;
        
        for (var i = 1; i <= fsProdJobCost.getSize(); i++) {
            /***@type {JSRecord<db:/avanti/prod_job_cost>} ***/
            var rProdJobCost = rMilestone.sch_milestone_to_prod_job_cost.getRecord(i);
            
            rProdJobCost.ms_id = null;
            
            databaseManager.saveData(rProdJobCost);
        }
    }
}

/**
 * @param {Number} deletedTaskNum
 *
 * @properties={typeid:24,uuid:"6DFED0A8-6484-4A94-BF1B-AE33B5205AFA"}
 */
function decrementTaskNums(deletedTaskNum){
	for(var i=1;i<=foundset.getSize();i++){
		var rec = foundset.getRecord(i)
		if(rec.sequence_nr > deletedTaskNum){
			rec.sequence_nr--
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} deletedRec
 *
 * @properties={typeid:24,uuid:"E652AE9C-4561-40B4-9B08-016A9BB792F9"}
 */
function linkPredecessorAndSuccessor(deletedRec){
	//foundset.sch_milestone_to_sch_milestone$pred_ms_id
	var numSuccs = foundset.sch_milestone_to_sch_milestone$successors.getSize();
	for(var i=1;i<=numSuccs;i++){
		var recSucc = foundset.sch_milestone_to_sch_milestone$successors.getRecord(i);
		recSucc.ms_pred_ms_id = deletedRec.ms_pred_ms_id;
		//Deleting the dependencies for the successor milestone
		recSucc = globals.avCalcs_schedule_deleteMilestoneDependency(recSucc);
		//Creating the dependencies for the successor's milestone using the operations from the deleted record's predecessor milestone
		if(deletedRec && utils.hasRecords(deletedRec.sch_milestone_to_sch_milestone$pred_ms_id)){
			globals.createDependency(recSucc,deletedRec.sch_milestone_to_sch_milestone$pred_ms_id.getRecord(1));
		}
	}	
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"A97EA5FA-842B-4793-B4D5-B3FD56BCF210"}
 */
function onDataChange_pred(oldValue, newValue, event) {
	if(foundset.getSelectedIndex()==1){
		return true
	}

	if(ms_flg_completed){
		if(utils.hasRecords(sch_milestone_to_sch_milestone$pred_ms_id)){
			sch_milestone_to_sch_milestone$pred_ms_id.sequence_nr = oldValue
		}
		globals.showWarning('cantModifyMS')
		return true
	}

	if(utils.hasRecords(sch_milestone_to_sch_milestone$pred_ms_id)){
		sch_milestone_to_sch_milestone$pred_ms_id.sequence_nr = oldValue
	}
	
	if(newValue != foundset.getSelectedRecord().sequence_nr && newValue <= foundset.getSize() && newValue > 0){
		ms_pred_ms_id = getPredFromSeqNum(newValue)
	}
	
	_dirty=true
	return true
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B67504CD-6939-4927-BC43-CB9125A5A9FF"}
 */
function onDataChange_UR(oldValue, newValue, event) {
	if(ms_flg_completed){
		ms_utilization_rate = oldValue
		globals.showWarning('cantModifyMS')
		return true
	}

	if(!newValue){
		newValue = 0
	}
	
	ms_time_budget = globals.avUtilities_roundNumber(globals.calcTimeNeededWithUR(ms_time_budget_no_ur_no_round, newValue), 0)
	ms_time_budget_formatted = get_ms_time_budget_formatted()
	
	if(forms.sch_schedule_dtl._scheduleDirection == 'F' && ms_date_scheduled){
		ms_date_due = globals.addMinutes(ms_date_scheduled, ms_time_budget)
	}
	else if(isSchedulableDept() && ms_date_due && ms_time_budget){		
		ms_date_scheduled = globals.addMinutes(ms_date_due, ms_time_budget * -1)
	}
	
	_dirty=true
	return true
}

/**
 * @return
 * @properties={typeid:24,uuid:"3214A33C-C53A-4484-B360-25B2BE5E132E"}
 */
function get_ms_time_budget_formatted()
{
	// sl-952 - display in hh:nn format
	if(ms_time_budget){
		var nHours = Math.floor(ms_time_budget / 60)
		var nMins = ms_time_budget % 60
		return nHours + ':' + utils.numberFormat(nMins, '00')
	}
	else{
		return '0:00'
	}
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"57481FE2-F749-4790-BEF7-17D25EFE72BC"}
 */
function onDataChange_SchedDate(oldValue, newValue, event) {
    // this event was sometimes firing a 2nd time, triggered by something within this function. and it was
    // writing an incorrect value, restore the correct one   
    if (bProcessingDateChange) {
        ms_date_scheduled = oldValue;
        return true;
    }

    bProcessingDateChange = true;

    var dOldStartDate = oldValue;
    var dOldEndDate = ms_date_due;
    
    try {
        if (!isSchedulableDept()) {
            ms_date_scheduled = oldValue;
            globals.showWarning('cantModifyMSNonSchedDept');
        }
        else if (!sch_milestone_to_sys_department.dept_active) {
            ms_date_scheduled = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('department')]);
        }
        else if (!sch_milestone_to_sys_operation_category.opcat_active) {
            ms_date_scheduled = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('OperationCategory')]);
        }
        else if (utils.hasRecords(sch_milestone_to_sys_cost_centre) && !sch_milestone_to_sys_cost_centre.cc_active) {
            ms_date_scheduled = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('CostCenter')]);
        }
        else if (!scopes.avScheduling.areThereAnyActiveEquipmentForMilestone(foundset.getSelectedRecord(), forms.sch_schedule_dtl._scheduleDirection)
                && !scopes.avScheduling.doesMilestoneScheduleEmps(foundset.getSelectedRecord())) {
            ms_date_scheduled = oldValue;
            globals.showWarning('equpmentInactive');
        }
        else if (ms_flg_completed) {
            ms_date_scheduled = oldValue;
            globals.showWarning('cantModifyMS');
        }
        else if (ms_date_scheduled < application.getTimeStamp()) {
            ms_date_scheduled = oldValue;
            globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('avanti.dialog.cannotMoveMilestoneIntoPast'), i18n.getI18NMessage('avanti.dialog.ok'));
        }
        else if (newValue == oldValue) {
            // old and new vals are the same - shouldnt happen - but put check in just in case, as below code assume a difference 
        }
        else {
            ms_date_scheduled = newValue;
            ms_date_due = plugins.DateUtils.addMinutes(newValue, ms_time_budget);

            if (ms_time_budget && ms_date_scheduled && ms_date_due) {
            	var sDirection;

            	// milestone moved forwards, move successors using forwards scheduling
    			if (newValue > oldValue) {
    				sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Forwards;
    			}
            	// milestone moved backwards, move predecessors using backwards scheduling
    			else if (newValue < oldValue) {
    				sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Backwards;
    			}
            	
                if (rescheduleMilestones(foundset.getSelectedRecord(), sDirection)) {
                    if (ms_date_scheduled != newValue) {
                        if (ms_date_scheduled == oldValue) {
                            scopes.avText.showInfo('revertedEnteredDates');
                        }
                        else {
                            scopes.avText.showInfo('cantUseEnteredDates');
                            _dirty = true;
                        }
                    }
                    else {
                        scopes.avText.showInfo('milestoneHaveBeenRescheduled');
                        _dirty = true;
                    }
                }
                else {
            		ms_date_scheduled = dOldStartDate;
            		ms_date_due = dOldEndDate;
                }
            }
        }

        bProcessingDateChange = false;
    }
    catch (ex) {
        bProcessingDateChange = false;
    }

    return true;
}

/**
 * @return {Boolean}
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {String} sDirection
 *
 * @properties={typeid:24,uuid:"8DBBB51A-B88F-4B58-8D4D-92B9142DA65B"}
 */
function rescheduleMilestones(rMilestone, sDirection){
    var rSchedule = rMilestone.sch_milestone_to_sch_schedule.getRecord(1);
    
    databaseManager.saveData(foundset);
    globals.gsJobCouldNotBeScheduledBecause = null;
    
	if (scheduleMilestones(rSchedule, sDirection, forms.sch_schedule_dtl._schBasedOnDate, null, null, rMilestone)) {
		scopes.avScheduling.clearMilestonesCapacity(rSchedule);
		return true;
	}
	else {
		// restore org dates and run scheduling again to restore to prev state
		scopes.avScheduling.restoreMilestonesBackedUpDates(rSchedule);
		scopes.avScheduling.clearMilestonesCapacity(rSchedule);

		var sMsg = scopes.avText.getDlgMsg('milestonesCantBeScheduledWithThisTime');

		if (globals.gsJobCouldNotBeScheduledBecause) {
			sMsg += " " + globals.gsJobCouldNotBeScheduledBecause;
		}

		scopes.avText.showWarning(sMsg, true);

		return false;
	}
}

/**
 * @return
 * @properties={typeid:24,uuid:"5A03673C-F539-48E4-9D37-CB8A5C5213AE"}
 */
function isSchedulableDept(){
	return (foundset.sch_milestone_to_sys_department.dept_schedule_flag == 1)
}

/**
 * @param {Date} startDate
 * @param {Date} stopDate
 *
 * @return
 * @properties={typeid:24,uuid:"3957D230-48F6-4B84-A153-B355BB7168D6"}
 */
function calcTimeNeeded(startDate, stopDate){
	return globals.getDiffInMinutes(startDate, stopDate)
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"98BC9F5B-3B68-412F-B697-0DF871E8F292"}
 */
function onDataChange_dateDue(oldValue, newValue, event) {
    // this event was sometimes firing a 2nd time, triggered by something within this function. and it was
    // writing an incorrect value, restore the correct one
    if (bProcessingDateChange) {
        ms_date_due = oldValue;
        return true;
    }

    bProcessingDateChange = true;
    
    var dOldStartDate = ms_date_scheduled;
    var dOldEndDate = oldValue;
    
    try {
        if (!isSchedulableDept()) {
            ms_date_due = oldValue;
            globals.showWarning('cantModifyMSNonSchedDept');
        }
        else if (!sch_milestone_to_sys_department.dept_active) {
            ms_date_due = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('department')]);
        }
        else if (!sch_milestone_to_sys_operation_category.opcat_active) {
            ms_date_due = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('OperationCategory')]);
        }
        else if (utils.hasRecords(sch_milestone_to_sys_cost_centre) && !sch_milestone_to_sys_cost_centre.cc_active) {
            ms_date_due = oldValue;
            scopes.avText.showWarning('inactiveDeptOpCatCC', null, [scopes.avText.getLblMsg('CostCenter')]);
        }
        else if (!scopes.avScheduling.areThereAnyActiveEquipmentForMilestone(foundset.getSelectedRecord(), forms.sch_schedule_dtl._scheduleDirection)
                && !scopes.avScheduling.doesMilestoneScheduleEmps(foundset.getSelectedRecord())) {
            ms_date_due = oldValue;
            globals.showWarning('equpmentInactive');
        }
        else if (ms_flg_completed) {
            ms_date_due = oldValue;
            globals.showWarning('cantModifyMS');
        }
        else if (ms_date_due < application.getTimeStamp()) {
            ms_date_due = oldValue;
            globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('avanti.dialog.cannotMoveMilestoneIntoPast'), i18n.getI18NMessage('avanti.dialog.ok'));
        }
        else if (newValue == oldValue) {
            // old and new vals are the same - shouldnt happen - but put check in just in case, as below code assume a difference 
        }
        else {
            ms_date_due = newValue;
            ms_date_scheduled = plugins.DateUtils.addMinutes(newValue, ms_time_budget * -1);

            if (ms_date_due == ms_date_scheduled) {
                ms_time_budget = 0;
            }

            if (ms_time_budget && ms_date_scheduled && ms_date_due) {
            	var sDirection;

            	// milestone moved forwards, move successors using forwards scheduling
    			if (newValue > oldValue) {
    				sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Forwards;
    			}
            	// milestone moved backwards, move predecessors using backwards scheduling
    			else if (newValue < oldValue) {
    				sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Backwards;
    			}
            	
                if (rescheduleMilestones(foundset.getSelectedRecord(), sDirection)) {
                    if (ms_date_due != newValue) {
                        if (ms_date_due == oldValue) {
                            scopes.avText.showInfo('revertedEnteredDates');
                        }
                        else {
                            scopes.avText.showInfo('cantUseEnteredDates');
                            _dirty = true;
                        }
                    }
                    else {
                        scopes.avText.showInfo('milestoneHaveBeenRescheduled');
                        _dirty = true;
                    }
                }
                else {
            		ms_date_scheduled = dOldStartDate;
            		ms_date_due = dOldEndDate;
                }
            }
        }

        bProcessingDateChange = false;
    }
    catch (ex) {
        bProcessingDateChange = false;
    }

    return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"EB2DBC43-F51D-417A-A111-9642EFDDAC11"}
 */
function onDataChange_timeNeeded(oldValue, newValue, event) {
	if (ms_flg_completed) {
		ms_time_budget = oldValue;
		globals.showWarning('cantModifyMS');
		return true;
	}

	var sDirection = null;
	var rMilestone = foundset.getSelectedRecord();
	var bPushSuccessor = false;
	var dOldStartDate = ms_date_scheduled;
	var dOldEndDate = ms_date_due;

	ms_time_budget = convertFormattedTimeToMins(newValue);

	if (forms.sch_schedule_dtl._scheduleDirection == scopes.avScheduling.SCHEDULE_DIRECTION.Forwards && ms_date_scheduled) {
		ms_date_due = globals.addMinutes(ms_date_scheduled, ms_time_budget);
		sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Forwards;
	}
	else if (forms.sch_schedule_dtl._scheduleDirection == scopes.avScheduling.SCHEDULE_DIRECTION.Backwards && ms_date_due) {
		ms_date_scheduled = globals.addMinutes(ms_date_due, ms_time_budget * -1);
		sDirection = scopes.avScheduling.SCHEDULE_DIRECTION.Backwards;
		
		var rSuccessor = scopes.avScheduling.getSuccessor(rMilestone, true, true);

		// time needed change has caused ms_date_scheduled to be > rSuccessor.ms_date_scheduled - have to push successor
		if (rSuccessor && ms_date_scheduled > rSuccessor.ms_date_scheduled) {
			bPushSuccessor = true;
		}
	}

	if (ms_time_budget_no_ur_no_round) {
		ms_utilization_rate = globals.calcURFromTimeNeededs(ms_time_budget_no_ur_no_round, ms_time_budget);
	}

	if (ms_time_budget && ms_date_scheduled && ms_date_due && sDirection) {
		var bRescheduleSuccess = false;
		
		if (rescheduleMilestones(rMilestone, sDirection)) {
			if (bPushSuccessor) {
				if (rescheduleMilestones(rMilestone, scopes.avScheduling.SCHEDULE_DIRECTION.Forwards)) {
					bRescheduleSuccess = true;
				}
			}
			else {
				bRescheduleSuccess = true;
			}
		}
		
		if (bRescheduleSuccess) {
			scopes.avText.showInfo('milestoneHaveBeenRescheduled');
		}
		else {
			ms_time_budget = convertFormattedTimeToMins(oldValue);
			ms_date_scheduled = dOldStartDate;
			ms_date_due = dOldEndDate;

			if (ms_time_budget_no_ur_no_round) {
				ms_utilization_rate = globals.calcURFromTimeNeededs(ms_time_budget_no_ur_no_round, ms_time_budget);
			}
		}
	}
	
	return true;
}

/**
 * @param {String} fmtTime
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"0865012C-5609-46E2-B7A3-05963F8E93C6"}
 */
function convertFormattedTimeToMins(fmtTime){
	if(fmtTime.indexOf(':') > -1){
		var aTimes = fmtTime.split(':')
		var hrs = parseInt(aTimes[0])
		var mins = parseInt(aTimes[1])
		return hrs * 60 + mins
	}
	else{
		return 0
	}
	
	
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"1FF190DC-08D9-43BB-92D8-C8113049731A"}
 */
function onDataChange_sect(oldValue, newValue, event) {
	if(ms_flg_completed){
		ordrevds_id = oldValue;
		globals.showWarning('cantModifyMS');
	}
	else{
	    ordrevdstask_id = null;
		_dirty=true;
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"143F3B47-4B42-4CCF-9E99-4567A32FE8AC"}
 */
function onDataChange_op(oldValue, newValue, event) {
	if(ms_flg_completed){
		dept_id = oldValue
		globals.showWarning('cantModifyMS')
	}
	else{
		updateMSInfo()
		_dirty=true
	}
	return true
}

/**
 * @properties={typeid:24,uuid:"2A99CFFC-8905-424E-9F03-673AB53E94D5"}
 */
function updateMSInfo() {
	if (!utils.hasRecords(sch_milestone_to_sch_milestone_group)) {
		sch_milestone_to_sch_milestone_group.loadAllRecords();
	}
	
	// clear old ms info
	if (utils.hasRecords(sch_milestone_to_sch_milestone_group)) {
		sch_milestone_to_sch_milestone_group.deleteAllRecords();
	}
	else if (scopes.avDB.SQLQuery("SELECT COUNT(*) FROM sch_milestone_group WHERE ms_id = ?", null, [ms_id.toString()]) > 0) {
		scopes.avUtils.devLog("SL-25556", "Milestone Group recs not loaded in updateMSInfo()", true, ms_id, null, null, null, true);
	}

	// create new ms group rec
	if (opcat_id && ms_oper_name) {
		/**@type {JSRecord<db:/avanti/sys_cost_centre>} */
		var rCC = scopes.avDB.getRec('sys_cost_centre', ['opcat_id', 'cc_desc'], [opcat_id, ms_oper_name])
		if (rCC) {
			var rGroup = sch_milestone_to_sch_milestone_group.getRecord(sch_milestone_to_sch_milestone_group.newRecord());

			rGroup.cc_id = rCC.cc_id;
			rGroup.sequence_nr = 1;
			rGroup.taskcostlink_id = getNewCostLink(rGroup.cc_id);
			rGroup.taskcostlink_set_by = "updateMSInfo";
			
			databaseManager.saveData(rGroup);
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/eq_equipment>} rNewEquip
 *
 * @properties={typeid:24,uuid:"AA27E560-A04F-43E3-AC9A-A9766B03BAE4"}
 */
function updateGroup(rNewEquip) {
	// this is modified code from schedule board
	if (rNewEquip) {
        var rNewTask = scopes.avScheduling.getEquipmentTask(rNewEquip);

        if (rNewTask && utils.hasRecords(sch_milestone_to_sch_milestone_group)) {
            for (var i = 1; i <= sch_milestone_to_sch_milestone_group.getSize(); i++) {
                var rMSGroup = sch_milestone_to_sch_milestone_group.getRecord(i);

                if (utils.hasRecords(rMSGroup.sch_milestone_group_to_sa_task_cost_link)) {
                    var rOldCostLink = rMSGroup.sch_milestone_group_to_sa_task_cost_link.getRecord(1);
                    var rNewCostLink = getTaskCostLinkForTaskOperation(rNewTask, rOldCostLink.taskoper_id, div_id, plant_id);

                    if (rNewCostLink) {
                        rMSGroup.taskcostlink_id = rNewCostLink.taskcostlink_id;
                        rMSGroup.taskcostlink_set_by = "updateGroup";
                        rMSGroup.cc_id = rNewCostLink.cc_id;

                        databaseManager.saveData(rMSGroup);
                    }
                }
            }
            
            ms_oper_name = scopes.avScheduling.getNewMilestoneOperName(foundset.getSelectedRecord());
        }
	}
}

/**
 * @param {UUID} sCCID
 * 
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"B7473C75-3DB0-4696-BF6A-1EC81861FFC2"}
 */
function getNewCostLink(sCCID) {
	/**@type {UUID} */
	var uCostLinkID = null;
	
	if (opcat_id && sCCID) {
		/***@type {JSRecord<db:/avanti/eq_equipment>} ***/
		var rEquip = scopes.avScheduling.getEquipmentFromOpCat(opcat_id);
		
		if (rEquip) {
			var rTask = scopes.avScheduling.getEquipmentTask(rEquip);

			if (rTask) {
				var sSQL = "SELECT taskcostlink_id \
							FROM sa_task_cost_link \
							WHERE \
								org_id = ? \
								AND task_id = ? \
								AND cc_id = ?";
				var aArgs = [globals.org_id, rTask.task_id.toString(), sCCID.toString()];
				
				uCostLinkID = scopes.avDB.SQLQuery(sSQL, null, aArgs);
			}
		}
	}
	
	return uCostLinkID;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"3D2FF047-B08D-47DD-B257-AAFF19CE1896"}
 */
function onDataChange_ready(oldValue, newValue, event) {
	if(ms_flg_completed){
		ms_pred_ms_id = oldValue
		globals.showWarning('cantModifyMS')
	}

	return true
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8AF8253E-2A11-4F71-96E5-95E8FF3D11BF"}
 */
function onDataChange_compDate(oldValue, newValue, event) {
	if(newValue){
		ms_flg_completed = 1;
		
	}
	else{
		ms_flg_completed = 0;
		
	}
	databaseManager.saveData(foundset.getSelectedRecord());
	scopes.avCost.updateLastCompletedMilestone(foundset.getSelectedRecord());
	return true
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"30CAE1FC-5FE6-4F6B-BE1E-3174710F1057"}
 */
function onDataChange_taskNum(oldValue, newValue, event) {
	_dirty=true
	return true
}

/**
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"D5135EDD-F7B1-4C6C-A6C6-8B290E328BB0"}
 */
function onDataChange_markMilestoneDone(oldValue, newValue, event) {
    var rMS = foundset.getSelectedRecord();
    
    if (newValue == 1) {
        ms_date_completed = application.getTimeStamp();
        completed_by_id = globals.avBase_employeeUUID;
        
        scopes.avCost.completeMilestoneOperations(rMS);
        
        //Set next milestone ready
        scopes.avShopFloor.setReadyFlagOnSuccessorMilestones(null, rMS);
    }
    else {
        ms_date_completed = null;
        completed_by_id = null;

        // unflag auto-JDF-exported Flag
        scopes.avJDF.toggleWasAutoJDFExportedFlagFromMilestone(rMS, false); 
        scopes.avCost.resetMilestoneOperations(rMS);
    }

    scopes.avCost.updateLastCompletedMilestone(rMS);

    // set this to false. we want to process code in sch_milestone_afterRecordUpdate() 
    scopes.avScheduling.bInReadyToSchedule = false;
    databaseManager.saveData(rMS);
    scopes.avScheduling.bInReadyToSchedule = true;
    
    _dirty = true;
    
    return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"1888E080-E06E-4AA5-863D-B3A8FABF6D53"}
 */
function onAction_btnShuffle(event) {
	if(!scopes.avUtils.isNavModeReadOnly()){
		scopes.globals.avUtilities_shuffle(event, true);

		// _shuffleSource + _shuffleDest have same values as avUtilities_shuffleSource + avUtilities_shuffleDest, which are used by avUtilities_shuffle(), but are cleared out   
		if (_shuffleSource == 0){
			_shuffleSource = foundset.getSelectedIndex();
		}
		else{
			_shuffleDest = foundset.getSelectedIndex();
		}

		if(_shuffleSource && _shuffleDest){
			for(var i=_shuffleDest+1;i>=_shuffleSource;i--){
				adjustPredecessors(i, i-1);
			}
			
			foundset.getRecord(1).ms_pred_ms_id=null;
		}
	}
	
	return;
}

/**
 * @param {Number} srcIdx
 * @param {Number} destIdx
 *
 * @properties={typeid:24,uuid:"524AD0A6-5F49-47D5-ACC3-B07B74FA697F"}
 */
function adjustPredecessors(srcIdx, destIdx){
	if(srcIdx && destIdx){
		var recSource = foundset.getRecord(srcIdx)
		var recDest = foundset.getRecord(destIdx)
		
		if(recSource && recDest){
			// only make the dest rec the pred of the now moved source if they are both the same section
			if(recSource.ordrevds_id == recDest.ordrevds_id){ // && recSource.sch_milestone_to_sys_department.dept_schedule_flag && recDest.sch_milestone_to_sys_department.dept_schedule_flag){
				recSource.ms_pred_ms_id = recDest.ms_id
			}
		}
	}
}

/**
 * @param {Number} seqNum
 *
 * @return
 * @properties={typeid:24,uuid:"4BD97912-5F80-4B5B-9CD6-0D9BC7FC7BBE"}
 */
function getPredFromSeqNum(seqNum){
	for(var i=1;i<=foundset.getSize();i++){
		if(foundset.getRecord(i).sequence_nr == seqNum){
			return foundset.getRecord(i).ms_id
		}
	}
	
	return null
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @public 
 *
 * @properties={typeid:24,uuid:"0CE586DB-A6F5-445C-9A95-96D63898462F"}
 */
function onAction_btnEmps(event) {
    var sTitle = scopes.avText.getLblMsg('ScheduledEmployeesFor') + ms_oper_name;
    globals.DIALOGS.showFormInModalDialog(forms.sch_ms_scheduled_emps_tbl, -1, -1, 720, 600, sTitle, true, false, "dlgMSSCheduledEmps",true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"E71D4E9A-76AA-4682-B041-AC9E53A75D44"}
 */
function onAction_split(event) {
    if (_to_sys_organization.org_sch_locked == 1) {        
        scopes.avScheduling.showScheduleBoardLockedMessage();        
    }
    else {
        scopes.avScheduling.splitMilestone(foundset.getSelectedRecord(), forms.sch_schedule_dtl._scheduleDirection);
        databaseManager.recalculate(foundset);
    }    
}

/**
 * @properties={typeid:24,uuid:"0B66B713-86A0-421F-9F72-1BA5BC02B213"}
 */
function oldSplitCode(){
    var rOrgRec = foundset.getSelectedRecord();
    var rNextRec;

    // make a space for new rec to be inserted
    databaseManager.saveData(foundset);
    foundset.sort('sequence_nr asc');
    for(var i=rOrgRec.sequence_nr+1; i<=foundset.getSize(); i++){
        rNextRec = foundset.getRecord(i);
        
        rNextRec.sequence_nr++;
    }
    
    // create new rec
    foundset.duplicateRecord(foundset.getSelectedIndex(), false, true);
    
    var rNewRec = foundset.getSelectedRecord();
    
    rNewRec.sequence_nr = rOrgRec.sequence_nr+1;
    rNewRec.ms_pred_ms_id = rOrgRec.ms_id;

    // update predecessors
    databaseManager.saveData(foundset);
    foundset.sort('sequence_nr asc');
    var rPrevRec = rNewRec;
    for(i=rNewRec.sequence_nr+1; i<=foundset.getSize(); i++){
        rNextRec = foundset.getRecord(i);
        rNextRec.ms_pred_ms_id = rPrevRec.ms_id;
        rPrevRec = rNextRec;
    }

    // select new rec
    foundset.setSelectedIndex(rNewRec.sequence_nr);
}
