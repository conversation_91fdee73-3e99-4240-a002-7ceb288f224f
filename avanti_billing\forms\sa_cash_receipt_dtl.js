/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"1EC4CBC1-BFB5-4CAB-8BA1-00CF7BB5D3A5",variableType:4}
 */
var _bWorkatoUseCashReceiptsRegister;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"26BDAA0F-DE76-42D5-866D-B3F701117303",variableType:8}
 */
var _nMaxDepositApplyAmount = 0.00;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F7EFFBDB-93F2-4C45-ABD1-63E5D6266E20"}
 */
var sPayMethodType = '';

/**
 * @properties={typeid:35,uuid:"751B8C38-D567-4E73-82E5-3CDC6735EBDA",variableType:-4}
 */
var bUpdateAppliedAmount = false;

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"23AB5242-A8E7-40F4-8658-183E56AFC6F2",variableType:-4}
 */
var tblFormNames = ['sa_cash_receipt_invoices_dtl_tbl', 'sa_cash_receipt_tab_order_details', 'sa_cash_receipt_note_tbl',
					'sa_cash_receipt_dist_tbl', 'sa_cash_receipt_dtl'];

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"5D1F8C48-9878-42B1-9898-801A3BA7A7A7"}
 */
function onShowForm(firstShow, event) {
	
	 _bWorkatoIntegration = globals.isWorkatoIntegration();
	 _bWorkatoUseCashReceiptsRegister = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.WorkatoUseCashReceiptsRegister);
	 _bIsNetSuiteIntegration = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration) == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoNetSuiteCloudAccounting;
	  
	if(firstShow == true) {
		foundset.sort('sa_cash_receipt_date desc');
		if(( foundset.getSize() > 0 ) && ( foundset.getSelectedRecord().sa_cash_receipt_status == 'P') ) {
			dc_new(event,'forms.sa_cash_receipt_dtl');
		}
	} else {
		refreshUI();
	}
	
    if (cust_id) {
        scopes.globals.avBase_selectedCustomerUUID = cust_id;
    }
	
	return _super.onShowForm(firstShow, event);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"2D8FCF49-F3B5-4201-9DF2-38908185D9BF"}
 */
function onDataChangeAmount(oldValue, newValue, event) {
	return paymentAmountChanged(oldValue);
}

/**
 * 
 * @param oldValue
 *
 * @return
 * @properties={typeid:24,uuid:"7C2C7887-2860-4B9D-BD91-76DD861530EC"}
 */
function paymentAmountChanged(oldValue) {
	getPayMethodType();
	
	if((sPayMethodType == 'PEA' || sPayMethodType == 'CDA') && sa_cash_receipt_amount < 0) {
		sa_cash_receipt_amount = oldValue;
		return false;
	}
	
	if(deposit_account_type != null && deposit_account_type != 0) {
		sa_cash_receipt_payment_amt = sa_cash_receipt_amount;
	}
	
	var bValidationResult = validatePaymentMethodAndAmount();
	
	if(!bValidationResult) {
		sa_cash_receipt_amount = oldValue;
		foundset.setPaymentAmount(forms.sa_cash_receipt_invoices_dtl_tbl.foundset);
		return false;
	}
	
	updateStatus();
	
	return true;
}

/**
 * @properties={typeid:24,uuid:"123F450D-A221-4839-A27A-E9A04F9CD8BC"}
 */
function refreshUI() {
	elements.sa_cash_receipt_date.format = globals.avBase_dateFormat;
    elements.sa_cash_receipt_trans_date.format = globals.avBase_dateFormat;
    
    elements.sa_cash_receipt_amount.format = globals.avBase_currencyFormat;
    elements.invoice_total_amount.format = globals.avBase_currencyFormat;
    elements.invoice_total_amount_remaining.format = globals.avBase_currencyFormat;
    elements.overpayment_amount.format = globals.avBase_currencyFormat;
    
    elements.sa_cash_receipt_amount_exchanged.format = globals.avBase_currencyFormat;
    elements.invoice_total_amount_exchanged.format = globals.avBase_currencyFormat;
    elements.invoice_total_amount_remaining_exchanged.format = globals.avBase_currencyFormat;
    elements.overpayment_amount_exchanged.format = globals.avBase_currencyFormat;
    
    elements.sacr_reg_number.visible = (foundset.getSize() > 0 && utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_register) ? true : false);
    elements.sa_cash_receipt_funds_deposited.visible = _bIsNetSuiteIntegration;
    
    if (scopes.avUtils.trackCurrencyExchange(foundset.curr_id)) {
    	elements.sa_cash_receipt_amount.visible = false;
    	elements.invoice_total_amount.visible = false;
    	elements.invoice_total_amount_remaining.visible = false;
    	elements.overpayment_amount.visible = false;
    	elements.sa_cash_receipt_amount_exchanged.visible = true;
    	elements.invoice_total_amount_exchanged.visible = true;
    	elements.invoice_total_amount_remaining_exchanged.visible = true;
    	elements.overpayment_amount_exchanged.visible = true;
    }
    else {
    	elements.sa_cash_receipt_amount.visible = true;
    	elements.invoice_total_amount.visible = true;
    	elements.invoice_total_amount_remaining.visible = true;
    	elements.overpayment_amount.visible = true;
    	elements.sa_cash_receipt_amount_exchanged.visible = false;
    	elements.invoice_total_amount_exchanged.visible = false;
    	elements.invoice_total_amount_remaining_exchanged.visible = false;
    	elements.overpayment_amount_exchanged.visible = false;
    }
    
    forms.sa_cash_receipt_dtl_tbl.refreshUI();

    //Hide the distribution tab unless the status is updated or posted
    scopes.globals.avUtilities_tabSetEnabled(controller.getName(), "tabs", 3, false);

    if (foundset.getSize() > 0 
            && (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Updated 
            || sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted
            || sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted)) {
        scopes.globals.avUtilities_tabSetEnabled(controller.getName(), "tabs", 3, true);
        forms.sa_cash_receipt_dist_tbl.refreshUI();
    }

    updateStatus();

    if (deposit_account_type == null || deposit_account_type == 0) {
        if (bUpdateAppliedAmount) {
            foundset.setPaymentAmount(forms.sa_cash_receipt_invoices_dtl_tbl.foundset);
        }
    }
    else {
        if (bUpdateAppliedAmount) {
            forms.sa_cash_receipt_invoices_dtl_tbl.clearAll();
            forms.sa_cash_receipt_tab_order_details.clearAll();
            sa_cash_receipt_payment_amt = sa_cash_receipt_amount;
            
            if (scopes.avUtils.trackCurrencyExchange(foundset.curr_id)) {
            	sa_cash_receipt_payment_amt_exchanged = sa_cash_receipt_amount_exchanged;
            }
        }
        forms.sa_cash_receipt_invoices_dtl_tbl.foundset.clear();
        forms.sa_cash_receipt_tab_order_details.foundset.clear();
    }

    getPayMethodType();

    forms.sa_cash_receipt_note_tbl.setNoteSource(cust_id, sa_cash_receipt_id, globals.$NoteObjectRelation_CashReceipt, controller.getName());
    forms.sa_cash_receipt_invoices_dtl_tbl.refreshUI();
    forms.sa_cash_receipt_invoices_dtl_tbl.setColumnVisibility(foundset.getSelectedRecord());
    
    if (utils.hasRecords(foundset.sa_cash_receipt_to_sa_cash_receipt_cancel) 
            && foundset.sa_cash_receipt_to_sa_cash_receipt_cancel.crc_status == scopes.avUtils.ENUM_CASH_RECEIPT_CANCEL_STATUS.Posted) {
        elements.lblCancelledReceipt.text = i18n.getI18NMessage("avanti.dialog.receiptCancelledOn") + sa_cash_receipt_to_sa_cash_receipt_cancel.sa_cash_receipt_cancel_document_num;
        elements.lblCancelledReceipt.visible = true;
    }
    else {
        elements.lblCancelledReceipt.text = null;
        elements.lblCancelledReceipt.visible = false;
    }
    
    setTabVisibility();
    
    _super.refreshUI();
}

/**
 * Update the status based on invoice's balances and adjustment balance and GLs.
 * @param {Number} [notify]
 * @properties={typeid:24,uuid:"2D7DEA2C-51D3-40BD-9833-6486E5C9CB46"}
 */
function updateStatus(notify) {
	
	
	var sOriginalStatus = sa_cash_receipt_status;
	
	if(sa_cash_receipt_status != 'P' && sa_cash_receipt_status != 'C') {
		var invoice_balance_adjustment_status = forms.sa_cash_receipt_invoices_dtl_tbl.getBalanceAndAdjustmentStatus();
		var bPaymentNumberMissing = false;
		var nRemainingAmount = 0.0;
		var bCurrencyEchange = false;
		
		if (scopes.avUtils.trackCurrencyExchange(foundset.curr_id)) {
		
			bCurrencyEchange = true;
		}
		
		if (bCurrencyEchange) {
			nRemainingAmount = Math.abs(sa_cash_receipt_remaining_amt_exchanged);
		}
		else {
			nRemainingAmount = Math.abs(sa_cash_receipt_remaining_amt);
		}
		
		var accountingIntegration = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration);
		if(accountingIntegration == 'GP') {
			if(!sa_cash_receipt_num) {
				bPaymentNumberMissing = true;
			}
		}
		
		// GD - Apr 13, 2016: SL-8427: notifications should only be showing on save, not when called from refreshUI
		if(sa_cash_receipt_status == 'H' && nRemainingAmount < 0.005 ) {
			if(invoice_balance_adjustment_status == 1) {
				if(notify == 1) globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notBalanced'),i18n.getI18NMessage('avanti.dialog.ok'));
			} else if(bPaymentNumberMissing) {
				if(notify == 1) globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.missingPaymentNumber'),i18n.getI18NMessage('avanti.dialog.ok'))
			} else if(notify == 1) {
				if(sa_cash_receipt_status != 'U' && !_bHoldButtonPressed) {
					var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.updateStatus'),i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
					if(answer == i18n.getI18NMessage('avanti.dialog.yes')) {
						sa_cash_receipt_status = 'M';
					}
				}
			}
		} else if(nRemainingAmount < 0.005 && sa_cash_receipt_status != 'P') {
			if(invoice_balance_adjustment_status == 1) {
				if(notify == 1) globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notBalanced'),i18n.getI18NMessage('avanti.dialog.ok'));
				if (foundset.sa_cash_receipt_document_num) {
					addLoggingForSL27985 ('Invoice balance is not adjusted', foundset, nRemainingAmount, bCurrencyEchange);
				}
				sa_cash_receipt_status = 'H';
			} else if(sa_cash_receipt_status != null && bPaymentNumberMissing) {
				if(notify == 1) globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.missingPaymentNumber'),i18n.getI18NMessage('avanti.dialog.ok'));
				sa_cash_receipt_status = 'H';
				if (foundset.sa_cash_receipt_document_num) {
					addLoggingForSL27985 ('Cash receipt payment missing', foundset, nRemainingAmount, bCurrencyEchange);
				}
			} else {
				if(sa_cash_receipt_status != 'U' && sa_cash_receipt_status != 'M') {
					sa_cash_receipt_status = 'M';
				}
			}
			
		} else if(sa_cash_receipt_status != 'P') {
			if (sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.OnHold && foundset.sa_cash_receipt_document_num) {
				addLoggingForSL27985 ('This transaction is not in balance', foundset, nRemainingAmount, bCurrencyEchange);
			}
			sa_cash_receipt_status = 'H';
			if(notify == 1) {
				globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notMatched'),i18n.getI18NMessage('avanti.dialog.ok'));
			}
		}
		scopes.avBilling.$cash_receipt_status = sa_cash_receipt_status;
	}
	
	if (!_bWorkatoUseInvoiceRegister && sOriginalStatus && sOriginalStatus != sa_cash_receipt_status) {
		if (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted) {
			generateDistribution();
		}
		else {
			clearDistribution();
		}
	}
}

/**
 * Adds logging for Create cash receipt
 * @param {String} sReason
 * @param {JSFoundSet<db:/avanti/sa_cash_receipt>} fsCashReceipt
 * @param {Number} nRemainingAmount
 * @param {Boolean} bCurrencyEchange
 *
 * @properties={typeid:24,uuid:"94301BC2-**************-1B87665A5818"}
 */
function addLoggingForSL27985 (sReason, fsCashReceipt, nRemainingAmount, bCurrencyEchange) {
	scopes.avUtils.devLog("SL-27985", sReason + '. Cash receipt: ' + fsCashReceipt.sa_cash_receipt_document_num +
		' Applied Amount: ' + fsCashReceipt.sa_cash_receipt_payment_amt +
		' Payment Amount: ' + fsCashReceipt.sa_cash_receipt_amount +
		' Remaining Amount: ' + nRemainingAmount +
		' Currency Exchange: ' + bCurrencyEchange , true);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"1E79B0BD-00C3-4554-B30B-EDFC23728860"}
 */
function onDataChangeCustomer(oldValue, newValue, event) {
	if(utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_order_payment$payment)){
		scopes.avText.showWarning('cantChangeCustOnCR');
		cust_id = oldValue;
		return false;
	}
	
	if(utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_detail)){
		for (var nDetailIndex = 1; nDetailIndex <= sa_cash_receipt_to_sa_cash_receipt_detail.getSize(); nDetailIndex++) {
			var rCashReceiptDetail = sa_cash_receipt_to_sa_cash_receipt_detail.getRecord(nDetailIndex);
			if(rCashReceiptDetail.invoice_payment_amount || rCashReceiptDetail.invoice_discount_amount || rCashReceiptDetail.invoice_adjustment_amount) {
				scopes.avText.showWarning('cantChangeCustOnCR_Invoice');
				cust_id = oldValue;
				return false;
			}
		}
	}
	
	
	if(utils.hasRecords(sa_cash_receipt_to_sa_customer)) {
		if(!paymethod_id){
			paymethod_id = sa_cash_receipt_to_sa_customer.paymethod_id;
			if(utils.hasRecords(sa_cash_receipt_to_sa_payment_method)){
				bank_account_glacct_id = sa_cash_receipt_to_sa_payment_method.glacct_id;
			}
		}
		
		curr_id = sa_cash_receipt_to_sa_customer.curr_id;
	} else {
		curr_id = null;
	}
	
	scopes.globals.avBase_selectedCustomerUUID = cust_id;
	
	//bank_account_glacct_id = globals.avGL_getControlAccount('BANK', curr_id) 
	scopes.avBilling.$cash_receipt_cust_id = cust_id;
	forms.sa_cash_receipt_invoices_dtl_tbl.loadRecords();
	
// 	elements.sa_cash_receipt_num.requestFocus();
	
	forms.sa_cash_receipt_tab_order_details.loadOrders();
	forms.sa_cash_receipt_note_tbl._noteSourceID = cust_id;
	refreshUI();
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"2CA81415-BDE4-4078-B825-49F3FD5A3AE3"}
 */
function onActionOnHold(event) {
	if(globals.nav.mode == 'edit' && sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted) {
		sa_cash_receipt_status = scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.OnHold;
		_bHoldButtonPressed = true;
		clearDistribution();
		refreshUI();
	} else {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'))
	}
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"914BD96A-A54B-47F3-BF71-8739A06E53C3"}
 */
function onActionReadyToBePosted(event) {
	
	var invoice_balance_adjustment_status = forms.sa_cash_receipt_invoices_dtl_tbl.getBalanceAndAdjustmentStatus();
	var bCurrencyEchange = false;
	
	if (scopes.avUtils.trackCurrencyExchange(foundset.curr_id)) {
	
		bCurrencyEchange = true;
	}
		 
	if(scopes.avUtils.isNavModeReadOnly()) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.browseMode_msg'));
	}
	else if(sa_cash_receipt_status == 'P') {
		scopes.avText.showWarning('thisCashReceiptHasAlreadyBeenPosted');
	}
	else if(!(sa_cash_receipt_amount > 0)) {
		scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.paymentAmount');
	}
	// sl-3509 - hp - feb-22-2016 - removed 'utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_detail)' - deposits and tender transactions dont have these records.
	// its not really necessary to check anyways. what we are concerned with is whether sa_cash_receipt_amount = sa_cash_receipt_payment_amt. sa_cash_receipt_payment_amt
	// will be set if they have filled in a sa_cash_receipt_detail rec or if its a deposits or tender transaction. did additional check for sa_cash_receipt_amount above, to make
	// sure thats entered, so we're not comparing zero to zero.
	//	if(utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_detail) && Math.abs(sa_cash_receipt_amount - sa_cash_receipt_payment_amt) < 0.005) 
	else if(bCurrencyEchange
			&& Math.abs(sa_cash_receipt_remaining_amt_exchanged) > 0.005) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notMatched'),i18n.getI18NMessage('avanti.dialog.ok'));
	}
	else if(bCurrencyEchange == false && Math.abs(sa_cash_receipt_remaining_amt) > 0.005) {

		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notMatched'),i18n.getI18NMessage('avanti.dialog.ok'));
	}
		else if(invoice_balance_adjustment_status == 1) {
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.notBalanced'),i18n.getI18NMessage('avanti.dialog.ok'));
	} else {
		sa_cash_receipt_status = 'M'
	} 
	
	generateDistribution();
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"8ECBF868-960F-4E8E-8C6C-2B1A54E2F293"}
 */
function onRecordSelection(event, _form) {
	var result = _super.onRecordSelection(event, _form)
	
	scopes.globals.avBase_selectedCustomerUUID = cust_id;
	elements.sa_cash_receipt_auto_apply.visible = sa_cash_receipt_auto_apply == 1;
	
	if(sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted 
			&& sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted
			&& scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs") == 3) {
		scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tabs", 1)
	}

	forms.sa_cash_receipt_invoices_dtl_tbl.refreshUI();
	forms.sa_cash_receipt_invoices_dtl_tbl.setColumnVisibility(foundset.getSelectedRecord());
	
	// old rec before paymethod field added - show old field - dont let them change it tho
	if(!paymethod_id && sa_cash_receipt_source){
		elements.sa_cash_receipt_source_label.visible = true;
		elements.sa_cash_receipt_source.visible = true;
		elements.paymethod_id_label.visible = false;
		elements.paymethod_id.visible = false;
	}
	else{
		elements.sa_cash_receipt_source_label.visible = false;
		elements.sa_cash_receipt_source.visible = false;
		elements.paymethod_id_label.visible = true;
		elements.paymethod_id.visible = true;
	}
	
	setDepositFields();
	setOrdersTabVisibility();
	
	forms.sa_cash_receipt_tab_order_details.loadOrders();
	
	return result 
}

/**
 * @return
 * @properties={typeid:24,uuid:"EF805687-5168-4347-B7BF-C941D50AA554"}
 */
function getCashReceiptAmount() {
	return sa_cash_receipt_amount
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DB11B4C7-9C5C-414A-A4B5-1F5F2B5D232F"}
 */
function onDataChange(oldValue, newValue, event) {
	if(scopes.avText.showYesNoQuestion('showPayMethod?') == scopes.avText.yes){
		sa_cash_receipt_source = null;
		elements.sa_cash_receipt_source_label.visible = false;
		elements.sa_cash_receipt_source.visible = false;
		elements.paymethod_id_label.visible = true;
		elements.paymethod_id.visible = true;
	}
	else{
		sa_cash_receipt_source = oldValue;
	}
	
	return false;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"30F636F7-FCC6-40CB-ADF9-A08EEAFFAA96"}
 */
function dc_edit(_event, _triggerForm) {
	if(cust_id) {
		controller.setTabSequence([
		        elements.sa_cash_receipt_num,
		        elements.sa_cash_receipt_date,
		        elements.sa_cash_receipt_trans_date,
		        elements.sa_cash_receipt_amount,
		        elements.sa_cash_receipt_source, 
		        elements.paymethod_id,
		        elements.btnApplyDeposit,
		        elements.bank_account_glacct_id, 
		        elements.btn_onHold,
		        elements.btn_ready_to_be_posted,
		        elements.apply_to_account_account,
		        elements.applyToAccountInfo, 
		        elements.tabs]);
	}
	
    if (utils.hasRecords(foundset.sa_cash_receipt_to_sa_order_revh_deposit$not_entered_thru_cr) || utils.hasRecords(foundset.sa_cash_receipt_to_sa_invoice_tender_trans$not_entered_thru_cr) || is_over_recon_variance || is_under_recon_variance) {

        if (foundset.sa_cash_receipt_auto_apply == 1) {
            scopes.avText.showWarning('autoCreatedCashReceiptApply');
            return -1;
        }
        else if (utils.hasRecords(foundset.sa_cash_receipt_to_sa_order_revh_deposit$not_entered_thru_cr)) {
            scopes.avText.showWarning('makeChangesOnDeposit');
        }
        else if (utils.hasRecords(foundset.sa_cash_receipt_to_sa_invoice_tender_trans$not_entered_thru_cr)) {
            scopes.avText.showWarning('makeChangesOnTender');
        }
        else if (is_over_recon_variance || is_under_recon_variance) {
            scopes.avText.showWarning('cantEditVarianceCashReceipt');
        }

        elements.sa_cash_receipt_amount.enabled = false;
        elements.paymethod_id.enabled = false;
        elements.cust_id.enabled = false;
        elements.sa_cash_receipt_date.enabled = false;
        elements.sa_cash_receipt_trans_date.enabled = true;
        elements.bank_account_glacct_id.enabled = false;
        elements.sa_cash_receipt_num.enabled = false;
        elements.btn_ready_to_be_posted.enabled = foundset.sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted;
        elements.curr_id.enabled = false;
        elements.invoice_total_amount.enabled = false;
        elements.invoice_total_amount_remaining_exchanged.enabled = false;
        elements.overpayment_amount_exchanged.enabled = false;
        elements.apply_to_account_account.enabled = false;
        elements.sa_cash_receipt_document_num.enabled = false;
        elements.status_id.enabled = false;
        elements.created_by_id.enabled = false;
        elements.sacr_reg_number.enabled = false;
        elements.overpayment_amount.enabled = false;
        elements.invoice_total_amount_remaining.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_amount_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_overpayment_amount.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.adjustment_amt_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.discount_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.payment_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_overpayment_amount_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.adjustment_amt_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.discount_amount_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.payment_amount_sum.enabled = false;
        forms.sa_cash_receipt_invoices_dtl_tbl.controller.enabled = false;
    }
	 
	// hp - moved this enabling from on recordselect. needs to be done on onedit. 
	else if(foundset.sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted) {
		elements.btn_onHold.enabled = false;
		elements.btn_ready_to_be_posted.enabled = false;
		elements.cust_id.enabled = false;
		elements.sa_cash_receipt_amount.enabled = false;
		elements.sa_cash_receipt_date.enabled = false;
		elements.sa_cash_receipt_trans_date.enabled = false;
		elements.bank_account_glacct_id.enabled = false;
		elements.btn_ready_to_be_posted.enabled = false;
		elements.paymethod_id.enabled = false;
		
		elements.sa_cash_receipt_amount.enabled = false;
        elements.paymethod_id.enabled = false;
        elements.cust_id.enabled = false;
        elements.sa_cash_receipt_date.enabled = false;
        elements.sa_cash_receipt_trans_date.enabled = true;
        elements.bank_account_glacct_id.enabled = false;
        elements.sa_cash_receipt_num.enabled = false;
        elements.btn_ready_to_be_posted.enabled = foundset.sa_cash_receipt_status != scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted;
        elements.curr_id.enabled = false;
        elements.invoice_total_amount.enabled = false;
        elements.invoice_total_amount_remaining_exchanged.enabled = false;
        elements.overpayment_amount_exchanged.enabled = false;
        elements.apply_to_account_account.enabled = false;
        elements.sa_cash_receipt_document_num.enabled = false;
        elements.status_id.enabled = false;
        elements.created_by_id.enabled = false;
        elements.sacr_reg_number.enabled = false;
        elements.overpayment_amount.enabled = false;
        elements.invoice_total_amount_remaining.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_amount_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_overpayment_amount.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.adjustment_amt_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.discount_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.payment_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_amount_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.total_overpayment_amount_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.adjustment_amt_sum_exchanged.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.discount_amount_sum.enabled = false;
        forms.sa_cash_receipt_dtl_tbl.elements.payment_amount_sum.enabled = false;
        forms.sa_cash_receipt_invoices_dtl_tbl.controller.enabled = false;
		
	} 
	else if (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted && !_bWorkatoUseCashReceiptsRegister && _bWorkatoIntegration) {
		elements.btn_onHold.enabled = true;
		elements.sa_cash_receipt_num.enabled = false;
		elements.btn_ready_to_be_posted.enabled = false;
		elements.cust_id.enabled = false;
		elements.sa_cash_receipt_amount.enabled = false;
		elements.sa_cash_receipt_date.enabled = false;
		elements.sa_cash_receipt_trans_date.enabled = false;
		elements.bank_account_glacct_id.enabled = false;
		elements.paymethod_id.enabled = false;
		elements.btnImport.enabled = false;
		forms.sa_cash_receipt_invoices_dtl_tbl.controller.enabled = false;
	}
	else {
		elements.btn_onHold.enabled = true;
		elements.sa_cash_receipt_num.enabled = true;
		elements.btn_ready_to_be_posted.enabled = true;
		elements.cust_id.enabled = true;
		elements.sa_cash_receipt_amount.enabled = true;
		elements.sa_cash_receipt_date.enabled = true;
		elements.sa_cash_receipt_trans_date.enabled = true;
		elements.bank_account_glacct_id.enabled = true;
		elements.btn_ready_to_be_posted.enabled = true;
		elements.sa_cash_receipt_num.enabled = true;
		elements.paymethod_id.enabled = true;
		elements.btnImport.enabled = true;
		forms.sa_cash_receipt_invoices_dtl_tbl.controller.enabled = true;
	}
	
	setOverpaymentFieldsEnabled();
	
	_super.dc_edit(_event, _triggerForm);
}

/**
 * @properties={typeid:24,uuid:"B9776E83-3AD9-474E-BF5F-C48C193CB173"}
 */
function setOverpaymentFieldsEnabled(){
	if(elements.sa_cash_receipt_amount.enabled && globals.avSecurity_checkForUserRight('Cash_Receipts', 'trackOverpayments', globals.avBase_employeeUserID)){
		elements.overpayment.enabled = true;
		forms.sa_cash_receipt_tab_order_details.elements.grid.getColumn(forms.sa_cash_receipt_tab_order_details.elements.grid.getColumnIndex("overpayment")).enabled = true;
	}
	else{
		elements.overpayment.enabled = false;
		forms.sa_cash_receipt_tab_order_details.elements.grid.getColumn(forms.sa_cash_receipt_tab_order_details.elements.grid.getColumnIndex("overpayment")).enabled = false;
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"6DD9F0C0-A295-47ED-8C0E-02AF1DDB2E2C"}
 */
function dc_new(_event, _triggerForm) {
	elements.btn_onHold.enabled = true;
	elements.btn_ready_to_be_posted.enabled = true;
	elements.cust_id.enabled = true;
	elements.sa_cash_receipt_amount.enabled = true;
	elements.sa_cash_receipt_date.enabled = true;
	elements.bank_account_glacct_id.enabled = true;
	elements.btn_ready_to_be_posted.enabled = true;
	elements.sa_cash_receipt_num.enabled = true;
	elements.paymethod_id.enabled = true;
	
    forms.sa_cash_receipt_invoices_dtl_tbl.controller.enabled = true;
    
	controller.setTabSequence([
	            elements.cust_id,
	            elements.sa_cash_receipt_num,
	            elements.sa_cash_receipt_date,
	            elements.sa_cash_receipt_amount, 
	            elements.sa_cash_receipt_source, 
	            elements.paymethod_id, 
	            elements.btnApplyDeposit,
	            elements.bank_account_glacct_id,
	            elements.btn_onHold, 
	            elements.btn_ready_to_be_posted,
	            elements.apply_to_account_account, 
	            elements.applyToAccountInfo, 
	            elements.tabs]);

	setOverpaymentFieldsEnabled();
	setDepositFields();
	
	return _super.dc_new(_event, _triggerForm);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"67220D28-6EEF-4766-8A85-30612B5368F1"}
 */
function onDataChange_payMethod(oldValue, newValue, event) {
    var sGLAccount;
    var oGLAccount;

    getPayMethodType();

    sa_cust_deposit_id = null;
    sa_cust_postage_id = null;
    sa_cash_receipt_transfer_cda = 0;
    
    elements.applyToAccountInfo.visible = true;

    if (utils.hasRecords(sa_cash_receipt_to_sa_payment_method)) {
        if (sa_cash_receipt_to_sa_payment_method.paymethod_type 
                && sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount) {
            elements.bank_account_glacct_id.enabled = false;
            scopes.globals.avUtilities_addBkgndColorStyle(elements.bank_account_glacct_id, "#FFFFFF");
            elements.applyToAccountInfo.visible = false;
            if (utils.hasRecords(sa_cash_receipt_to_sa_customer) && sa_cash_receipt_to_sa_customer.cust_deposit_glacct_id) {
                bank_account_glacct_id = sa_cash_receipt_to_sa_customer.cust_deposit_glacct_id;
            }
            else {
                // Customer Deposit Control Account
                sGLAccount = globals.avGL_getControlAccount('CUSTOMER DEPOSIT', curr_id);
                oGLAccount = scopes.avAccounting.getGLAccount(sGLAccount, null, null, null, null, null, null);

                if (oGLAccount.glacct_id) {
                    bank_account_glacct_id = oGLAccount.glacct_id;
                }
                else {
                    bank_account_glacct_id = null;
                }
            }
        }
        else if (sa_cash_receipt_to_sa_payment_method.paymethod_type 
                && sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount) {
            elements.bank_account_glacct_id.enabled = false;
            scopes.globals.avUtilities_addBkgndColorStyle(elements.bank_account_glacct_id, "#FFFFFF");
            elements.applyToAccountInfo.visible = false;
            if (utils.hasRecords(sa_cash_receipt_to_sa_customer) && sa_cash_receipt_to_sa_customer.cust_postage_deposit_glacct_id) {
                bank_account_glacct_id = sa_cash_receipt_to_sa_customer.cust_postage_deposit_glacct_id;
            }
            else {
                // Postage Escrow Control Account
                sGLAccount = globals.avGL_getControlAccount('CUSTOMER POSTAGE', curr_id);
                oGLAccount = scopes.avAccounting.getGLAccount(sGLAccount, null, null, null, null, null, null);

                if (oGLAccount.glacct_id) {
                    bank_account_glacct_id = oGLAccount.glacct_id;
                }
                else {
                    bank_account_glacct_id = null;
                }
            }

            //Users must select the postage deposit to apply against

        }
        else {
            elements.bank_account_glacct_id.enabled = true;
            scopes.globals.avUtilities_addBkgndColorStyle(elements.bank_account_glacct_id, "#F5F6BE");
            bank_account_glacct_id = sa_cash_receipt_to_sa_payment_method.glacct_id;
        }
    }
    else {
        elements.bank_account_glacct_id.enabled = true;
        scopes.globals.avUtilities_addBkgndColorStyle(elements.bank_account_glacct_id, "#F5F6BE");
    }

    var bValidateResult = validatePaymentMethodAndAmount();

    if (!bValidateResult) {
        paymethod_id = oldValue;
    }

    setDepositFields();
    setOrdersTabVisibility();
    forms.sa_cash_receipt_invoices_dtl_tbl.setColumnVisibility(foundset.getSelectedRecord());

    if (bValidateResult 
            && (sPayMethodType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount 
            || sPayMethodType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount)) {
        onAction_btnApplyDeposit(event);
    }

    return bValidateResult;
}

/** 
 *
 * @param {JSFoundSet} _foundset
 * @param {String} _program
 *
 * @return
 * @properties={typeid:24,uuid:"260C50E4-8733-4EC9-8544-6B7709CA6D67"}
 */
function dc_save_validate(_foundset, _program) {
	
	if(!bank_account_glacct_id){
		scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.bankAccount');
		return -1;
	}
	
    if (utils.hasRecords(sa_cash_receipt_to_sa_payment_method)) {
        if (sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount && !sa_cust_deposit_id 
                || (sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount && !sa_cust_postage_id)) {

            switch (sa_cash_receipt_to_sa_payment_method.paymethod_type) {
                case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount:
                    scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.customerDeposit');
                    break;

                case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount:
                    scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.PostageDeposit');
                    break;

            }

            return -1;
        }
    }

	if(deposit_account_type != null && (deposit_account_type == 1 || deposit_account_type == 2)) {
		
		if(!apply_to_account_glacct_id){
			scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.applyToAccount');
			return -1;
		}
	}
	
	// set payment num
	if(!forms.sa_cash_receipt_dtl.sa_cash_receipt_num){
		databaseManager.saveData();
		var fsCROPs = forms.sa_cash_receipt_dtl.sa_cash_receipt_to_sa_cash_receipt_order_payment$payment;
		var nNumCROPs = fsCROPs.getSize(); 
		
		if(nNumCROPs == 1){
			var rOrder = fsCROPs.sa_cash_receipt_order_payment_to_sa_order.getRecord(1);
			var nSeqNr = 1;
			
			if(utils.hasRecords(rOrder, 'sa_order_to_sa_order_revision_header$first_rev.sa_order_revision_header_to_sa_order_revh_deposit')){
				nSeqNr = scopes.avDB.getNextSequenceNr(rOrder.sa_order_to_sa_order_revision_header$first_rev.sa_order_revision_header_to_sa_order_revh_deposit);
			}
			
			forms.sa_cash_receipt_dtl.sa_cash_receipt_num = rOrder.ordh_document_num + '-' + scopes.avText.getLblMsg('DepositShortForm') + nSeqNr;
		}
	}
	
	if(!sa_cash_receipt_num){
		scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.paymentNumber');
		return -1;
	}
	
	if(!paymethod_id){
		scopes.avText.pleaseEnterAValueFor('i18n:avanti.lbl.paymethod_id');
		return -1;
	}

	return _super.dc_save_validate(_foundset, _program)
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"9AC26626-23CD-4C75-A491-BD8740F881BE"}
 */
function onDataChange_PaymentNumber(oldValue, newValue, event) {
	var sAccountingIntegrationType = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.AccountingIntegration);

    if (sAccountingIntegrationType == "GP") {
    	if(sa_cash_receipt_num && sa_cash_receipt_num.length > 17) {
    		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.cashReceipt.paymentNumberTooLongForGP'),i18n.getI18NMessage('avanti.dialog.okay'));
    		sa_cash_receipt_num = sa_cash_receipt_num.substr(0,17);
    	}
    }
	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"78E9842C-1C58-4463-B977-057125D12295"}
 */
function onAction_ApplyToAccount(event) {
	if (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted 
			|| (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted && !_bWorkatoUseCashReceiptsRegister && _bWorkatoIntegration)) {
		return
	}
	
    bUpdateAppliedAmount = false;
    if (globals.nav.mode != 'edit' && globals.nav.mode != 'add') {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'), i18n.getI18NMessage('avanti.dialog.okay'));
    }
    else if (sPayMethodType == 'PEA' && sa_cash_receipt_transfer_cda) {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.applyToAccount_cannotChange'), i18n.getI18NMessage('avanti.dialog.okay'));
    }
    else {
        forms.sa_cash_receipt_deposit.foundset.loadRecords(foundset);
        globals.DIALOGS.showFormInModalDialog(forms.sa_cash_receipt_deposit, -1, -1, 625, 165, i18n.getI18NMessage('avanti.lbl.applyToAccount'), false, false, 'applyToAccountDialog');
    }
}

/**
 * Validate the payment method and if there is enough in the balance to apply in the cash receipt.
 * 
 * @return
 * @properties={typeid:24,uuid:"EA9936FC-6840-4004-AC4C-A857202C263D"}
 */
function validatePaymentMethodAndAmount() {
	var sAmount = utils.numberFormat(sa_cash_receipt_amount, globals.avBase_currencySymbol + '#,##0.00');
	if(sPayMethodType == 'CDA') {
		if(utils.hasRecords(sa_cash_receipt_to_sa_customer) && utils.hasRecords(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_deposit) && 
			sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_deposit.customer_balance >= sa_cash_receipt_amount) {
		    if (sa_cash_receipt_amount) {
		        var sDepositResponse = globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.lbl.customerDepositAccount_sufficientFunds', new Array(sAmount)), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
	            if(sDepositResponse == i18n.getI18NMessage('avanti.dialog.no')) {
	                return false;
	            }
		    }
		} else {
			var sDepositBalance = utils.numberFormat(0.00, globals.avBase_currencySymbol + '#,##0.00');
			if(utils.hasRecords(sa_cash_receipt_to_sa_customer) && 
			utils.hasRecords(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_deposit)) {
				sDepositBalance = utils.numberFormat(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_deposit.customer_balance, globals.avBase_currencySymbol + '#,##0.00');
			}
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.lbl.customerDepositAccount_insufficientFunds', new Array(sDepositBalance)), i18n.getI18NMessage('avanti.dialog.okay'));
			return false;
		}
	} else if(sPayMethodType == 'PEA') {
		if(utils.hasRecords(sa_cash_receipt_to_sa_customer) && utils.hasRecords(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_postage) && 
			sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_postage.customer_balance >= sa_cash_receipt_amount) {
            if (sa_cash_receipt_amount) {
                var sPostageResponse = globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.lbl.postageEscrowAccount_sufficientFunds', new Array(sAmount)), i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));
                if (sPostageResponse == i18n.getI18NMessage('avanti.dialog.no')) {
                    return false;
                }
            }
	
		} else {
			var sPostageBalance = utils.numberFormat(0.00, globals.avBase_currencySymbol + '#,##0.00');
			if(utils.hasRecords(sa_cash_receipt_to_sa_customer) && 
			utils.hasRecords(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_postage)) {
				sPostageBalance = utils.numberFormat(sa_cash_receipt_to_sa_customer.sa_customer_to_sa_customer_postage.customer_balance, globals.avBase_currencySymbol + '#,##0.00');
			}
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.lbl.postageEscrowAccount_insufficientFunds', new Array(sPostageBalance)), i18n.getI18NMessage('avanti.dialog.okay'));
			return false;
		}
	} else {
		if(deposit_account_type == null || deposit_account_type == 0) {
			foundset.setPaymentAmount(forms.sa_cash_receipt_invoices_dtl_tbl.foundset);
		}
	}
	return true;
}

/**
 * 
 * Get the Payment Method's Payment Type
 * @properties={typeid:24,uuid:"C5E27B84-DB3B-4B50-83F8-6D4F21E2FDCD"}
 */
function getPayMethodType() {
	if(paymethod_id && utils.hasRecords(sa_cash_receipt_to_sa_payment_method) && sa_cash_receipt_to_sa_payment_method.paymethod_type) {
		sPayMethodType = sa_cash_receipt_to_sa_payment_method.paymethod_type;
	} else {
		sPayMethodType = '';
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"0906B237-A0A0-43D7-AE70-D24954423DEE"}
 */
function onAction_TransfterToCDA(event) {
	bUpdateAppliedAmount = true;
	if(sa_cash_receipt_transfer_cda) {
		deposit_account_type = 1;
		if(utils.hasRecords(foundset.sa_cash_receipt_to_sa_customer) && foundset.sa_cash_receipt_to_sa_customer.cust_deposit_glacct_id) {
			apply_to_account_glacct_id = foundset.sa_cash_receipt_to_sa_customer.cust_deposit_glacct_id;
			refreshUI();
		} else {
			// Customer Deposit Control Account
			var sGLAccount = globals.avGL_getControlAccount('CUSTOMER DEPOSIT', curr_id);	
			var 	oGLAccount = scopes.avAccounting.getGLAccount(sGLAccount,null,null,null,null,null,null);
			
			if(oGLAccount.glacct_id) {
				apply_to_account_glacct_id = oGLAccount.glacct_id;
				refreshUI();
			} else {
				deposit_account_type = 0;
				apply_to_account_glacct_id = null;
				globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.lbl.customerDepositAccount_notFound'), i18n.getI18NMessage('avanti.dialog.okay'));
			}
		}
	}
}

/**
 * @param {JSEvent} [_event] the event that triggered the action
 * @param {String}	[_triggerForm] (svy_nav_fr_buttonbar_browser/svy_nav_fr_buttonbar_viewer)
 * @return  none
 *
 * @properties={typeid:24,uuid:"77DEE41F-4026-4A6A-9EB7-EDBD351F7C28"}
 */
function dc_resetTableViewPersistance(_event, _triggerForm) {	
	forms.sa_cash_receipt_invoices_dtl_tbl.controller.recreateUI();
	forms.sa_cash_receipt_tab_order_details.controller.recreateUI();
	forms.sa_cash_receipt_note_tbl.controller.recreateUI();
	forms.sa_cash_receipt_dist_tbl.controller.recreateUI();
	_super.dc_resetTableViewPersistance(_event, _triggerForm);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"EDE32CF2-41CF-4B25-AB54-A116B8FF99D4"}
 */
function onAction_import(event) {
	if(sa_cash_receipt_status != 'P' && !scopes.avUtils.isNavModeReadOnly()){
		if(cust_id){
			databaseManager.saveData();
			forms.utils_dataImport_dtl.sCashReceiptID = sa_cash_receipt_id;
			forms.utils_dataImport_dtl.sCashReceiptCustID = cust_id;
			forms.utils_dataImport_dtl.selectFile("Cash Receipt Details");
		}
		else{
			scopes.avText.showWarning('selectACust');
		}
	}
}

/**
 * @properties={typeid:24,uuid:"41BD4901-2F00-4889-A8A0-124C1BD90703"}
 */
function refreshDetailsFromDB(){
	databaseManager.refreshRecordFromDatabase(forms.sa_cash_receipt_invoices_dtl_tbl.foundset, -1);
	databaseManager.refreshRecordFromDatabase(forms.sa_cash_receipt_invoices_dtl_tbl.foundset.sa_invoice_to_sa_cash_receipt_detail, -1);
	forms.sa_cash_receipt_invoices_dtl_tbl.loadRecords();
//	application.output('AMT: ' + forms.sa_cash_receipt_invoices_dtl_tbl.foundset.getRecord(1).sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount);
//	application.output('AMT: ' + forms.sa_cash_receipt_invoices_dtl_tbl.foundset.getRecord(3).sa_invoice_to_sa_cash_receipt_detail$billing_cash_receipt_id.invoice_payment_amount);
}

/**
 *
 * @param {JSFoundSet} _foundset
 *
 * @return
 * @properties={typeid:24,uuid:"A934EF11-631F-4B66-82A6-85B2DD6C0DF1"}
 */
function dc_save_pre(_foundset) {
	/** @type {JSRecord<db:/avanti/sa_cash_receipt>} **/
	var rRec = _foundset.getSelectedRecord();
	
    if (scopes.avAccounting.isCashReceiptsPeriodClosedForDate(rRec.sa_cash_receipt_trans_date, rRec.org_id)) {
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'),
            i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'),
            i18n.getI18NMessage('avanti.dialog.ok'));
        return -1;
    }
	
	return _super.dc_save_pre(_foundset);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"255D510F-D4A9-4296-B12A-4FA3C0819C8B"}
 */
function onDataChange_overpayment(oldValue, newValue, event) {
	if(globals.avSecurity_checkForUserRight('Cash_Receipts', 'trackOverpayments', globals.avBase_employeeUserID)){
		if(newValue){
			if(sa_cash_receipt_remaining_amt > 0){
				overpayment_amount = sa_cash_receipt_remaining_amt; 
				total_overpayment_amount += overpayment_amount;
			}
			else{
				scopes.avText.showWarning('noRemainingAmount');
				overpayment = oldValue;
			}
		}
		else{
			total_overpayment_amount -= overpayment_amount;
			overpayment_amount = 0;
		}
	}
	else{
		overpayment = oldValue;
	}
	
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CFC17FA9-F835-4569-8E78-90CDD197B52C"}
 */
function onAction_btnApplyDeposit(event) {
    if (scopes.avUtils.isNavModeReadOnly() || foundset.sa_cash_receipt_status == "P") {
        return;
    }
    
    if (cust_id && utils.hasRecords(sa_cash_receipt_to_sa_payment_method)) {
        switch (sa_cash_receipt_to_sa_payment_method.paymethod_type) {
            case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount:
                
                forms.sa_cash_receipt_dtl_postage_apply._selectedDeposit = sa_cust_postage_id;
                forms.sa_cash_receipt_dtl_postage_apply.showPostageDeposits(cust_id);
                sa_cust_postage_id = forms.sa_cash_receipt_dtl_postage_apply._selectedDeposit;
    
                if (!sa_cash_receipt_amount && utils.hasRecords(sa_cash_receipt_to_sa_customer_postage)) {
                    if (utils.hasRecords(sa_cash_receipt_to_sa_customer_postage.sa_customer_postage_to_gl_account)) {
                        bank_account_glacct_id = sa_cash_receipt_to_sa_customer_postage.postage_glacct_id;
                    }
                    
                    if (scopes.avUtils.trackCurrencyExchange(curr_id)) {
                    	sa_cash_receipt_amount_exchanged = sa_cash_receipt_to_sa_customer_postage.originating_balance_at_time;
                    	sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_postage.balance_at_time;
                    	paymentAmountExchangeChanged(null, sa_cash_receipt_amount_exchanged);
                    }
                    else {
                    	sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_postage.balance_at_time;
                    	paymentAmountChanged(null);
                    }
                }
                else if (utils.hasRecords(sa_cash_receipt_to_sa_customer_postage)) {
                	if (scopes.avUtils.trackCurrencyExchange(curr_id)
                			&& sa_cash_receipt_amount_exchanged > sa_cash_receipt_to_sa_customer_postage.originating_balance_at_time) {
        				sa_cash_receipt_amount_exchanged = sa_cash_receipt_to_sa_customer_postage.originating_balance_at_time;
        				sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_postage.balance_at_time;
        				paymentAmountExchangeChanged(null, sa_cash_receipt_amount_exchanged);
        			}
        			else if (sa_cash_receipt_amount > sa_cash_receipt_to_sa_customer_postage.balance_at_time) {
        				sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_postage.balance_at_time;
        				paymentAmountChanged(null);
        			}
                }
                
//                 elements.sa_cust_postage_id.requestFocus();
                
                break;
                
            case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount:
                
                forms.sa_cash_receipt_dtl_deposit_apply._selectedDeposit = sa_cust_deposit_id;
                forms.sa_cash_receipt_dtl_deposit_apply.showDeposits(cust_id);
                sa_cust_deposit_id = forms.sa_cash_receipt_dtl_deposit_apply._selectedDeposit;
    
                if (!sa_cash_receipt_amount && utils.hasRecords(sa_cash_receipt_to_sa_customer_deposit)) {
                    if (utils.hasRecords(sa_cash_receipt_to_sa_customer_deposit.sa_customer_deposit_to_gl_account)) {
                        bank_account_glacct_id = sa_cash_receipt_to_sa_customer_deposit.deposit_glacct_id;
                    }
                    
                    if (scopes.avUtils.trackCurrencyExchange(curr_id)) {
                    	sa_cash_receipt_amount_exchanged = sa_cash_receipt_to_sa_customer_deposit.originating_balance_at_time;
                    	sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_deposit.balance_at_time;
                    	paymentAmountExchangeChanged(null, sa_cash_receipt_amount_exchanged);
                    }
                    else {
                    	sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_deposit.balance_at_time;
                    	paymentAmountChanged(null);
                    }
                }
                else if (utils.hasRecords(sa_cash_receipt_to_sa_customer_deposit)){
                	if (scopes.avUtils.trackCurrencyExchange(curr_id)
                			&& sa_cash_receipt_amount_exchanged > sa_cash_receipt_to_sa_customer_deposit.originating_balance_at_time + sa_cash_receipt_payment_amt_exchanged) {
        				sa_cash_receipt_amount_exchanged = sa_cash_receipt_to_sa_customer_deposit.originating_balance_at_time;
        				sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_deposit.balance_at_time;
        				paymentAmountExchangeChanged(null, sa_cash_receipt_amount_exchanged);
        			}
        			else if (sa_cash_receipt_amount > sa_cash_receipt_to_sa_customer_deposit.balance_at_time + sa_cash_receipt_payment_amt) {
        				sa_cash_receipt_amount = sa_cash_receipt_to_sa_customer_deposit.balance_at_time;
        				paymentAmountChanged(null);
        			}
                }
                
//                 elements.sa_cust_deposit_id.requestFocus();
                
                break;
        }
    }
}

/**
 * Set Deposit Fields
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C02F3E49-8EB5-4301-9572-37808FC72A89"}
 */
function setDepositFields() {
    elements.sa_cust_postage_id.visible = false;
    elements.sa_cust_deposit_id.visible = false;
    elements.btnApplyDeposit.visible = false;
    elements.applyToAccountInfo.visible = true;
    
    if (utils.hasRecords(sa_cash_receipt_to_sa_payment_method)) {
        if (sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount) {
            elements.sa_cust_postage_id.visible = true;
            elements.btnApplyDeposit.visible = true;
            elements.applyToAccountInfo.visible = false;
        }
        else if (sa_cash_receipt_to_sa_payment_method.paymethod_type == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount) {
            elements.sa_cust_deposit_id.visible = true;
            elements.btnApplyDeposit.visible = true;
            elements.applyToAccountInfo.visible = false;
        }
    }
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"6A9BABFC-797B-4236-A1CC-F0A397FBC94B"}
*/
function dc_duplicate(_event, _triggerForm) {
   return -1;
}

/**
 * @properties={typeid:24,uuid:"CC407E4D-D322-4543-887A-5E6719D95A0A"}
 */
function gotoEdit() {
   _bHoldButtonPressed = false;
}

/**
 * set Orders Tab Visibility - Need to hide if payment method is customer deposit or postage escrow
 * as you can no longer apply a deposit to an order from cash receipts.
 *
 * @private
 *
 * @properties={typeid:24,uuid:"3BD08D77-E6C8-4055-9E2B-15EBB6046574"}
 */
function setOrdersTabVisibility() {

    var iMaxTab = scopes.globals.avUtilities_tabGetMaxTabIndex(controller.getName(), "tabs");

    for (var i = 1; i <= iMaxTab; i++) {
        if (scopes.globals.avUtilities_tabGetName(controller.getName(), "tabs", i) == "tabOrders") {
            scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "tabs", i);
            break;
        }
    }

    if (utils.hasRecords(sa_cash_receipt_to_sa_payment_method)) {
        var rPaymentMethod = sa_cash_receipt_to_sa_payment_method.getRecord(1);

        if (utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_order_payment) 
                || (rPaymentMethod.paymethod_type != scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount 
                && rPaymentMethod.paymethod_type != scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount)) {
            scopes.globals.avUtilities_tabAdd(controller.getName(), "tabs", forms.sa_cash_receipt_tab_order_details, i18n.getI18NMessage("avanti.tab.OrderDetails"), null, null, "tabOrders", 4, null);
        }
    }
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"68160D32-1814-4581-9A22-E7F78D4789C4"}
 */
function onDataChange_transactionDate(oldValue, newValue, event) {

    if (scopes.avAccounting.isCashReceiptsPeriodClosedForDate(newValue, org_id)) {
        globals.DIALOGS.showWarningDialog(
            i18n.getI18NMessage('servoy.general.warning'), 
            i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'), 
            i18n.getI18NMessage('avanti.dialog.ok'));
        sa_cash_receipt_trans_date = oldValue;
        return false;
    }

    return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"********-85C0-4319-BFC9-1736B0EDEC85"}
 */
function onDataChangeAmountExchanged(oldValue, newValue, event) {
	return paymentAmountExchangeChanged(oldValue, newValue);
}

/**
 * 
 * @param {Number} oldValue
 * @param {Number} newValue
 *
 * @return
 * @properties={typeid:24,uuid:"********-1F27-4002-AD9B-D546B58F9550"}
 */
function paymentAmountExchangeChanged(oldValue, newValue) {
	sa_cash_receipt_amount = globals.convertCustomerCurrencyToOrganizationCurrency(newValue, foundset.curr_id, foundset.sa_cash_receipt_date);
	var oldValueHome = globals.convertCustomerCurrencyToOrganizationCurrency(oldValue, foundset.curr_id, foundset.sa_cash_receipt_date);
	
	getPayMethodType();
	
	if((sPayMethodType == 'PEA' || sPayMethodType == 'CDA') && sa_cash_receipt_amount < 0) {
		sa_cash_receipt_amount_exchanged = oldValue;
		sa_cash_receipt_amount = oldValueHome;
		return false;
	}
	
	if(deposit_account_type != null && deposit_account_type != 0) {
		sa_cash_receipt_payment_amt_exchanged = sa_cash_receipt_amount_exchanged;
		sa_cash_receipt_payment_amt = sa_cash_receipt_amount;
	}
	
	var bValidationResult = validatePaymentMethodAndAmount();
	
	if(!bValidationResult) {
		sa_cash_receipt_amount_exchanged = oldValue;
		sa_cash_receipt_amount = oldValueHome;
		foundset.setPaymentAmount(forms.sa_cash_receipt_invoices_dtl_tbl.foundset);
		return false;
	}
	
	updateStatus();
	
	return true;
}

/**
 * Set tab visibility
 *
 * @properties={typeid:24,uuid:"47728B07-B779-4B8C-BE16-C1ED2C219BA6"}
 */
function setTabVisibility() {
    if (!_bWorkatoUseCashReceiptsRegister 
    		&& _bWorkatoIntegration
    		&& (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted || sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted)) {
        globals.avUtilities_tabAdd("sa_cash_receipt_dtl", "tabs", "sa_cash_receipt_dtl_integration", "avanti.lbl.IntegrationDetails", "_to_sa_cash_receipt", null);
    }
    else {
        globals.avUtilities_tabRemove("sa_cash_receipt_dtl", "tabs", "avanti.lbl.IntegrationDetails");
    }
}

/**
 * Generate the cash receipt distribution
 *
 * @properties={typeid:24,uuid:"EC1894D9-11E1-4712-86E6-53C37D819E70"}
 */
function generateDistribution() {
	if (!_bWorkatoUseInvoiceRegister && sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted) {
		databaseManager.saveData();
		clearDistribution();
		scopes.globals.setCashReceiptDistribution(null, sa_cash_receipt_id);
		refreshUI();
	}
}

/**
 * Clear distribution
 *
 * @properties={typeid:24,uuid:"061094D4-FA17-48E2-916B-D56F61DE4B56"}
 */
function clearDistribution() {
	sa_cash_receipt_to_sa_cash_receipt_dist.deleteAllRecords();
}

/**
 * @param {JSFoundSet} _foundset
 * @override
 *
 * @return
 * @properties={typeid:24,uuid:"CAAA9392-5261-4D05-B883-713B5585F086"}
 */
function dc_new_post(_foundset) {
    if (_bIsNetSuiteIntegration) {
        sa_cash_receipt_funds_deposited = 1;
    }
    return _super.dc_new_post(_foundset);
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"5339350C-4B3D-464B-8E94-D24B13D043AB"}
 */
function onDataChange_sa_cash_receipt_date(oldValue, newValue, event) {
	var bResult = true;
	sa_cash_receipt_trans_date = newValue;
	
	//Check if the new date is within a closed period.
	if(scopes.avAccounting.isCashReceiptsPeriodClosedForDate(newValue,org_id)){
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'),i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'),i18n.getI18NMessage('avanti.dialog.ok'));
		sa_cash_receipt_date = oldValue;
		bResult = false;
	}

	return bResult;
}

/**
 * Fired when the user clicks on a tab. When false is returned, the tab switch is prevented.
 *
 * @param {JSEvent} event The event that triggered the action
 * @param {Number} clickedTabIndex The index of the tab that was clicked
 * @param {String} dataTarget The value of the closest data-target attribute when found
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F053FA59-341C-4507-B14B-54F048CECD72"}
 */
function onTabClicked(event, clickedTabIndex, dataTarget) {
	setToolBarOptions();
	return true;
}
