customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_task_standard",
extendsID:"DFD53413-00EA-4443-8EF6-2A4D18AEFBCD",
items:[
{
cssPosition:"222,-1,-1,155,270,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"222",
width:"270"
},
dataProviderID:"taskstd_inline",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"4DD8F19B-EC3C-4032-9C4D-E2ECE31A6F77",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_inline",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"07597C53-DE4D-4CA6-A7EF-70E1309A56AB"
},
{
cssPosition:"112,-1,-1,315,195,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"315",
right:"-1",
top:"112",
width:"195"
},
enabled:true,
labelFor:"fldPockets",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.numberFeedersPockets",
visible:true
},
name:"lblPockets",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0889DD67-D0F9-4090-A141-0D02F038698B"
},
{
cssPosition:"10,-1,-1,155,397,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"10",
width:"397"
},
dataProviderID:"systaskfunc_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"0CFDFA91-30A8-45C3-8721-498AC8B09F74",
visible:true
},
name:"fldTaskFunctionalArea",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"0F7B1010-FA8D-4BEE-98BB-820E30D68764"
},
{
cssPosition:"299,-1,-1,655,116,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"655",
right:"-1",
top:"299",
width:"116"
},
dataProviderID:"taskstd_cost_per_piece",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"3AE88BEF-70DA-4616-8E87-6C58DA3FE18B",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_56876D1C",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"1680F7F4-E749-4F58-B6B8-5DA27F6937CD"
},
{
cssPosition:"273,-1,-1,10,140,21",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"21",
left:"10",
right:"-1",
top:"273",
width:"140"
},
enabled:true,
formIndex:14,
labelFor:"fldHelpers",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.#helpers",
visible:true
},
name:"lblNrHelpers",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"17E5E827-A899-4B5B-9104-E3CBD703C959"
},
{
cssPosition:"299,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"299",
width:"75"
},
dataProviderID:"taskstd_mrkup",
editable:true,
enabled:true,
format:"0.00%",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.percent_tooltip",
visible:true
},
name:"fldMrkUp",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"18B6A79F-8E7C-4156-8BC9-0BD05D31398F"
},
{
cssPosition:"137,-1,-1,460,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"137",
width:"60"
},
dataProviderID:"taskstd_lifts_per_hr",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldLift2",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"1AC85DAF-CE3D-45FD-A62E-D82FFF3FE2F8"
},
{
cssPosition:"272,-1,-1,155,102,22",
formIndex:29,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"272",
width:"102"
},
dataProviderID:"taskstd_helpers",
editable:true,
enabled:true,
formIndex:29,
onDataChangeMethodID:null,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"fldNrHelpers",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"23C25036-90D0-42CB-A3E0-51F7C2462925"
},
{
cssPosition:"112,-1,-1,685,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"685",
right:"-1",
top:"112",
width:"161"
},
dataProviderID:"taskstd_flg_cover_deck",
enabled:true,
onDataChangeMethodID:"32DFF977-0BC5-4C77-A7F4-E9BEEACEAE04",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.coverDeck",
visible:true
},
name:"fldAcc1",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"24317B68-06DC-4D2E-A52B-E2DCCC4C0A39"
},
{
cssPosition:"168,-1,-1,685,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"685",
right:"-1",
top:"168",
width:"161"
},
dataProviderID:"taskstd_flg_card_feeder",
enabled:true,
onDataChangeMethodID:"92ED8BB4-5298-498D-B726-E6230A1BAC22",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.cardFeeder",
visible:true
},
name:"fldAcc3",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"374071DB-54AA-42E8-AD63-DEF09DC5CB01"
},
{
cssPosition:"247,-1,-1,341,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"341",
right:"-1",
top:"247",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.min",
visible:true
},
name:"lblSetupMins",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3A4AF5C3-AE47-44CC-AC8C-6CCDBEED2532"
},
{
cssPosition:"10,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.functionalArea",
visible:true
},
name:"component_E57AB40F",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5C3BB37A-1644-41BE-A251-2519772970D9"
},
{
cssPosition:"299,-1,-1,388,116,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"388",
right:"-1",
top:"299",
width:"116"
},
dataProviderID:"taskstd_custpricing",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"8A1143B3-056C-47EF-9DAC-CD94FEC115D4",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_72ACB87D",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"644477D0-3852-46C8-BE3D-EE287ADF25CE"
},
{
cssPosition:"247,-1,-1,276,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"276",
right:"-1",
top:"247",
width:"60"
},
dataProviderID:"taskstd_setup_time",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldTime",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"70903ABE-FE9A-493D-8766-3DA741650C0F"
},
{
cssPosition:"112,-1,-1,315,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"315",
right:"-1",
top:"112",
width:"140"
},
enabled:true,
labelFor:"fldLift1",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.inches/Lift",
visible:true
},
name:"lblLift1",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"720A8E38-DC2A-4CCD-AAB1-96A5B309E934"
},
{
cssPosition:"112,-1,-1,515,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"515",
right:"-1",
top:"112",
width:"60"
},
dataProviderID:"taskstd_nr_pockets",
editable:true,
enabled:true,
onDataChangeMethodID:"6B9928C1-F4DF-478F-9455-2AE4046073F9",
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldPockets",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"72A49981-B16F-4639-97FC-D71F929B5D11"
},
{
cssPosition:"326,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"326",
width:"75"
},
dataProviderID:"taskstd_min_charge",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldMinCharge",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"788FC2F6-0B21-4B1D-B4A6-7AAE5C60BBA8"
},
{
cssPosition:"326,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"326",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.minimumChargeForTask",
visible:true
},
name:"lblMinCharge",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"905DE770-2E8D-4567-AB81-F46EF1951E68"
},
{
cssPosition:"222,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"222",
width:"140"
},
enabled:true,
labelFor:"taskstd_inline",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.hasInlineTasks",
visible:true
},
name:"component_CE2A0C1F",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A379828E-05C1-4F95-BADA-C7C08998E751"
},
{
cssPosition:"299,-1,-1,243,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"243",
right:"-1",
top:"299",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customerPricing",
visible:true
},
name:"component_B1D41FDF",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A77666A0-67FB-4574-A3F6-950E55E041B5"
},
{
cssPosition:"137,-1,-1,315,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"315",
right:"-1",
top:"137",
width:"140"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.liftsHour",
visible:true
},
name:"lblLift2",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B0D381DA-**************-75B2B1440116"
},
{
cssPosition:"197,-1,-1,155,230,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"197",
width:"230"
},
dataProviderID:"taskstd_materials_needed",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"740EE70A-81EA-480E-8836-63B926B6C14D",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_materials_needed",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"********-AA54-45D0-8E3A-401FB88AE0C9"
},
{
cssPosition:"87,-1,-1,10,490,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"87",
width:"490"
},
enabled:true,
labelFor:"taskstd_machine_var_based_on",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.machineVariableBasedOn",
visible:true
},
name:"component_940C74F4",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B82A16E3-59FC-4F66-94FD-B26EDD1206F9"
},
{
cssPosition:"351,-1,-1,155,345,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"351",
width:"345"
},
dataProviderID:"jdftype_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EA854D03-A99A-46D5-B366-74F2E62A1987",
visible:true
},
name:"component_35F50CEE",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BAF34456-B4A3-42D5-9E57-8213816E87C0"
},
{
cssPosition:"112,-1,-1,155,161,85",
json:{
cssPosition:{
bottom:"-1",
height:"85",
left:"155",
right:"-1",
top:"112",
width:"161"
},
dataProviderID:"taskstd_machine_var_based_on",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"55474315-A4BB-450F-B92E-4909D4CFAB37",
styleClass:"choicegroup_bts vertical",
tabSeq:0,
valuelistID:"E7EB02C3-4A7A-424D-B4CA-E598E692D92E",
visible:true
},
name:"taskstd_machine_var_based_on",
styleClass:"choicegroup_bts vertical",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"BE78780A-672A-43FB-9DD9-B6D4F2BAF34F"
},
{
cssPosition:"351,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"351",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.jdfType",
visible:true
},
name:"component_A43FC849",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C75DB339-AC87-409E-B479-2C6F485B4630"
},
{
cssPosition:"247,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"247",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.setupTimeStandard",
visible:true
},
name:"component_5D09EADC",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"********-6FF7-4AD5-B6A5-051887D9876F"
},
{
cssPosition:"112,-1,-1,460,60,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"460",
right:"-1",
top:"112",
width:"60"
},
dataProviderID:"taskstd_inches_per_lift",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"text-center textbox_bts",
tabSeq:0,
visible:true
},
name:"fldLift1",
styleClass:"text-center textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"CE45855A-4532-4273-B7ED-46881F0EA52C"
},
{
cssPosition:"247,-1,-1,155,116,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"247",
width:"116"
},
dataProviderID:"taskstd_variable_data",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"D5FB202D-477D-49C6-A740-E3FF5E11939A",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"DC27E180-9E1F-4471-BC08-9BC82C747CD2",
visible:true
},
name:"component_FBAF09A8",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"CF6B2360-76A8-4974-856D-4456C2329647"
},
{
cssPosition:"299,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"299",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.markup%",
visible:true
},
name:"lblMrkUp",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D0E1A5AC-68B5-49C8-BC5E-F8B2AC508673"
},
{
cssPosition:"196,-1,-1,685,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"685",
right:"-1",
top:"196",
width:"161"
},
dataProviderID:"taskstd_flg_face_trim",
enabled:true,
onDataChangeMethodID:"960691E8-0A78-4CD9-A6A2-E5E0FA508ADB",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.faceTrim",
visible:true
},
name:"fldAcc4",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"D0F135E7-89CA-4AD8-86E4-EBE0448533B9"
},
{
cssPosition:"197,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"197",
width:"140"
},
enabled:true,
labelFor:"taskstd_materials_needed",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.materialsNeeded",
visible:true
},
name:"component_39385DF7",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D368C068-9E6D-494E-A815-7532B77B162D"
},
{
cssPosition:"37,-1,-1,10,490,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"37",
width:"490"
},
enabled:true,
labelFor:"taskstd_calc_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.calculationQuantityVariable",
visible:true
},
name:"component_74DF84E3",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DE8F1F42-13AF-4E2C-92EF-8C12945A7359"
},
{
cssPosition:"62,-1,-1,155,397,22",
json:{
alignment:"horizontal",
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"62",
width:"397"
},
dataProviderID:"taskstd_calc_type",
enabled:true,
inputType:"radio",
styleClass:"choicegroup_bts bts-radiogroup",
tabSeq:0,
valuelistID:"E0BC5E00-F50B-4887-86B4-2A8BAFB85DEF",
visible:true
},
name:"taskstd_calc_type",
styleClass:"choicegroup_bts bts-radiogroup",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E2FAAD4F-A20A-4FC6-BBBA-260D37143BC8"
},
{
cssPosition:"299,-1,-1,510,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"510",
right:"-1",
top:"299",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.costPerPiecePricing",
visible:true
},
name:"component_E4946A20",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E3B18F78-FB79-402A-8855-7DBD0BA591C4"
},
{
cssPosition:"140,-1,-1,685,161,25",
json:{
cssPosition:{
bottom:"-1",
height:"25",
left:"685",
right:"-1",
top:"140",
width:"161"
},
dataProviderID:"taskstd_flg_hand_feeder",
enabled:true,
onDataChangeMethodID:"5A117DBC-0B45-478D-A999-9C4F625A91B6",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.handFeeder",
visible:true
},
name:"fldAcc2",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"E839A970-65CF-4C11-9CF1-208E32AE31E1"
},
{
height:386,
partType:5,
typeid:19,
uuid:"F8890EBB-FEF9-4FB5-B585-39F11D4D5E72"
},
{
cssPosition:"112,-1,-1,590,90,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"590",
right:"-1",
top:"112",
width:"90"
},
enabled:true,
labelFor:"fldLift2",
styleClass:"label_bts text-right",
tabSeq:-1,
text:"i18n:avanti.lbl.accessories",
visible:true
},
name:"lblAccessories",
styleClass:"label_bts text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FE7CEEB9-DF5C-4DF0-802C-08C953C2D55E"
}
],
name:"sa_task_standards_finishing",
onShowMethodID:"50F7B4FA-AD24-43C7-98FA-A4713087A282",
paperPrintScale:100,
scrollbars:33,
size:"945,360",
styleName:null,
typeid:3,
uuid:"05231E62-3D37-43D8-9F52-9D888993AB00"