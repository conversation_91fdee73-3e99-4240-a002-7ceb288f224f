borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"19,-1,-1,416,105,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"416",
right:"-1",
top:"19",
width:"105"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2E0E38A6-98AD-4193-8484-23CE221038D0"
},
{
cssPosition:"167,-1,-1,156,250,22",
formIndex:4,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"156",
right:"-1",
top:"167",
width:"250"
},
dataProviderID:"fromJobNr",
enabled:true,
formIndex:4,
onDataChangeMethodID:"2A0A3AF9-78B5-4070-8816-A4DA4C7F0991",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_fromJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3BCC9335-E6CA-4ED9-AE82-D591B42E67F3"
},
{
cssPosition:"203,-1,-1,16,140,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"16",
right:"-1",
top:"203",
width:"140"
},
enabled:true,
formIndex:3,
labelFor:"_toJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobNumber",
visible:true
},
name:"_toJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"60B09B9B-9352-4B42-AF91-E466397A5DC8"
},
{
cssPosition:"167,-1,-1,16,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"16",
right:"-1",
top:"167",
width:"140"
},
enabled:true,
formIndex:2,
labelFor:"_fromJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6B3C2BDD-C5D9-49C4-889A-76A64C4C9EE5"
},
{
cssPosition:"19,-1,-1,156,250,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"156",
right:"-1",
top:"19",
width:"250"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"E6337316-FAC5-4807-B92C-36B16A12BB06",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"6BE7B680-E8F8-45E9-958B-08963961899F"
},
{
cssPosition:"203,-1,-1,156,250,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"156",
right:"-1",
top:"203",
width:"250"
},
dataProviderID:"toJobNr",
enabled:true,
formIndex:1,
onDataChangeMethodID:"A52226EF-2BE3-468F-A1E6-2E3A9B760133",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_toJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"70B4EF26-3DA3-4A85-B250-E40C4B33FA4E"
},
{
cssPosition:"19,-1,-1,15,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"19",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"78BEA441-E940-4644-9654-071BF633D3D4"
},
{
height:200,
partType:5,
typeid:19,
uuid:"A6EA2F82-51C4-4E9C-91DB-A848246005BF"
},
{
cssPosition:"230,-1,-1,690,140,20",
formIndex:32,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"690",
right:"-1",
top:"230",
width:"140"
},
dataProviderID:"_includeAllPlants",
enabled:true,
formIndex:32,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeAllPlants",
visible:false
},
name:"_includeAllPlants",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"B35F0BC0-80C3-492E-94EA-FCAB7895D14B",
visible:false
},
{
cssPosition:"16,-1,-1,519,250,112",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"519",
right:"-1",
top:"16",
width:"250"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E6B8559E-6DBD-4F59-AEF2-35D28EC205FA"
}
],
name:"rpt_report_dlg_partially_shipped_jobs",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"822DA3FE-9820-48EE-8E62-31028E81DCFD"