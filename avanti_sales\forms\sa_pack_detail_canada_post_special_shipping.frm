customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_pack_detail_carrier",
extendsID:"A1EFC35D-7C05-4C29-A9CC-EA88B6E6138D",
items:[
{
cssPosition:"5,-1,83,5,255,482",
json:{
cssPosition:{
bottom:"83",
height:"482",
left:"5",
right:"-1",
top:"5",
width:"255"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_4513E072",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0060F1CD-F85B-43FB-870E-93E7F898AB7A"
},
{
cssPosition:"-1,103,51,-1,80,22",
json:{
cssPosition:{
bottom:"51",
height:"22",
left:"-1",
right:"103",
top:"-1",
width:"80"
},
enabled:true,
onActionMethodID:"0F7FFCB7-7143-43ED-9601-0F24DA1D5847",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_EA08A1DF",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"6B97464E-8B8E-4D6C-9A2E-BD4A2395E3FD"
},
{
cssPosition:"-1,18,51,-1,80,22",
json:{
cssPosition:{
bottom:"51",
height:"22",
left:"-1",
right:"18",
top:"-1",
width:"80"
},
enabled:true,
onActionMethodID:"967B4866-778E-4464-95D2-221A2DBAB6DE",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_89F8270A",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"8203A911-AFA7-4586-9983-C958A3694E13"
},
{
cssPosition:"10,-1,94,10,245,466",
json:{
cssPosition:{
bottom:"94",
height:"466",
left:"10",
right:"-1",
top:"10",
width:"245"
},
dataProviderID:"special_options_selected",
enabled:true,
onActionMethodID:"760FA738-E215-4C74-8F7F-7BA643AB9CE8",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AB826D20-03E9-4259-967D-A7F66A026C1A",
visible:true
},
name:"component_F08E7047",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"83F384B4-DB59-4FAF-979F-D61A7809C1EB"
},
{
height:570,
partType:5,
typeid:19,
uuid:"A2BF8311-C22C-4B14-8509-BD9838B5617B"
},
{
cssPosition:"5,0,83,265,623,482",
json:{
cssPosition:{
bottom:"83",
height:"482",
left:"265",
right:"0",
top:"5",
width:"623"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_76A6CD73",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AB41AB67-AE17-4306-86E5-83607ACC7216"
},
{
cssPosition:"10,18,94,270,600,466",
formIndex:1,
json:{
containedForm:"7734C23B-EF22-4BF2-A159-063C1C9E142D",
cssPosition:{
bottom:"94",
height:"466",
left:"270",
right:"18",
top:"10",
width:"600"
},
formIndex:1,
relationName:"sa_pack_detail_carrier_to_sa_pack_canada_post_options",
visible:true
},
name:"canadapost_option_details",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"BDE4E061-3CF5-4632-AF36-236F316E3AE3"
}
],
name:"sa_pack_detail_canada_post_special_shipping",
onShowMethodID:"D4EC4844-2CE6-460E-963C-7868D6B9E820",
scrollbars:33,
size:"888,570",
styleName:null,
typeid:3,
uuid:"C3D47F95-6D1C-4754-8934-34CEB02089C3",
view:0