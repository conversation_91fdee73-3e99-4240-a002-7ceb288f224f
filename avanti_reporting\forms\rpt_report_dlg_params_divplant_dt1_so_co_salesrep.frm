borderType:"EmptyBorder,0,0,0,0",
customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"181,-1,-1,150,225,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"181",
width:"225"
},
dataProviderID:"_fromSalesOrder",
enabled:true,
onDataChangeMethodID:"7F7345A2-2D14-446A-9B6E-4C92E88E156A",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_fromSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"17632E16-3E88-4821-83DF-C334B409ED2D"
},
{
cssPosition:"22,-1,-1,381,140,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"22",
width:"140"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1B0545DB-9E4D-4993-8919-494BDE4E8EA3"
},
{
cssPosition:"123,-1,-1,150,225,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"123",
width:"225"
},
dataProviderID:"_dateFrom",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"1D0C449D-6BC0-4306-B6A0-8AAD0C5D8559"
},
{
cssPosition:"181,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"181",
width:"140"
},
enabled:true,
labelFor:"_toSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toSalesOrder",
visible:true
},
name:"_toSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"267484ED-5F89-4719-8A03-431C776EC395"
},
{
cssPosition:"121,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"121",
width:"140"
},
enabled:true,
labelFor:"dTo",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.ToOrderCloseDate",
visible:true
},
name:"_toJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"268271A6-5E0D-45E9-B4E6-C7A08863BB6C"
},
{
cssPosition:"211,309,-1,-1,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"309",
top:"211",
width:"140"
},
enabled:true,
labelFor:"_toSalesRep",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toSalesRep",
visible:true
},
name:"_toSalesRep_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2905BB7B-4EA9-4E30-A4C3-338C1BD8AFBF"
},
{
cssPosition:"211,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"211",
width:"140"
},
enabled:true,
labelFor:"_fromSalesRep",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesRep",
visible:true
},
name:"_fromSalesRep_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"325AB844-44BF-436A-898A-C9A12FA1FAF5"
},
{
cssPosition:"22,-1,-1,9,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"22",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"32C8E2D0-5BAB-47D9-A9C2-41077E5F656B"
},
{
cssPosition:"153,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"153",
width:"140"
},
enabled:true,
labelFor:"_fromCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromCustomer",
visible:true
},
name:"_customer_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"34294486-EFB5-43BB-BDE8-B086AB24156D"
},
{
cssPosition:"123,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"123",
width:"140"
},
enabled:true,
labelFor:"dFrom",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FromOrderCloseDate",
visible:true
},
name:"_fromJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"44CFE25B-5178-43B9-B6AE-513499717DA1"
},
{
cssPosition:"151,-1,-1,381,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"381",
right:"-1",
top:"151",
width:"140"
},
enabled:true,
labelFor:"_toCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toCustomer",
visible:true
},
name:"_toCustomerCode_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4CCD073B-88C5-4AC1-977A-779DB99D63A7"
},
{
cssPosition:"152,-1,-1,150,225,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"152",
width:"225"
},
dataProviderID:"_fromCustomer",
editable:true,
enabled:true,
onDataChangeMethodID:"2C7885AE-9E88-407F-B9F9-0D5331F5F9F2",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_fromCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"51A57F5B-2CD2-4120-BD7A-D93878B6A288"
},
{
cssPosition:"151,-1,-1,522,225,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"151",
width:"225"
},
dataProviderID:"_toCustomer",
enabled:true,
onDataChangeMethodID:"94F38286-1400-49A8-AF3E-0544151CA5EB",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"F88F7915-B5A7-41BD-B12A-793C98AF75F7",
visible:true
},
name:"_toCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"57CF966C-DD4F-4427-A191-A51E25BE9981"
},
{
cssPosition:"22,-1,-1,150,225,94",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"94",
left:"150",
right:"-1",
top:"22",
width:"225"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"EE9BA838-A922-4475-B7B9-BDF91810FD40",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"7A06E5A8-72DE-435D-805C-C68E382E786E"
},
{
cssPosition:"211,-1,-1,522,225,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"211",
width:"225"
},
dataProviderID:"_toSalesRepCode",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"627651CC-8BFA-40FD-9DCD-CD71E26DB231",
visible:true
},
name:"_toSalesRep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8338A786-DA7F-4AEB-B3F4-F28D951975A0"
},
{
cssPosition:"181,-1,-1,522,225,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"181",
width:"225"
},
dataProviderID:"_toSalesOrder",
enabled:true,
onDataChangeMethodID:"27274EBA-BB7F-4C42-BD0D-2D279D38992A",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_toSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8B392065-6F88-4B9D-98E6-5363724647E7"
},
{
cssPosition:"211,-1,-1,150,225,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"211",
width:"225"
},
dataProviderID:"_fromSalesRepCode",
editable:true,
enabled:true,
onDataChangeMethodID:"14B7EE58-6D68-432B-A4F7-D7A3C125D2F6",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"627651CC-8BFA-40FD-9DCD-CD71E26DB231",
visible:true
},
name:"_fromSalesRep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"8D69832F-1A9B-46D9-A2E3-0EA0E743719A"
},
{
height:200,
partType:5,
typeid:19,
uuid:"9DD93079-58AC-4190-B022-0414AE021815"
},
{
cssPosition:"19,-1,-1,522,225,94",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"94",
left:"522",
right:"-1",
top:"19",
width:"225"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"C1F19107-2692-4A35-AB3A-8AC5616E0AC4"
},
{
cssPosition:"181,-1,-1,9,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"181",
width:"140"
},
enabled:true,
labelFor:"_fromSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesOrder",
visible:true
},
name:"_fromSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C92D51CA-36C0-4FDD-A26B-902FE947D273"
},
{
cssPosition:"121,-1,-1,522,225,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"522",
right:"-1",
top:"121",
width:"225"
},
dataProviderID:"_dateTo",
enabled:true,
onDataChangeMethodID:"AB0176D6-3471-4BFE-9CEA-46659406114B",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"DB6E39C9-1C69-40C1-AFB7-416463C680CC"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:328,
typeid:19,
uuid:"DC51D1B5-0A39-419F-97A4-69D15EFD2D97"
}
],
name:"rpt_report_dlg_params_divplant_dt1_so_co_salesrep",
navigatorID:"-2",
onShowMethodID:"79D66959-EEF1-4980-9986-B6BCADBC5AD7",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"829E7EAC-FB08-413F-A2DB-04FAB2AAA67B"