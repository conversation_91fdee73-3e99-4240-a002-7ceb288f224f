columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
creationOrderIndex:0,
dataType:1,
flags:37,
length:36,
name:"taskstd_id"
},
{
allowNull:true,
creationOrderIndex:170,
dataType:4,
name:"auto_create_postage_task"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:144,
dataType:1,
flags:36,
length:36,
name:"inktype_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:151,
dataType:1,
flags:36,
length:36,
name:"item_id_blanket_plate"
},
{
allowNull:true,
creationOrderIndex:59,
dataType:1,
flags:36,
length:36,
name:"jdftype_id"
},
{
allowNull:true,
autoEnterType:5,
creationOrderIndex:2,
dataType:1,
flags:36,
length:36,
lookupValue:"globals.org_id",
name:"org_id"
},
{
allowNull:true,
creationOrderIndex:58,
dataType:1,
flags:36,
length:36,
name:"systaskfunc_id"
},
{
allowNull:false,
autoEnterType:5,
creationOrderIndex:1,
dataType:1,
flags:36,
length:36,
lookupValue:"globals.avBase_selectedTaskID",
name:"task_id"
},
{
allowNull:true,
creationOrderIndex:69,
dataType:8,
length:53,
name:"taskstd_1_knife_setup"
},
{
allowNull:true,
creationOrderIndex:149,
dataType:4,
name:"taskstd_2_sided"
},
{
allowNull:true,
creationOrderIndex:172,
dataType:4,
name:"taskstd_2_sides_1_pass"
},
{
allowNull:true,
creationOrderIndex:43,
dataType:8,
length:53,
name:"taskstd_3_knife_setup"
},
{
allowNull:true,
creationOrderIndex:44,
dataType:8,
length:53,
name:"taskstd_5_knife_setup"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,50,0]]",
creationOrderIndex:182,
dataType:12,
length:50,
name:"taskstd_acc_expense_link"
},
{
allowNull:true,
creationOrderIndex:184,
dataType:-9,
length:50,
name:"taskstd_acc_internal_code"
},
{
allowNull:true,
creationOrderIndex:169,
dataType:4,
name:"taskstd_access_all_dies"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:105,
dataType:8,
name:"taskstd_add_to_spine_thickness"
},
{
allowNull:true,
creationOrderIndex:86,
dataType:4,
name:"taskstd_adj_caliper"
},
{
allowNull:true,
creationOrderIndex:117,
dataType:4,
name:"taskstd_adj_coated_paper"
},
{
allowNull:true,
creationOrderIndex:173,
dataType:4,
name:"taskstd_adj_flat_size"
},
{
allowNull:true,
creationOrderIndex:87,
dataType:4,
name:"taskstd_adj_ink_coverage"
},
{
allowNull:true,
creationOrderIndex:100,
dataType:4,
name:"taskstd_adj_substrate"
},
{
allowNull:true,
creationOrderIndex:118,
dataType:4,
name:"taskstd_adj_win_count"
},
{
allowNull:true,
creationOrderIndex:119,
dataType:4,
name:"taskstd_adj_win_open"
},
{
allowNull:true,
creationOrderIndex:120,
dataType:4,
name:"taskstd_adj_win_size"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:106,
dataType:8,
name:"taskstd_auto_calc_coil_size"
},
{
allowNull:true,
creationOrderIndex:125,
dataType:8,
length:53,
name:"taskstd_bed_length"
},
{
allowNull:true,
creationOrderIndex:124,
dataType:8,
length:53,
name:"taskstd_bed_width"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:40,
dataType:-9,
defaultValue:"P",
length:1,
name:"taskstd_bindery_type"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:183,
dataType:8,
name:"taskstd_bundle_add_material"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:49,
dataType:4,
defaultValue:"10",
name:"taskstd_bundle_qty"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:176,
dataType:12,
defaultValue:"S",
length:1,
name:"taskstd_calc_method"
},
{
allowNull:true,
creationOrderIndex:152,
dataType:4,
name:"taskstd_calc_roll_changes"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:23,
dataType:-9,
defaultValue:"I",
length:1,
name:"taskstd_calc_type"
},
{
allowNull:true,
creationOrderIndex:62,
dataType:-9,
length:1,
name:"taskstd_calc_type_2"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:195,
dataType:12,
length:1,
name:"taskstd_click_calc_based_on"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:166,
dataType:4,
defaultValue:"0",
name:"taskstd_click_calc_total_imp"
},
{
allowNull:true,
creationOrderIndex:52,
dataType:-9,
length:1,
name:"taskstd_click_pricing_method"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:56,
dataType:4,
defaultValue:"0",
name:"taskstd_clicks_x_colors"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:4,
dataType:4,
defaultValue:"0",
name:"taskstd_coaters"
},
{
allowNull:true,
creationOrderIndex:63,
dataType:4,
name:"taskstd_coaters_nr"
},
{
allowNull:true,
creationOrderIndex:129,
dataType:8,
length:53,
name:"taskstd_color_bar_allowance"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:29,
dataType:4,
defaultValue:"0",
name:"taskstd_color_diff_rate"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:28,
dataType:-9,
defaultValue:"B",
length:1,
name:"taskstd_color_type"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:45,
dataType:4,
defaultValue:"1",
name:"taskstd_colorbar"
},
{
allowNull:true,
creationOrderIndex:3,
dataType:4,
name:"taskstd_colours"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:64,
dataType:4,
defaultValue:"0",
name:"taskstd_cost_per_piece"
},
{
allowNull:true,
creationOrderIndex:143,
dataType:-9,
length:10,
name:"taskstd_cost_per_piece_type"
},
{
allowNull:true,
creationOrderIndex:104,
dataType:8,
length:53,
name:"taskstd_coverage"
},
{
allowNull:true,
autoEnterSubType:0,
autoEnterType:1,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:185,
dataType:12,
length:1,
name:"taskstd_custom_invoice_qty"
},
{
allowNull:true,
creationOrderIndex:190,
dataType:4,
name:"taskstd_custom_run_types"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:47,
dataType:4,
defaultValue:"0",
name:"taskstd_custpricing"
},
{
allowNull:true,
creationOrderIndex:91,
dataType:8,
length:53,
name:"taskstd_cutoff"
},
{
allowNull:true,
creationOrderIndex:76,
dataType:4,
name:"taskstd_cutoff_after_tower"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:98,
dataType:12,
defaultValue:"M",
length:1,
name:"taskstd_cutoff_from"
},
{
allowNull:true,
creationOrderIndex:75,
dataType:8,
length:53,
name:"taskstd_cutoff_max"
},
{
allowNull:true,
creationOrderIndex:74,
dataType:8,
length:53,
name:"taskstd_cutoff_min"
},
{
allowNull:true,
creationOrderIndex:88,
dataType:4,
name:"taskstd_cutoff_nr"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:78,
dataType:4,
defaultValue:"0",
name:"taskstd_cutoff_var"
},
{
allowNull:true,
creationOrderIndex:163,
dataType:4,
name:"taskstd_cuts_per_hr"
},
{
allowNull:true,
creationOrderIndex:19,
dataType:8,
length:53,
name:"taskstd_cyl_circum"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:97,
dataType:12,
defaultValue:"F",
length:1,
name:"taskstd_cylinder_type"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:127,
dataType:12,
length:1,
name:"taskstd_delivery_method"
},
{
allowNull:true,
creationOrderIndex:33,
dataType:8,
length:53,
name:"taskstd_die_charge"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:53,
dataType:4,
defaultValue:"0",
name:"taskstd_diff_rates_fb"
},
{
allowNull:true,
creationOrderIndex:54,
dataType:8,
length:53,
name:"taskstd_diff_rates_prcnt_cost"
},
{
allowNull:true,
creationOrderIndex:55,
dataType:8,
length:53,
name:"taskstd_diff_rates_prcnt_price"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:99,
dataType:4,
defaultValue:"0",
name:"taskstd_donot_purchase"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:21,
dataType:4,
defaultValue:"0",
name:"taskstd_double_round"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:20,
dataType:4,
defaultValue:"0",
name:"taskstd_double_wide"
},
{
allowNull:true,
creationOrderIndex:126,
dataType:4,
name:"taskstd_expand_forms"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:167,
dataType:1,
flags:36,
length:36,
name:"taskstd_final_trim_task_id"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:153,
dataType:4,
defaultValue:"0",
name:"taskstd_flag_postage_in_sales"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:189,
dataType:12,
length:1,
name:"taskstd_flexo_worktype"
},
{
allowNull:true,
creationOrderIndex:84,
dataType:4,
name:"taskstd_flg_card_feeder"
},
{
allowNull:true,
creationOrderIndex:82,
dataType:4,
name:"taskstd_flg_cover_deck"
},
{
allowNull:true,
creationOrderIndex:85,
dataType:4,
name:"taskstd_flg_face_trim"
},
{
allowNull:true,
creationOrderIndex:83,
dataType:4,
name:"taskstd_flg_hand_feeder"
},
{
allowNull:true,
creationOrderIndex:16,
dataType:8,
length:53,
name:"taskstd_gap"
},
{
allowNull:true,
creationOrderIndex:65,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_costsales"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:161,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_fg"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:158,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_postage_clr"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:154,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_postage_exp"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:159,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_postage_rev"
},
{
allowNull:true,
creationOrderIndex:66,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_sales"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:137,
dataType:1,
flags:36,
length:36,
name:"taskstd_glacct_id_wip"
},
{
allowNull:true,
creationOrderIndex:115,
dataType:8,
length:53,
name:"taskstd_grind_off"
},
{
allowNull:true,
creationOrderIndex:128,
dataType:-9,
length:8,
name:"taskstd_grindoff"
},
{
allowNull:true,
creationOrderIndex:5,
dataType:8,
length:53,
name:"taskstd_grip"
},
{
allowNull:true,
creationOrderIndex:93,
dataType:4,
name:"taskstd_helpers"
},
{
allowNull:true,
creationOrderIndex:24,
dataType:8,
length:53,
name:"taskstd_inches_per_lift"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:38,
dataType:4,
defaultValue:"0",
name:"taskstd_inline"
},
{
allowNull:true,
creationOrderIndex:155,
dataType:4,
name:"taskstd_is_coil_binding"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:109,
dataType:4,
defaultValue:"0",
name:"taskstd_is_converting_press"
},
{
allowNull:true,
creationOrderIndex:130,
dataType:4,
name:"taskstd_is_envelope_press"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:131,
dataType:8,
name:"taskstd_left_side_guide"
},
{
allowNull:true,
creationOrderIndex:36,
dataType:4,
name:"taskstd_lifts_per_hr"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:34,
dataType:-9,
defaultValue:"N",
length:1,
name:"taskstd_machine_var_based_on"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:25,
dataType:4,
defaultValue:"0",
name:"taskstd_materials_needed"
},
{
allowNull:true,
creationOrderIndex:132,
dataType:8,
length:53,
name:"taskstd_max_caliper"
},
{
allowNull:true,
creationOrderIndex:188,
dataType:4,
name:"taskstd_max_helpers"
},
{
allowNull:true,
creationOrderIndex:15,
dataType:8,
length:53,
name:"taskstd_max_lift"
},
{
allowNull:true,
creationOrderIndex:138,
dataType:4,
name:"taskstd_max_nr_rolls"
},
{
allowNull:true,
creationOrderIndex:139,
dataType:4,
name:"taskstd_max_nr_slitters"
},
{
allowNull:true,
creationOrderIndex:10,
dataType:8,
length:53,
name:"taskstd_max_paper_height"
},
{
allowNull:true,
creationOrderIndex:9,
dataType:8,
length:53,
name:"taskstd_max_paper_width"
},
{
allowNull:true,
creationOrderIndex:12,
dataType:8,
length:53,
name:"taskstd_max_print_area_height"
},
{
allowNull:true,
creationOrderIndex:11,
dataType:8,
length:53,
name:"taskstd_max_print_area_width"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:150,
dataType:8,
name:"taskstd_max_proof_width"
},
{
allowNull:true,
creationOrderIndex:18,
dataType:8,
length:53,
name:"taskstd_max_roll_size"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:187,
dataType:8,
name:"taskstd_max_runspoils"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:92,
dataType:8,
name:"taskstd_max_slowdown"
},
{
allowNull:true,
creationOrderIndex:81,
dataType:8,
length:53,
name:"taskstd_max_thickness"
},
{
allowNull:true,
creationOrderIndex:133,
dataType:8,
length:53,
name:"taskstd_min_caliper"
},
{
allowNull:true,
creationOrderIndex:61,
dataType:8,
length:53,
name:"taskstd_min_charge"
},
{
allowNull:true,
creationOrderIndex:8,
dataType:8,
length:53,
name:"taskstd_min_paper_heigth"
},
{
allowNull:true,
creationOrderIndex:7,
dataType:8,
length:53,
name:"taskstd_min_paper_width"
},
{
allowNull:true,
creationOrderIndex:17,
dataType:8,
length:53,
name:"taskstd_min_roll_size"
},
{
allowNull:true,
autoEnterType:5,
creationOrderIndex:46,
dataType:8,
length:53,
lookupValue:"globals.avPref_sales_markup",
name:"taskstd_mrkup"
},
{
allowNull:true,
creationOrderIndex:146,
dataType:-9,
length:1,
name:"taskstd_mrkup_calc_type"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:147,
dataType:12,
length:1,
name:"taskstd_mrkup_type"
},
{
allowNull:true,
creationOrderIndex:121,
dataType:4,
name:"taskstd_multi_sheets"
},
{
allowNull:true,
creationOrderIndex:164,
dataType:4,
name:"taskstd_multiple_materials"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:136,
dataType:4,
defaultValue:"0",
name:"taskstd_needs_plates"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:31,
dataType:4,
defaultValue:"0",
name:"taskstd_nr_inks"
},
{
allowNull:true,
creationOrderIndex:35,
dataType:4,
name:"taskstd_nr_pockets"
},
{
allowNull:true,
creationOrderIndex:27,
dataType:4,
name:"taskstd_nr_roll_stands"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:140,
dataType:4,
defaultValue:"0",
name:"taskstd_one_plate_per_color"
},
{
allowNull:true,
creationOrderIndex:48,
dataType:-9,
length:8,
name:"taskstd_overlap"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:148,
dataType:12,
defaultValue:"B",
length:1,
name:"taskstd_pack_by"
},
{
allowNull:true,
creationOrderIndex:95,
dataType:4,
name:"taskstd_pack_weight_only"
},
{
allowNull:true,
creationOrderIndex:37,
dataType:-9,
length:1,
name:"taskstd_packaging_option"
},
{
allowNull:true,
creationOrderIndex:41,
dataType:-9,
length:1,
name:"taskstd_paper_type"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:6,
dataType:4,
defaultValue:"0",
name:"taskstd_perf_score"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:57,
dataType:4,
defaultValue:"1",
name:"taskstd_perfecting_only"
},
{
allowNull:true,
creationOrderIndex:60,
dataType:4,
name:"taskstd_perfector2_after_unit"
},
{
allowNull:true,
creationOrderIndex:14,
dataType:4,
name:"taskstd_perfector_after_unit"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:13,
dataType:-9,
defaultValue:"F",
length:1,
name:"taskstd_perfector_type"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:162,
dataType:1,
flags:36,
length:36,
name:"taskstd_plating_standard"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:156,
dataType:8,
name:"taskstd_postage_markup"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:168,
dataType:1,
flags:36,
length:36,
name:"taskstd_pre_trim_task_id"
},
{
allowNull:true,
autoEnterType:3,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:96,
dataType:12,
defaultValue:"W",
length:1,
name:"taskstd_press_type"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:32,
dataType:-9,
defaultValue:"I",
length:1,
name:"taskstd_pricing_model"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:110,
dataType:4,
defaultValue:"1",
name:"taskstd_print_engines"
},
{
allowNull:true,
creationOrderIndex:70,
dataType:1,
flags:36,
length:36,
name:"taskstd_purch_taxgroup_id"
},
{
allowNull:true,
creationOrderIndex:71,
dataType:-9,
length:1,
name:"taskstd_purchtax_option"
},
{
allowNull:true,
creationOrderIndex:141,
dataType:4,
name:"taskstd_qty_per_core"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:50,
dataType:4,
defaultValue:"0",
name:"taskstd_requires_backer"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:51,
dataType:4,
defaultValue:"0",
name:"taskstd_requires_cover"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:134,
dataType:8,
name:"taskstd_right_side_guide"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:174,
dataType:8,
name:"taskstd_roll_change_material"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:142,
dataType:8,
name:"taskstd_roll_change_time"
},
{
allowNull:true,
creationOrderIndex:122,
dataType:4,
name:"taskstd_roll_to_roll"
},
{
allowNull:true,
creationOrderIndex:123,
dataType:8,
length:53,
name:"taskstd_roll_to_roll_setup"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:135,
dataType:8,
name:"taskstd_roll_to_roll_setup_ft"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:175,
dataType:1,
flags:36,
length:36,
name:"taskstd_rolltosheet_task_id"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:22,
dataType:-9,
defaultValue:"P",
length:1,
name:"taskstd_run_type"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:80,
dataType:-9,
defaultValue:"S",
length:1,
name:"taskstd_run_type_web"
},
{
allowNull:true,
creationOrderIndex:67,
dataType:4,
name:"taskstd_setup_sheets"
},
{
allowNull:true,
creationOrderIndex:26,
dataType:8,
length:53,
name:"taskstd_setup_time"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:30,
dataType:4,
defaultValue:"0",
name:"taskstd_special_inks"
},
{
allowNull:true,
creationOrderIndex:101,
dataType:4,
name:"taskstd_speed_bw"
},
{
allowNull:true,
creationOrderIndex:102,
dataType:4,
name:"taskstd_speed_color"
},
{
allowNull:true,
creationOrderIndex:68,
dataType:4,
name:"taskstd_speed_unit"
},
{
allowNull:true,
creationOrderIndex:181,
dataType:4,
name:"taskstd_spine_glue"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:107,
dataType:1,
flags:36,
length:36,
name:"taskstd_spine_item_class"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:108,
dataType:8,
name:"taskstd_spine_rounding_factor"
},
{
allowNull:true,
creationOrderIndex:180,
dataType:4,
name:"taskstd_spine_thickness_calc"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:160,
dataType:12,
length:1,
name:"taskstd_spoils_based_on"
},
{
allowNull:true,
creationOrderIndex:157,
dataType:4,
name:"taskstd_std_spine_thickness"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,100,0]]",
creationOrderIndex:192,
dataType:12,
length:100,
name:"taskstd_task_prod_billing_rpt_col_header"
},
{
allowNull:true,
creationOrderIndex:194,
dataType:4,
name:"taskstd_task_prod_billing_rpt_col_position"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,50,0]]",
creationOrderIndex:193,
dataType:12,
length:50,
name:"taskstd_task_prod_billing_rpt_data_source"
},
{
allowNull:true,
creationOrderIndex:191,
dataType:4,
name:"taskstd_task_use_in_prod_billing_rpt"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:179,
dataType:12,
length:1,
name:"taskstd_task_use_in_qty_cost_by_cust_po_rpt"
},
{
allowNull:true,
creationOrderIndex:177,
dataType:4,
name:"taskstd_task_use_in_sales_revenue_rpt"
},
{
allowNull:true,
creationOrderIndex:178,
dataType:4,
name:"taskstd_task_use_in_various_fees_rpt"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:196,
dataType:4,
defaultValue:"0",
name:"taskstd_tax_additional_charges_postage"
},
{
allowNull:true,
creationOrderIndex:72,
dataType:4,
name:"taskstd_towers"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:114,
dataType:8,
name:"taskstd_trim_face"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:113,
dataType:12,
length:1,
name:"taskstd_trim_folio"
},
{
allowNull:true,
creationOrderIndex:116,
dataType:8,
length:53,
name:"taskstd_trim_foot"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:112,
dataType:8,
name:"taskstd_trim_head"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:111,
dataType:8,
name:"taskstd_trim_pull_lap"
},
{
allowNull:true,
creationOrderIndex:165,
dataType:4,
name:"taskstd_trim_roll_2_sheets"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:42,
dataType:4,
defaultValue:"1",
name:"taskstd_trimmer"
},
{
allowNull:true,
autoEnterSubType:0,
autoEnterType:1,
creationOrderIndex:73,
dataType:4,
defaultValue:"0",
name:"taskstd_turning_bar"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:89,
dataType:4,
defaultValue:"1",
name:"taskstd_turning_bar_nr"
},
{
allowNull:true,
creationOrderIndex:145,
dataType:4,
name:"taskstd_use_1plate_for_0inks"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:39,
dataType:4,
defaultValue:"0",
name:"taskstd_variable_data"
},
{
allowNull:true,
compatibleColumnTypes:"[[8,53,0]]",
creationOrderIndex:103,
dataType:8,
name:"taskstd_wash_time"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:79,
dataType:-9,
defaultValue:"S",
length:1,
name:"taskstd_web_config"
},
{
allowNull:true,
creationOrderIndex:90,
dataType:4,
name:"taskstd_web_nr"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,1,0]]",
creationOrderIndex:186,
dataType:12,
length:1,
name:"taskstd_web_tower_config"
},
{
allowNull:true,
autoEnterType:3,
creationOrderIndex:94,
dataType:4,
defaultValue:"1",
name:"taskstd_webs_count"
},
{
allowNull:true,
creationOrderIndex:77,
dataType:4,
name:"tasktd_turnbar_after_tower"
},
{
allowNull:true,
creationOrderIndex:171,
dataType:4,
name:"use_for_mailing_items"
}
],
name:"sa_task_standard",
tableType:0