/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"FE7FEBC0-2144-4D46-BC95-C236250E1EE7",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"BE08EC2F-3CA5-453A-9545-5CDBEFE37C4E"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"11D35284-1251-4E87-82BE-623BDF316C30"}
 */
function onShowForm(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, event]);
			return null;
		}
	}

	var result =  _super.onShowForm(firstShow, event);
	controller.readOnly = false;
	elements.grid.getColumn(elements.grid.getColumnIndex("sa_cash_receipt_date")).format= globals.avBase_dateFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("sa_cash_receipt_trans_date")).format= globals.avBase_dateFormat;
	elements.grid.getColumn(elements.grid.getColumnIndex("sa_cash_receipt_payment_amt")).format= globals.avBase_currencyFormat;
	
	_bWorkatoIntegration = globals.isWorkatoIntegration();
	_bWorkatoUseInvoiceRegister = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.WorkatoUseCashReceiptsRegister);
	
	applyFilters(firstShow);
	refreshUI();
	foundset.sort('sa_cash_receipt_date desc');
	application.executeLater(setToolBarOptions,500);
	return result;
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F3E6C0E0-D1B6-415A-94FD-16569EDF0139"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0" && col.styleClass.search(' disabled') == -1) {
		scopes.globals.svy_nav_toggleView(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"B558D6D3-2D69-400F-824C-E932DB37759A"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	scopes.globals.svy_nav_toggleView(event)
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"37AB7B88-63A0-40ED-BF44-7309735661A3",variableType:8}
 */
var _IntegrationStatusFilter = null;

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"A578082E-CFFF-44DF-9842-FE5CD140FAE9"}
 */
function onDataChangeFilter(oldValue, newValue, event) {
	applyFilters(true);
	refreshUI();
	return true;
}

/**
 * Apply filters to foundset
 * @param {Boolean} bReApplyFilters
 * @properties={typeid:24,uuid:"D43E83FF-6233-495A-B917-1D3611D3AE8A"}
 */
function applyFilters(bReApplyFilters) {
	if(bReApplyFilters) {
		foundset.removeFoundSetFilterParam('CashReceiptStatusFilter');
		foundset.removeFoundSetFilterParam('CashReceiptCustomerFilter');
		foundset.removeFoundSetFilterParam("CashReceiptIntegrationStatusFilter");
		
		if (_StatusFilter == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted && _IntegrationStatusFilter != '2') {
			foundset.addFoundSetFilterParam('sa_cash_receipt_ws_posted','=', _IntegrationStatusFilter,'CashReceiptIntegrationStatusFilter');
		}
		
		if (_StatusFilter != null && _StatusFilter != '') {
			foundset.addFoundSetFilterParam('sa_cash_receipt_status','=',_StatusFilter,'CashReceiptStatusFilter');
		}
		
		if (_CustomerFilter != null && _CustomerFilter != '') {
			foundset.addFoundSetFilterParam('cust_id','=',_CustomerFilter,'CashReceiptCustomerFilter');
		}
		
		foundset.loadAllRecords();
		forms.utils_quickSearch._qs_quickSearch = '';
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * 
 * @return
 *
 * @properties={typeid:24,uuid:"639447FE-7023-4B8C-96C1-BAAAA7FA7FA9"}
 */
function dc_edit(_event, _triggerForm) {
	return forms.sa_cash_receipt_dtl.dc_edit(_event,_triggerForm);
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * @return
 * @properties={typeid:24,uuid:"35285B52-5EEA-47AC-8E4E-04C9C6CE35A4"}
 */
function dc_new(_event, _triggerForm) {
	return forms.sa_cash_receipt_dtl.dc_new(_event,_triggerForm);
}

/**
 * Refresh UI
 *
 * @properties={typeid:24,uuid:"C2F3E636-E8EF-4C06-A861-7BBA1D3C7942"}
 */
function refreshUI() {
	var bActive = _StatusFilter == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted && !_bWorkatoUseInvoiceRegister && _bWorkatoIntegration;
	elements._integrationStatusFilter.visible = bActive;
	elements.btnRelease.visible = bActive && _IntegrationStatusFilter == 0
	elements.grid.getColumn(elements.grid.getColumnIndex("print_check_flag")).visible= bActive && _IntegrationStatusFilter == 0;
	elements.grid.getColumn(elements.grid.getColumnIndex("sa_cash_receipt_ws_posted")).visible= bActive;
	elements.sa_cash_receipt_ws_message.visible = bActive && sa_cash_receipt_ws_posted == 0;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BF58FF6F-A0D2-48D0-9245-6A428880342B"}
 */
function onAction_import(event) {
	forms.utils_dataImport_dtl.selectFile("Cash Receipts");
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5E57BD19-086A-44CF-8C4D-BD006CBC48FD"}
 */
function onDataChange_integrationFilter(oldValue, newValue, event) {
	if (newValue == 0) {
		resetSelectFlag();
	}
	applyFilters(true);
	refreshUI();
	return true;
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"04DEA085-56B2-4FCA-BBE7-7FC0ABD7B9A2"}
 */
function onAction_btnResetToPost(event) {
	if (foundset.getSize() == 0) {
		return;
	}
	
	var rCashReceipt;
	var iMax = foundset.getSize();
	var aSelectedInvoices = [];
	globals.selectedInvoices = 0;
	var sMessage = "";
	
	for (var inv = 1; inv <= iMax; inv++) {
		rCashReceipt = foundset.getRecord(inv);
		if (rCashReceipt.sa_cash_receipt_ws_posted == 0 && !rCashReceipt.external_id) {
			aSelectedInvoices[globals.selectedInvoices] = rCashReceipt.sa_cash_receipt_id;
			globals.selectedInvoices = globals.selectedInvoices + 1;
		}
	}

	if (globals.selectedInvoices > 1) {
		sMessage = i18n.getI18NMessage('avanti.dialog.resetReceiptToPostSelected_msg').replace("selected", globals.selectedInvoices.toString());
	}
	else {
		sMessage = i18n.getI18NMessage('avanti.dialog.resetReceiptToPost_msg');
	}
	
	var _ok = i18n.getI18NMessage('avanti.dialog.ok');
	var _no = i18n.getI18NMessage('avanti.dialog.cancel');
	var _response = globals.DIALOGS["showWarningDialog"](i18n.getI18NMessage('avanti.dialog.confirmAction'), sMessage, _ok, _no);

	if (_response == _ok) {
		if (globals.selectedInvoices > 0) {
			for (var i = 0; i < globals.selectedInvoices; i++) {
				foundset.selectRecord(aSelectedInvoices[i]);
				resetCashReceiptToPost(foundset.getSelectedRecord());
			}
		} else {
			resetCashReceiptToPost(foundset.getSelectedRecord());
		}

		databaseManager.saveData();
		applyFilters(true);
		refreshUI();
	}
	
	/**
	 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceiptToReset
	 */
	function resetCashReceiptToPost(rCashReceiptToReset) {
		if (rCashReceiptToReset) {
			rCashReceiptToReset.sa_cash_receipt_ws_posted = null;
			rCashReceiptToReset.sa_cash_receipt_ws_message = null;
		}
	}
}

/**
 * reset the select flag
 *
 * @properties={typeid:24,uuid:"F205A388-EC9C-4EB8-BFC4-C1738CD4DAE1"}
 */
function resetSelectFlag() {
	for (var i = 1; i <=  foundset.getSize(); i++) {
		var rRec = foundset.getRecord(i);
		rRec.print_check_flag = 0;
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C95A81C0-6BFB-4BE1-95D7-89F693E9BD47"}
 */
function onAction_check(event) {
    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.RequiredFields) {
        return;
    }

    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Browse) {
        if (globals.avBase_tableViewInvoiceCheckStatus == 0) {
            //Check All
            globals.avBase_tableViewInvoiceCheckStatus = 1
        }
        else {
            //Uncheck All
            globals.avBase_tableViewInvoiceCheckStatus = 0
        }

        setPrintCheckFlag()
    }

    return;
}

/**
 * Set record print flag
 *
 *
 * @properties={typeid:24,uuid:"C341EF6D-40DA-4219-854D-E1BFD847594F"}
 */
function setPrintCheckFlag() {
    for (var i = 1; i <= foundset.getSize(); i++) {
        var rRec = foundset.getRecord(i);

        rRec.print_check_flag = globals.avBase_tableViewInvoiceCheckStatus;
    }
}

/**
 * Called when the selected rows have changed.
 *
 * @param {Boolean} [isgroupselection]
 * @param {String} [groupcolumnid]
 * @param [groupkey]
 * @param {Boolean} [groupselection]
 *
 * @properties={typeid:24,uuid:"C94CA6C1-4FAE-4590-ABD4-9FCC57B37B86"}
 */
function onSelectedRowsChanged(isgroupselection, groupcolumnid, groupkey, groupselection) {
    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.RequiredFields) {
        return;
    }

    if (isgroupselection && globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Browse) {

		if (groupselection) {

			// Do something when header checkbox is selected
			globals.avBase_tableViewInvoiceCheckStatus = 1

		} else {

			// Do something when header checkbox is deselected
			globals.avBase_tableViewInvoiceCheckStatus = 0

		}

		setPrintCheckFlag();

	}

	return;
}
