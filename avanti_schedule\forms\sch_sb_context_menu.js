/**
 * @type {UUID}
 * 
 * @properties={typeid:35,uuid:"2BB392F2-8BD2-416C-AD70-38990F9EFE1B",variableType:-4}
 */
var _uMSID = null;

/**
 * @public 
 * 
 * @param {Number} x
 * @param {Number} y
 * @param {String} uMSID
 *
 * @properties={typeid:24,uuid:"6F531939-993E-4094-8671-5DCB8EF44209"}
 */
function show(x, y, uMSID) {
	_uMSID = application.getUUID(uMSID);
	globals.DIALOGS.showFormInModalDialog(forms.sch_sb_context_menu, x, y, null, null, "", false, false, "dlgSBContextMenu", true);
}

/**
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 *
 * @private
 * @override
 *
 * @properties={typeid:24,uuid:"8FE8B946-BA49-44A1-81B5-C14D16C4DBDE"}
 */
function onShowForm(_firstShow, _event) {
	_super.onShowForm(_firstShow, _event);
	
	if (_uMSID) {
		foundset.loadRecords(_uMSID);
		
		if (contains_shift_break && !ms_flg_locked) {
			elements.btnSplit.enabled = true;
		}
		else {
			elements.btnSplit.enabled = false;
		}
	}
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5898C751-51CF-473F-ABEF-55E08CEFC767"}
 */
function onDataChange_chkLockMS(oldValue, newValue, event) {
	if (newValue) {
		forms.sch_agenda_dtl.lockMilestone(ms_id);
		elements.btnSplit.enabled = false;
	} 
	else {
		forms.sch_agenda_dtl.unlockTask(ms_id);
		elements.btnSplit.enabled = (contains_shift_break == 1);
	}
	
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2452B10F-E727-4920-817C-2CD65A99C015"}
 */
function onDataChange_chkLockSection(oldValue, newValue, event) {
	if (newValue) {
		forms.sch_agenda_dtl.lockSection(ms_id);
		elements.btnSplit.enabled = false;
	} 
	else {
		forms.sch_agenda_dtl.unlockSection(ms_id);
		elements.btnSplit.enabled = (contains_shift_break == 1);
	}
	
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9557C2D9-451C-4EA8-BFD6-9CB45FAF037B"}
 */
function onDataChange_chkLockJob(oldValue, newValue, event) {
	if (newValue) {
		forms.sch_agenda_dtl.lockJob(ms_id);
		elements.btnSplit.enabled = false;
	} 
	else {
		forms.sch_agenda_dtl.unlockJob(ms_id);
		elements.btnSplit.enabled = (contains_shift_break == 1);
	}
	
	return true;
}


/**
 * Fired when the button is clicked.
 *
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B6AFF85A-A103-4B65-8F32-7CFE383432AD"}
 */
function onAction_btnSplit(event) {
	forms.sch_agenda_dtl.splitMilestone(ms_id);
	elements.btnSplit.enabled = false;
	globals.DIALOGS.closeForm(null, "dlgSBContextMenu");
}
