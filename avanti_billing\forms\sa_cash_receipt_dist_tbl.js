/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"6E8FB15C-155B-4CE7-B01C-F8CCD4081256",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"F58B4083-9EDA-4608-B1DE-6B9DEF551FDB"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"D13C9B08-AFCB-4400-997F-3EEEE360F136"}
 */
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	_super.onShowForm(_firstShow, _event)
	 
	 elements.grid.getColumn(elements.grid.getColumnIndex("cr_dist_credit_amount")).format= globals.avBase_currencyFormat;
	 elements.grid.getColumn(elements.grid.getColumnIndex("cr_dist_debit_amount")).format= globals.avBase_currencyFormat;
	 elements.total_credit_amount.format = globals.avBase_currencyFormat;
	 elements.total_debit_amount.format = globals.avBase_currencyFormat;
	 elements.total_credit_amount.enabled = false;
	 elements.total_debit_amount.enabled = false;
	 
	 refreshUI();
	 
	 
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"7325C6DF-9AA5-45B2-ADDE-364638F044C4",variableType:8}
 */
var total_debit_amount = 0.00;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"923F834D-9A28-413C-8641-AC79C95C5BBD",variableType:8}
 */
var total_credit_amount = 0.00;

/**
 * Refresh U/I
 *
 * @properties={typeid:24,uuid:"3FBC5F15-56A0-43C5-96AB-601F56975E49"}
 */
function refreshUI() 
{
	total_debit_amount = 0;
	total_credit_amount = 0;
	
	var iMax = foundset.getSize();
	for (var i = 1; i <= iMax; i++) {
		var rRec = foundset.getRecord(i);
		
		total_debit_amount += rRec.sacrdist_debit_amount;
		total_credit_amount += rRec.sacrdist_credit_amount;
	}
}
