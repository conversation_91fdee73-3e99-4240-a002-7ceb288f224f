/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"701823D5-515F-4F84-B86A-376AC10715C4",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"EE50F10B-BEC8-4BD2-B379-861CA7C8F3DE"}
 */
function onReady() {
    _gridReady = 1;
}

/**
*
* @param {Boolean} _firstShow
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"D1F99CB0-E7FA-43F9-AD25-E1C40732DB22"}
*/
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	var retval = _super.onShowForm(_firstShow, _event);
	
	elements.grid.getColumn(elements.grid.getColumnIndex("fldPosition")).visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'; 
	elements.grid.getColumn(elements.grid.getColumnIndex("fldLength")).visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position';
	elements.btnClearPositions.visible = forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position';
	
	if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
		foundset.sort('sequence_nr asc');
	}

	return retval;
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CB82A65C-75E3-4B2A-B868-28C20951C75A"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	
	var flag = foundset.getSelectedRecord().biff_use_udv;
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldUseUDV") {
		onAction_uudv(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"FD1F4ABC-DE89-4994-B9C0-54B04AEF0E47"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldPosition") {
		onDataChange_position(oldValue, newValue, event);
	}
	if (col.id == "fldLength") {
		onDataChange_length(oldValue, newValue, event);
	}
	if (col.id == "fldFieldName") {
		onDataChange_avantiField(oldValue, newValue, event);
	}
	if (col.id == "fldCBTableColName") {
		onDataChange_biTableCol(oldValue, newValue, event);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4F319800-B370-453F-8337-9D298D1DCF4A"}
 */
function btnMoveUp_onAction(event) {
    
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		foundset.sort('sequence_nr asc')
		foundset.loadAllRecords()

		var rec = foundset.getSelectedRecord()
		if(rec && rec.sequence_nr > 1){
			var recBef = getRecFromSeqNum(rec.sequence_nr-1)
			if(recBef){
				recBef.sequence_nr++
				rec.sequence_nr--
				databaseManager.saveData()			
				foundset.sort('sequence_nr asc')
				foundset.loadAllRecords()
				foundset.setSelectedIndex(rec.sequence_nr)
				
				if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
					rec.biff_position = recBef.biff_position;
					setNextRecPosition(rec.sequence_nr, rec.biff_position, rec.biff_length);
				}
			}
		}
	}
    
	return;
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"0E1D67B9-0909-4ACC-A42D-F5B1CEF1F8F8"}
 */
function btnMoveDown_onAction(event) {
    
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		foundset.sort('sequence_nr asc')
		foundset.loadAllRecords()

		var rec = foundset.getSelectedRecord()
		if(rec && rec.sequence_nr < foundset.getSize()){
			var recAft = getRecFromSeqNum(rec.sequence_nr+1)
			if(recAft){
				recAft.sequence_nr--
				rec.sequence_nr++
				databaseManager.saveData()			
				foundset.sort('sequence_nr asc')
				foundset.loadAllRecords()
				foundset.setSelectedIndex(rec.sequence_nr)
				
				if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
					recAft.biff_position = rec.biff_position;
					setNextRecPosition(recAft.sequence_nr, recAft.biff_position, recAft.biff_length);
				}
			}
		}
	}
    
	return;
}

/**
 * @param {Number} iSeqNum
 * 
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_field>}
 *
 * @properties={typeid:24,uuid:"6DF4A1F9-9381-42AB-AD88-FB9023DB5288"}
 * @AllowToRunInFind
 */
function getRecFromSeqNum(iSeqNum){
	return foundset.getRecord(iSeqNum);
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"733D7F3E-2F50-445C-AD6F-4968C770C389"}
 */
function onAction_btnAdd(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		foundset.newRecord(false, true);
		foundset.org_id=globals.org_id;
		foundset.sequence_nr = GetNextSeqNum();

		if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format == 'Fixed Position'){
			var rPrev = getPrevRec(foundset.sequence_nr);
			if(rPrev){
				if(rPrev.biff_position > 0 && rPrev.biff_length > 0){
					foundset.biff_position = rPrev.biff_position + rPrev.biff_length;
				}
			}
			else{
				foundset.biff_position = 1;
			}		
		}
		
		databaseManager.saveData(foundset);
	}
    
	return;
}

/**
 * @return
 * @properties={typeid:24,uuid:"9DA4FD05-2CFA-4346-BA3C-B9EF676C2AB2"}
 */
function GetNextSeqNum(){
    
	var tiRetVal;

	tiRetVal = globals.Query("SELECT max(sequence_nr) from sys_batch_inv_file_field where bif_id = '" + forms.sys_batch_inv_file_dtl.foundset.bif_id + "'", true);
	
	if(tiRetVal=='')
		tiRetVal=1;
	else
		tiRetVal+=1;
		
	return tiRetVal;
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"188C41BC-4282-480F-99F7-55D9B47DA93C"}
 */
function onAction_btnDelete(event)
{
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ) {
        
		var rPrev = getPrevRec(sequence_nr);
		
		globals.avUtilities_delete(event);
		
		if(rPrev){
			setNextRecPosition(rPrev.sequence_nr, rPrev.biff_position, rPrev.biff_length);
		}
		else if(utils.hasRecords(foundset)){
			var rFirst = foundset.getRecord(1);
			rFirst.biff_position = 1;
			setNextRecPosition(rFirst.sequence_nr, rFirst.biff_position, rFirst.biff_length);
		}
	}
    
	return;
}

/**
*
* @param {JSEvent} event
*
 * @return
* @properties={typeid:24,uuid:"211D7340-4FFA-41D1-9B8D-CA3BC7A5250B"}
*/
function onLoad(event) {
	return _super.onLoad(event);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C97D610D-762C-406B-AB88-682F4F966A92"}
 */
function onDataChange_avantiField(oldValue, newValue, event) {
	if(!newValue){
		biff_bi_data_table_field_num = null
	}
	else{
		var newDataType = getDataType(newValue)

		if(oldValue){
			var oldDataType = getDataType(oldValue)
			if(!sameDataType(oldDataType, newDataType)){
				biff_bi_data_table_field_num = getNextBITableColNum(newDataType)
			}
		}
		else{
			biff_bi_data_table_field_num = getNextBITableColNum(newDataType)
		}
		
		if(!biff_col_header){
			biff_col_header = application.getValueListDisplayValue('vl_batch_inv_field_names', newValue);
		}
	}
	
	return true
}

/**
 * @param {String} type1
 * @param {String} type2
 *
 * @return
 * @properties={typeid:24,uuid:"8D38F37E-3501-453D-B227-9D27D0367B6B"}
 */
function sameDataType(type1, type2){
	var same = false
	
	if(type1==type2){
		same = true
	}
	else if((type1 == 'number' || type1 == 'currency') && (type2 == 'number' || type2 == 'currency')){
		same = true
	}
	
	return same
}

/**
 * @param {String} dataType
 *
 * @return
 * @properties={typeid:24,uuid:"C7F56383-FC27-46BB-B8FF-5A5F274ADB4E"}
 */
function getNextBITableColNum(dataType){
	var last_num = 0
	var sql = 'select max(biff_bi_data_table_field_num) ' +
	  'from sys_batch_inv_file_field sys ' +
	  'inner join app_avail_batch_inv_fields app on app.abif_id = sys.abif_id ' +
	  'where sys.bif_id = ? and (app.abif_data_type = ? or app.abif_data_type = ?)'

	 if(dataType == 'number' || dataType == 'currency'){
		 last_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(bif_id), 'number', 'currency'])
	 }
	 else{
		 last_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(bif_id), dataType, dataType])
	 }
	 
	 last_num++
	 
	 return last_num
}

/**
 * @param {Number} appFieldID
 *
 * @return
 * @properties={typeid:24,uuid:"053D161E-3863-47E4-94F7-4326BDCA8B87"}
 */
function getDataType(appFieldID){
	return scopes.avDB.getVal('app_avail_batch_inv_fields', ['abif_id'], [appFieldID], 'abif_data_type')
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"6F3EB880-2C38-4B6B-A677-EF6785C94085"}
 */
function onRecordSelection(_event, _form) {
	return _super.onRecordSelection(_event, _form);
}

/**
 * @properties={typeid:24,uuid:"549DADE9-5220-4667-954A-92B5B6262F02"}
 */
function fill_vlBITableColsByDataType(){
	var vlOptions = new Array();	

	if(utils.hasRecords(sys_batch_inv_file_field_to_app_avail_batch_inv_fields)){
		var dataType = sys_batch_inv_file_field_to_app_avail_batch_inv_fields.abif_data_type
		var prefix = ''
		var max = 0
		
		if(dataType == 'char'){
			prefix = 'char_'
			max = 20
		}
		else if(dataType == 'date'){
			prefix = 'date_time_'
			max = 10
		}
		else if(dataType == 'number' || dataType == 'currency'){
			prefix = 'number_'
			max = 20
		}
		
		for(var i=1;i<=max;i++){
			vlOptions.push(prefix + i)
		}
	}

	application.setValueListItems('vlBITableColsByDataType', vlOptions);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"79B4304F-CBEB-4127-AAAF-A8983DF8B683"}
 */
function onDataChange_biTableCol(oldValue, newValue, event) {
	if(newValue){
		if(scopes.avDB.doesFSHaveValue(foundset, 'biff_bi_data_table_field_name', newValue, foundset.getSelectedIndex())){
			globals.showWarning('ThisColumnHasAlreadyBeenUsed.')
//			return false
			biff_bi_data_table_field_name = oldValue
		}
		biff_bi_data_table_field_num = getBITableColNum(newValue)
	}
	else{
		biff_bi_data_table_field_num = null	
	}
	 
	return true
}

/**
 * @param {String} biTableColName
 *
 * @return
 * @properties={typeid:24,uuid:"29B17B73-64DF-4C09-BBCE-A311F9158D3C"}
 */
function getBITableColNum(biTableColName){
	var num = null 

	if(biTableColName){
		var underscorePos = biTableColName.lastIndexOf('_')
		if(underscorePos){
			num = parseInt(biTableColName.substr(underscorePos+1)) 
		}
	}
	
	return num
}

/**
 * Handle focus gained event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"12F16E42-DC30-4C96-BB71-E318C88D6D80"}
 */
function onFocusGained_biTableCol(event) {
	fill_vlBITableColsByDataType();
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"80F6DE50-D3CF-449F-ABBE-A26040BC0BD8"}
 */
function onDataChange_position(oldValue, newValue, event) {
	if(newValue != null){
		if(newValue < 1){
			biff_position = oldValue;
			scopes.avText.showWarning('PositionTooSmall');
		}
		else if(newValue <= getPrevPos()){
			biff_position = oldValue;
			scopes.avText.showWarning('PosMustBe>PrevPos');
		}
		else{
			if(sequence_nr > 1){
				setPrevRecLength(sequence_nr, biff_position);
			}
			if(biff_length > 0){
				setNextRecPosition(sequence_nr, biff_position, biff_length);
			}
		}
	}
	
	return true;
}

/**
 * @param {Number} nCurIdx
 * @param {Number} nCurPos
 *
 * @properties={typeid:24,uuid:"E86DB6F8-35E5-4ED7-BC2F-9C4C34FC477A"}
 */
function setPrevRecLength(nCurIdx, nCurPos){
	var rPrev = getPrevRec(nCurIdx);
	
	if(rPrev){
		rPrev.cbff_length = nCurPos - rPrev.biff_position;
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9C1F3B13-2C48-4267-9532-BD9CE06A0456"}
 */
function onAction_uudv(event) {
	if(biff_use_udv == 1){
		abif_id = null;
	}
	else{
		biff_user_defined_value = null;
	}
}


/**
 * Perform sort.
 *
 * @param {String} dataProviderID element data provider
 * @param {Boolean} asc sort ascending [true] or descending [false]
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5069AEC1-1CEC-4A40-B17C-8C27B26E3662"}
 */
function onSort(dataProviderID, asc, event) {
	if(forms.sys_batch_inv_file_dtl.foundset.bif_file_format != 'Fixed Position'){
		foundset.sort(dataProviderID + (asc ? ' asc' : ' desc'), false);
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DFF92E9D-19CE-432A-A84A-93D328A7B321"}
 */
function onDataChange_length(oldValue, newValue, event) {
	if(newValue != null){
		if(newValue >= 1){
			setNextRecPosition(sequence_nr, biff_position, biff_length);
		}
		else{
			scopes.avText.showWarning('LengthTooSmall');
			biff_length = oldValue;
		}
	}

	return true;
}

/**
 * @param {Number} nCurIdx
 * @param {Number} nCurPos
 * @param {Number} nCurLen
 *
 * @properties={typeid:24,uuid:"B68401CB-4DBA-4133-AD24-56CF03E70356"}
 */
function setNextRecPosition(nCurIdx, nCurPos, nCurLen){
	var rNext = getNextRec(nCurIdx);
	
	if(rNext){
		rNext.biff_position = nCurPos + nCurLen;
		
		if(rNext.biff_length > 0){
			setNextRecPosition(rNext.sequence_nr, rNext.biff_position, rNext.biff_length);
		}
	}
}

/**
 * @param {Number} nCurIdx
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_field>}
 * 
 * @properties={typeid:24,uuid:"3ADBC45D-4283-4AE5-8FB6-9706A4FAA408"}
 */
function getNextRec(nCurIdx){
	if(foundset.getSize() > nCurIdx){
		return foundset.getRecord(nCurIdx + 1);
	}
	else{
		return null;
	}
}

/**
 * @param {Number} nCurIdx
 * @return {JSRecord<db:/avanti/sys_batch_inv_file_field>}
 * 
 * @properties={typeid:24,uuid:"E796FEB1-35C3-498B-B8D6-9C1194827C3A"}
 */
function getPrevRec(nCurIdx){
	if(nCurIdx > 1){
		return foundset.getRecord(nCurIdx - 1);
	}
	else{
		return null;
	}
}

/**
 * @return {Number}
 * 
 * @properties={typeid:24,uuid:"B7065717-6FAA-42BC-B33D-C7373AFBBC12"}
 */
function getPrevPos(){
	var rPrev = getPrevRec(sequence_nr);
	
	if(rPrev){
		return rPrev.biff_position;
	}
	else{
		return null;
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"913848E2-6BA1-4444-97FD-0AB2C2EDF13E"}
 */
function onAction_clearPos(event) {
    
    if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) ){
        
		for(var i=1; i<=foundset.getSize(); i++){
		    
			var r = foundset.getRecord(i);
			
			if(i > 1){
				r.biff_position = null;
			}
			
			r.biff_length = null;
		}
	}
    
	return;
}
