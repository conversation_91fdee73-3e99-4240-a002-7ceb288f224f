customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_cash_receipt",
encapsulation:4,
extendsID:"002BD0A7-8C1D-4970-B183-3BC4738F76F3",
items:[
{
cssPosition:"126,186,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"186",
top:"126",
width:"100"
},
dataProviderID:"total_overpayment_amount_exchanged",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"total_overpayment_amount_exchanged",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"01E993C4-6274-4FB2-AF8E-05E3A01B4CFB"
},
{
cssPosition:"-1,0,50,0,985,1",
json:{
cssPosition:{
bottom:"50",
height:"1",
left:"0",
right:"0",
top:"-1",
width:"985"
},
enabled:true,
styleClass:"label_bts line black text-right",
tabSeq:-1,
visible:true
},
name:"lblLine3",
styleClass:"label_bts line black text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"13A8EE6B-70FB-4E06-9B96-D643E45B9A52"
},
{
cssPosition:"127,10,5,-1,140,22",
json:{
cssPosition:{
bottom:"5",
height:"22",
left:"-1",
right:"10",
top:"127",
width:"140"
},
dataProviderID:"sa_cash_receipt_total_amt_exchanged",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"total_amount_sum_exchanged",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"26B28485-1C51-43CF-9356-2354FF2D8918"
},
{
cssPosition:"126,386,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"386",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_discount_amt",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"discount_amount_sum",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3EC5A930-536A-452A-9636-A03599290FA0"
},
{
cssPosition:"106,10,26,-1,140,22",
json:{
cssPosition:{
bottom:"26",
height:"22",
left:"-1",
right:"10",
top:"106",
width:"140"
},
enabled:true,
styleClass:"bold_black label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.ordrevh_total_amount",
visible:true
},
name:"component_1108A965",
styleClass:"bold_black label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"44739420-A728-4803-B092-9963E7FD4821"
},
{
cssPosition:"105,186,27,-1,100,22",
json:{
cssPosition:{
bottom:"27",
height:"22",
left:"-1",
right:"186",
top:"105",
width:"100"
},
enabled:true,
styleClass:"bold_black label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Overpayment",
visible:true
},
name:"component_ACD59C70",
styleClass:"bold_black label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"467DACF1-527B-4E32-B38C-94136EC9EEE2"
},
{
cssPosition:"126,486,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"486",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_payment_amt",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"payment_amount_sum",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"7208CBAC-A3F3-4375-8883-869198E46701"
},
{
cssPosition:"126,286,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"286",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_adjustment_amt_exchanged",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"adjustment_amt_sum_exchanged",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"786F1CC6-BBC2-496F-8A2E-09A21B96FF4E"
},
{
cssPosition:"105,386,27,-1,100,22",
json:{
cssPosition:{
bottom:"27",
height:"22",
left:"-1",
right:"386",
top:"105",
width:"100"
},
enabled:true,
styleClass:"bold_black label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Discount",
visible:true
},
name:"component_4B471B28",
styleClass:"bold_black label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"882B9946-E214-4E83-8E8D-523A7C31DDA8"
},
{
height:100,
partType:5,
typeid:19,
uuid:"A0F516AC-9269-425D-B8B9-FE291EDB228C"
},
{
cssPosition:"105,486,27,-1,100,22",
json:{
cssPosition:{
bottom:"27",
height:"22",
left:"-1",
right:"486",
top:"105",
width:"100"
},
enabled:true,
styleClass:"bold_black label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.payment",
visible:true
},
name:"component_A5538356",
styleClass:"bold_black label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A416A5F0-A45D-4208-8181-E57CCA41EB34"
},
{
height:154,
partType:8,
typeid:19,
uuid:"B89AAE10-866A-4847-8063-044868E0EB53"
},
{
cssPosition:"126,486,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"486",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_payment_amt_exchanged",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"payment_amount_sum_exchanged",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BFBCEC0D-BE65-427F-9A2F-AA1009180C94"
},
{
cssPosition:"127,10,5,-1,140,22",
json:{
cssPosition:{
bottom:"5",
height:"22",
left:"-1",
right:"10",
top:"127",
width:"140"
},
dataProviderID:"sa_cash_receipt_total_amt",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"total_amount_sum",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C0DA435B-279E-4F26-8DA4-36F1CC1B3BB5"
},
{
cssPosition:"126,186,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"186",
top:"126",
width:"100"
},
dataProviderID:"total_overpayment_amount",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"total_overpayment_amount",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C3068341-8F6A-46B4-8BC9-66D592C6C154"
},
{
cssPosition:"126,386,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"386",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_discount_amt_exchanged",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"discount_amount_sum_exchanged",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D0C67BA5-C02A-4378-98B2-10FA61C6F744"
},
{
cssPosition:"105,286,27,-1,100,22",
json:{
cssPosition:{
bottom:"27",
height:"22",
left:"-1",
right:"286",
top:"105",
width:"100"
},
enabled:true,
styleClass:"bold_black label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.adjustment",
visible:true
},
name:"component_6646A196",
styleClass:"bold_black label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D3721968-01E4-49AF-9EC2-4A9895E3BF19"
},
{
cssPosition:"0,0,59,0,985,95",
json:{
containedForm:"AFD1F377-0BF7-4B22-9727-ECF187524852",
cssPosition:{
bottom:"59",
height:"95",
left:"0",
right:"0",
top:"0",
width:"985"
},
visible:true
},
name:"tabless",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"E0D6C229-6B5E-4695-845F-7FA5D85156FC"
},
{
cssPosition:"126,596,6,10,379,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"10",
right:"596",
top:"126",
width:"379"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
visible:true
},
name:"component_DD20C609",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E6F382FA-F082-4977-8988-F2BD1DABA69D"
},
{
cssPosition:"126,286,6,-1,100,22",
json:{
cssPosition:{
bottom:"6",
height:"22",
left:"-1",
right:"286",
top:"126",
width:"100"
},
dataProviderID:"sa_cash_receipt_adjustment_amt",
editable:false,
enabled:true,
selectOnEnter:false,
styleClass:"not_editable_bold_black textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"adjustment_amt_sum",
styleClass:"not_editable_bold_black textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E7D4AC1F-F7BB-4DA5-9526-61C7112E06E8"
}
],
name:"sa_cash_receipt_dtl_tbl",
onShowMethodID:"A32F6B10-B9D4-4B3B-A6DF-D08EADB58C71",
scrollbars:33,
size:"985,154",
styleName:null,
typeid:3,
uuid:"A9979870-2BD9-4164-9A1D-F674DC5457F6",
view:0