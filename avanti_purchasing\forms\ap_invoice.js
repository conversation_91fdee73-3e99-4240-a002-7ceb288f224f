/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"B46CC865-26DB-454A-9EC5-7229B917044A"}
 */
var _StatusFilter = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"DF3BDE65-22E6-4858-8DF4-B4BB7176FC2C"}
 */
var _SupplierFilter = "";

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"3A626BB8-F577-4ABA-AECA-0E1FC6DC2990",variableType:-4}
 */
var _bOverridePurchaseAllocations = false;

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"A26B3127-62FE-4C21-B82F-3012D611FDBD",variableType:-4}
 */
var _bOverrideFreightAllocations = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B787335E-618D-46B9-AF2A-5F07792BE224",variableType:4}
 */
var _bWorkatoUseInvoiceRegister;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"6536980B-8E55-460B-8941-00BDAEAF2E08",variableType:-4}
 */
var _bWorkatoIntegration = false;

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"0DF4D812-CD96-41BF-A837-A4B3C30EA7DC"}
 */
function onRecordSelection(event, _form) {
	var result = _super.onRecordSelection(event, _form)
	forms.ap_invoice_dtl.refreshUI()
	setToolBarOptions();
	setNoteSource();
	return result
}

/**
 * @properties={typeid:24,uuid:"67A2A9CA-D061-4547-9141-B16296CCAB9E"}
 */
function setNoteSource()
{
    forms.ap_invoice_note_tbl.setNoteSource(ap_invoice_id, ap_invoice_id, globals.$NoteSourceType_AP_Invoice, controller.getName()); 
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"A5E55C76-B305-4BD5-83AA-B1C44A663E53"}
*/
function dc_new(_event, _triggerForm) {
	var result =  _super.dc_new(_event, _triggerForm);
	ap_invoice_status = 'O';
	forms.ap_invoice_dtl.lockSupplierField();
	return result;
}

/**
 * @param {JSFoundSet} _foundset
 * @param {Boolean} _multiDelete
 *
 * @return
 * @properties={typeid:24,uuid:"7C21EC28-FED5-4EF7-89EB-F5966D0E2F89"}
 */
function dc_delete_pre(_foundset, _multiDelete) {
    _super.dc_delete_pre(_foundset, _multiDelete)
    
    if (ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.Posted) {
        globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.deleteAPInvoice_title"),
            i18n.getI18NMessage("avanti.dialog.deleteAPInvoice_msg"),
            i18n.getI18NMessage("avanti.dialog.ok"));
        return -1;
    }
    
    return 1;
}

/**
 * Get the buyout offset account
 *
 * <AUTHOR> Dol
 * @since Apr 16, 2019
 * @param {UUID} uPoRecId
 * @param {String} sDistType
 * @returns {JSRecord<db:/avanti/po_receipt_dist>} 
 * @private
 *
 * @properties={typeid:24,uuid:"34319C36-7D51-4CDA-A0DB-E7BBEC5F98C4"}
 */
function getBuyoutOffsetAccount(uPoRecId, sDistType) {
    if (!uPoRecId && !sDistType) {
        return null;
    }
    
    /***@type {{sql:String, 
     *          args:Array, 
     *          server:String, 
     *          maxRows:Number, 
     *          table:String}}*/
    var oSQL = {};
    oSQL.table = "po_receipt_dist";
    oSQL.args = [globals.org_id, uPoRecId.toString(), sDistType];
    
    oSQL.sql = "SELECT porecdist_id \
                FROM po_receipt_dist \
                INNER JOIN gl_account ON po_receipt_dist.glacct_id = gl_account.glacct_id \
                WHERE (po_receipt_dist.org_id = ?) AND (porec_id = ? AND porecdist_dist_type = ?)\
                ";
    
    /**@type {JSFoundset<db:/avanti/po_receipt_dist>} */
    var fsReceiptDist = globals["avUtilities_sqlFoundset"](oSQL);
    
    if (fsReceiptDist && fsReceiptDist.getSize() > 0){
        return fsReceiptDist.getRecord(1);
    }
    
    return null;
}

/**
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"ACF73574-4813-4574-B735-852D0F4A6DA1"}
 */
function dc_cancel(_event, _triggerForm) {
    if (globals.nav.mode == "add" && ap_invoice_num == null && !utils.hasRecords(ap_invoice_to_ap_invoice_detail)) {
        _super.dc_delete(_event, _triggerForm, true);
        _super.dc_cancel(_event, _triggerForm, i18n.getI18NMessage('svy.fr.lbl.ok'));
    }
    else{
        _super.dc_cancel(_event, _triggerForm);
        forms.ap_invoice_dist_tbl.refreshUI();
    }
}
/**
 * @param _event
 * @param _triggerForm
 *
 * @return
 *
 * @properties={typeid:24,uuid:"A576035B-3B79-4221-B30B-DA73A7FC4ED5"}
 */
function dc_edit(_event, _triggerForm) {
    var result = _super.dc_edit(_event, _triggerForm);
    
    forms.ap_invoice_dist_tbl.setEditingState();
    forms.ap_invoice_dtl.lockSupplierField();
    forms.ap_invoice_dtl.refreshUI();
    
    return result;
}
/**
 * Set toolbar options
 *
 * @properties={typeid:24,uuid:"F2A0142B-CDAC-427C-B696-E36E6ECBD63E"}
 */
function setToolBarOptions() {
    
    forms.svy_nav_fr_buttonbar_browser.elements.btn_duplicate.enabled = false;
    
    if (globals.nav.mode == "browse") {
        if (utils.hasRecords(foundset) && ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.Posted 
        		|| ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.Reviewed) {
            forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
            forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
        }
        else {
            forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
            forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
        }
    }
}
