customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_price_rule_detail",
extendsID:"4C4DE663-EBC6-4D9C-8289-F7376002C0BF",
items:[
{
cssPosition:"257,5,5,-1,363,239",
json:{
cssPosition:{
bottom:"5",
height:"239",
left:"-1",
right:"5",
top:"257",
width:"363"
},
tabs:[
{
containedForm:"78C26E1D-D518-4027-9C66-239C000A88EC",
name:"qtyBreaks",
relationName:"sa_price_rule_detail_to_sa_price_rule_breaks",
svyUUID:"907A3F47-F54D-453A-AB4E-FF6449354495",
text:"Quantity Breaks"
}
]
},
name:"tabQuantityBreaks",
typeName:"bootstrapcomponents-tabpanel",
typeid:47,
uuid:"231BC2E2-D959-4678-B331-FC4DA4E5A836"
},
{
height:252,
partType:5,
typeid:19,
uuid:"2CE77ACF-3BAB-42C6-92A5-478E06FF9263"
},
{
cssPosition:"319,-1,5,0,619,176",
json:{
containedForm:"87F50F38-EECF-4EE1-AE71-3D30FE9746EA",
cssPosition:{
bottom:"5",
height:"176",
left:"0",
right:"-1",
top:"319",
width:"619"
},
relationName:"sa_price_rule_detail$foundset",
visible:true
},
name:"tabActualPrice",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"30FD90BD-8918-4B08-861F-B13FD542AA0D"
},
{
cssPosition:"-1,-1,215,5,140,22",
json:{
cssPosition:{
bottom:"215",
height:"22",
left:"5",
right:"-1",
top:"-1",
width:"140"
},
enabled:true,
labelFor:"priceruledtl_price_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.priceMethod",
visible:true
},
name:"priceruledtl_price_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"354D8C82-5D45-486C-A8F0-9FC075795294"
},
{
cssPosition:"319,-1,5,0,619,176",
json:{
containedForm:"C7182F71-D5B2-4BE9-B354-7D488B8E954A",
cssPosition:{
bottom:"5",
height:"176",
left:"0",
right:"-1",
top:"319",
width:"619"
},
relationName:"sa_price_rule_detail$foundset",
visible:true
},
name:"tabCostPlus",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"5B5F0F34-DECF-4543-AC7D-85085CDCDCB6"
},
{
cssPosition:"-1,-1,188,149,161,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"188",
height:"22",
left:"149",
right:"-1",
top:"-1",
width:"161"
},
dataProviderID:"priceruledtl_break_method",
enabled:true,
onDataChangeMethodID:"4DE52338-C287-47BF-8987-A81EF85F7292",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"4561B4BD-D6AB-4926-B4DA-3C5E52C36F0C",
visible:true
},
name:"priceruledtl_break_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6C3DA18C-0DA9-4C8A-916D-5687493FA085"
},
{
cssPosition:"0,0,248,0,991,252",
json:{
containedForm:"75878153-0B0D-4133-8DDB-0C676E3DE99A",
cssPosition:{
bottom:"248",
height:"252",
left:"0",
right:"0",
top:"0",
width:"991"
},
relationName:"sa_price_rule_detail$foundset",
visible:true
},
name:"tab1",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"6CB18CA6-7050-4F6B-B0A9-07ECC757A3FA"
},
{
cssPosition:"-1,372,5,0,619,176",
json:{
containedForm:"9A668F9B-1575-43CE-B640-BA96A958F304",
cssPosition:{
bottom:"5",
height:"176",
left:"0",
right:"372",
top:"-1",
width:"619"
},
relationName:"sa_price_rule_detail$foundset",
visible:true
},
name:"tabActualPriceTasks",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"96BB1A9D-EC58-4F68-8AA7-87C6CE601774"
},
{
cssPosition:"-1,-1,186,5,140,24",
json:{
cssPosition:{
bottom:"186",
height:"24",
left:"5",
right:"-1",
top:"-1",
width:"140"
},
enabled:true,
labelFor:"priceruledtl_break_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.breakMethod",
visible:true
},
name:"priceruledtl_break_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A8444B17-82E3-4C22-8983-E7E88E42F8F3"
},
{
height:500,
partType:8,
typeid:19,
uuid:"E38B17BA-8F11-492D-ADE0-F00A3667B588"
},
{
cssPosition:"-1,-1,215,150,160,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"215",
height:"22",
left:"150",
right:"-1",
top:"-1",
width:"160"
},
dataProviderID:"priceruledtl_price_method",
enabled:true,
onDataChangeMethodID:"D8BFA261-4B70-47EE-AD01-BB34E7B78233",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:2,
valuelistID:"BF36A6F8-B4B8-44E4-8031-B051FBB2FC5E",
visible:true
},
name:"priceruledtl_price_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"EC449642-3C97-4504-965D-EC2FBEAD1DBF"
}
],
name:"sa_price_rule_detail_dtl",
scrollbars:33,
size:"991,500",
styleName:null,
typeid:3,
uuid:"A5E88E7E-6048-421D-BECE-1B2BCA7D91A5"