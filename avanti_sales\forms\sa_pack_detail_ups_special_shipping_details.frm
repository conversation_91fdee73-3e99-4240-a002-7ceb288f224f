customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_pack_ups_options",
extendsID:"A1EFC35D-7C05-4C29-A9CC-EA88B6E6138D",
items:[
{
cssPosition:"294,-1,-1,5,170,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"294",
width:"170"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_FedEx_EmailNotificationDetails",
visible:true
},
name:"ups_email_notification_label",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"089DBF9A-81AC-4193-9CDA-91AABBE0B43A"
},
{
cssPosition:"105,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"105",
width:"140"
},
enabled:true,
labelFor:"ups_declared_value",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.value",
visible:true
},
name:"ups_declared_value_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0B3CA4DC-ECD2-4021-8897-17710885C40C"
},
{
cssPosition:"494,-1,-1,159,400,44",
json:{
cssPosition:{
bottom:"-1",
height:"44",
left:"159",
right:"-1",
top:"494",
width:"400"
},
dataProviderID:"ups_email_recipients",
editable:true,
enabled:true,
styleClass:"textarea_bts",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.shippingMethod_FedEx_EmailRecipient_message",
visible:true
},
name:"ups_email_recipients",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"10528E18-2739-42A9-BCD1-4744A761E195"
},
{
cssPosition:"852,-1,-1,10,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"852",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.CommercialInvoice",
visible:true
},
name:"ups_return_shipment_labelc",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"13CD8A4A-0C61-46EB-B3E8-82223C8BA874"
},
{
cssPosition:"30,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"30",
width:"200"
},
dataProviderID:"ups_invoice_total_value",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_invoice_total_value",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"13E1A8CD-DA64-45F1-AC6A-5080A7854CE8"
},
{
cssPosition:"237,-1,-1,159,200,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"237",
width:"200"
},
dataProviderID:"ups_delivery_confirmation_type",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2607FA3C-734D-4FC9-A37E-67A07F7E25D7",
visible:true
},
name:"ups_delivery_type",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"17560463-F5EA-41AD-BABD-0E7E22E874AE"
},
{
cssPosition:"5,-1,-1,5,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"5",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_InvoiceTotalDetails",
visible:true
},
name:"ups_invoice_total_detail_label",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2BAADF18-62D0-422C-B9E5-FEF9AA6F302B"
},
{
cssPosition:"419,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"419",
width:"200"
},
dataProviderID:"ups_email_from_name",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_email_from_name",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"2C13A0CC-DE10-4166-A502-8AE5BBD14430"
},
{
cssPosition:"739,-1,-1,16,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"16",
right:"-1",
top:"739",
width:"140"
},
enabled:true,
labelFor:"third_party_country_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.country",
visible:true
},
name:"lbl_third_party_country_id",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"32014DC4-717B-4297-8782-F856F1E0244E"
},
{
cssPosition:"877,-1,-1,164,400,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"164",
right:"-1",
top:"877",
width:"400"
},
dataProviderID:"ups_terms_of_sale",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"E4D32C5D-06F3-4AF1-9F96-9C88C6D67796",
visible:true
},
name:"terms_of_sale",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"33AC1659-ED11-4BA2-BDAD-DFCF56BCFF31"
},
{
height:1028,
partType:5,
typeid:19,
uuid:"352B1A14-16E4-4CDE-8110-38828832369E"
},
{
cssPosition:"419,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"419",
width:"140"
},
enabled:true,
labelFor:"ups_email_from_name",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_EmailFromName",
visible:true
},
name:"ups_email_from_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"352F3985-A818-459C-961D-270A2CEA2F7A"
},
{
cssPosition:"30,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"30",
width:"140"
},
enabled:true,
labelFor:"ups_invoice_total_value",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.value",
visible:true
},
name:"ups_invoice_total_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3604637C-E3FC-4979-86EC-05112BC85170"
},
{
cssPosition:"469,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"469",
width:"140"
},
enabled:true,
labelFor:"ups_email_subject",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.subject",
visible:true
},
name:"ups_email_subejct_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"381B3255-E27C-48BD-9C3B-855DB3A0F533"
},
{
cssPosition:"212,-1,-1,5,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"212",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_DeliveryConfirmationDetails",
visible:true
},
name:"ups_delivery_confirmation_label",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3E2525BA-72A5-435A-9CB7-DD33E13B7FB1"
},
{
cssPosition:"80,-1,-1,5,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"80",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_DeclaredValueDetails",
visible:true
},
name:"ups_declared_value_detail_label",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3E576500-E4B1-4B67-A19D-267E89134EBA"
},
{
cssPosition:"155,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"155",
width:"140"
},
enabled:true,
labelFor:"ups_declared_value_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.type",
toolTipText:"The user cannot specify different type of declared value for the shipment. You can either have shipper declared value (DVS) or declared value (EVS) but not both at package level. \r\
Note: The Shipper Declared Value is applicable for forward shipments when the billing option is freight collect or third party",
visible:true
},
name:"ups_declared_value_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"43B73A96-686D-4932-A4B7-3538C2776CAE"
},
{
cssPosition:"651,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"651",
width:"140"
},
enabled:true,
labelFor:"ups_return_service_code",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.code",
visible:true
},
name:"ups_return_service_code_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"48E2F5FA-F45F-4886-916B-678E7201383E"
},
{
cssPosition:"444,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"444",
width:"140"
},
enabled:true,
labelFor:"ups_email_from_address",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_EmailFromAddress",
visible:true
},
name:"ups_email_from_address_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4CC5F224-E7B0-49AD-9CF1-D6E451EB17E8"
},
{
cssPosition:"180,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"180",
width:"200"
},
dataProviderID:"ups_declared_value_desc",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_declared_value_desc",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4E5AEA5B-84E7-4DC4-8968-717F2F61B8F7"
},
{
cssPosition:"130,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"130",
width:"140"
},
enabled:true,
labelFor:"ups_declared_value_currency",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.curr_id",
visible:true
},
name:"ups_declared_value_currency_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"54C3D59B-55C2-4DCF-9389-243A8D4EAAC9"
},
{
cssPosition:"739,-1,-1,160,400,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"160",
right:"-1",
top:"739",
width:"400"
},
dataProviderID:"third_party_country_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8EB00775-A6CD-43BF-96ED-E8E6951B8675",
visible:true
},
name:"third_party_country_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"56EAAA68-78D6-4EA2-800D-F8E8A1469050"
},
{
cssPosition:"626,-1,-1,5,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"626",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_FedEx_ReturnShipmentDetails",
visible:true
},
name:"ups_return_shipment_label",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6DA667C7-3EAD-4954-B807-0D1E3F8E46B3"
},
{
cssPosition:"543,-1,-1,159,400,75",
json:{
cssPosition:{
bottom:"-1",
height:"75",
left:"159",
right:"-1",
top:"543",
width:"400"
},
dataProviderID:"ups_email_memo",
editable:true,
enabled:true,
styleClass:"textarea_bts",
tabSeq:0,
visible:true
},
name:"ups_email_message",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"6F0321C4-22BF-4785-B44E-BBDE615FCBEF"
},
{
cssPosition:"494,-1,-1,15,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"15",
right:"-1",
top:"494",
width:"140"
},
enabled:true,
labelFor:"ups_email_recipients",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_FedEx_EmailRecipient",
toolTipText:"i18n:avanti.lbl.shippingMethod_FedEx_EmailRecipient_message",
visible:true
},
name:"ups_email_recipients_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7A1913D6-D35B-4BF6-AF5E-3C3D8910FC3F"
},
{
cssPosition:"928,-1,-1,20,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"20",
right:"-1",
top:"928",
width:"140"
},
enabled:true,
labelFor:"declaration_statement",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.DeclarationStatement",
visible:true
},
name:"lbl_declaration_statement",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"859655EB-9940-41C6-B812-77EBA6801BA8"
},
{
cssPosition:"55,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"55",
width:"140"
},
enabled:true,
labelFor:"ups_invoice_total_currency",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.curr_id",
visible:true
},
name:"ups_invoice_total_currency_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8E1D352D-1060-40B9-8919-60714A5F67DF"
},
{
cssPosition:"262,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"262",
width:"140"
},
enabled:true,
labelFor:"ups_delivery_num",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_DCISNumber",
visible:true
},
name:"ups_delivery_num_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9726296F-787E-46AB-80DC-BB796E398957"
},
{
cssPosition:"262,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"262",
width:"200"
},
dataProviderID:"ups_delivery_confirmation_num",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_delivery_num",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"9FF47FBA-B42F-4EA6-964D-9C31FACDF881"
},
{
cssPosition:"543,-1,-1,15,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"15",
right:"-1",
top:"543",
width:"140"
},
enabled:true,
labelFor:"ups_email_message",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_FedEx_EmailMessage",
visible:true
},
name:"ups_email_message_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A236BC3F-AF02-45FA-90E3-2E0484578460"
},
{
cssPosition:"180,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"180",
width:"140"
},
enabled:true,
labelFor:"ups_declared_value_desc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Description",
visible:true
},
name:"ups_declared_value_desc_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A281B7AE-A4D2-4427-97C4-43013711E9A2"
},
{
cssPosition:"714,-1,-1,6,354,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"6",
right:"-1",
top:"714",
width:"354"
},
enabled:true,
styleClass:"bold label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.ThirdPartyAddress",
visible:true
},
name:"lblThirdPartyAddress",
styleClass:"bold label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ABE5843F-E9BD-4D80-BD7B-C1C3D55D13CF"
},
{
cssPosition:"319,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"319",
width:"140"
},
enabled:true,
labelFor:"ups_email_notification_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.type",
toolTipText:"For Mail Innovations forward shipments, QV Email Notifications are allowed for First Class, Priority Mail, and Expedited Mail Innovation services. ",
visible:true
},
name:"ups_email_notification_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AC670E68-0597-4EDA-A9E7-801EE9EDD4C6"
},
{
cssPosition:"676,-1,-1,159,400,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"676",
width:"400"
},
dataProviderID:"ups_return_service_desc",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_return_service_desc",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B16B0A32-D40A-4D6A-9B07-B2D24866D89D"
},
{
cssPosition:"764,-1,-1,16,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"16",
right:"-1",
top:"764",
width:"140"
},
enabled:true,
labelFor:"third_party_postal_code",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.zip/postalCode",
visible:true
},
name:"lbl_third_party_postal_code",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B39BE2C0-6B03-407C-8A55-21F70C0FB744"
},
{
cssPosition:"902,-1,-1,164,400,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"164",
right:"-1",
top:"902",
width:"400"
},
dataProviderID:"ups_reason_for_export",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"B012FE6C-7595-4A47-88E3-1C80E7FEFBFC",
visible:true
},
name:"reason_for_export",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"B8635600-F136-4B20-BA63-F190909C6562"
},
{
cssPosition:"155,-1,-1,159,200,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"155",
width:"200"
},
dataProviderID:"ups_declared_value_type",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"737F9E37-6E94-4DF8-8E1F-152292D1AC09",
visible:true
},
name:"ups_declared_value_type",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"BEA16C9B-1E6A-4B08-815B-D46C2AE46FB5"
},
{
cssPosition:"469,-1,-1,159,400,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"469",
width:"400"
},
dataProviderID:"ups_email_subject",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_email_subject",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BF3EF2AC-C302-4941-BF60-00777BADA5FC"
},
{
cssPosition:"651,-1,-1,159,400,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"651",
width:"400"
},
dataProviderID:"ups_return_service_code",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"593FB478-80E8-478C-A10E-0C7ECF9E31B7",
visible:true
},
name:"ups_return_service_code",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"C1B25E61-826A-408B-8339-EC89DD7DE54C"
},
{
cssPosition:"928,-1,-1,164,400,90",
json:{
cssPosition:{
bottom:"-1",
height:"90",
left:"164",
right:"-1",
top:"928",
width:"400"
},
dataProviderID:"ups_declaration_statement",
editable:true,
enabled:true,
styleClass:"textarea_bts",
tabSeq:0,
visible:true
},
name:"declaration_statement",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"D4744307-B397-473E-B276-B5271209C497"
},
{
cssPosition:"811,-1,-1,159,400,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"811",
width:"400"
},
dataProviderID:"ups_transportation_mode",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"280EF7AE-DA0B-4A9C-A232-FDC7D5B1E9C9",
visible:true
},
name:"ups_transportation_mode",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D914A682-E999-4338-AE44-D3310FB5707D"
},
{
cssPosition:"55,-1,-1,159,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"55",
width:"140"
},
dataProviderID:"ups_invoice_total_currency",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"205EEF07-96A8-4922-BB53-FD2381C9B4DB",
visible:true
},
name:"ups_invoice_total_currency",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"DAAD23D0-123E-4486-8908-4CCD4F7678D0"
},
{
cssPosition:"444,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"444",
width:"200"
},
dataProviderID:"ups_email_from_address",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_email_from_address",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"DCCB81A4-8AB7-40BE-A527-A25B16196624"
},
{
cssPosition:"237,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"237",
width:"140"
},
enabled:true,
labelFor:"ups_delivery_type",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.type",
visible:true
},
name:"ups_delivery_type_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E0795265-E4DB-4DA2-880E-AB5F4E5E1A8D"
},
{
cssPosition:"105,-1,-1,159,200,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"105",
width:"200"
},
dataProviderID:"ups_declared_value",
editable:true,
enabled:true,
onDataChangeMethodID:"0C401765-1745-49CB-BD87-C3289738E831",
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"ups_declared_value",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E08A9741-C427-4123-9F07-E7580D7F0D9D"
},
{
cssPosition:"902,-1,-1,20,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"20",
right:"-1",
top:"902",
width:"140"
},
enabled:true,
labelFor:"reason_for_export",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.ReasonForExport",
visible:true
},
name:"lbl_reason_for_export",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E8C900C4-A57E-4DBF-9EDA-D08F28D2F8AE"
},
{
cssPosition:"317,-1,-1,159,200,101",
json:{
cssPosition:{
bottom:"-1",
height:"101",
left:"159",
right:"-1",
top:"317",
width:"200"
},
dataProviderID:"ups_email_notification_code",
enabled:true,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"1BE32F2B-CB15-4F41-8BB3-4354CD0F9E9A",
visible:true
},
name:"ups_email_notification_type",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"F1529CA7-6082-4B49-BCFB-076BCC02ECE2"
},
{
cssPosition:"130,-1,-1,159,140,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"159",
right:"-1",
top:"130",
width:"140"
},
dataProviderID:"ups_declared_value_currency",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"205EEF07-96A8-4922-BB53-FD2381C9B4DB",
visible:true
},
name:"ups_declared_value_currency",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F1C6C9D5-9F3F-44F1-A157-51830056E088"
},
{
cssPosition:"811,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"811",
width:"140"
},
enabled:true,
labelFor:"ups_transportation_mode",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.TransportationMode",
visible:true
},
name:"lbl_ups_transportation_mode",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F5D07FCA-FC01-4BF5-9E54-41B08587E0A9"
},
{
cssPosition:"764,-1,-1,160,400,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"160",
right:"-1",
top:"764",
width:"400"
},
dataProviderID:"third_party_postal_code",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:true
},
name:"third_party_postal_code",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"FA4226EC-E694-4BB0-98B6-5354853BF26B"
},
{
cssPosition:"676,-1,-1,15,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"15",
right:"-1",
top:"676",
width:"140"
},
enabled:true,
labelFor:"ups_return_service_desc",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Description",
visible:true
},
name:"ups_return_service_desc_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FAD4B79F-98F8-4FE4-AF47-40D3B900A307"
},
{
cssPosition:"877,-1,-1,20,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"20",
right:"-1",
top:"877",
width:"140"
},
enabled:true,
labelFor:"terms_of_sale",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shippingMethod_UPS_TermsOfSale",
visible:true
},
name:"lbl_terms_of_sale",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FC7FBB81-B60F-4749-8D46-0612085EC600"
}
],
name:"sa_pack_detail_ups_special_shipping_details",
onShowMethodID:"DA23AD91-77AE-4D81-8C9D-CE82C65CB237",
scrollbars:33,
size:"577,633",
styleName:null,
typeid:3,
uuid:"8AB200AF-28F8-405B-A78A-681BE5F6F963",
view:0