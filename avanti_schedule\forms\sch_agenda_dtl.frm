customProperties:"methods:{\
onRenderMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sch_view",
extendsID:"961E871C-FAF1-4A34-AE99-B2CFCB1F0157",
items:[
{
cssPosition:"70,-1,-1,421,140,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"421",
right:"-1",
top:"70",
width:"140"
},
dataProviderID:"nConvertType",
editable:true,
enabled:true,
selectOnEnter:false,
styleClass:"textbox_bts",
tabSeq:0,
visible:false
},
name:"nConvertType",
styleClass:"textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"01C12C7B-9F77-4C8E-977D-7DA81ECEF433",
visible:false
},
{
cssPosition:"-1,-1,38,10,225,22",
formIndex:3,
json:{
appendToBody:true,
cssPosition:{
bottom:"38",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"225"
},
dataProviderID:"_emplID",
enabled:true,
formIndex:3,
onDataChangeMethodID:"F4CFE41D-AE69-4822-A1EC-EDE37B55E243",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"4D48723D-42D5-4EB8-90DB-BEE9F959325B",
visible:true
},
name:"fldEmplID",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"0465F025-0858-4292-979C-DACC3EA457CD"
},
{
cssPosition:"0,-1,-1,0,18,17",
json:{
cssPosition:{
bottom:"-1",
height:"17",
left:"0",
right:"-1",
top:"0",
width:"18"
},
dataProviderID:"globals.avSchedule_html",
editable:false,
enabled:true,
styleClass:"textarea_bts",
tabSeq:0,
visible:false
},
name:"html",
styleClass:"textarea_bts",
typeName:"bootstrapcomponents-textarea",
typeid:47,
uuid:"159573E5-7D39-4E09-9407-290DFCF27EA8",
visible:false
},
{
cssPosition:"-1,-1,114,10,145,22",
formIndex:4,
json:{
cssPosition:{
bottom:"114",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"145"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.equipment",
visible:true
},
name:"component_22C01E7A",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"1D72D694-9EEA-4F96-ABDE-3996C1425DF6"
},
{
cssPosition:"35,-1,-1,403,1,48",
json:{
cssPosition:{
bottom:"-1",
height:"48",
left:"403",
right:"-1",
top:"35",
width:"1"
},
enabled:true,
styleClass:"label_bts line black text-right",
tabSeq:-1,
visible:true
},
name:"component_7473EECA",
styleClass:"label_bts line black text-right",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"25F111E0-2B80-4501-9FA6-CC8E61AC9E07"
},
{
cssPosition:"-1,-1,164,10,145,22",
formIndex:1,
json:{
cssPosition:{
bottom:"164",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"145"
},
enabled:true,
formIndex:1,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dept",
visible:true
},
name:"component_0FF24DA9",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2653199F-9E1C-4587-BD2D-CF2403F398AD"
},
{
cssPosition:"5,-1,-1,1084,119,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"1084",
right:"-1",
top:"5",
width:"119"
},
dataProviderID:"_zoomFactor",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"B5103D4C-271C-4756-B740-E24186B131D9",
visible:false
},
name:"component_44239813",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"288A70FC-4C8F-4B8A-8C38-F87957DE82A7",
visible:false
},
{
cssPosition:"5,15,-1,0,1208,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"15",
top:"5",
width:"1208"
},
enabled:true,
onDoubleClickMethodID:"2A7A5E48-77C8-46F4-ABE2-D7470D333292",
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.scheduleBoard",
visible:true
},
name:"component_6F06A40A",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2A066B2D-626D-4774-9333-6F9AA3B25233"
},
{
height:660,
partType:5,
typeid:19,
uuid:"38744ECD-7A17-4615-9AB3-07EF29FDDAB1"
},
{
cssPosition:"-1,-1,188,10,145,22",
formIndex:6,
json:{
cssPosition:{
bottom:"188",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"145"
},
enabled:true,
formIndex:6,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.editResource",
visible:true
},
name:"component_4576539F",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3B9C7024-E9ED-41BD-AD06-F6743D19B269"
},
{
cssPosition:"48,-1,-1,245,153,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"245",
right:"-1",
top:"48",
width:"153"
},
enabled:true,
onActionMethodID:"150A1145-84E7-4444-9461-3A2314EB2480",
onRightClickMethodID:null,
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.optimizeSchedule",
visible:true
},
name:"component_1FA40E3B",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"447BAB6C-1517-4B08-906B-77360AFE13AB"
},
{
cssPosition:"-1,-1,27,5,235,189",
json:{
cssPosition:{
bottom:"27",
height:"189",
left:"5",
right:"-1",
top:"-1",
width:"235"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_5F17AABC",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"579223FF-EEA6-4114-8D25-EF2F86B866E8"
},
{
cssPosition:"35,-1,223,5,235,402",
json:{
containedForm:"75559025-F943-444A-8067-9147CD30AF4F",
cssPosition:{
bottom:"223",
height:"402",
left:"5",
right:"-1",
top:"35",
width:"235"
},
relationName:"_to_sch_view$foundset",
visible:true
},
name:"tabsSchedule",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"636EED2B-F068-4318-A7F4-07199A2132EE"
},
{
cssPosition:"48,-1,-1,1006,175,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"1006",
right:"-1",
top:"48",
width:"175"
},
dataProviderID:"_nDebugLoadAllData",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"Load All Data",
toolTipText:"This is for debugging puposes. When checked it will load all data for a view, rather than by tab/date.",
visible:false
},
name:"chkLoadAllData",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"66078DCA-54C1-4E4E-B451-7008E1EB0379",
visible:false
},
{
cssPosition:"48,-1,-1,748,175,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"748",
right:"-1",
top:"48",
width:"175"
},
dataProviderID:"_pullSuccessors",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.pullSuccessorsBack",
toolTipText:"i18n:avanti.lbl.pullSuccessors",
visible:true
},
name:"component_EBB56A76",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"6679BA5F-A22B-4602-A858-715DB1095C40"
},
{
cssPosition:"5,-1,-1,953,127,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"953",
right:"-1",
top:"5",
width:"127"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.timeScale",
visible:false
},
name:"component_04A07368",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6F5FE388-D11E-4DEC-9118-6A5E85FFFACE",
visible:false
},
{
cssPosition:"48,-1,-1,957,22,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"957",
right:"-1",
top:"48",
width:"22"
},
enabled:true,
onActionMethodID:"BEA17E6D-7B71-46BD-BE59-5AA506BC2D47",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_refresh%%",
visible:true
},
name:"btnRefresh",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9791F4DB-6430-426A-8D2E-28D499DF6F59"
},
{
cssPosition:"-1,-1,62,10,145,22",
formIndex:7,
json:{
cssPosition:{
bottom:"62",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"145"
},
enabled:true,
formIndex:7,
labelFor:"fldEmplID",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.employee",
visible:true
},
name:"component_86E8B250",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B0699D73-C182-4423-BC6B-7F0B2CFA6C0C"
},
{
cssPosition:"48,-1,-1,566,177,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"566",
right:"-1",
top:"48",
width:"177"
},
dataProviderID:"_pullPredecessors",
enabled:true,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.pullPredecessorsForward",
toolTipText:"i18n:avanti.lbl.pullPredecessors",
visible:true
},
name:"component_A6F9E5C3",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"DB20AEC4-E7A9-45A3-9A44-8DF4EC5275B5"
},
{
cssPosition:"-1,-1,94,10,225,22",
formIndex:5,
json:{
appendToBody:true,
cssPosition:{
bottom:"94",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"225"
},
dataProviderID:"_equipID",
enabled:true,
formIndex:5,
onDataChangeMethodID:"C0DEDA25-**************-897FD517A023",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"06DEE827-413C-4BDE-9979-86719F89A39B",
visible:true
},
name:"fldEquipID",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"DDD592F5-B12A-4616-8BD2-0665758AC697"
},
{
cssPosition:"48,-1,-1,419,145,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"419",
right:"-1",
top:"48",
width:"145"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avant.lbl.dragDropOptions",
visible:true
},
name:"component_1841FDFF",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E52A2492-ACB1-4FB4-A7D5-916CD98778EC"
},
{
cssPosition:"-1,-1,144,10,225,22",
formIndex:2,
json:{
appendToBody:true,
cssPosition:{
bottom:"144",
height:"22",
left:"10",
right:"-1",
top:"-1",
width:"225"
},
dataProviderID:"globals.avSchedule_selectedViewDept",
enabled:true,
formIndex:2,
onDataChangeMethodID:"DEAEFB30-5B86-4FDD-8E8F-6B937EC3DD3E",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"C1A960C2-11F4-47AB-BE6C-A7AC0D43B324",
visible:true
},
name:"fldDeptID",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F1B0E6F1-38EE-4179-9C04-84F6D4D22314"
},
{
anchors:15,
cssPosition:"95,3,4,243,962,540",
json:{
anchors:15,
cssPosition:{
bottom:"4",
height:"540",
left:"243",
right:"3",
top:"95",
width:"962"
},
location:{
x:245,
y:100
},
onDateClickMethodID:null,
onDatesSetMethodID:"0395B69F-657E-4D39-85DB-907278B3347C",
onDropMethodID:null,
onEventChangeMethodID:"F741A8FD-D96E-4483-835D-9304CCE71D97",
onEventClickMethodID:null,
onEventDblClickMethodID:"6E04D719-2AA0-4C22-8907-87F2609B0F09",
onEventDragStartMethodID:null,
onEventDropMethodID:"3751440C-C335-4112-84CA-FE40282F8652",
onEventMouseEnterMethodID:"CF0D13B8-7030-4540-A1AC-7EE74209BBB5",
onEventMouseLeaveMethodID:"5BE401C7-08C5-4442-B337-49A66822E3F7",
onEventResizeStartMethodID:"74599920-4D13-4ADB-8DD8-57BC6EE6B359",
onEventRightClickMethodID:"BCF028EB-9CC2-4010-B3D7-D22777DD2EA9",
onNavLinkDayClickMethodID:"BDEC1879-5042-4CAA-A0BF-514D5E02FC7C",
size:{
height:533,
width:963
}
},
location:"245,100",
name:"calendar",
size:"963,533",
typeName:"svy-fullcalendar2",
typeid:47,
uuid:"F6BE8BB1-1DCF-42E6-B35B-CA5A4EB0AEB1"
}
],
name:"sch_agenda_dtl",
onHideMethodID:"8368BF7B-5775-4BCE-A1BC-B44B3C2C89D8",
onRenderMethodID:"-1",
onShowMethodID:"0CFD7B4D-0E99-40DF-8077-7AA886F8FE54",
onUnLoadMethodID:"31F06E30-3A0D-4435-A311-BB0900AE67AB",
scrollbars:33,
size:"1223,660",
styleName:null,
typeid:3,
uuid:"215E8775-9BA2-4569-981A-3E1DE2D75DE4"