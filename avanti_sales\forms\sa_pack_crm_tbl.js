/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"FA3CB9C2-4786-4155-A875-6A60447A0D46",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"71817A8A-1394-491F-80E3-158C36CFE1CC"}
 */
function onReady() {
    _gridReady = 1;
}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"02597C11-21F0-4370-A64A-0B81191098D0"}
 */
function onShowForm(_firstShow, _event) {

	if (_firstShow) {
		if (!_gridReady) {
			application.executeLater(onShowForm, 500, [true, _event]);
			return null;
		}
	}

	_super.onShowForm(_firstShow, _event)

	elements.grid.getColumn(elements.grid.getColumnIndex("pack_ship_date")).format = globals.avBase_dateFormat;

	refreshUI();
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2E342FA4-8A3E-40F7-88A3-E2F4450BC08A"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0") {
		onAction_ShowPo(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"9D06A2F7-1E18-4CC1-8646-12DD8AD0C8F2"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	onAction_ShowPo(event)
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4E7BF797-4F78-4003-81F9-144CDC18F374"}
 */
function onDataChange_StatusFilter(oldValue, newValue, event) {

	refreshUI();

	return true
}

/**
 * refreshUI
 *
 *
 * @properties={typeid:24,uuid:"2CA86733-E140-43AE-843A-D330BC5C535F"}
 */
function refreshUI() {
	elements.grid.getColumn(elements.grid.getColumnIndex("pack_status")).visible = true;
	controller.readOnly = true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2BC44E00-0A82-46B4-B484-E7286D7A04F1"}
 */
function onAction_ClearFilter(event) {
	switch (event.getElementName()) {
	case "btnClearStatusFilter":
		_StatusFilter = null;
		break;

	}

}

/**
 * @public
 * @param {UUID} [uJobID]
 * @param {UUID} [uOrderID]
 *
 * @properties={typeid:24,uuid:"75E9F3C5-1636-4F83-955F-24766194BFDB"}
 */
function loadFoundset(uJobID, uOrderID) {

	if (globals.nav_program_name.toLowerCase() == "crm_order_manager_view" && !uOrderID) {
		foundset.clear();
		return;
	}
	else if (globals.nav_program_name.toLowerCase() != "crm_order_manager_view" && !uJobID) {
		foundset.clear();
		return;
	}

	/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
	var oSQL = { };

	if (globals.nav_program_name.toLowerCase() == "crm_order_manager_view") {
		oSQL.sql = "SELECT DISTINCT sa_pack.pack_id \
			  		  FROM sa_pack \
			  	INNER JOIN sa_pack_detail ON sa_pack.pack_id = sa_pack_detail.pack_id \
			  	INNER JOIN sa_ship ON sa_pack_detail.ship_id = sa_ship.ship_id \
			  	INNER JOIN sa_order_revision_header ON sa_ship.ordrevh_id = sa_order_revision_header.ordrevh_id \
			  		 WHERE sa_order_revision_header.ordh_id = ?";
		oSQL.args = [uOrderID.toString()];
	}
	else {
		oSQL.sql = "SELECT DISTINCT sa_pack.pack_id \
					  FROM sa_pack \
				INNER JOIN sa_pack_detail ON sa_pack.pack_id = sa_pack_detail.pack_id \
				INNER JOIN sa_ship ON sa_pack_detail.ship_id = sa_ship.ship_id \
				INNER JOIN sa_order_revision_header ON sa_ship.ordrevh_id = sa_order_revision_header.ordrevh_id \
				INNER JOIN sa_order ON sa_order_revision_header.ordh_id = sa_order.ordh_id \
				INNER JOIN prod_job ON sa_order.ordh_id = prod_job.ordh_id \
					 WHERE prod_job.job_id = ?";
		oSQL.args = [uJobID.toString()];
	}
	oSQL.table = "sa_pack";

	/***@type {JSFoundSet<db:/avanti/sa_pack>} ***/
	var fsPackingSlips = globals["avUtilities_sqlFoundset"](oSQL);
	
	controller.loadRecords(fsPackingSlips);
	refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @protected
 *
 * @properties={typeid:24,uuid:"CFEC3866-3A1E-4C11-9C67-A2A8A4E799ED"}
 */
function onAction_ShowPo(event) {
	forms.sa_pack_dtl.controller.loadRecords(foundset);
	scopes.globals.svy_nav_toggleView(event, "sa_pack_dtl", null, true);
}
