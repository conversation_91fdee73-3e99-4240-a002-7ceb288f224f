/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"6D72A183-1BCC-4489-8739-6FBC3FBB703C"}
 */
function dc_new(_event, _triggerForm) {
	foundset.org_id = globals.org_id
	return _super.dc_new(_event, _triggerForm)
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"57D8B0F2-1104-4CCD-A610-398020BF0D02"}
*/
function dc_save_new(_event) {
	return _super.dc_save_new(_event)
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"DE489BE0-81FF-4270-B6FF-ACB516108B3F"}
*/
function dc_save(_event, _triggerForm) {
	var bSaveResult = false;

	if(!foundset.org_id){
		foundset.org_id = globals.org_id
	}
	
	if(validateRequiredFields()){
		bSaveResult = _super.dc_save(_event, _triggerForm);
	}
	
	return bSaveResult;
}

/**
 *
 * @param {JSEvent} event
 *
 * @return
 * @properties={typeid:24,uuid:"6B9E2932-11CC-462A-880B-7DEBAE133819"}
 */
function onLoad(event) {
	popFilePlacementOptionsVL()
	return _super.onLoad(event)
}

/**
 * @properties={typeid:24,uuid:"5B4115F6-C569-4BB4-B436-FAC5D85AE9CC"}
 */
function updatePayrollFieldCBTableColNames(){
	/***@type {JSFoundset<db:/avanti/sys_payroll_file>} */
	var fs_files = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_payroll_file')
	fs_files.loadAllRecords()
	
	for(var i=1;i<=fs_files.getSize();i++){
		var rec_file = fs_files.getRecord(i)
		
		var sql = 'select max(prff_pr_data_table_field_num) ' +
		  		  'from sys_payroll_file_field sys ' +
				  'inner join app_avail_payroll_fields app on app.aprf_id = sys.aprf_id ' +
				  'where sys.prf_id = ? and (app.aprf_data_type = ? or app.aprf_data_type = ?)'
		
		var last_char_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.prf_id), 'char', 'char'])
		if(!last_char_field_num){
			last_char_field_num=0
		}
		
		var last_num_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.prf_id), 'number', 'currency'])
		if(!last_num_field_num){
			last_num_field_num=0
		}
		
		var last_date_field_num = scopes.globals.Query(sql, true, [globals.UUIDtoStringNew(fs_files.prf_id), 'date', 'date'])
		if(!last_date_field_num){
			last_date_field_num=0
		}		
		
		if(utils.hasRecords(rec_file.sys_payroll_file_to_sys_payroll_file_field)){
			var numFields = rec_file.sys_payroll_file_to_sys_payroll_file_field.getSize()
			
			for(var j=1;j<=numFields;j++){
				var rec_field = rec_file.sys_payroll_file_to_sys_payroll_file_field.getRecord(j)
				
				if(!rec_field.prff_pr_data_table_field_num && utils.hasRecords(rec_field.sys_payroll_file_field_to_app_avail_payroll_fields)){
					var dataType = rec_field.sys_payroll_file_field_to_app_avail_payroll_fields.aprf_data_type 
					
					if(dataType == 'char'){
						last_char_field_num++
						rec_field.prff_pr_data_table_field_num = last_char_field_num
						rec_field.prff_pr_data_table_field_name = 'char_' + last_char_field_num
					}
					else if(dataType == 'date'){
						last_date_field_num++
						rec_field.prff_pr_data_table_field_num = last_date_field_num
						rec_field.prff_pr_data_table_field_name = 'date_time_' + last_char_field_num
					}
					else if(dataType == 'number' || dataType == 'currency'){
						last_num_field_num++
						rec_field.prff_pr_data_table_field_num = last_num_field_num
						rec_field.prff_pr_data_table_field_name = 'number_' + last_char_field_num
					}
					
					databaseManager.saveData(rec_field)
				}
			}
		}
	}	
}

/**
 * @properties={typeid:24,uuid:"2E6B1DDE-4BB6-4A54-9CE1-503DAD8B107A"}
 */
function popFilePlacementOptionsVL() {
	// hide not hotfolder options for now
//	application.setValueListItems('vl_filePlacementOptions', ['Hotfolder', 'Download', 'FTP'], [i18n.getI18NMessage('avanti.lbl.hotfolder'), i18n.getI18NMessage('avanti.lbl.download'), i18n.getI18NMessage('avanti.lbl.ftp')]);	
	application.setValueListItems('vl_filePlacementOptions', ['Hotfolder', 'Download'], [i18n.getI18NMessage('avanti.lbl.hotfolder'), i18n.getI18NMessage('avanti.lbl.download')]);	
}

/**
 * @return
 * @properties={typeid:24,uuid:"3F48BF61-ED3A-4304-9722-C30BDC7DEB09"}
 */
function onSelectFilePath()
{
	var fileDir = plugins.file.showDirectorySelectDialog();

	if(fileDir!=null){
		return fileDir.getAbsolutePath();
	}else {
		return '';
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"4899CA44-DDCA-40D6-ABA4-019234E8D738"}
 */
function btnChooseFolder_onAction(event) {
	var filePath = onSelectFilePath()
	if(filePath != ''){
		foundset.prf_output_folder = filePath
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"57B17C89-E6E1-43EC-9205-DC3EF247E0F2"}
 */
function onRecordSelection(_event, _form) {
	// onRecordSelection() wasn't firing on server - put loadAvantiFieldNamesVL() call somewhere else
//	loadAvantiFieldNamesVL();
	return _super.onRecordSelection(_event, _form);
}

/**
 * @properties={typeid:24,uuid:"AF9AF306-350D-4B25-8CDC-4CA481715196"}
 */
function loadAvantiFieldNamesVL(){
 	/***@type {JSFoundset<db:/avanti/app_avail_payroll_fields>} */
 	var fsField = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'app_avail_payroll_fields')
	fsField.loadAllRecords()
	fsField.sort('aprf_field_name asc')
	var vlRealValues = new Array();
 	var vlDisplayValues = new Array();
	var vlRealValuesCond = new Array();
 	var vlDisplayValuesCond = new Array();

	for (var i = 1; i <= fsField.getSize(); i++){
 		var rField = fsField.getRecord(i);
 		var bAddThisField = true;

 		// sl-6852 - filter avanti fields based on detail level
 		if(rField.aprf_detail_level == 'T' && prf_detail_level != 'T'){
 			bAddThisField = false;
 		}
 		if(rField.aprf_detail_level == 'E' && prf_detail_level != 'E'){
 			bAddThisField = false;
 		}

 		if(bAddThisField){
 			vlDisplayValues.push(rField.aprf_field_name)
			vlRealValues.push(rField.aprf_id)

 			vlDisplayValuesCond.push(rField.aprf_field_name)
			vlRealValuesCond.push(rField.aprf_id)
 		}
 	}
	
 	application.setValueListItems('vl_payroll_field_names', vlDisplayValues, vlRealValues);
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D2C93989-E477-4CEB-BBEF-5B73DF4489AB"}
 */
function onDataChange_detailLevel(oldValue, newValue, event) {
	loadAvantiFieldNamesVL();
	
	return true;
}

/**
 *
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 *
 * @return
 * @properties={typeid:24,uuid:"1DF1E18D-9FBF-42B1-9BC4-528EE02F3F52"}
 */
function onShowForm(_firstShow, _event) {
	loadAvantiFieldNamesVL();
	return _super.onShowForm(_firstShow, _event)
}

/**
 *
 * @param {JSEvent} _event
 *
 * @return
 * @properties={typeid:24,uuid:"FE92DE3B-64BE-44BF-9A58-80520D873C31"}
 */
function dc_rec_first(_event) {
	var r = _super.dc_rec_first(_event);
	loadAvantiFieldNamesVL();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"5F8DDEC0-9050-4EB2-8D45-6B7DDC69BC72"}
*/
function dc_rec_last(_event) {
	var r = _super.dc_rec_last(_event)
	loadAvantiFieldNamesVL();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"946422D3-7C7D-419D-A132-C475B5C9745C"}
*/
function dc_rec_next(_event) {
	var r = _super.dc_rec_next(_event)
	loadAvantiFieldNamesVL();
	return r;
}

/**
*
* @param {JSEvent} _event
*
 * @return
* @properties={typeid:24,uuid:"1A76D0C9-0EF6-4265-BAE4-5E6E64731B62"}
*/
function dc_rec_prev(_event) {
	var r = _super.dc_rec_prev(_event)
	loadAvantiFieldNamesVL();
	return r;
}

/**
 *
 * @param {JSFoundSet} _foundset
 * @param {String} _program
 *
 * @properties={typeid:24,uuid:"45FF9C6F-E89A-4294-9020-CEB2710E04AA"}
 */
function dc_save_validate(_foundset, _program) {
	_super.dc_save_validate(_foundset, _program);
}

/**
 * Validate that the required fields are populated before continuing with a save
 * 
 * @return {Boolean}
 * @properties={typeid:24,uuid:"C5E26766-38E6-4296-AA73-5EBB5F1BDD9E"}
 */
function validateRequiredFields() {
	var bContinueSave = true;
	if(bContinueSave){
		var sMessage = "";
		
		//prf_name "Name" is required 
		if(prf_name == null || utils.stringTrim(prf_name) == ""){
			bContinueSave = false;
			if(sMessage == ""){
				sMessage += i18n.getI18NMessage("avanti.dialog.nameRequiredField");
			} else {
				sMessage += "\n" + i18n.getI18NMessage("avanti.dialog.nameRequiredField");
			}
		}
		//prf_file_name "File Name" is required 
		if(prf_file_name == null || utils.stringTrim(prf_file_name) == ""){
			bContinueSave = false;			
			if(sMessage == ""){
				sMessage += i18n.getI18NMessage("avanti.dialog.fileNameRequiredField");
			} else {
				sMessage += "\n" + i18n.getI18NMessage("avanti.dialog.fileNameRequiredField");
			}
		}
		//prf_file_format "File Format" is required 
		if(prf_file_format == null || utils.stringTrim(prf_file_format) == ""){
			bContinueSave = false;
			if(sMessage == ""){
				sMessage += i18n.getI18NMessage("avanti.dialog.fileFormatRequiredField");
			} else {
				sMessage += "\n" + i18n.getI18NMessage("avanti.dialog.fileFormatRequiredField");
			}
		}
		
		if(!bContinueSave && sMessage != ""){
		    globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.missingRequiredField"),sMessage,i18n.getI18NMessage("avanti.dialog.ok"))
		}
	}
	
	return bContinueSave;
}
