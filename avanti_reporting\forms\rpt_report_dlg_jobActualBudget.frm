borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"15,-1,-1,549,250,112",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"549",
right:"-1",
top:"15",
width:"250"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"01CE6203-AEFA-4483-AAB9-EE7DFF5CC1B3"
},
{
height:200,
partType:5,
typeid:19,
uuid:"1175A91A-BB64-4079-8E2E-AC2BF906067F"
},
{
cssPosition:"137,-1,-1,421,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"421",
right:"-1",
top:"137",
width:"140"
},
enabled:true,
labelFor:"_toJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobNumber",
visible:true
},
name:"_toJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"14C5960C-15CF-4520-8AE3-0486200EC713"
},
{
cssPosition:"223,-1,-1,150,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"223",
width:"250"
},
dataProviderID:"_postedCosts",
enabled:true,
inputType:"radio",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"_postedCosts",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"3863FA0A-7CBE-4C63-9764-0CE7254E1A0B"
},
{
cssPosition:"137,-1,-1,151,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"151",
right:"-1",
top:"137",
width:"250"
},
dataProviderID:"_fromJobNr",
enabled:true,
onDataChangeMethodID:"7DFC07DC-8F2F-4D74-A2C3-92CBC03DEC14",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_fromJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"4169E96A-9D2F-42D7-BF11-1BE726740CEA"
},
{
cssPosition:"15,-1,-1,6,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"6",
right:"-1",
top:"15",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4F7CEF08-4A74-4E41-B0A8-CBC9F9E4328F"
},
{
cssPosition:"196,-1,-1,150,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"196",
width:"250"
},
dataProviderID:"_completedJobs",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"0B4B9E83-DC34-4141-92C8-00CC8134123F",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"_completedJobs",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"751F95A8-B720-4EA2-8510-6D343EDE7360"
},
{
cssPosition:"137,-1,-1,6,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"6",
right:"-1",
top:"137",
width:"140"
},
enabled:true,
labelFor:"_fromJobNumber",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobNumber",
visible:true
},
name:"_fromJobNumber_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7E5EF8A3-C67C-4B17-B7DA-08DD9C9BE4D8"
},
{
cssPosition:"15,-1,-1,151,250,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"151",
right:"-1",
top:"15",
width:"250"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"605D903E-BD36-4DD2-A819-67B61345743C",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"7F423848-3574-492A-97CD-BE446AA9E87E"
},
{
cssPosition:"166,-1,-1,420,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"420",
right:"-1",
top:"166",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.toJobStartDate",
visible:true
},
name:"_toJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8837D4C4-6DDA-4953-B2E8-1EA8B00BC493"
},
{
cssPosition:"168,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"168",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromJobStartDate",
visible:true
},
name:"_fromJobDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A534E4B3-1FBF-4D17-8D3C-C301D634B832"
},
{
cssPosition:"166,-1,-1,150,160,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"150",
right:"-1",
top:"166",
width:"160"
},
dataProviderID:"_dateFrom",
enabled:true,
onDataChangeMethodID:"AEC65207-C9D5-4286-852B-7ED13962A5F1",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"AF62E218-1A91-4E9D-BD7D-6435F83EB117"
},
{
cssPosition:"137,-1,-1,550,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"550",
right:"-1",
top:"137",
width:"250"
},
dataProviderID:"_toJobNr",
enabled:true,
onDataChangeMethodID:"13DB1E80-7BC7-499B-B17C-74643B3C66E2",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"7BE00F45-72D4-42CC-8257-0674356B8D8A",
visible:true
},
name:"_toJobNumber",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"B459B8FF-C556-43BB-9762-FACADB7B1253"
},
{
cssPosition:"15,-1,-1,414,138,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"414",
right:"-1",
top:"15",
width:"138"
},
enabled:true,
formIndex:30,
labelFor:"fromPlant",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CF6BF4E2-A209-4F3C-92A4-3BAF6C8A2229"
},
{
cssPosition:"223,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"223",
width:"140"
},
enabled:true,
labelFor:"_postedCosts",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.postedCostsOnly",
visible:true
},
name:"_postedCosts_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DAFE7F77-8A4F-4DD8-817B-F47BEBBC226C"
},
{
cssPosition:"196,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"196",
width:"140"
},
enabled:true,
labelFor:"_fromEquip",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.completedJobs",
visible:true
},
name:"_completedJobs_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E27B70DA-1F54-4EB5-B85D-5609611C0355"
},
{
cssPosition:"166,-1,-1,550,160,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"550",
right:"-1",
top:"166",
width:"160"
},
dataProviderID:"_dateTo",
enabled:true,
onDataChangeMethodID:"700D30CC-94C5-44DD-8D52-685E73855D2A",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"EEB082E6-92AF-45C4-A26D-E6AFF3F1067D"
}
],
name:"rpt_report_dlg_jobActualBudget",
navigatorID:"-2",
onShowMethodID:"C8CB24A7-74AB-4628-A476-3EF7D1C44230",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"645F49A5-F62D-4790-9960-0CC4C10E2E2D"