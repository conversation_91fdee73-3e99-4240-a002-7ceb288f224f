/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"D48C267C-D434-4209-A13D-834AF5611312"}
 */
function onAction_New(event)
{
	if ( !scopes.avUtils.isGivenNavModeReadOnly( globals.avUtilities_getLookupWindowMode(event.getFormName()) ) )
	{
		// GD - 2012-04-02: Needed for when we call this form in the svy_lookup_window from estimates
		var sItemUUID = forms.in_item_dtl.item_id;
		
		foundset.newRecord(false, true);
		foundset.sequence_nr = getNextSequenceNr();
		setNewRecordDefaults(sItemUUID,event);
		
		// GD - Oct 26, 2014: SL-2137: Lock down all fields until a supplier record is present
		scopes.globals.avUtilities_setFormEditMode(scopes.globals.avUtilities_tabGetFormName("in_item_supplier_dtl", "tabs_240", scopes.globals.avUtilities_tabGetSelectedIndex("in_item_supplier_dtl", "tabs_240")), "edit");
		// RG 2015-01-14 SL-3781 added to allow changing Supplier from default set (also set supplier VL to sort on name)
		scopes.globals.avUtilities_setFormEditMode("in_item_supplier_tbl", "edit");
		forms.in_item_supplier_tbl.elements.grid.requestFocus(forms.in_item_supplier_tbl.elements.grid.getColumnIndex("supplier_id"));
	}
	
	return;
}

/**
 * SetUDFNewRecordDefaults
 *
 * @properties={typeid:24,uuid:"1A0DE9C1-B6BA-4A91-80E7-0B5FDCF1A02B"}
 * @AllowToRunInFind
 */
function setUDFNewRecordDefaults() {
	
	/** @type {JSFoundSet<db:/avanti/in_landed_cost_udf>} */
	var loFoundSet = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_landed_cost_udf')

	if (loFoundSet.find() || loFoundSet.find())
	{
		loFoundSet.search();

		/** @type {JSRecord<db:/avanti/in_landed_cost_udf>}*/
		var _jsRecord;
				
		for (var j = 1; j <= loFoundSet.getSize(); j++)
		{
			_jsRecord = loFoundSet.getRecord(j);
			
			switch (_jsRecord.lcudf_line)
			{
				
				case 1:
				foundset.itemsupp_udf1_type = _jsRecord.lcudf_cost_type;
				break;
				
				case 2:
				foundset.itemsupp_udf2_type = _jsRecord.lcudf_cost_type;
				break;
				
				case 3:
				foundset.itemsupp_udf3_type = _jsRecord.lcudf_cost_type;
				break;
				
				case 4:
				foundset.itemsupp_udf4_type = _jsRecord.lcudf_cost_type;
				break;
				
			}
						
		}

	}

}

/**
 * Callback method when the user changes tab in a tab panel or divider position in split pane.
 *
 * @param {Number} previousIndex index of tab shown before the change
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"3380A328-E8D5-485E-A3F2-1EEF4CD3080C"}
 */
function onTabChange(previousIndex, event)
{
	if (globals.nav.mode != scopes.avUtils.ENUM_NAV_MODE.Browse && scopes.globals.avUtilities_tabGetSelectedIndex(controller.getName(), "tabs_240") == 3)
	{
		if (foundset.getSize() == 0 || foundset.getSize() > 0 && foundset.itemsupp_fob_cost == null || foundset.itemsupp_fob_cost == 0)
		{
			
			//Display Message to user
			globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.supplierQuantityBreakException', i18n.getI18NMessage('avanti.dialog.ok'));
			scopes.globals.avUtilities_tabSetSelectedIndex(controller.getName(), "tabs_240", 2);
		}
	
	}

}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"2E18C3B8-16E0-4000-A215-F75A8E06A07F"}
 */
function onHide(event)
{
	// GD - 2012-10-09: Need to load the Order Items Supplier table with the same foundset
	/** @type {JSFoundSet<db:/avanti/sa_order_revh_item>} */
	var fs = forms["sa_order_revision_cost_dlg_item_tbl"].foundset;
	globals.avCalcs_revhItem_getSuppliers(fs.getSelectedRecord(), true);
	
	var _context = controller.getFormContext();
	var _formName = _context.getValue(1,2)
	
	if (utils.stringLeft(_formName,34) == 'svy_nav_fr_buttonbar_lookup_window')
	{
		if (globals.avBase_callback_afterItemSupplierLookupWindow != null)
		{
			eval(globals.avBase_callback_afterItemSupplierLookupWindow);
			globals.avBase_callback_afterItemSupplierLookupWindow
		}
		
	}
	
	return _super.onHide(event);
}

/**
 * setNewRecordDefaults
 * 
 * @param {String} _itemId
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"F6B571A3-56AF-492E-AB4B-A29D6EA01BB0"}
 */
function setNewRecordDefaults(_itemId,event) 
{
	var rSuppl = foundset.getSelectedRecord(),
		_rRec;
	
	if (!rSuppl.item_id) rSuppl.item_id = _itemId;
	
	//TODO: Add supplier and system preferences
	rSuppl.itemsupp_leadtime_flag = 'A';
	rSuppl.itemsupp_discount_type = '%';
	rSuppl.itemsupp_freight_type = '%';
	rSuppl.itemsupp_freight_in_method = 'S';
	rSuppl.itemsupp_cost_uom_factor = 1;
	
	// GD - 2012-11-02: Load the first supplier in the vl as the default
	var dsVL = application.getValueListItems("avSupplierName");
	dsVL.sort(1, true);
	// GD - Oct 26, 2014: Throwing error if null supplier records
	var iCount = 1;
	while ( dsVL.getValue(iCount,2) == null && iCount <= dsVL.getMaxRowIndex() ) {
		iCount += 1;
	}
	if (rSuppl.supplier_id == null) rSuppl.supplier_id = dsVL.getValue(iCount,2);
//	rSuppl.supplier_id = dsVL.getValue(1,2);
	
	// GD - 2012-04-02: Load the landed cost form with the new supplier
//	forms.in_item_supplier_landedcost.foundset.loadRecords(rSuppl)
// GD - Jun 14, 2014: Throwing an error (cant load records in related foundset; should be there automatically)
//	forms.in_item_supplier_landedcost.foundset.loadRecords(rSuppl.itemsupp_id)
	
	rSuppl.uom_id = rSuppl.in_item_supplier_to_in_item.item_standard_uom_id;
	forms.in_item_supplier_tbl.onDataChange_PurchaseUOM(null,rSuppl.uom_id,event)
	
	// GD - Apr 23, 2014: Added _super call and changed getSelectedRecord to getRecord(1) to avoid server errors
	_rRec = rSuppl.in_item_supplier_to_in_item.getRecord(1);
	_super.setValueList_avItem_SellingUOM(_rRec);
	_super.setValueList_avItem_PurchaseCostingUOM(_rRec); 

	// Set the UDF Type defaults
	setUDFNewRecordDefaults();
	
}

/**
 * getNextSequenceNr
 * @return {Number}
 *
 *
 *
 * @properties={typeid:24,uuid:"65C1DC74-B96E-4D59-8134-42DAD583D390"}
 */
function getNextSequenceNr()
{
	var iSeq = 0;
	
	for (var i = 1; i <= foundset.getSize(); i++)
	{
		var rRec = foundset.getRecord(i)
		if (rRec.sequence_nr > iSeq) iSeq = rRec.sequence_nr;
	}
	
	return iSeq + 1;
}

/**
 *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"2B6D94E8-56EB-4FD1-81E9-7CB94119D345"}
 */
function onShowForm(_firstShow, _event) {
    var _rRec;

    _super.onShowForm(_firstShow, _event)

    //Load UOM ValueLists
    if (utils.hasRecords(foundset.in_item_supplier_to_in_item)) {
        // GD - Apr 23, 2014: Added _super call and changed getSelectedRecord to getRecord(1) to avoid server errors
        _rRec = foundset.in_item_supplier_to_in_item.getRecord(1);
        _super.setValueList_avItem_SellingUOM(_rRec);
        _super.setValueList_avItem_PurchaseCostingUOM(_rRec);
    }

    forms.in_item_supplier_iteminfo.elements.itemsupp_last_price_date.format = globals.avBase_dateFormat;

    setUDFFields();
    forms.in_item_supplier_landedcost.refreshUI();

    //set the sort based on the sequence_nr field. The first supplier is the default
    forms.in_item_supplier_tbl.foundset.sort("sequence_nr asc");
    if (globals.avBase_callback_afterItemSupplierLookupWindowLoad) {
        forms['in_item_bo_flush_tbl'].controller.loadRecords(forms['in_item_bo_flush_tbl']._foundset);
    }
}
