/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"88602D14-AE7E-42D7-B750-AD31F62E455D",variableType:93}
 */
var dReferenceTime = application.getTimeStamp();

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"B3B64618-9C29-4749-A51C-513F16419E3C",variableType:93}
 */
var dScheduleFrom = application.getTimeStamp();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3B5D988D-2059-4A66-9DBB-210646FBE5EF",variableType:4}
 */
var nJobCount = 0;

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"6F2118B8-625F-4776-BA86-C178CDE703B9"}
 */
function onShow(firstShow, event) {
    
	dReferenceTime = application.getTimeStamp();
	dScheduleFrom = application.getTimeStamp();
		
	forms.sch_schedule_board_zone_department.foundset.loadAllRecords();
	filterScheduledJobs();
	_super.onShowForm(firstShow,event);
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"A69618AB-264B-46E4-9872-8DA51CC4FBB3"}
 */
function onDataChange_ReferenceDate(oldValue, newValue, event) {
	if(newValue){
		dReferenceTime = new Date(newValue);
		filterScheduledJobs();
	}
	
	return true;
}

/**
 * TODO generated, please specify type and doc for the params
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"CB14E0F4-2352-4207-B4B5-0AB8B0CF8176"}
 */
function onDataChange_ScheduleFrom(oldValue, newValue, event) {
    if(newValue){
        dScheduleFrom = new Date(newValue);
    }
    
    return true;
}

/**
 * @properties={typeid:24,uuid:"D1ECC7C7-73FD-4B33-922C-F8EE1BFBF69D"}
 */
function filterScheduledJobs() {
    
    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = new Object();
        
    // Get the number of jobs that will be optimized        
    oSQL.sql = "SELECT sch_id \
                FROM sch_schedule s \
                WHERE jobstat_id = 'Scheduled' \
                    AND EXISTS (SELECT NULL \
                                FROM sch_milestone m \
                                WHERE m.org_id = ? \
                                    AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                    AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                    AND m.ms_date_scheduled >= ? \
                                    AND m.sch_id = s.sch_id) \
                    AND s.org_id = ?";
    
    oSQL.args = [globals.org_id, scopes.avUtils.formatDateTime(dReferenceTime, true), globals.org_id];
    oSQL.table = 'sch_schedule';
        
    /** @type {JSDataSet} */
    var fsData = globals.avUtilities_sqlDataset(oSQL);
    nJobCount = fsData.getMaxRowIndex();
    
    var fsSchedule = forms.sch_schedule_board_optimize_schedule_jobs_tbl.foundset;
    fsSchedule.loadRecords(fsData);       
    fsSchedule.sort("schedule_sequence asc");
    
    if (nJobCount == 0) {
        // Nothing to optimize disable button
        forms.sch_schedule_board_optimize_schedule_dlg.elements.btn_optimize.enabled = false;
    }
    else {
        forms.sch_schedule_board_optimize_schedule_dlg.elements.btn_optimize.enabled = true;
    }
        
}
