/** *
 * @param _event
 * @param _form
 *
 * @properties={typeid:24,uuid:"151CF942-0F31-4884-82A0-5397198DD477"}
 */
function onRecordSelection(_event, _form) {
    _super.onRecordSelection(_event, _form);
    refreshUI();
}

/**
 * refreshUI
 *
 * @properties={typeid:24,uuid:"E4EF8302-E27C-433C-852A-004B55B0A376"}
 */
function refreshUI() {
    if (intranstype_system_record == 1) {
        elements.intranstype_trans_code.enabled = false;
        elements.intranstype_active.enabled = false;
        elements.intranstype_adjustment_type.enabled = false;
        elements.intranstype_effect_on_qty.enabled = false;
    }
    else {
        elements.intranstype_trans_code.enabled = true;
        elements.intranstype_active.enabled = true;
        elements.intranstype_adjustment_type.enabled = true;
        elements.intranstype_effect_on_qty.enabled = true;
    }

    application.executeLater(setToolBarOptions,500);
}

/**
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 * @override
 *
 * @properties={typeid:24,uuid:"53278762-CDF3-4630-B0D7-E3102DA2EC0D"}
 */
function onShowForm(_firstShow, _event) {
    _super.onShowForm(_firstShow, _event);
    refreshUI();
}
