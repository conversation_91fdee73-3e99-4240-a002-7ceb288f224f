customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sys_payroll_file_field",
extendsID:"BD3226CB-C29A-4CAE-A46B-646C3FE287B0",
initialSort:"sequence_nr asc",
items:[
{
height:250,
partType:5,
typeid:19,
uuid:"1A9FD25B-C430-46A2-883F-32367BD101B9"
},
{
height:41,
partType:1,
typeid:19,
uuid:"54E0477D-F2D7-4419-A29A-67E14460F1AB"
},
{
height:255,
partType:8,
typeid:19,
uuid:"5C3F8002-D6C0-4AA2-AB40-E93F17E16FBA"
},
{
anchors:15,
cssPosition:"41px,0px,5px,0px,1001px,209px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnOrder",
id:"fldColOrder",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"41CAAC8A-53C9-49C6-A6FD-E889F9154241",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"prff_col_header",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnHeader",
id:"fldColHeader",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"567E8BDC-F14B-43E1-B9EE-B2F29A24EEDD",
valuelist:null,
visible:true,
width:251
},
{
autoResize:false,
dataprovider:"prff_use_database_field_flag",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.UseDatabaseField",
id:"fldUseDatabaseFieldFlag",
maxWidth:120,
minWidth:120,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"5A74D105-B86D-4918-BD9B-8625B44283DF",
valuelist:null,
visible:true,
width:120
},
{
autoResize:true,
dataprovider:"aprf_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.avantiFieldName",
id:"fldFieldName",
isEditableDataprovider:"clc_EnablefldFieldPayroll",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"CFDB3B6B-9ADD-42A1-BD91-A008831A3C22",
valuelist:"424C591C-2D37-44E9-886C-D3D7C4B090C1",
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"prff_user_defined_value",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.UserDefinedValue",
id:"fldUserDefinedValue",
isEditableDataprovider:"clc_EnableFldUserDefinedValuePayroll",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"A72F3533-A5A7-4AC0-92A5-C9B6220633C3",
valuelist:null,
visible:true,
width:150
},
{
autoResize:true,
dataprovider:"prff_sort_num",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.sortNum",
id:"fldSortNum",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"00093911-425A-4B73-B767-CE0402D5E907",
valuelist:null,
visible:true,
width:108
},
{
autoResize:true,
dataprovider:"prff_sort_dir",
editType:"TYPEAHEAD",
enableRowGroup:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.sortDirection",
id:"fldSortDir",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"8A14EA40-F935-4A1C-A957-06E671BFC2ED",
valuelist:"B8D64AAD-CBB6-4F2C-B787-A703F0C9F9A8",
visible:true,
width:111
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"btnDeleteRow",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined delete",
svyUUID:"9A5A6FD8-EFE2-467D-BBEF-CD029F1441BF",
valuelist:null,
visible:true,
width:25
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"sort_icon",
rowGroupIndex:-1,
styleClass:"material-symbols-outlined swap_vert",
svyUUID:"6BD203C3-99A0-44F9-82BA-5AEDDADB3A48",
valuelist:null,
visible:true,
width:25
}
],
cssPosition:{
bottom:"5px",
height:"209px",
left:"0px",
right:"0px",
top:"41px",
width:"1001px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"9643C1F6-F634-4A61-8631-A86BB5C1CF48",
onColumnDataChange:"752F9BAE-38FE-417C-9812-7195DD8C037D",
onReady:"D19B4E1B-092C-466A-912D-3A08B1CE34AB",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"5477F039-316B-4A01-A425-3758D9E8C933"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"5C81D5A9-B748-4FF6-BA93-C02A9D61F81B"
},
{
cssPosition:"8,-1,-1,39,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"39",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"521410A6-489E-4041-913F-3BA4B477AFD2",
styleClass:"listview_noborder label_bts",
tabSeq:2,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnDelete",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"974624D5-096E-42CF-8C20-FEF8EFA9BDD2"
},
{
cssPosition:"8,-1,-1,9,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"1DAA59AF-8F90-4E93-BCB7-C1F84ACA3D1E",
styleClass:"listview_noborder label_bts",
tabSeq:1,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CB12632F-B64D-4F3B-84B7-1AD2FAB65B7B"
}
],
name:"sys_payroll_file_field_tbl",
navigatorID:"-1",
onShowMethodID:"-1",
paperPrintScale:100,
scrollbars:33,
size:"1001,585",
styleName:null,
typeid:3,
uuid:"31ACB2E1-999F-478F-A63C-4D9DBDF1D83D",
view:0