/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C04B8C3A-935F-4343-9B68-9DA70AF94859",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"74F5B449-6FAE-48DE-B3B2-1622D8E79BC0"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"EAB612C5-24EF-4A34-BFB2-B62847FF4179"}
 */
function onShow(firstShow, event)
{

if (firstShow) {
	if (!_gridReady) {
		application.executeLater(onShow, 500, [true, event]);
		return null;
	}
}

//	controller.readOnly = false;
//	globals.svy_nav_setFieldsColor(controller.getName(), 'edit');
		
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"51E51147-38ED-4F00-9188-AF4396D088FE"}
 */
var avail_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.equipmentOff');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"971481D0-C9CC-4E97-A035-829885BA440F"}
 */
var btnDelete_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.equipmentDelete');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"72E1C7B0-535D-4531-A04F-5B8DFF7AF8CB"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "sort_icon") {
		scopes.globals.avUtilities_shuffle(event);
	}
	if (col.id == "btnDelete") {
		deleteScheduleEvent(event);
	}
}

/**
 * Called when the mouse is right clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"32B542F7-33E6-4B2C-B3BA-17A6845DC301"}
 */
function onCellRightClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "viewName") {
		onRightClick_resource(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"CA07C35B-2EFD-4ECD-9F8D-7B338ACC259C"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "avail") {
		onDataChange_resourceAvail(oldValue, newValue, event);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3D6D9751-3303-4EE7-96A3-0599B50FEAC8"}
 */
function btnAdd(event) {
    if (!scopes.avUtils.isNavModeReadOnly() && utils.hasRecords(_to_sch_view$avschedule_selectedviewid)) {
        var rResource = foundset.getRecord(_to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource.newRecord(false, true));
        rResource.sequence_nr = foundset.getSize();
        elements.grid.requestFocus(elements.grid.getColumnIndex("viewName"));
    }
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"45F58FD0-EFBE-434F-BF5F-CE56BB95F440"}
 */
function onDataChange_resourceAvail(oldValue, newValue, event) {
    var bMovedMilestones = false;
    var bMoveMS = true;
    
    if (newValue) {
    	bMoveMS = (scopes.avText.showYesNoQuestion("optimizeThisResource?") == scopes.avText.yes); 
    }
    else {
        newValue = 0;
    }
    
    if (empl_id) {
    	bMovedMilestones = changeEmpAvailability(empl_id, newValue, !bMoveMS);
    }
    else if (equip_id) {
        // only revert avail for equip as this is need for equip changing for work pools - which is not an option for employees
        if (sch_edit_id != forms.sch_agenda_dtl._uScheduleEditID) {
            sch_edit_id = forms.sch_agenda_dtl._uScheduleEditID;
            avail_bak = oldValue;
            databaseManager.saveData(foundset.getSelectedRecord());
            forms.sch_agenda_dtl._bAvailChanged = true;
        }
        
        bMovedMilestones = changeEquipAvailability(equip_id, newValue, !bMoveMS);
    }

	if (bMovedMilestones) {
		forms.sch_agenda_dtl.loadView();
	}
	else {
		forms.sch_agenda_dtl.addNonShiftTimeExceptonsForAllResourcesForThisDate(application.getTimeStamp(), true);
	}

    return true;
}

/**
 * @param {UUID} uEquipID
 * @param {Number} nAvailable
 * @param {Boolean} [bBypassMoveMilestones]
 * 
 * @return {Boolean} - returns whether any milestones were moved as a result of changing 
 *
 * @properties={typeid:24,uuid:"E7D8EE12-C6DB-441A-845F-0000D535440D"}
 */
function changeEquipAvailability(uEquipID, nAvailable, bBypassMoveMilestones){
	var dCurDate = application.getTimeStamp();

    dCurDate.setHours(0,0,0,0);
    
    /***@type {JSRecord<db:/avanti/sch_milestone>}*/
    var rMilestone;
    /***@type {JSRecord<db:/avanti/eq_equipment>}*/
    var rEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uEquipID]);
    var rShiftDet = scopes.avScheduling.getShiftDetailForEquip(rEquip, dCurDate);;
    var aReschedMS = [];
    var bMovedMilestones = false;
    
    // avail
    if(nAvailable){
        scopes.avScheduling.deleteResourceCapRecsForDate('EQ', uEquipID, dCurDate);
        
        // if there's a shift today, delete any not-avail ex
        if(rShiftDet){
            scopes.avScheduling.deleteEquipExceptionsForDate(uEquipID, dCurDate, 0);
        }
        
        // if there's no shift create an avail ex 
        else{
            scopes.avScheduling.createEquipmentExceptionForDate(rEquip, dCurDate, 1, rShiftDet);
        }

        // create new cap recs after exceptions have been created/deleted above
        scopes.avScheduling.createEquipmentCapacityRecords(rEquip, null, dCurDate, 'F', true);
        
        if (!bBypassMoveMilestones) {
			bMovedMilestones = rescheduleResourceMSAfterDate("EQ", uEquipID, dCurDate);
        }
    }
    
    // not avail
    else{
    	var bDoesEquipUseLoadBalancing = scopes.avScheduling.doesEquipUseLoadBalancing(uEquipID);
    	
        if (!bBypassMoveMilestones) {
			aReschedMS = scopes.avScheduling.clearMilestonesUsingResource("EQ", uEquipID, !bDoesEquipUseLoadBalancing);
        }

        // sl-26607 - have to call deleteResourceCapRecsForDate after clearMilestonesUsingResource, as clearMilestonesUsingResource query looks at capacity recs
        scopes.avScheduling.deleteResourceCapRecsForDate('EQ', uEquipID, dCurDate);
        
        // if there's a shift today, create not-avail ex
		if (rShiftDet) {
			scopes.avScheduling.createEquipmentExceptionForDate(rEquip, dCurDate, 0, rShiftDet);
		}
		// if there's no shift delete any existing avail ex
		else {
			scopes.avScheduling.deleteEquipExceptionsForDate(uEquipID, dCurDate, 1);
		}

        // create new cap recs after exceptions have been created/deleted above
        scopes.avScheduling.createEquipmentCapacityRecords(rEquip, null, dCurDate, 'F', true);

		if (aReschedMS.length == 0) {
			bMovedMilestones = false;
		}
        // re-schedule milestones that were taken off
		else if (rescheduleMilestones()) {
			bMovedMilestones = true;
		}
		// sl-27850 - if we use load balancing and we couldnt schedule the ms today then we need to schedule tmrw, but we want to 
		// schedule before all the other ms on this equip, so we need to re-schedule all ms for this equip going forwards.  
		else {
			// have to concat new results to existing array. if we just set it to new results it wont have old because those milestones 
			// were removed from the schedule above. 
			aReschedMS = aReschedMS.concat(scopes.avScheduling.clearMilestonesUsingResource("EQ", uEquipID, true));
			bMovedMilestones = rescheduleMilestones(true);
		}
    }
    
    return bMovedMilestones;
    
    /**
     * @param {Boolean} [b2ndCall]
     * 
     * @return {Boolean}
     */
    function rescheduleMilestones(b2ndCall) {
		var bDontAllowMoveAfterToday = !b2ndCall && bDoesEquipUseLoadBalancing;
		
		for (var i = 0; i < aReschedMS.length; i++) {
			rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [aReschedMS[i]]);
			
			if (!moveMiletone(rMilestone, bDontAllowMoveAfterToday)) {
				scopes.avScheduling.removeMSFromSchedule(rMilestone.ms_id);
				return false;
			}
		}
		
		return true;
    }
}

/**
 * @param {UUID} uEmpID
 * @param {Number} nAvailable
 * @param {Boolean} [bBypassMoveMilestones]
 * 
 * @return {Boolean} - returns whether any milestones were moved as a result of changing 
 *
 * @properties={typeid:24,uuid:"BBBB71A7-9822-4B95-BE09-A30763C59B42"}
 */
function changeEmpAvailability(uEmpID, nAvailable, bBypassMoveMilestones){
    var dCurDate = application.getTimeStamp();
    dCurDate.setHours(0,0,0,0);
    /***@type {JSRecord<db:/avanti/sch_milestone>}*/
    var rMilestone;
    /***@type {JSRecord<db:/avanti/sys_employee>}*/
    var rEmp = scopes.avDB.getRec('sys_employee', ['empl_id'], [empl_id]);
    var rShiftDet = scopes.avScheduling.getShiftDetailForEmp(rEmp, dCurDate);;
    var aReschedMS = [];
    var bMovedMilestones = false;
    
    // avail
    if(nAvailable){
        scopes.avScheduling.deleteResourceCapRecsForDate('EM', empl_id, dCurDate);
        
        // if there's a shift today, delete any not-avail ex
        if(rShiftDet){
            scopes.avScheduling.deleteEmpExceptionsForDate(empl_id, dCurDate, 0);
        }
        
        // if there's no shift create an avail ex 
        else{
            scopes.avScheduling.createEmployeeExceptionForDate(rEmp, dCurDate, 1, rShiftDet);
        }

        // create new cap recs after exceptions have been created/deleted above
        scopes.avScheduling.createEmployeeCapacityRecords(rEmp, null, dCurDate, 100 ,'F', true);

		if (!bBypassMoveMilestones) {
			bMovedMilestones = rescheduleResourceMSAfterDate("EM", empl_id, dCurDate);
		}
    }
    
    // not avail
    else{
        if (!bBypassMoveMilestones) {
            aReschedMS = scopes.avScheduling.clearMilestonesUsingResource("EM", empl_id);
        }

        // sl-26607 - have to call deleteResourceCapRecsForDate after clearMilestonesUsingResource, as clearMilestonesUsingResource query looks at capacity recs
        scopes.avScheduling.deleteResourceCapRecsForDate('EM', empl_id, dCurDate);
        
        // if there's a shift today, create not-avail ex
        if(rShiftDet){
            scopes.avScheduling.createEmployeeExceptionForDate(rEmp, dCurDate, 0, rShiftDet);
        }
        
        // if there's no shift delete any existing avail ex 
        else{
            scopes.avScheduling.deleteEmpExceptionsForDate(empl_id, dCurDate, 1);
        }
    
        // create new cap recs after exceptions have been created/deleted above
        scopes.avScheduling.createEmployeeCapacityRecords(rEmp, null, dCurDate, 100 ,'F', true);
        
        // re-schedule milestones that were taken off
		if (aReschedMS.length > 0) {
			bMovedMilestones = true;

			for (var i = 0; i < aReschedMS.length; i++) {
				rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [aReschedMS[i]]);
				moveMiletone(rMilestone);
			}
		}
    }
    
    return bMovedMilestones;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bDontAllowMoveAfterToday]
 * @param {Boolean} [bBypassPushPredecessorsBack]
 * 
 * @return {Boolean} - whether or not the move was made
 *
 * @properties={typeid:24,uuid:"4D174128-4330-469B-BD21-2B6C304E9CA6"}
 */
function moveMiletone(rMilestone, bDontAllowMoveAfterToday, bBypassPushPredecessorsBack) {
    var nCurDate = scopes.avDate.getDateAsNum(application.getTimeStamp());
	
	scopes.avScheduling.clearMilestoneCapacity(rMilestone);

    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
        rMilestone.sch_milestone_to_sch_equip_schedule.deleteAllRecords();
    }
    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
        rMilestone.sch_milestone_to_sch_empl_schedule.deleteAllRecords();
    }
    
    createTemporaryMilestone(rMilestone);

    var dStartDateBak = rMilestone.ms_date_scheduled;
    var dEndDateBak = rMilestone.ms_date_due;
    var nDiffMins;
    
    rMilestone.ms_date_scheduled = application.getTimeStamp();
    
    // SL-17471 - have to consider predecessor when scheduling milestone. dont want to push it into the past, and want to maintain lag if there is one.
    var dMaxPredecessorDate = scopes.avScheduling.getMaxPredecessorDate(rMilestone, true);
    if (dMaxPredecessorDate > rMilestone.ms_date_scheduled) {
        rMilestone.ms_date_scheduled = dMaxPredecessorDate;
    }
    
	if (bDontAllowMoveAfterToday && scopes.avDate.getDateAsNum(rMilestone.ms_date_scheduled) > nCurDate) {
		return false;
	}    
    
    rMilestone.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
    
    scopes.avScheduling.getNextFreeSpot(rMilestone, 'F');

    // have to test again after getNextFreeSpot() as the milestone could have been pushed out
	if (bDontAllowMoveAfterToday && scopes.avDate.getDateAsNum(rMilestone.ms_date_scheduled) > nCurDate) {
		return false;
	}    
    
    updateResourceSchedule(rMilestone);
    updateEmpSchedule(rMilestone);
    
    // if ms end time has moved ahead - have to push successors ahead
    if (rMilestone.ms_date_due > dEndDateBak) {
        forms.sch_agenda_dtl.pushSuccessorForward(rMilestone);
    }
    // if ms start time has moved ahead - have to pull predecessors ahead 
    if (rMilestone.ms_date_scheduled > dStartDateBak && forms.sch_agenda_dtl._pullPredecessors) {
        forms.sch_agenda_dtl.pullPredecessorsForward(rMilestone.ms_id);
    }
    // if ms start time has moved back - have to push predecessors back
    if (!bBypassPushPredecessorsBack && rMilestone.ms_date_scheduled < dStartDateBak) {
        nDiffMins = scopes.avDate.getDiffInMinutes(dStartDateBak, rMilestone.ms_date_scheduled);
		forms.sch_agenda_dtl.pushPredecessorsBack(rMilestone.ms_id, nDiffMins)
    }
    // if ms end time has moved back - have to pull successors back
    if (rMilestone.ms_date_due < dEndDateBak && forms.sch_agenda_dtl._pullSuccessors) {
        forms.sch_agenda_dtl.pullSuccessorBack(rMilestone);
    }
 
    return true;
}

/**
 * @param {String} sResType - EQ or EM
 * @param {UUID} uResID
 * @param {Date} dDate
 * 
 * @return {Boolean} - returns whether any milestones were moved 
 *
 * @properties={typeid:24,uuid:"8FFCAA76-A9CC-4E5E-9B9C-E11F403B13F2"}
 */
function rescheduleResourceMSAfterDate(sResType, uResID, dDate) {
    var sSQL;
    var sDateSQL = scopes.avDate.getDateTimeSQl(dDate, "M", true);
    var aArgs = [globals.org_id];
    
	if (sResType == 'EQ') {
		sSQL = "select es.ms_id \
	            from sch_equip_schedule es \
	            inner join sch_milestone ms on ms.ms_id = es.ms_id \
	            where es.org_id = ? and equipsch_start > " + sDateSQL + " \
	            and isnull(ms.ms_flg_completed, 0) = 0";

		var aWorkPoolEquipment = scopes.avScheduling.getLoadBalancingWorkPoolEquipment(uResID);
		
		if (aWorkPoolEquipment.length > 1) {
			sSQL += " and equip_id IN (";			
			
			for (var i = 0; i < aWorkPoolEquipment.length; i++) {
				if (i > 0) {
					sSQL += ",";
				}

				sSQL += "?";
				aArgs.push(aWorkPoolEquipment[i].toString());
			}			
			
			sSQL += ")";
			
		}
		else {
			sSQL += " and equip_id = ?";
			aArgs.push(uResID.toString());
		}
	}
	else {
		sSQL = "select es.ms_id \
                from sch_empl_schedule es \
                inner join sch_milestone ms on ms.ms_id = es.ms_id \
                where es.org_id = ? and empl_id = ? and emplsch_start > " + sDateSQL + " \
                and isnull(ms.ms_flg_completed, 0) = 0";
		
		aArgs.push(uResID.toString());
	}
    
    /**@type {JSFoundSet<db:/avanti/sch_milestone>} */
    var fsMS = scopes.avDB.getFSFromSQL(sSQL, "sch_milestone", aArgs);
    
	if (utils.hasRecords(fsMS)) {
		fsMS.sort("ms_date_scheduled ASC");
		
		for (i = 1; i <= fsMS.getSize(); i++) {
			// have to move the predecessors back first or we wont be able to move the resource milestone back 
			rescheduleMilestoneAndPredecessors(fsMS.getRecord(i));
		}
		
		return true;
	}
	else {
		return false;
	}
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"68F50EEC-**************-3B805749C6F1"}
 */
function rescheduleMilestoneAndPredecessors(rMilestone) {
	var aPredecessors = scopes.avScheduling.getSchedulablePredecessors(rMilestone, true);
	
	// if this milestone has any predecessors they need to be schedule first
	if (aPredecessors.length > 0) {
		for (var i = 0; i < aPredecessors.length; i++) {
			rescheduleMilestoneAndPredecessors(aPredecessors[i]);
		}
	}
	
	moveMiletone(rMilestone, null, true);
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param _form
 *
 * @properties={typeid:24,uuid:"956F7847-6197-4EC1-A6FB-E2FA7E6F8C63"}
 */
function onRecordSelection(event, _form)
{
	globals.avSchedule_selectedViewDept = dept_id; 
	if (empl_id){
        forms.sch_agenda_dtl._emplID = empl_id;
	}
	else{
        forms.sch_agenda_dtl._equipID = equip_id;
	}
	
	forms.sch_agenda_dtl.refreshUI();
}

/**
 * Perform the element right-click action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"539F9347-66F7-4514-A2FD-F2F71749E6C6"}
 */
function onRightClick_resource(event) {
    if (scopes.avScheduling.inDebugMode()) {
        var sName;
        
        if (utils.hasRecords(sch_view_resource_to_eq_equipment)) {
            sName = sch_view_resource_to_eq_equipment.equip_desc;
        }
        else if (utils.hasRecords(sch_view_resource_to_sys_employee)) {
            sName = sch_view_resource_to_sys_employee.empl_full_name;
        }
        
        forms.sch_debug_cap_recs.loadCapRecs(foundset.getSelectedRecord());
        globals.DIALOGS.showFormInModalDialog(forms.sch_debug_cap_recs, -1, -1, 1100, -1, "Capacity recs for: " + sName, true, false, "dlgCapacityRecs", true);
    }
}
