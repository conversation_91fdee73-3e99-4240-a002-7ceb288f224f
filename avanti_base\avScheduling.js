/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"51365DC9-6309-4478-852B-BE11E073BCD5",variableType:4}
 */
var CAPACITY_RECORDS_BUFFER_DAYS = 10;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"0E0876ED-14F2-46AF-85A3-942B65F1D520",variableType:4}
 */
var nMaxDaysToLookAheadInTheSchedule = 365;


/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"ACBEDE76-820F-4782-A96C-35BA9F0BD982",variableType:4}
 */
var nRecursiveGetNextFreeSpotCalls = 0;

/**
 * @public 
 * 
 * @properties={typeid:35,uuid:"49B77590-8D94-4722-95F2-8405CF27EE23",variableType:-4}
 */
var bDoingAutoSchedule = false;

/**
 * @public 
 * 
 * @properties={typeid:35,uuid:"BB0572B1-BD32-435B-BB9F-1248DF522983",variableType:-4}
 */
var SCHEDULE_DIRECTION = {
    Forwards: "F",
    Backwards: "B"
};

/**
 * @public 
 * 
 * @properties={typeid:35,uuid:"CAEB047C-E7D7-4C7B-8430-ABB522301150",variableType:-4}
 */
var MOVE_FAIL_REASON = {
    StartOutsideOfShift: "StartOutsideOfShift",
    InsufficientTimeAvailable: "InsufficientTimeAvailable",
    CantMoveEquipToEmp: "CantMoveEquipToEmp",
    CantMoveEmpToEquip: "CantMoveEmpToEquip",
    NotInSameWorkPool: "NotInSameWorkPool",
    RequiredEmpsUnavailable: "RequiredEmpsUnavailable"
};

/**
 * @properties={typeid:35,uuid:"A910E597-B895-4D24-BFD7-EEF0279804B0",variableType:-4}
 */
var SCHEDULE_BOARD_MODE = {
	week: "week",
	month: "month",
	day: "day",
	resource: "unit",
    timeline: "timeline"
};

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F30B9F46-242E-4C49-90EB-3E82C6CD0DFA"}
 */
var sMoveFailReason = null;

/**
 * @properties={typeid:35,uuid:"20187BF5-1FCC-4640-8447-0C15A142A1E9",variableType:-4}
 */
var bEquipChangeTimeRecalc = false;

/**
 * @properties={typeid:35,uuid:"2B98A3D7-C18D-4A07-93AE-0245CC9015AA",variableType:-4}
 */
var RESOURCE_TYPE = {
    Employee: "EM",
    Equipment: "EQ"
};

/**
 * @properties={typeid:35,uuid:"DEF4D2DC-063F-4A38-A17A-0C88BBECD5DF",variableType:-4}
 */
var _bGroupJobs = false;

/**
 * @properties={typeid:35,uuid:"B50CDE7A-BD21-48B0-98C7-AB14D7483316",variableType:-4}
 */
var _bValidateLoadBalanced = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"20D8ECE8-0B42-4D0A-84B2-27C0AEF6E41D"}
 */
var _uValidateLoadBalancedEquipIDOverride = null;

/**
 * @type {Array<Date>}
 * 
 * @properties={typeid:35,uuid:"621DBF5E-DBA4-4036-A87B-318966F24208",variableType:-4}
 */
var _aWorkPoolSchedTimes = [];

/**
 * @type {UUID}
 *
 * @properties={typeid:35,uuid:"7AE6364E-86CC-42F5-81A5-4FAAB21EC0CF",variableType:-4}
 */
var _uSelectedShift = null;

/**
 * @properties={typeid:35,uuid:"7AB67DEE-B0D4-4BC2-9F8A-B18233BAEAA3",variableType:-4}
 */
var _aResourceDatesProcessed = [];

/**
 * @properties={typeid:35,uuid:"2AAF59B9-717D-4669-86BD-32A0886C2D7F",variableType:-4}
 */
var bReservationScheduling = false;

/**
 * @properties={typeid:35,uuid:"040A563C-F500-4300-889C-AE40029B5152",variableType:-4}
 */
var bCommittingSchedule = false;

/**
 * @properties={typeid:35,uuid:"1B5D6CA3-8C2B-461F-86E6-59DA8AD870FB",variableType:-4}
 */
var bCommittingDetailSchedule = false;

/**
 * @properties={typeid:35,uuid:"3ED3DAF6-3EB8-48FB-8F43-BE0E84728818",variableType:-4}
 */
var bInReadyToSchedule = false;

/**
 * @properties={typeid:35,uuid:"9CCD623E-8B7E-4095-BEC0-24D4A3A953FE",variableType:-4}
 */
var bInScheduleBoard = false;

/**
 * @properties={typeid:35,uuid:"6BF120C9-84BA-4676-8A3D-E193B33843C6",variableType:-4}
 */
var bValidatingScheduleBoardMove = false;

/**
 * @properties={typeid:35,uuid:"DCBC9BEE-F93E-4EB9-B25B-EC69880B311D",variableType:-4}
 */
var bInQuickSchedule = false;

/**
 * @properties={typeid:35,uuid:"10231B7D-33F2-40AE-A93E-DB81B95057E3",variableType:-4}
 */
var bInShopfloorView = false;

/**
 * @properties={typeid:35,uuid:"94FD1D64-6CF4-44E9-B2EE-B9E5F3D3CEFA",variableType:-4}
 */
var bInEmployeeShifts = false;

/**
 * Wrapper function for Integration Log reporting for simplified calls to
 * report success/failure/warning for automatic scheduling of jobs triggered
 * via an XML import.
 *
 * <AUTHOR> Meixner
 * @since 2016-06-29
 *
 * @param {String} sMsg - message to log
 * @param {String} sStatus - status (Error|Warning|Success)
 * @param {String} sNumber - Ref Number (eg: Job Number, etc.)
 * @param {UUID} sDBLogParentUUID - parent log record
 * @param {String} sTroubleShootingDetails
 * @param {String} sDownloadableFile
 * @param {Error} oException
 *
 * @properties={typeid:24,uuid:"F1CFFD56-26F5-4AB9-B558-0D8E9419D14B"}
 *
 * @return {UUID} - log record id
 */
function logAutoScheduleStatus(sMsg, sStatus, sNumber, sDBLogParentUUID, sTroubleShootingDetails, sDownloadableFile, oException) {

    var sUUIDLogRecordID = globals.dbLogUnified(sMsg,
        'wtp_request',
        'import',
        'wtp',
        'http',
        globals.org_id,
        'web_to_print',
        'Summary',
        sDBLogParentUUID,
        sStatus,
        'Scheduling Job',
        sNumber,
        sTroubleShootingDetails,
        sDownloadableFile,
        oException
    );

    return sUUIDLogRecordID;
}

/**
 * This will check the worktype of all jobs attached to the given order to
 * see if their 'sysworktype_autoschedule' flag is true and, if so, that job
 * will be automatically scheduled.
 *
 * Note: This function is only intended to be called for Sales Orders that
 * have been created via an XML import and automatically released.
 *
 * <AUTHOR> Meixner
 * @since 2016-06-28
 *
 * @param {JSFoundSet<db:/avanti/sa_order>} fsOrder
 * @param {UUID} sDBLogParentUUID
 * @param {Boolean} bUpdateAutoScheduledFlagOnly we need this flag only for auto export to JDF for 
 * imported orders which were released manuallly by user SL-22765
 *
 * @return
 * @properties={typeid:24,uuid:"FA6ACB24-2745-4E79-98D1-5421728A9B82"}
 */
function addOrderToPublishSchedule(fsOrder, sDBLogParentUUID, bUpdateAutoScheduledFlagOnly) {
    // Validate Data:
    if (!fsOrder) {
        application.output('avanti_base/avScheduling.js:addOrderToPublishSchedule(): ERROR: received null/undefined value for fsOrder', LOGGINGLEVEL.ERROR);
        return false;
    }

    if (!utils.hasRecords(fsOrder.sa_order_to_sa_order_revision_header) || !utils.hasRecords(fsOrder.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail$is_deleted)) {
    	if (!bUpdateAutoScheduledFlagOnly) {
    		logAutoScheduleStatus(i18n.getI18NMessage('avanti.dialog.warningNoLineItems'), 'Warning', fsOrder.ordh_document_num, sDBLogParentUUID, null, null, null);
    	}
        return true;
    }

    var aSchedIds = [];
    var rHeader = fsOrder.sa_order_to_sa_order_revision_header.getRecord(1);

    // Look for Worktypes configured to be automatically scheduled and publish them to the schedule:
    for (var nJobIndex = 1; nJobIndex <= rHeader.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getSize(); nJobIndex++) {
        var rJob = fsOrder.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail$is_deleted.getRecord(nJobIndex);

        // Note: All jobs created via an XML import will have a worktype
        // All jobs created via XML imports will have a job id
        if (utils.hasRecords(rJob, 'sa_order_revision_detail_to_prod_job.prod_job_to_sch_schedule')
            && utils.hasRecords(rJob.sa_order_revision_detail_to_sys_task_worktype)
            && rJob.sa_order_revision_detail_to_sys_task_worktype.sysworktype_autoschedule) {

            aSchedIds.push(rJob.sa_order_revision_detail_to_prod_job.prod_job_to_sch_schedule.sch_id);
        }
    }
    
    if (aSchedIds.length > 0) {
        // Get the system preference for 'Forwards' (F) or 'Backards' (B) scheduling:
        var sScheduleDirection = globals.avBase_getSystemPreference_String(147);
        var sSchedIds = "(" + scopes.avText.arrayToString(aSchedIds, ",", "'") + ")";
        /**@type {JSFoundSet<db:/avanti/sch_schedule>} */
        var fsSchedule = scopes.avDB.getFSFromSQL("select sch_id from sch_schedule where sch_id in " + sSchedIds,
            "sch_schedule");

        if (fsSchedule) {
        	if (!bUpdateAutoScheduledFlagOnly) {
        		globals["setValueOfJobsCreatedTroughWebToPress"](true);
        		forms['sch_schedule_tbl_by_job']._scheduleDirection = sScheduleDirection;
        		forms['sch_schedule_tbl_by_job'].btnAddSelected_onAction(null, fsSchedule);
        		globals["setValueOfJobsCreatedTroughWebToPress"](false);
        	}

            // check to see which ones were successfully scheduled and log it
            for (var i = 1; i <= fsSchedule.getSize(); i++) {
                var rSchedule = fsSchedule.getRecord(i);
                rSchedule.sch_auto_scheduled = 1;
                databaseManager.saveData(rSchedule);
                var sJobNumber = rSchedule.sch_schedule_to_prod_job.job_number;

				if (!bUpdateAutoScheduledFlagOnly) {
					if (rSchedule.jobstat_id == 'Scheduled') {
						logAutoScheduleStatus(i18n.getI18NMessage('avanti.dialog.SuccessfulAutoSchedule'), 'Success', sJobNumber, sDBLogParentUUID, null, null, null);
					} else {
						logAutoScheduleStatus(i18n.getI18NMessage('avanti.dialog.failedAutoSchedule'), 'Error', sJobNumber, sDBLogParentUUID, null, null, null);
					}
				}
            }
        }
    }
    
    return true;
}

/**
 * Remove job from schedule, including sch_equip_schedule, sch_empl_schedule and sch_emp_capacity
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/prod_job>} rJob
 *
 * @properties={typeid:24,uuid:"9B2E70C1-89B3-44B6-84AD-5E81E616F64B"}
 */
function removeJobFromSchedule(rJob) {
    if (rJob &&
    	utils.hasRecords(rJob.prod_job_to_sch_schedule) &&
		utils.hasRecords(rJob.prod_job_to_sch_schedule.sch_schedule_to_sch_milestone)) {
        var fsMilestones = rJob.prod_job_to_sch_schedule.sch_schedule_to_sch_milestone;

        var bThomas = utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_name.indexOf("THOMAS PRINTWORKS") > -1; 
        if (bThomas) {
        	scopes.avUtils.devLog("SL-27560", 'removeJobFromSchedule for job: ' + rJob.job_number, true, null, null, null, null, false);
        }
        
        for (var i = 1; i <= fsMilestones.getSize(); i++) {
            var rMilestone = fsMilestones.getRecord(i);
            
            rMilestone.ms_date_scheduled = null;
            rMilestone.ms_date_due = null;

            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
                rMilestone.sch_milestone_to_sch_equip_schedule.deleteAllRecords();
                databaseManager.saveData(rMilestone.sch_milestone_to_sch_equip_schedule);
            }

            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
                rMilestone.sch_milestone_to_sch_empl_schedule.deleteAllRecords();
                databaseManager.saveData(rMilestone.sch_milestone_to_sch_empl_schedule);
            }

            // sl-15714 - restore capacity for completed milestones
            removeMSCapacityRecs(rMilestone);
        }
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return {{sLagType:String,nLagTime:Number}}
 *
 * @properties={typeid:24,uuid:"A1A03D61-8B6F-4DFB-A7DA-58082D39DBD5"}
 */
function getMilestoneLag(rMilestone) {
    /***@type {{sLagType:String,nLagTime:Number}}*/
    var oLag = {};

	if (rMilestone.ms_date_scheduled && rMilestone.ms_date_due) {
	    var nStartDateLag = scopes.avScheduling.getMilestoneStartDateLag(rMilestone);
	    var nEndDateLag = scopes.avScheduling.getMilestoneEndDateLag(rMilestone);
		var dStartTimeWithLag = null;
		var dEndTimeWithLag = null;
	    
	    // if there is a FS lag and an SS lag then return whichever one produces the biggest lag between milestones when applied to rMilestone start and stop time
		if (nStartDateLag) {
			dStartTimeWithLag = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_scheduled, nStartDateLag);
		}
		if (nEndDateLag) {
			dEndTimeWithLag = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, nEndDateLag);
		}

		if (nStartDateLag && dStartTimeWithLag > dEndTimeWithLag) {
	        oLag.sLagType = "SS";
	        oLag.nLagTime = nStartDateLag;
		}
		else if (nEndDateLag) {
	        oLag.sLagType = "FS";
	        oLag.nLagTime = nEndDateLag;
		}
	}
	
	return oLag;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rLineItem
 * @param {Boolean} bDeleteSchedule
 * @param {String} sOrderType
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"218802E2-1008-4551-ADD6-A99C6865B5DF"}
 */
function deleteReservation(rLineItem, bDeleteSchedule, sOrderType) {
    if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_prod_job$reservation)) {
        rLineItem.sa_order_revision_detail_to_prod_job$reservation.deleteRecord();
        rLineItem.res_job_id = null;
        databaseManager.saveData(rLineItem);

        if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_sch_schedule)) {
            for (var s = 1; s <= rLineItem.sa_order_revision_detail_to_sch_schedule.getSize(); s++) {
                var rSched = rLineItem.sa_order_revision_detail_to_sch_schedule.getRecord(s);
                
                if (rSched.sch_schedule_to_sch_milestone) {
                    for (var m = 1; m <= rSched.sch_schedule_to_sch_milestone.getSize(); m++) {
                        var rMS = rSched.sch_schedule_to_sch_milestone.getRecord(m);
                        
                        // deletes sch_equip_schedule and sch_emp_schedule recs
                        removeMSFromSchedule(rMS.ms_id);
                        
                        // removes MS from capacity recs
                        removeMSCapacityRecs(rMS);
                    }
                }
                
                if (!bDeleteSchedule) {
                    rSched.job_id = null;
                    rSched.jobstat_id = null;
                }
            }

            if (bDeleteSchedule) {
                rLineItem.sa_order_revision_detail_to_sch_schedule.deleteAllRecords();
            }
        }

        var rHeader = rLineItem.sa_order_revision_detail_to_sa_order_revision_header.getRecord(1);
        var rOrder = rHeader.sa_order_revision_header_to_sa_order.getRecord(1);

        if (sOrderType == 'EST') {
            removeReservedQty(rLineItem);

            if (rOrder.ordh_estimate_status == 'Reserved') {
                rOrder.ordh_estimate_status = 'Open';
            }
        }
        else if (rHeader.ordrevh_order_status == 'Reserved') {
            if (rHeader.ordrevh_order_status_bak) {
                rHeader.ordrevh_order_status = rHeader.ordrevh_order_status_bak;
            }
            else {
                rHeader.ordrevh_order_status = 'Open';
            }
        }

        return true;
    }

    return false;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rLineItem
 *
 * @properties={typeid:24,uuid:"A6933794-E455-435B-B550-73CC38329881"}
 */
function removeReservedQty(rLineItem) {
    //  - update the ordrevditem_reserved_qty, which will feed the item reserved qty
    if (utils.hasRecords(rLineItem.sa_order_revision_detail_to_sa_order_revd_item)) {
        for (var i = 1; i <= rLineItem.sa_order_revision_detail_to_sa_order_revd_item.getSize(); i++) {
            var rRevDItem = rLineItem.sa_order_revision_detail_to_sa_order_revd_item.getRecord(i);

            if (rRevDItem.ordrevditem_reserved_qty_est && rRevDItem.ordrevditem_reserved_qty && rRevDItem.ordrevditem_reserved_qty_est == rRevDItem.ordrevditem_reserved_qty) {

                rRevDItem.ordrevditem_reserved_qty = 0;
                databaseManager.saveData(rRevDItem);

                if (utils.hasRecords(rRevDItem.sa_order_revd_item_to_in_item)) {
                    var rItem = rRevDItem.sa_order_revd_item_to_in_item.getRecord(1);

                    rItem.item_reserved_qty -= rRevDItem.ordrevditem_reserved_qty_est;
                    databaseManager.saveData(rItem);

                    if (rLineItem.whse_id) {
                        globals.avSales_selectedWhseID = rLineItem.whse_id;
                        if (utils.hasRecords(rItem.in_item_to_in_item_warehouse$avsales_selectedwhseid)) {
                            rItem.in_item_to_in_item_warehouse$avsales_selectedwhseid.itemwhse_reserved_qty -= rRevDItem.ordrevditem_reserved_qty_est;
                            databaseManager.saveData(rItem);
                        }
                    }
                }
            }
        }
    }
}

/**
 * @public
 *
 * @param {String} sStatus
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F477A930-BCFC-45F6-B535-53F05AE7AE1E"}
 */
function estStatusAllowsReservations(sStatus) {
    //InProgress is for project plans
    var aValidStatuses = ['Open', 'Reserved', 'Incomplete', 'RFQ', 'QuoteSent', 'ReadyForCustomer', 'ReadyForSalesRep', 'InProgress'];

    if (aValidStatuses.indexOf(sStatus) > -1) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public
 *
 * @param {String} sStatus
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B6B6A6E5-ACB9-4E36-B7EA-C9C9DB9B3681"}
 */
function ordStatusAllowsReservations(sStatus) {
    var aValidStatuses = ['Open', 'Reserved', 'Incomplete'];

    if (aValidStatuses.indexOf(sStatus) > -1) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sa_order>} rEst
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"A3658D11-C349-466F-A6CD-E28A87EE5085"}
 */
function estimateHasReservations(rEst) {
    if (utils.hasRecords(rEst, 'sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail$is_deleted')) {
        var fsLines = rEst.sa_order_to_sa_order_revision_header.sa_order_revision_header_to_sa_order_revision_detail$is_deleted;

        if (utils.hasRecords(fsLines)) {
            for (var i = 1; i <= fsLines.getSize(); i++) {
                var rLine = fsLines.getRecord(i);

                if (utils.hasRecords(rLine.sa_order_revision_detail_to_prod_job$reservation)) {
                    return true;
                }
            }
        }
    }

    return false;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sa_order>} rOrder
 * @param {String} sOrderType
 *
 * @properties={typeid:24,uuid:"CADFFD66-E045-4501-88CC-8D2883887F22"}
 */
function deleteAllReservations(rOrder, sOrderType) {
    if (sOrderType != "EST" && sOrderType != "ORD") return;

    if (utils.hasRecords(rOrder.sa_order_to_sa_order_revision_header)) {
        var rHead = rOrder.sa_order_to_sa_order_revision_header.getRecord(1);

        if (utils.hasRecords(rHead.sa_order_revision_header_to_sa_order_revision_detail$is_deleted)) {
            var fsLines = rHead.sa_order_revision_header_to_sa_order_revision_detail$is_deleted;

            if (utils.hasRecords(fsLines)) {
                for (var i = 1; i <= fsLines.getSize(); i++) {
                    var rLine = fsLines.getRecord(i);

                    deleteReservation(rLine, true, sOrderType);
                }
            }
        }
    }
}

/**
 * @public
 *
 * @param {String} sOrderType
 *
 * @properties={typeid:24,uuid:"CC8E8B6A-5503-45ED-9B27-D7DAD8D00315"}
 */
function deleteExpiredReservationsByOrderType(sOrderType) {
    if (sOrderType != "EST" && sOrderType != "ORD") return;

    /***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
    var oSQL = { };
    /***@type {JSDataSet} */
    var dsData;
    var i;
    var sOrdID;
    /**@type {JSRecord<db:/avanti/sa_order>} */
    var rOrd;

    oSQL.sql = "select distinct ord.ordh_id \
			    from prod_job job \
			    inner join sa_order_revision_detail det on det.res_job_id = job.job_id \
			    inner join sa_order_revision_header head on head.ordrevh_id = det.ordrevh_id \
			    inner join sa_order ord on ord.ordh_id = head.ordh_id \
			    where ord.org_id = ? and job.job_is_reservation = 1 and ";

    if (sOrderType == 'EST') {
        oSQL.sql += "GETDATE() > ord.ordh_estimate_good_until_date";
    }
    else if (sOrderType == 'ORD') {
        oSQL.sql += "GETDATE() > head.ordrevh_expected_date";
    }

    oSQL.args = [globals.org_id.toString()];
    dsData = globals["avUtilities_sqlDataset"](oSQL);

    if (dsData != null) {
        for (i = 1; i <= dsData.getMaxRowIndex(); i++) {
            sOrdID = dsData.getValue(i, 1);

            if (sOrdID) {
                rOrd = scopes.avDB.getRec('sa_order', ['ordh_id'], [sOrdID]);

                if (rOrd) {
                    deleteAllReservations(rOrd, sOrderType);
                }
            }
        }
    }
}

/**
 * @public
 *
 * @properties={typeid:24,uuid:"6B955737-7760-41D7-A5A4-D63AFC3A8FF1"}
 */
function deleteExpiredReservations() {
    //TODO turn it into a scheduled nightly cron
    if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.EnableReservationScheduling)) {
        deleteExpiredReservationsByOrderType("EST");
        deleteExpiredReservationsByOrderType("ORD");
        databaseManager.saveData();
    }
}

/**
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Number} nNumMinutesNeeded
 * @param {Date} dCurDateTime
 * @param {String} sDirection - (F)orwards or (B)ackwards
 * @param {Boolean} [bJustDoThisDate]
 *
 * @return {Date} - dSoonestDateAnyTimeIsAvail
 *
 * @properties={typeid:24,uuid:"71ABA727-4E59-40B5-A83C-46765B99D1A0"}
 */
function createEquipmentCapacityRecords(rEquip, nNumMinutesNeeded, dCurDateTime, sDirection, bJustDoThisDate) {
    var nFreeMinutes = 0;
    var dSoonestDateAnyTimeIsAvail = null;
    
    // sl-22451 - check for and fix problems with capacity recs
	clearCapacityRecsOutsideEquipScheduleDates(rEquip.equip_id);
    
    /**@type {JSRecord<db:/avanti/sch_shift>} */
    var rShift;
    var dCurDate = scopes.avDate.copyDate(dCurDateTime, true);
    /**@type {{dSoonestDateAnyTimeIsAvail:Date, nFreeMinutes:Number}} */
    var oShift = {}; 

    if (rEquip.eq_equipment_to_sch_equipment_shift.getSize() > 1) {
        if (sDirection == 'F') {
            rEquip.eq_equipment_to_sch_equipment_shift.sort('sequence_nr asc');
        }
        else {
            rEquip.eq_equipment_to_sch_equipment_shift.sort('sequence_nr desc');
        }
    }

    for (var s = 1; s <= rEquip.eq_equipment_to_sch_equipment_shift.getSize(); s++) {
        var rEquipShift = rEquip.eq_equipment_to_sch_equipment_shift.getRecord(s);

        if (rEquipShift.start_date <= dCurDate && ( rEquipShift.end_date >= dCurDate || rEquipShift.end_date == null )) {
            if (utils.hasRecords(rEquipShift.sch_equipment_shift_to_sch_shift)) {
                rShift = rEquipShift.sch_equipment_shift_to_sch_shift.getRecord(1);
                oShift = processShift(rShift, nNumMinutesNeeded, dCurDate, RESOURCE_TYPE.Equipment, rEquip.equip_id, sDirection, 100, bJustDoThisDate);
                
                if (oShift.nFreeMinutes) {
                    nFreeMinutes += oShift.nFreeMinutes;
                }
                if (!dSoonestDateAnyTimeIsAvail && oShift.dSoonestDateAnyTimeIsAvail) {
                    dSoonestDateAnyTimeIsAvail = oShift.dSoonestDateAnyTimeIsAvail;
                }
                
                if (nFreeMinutes >= nNumMinutesNeeded) {
                    break;
                }
            }
        }
    }

    if (nFreeMinutes >= nNumMinutesNeeded) {
        return dSoonestDateAnyTimeIsAvail;
    }
    else {
        return null;
    }
}

/**
 * @private 
 * 
 * @param {JSRecord<db:/avanti/sch_shift>} rShift
 * @param {Number} nNumMinutesNeeded
 * @param {Date} dCurDate
 * @param {String} sResourceType
 * @param {UUID} sResourceID
 * @param {String} sDirection - (F)orwards or (B)ackwards
 * @param {Number} nCapacityRequired
 * @param {Boolean} [bJustDoThisDate]
 * 
 * @return {{dSoonestDateAnyTimeIsAvail:Date, nFreeMinutes:Number}}
 *
 * @properties={typeid:24,uuid:"6F51B2BF-19E4-4D81-9D95-34EB68E53A56"}
 */
function processShift(rShift, nNumMinutesNeeded, dCurDate, sResourceType, sResourceID, sDirection, nCapacityRequired, bJustDoThisDate) {
    /**@type {{dSoonestDateAnyTimeIsAvail:Date, nFreeMinutes:Number}} */
    var oReturn = {}; 
    /**@type {Date} */
    var dSoonestDateAnyTimeIsAvail = null;
    var nFreeMinutes = 0;
    var dNow = application.getTimeStamp();
    var nNumMinutesNeededWithBuffer = nNumMinutesNeeded + (480 * CAPACITY_RECORDS_BUFFER_DAYS);
    var dStartProcessDate = dCurDate;
    
    if (utils.hasRecords(rShift.sch_shift_to_sch_shift_detail)) {
        // added this counter as a failsafe to prevent endless loop
        var nWhileCount = 0;
        var dLastDayTimeAvail = null;

        while (nFreeMinutes < nNumMinutesNeededWithBuffer && nWhileCount < nMaxDaysToLookAheadInTheSchedule) {
        	// if there aren't any shift details now or in the future that work then there's no point in continuing
			if (!areThereAnyShiftDetailsThatWorkForDateOnwards(rShift, dCurDate, sDirection)) {
				break;
			}
        	
            var rShiftDet = getShiftDetailThatWorksThisWeekDay(rShift, dCurDate);
            var nCapacityRecs = getNumExistingCapacityRecsForDate(dCurDate, sResourceType, sResourceID);

            // sl-27565 - if no rShiftDetail works this day then see if we have any available exceptions for this date  
            if (!rShiftDet) {
            	rShiftDet = getShiftDetailThatWorksThisWeekDay(rShift, dCurDate, true);
    	        
    			if (rShiftDet) {
    				var aAvailEx = sResourceType == RESOURCE_TYPE.Employee
    					? getExceptionsForDate(rShiftDet.shift_id, dCurDate, 1, null, sResourceID)
    					: getExceptionsForDate(rShiftDet.shift_id, dCurDate, 1, sResourceID);
    				var bAvailEx = (aAvailEx && aAvailEx.length);
    				
   	            	rShiftDet = null;
    			}
            }
            
            if (rShiftDet || bAvailEx) {
                var nFreeMinutesThisDate = 0;
                var nLongestSingleFreeBlock = 0;
                var bCreateCaprecs = false;
                var bAreThereAnyScheduledJobsForThisResourceDate = false;

                if (nCapacityRecs > 0) {
                    nFreeMinutesThisDate = getFreeCapacityForDate(dCurDate, sResourceType, sResourceID, nCapacityRequired);                    
                    nLongestSingleFreeBlock = getFreeCapacityForDate(dCurDate, sResourceType, sResourceID, nCapacityRequired, null, true)
					bAreThereAnyScheduledJobsForThisResourceDate = areThereAnyScheduledJobsForThisResourceDate(sResourceType, sResourceID, dCurDate);

					// sl-28281 - if no schedule jobs for this date and the caprecs for the date have been marked as 'shift changed' then delete and recreate
					// caprecs to use new shift setup
					if (!bAreThereAnyScheduledJobsForThisResourceDate && hasShiftSetupChangedForResourceDate(dCurDate, sResourceType, sResourceID)) {
                    	deleteCapRecsForThisDate(sResourceType, sResourceID, dCurDate);
                        bCreateCaprecs = true;
					}
					else if (nFreeMinutesThisDate > 0) {
                        if (dSoonestDateAnyTimeIsAvail == null) {
                            dSoonestDateAnyTimeIsAvail = dCurDate;
                        }
                        
                        // if we have a single free block that can handle the whole milestone then we dont need the buffer anymore.
                        // sl-29622 - if dCurDate == dStartProcessDate then the nLongestSingleFreeBlock test is not sufficient as we are likely not dealing with a full day 
    					if (nLongestSingleFreeBlock > nNumMinutesNeeded && dCurDate != dStartProcessDate) {
    						nNumMinutesNeededWithBuffer = nNumMinutesNeeded;
    					}
                    }
                    // sl-27565 - if we dont have any nFreeMinutesThisDate, but we have bAvailEx, then we need to recreate caprecs
                    // for this date to create caprec for avail exception  
                    // sl-27565 - there was a problem which has since been resolved, but left a data problem. because shift_id wasnt being 
                    // set for break caprecs in processShift() deleteShiftFreeResourceDayCapacityRecs->getShiftFreeResourceDayCapacityRecs() 
                    // wasnt deleting these break caprecs when they should. added code here ('< 1440') to detect and delete them   
                    else if (!bAreThereAnyScheduledJobsForThisResourceDate && (bAvailEx || getTotalMinsForResourceDate(dCurDate, sResourceType, sResourceID) < 1440)) {
                    	deleteCapRecsForThisDate(sResourceType, sResourceID, dCurDate);
                        bCreateCaprecs = true;
                    }
                }
                else{
                    bCreateCaprecs = true;
                }
                
                if (bCreateCaprecs) {
					if (bAvailEx) {
						createResourceCapacityRecord(sResourceType, sResourceID, dCurDate, 0, 1440, rShift);
					}
					else {
	                    // create shift cap rec - unavail exceps will be created within this
	                    createResourceCapacityRecord(sResourceType, sResourceID, dCurDate,
	                        rShiftDet.shiftdet_starttime_num_mins, rShiftDet.shiftdet_endtime_num_mins, rShift,
	                        rShiftDet.shiftdet_id);

	                    // create non-shift cap rec for before shift starts - avail exceps will be created within this
	                    if (rShiftDet.shiftdet_starttime_num_mins > 0) {
	                        createResourceCapacityRecord(sResourceType, sResourceID, dCurDate,
	                            0, rShiftDet.shiftdet_starttime_num_mins, rShift);
	                    }

	                    // create non-shift cap rec for after shift ends - avail exceps will be created within this
	                    if (rShiftDet.shiftdet_endtime_num_mins < 1440) {
	                        createResourceCapacityRecord(sResourceType, sResourceID, dCurDate,
	                            rShiftDet.shiftdet_endtime_num_mins, 1440, rShift);
	                    }
					}

					nFreeMinutesThisDate = getFreeCapacityForDate(dCurDate, sResourceType, sResourceID, nCapacityRequired);

                    if (dSoonestDateAnyTimeIsAvail == null && nFreeMinutesThisDate > 0) {
                        dSoonestDateAnyTimeIsAvail = dCurDate;
                    }
                }

                if (nFreeMinutesThisDate > 0) {
                    if (dLastDayTimeAvail) {
                        if (sDirection == 'F' && areThereAnyScheduledJobsBetweenDates(sResourceType, sResourceID, dLastDayTimeAvail, dCurDate)) {
                            nFreeMinutes = 0;
                        }
                        else if (sDirection == 'B' && areThereAnyScheduledJobsBetweenDates(sResourceType, sResourceID, dCurDate, dLastDayTimeAvail)) {
                            nFreeMinutes = 0;
                        }
                    }

                    dLastDayTimeAvail = dCurDate;
                    nFreeMinutes += nFreeMinutesThisDate;
                }
                
                // sl-29656 - if there are scheduled jobs today (and we havent hit our target yet) then reset nFreeMinutes as we wont be able to use any time up to 
                // and including today, as we cant stop and start this job if another job is on the equipment.  
    			if (bAreThereAnyScheduledJobsForThisResourceDate && nFreeMinutes > 0 && nFreeMinutes < nNumMinutesNeeded) {
    				nFreeMinutes = 0;
    			}
            }
            // no shift det works today
            else if (nCapacityRecs == 0) {
                createResourceCapacityRecord(sResourceType, sResourceID, dCurDate, 0, 1440, rShift);
            }
            
            // just making sure cap rec processing has been done for this date - set nFreeMinutes = nNumMinutesNeededWithBuffer so it exits loop
            if (bJustDoThisDate) {
                nFreeMinutes = nNumMinutesNeededWithBuffer;
            }

            if (sDirection == 'F') {
                dCurDate = plugins.DateUtils.addDays(dCurDate, 1);
            }
            else {
            	// can't schedule into past
				if (dCurDate < dNow) {
					break;
				}
				
                dCurDate = plugins.DateUtils.addDays(dCurDate, -1);
            }

            nWhileCount++;
        }
    }

    oReturn.dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail; 
    oReturn.nFreeMinutes = nFreeMinutes;
    
    return oReturn;
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"DED9F290-3750-40A2-A9D3-CB3A050BD6EE"}
 */
function isToday(dDate) {
	var bIsToday = false;
	
	if (dDate) {
	    var dNow = application.getTimeStamp();
	    var sNow = dNow.getFullYear().toString() + dNow.getMonth().toString() + dNow.getDate().toString();
	    var sDate = dDate.getFullYear().toString() + dDate.getMonth().toString() + dDate.getDate().toString();
	    
	    bIsToday = (sNow == sDate);
	}
	
	return bIsToday;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_shift>} rShift
 * @param {Date} dCurDate
 * @param {Boolean} [bIncludeNonShiftDays]
 * 
 * @return {JSRecord<db:/avanti/sch_shift_detail>}
 *
 * @properties={typeid:24,uuid:"2DBC7AD7-406B-46E8-A06A-128F57F14BBF"}
 */
function getShiftDetailThatWorksThisWeekDay(rShift, dCurDate, bIncludeNonShiftDays) {
	/**@type {JSRecord<db:/avanti/sch_shift_detail>} */
    var rShiftDet = null;
    var iDay = dCurDate.getDay();
    var sDateSQL = scopes.avDate.getDateSQl(dCurDate, true);
    var sSQL = "SELECT shiftdet_id \
                FROM sch_shift_detail \
                WHERE org_id = ? AND shift_id = ? \
                AND shiftdet_startdate <= " + sDateSQL + " \
                AND (shiftdet_enddate >= " + sDateSQL + " OR shiftdet_enddate is null) ";
    var aArgs = [globals.org_id, rShift.shift_id.toString()];
    
	if (!bIncludeNonShiftDays) {
	    switch (iDay) {
	        case 0:
	            sSQL += "AND shiftdet_flg_sunday = 1";
	            break;
	        case 1:
	            sSQL += "AND shiftdet_flg_monday = 1";
	            break;
	        case 2:
	            sSQL += "AND shiftdet_flg_tuesday = 1";
	            break;
	        case 3:
	            sSQL += "AND shiftdet_flg_wednesday = 1";
	            break;
	        case 4:
	            sSQL += "AND shiftdet_flg_thursday = 1";
	            break;
	        case 5:
	            sSQL += "AND shiftdet_flg_friday = 1";
	            break;
	        case 6:
	            sSQL += "AND shiftdet_flg_saturday = 1";
	            break;
	    }
	}
    
    rShiftDet = scopes.avDB.getRecFromSQL(sSQL, 'sch_shift_detail', aArgs);
    
    return rShiftDet;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_shift>} rShift
 * @param {Date} dCurDate
 * @param {String} sDirection - (F)orwards or (B)ackwards
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"57E97CFF-84BF-400C-84BA-EC2D40A2037F"}
 */
function areThereAnyShiftDetailsThatWorkForDateOnwards(rShift, dCurDate, sDirection) {
	var bOK = false;
	
	if (rShift && dCurDate && (sDirection == "F" || sDirection == "B")) {
	    var sDateSQL = scopes.avDate.getDateSQl(dCurDate, true);
	    var sSQL = "SELECT COUNT(shiftdet_id) \
	                FROM sch_shift_detail \
	                WHERE \
	                	shift_id = ? \
	                	AND (shiftdet_flg_sunday = 1 OR shiftdet_flg_monday = 1 OR shiftdet_flg_tuesday = 1 OR shiftdet_flg_wednesday = 1 \
	                		OR shiftdet_flg_thursday = 1 OR shiftdet_flg_friday = 1 OR shiftdet_flg_saturday = 1) ";
	
		if (sDirection == "F") {
			sSQL += "AND (shiftdet_enddate >= " + sDateSQL + " OR shiftdet_enddate IS NULL)";
		}
		else if (sDirection == "B") {
			sSQL += "AND shiftdet_startdate <= " + sDateSQL + "";
		}

		bOK = (scopes.avDB.SQLQuery(sSQL, null, [rShift.shift_id.toString()]) > 0);
	}

	return bOK;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sys_employee>} rEmp
 * @param {Number} nNumMinutesNeeded
 * @param {Date} dCurDateTime
 * @param {Number} nCapacityRequired
 * @param {String} sDirection - (F)orwards or (B)ackwards
 * @param {Boolean} [bJustDoThisDate]
 *
 * @return {Date} - dSoonestDateAnyTimeIsAvail
 *
 * @properties={typeid:24,uuid:"884E252E-F23E-412B-B248-435F10623697"}
 */
function createEmployeeCapacityRecords(rEmp, nNumMinutesNeeded, dCurDateTime, nCapacityRequired, sDirection, bJustDoThisDate) {
    var nFreeMinutes = 0;
    var dSoonestDateAnyTimeIsAvail = null;
    
    /**@type {JSRecord<db:/avanti/sch_shift>} */
    var rShift;
    var dCurDate = scopes.avDate.copyDate(dCurDateTime, true);
    /**@type {{dSoonestDateAnyTimeIsAvail:Date, nFreeMinutes:Number}} */
    var oShift = {}; 
    
    if(!nCapacityRequired && nCapacityRequired != 0){
        nCapacityRequired = 100;
    }

    if (sDirection == 'F') {
        rEmp.sys_employee_to_sch_employee_shift.sort('sequence_nr asc');
    }
    else {
        rEmp.sys_employee_to_sch_employee_shift.sort('sequence_nr desc');
    }

    for (var s = 1; s <= rEmp.sys_employee_to_sch_employee_shift.getSize(); s++) {
        var rEmpShift = rEmp.sys_employee_to_sch_employee_shift.getRecord(s);

        if (rEmpShift.start_date <= dCurDate && ( rEmpShift.end_date >= dCurDate || rEmpShift.end_date == null )) {
            if (utils.hasRecords(rEmpShift.sch_employee_shift_to_sch_shift)) {
                rShift = rEmpShift.sch_employee_shift_to_sch_shift.getRecord(1);
                oShift = processShift(rShift, nNumMinutesNeeded, dCurDate, RESOURCE_TYPE.Employee, rEmp.empl_id, sDirection, nCapacityRequired, bJustDoThisDate);
                
                if (oShift.nFreeMinutes) {
                    nFreeMinutes += oShift.nFreeMinutes;
                }
                if (!dSoonestDateAnyTimeIsAvail && oShift.dSoonestDateAnyTimeIsAvail) {
                    dSoonestDateAnyTimeIsAvail = oShift.dSoonestDateAnyTimeIsAvail;
                }
                
                if (nFreeMinutes >= nNumMinutesNeeded) {
                    break;
                }
            }
        }
    }

    if (nFreeMinutes >= nNumMinutesNeeded) {
        return dSoonestDateAnyTimeIsAvail;
    }
    else {
        return null;
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_shift_detail>} rShiftDet
 * @param {Date} dCurDate
 *
 * @return
 * @properties={typeid:24,uuid:"B6B4AD23-741B-4D8F-9F08-970E4D55A7F8"}
 */
function isShiftDetailValid(rShiftDet, dCurDate) {
    var bDateRangeValid = rShiftDet.shiftdet_startdate && rShiftDet.shiftdet_startdate <= dCurDate && ( rShiftDet.shiftdet_enddate >= dCurDate || rShiftDet.shiftdet_enddate == null );

    var bTimeRangeValid = rShiftDet.shiftdet_starttime && rShiftDet.shiftdet_endtime && rShiftDet.shiftdet_starttime_num_mins != null && rShiftDet.shiftdet_endtime_num_mins != null && rShiftDet.shiftdet_endtime_num_mins > rShiftDet.shiftdet_starttime_num_mins && rShiftDet.shiftdet_duration > 0;

    var nNumWeekDaysAvail = rShiftDet.shiftdet_flg_sunday + rShiftDet.shiftdet_flg_monday + rShiftDet.shiftdet_flg_tuesday + rShiftDet.shiftdet_flg_wednesday + rShiftDet.shiftdet_flg_thursday + rShiftDet.shiftdet_flg_friday + rShiftDet.shiftdet_flg_saturday;

    return bDateRangeValid && bTimeRangeValid && nNumWeekDaysAvail > 0;
}

/**
 * @param {String} sResourceType - EQ, EMP
 * @param {UUID} uResourceID
 * @param {Date} dCurDate
 * @param {Number} nStartMins
 * @param {Number} nEndMins
 * @param {JSRecord<db:/avanti/sch_shift>} rShift
 * @param {UUID} [uShiftDetID]
 *
 * @properties={typeid:24,uuid:"C1C37FDE-C265-4DAF-BB42-7A2DD417CD4A"}
 */
function createResourceCapacityRecord(sResourceType, uResourceID, dCurDate, nStartMins, nEndMins, rShift, uShiftDetID) {
    var dStartDate = scopes.avDate.addMinutesNoDSTChange(dCurDate, nStartMins);
    var dEndDate = scopes.avDate.addMinutesNoDSTChange(dCurDate, nEndMins);
    
    /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
    var rEmpCapacity = scopes.avDB.newRecord('sch_emp_capacity');
    var nExAvailableFlag;

    if (sResourceType == RESOURCE_TYPE.Employee) {
        rEmpCapacity.empl_id = uResourceID;
    }
    else if (sResourceType == RESOURCE_TYPE.Equipment) {
        rEmpCapacity.equip_id = uResourceID;
    }

    // we only schedule to mins
    dStartDate.setSeconds(0, 0);
    dEndDate.setSeconds(0, 0);

    rEmpCapacity.schedule_date_num = scopes.avDate.getDateAsNum(dCurDate);
    rEmpCapacity.start_date = dStartDate;
    rEmpCapacity.end_date = dEndDate;
    rEmpCapacity.starttime_num_mins = nStartMins;
    rEmpCapacity.endtime_num_mins = nEndMins;
    rEmpCapacity.duration = nEndMins - nStartMins;
    rEmpCapacity.shift_id = rShift.shift_id;

    // shift time
    if (uShiftDetID) {
        rEmpCapacity.shiftdet_id = uShiftDetID;
        rEmpCapacity.capacity = 100;
        nExAvailableFlag = 0; // look for unavail exceptions
    }
    // non-shift time
    else {
        rEmpCapacity.capacity = 0;
        rEmpCapacity.is_break = 1;
        rEmpCapacity.is_shift_break = 1;
        nExAvailableFlag = 1; // look for avail exceptions
    }

    databaseManager.saveData(rEmpCapacity);

    processExceptions(rShift, rEmpCapacity, nExAvailableFlag);
}

/**
 * @param {Date} dDate
 * @param {String} sResourceType - EQ, EMP
 * @param {UUID} uResourceID
 * @param {Number} [nCapacityRequired]
 *
 * @return
 * @properties={typeid:24,uuid:"DE545AE1-65E8-45AF-9DF0-D7CCAA082452"}
 */
function getNumExistingCapacityRecsForDate(dDate, sResourceType, uResourceID, nCapacityRequired) {
	var nDate = scopes.avDate.getDateAsNum(dDate);
    var sSQL = "select count(*) \
                from sch_emp_capacity \
                where \
                	org_id = ? \
                	and schedule_date_num = ?";
    var aArgs = [globals.org_id, nDate, uResourceID.toString()];

    if (sResourceType == RESOURCE_TYPE.Employee) {
        sSQL += " and empl_id = ?";
    }
    else if (sResourceType == RESOURCE_TYPE.Equipment) {
        sSQL += " and equip_id = ?";
    }

    if (nCapacityRequired) {
        sSQL += " and (capacity >= ? or isnull(is_break, 0) = 1)";
        aArgs.push(nCapacityRequired);
    }

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @param {Date} dDate
 * @param {String} sResourceType - EQ, EMP
 * @param {UUID} uResourceID
 * @param {Number} nCapacityRequired
 * @param {UUID} [uGroupByOrderID] - used when we ValidateLoadBalanced on a group by press order
 * @param {Boolean} [bGetLongestSingleFreeBlock]
 *
 * @return
 * @properties={typeid:24,uuid:"1EEAE3C8-B46A-4355-9853-0D5098522B6B"}
 */
function getFreeCapacityForDate(dDate, sResourceType, uResourceID, nCapacityRequired, uGroupByOrderID, bGetLongestSingleFreeBlock) {
	var sOperation = bGetLongestSingleFreeBlock ? "MAX" : "SUM";
    var sSQL = "SELECT " + sOperation + "(duration) \
                FROM sch_emp_capacity \
                WHERE \
                    org_id = ? \
                    AND schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);
    var aArgs = [globals.org_id.toString(), uResourceID.toString(), nCapacityRequired];

    if (sResourceType == RESOURCE_TYPE.Employee) {
        sSQL += " AND empl_id = ?";
    }
    else if (sResourceType == RESOURCE_TYPE.Equipment) {
        sSQL += " AND equip_id = ?";
    }

    if (uGroupByOrderID) {
        var sOrderMSIDs = getOrderMSIDs(uGroupByOrderID);
        sSQL += " AND (capacity >= ? OR equip_ms_id IN (" + sOrderMSIDs + "))";
    }
    else {
        sSQL += " AND capacity >= ?";
    }
    
    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {String} sResourceType - EQ, EMP
 * @param {UUID} uResourceID
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"E0A2F356-30C6-4CBF-8A91-EC8A561769D0"}
 */
function getTotalMinsForResourceDate(dDate, sResourceType, uResourceID) {
    var sSQL = "SELECT SUM(duration) \
                FROM sch_emp_capacity \
                WHERE \
                    org_id = ? \
                    AND schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);
    var aArgs = [globals.org_id.toString(), uResourceID.toString()];

    if (sResourceType == RESOURCE_TYPE.Employee) {
        sSQL += " AND empl_id = ?";
    }
    else if (sResourceType == RESOURCE_TYPE.Equipment) {
        sSQL += " AND equip_id = ?";
    }

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public 
 * 
 * @param {Date} dDate
 * @param {String} sResourceType - EQ, EMP
 * @param {UUID} uResourceID
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"F6132463-59BF-4D6F-92E6-B2D58F2C7C2E"}
 */
function hasShiftSetupChangedForResourceDate(dDate, sResourceType, uResourceID) {
    var sSQL = "SELECT SUM(shift_setup_changed) \
                FROM sch_emp_capacity \
                WHERE \
                    org_id = ? \
                    AND schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);
    var aArgs = [globals.org_id.toString(), uResourceID.toString()];

    if (sResourceType == RESOURCE_TYPE.Employee) {
        sSQL += " AND empl_id = ?";
    }
    else if (sResourceType == RESOURCE_TYPE.Equipment) {
        sSQL += " AND equip_id = ?";
    }

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public 
 * 
 * @param {UUID} [uOrderID]
 *
 * @return
 * @properties={typeid:24,uuid:"D9D2AB65-04B3-4045-AAC1-B89A3B4D4CE4"}
 */
function getOrderMSIDs(uOrderID) {
    var sSQL = "SELECT DISTINCT ms.ms_id \
                FROM sch_milestone ms \
                INNER JOIN sch_schedule sch ON ms.sch_id = sch.sch_id \
                INNER JOIN prod_job j ON sch.job_id = j.job_id \
                WHERE \
                    ms.org_id = ? \
                    AND j.ordh_id = ?";
    var aArgs = [globals.org_id.toString(), uOrderID.toString()];
    var aMSIDs = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);
    var sMSIDs = scopes.avText.arrayToString(aMSIDs, ",", "'");
    
    return sMSIDs;
}

/**
 * @param {JSRecord<db:/avanti/sch_shift>} rShift
 * @param {JSRecord<db:/avanti/sch_emp_capacity>} rEmpCapacity
 * @param {Number} nAvailable
 *
 * @properties={typeid:24,uuid:"9737E933-B53A-466A-B8A1-E9ACD10D6A3B"}
 */
function processExceptions(rShift, rEmpCapacity, nAvailable) {
    var sSQL;
    var aArgs = [];
    var nDate = scopes.avDate.getDateAsNum(rEmpCapacity.start_date);

    var sShiftExSQL = "select starttime_num_mins, endtime_num_mins, shiftexcep_id, 'S', duration \
                        from sch_shift_excep \
                        where org_id = ? and shift_id = ? and isnull(shiftexcep_avail, 0) = ? and duration > 0 \
                        and starttime_num_mins is not null and endtime_num_mins is not null \
                        and shiftexcep_startdate_num is not null and " + nDate + " >= shiftexcep_startdate_num \
                        and (" + nDate + " <= shiftexcep_enddate_num or shiftexcep_enddate_num is null) \
                        and (starttime_num_mins < ? and endtime_num_mins > ?)";
    var aShiftExArgs = [globals.org_id.toString(), rShift.shift_id.toString(), nAvailable,
        rEmpCapacity.endtime_num_mins, rEmpCapacity.starttime_num_mins];

    if (rEmpCapacity.empl_id) {
        var sEmpExSQL = "select starttime_num_mins, endtime_num_mins, empschexcep_id, 'EM', duration \
             from sch_empl_excep \
             where org_id = ? and empl_id = ? and isnull(empschexcep_avail, 0) = ? and duration > 0 \
             and starttime_num_mins is not null and endtime_num_mins is not null \
             and empschexcep_start_num is not null and " + nDate + " = empschexcep_start_num \
             and (starttime_num_mins < ? and endtime_num_mins > ?)";
        var aEmpExArgs = [globals.org_id.toString(), rEmpCapacity.empl_id.toString(), nAvailable,
            rEmpCapacity.endtime_num_mins, rEmpCapacity.starttime_num_mins];

        sSQL = sShiftExSQL + " union " + sEmpExSQL + " order by duration desc";
        aArgs = aShiftExArgs.concat(aEmpExArgs);
    }
    else if (rEmpCapacity.equip_id) {
        var sEquipExSQL = "select starttime_num_mins, endtime_num_mins, equipschexcep_id, 'EQ', duration \
             from sch_equip_excep \
             where org_id = ? and equip_id = ? and isnull(equipschexcep_avail, 0) = ? and duration > 0 \
             and starttime_num_mins is not null and endtime_num_mins is not null \
             and equipschexcep_start_num is not null and " + nDate + " = equipschexcep_start_num \
             and (starttime_num_mins < ? and endtime_num_mins > ?)";
        var aEquipExArgs = [globals.org_id.toString(), rEmpCapacity.equip_id.toString(), nAvailable,
            rEmpCapacity.endtime_num_mins, rEmpCapacity.starttime_num_mins];
        
        sSQL = sShiftExSQL + " union " + sEquipExSQL + " order by duration desc";
        aArgs = aShiftExArgs.concat(aEquipExArgs);
    }

    var aException = scopes.avDB.SQLQueryReturnMultiColArray(sSQL, aArgs, 5);

    if (aException && aException.length == 5) {
        /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
        var rFrontRemainderBlock = null;
        /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
        var rBackRemainderBlock = null;
        // set exception dates to rEmpCapacity.start_date
        var dExStartDateTime = rEmpCapacity.start_date;
        dExStartDateTime.setHours(0, 0, 0);
        var dExEndDateTime = scopes.avDate.addMinutesNoDSTChange(dExStartDateTime, aException[1]);
        dExStartDateTime = scopes.avDate.addMinutesNoDSTChange(dExStartDateTime, aException[0]);

        // we only schedule to mins
        dExStartDateTime.setSeconds(0, 0);
        dExEndDateTime.setSeconds(0, 0);

        if (dExStartDateTime <= rEmpCapacity.start_date && dExEndDateTime >= rEmpCapacity.end_date) {
            // shift wholly contained within exception - no remainders
        }
        else {
            // exception starts after shift starts - create front remainder time block
            if (dExStartDateTime > rEmpCapacity.start_date) {
                rFrontRemainderBlock = scopes.avDB.duplicateRecord(rEmpCapacity);

                rFrontRemainderBlock.end_date = dExStartDateTime;
                rFrontRemainderBlock.endtime_num_mins = scopes.avDate.getNumMinutesFromDate(dExStartDateTime);
                rFrontRemainderBlock.duration = scopes.avDate.getDiffInMinutes(rFrontRemainderBlock.start_date,
                    rFrontRemainderBlock.end_date);

                databaseManager.saveData(rFrontRemainderBlock);

                // now call this func again with the remainder to see if there are any exceptions within it
                processExceptions(rShift, rFrontRemainderBlock, nAvailable);
            }

            // exception ends before shift ends - create back remainder time block
            if (dExEndDateTime < rEmpCapacity.end_date) {
                rBackRemainderBlock = scopes.avDB.duplicateRecord(rEmpCapacity);

                rBackRemainderBlock.start_date = dExEndDateTime;
                rBackRemainderBlock.starttime_num_mins = scopes.avDate.getNumMinutesFromDate(dExEndDateTime);
                rBackRemainderBlock.duration = scopes.avDate.getDiffInMinutes(rBackRemainderBlock.start_date,
                    rBackRemainderBlock.end_date);

                databaseManager.saveData(rBackRemainderBlock);

                // now call this func again with the remainder to see if there are any exceptions within it
                processExceptions(rShift, rBackRemainderBlock, nAvailable);
            }
        }

        // rEmpCapacity becomes the exception
        rEmpCapacity.shiftdet_id = null;
        rEmpCapacity.is_shift_break = null;

        if (aException[3] == 'S') {
            rEmpCapacity.shiftexcep_id = aException[2];
        }
        else if (aException[3] == RESOURCE_TYPE.Employee) {
            rEmpCapacity.empschexcep_id = aException[2];
        }
        else if (aException[3] == RESOURCE_TYPE.Equipment) {
            rEmpCapacity.equipschexcep_id = aException[2];
        }

        if (nAvailable) {
            rEmpCapacity.is_break = 0;
            rEmpCapacity.capacity = 100;
            rEmpCapacity.shift_id = rShift.shift_id;
        }
        else {
            rEmpCapacity.is_break = 1;
            rEmpCapacity.capacity = 0;
        }

        // if remainders created then update start and end time of rEmpCapacity
        if (rFrontRemainderBlock) {
            rEmpCapacity.start_date = dExStartDateTime;
            rEmpCapacity.starttime_num_mins = scopes.avDate.getNumMinutesFromDate(dExStartDateTime);
        }
        if (rBackRemainderBlock) {
            rEmpCapacity.end_date = dExEndDateTime;
            rEmpCapacity.endtime_num_mins = scopes.avDate.getNumMinutesFromDate(dExEndDateTime);
        }

        if (rFrontRemainderBlock || rBackRemainderBlock) {
            rEmpCapacity.duration = scopes.avDate.getDiffInMinutes(rEmpCapacity.start_date,
                rEmpCapacity.end_date);
        }

        databaseManager.saveData(rEmpCapacity);
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_shift_detail>} rShiftDet
 * @param {String} sWeekDay
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"BE6C0196-71D0-47BC-A7E6-E8B5F3FB3011"}
 */
function isWeekDayAvailableInShiftDet(rShiftDet, sWeekDay) {
    switch (sWeekDay) {
        case 'Sun':
            return rShiftDet.shiftdet_flg_sunday == 1;
            break;
        case 'Mon':
            return rShiftDet.shiftdet_flg_monday == 1;
            break;
        case 'Tue':
            return rShiftDet.shiftdet_flg_tuesday == 1;
            break;
        case 'Wed':
            return rShiftDet.shiftdet_flg_wednesday == 1;
            break;
        case 'Thu':
            return rShiftDet.shiftdet_flg_thursday == 1;
            break;
        case 'Fri':
            return rShiftDet.shiftdet_flg_friday == 1;
            break;
        case 'Sat':
            return rShiftDet.shiftdet_flg_saturday == 1;
    }

    return false;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return
 * @properties={typeid:24,uuid:"AB0F74FB-8A1C-4AB9-84AF-AE2EC2655E42"}
 */
function getEmpCapacityRequired(rMilestone) {
    var nCapacityRequired = 100;

    if (utils.hasRecords(rMilestone.sch_milestone_to_sys_cost_centre)) {
        if (rMilestone.sch_milestone_to_sys_cost_centre.emp_workload_pct != null) {
            nCapacityRequired = rMilestone.sch_milestone_to_sys_cost_centre.emp_workload_pct;
        }
    }
    else if (utils.hasRecords(rMilestone.sch_milestone_to_sys_operation_category)) {
        if (rMilestone.sch_milestone_to_sys_operation_category.emp_workload_pct != null) {
            nCapacityRequired = rMilestone.sch_milestone_to_sys_operation_category.emp_workload_pct;
        }
    }

    return nCapacityRequired;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} uEmpID
 * @param {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} oEquipmentWindow
 * @param {Number} [nCapacityOver]
 * @param {Boolean} [bSplit]
 *
 * @return {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}}
 *
 * @properties={typeid:24,uuid:"B948FF5D-B669-45AD-AE23-1B743A43F971"}
 */
function getEarliestAdequateEmployeeWindow(rMilestone, uEmpID, oEquipmentWindow, nCapacityOver, bSplit) {
    /**@type {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}} */
    var oEmployeeWindow = { };
    /**@type {JSRecord<db:/avanti/sys_employee>} */
    var rEmp = scopes.avDB.getRec('sys_employee', ['empl_id'], [uEmpID]);
    var nCapacity = nCapacityOver ? nCapacityOver : rMilestone.emp_capacity_required;
    var nCapacityMins = 0;
    var bShiftBreak = false;
    var dSoonestDateAnyTimeIsAvail = createEmployeeCapacityRecords(rEmp, rMilestone.ms_time_budget, oEquipmentWindow.startDate, nCapacity, 'F');
    var dStartDate = oEquipmentWindow.startDate;
    var dDateSQL = scopes.avDate.getDateTimeSQl(dStartDate, "M", true);

    // make sure sufficient Employee Capacity Records exist to schedule this milestone
    if (!dSoonestDateAnyTimeIsAvail) {
        return oEmployeeWindow;
    }
    
    oEmployeeWindow.dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail;

    // we might have any time avail for a couple months - bypass all intervening time
    if (dSoonestDateAnyTimeIsAvail > dStartDate) {
        dStartDate = dSoonestDateAnyTimeIsAvail;
    }

    /**@type {Date} */
    var dEmpStartDate = null;
    var dEmpEndDate = null;
    var sSQL = "select ec_id \
                from sch_emp_capacity \
                where org_id = ? and empl_id = ? and end_date > " + dDateSQL + "\
                and (capacity >= ? or isnull(is_break, 0) = 1)";
    var aArgs = [globals.org_id.toString(), uEmpID.toString(), nCapacity];

    if (oEquipmentWindow.blockedDate) {
        sSQL += " and start_date < " + scopes.avDate.getDateTimeSQl(oEquipmentWindow.blockedDate, "M", true);
    }

    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
    var fsEmpCapacity = scopes.avDB.getFSFromSQL(sSQL, 'sch_emp_capacity', aArgs);

    /**@type {Array<JSRecord<db:/avanti/sch_emp_capacity>>} */
    var aBlockChain = [];
    var nTimeStillNeeded = rMilestone.ms_time_budget;
    dStartDate = oEquipmentWindow.startDate;

    if (fsEmpCapacity.getSize() > 0) {
        fsEmpCapacity.sort('start_date asc');

        for (var i = 1; i <= fsEmpCapacity.getSize(); i++) {
            var rTimeBlock = fsEmpCapacity.getRecord(i);
            var dEndDate;

            if (dStartDate > rTimeBlock.start_date) {
                dEndDate = scopes.avDate.addMinutesNoDSTChange(dStartDate, nTimeStillNeeded);
            }
            else {
                dEndDate = scopes.avDate.addMinutesNoDSTChange(rTimeBlock.start_date, nTimeStillNeeded);
            }

            // this puts us past the end of equipment window - emp cant finish in this time period, exit
            if (oEquipmentWindow.blockedDate && dEndDate > oEquipmentWindow.blockedDate) {
                break;
            }

            // if we have a chain and these blocks arent contiguous break the chain and start over
            if (aBlockChain.length > 0) {
                var rPrevTimeBlock = aBlockChain[aBlockChain.length - 1];

                if (rTimeBlock.start_date != rPrevTimeBlock.end_date) {
                    aBlockChain = [];
                    nTimeStillNeeded = rMilestone.ms_time_budget;
                    bShiftBreak = false;
                    
                    // now that we have discarded previous time blocks we have to do this test again to make sure we havent gone past the end of the equipment window
                    if (dStartDate > rTimeBlock.start_date) {
                        dEndDate = scopes.avDate.addMinutesNoDSTChange(dStartDate, nTimeStillNeeded);
                    }
                    else {
                        dEndDate = scopes.avDate.addMinutesNoDSTChange(rTimeBlock.start_date, nTimeStillNeeded);
                    }

                    // this puts us past the end of equipment window - emp cant finish in this time period, exit
                    if (oEquipmentWindow.blockedDate && dEndDate > oEquipmentWindow.blockedDate) {
                        break;
                    }
                }
            }

            if (rTimeBlock.is_break) {
                // break cant be fst block in the chain
                if (aBlockChain.length > 0) {
                    aBlockChain.push(rTimeBlock);

                    if (rTimeBlock.is_shift_break) {
                        if (bSplit) {
                            aBlockChain.pop();
                        }
                        else {
                            bShiftBreak = true;
                        }
                    }
                }

                // if this is a break we dont need to go any further - we cant finish a ms in a break
                if (! ( bSplit && rTimeBlock.is_shift_break ) || aBlockChain.length == 0) {
                    continue;
                }
            }
            else {
                // we always have at least 1 block (current one) in chain
                aBlockChain.push(rTimeBlock);
            }

            if (!rTimeBlock.is_shift_break) {
                var nDuration = rTimeBlock.duration;

                if (dStartDate > rTimeBlock.start_date) {
                    nDuration = Math.floor(scopes.avDate.getDiffInMinutes(dStartDate, rTimeBlock.end_date));

                    // could be a fraction of a minute - we arent goign to start this ms with 30 sec left in this block
                    if (nDuration <= 0) {
                        aBlockChain.pop();
                        continue;
                    }
                }
            }

            // we can finish it in this time block
            if (nDuration >= nTimeStillNeeded || ( rTimeBlock.is_shift_break && bSplit )) {
                var rFstTimeBlock = aBlockChain[0];
                var rLastTimeBlock = aBlockChain[aBlockChain.length - 1];

                dEmpStartDate = dStartDate > rFstTimeBlock.start_date ? dStartDate : rFstTimeBlock.start_date;
                var dLastTimeBlockStartTime = dStartDate > rLastTimeBlock.start_date ? dStartDate : rLastTimeBlock.start_date;

                if (bSplit) {
                    if (rTimeBlock.is_shift_break) {
                        dEmpEndDate = rLastTimeBlock.end_date;
                    }
                    else {
                        dEmpEndDate = scopes.avDate.addMinutesNoDSTChange(dLastTimeBlockStartTime, nTimeStillNeeded);
                    }
                }
                else {
                    dEmpEndDate = scopes.avDate.addMinutesNoDSTChange(dLastTimeBlockStartTime, nTimeStillNeeded);
                }

                nCapacityMins = updateUsedTimeBlocks(aBlockChain, dEmpStartDate, dEmpEndDate, rMilestone.ms_id, RESOURCE_TYPE.Employee, 'F', nCapacity);

                if (!rTimeBlock.is_shift_break) {
                    nTimeStillNeeded = 0;
                }

                break;
            }
            // we can work on the ms in this block, but not finish. if the next block is contiguous we can continue there.
            // but we need to string enough contiguous block together to finsh the ms
            else {
                nTimeStillNeeded -= nDuration;
            }
        }
    }

    oEmployeeWindow.empID = uEmpID;
    oEmployeeWindow.startDate = dEmpStartDate;
    oEmployeeWindow.endDate = dEmpEndDate;
    oEmployeeWindow.capacity = nCapacityMins;
    oEmployeeWindow.bContainsShiftBreak = bShiftBreak;
    oEmployeeWindow.nTimeStillNeeded = nTimeStillNeeded;

    return oEmployeeWindow;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} uEmpID
 * @param {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} oEquipmentWindow
 * @param {Number} [nCapacityOver]
 * @param {Boolean} [bSplit]
 *
 * @return {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}}
 *
 * @properties={typeid:24,uuid:"E9FF15F7-951F-4ACC-902A-6A3974B865C7"}
 */
function getLatestAdequateEmployeeWindow(rMilestone, uEmpID, oEquipmentWindow, nCapacityOver, bSplit) {
    /**@type {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}} */
    var oEmployeeWindow = { };
    /**@type {JSRecord<db:/avanti/sys_employee>} */
    var rEmp = scopes.avDB.getRec('sys_employee', ['empl_id'], [uEmpID]);
    var nCapacity = nCapacityOver ? nCapacityOver : rMilestone.emp_capacity_required;
    var nCapacityMins = 0;
    var bShiftBreak = false;
    var dEndDate = oEquipmentWindow.endDate;
    var dDueDateStart = scopes.avDate.copyDate(dEndDate, true);
    var dSoonestDateAnyTimeIsAvail = createEmployeeCapacityRecords(rEmp, rMilestone.ms_time_budget, oEquipmentWindow.endDate, nCapacity, 'B');

    // make sure sufficient Employee Capacity Records exist to schedule this milestone
    if (!dSoonestDateAnyTimeIsAvail) {
        return oEmployeeWindow;
    }
    
    oEmployeeWindow.dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail;

    // we might have any time avail for a couple months - bypass all intervening time.
    // dSoonestDateAnyTimeIsAvail is just a date (no time), so beginning of day. free spot could anytime 
    // during that day. so add 1 day to make it midnight before doing comparison.  
    dSoonestDateAnyTimeIsAvail = plugins.DateUtils.addDays(dSoonestDateAnyTimeIsAvail, 1);
    if (dSoonestDateAnyTimeIsAvail < dDueDateStart) {
        dEndDate = dSoonestDateAnyTimeIsAvail;
    }

    /**@type {Date} */
    var dEmpStartDate = null;
    var dEmpEndDate = null;
    var dDateSQL = scopes.avDate.getDateTimeSQl(dEndDate, "M", true);
    var sSQL = "select ec_id \
                from sch_emp_capacity \
                where org_id = ? and empl_id = ? and start_date < " + dDateSQL + " \
                and (capacity >= ? or isnull(is_break, 0) = 1)";
    var aArgs = [globals.org_id.toString(), uEmpID.toString(), nCapacity];

    if (oEquipmentWindow.blockedDate) {
        sSQL += " and end_date > " + scopes.avDate.getDateTimeSQl(oEquipmentWindow.blockedDate, "M", true);
    }

    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
    var fsEmpCapacity = scopes.avDB.getFSFromSQL(sSQL, 'sch_emp_capacity', aArgs);

    /**@type {Array<JSRecord<db:/avanti/sch_emp_capacity>>} */
    var aBlockChain = [];
    var nTimeStillNeeded = rMilestone.ms_time_budget;
    dEndDate = oEquipmentWindow.endDate;

    if (fsEmpCapacity.getSize() > 0) {
        fsEmpCapacity.sort('end_date desc');

        for (var i = 1; i <= fsEmpCapacity.getSize(); i++) {
            var rTimeBlock = fsEmpCapacity.getRecord(i);
            var dStartDate;

            if (dEndDate < rTimeBlock.end_date) {
                dStartDate = scopes.avDate.addMinutesNoDSTChange(dEndDate, -nTimeStillNeeded);
            }
            else {
                dStartDate = scopes.avDate.addMinutesNoDSTChange(rTimeBlock.end_date, -nTimeStillNeeded);
            }

            // this puts us past the start of resource window - emp cant finish in this time period, exit
            if (oEquipmentWindow.blockedDate && dStartDate < oEquipmentWindow.blockedDate) {
                break;
            }

            // if we have a chain and these blocks arent contiguous break the chain and start over
            if (aBlockChain.length > 0) {
                var rNextTimeBlock = aBlockChain[aBlockChain.length - 1];

                if (rTimeBlock.end_date != rNextTimeBlock.start_date) {
                    aBlockChain = [];
                    nTimeStillNeeded = rMilestone.ms_time_budget;
                    bShiftBreak = false;
                }
            }

            if (rTimeBlock.is_break) {
                // break cant be fst block in the chain
                if (aBlockChain.length > 0) {
                    aBlockChain.push(rTimeBlock);
                }

                if (rTimeBlock.is_shift_break) {
                    bShiftBreak = true;
                }

                // if this is a break we dont need to go any further - we cant finish a ms in a break
                continue;
            }
            else {
                // we always have at least 1 block (current one) in chain
                aBlockChain.push(rTimeBlock);
            }

            var nDuration = rTimeBlock.duration;

            if (dEndDate < rTimeBlock.end_date) {
                nDuration = Math.floor(scopes.avDate.getDiffInMinutes(rTimeBlock.start_date, dEndDate));

                // could be a fraction of a minute - we arent goign to start this ms with 30 sec left in this block
                if (nDuration <= 0) {
                    aBlockChain.pop();
                    continue;
                }
            }

            // we can finish it in this time block
            if (nDuration >= nTimeStillNeeded) {
                var rLastTimeBlock = aBlockChain[0];
                var rFstTimeBlock = aBlockChain[aBlockChain.length - 1];

                dEmpEndDate = dEndDate < rLastTimeBlock.end_date ? dEndDate : rLastTimeBlock.end_date;
                var dFirstTimeBlockEndTime = dEndDate < rFstTimeBlock.end_date ? dEndDate : rFstTimeBlock.end_date;
                dEmpStartDate = scopes.avDate.addMinutesNoDSTChange(dFirstTimeBlockEndTime, -nTimeStillNeeded);

                nCapacityMins = updateUsedTimeBlocks(aBlockChain, dEmpStartDate, dEmpEndDate, rMilestone.ms_id, RESOURCE_TYPE.Employee, 'B', nCapacity);

                break;
            }
            // we can work on the ms in this block, but not finish. if the next block is contiguous we can continue there.
            // but we need to string enough contiguous block together to finsh the ms
            else {
                nTimeStillNeeded -= nDuration;
            }
        }
    }

    oEmployeeWindow.empID = uEmpID;
    oEmployeeWindow.startDate = dEmpStartDate;
    oEmployeeWindow.endDate = dEmpEndDate;
    oEmployeeWindow.capacity = nCapacityMins;
    oEmployeeWindow.bContainsShiftBreak = bShiftBreak;

    return oEmployeeWindow;
}

/**
 * @param {UUID} uMSID
 * @param {UUID} uEmpID
 * @param {UUID} uECID
 * @param {Number} nSeqNum
 *
 * @properties={typeid:24,uuid:"04204361-3F05-47F3-83C8-205ACD1C3566"}
 */
function createMSEmpCapacityRec(uMSID, uEmpID, uECID, nSeqNum) {
    var sSQL = "insert into sch_ms_emp_capacity (msec_id, org_id, ms_id, empl_id, ec_id, sequence_nr) \
               values (?, ?, ?, ?, ?, ?)";
    var aArgs = [application.getUUID().toString(), globals.org_id.toString(), uMSID.toString(),
        uEmpID.toString(), uECID.toString(), nSeqNum];
    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dStartDate
 *
 * @return {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}}
 *
 * @properties={typeid:24,uuid:"C724BC44-82EC-425A-8085-A3FD0FD5E747"}
 */
function getEarliestAdequateEquipmentWindow(rMilestone, rEquip, dStartDate) {
	/**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow = { };
    var dSoonestDateAnyTimeIsAvail = null;
    var dSoonestDateAnyTimeIsAvail1 = createEquipmentCapacityRecords(rEquip, rMilestone.ms_time_budget, dStartDate, 'F');
    
	if (rMilestone.ms_is_lag && rMilestone.equip2_id) {
	    /**@type {JSRecord<db:/avanti/eq_equipment>} */
		var rEquip2 = scopes.avDB.getRec("eq_equipment", ["equip_id"], [rMilestone.equip2_id]);
		
		if (rEquip2) {
		    var dSoonestDateAnyTimeIsAvail2 = createEquipmentCapacityRecords(rEquip2, rMilestone.ms_time_budget, dStartDate, 'F');
		}
	}

	if (dSoonestDateAnyTimeIsAvail1 && dSoonestDateAnyTimeIsAvail2) {
		if (dSoonestDateAnyTimeIsAvail1 < dSoonestDateAnyTimeIsAvail2) {
			dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail1;
		}
		else {
			dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail2;
		}
	}
	else if (dSoonestDateAnyTimeIsAvail1) {
		dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail1;
	}
	else if (dSoonestDateAnyTimeIsAvail2) {
		dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail2;
	}
	
    // make sure sufficient Equipment Capacity Records exist to schedule this milestone
    if (!dSoonestDateAnyTimeIsAvail) {
        return oEquipmentWindow;
    }

    // we might have any time avail for a couple months - bypass all intervening time
    if (dSoonestDateAnyTimeIsAvail > dStartDate) {
        dStartDate = dSoonestDateAnyTimeIsAvail;
    }

    /**@type {Date} */
    var dEquipStartDate = null;
    var dEquipEndDate = null;
    var dDateSQL = scopes.avDate.getDateTimeSQl(dStartDate, "M", true);
    var sEquips = "('" + rEquip.equip_id.toString() + "')";
    var b2Equip = false;
    
	if (dSoonestDateAnyTimeIsAvail2) {
		sEquips = "('" + rEquip.equip_id.toString() + "', '" + rMilestone.equip2_id.toString() + "')";
		b2Equip = true;
	}
    
    var sSQL = "select ec_id \
                from sch_emp_capacity \
                where org_id = ? and equip_id in " + sEquips + " and end_date > " + dDateSQL + " \
                and (capacity = 100 or isnull(is_break, 0) = 1)";
    var aArgs = [globals.org_id.toString()];
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
    var fsEquipCapacity = scopes.avDB.getFSFromSQL(sSQL, 'sch_emp_capacity', aArgs);

    /**@type {Array<JSRecord<db:/avanti/sch_emp_capacity>>} */
    var aBlockChain = [];
    var nTimeStillNeeded = rMilestone.ms_time_budget;
    var bAddedTimeBlock = false;

    if (fsEquipCapacity.getSize() > 0) {
        fsEquipCapacity.sort('start_date asc');

        for (var i = 1; i <= fsEquipCapacity.getSize(); i++) {
            var rTimeBlock = fsEquipCapacity.getRecord(i);

            // if we have a chain and these blocks arent contiguous break the chain and start over - unless we are scheduling lag on 2 equip
            if (aBlockChain.length > 0) {
                var rPrevTimeBlock = aBlockChain[aBlockChain.length - 1];

                if (rTimeBlock.start_date != rPrevTimeBlock.end_date && !b2Equip) {
                    aBlockChain = [];
                    nTimeStillNeeded = rMilestone.ms_time_budget;
                    
					if (!oEquipmentWindow.nextJobDate) {
						oEquipmentWindow.nextJobDate = rPrevTimeBlock.end_date;
					}
                }
            }

            if (rTimeBlock.is_break) {
                // break cant be fst block in the chain
                if (aBlockChain.length > 0) {
                    aBlockChain.push(rTimeBlock);
                }
                // ms start time falls in break, note this. we dont want to display InsufficientTimeAvailable msg in this case 
                else if (bValidatingScheduleBoardMove && !bAddedTimeBlock) {
                    sMoveFailReason = MOVE_FAIL_REASON.StartOutsideOfShift;
                }

                // if this is a break we dont need to go any further - we cant finish a ms in a break
                continue;
            }
            else {
                // we always have at least 1 block (current one) in chain
                aBlockChain.push(rTimeBlock);
                bAddedTimeBlock = true;
            }

            var nDuration = rTimeBlock.duration;

            if (dStartDate > rTimeBlock.start_date) {
                nDuration = Math.floor(scopes.avDate.getDiffInMinutes(dStartDate, rTimeBlock.end_date));

                // could be a fraction of a minute - we arent goign to start this ms with 30 sec left in this block
                if (nDuration <= 0) {
                    aBlockChain.pop();
                    continue;
                }
            }

            // we can finish it in this time block
            if (nDuration >= nTimeStillNeeded) {
                var rFstTimeBlock = aBlockChain[0];
                var rLastTimeBlock = aBlockChain[aBlockChain.length - 1];

                dEquipStartDate = dStartDate > rFstTimeBlock.start_date ? dStartDate : rFstTimeBlock.start_date;
                var dLastTimeBlockStartTime = dStartDate > rLastTimeBlock.start_date ? dStartDate : rLastTimeBlock.start_date;
                dEquipEndDate = scopes.avDate.addMinutesNoDSTChange(dLastTimeBlockStartTime, nTimeStillNeeded);

                // lag doesnt block the schedule
				if (!rMilestone.ms_is_lag) {
	                updateUsedTimeBlocks(aBlockChain, dEquipStartDate, dEquipEndDate, rMilestone.ms_id, RESOURCE_TYPE.Equipment, 'F');
				}

                break;
            }
            // we can work on the ms in this block, but not finish. if the next block is contiguous we can continue there.
            // but we need to string enough contiguous block together to finsh the ms
            else {
                nTimeStillNeeded -= nDuration;
                
                // sl-28902 - have to change dStartDate when time needed consumed if we have 2 equip or it may consider the same time for the 2 diff equip caprecs  
				if (b2Equip) {
					dStartDate = scopes.avDate.addMinutesNoDSTChange(dStartDate, nDuration);
				}
            }
        }
    }

    oEquipmentWindow.startDate = dEquipStartDate;
    oEquipmentWindow.endDate = dEquipEndDate;

    if (oEquipmentWindow.startDate && oEquipmentWindow.endDate) {
        oEquipmentWindow.blockedDate = getNextBlockedDate(rEquip.equip_id, dEquipEndDate);
    }

    return oEquipmentWindow;
}

/**
 * @param {UUID} uEquipID
 * @param {Date} dFromDate
 *
 * @return
 * @properties={typeid:24,uuid:"58B553B2-1A9A-4BB0-AC5A-0DA74613DCC8"}
 */
function getNextBlockedDate(uEquipID, dFromDate) {
    var dDateSQL = scopes.avDate.getDateTimeSQl(dFromDate, "M", true);
	var sSQL = "select equipsch_start \
                from sch_equip_schedule \
                where org_id = ? and equip_id = ? and equipsch_start > " + dDateSQL;
    var aArgs = [globals.org_id, uEquipID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @param {UUID} uEquipID
 * @param {Date} dFromDate
 *
 * @return
 * @properties={typeid:24,uuid:"17785756-3C53-4DB6-BD81-8E29B133B0B2"}
 */
function getPrevBlockedDate(uEquipID, dFromDate) {
    var dDateSQL = scopes.avDate.getDateTimeSQl(dFromDate, "M", true);
    var sSQL = "select equipsch_end \
                from sch_equip_schedule \
                where org_id = ? and equip_id = ? and equipsch_end < " + dDateSQL;
    var aArgs = [globals.org_id, uEquipID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dEndDate
 *
 * @return {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}}
 *
 * @properties={typeid:24,uuid:"18F6F695-4B95-401D-8BE5-FE2886655E59"}
 */
function getLatestAdequateEquipmentWindow(rMilestone, rEquip, dEndDate) {
    /**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow = { };
    var dDueDateStart = scopes.avDate.copyDate(dEndDate, true);
    var dSoonestDateAnyTimeIsAvail = null;
    var dSoonestDateAnyTimeIsAvail1 = createEquipmentCapacityRecords(rEquip, rMilestone.ms_time_budget, dEndDate, 'B');

	if (rMilestone.ms_is_lag && rMilestone.equip2_id) {
	    /**@type {JSRecord<db:/avanti/eq_equipment>} */
		var rEquip2 = scopes.avDB.getRec("eq_equipment", ["equip_id"], [rMilestone.equip2_id]);
		
		if (rEquip2) {
		    var dSoonestDateAnyTimeIsAvail2 = createEquipmentCapacityRecords(rEquip2, rMilestone.ms_time_budget, dEndDate, 'B');
		}
	}

	if (dSoonestDateAnyTimeIsAvail1 && dSoonestDateAnyTimeIsAvail2) {
		if (dSoonestDateAnyTimeIsAvail1 < dSoonestDateAnyTimeIsAvail2) {
			dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail1;
		}
		else {
			dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail2;
		}
	}
	else if (dSoonestDateAnyTimeIsAvail1) {
		dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail1;
	}
	else if (dSoonestDateAnyTimeIsAvail2) {
		dSoonestDateAnyTimeIsAvail = dSoonestDateAnyTimeIsAvail2;
	}
	
    // make sure sufficient Equipment Capacity Records exist to schedule this milestone
    if (!dSoonestDateAnyTimeIsAvail) {
        return oEquipmentWindow;
    }

    // we might not have any time avail for a couple months - bypass all intervening time
    // dSoonestDateAnyTimeIsAvail is just a date (no time), so beginning of day. free spot could be any time 
    // during that day. so add 1 day to make it midnight before doing comparison.  
    dSoonestDateAnyTimeIsAvail = plugins.DateUtils.addDays(dSoonestDateAnyTimeIsAvail, 1);
    if (dSoonestDateAnyTimeIsAvail < dDueDateStart) {
        dEndDate = dSoonestDateAnyTimeIsAvail;
    }

    /**@type {Date} */
    var dEquipStartDate = null;
    var dEquipEndDate = null;
    var dDateSQL = scopes.avDate.getDateTimeSQl(dEndDate, "M", true);
    var sEquips = "('" + rEquip.equip_id.toString() + "')";
    var b2Equip = false;
    
	if (dSoonestDateAnyTimeIsAvail2) {
		sEquips = "('" + rEquip.equip_id.toString() + "', '" + rMilestone.equip2_id.toString() + "')";
		b2Equip = true;
	}
    
    var sSQL = "select ec_id \
                from sch_emp_capacity \
                where org_id = ? and equip_id in " + sEquips + " and start_date < " + dDateSQL + " \
                and (capacity = 100 or isnull(is_break, 0) = 1)";
    var aArgs = [globals.org_id.toString()];
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
    var fsEquipCapacity = scopes.avDB.getFSFromSQL(sSQL, 'sch_emp_capacity', aArgs);

    /**@type {Array<JSRecord<db:/avanti/sch_emp_capacity>>} */
    var aBlockChain = [];
    var nTimeStillNeeded = rMilestone.ms_time_budget;

    if (fsEquipCapacity.getSize() > 0) {
        fsEquipCapacity.sort('end_date desc');

        for (var i = 1; i <= fsEquipCapacity.getSize(); i++) {
            var rTimeBlock = fsEquipCapacity.getRecord(i);

            // if we have a chain and these blocks arent contiguous break the chain and start over - unless we are scheduling lag on 2 equip
            if (aBlockChain.length > 0) {
                var rNextTimeBlock = aBlockChain[aBlockChain.length - 1];

                if (rTimeBlock.end_date != rNextTimeBlock.start_date && !b2Equip) {
                    aBlockChain = [];
                    nTimeStillNeeded = rMilestone.ms_time_budget;
                    
					if (!oEquipmentWindow.nextJobDate) {
						oEquipmentWindow.nextJobDate = rNextTimeBlock.start_date;
					}
                }
            }

            if (rTimeBlock.is_break) {
                // break cant be fst block in the chain
                if (aBlockChain.length > 0) {
                    aBlockChain.push(rTimeBlock);
                }

                // if this is a break we dont need to go any further - we cant finish a ms in a break
                continue;
            }
            else {
                // we always have at least 1 block (current one) in chain
                aBlockChain.push(rTimeBlock);
            }

            var nDuration = rTimeBlock.duration;

            if (dEndDate < rTimeBlock.end_date) {
                nDuration = Math.floor(scopes.avDate.getDiffInMinutes(rTimeBlock.start_date, dEndDate));

                // could be a fraction of a minute - we arent going to start this ms with 30 sec left in this block
                if (nDuration <= 0) {
                    aBlockChain.pop();
                    continue;
                }
            }

            // we can finish it in this time block
            if (nDuration >= nTimeStillNeeded) {
                var rLastTimeBlock = aBlockChain[0];
                var rFstTimeBlock = aBlockChain[aBlockChain.length - 1];

                dEquipEndDate = dEndDate < rLastTimeBlock.end_date ? dEndDate : rLastTimeBlock.end_date;
                var dFirstTimeBlockEndTime = dEndDate < rFstTimeBlock.end_date ? dEndDate : rFstTimeBlock.end_date;
                dEquipStartDate = scopes.avDate.addMinutesNoDSTChange(dFirstTimeBlockEndTime, -nTimeStillNeeded);

                // lag doesnt block the schedule
				if (!rMilestone.ms_is_lag) {
	                updateUsedTimeBlocks(aBlockChain, dEquipStartDate, dEquipEndDate, rMilestone.ms_id, RESOURCE_TYPE.Equipment, 'B');
				}

                break;
            }
            // we can work on the ms in this block, but not finish. if the next block is contiguous we can continue there.
            // but we need to string enough contiguous block together to finsh the ms
            else {
                nTimeStillNeeded -= nDuration;
                
                // sl-28902 - have to change dEndDate when time needed consumed if we have 2 equip or it may consider the same time for the 2 diff equip caprecs  
				if (b2Equip) {
					dEndDate = scopes.avDate.addMinutesNoDSTChange(dEndDate, -nDuration);
				}
            }
        }
    }

    oEquipmentWindow.startDate = dEquipStartDate;
    oEquipmentWindow.endDate = dEquipEndDate;

    if (oEquipmentWindow.startDate && oEquipmentWindow.endDate) {
        oEquipmentWindow.blockedDate = getPrevBlockedDate(rEquip.equip_id, dEquipStartDate);
    }

    return oEquipmentWindow;
}

/**
 * @param {Array<JSRecord<db:/avanti/sch_emp_capacity>>} aBlockChain
 * @param {Date} dStartDate
 * @param {Date} dEndDate
 * @param {UUID} uMsID
 * @param {String} sResourceType - EQ or EM
 * @param {String} sDirection - F or B
 * @param {Number} [nCapacity]
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"58EEF799-6606-4922-8EBC-2B14BCC1F17F"}
 */
function updateUsedTimeBlocks(aBlockChain, dStartDate, dEndDate, uMsID, sResourceType, sDirection, nCapacity) {
    var rFstTimeBlock;
    var rLastTimeBlock;
    var r2ndLastTimeBlock;
    var r2ndFirstTimeBlock;
    var nCapacityMins = 0;

    // we only schedule to mins
    dStartDate.setSeconds(0, 0);
    dEndDate.setSeconds(0, 0);
    
    if (sDirection == 'F') {
        rFstTimeBlock = aBlockChain[0];
        rLastTimeBlock = aBlockChain[aBlockChain.length - 1];
        if (aBlockChain.length > 1) {
            r2ndLastTimeBlock = aBlockChain[aBlockChain.length - 2];

        }
    }
    else {
        rLastTimeBlock = aBlockChain[0];
        rFstTimeBlock = aBlockChain[aBlockChain.length - 1];
        if (aBlockChain.length > 1) {
        	r2ndFirstTimeBlock = aBlockChain[aBlockChain.length - 2];
        }
    }

    // if dStartDate > the fst block start then we need to create a front remainder
    if (dStartDate > rFstTimeBlock.start_date) {
        // fst see if there is a prev time block with the same capacity, and if so extend its end date
        // rather than creating a new remainder block
        /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
        var rFrontRemainderBlock = getContiguousTimeBlock(rFstTimeBlock, 'prev', true);

        if (!rFrontRemainderBlock) {
            rFrontRemainderBlock = scopes.avDB.duplicateRecord(rFstTimeBlock);
        }

        rFrontRemainderBlock.end_date = dStartDate;
        rFrontRemainderBlock.endtime_num_mins = scopes.avDate.getNumMinutesFromDate(rFrontRemainderBlock.end_date);
        rFrontRemainderBlock.duration = scopes.avDate.getDiffInMinutes(rFrontRemainderBlock.start_date, rFrontRemainderBlock.end_date);

        // update start date, duration on first block
        rFstTimeBlock.start_date = dStartDate;
        rFstTimeBlock.starttime_num_mins = scopes.avDate.getNumMinutesFromDate(rFstTimeBlock.start_date);
        rFstTimeBlock.duration = scopes.avDate.getDiffInMinutes(rFstTimeBlock.start_date, rFstTimeBlock.end_date);

        databaseManager.saveData(rFrontRemainderBlock);
        databaseManager.saveData(rFstTimeBlock);
    }

    // create capacity rec for back remainder of time block not used by this ms
    if (dEndDate < rLastTimeBlock.end_date) {
        /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
        var rBackRemainderBlock = null;

        // fst see if the last time block has the the same capacity as the 2nd last, and if so extend the
        // last time block start date rather than creating a new remainder block
        // sl-14696 - added schedule_date_num condition  - both time blocks must have the same schedule_date_num 
        // in order to extend 2nd last time block end time
        if (sDirection == 'F'
        	&& r2ndLastTimeBlock 
            && rLastTimeBlock.capacity == r2ndLastTimeBlock.capacity 
            && rLastTimeBlock.is_break == r2ndLastTimeBlock.is_break 
            && rLastTimeBlock.schedule_date_num == r2ndLastTimeBlock.schedule_date_num) {
                    
            rBackRemainderBlock = rLastTimeBlock;
            rLastTimeBlock = r2ndLastTimeBlock;
            aBlockChain.pop();
        }
        // logic has to be different for Backwards sched - but same idea 
        else if (sDirection == 'B'
	    	&& r2ndFirstTimeBlock 
	        && rFstTimeBlock.capacity == r2ndFirstTimeBlock.capacity 
	        && rFstTimeBlock.is_break == r2ndFirstTimeBlock.is_break 
	        && rFstTimeBlock.schedule_date_num == r2ndFirstTimeBlock.schedule_date_num) {
	                
        	rFrontRemainderBlock = rFstTimeBlock;
        	rFstTimeBlock = r2ndFirstTimeBlock;
	        aBlockChain.pop();
	    }

        if (!rBackRemainderBlock) {
            rBackRemainderBlock = scopes.avDB.duplicateRecord(rLastTimeBlock);
        }

        rBackRemainderBlock.start_date = dEndDate;
        rBackRemainderBlock.starttime_num_mins = scopes.avDate.getNumMinutesFromDate(rBackRemainderBlock.start_date);
        rBackRemainderBlock.duration = scopes.avDate.getDiffInMinutes(rBackRemainderBlock.start_date, rBackRemainderBlock.end_date)

        // update end date, duration on last block
        rLastTimeBlock.end_date = dEndDate;
        rLastTimeBlock.endtime_num_mins = scopes.avDate.getNumMinutesFromDate(rLastTimeBlock.end_date);
        rLastTimeBlock.duration = scopes.avDate.getDiffInMinutes(rLastTimeBlock.start_date, rLastTimeBlock.end_date);

        databaseManager.saveData(rBackRemainderBlock);
        databaseManager.saveData(rLastTimeBlock);
    }
    
    // update capacity on all blocks in the chain
    if (aBlockChain.length > 0) {
        for (var b = 0; b < aBlockChain.length; b++) {
            // no need to modify break blocks - they dont have capacity
            if (!aBlockChain[b].is_break) {
                if (sResourceType == RESOURCE_TYPE.Equipment) {
                    aBlockChain[b].equip_ms_id = uMsID;
                    aBlockChain[b].capacity = 0;
                }
                else {
                    createMSEmpCapacityRec(uMsID, aBlockChain[b].empl_id, aBlockChain[b].ec_id, b + 1);
                    nCapacityMins += ( aBlockChain[b].capacity * aBlockChain[b].duration );
                    aBlockChain[b].capacity -= nCapacity;
                    
                    if (aBlockChain[b].capacity < 0) {
                        aBlockChain[b].capacity = 0;
                    }
                    else if (aBlockChain[b].capacity > 100) {
                        aBlockChain[b].capacity = 100;
                    }
                }

                databaseManager.saveData(aBlockChain[b]);
            }
        }
    }

    return nCapacityMins;
}

/**
 * @param {JSRecord<db:/avanti/sch_emp_capacity>} rTimeBlock
 * @param {String} sNextOrPrev - next or prev
 * @param {Boolean} [bWithSameCapacity]
 *
 * @return {JSRecord<db:/avanti/sch_emp_capacity>}
 *
 * @properties={typeid:24,uuid:"2BCFF7C9-1D4F-4FB0-882A-4A1685CC5F9C"}
 */
function getContiguousTimeBlock(rTimeBlock, sNextOrPrev, bWithSameCapacity) {
    var sSQL = "select ec_id from sch_emp_capacity where org_id = ? ";
    var aArgs = [globals.org_id];
    var sStartDateSQL = scopes.avDate.getDateTimeSQl(rTimeBlock.start_date, "M", true);
    var sEndDateSQL = scopes.avDate.getDateTimeSQl(rTimeBlock.end_date, "M", true);

    if (sNextOrPrev == 'next') {
        sSQL += " and start_date = " + sEndDateSQL;
    }
    else if (sNextOrPrev == 'prev') {
        sSQL += " and end_date = " + sStartDateSQL;
    }

    if (rTimeBlock.empl_id) {
        sSQL += " and empl_id = ?";
        aArgs.push(rTimeBlock.empl_id.toString());
    }
    else if (rTimeBlock.equip_id) {
        sSQL += " and equip_id = ?";
        aArgs.push(rTimeBlock.equip_id.toString());
    }

    if (bWithSameCapacity) {
        sSQL += " and capacity = ? and is_break = ? and schedule_date_num = ? ";
        aArgs.push(rTimeBlock.capacity, rTimeBlock.is_break, rTimeBlock.schedule_date_num);
    }

    /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
    var rContiguousTimeBlock = scopes.avDB.getRecFromSQL(sSQL, 'sch_emp_capacity', aArgs);

    return rContiguousTimeBlock;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {String} sDirection - (F)orwards or (B)ackwards
 * @param {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} [oEquipmentWindowOver]
 * @param {Boolean} [bKeepExistingEmps]
 * @param {Boolean} [bSplit]
 * @param {Boolean} [bMustUseMSDates]
 * @param {Array<UUID>} [aEmpsOver]
 * @param {UUID} [uEquipIDOver]
 * @param {Boolean} [bDontScheduleEmps]
 * @param {Boolean} [bBypassBackupMSDates]
 * @param {Boolean} [bDontAllowShiftBreak]
 * @param {Boolean} [bKeepCurrentEquipment]
 * @param {Boolean} [bEmpChanged]
 * @param {Boolean} [bRecursiveCall]
 * @param {Boolean} [bBackwardSSLag]
 *
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"6AE36D3B-1982-46D1-B7DF-289ECE243B05"}
 */
function getNextFreeSpot(rMilestone, sDirection, oEquipmentWindowOver, bKeepExistingEmps, bSplit, bMustUseMSDates, aEmpsOver, uEquipIDOver, bDontScheduleEmps, bBypassBackupMSDates, bDontAllowShiftBreak, bKeepCurrentEquipment, bEmpChanged, bRecursiveCall, bBackwardSSLag) {
	scopes.avUtils.devOutput("getNextFreeSpot.rMilestone: " + rMilestone.ms_oper_name);	
	
	var rEquip;
    var dStartDate = rMilestone.ms_date_scheduled;
    var dEndDate = rMilestone.ms_date_due;
    /**@type {Date} */
    var dNextFreeTime = null;
    /**@type {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}} */
    var oEmployeeWindow = { };
    /**@type {Array<{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number}>} */
    var aoEmployeeWindows = [];
    var aChosenEmps = null;
    var dChosenStartTime = null;
    var dChosenEndTime = null;
    /**@type {Array<String>} */
    var aEmpTimes = [];
    var dNow = application.getTimeStamp();
    var dTodayStart = scopes.avDate.copyDate(dNow, true);
    var bGroupJobs = _bGroupJobs && rMilestone.group_jobs == 1;
    var dLastTime;
    var rOpCat;
    var nNumEmpsNeeded = 0;
    var bUseChosenEmps = false;
    var nDebugMilestone = 0;
    var nNumberOfDaysToScheduleAhead = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.NumberOfDaysToScheduleAhead);
    
    if (bBackwardSSLag) {
        globals["sScheduleDirection"] = "F";
        bMustUseMSDates = true;
    }
    
	if (rMilestone.sequence_nr == nDebugMilestone) {
		rMilestone.sequence_nr = rMilestone.sequence_nr; // for breakpoint 
	}
	
	if (bRecursiveCall) {
		nRecursiveGetNextFreeSpotCalls++;
	}
	else {
		nRecursiveGetNextFreeSpotCalls = 0;
	}

	// sl-26145 - this is to clear any temp Orphan Capacity Recs that may exist if the user shut the browser down while RTS.sceduleMilestones running 
	clearOrphanCapacityRecs();
	
	// SL-27565 - there were some Duplicate Non-Shift CapRecs, the underlying cause may have been resolved. clean up data here
	deleteDuplicateNonShiftCapRecs();
	
	////////////////////////////// STEP 1 - PREPARE MILESTONE FOR SCHEDULING //////////////////////////////
	
    if (_uValidateLoadBalancedEquipIDOverride && rMilestone.group_jobs == 1) {
        uEquipIDOver = _uValidateLoadBalancedEquipIDOverride;
    }

    if (uEquipIDOver) {
        rEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uEquipIDOver]);
        rOpCat = scopes.avDB.getRec('sys_operation_category', ['opcat_id'], [rEquip.opcat_id]);
    }
    else {
        rOpCat = rMilestone.sch_milestone_to_sys_operation_category.getRecord(1);
    }

    var bScheduleEmps = scopes.avUtils.getSysPrefNum('ScheduleEmployees') && rOpCat.opcat_schedule_employees == 1 && !bDontScheduleEmps && !rMilestone.ms_is_lag;
    var sRankOrBalance = rOpCat.emp_rank_or_balance == 'R' ? 'R' : 'B';
    
    // if dStartDate < today cant schedule in past so push date to now
    if (sDirection == 'F' && dStartDate < dTodayStart) {
        dStartDate = dNow;
        dEndDate = scopes.avDate.addMinutesNoDSTChange(dStartDate, rMilestone.ms_time_budget);
    }

    if (aEmpsOver) {
        aChosenEmps = aEmpsOver;

        // where aChosenEmps is used further down we make a distinction between null and []. null means chosen emps is off, [] means they have chosen zero emps
		if (aChosenEmps == null) {
			aChosenEmps = [];
		}
    }
    else if (bCommittingDetailSchedule) {
        aChosenEmps = getEmpsSelectedForMS(rMilestone.ms_id);

        // where aChosenEmps is used further down we make a distinction between null and []. null means chosen emps is off, [] means they have chosen zero emps
		if (aChosenEmps == null) {
			aChosenEmps = [];
		}
    }
    else if (bKeepExistingEmps) {
        aChosenEmps = getEmpsWorkingOnMS(rMilestone.ms_id);
    
        // where aChosenEmps is used further down we make a distinction between null and []. null means chosen emps is off, [] means they have chosen zero emps
		if (aChosenEmps == null) {
			aChosenEmps = [];
		}
    }

    rMilestone.emp_capacity_required = getEmpCapacityRequired(rMilestone);
    rMilestone.contains_shift_break = null;
    rMilestone.emp_changed = null;
    
    if(bInReadyToSchedule){
        rMilestone.scheduled_direction = sDirection;
    }

    // if we have aChosenEmps - then just clear the selected flag for all emps - will be reset for selected emps further down
    if(aChosenEmps && aChosenEmps.length > 0){
        clearSelectedMsAvailableEmpRecs(rMilestone.ms_id);
    }
    else{
        deleteMsAvailableEmpRecs(rMilestone.ms_id);
    }

    // uEquipIDOver used if changing ms equip in sched board
    if (uEquipIDOver) {
        rEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uEquipIDOver]);
    }
    else if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
        rEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
        
        if (bGroupJobs) {
            var uWorkPoolID = rEquip.eq_equipment_to_sch_workpool_machine.wrkpool_id;
            uEquipIDOver = rEquip.equip_id;
        }
        else if (bKeepCurrentEquipment) {
            uEquipIDOver = rEquip.equip_id;
        }
    }
    
	if (rEquip) {
		rMilestone.equip_id = rEquip.equip_id;
	}
    
    if (!bBypassBackupMSDates) {
        rMilestone.ms_date_scheduled_bak = rMilestone.ms_date_scheduled;
        rMilestone.ms_date_due_bak = rMilestone.ms_date_due;
        rMilestone.ms_time_budget_bak = rMilestone.ms_time_budget;
    }
    
    
    ////////////////////////////// STEP 2 - SCHEDULE EQUIPMENT //////////////////////////////
    
	if (nNumberOfDaysToScheduleAhead) {
		nMaxDaysToLookAheadInTheSchedule = nNumberOfDaysToScheduleAhead; 
	}
	else {
		nMaxDaysToLookAheadInTheSchedule = 365;		
	}
    
	if (rMilestone.sequence_nr == nDebugMilestone) scopes.avUtils.stopWatchStart("SCHEDULE EQUIPMENT");
    
    /**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow = { };

    if (oEquipmentWindowOver) {
        oEquipmentWindow = oEquipmentWindowOver;
    }
    else if (uEquipIDOver) {
        if (sDirection == 'F') {
            oEquipmentWindow = scopes.avScheduling.getEarliestAdequateEquipmentWindow(rMilestone, rEquip, dStartDate);

            // if bGroupJobs then all presses using this pool must run contiguously
            if (bGroupJobs && oEquipmentWindow) {
                if (_aWorkPoolSchedTimes[uWorkPoolID]) {
                    dLastTime = _aWorkPoolSchedTimes[uWorkPoolID];

                    if (oEquipmentWindow.startDate != dLastTime) {
                        // we allow breaks in between running of press, just not for other jobs
                        if (areThereAnyScheduledJobsBetweenDates(RESOURCE_TYPE.Equipment, uEquipIDOver, dLastTime, oEquipmentWindow.startDate, true)) {
                            rMilestone.ms_date_scheduled = null;
                            rMilestone.ms_date_due = null;
                            return null;
                        }
                    }
                }

                _aWorkPoolSchedTimes[uWorkPoolID] = oEquipmentWindow.endDate;
            }
        }
        else {
            oEquipmentWindow = scopes.avScheduling.getLatestAdequateEquipmentWindow(rMilestone, rEquip, dEndDate);

            // if bGroupJobs then all presses using this pool must run contiguously
            if (bGroupJobs && oEquipmentWindow) {
                if (_aWorkPoolSchedTimes[uWorkPoolID]) {
                    dLastTime = _aWorkPoolSchedTimes[uWorkPoolID];

                    if (oEquipmentWindow.endDate != dLastTime) {
                        // we allow breaks in between running of press, just not for other jobs
                        if (areThereAnyScheduledJobsBetweenDates(RESOURCE_TYPE.Equipment, uEquipIDOver, oEquipmentWindow.endDate, dLastTime, true)) {
                            rMilestone.ms_date_scheduled = null;
                            rMilestone.ms_date_due = null;
                            return null;
                        }
                    }
                }

                _aWorkPoolSchedTimes[uWorkPoolID] = oEquipmentWindow.startDate;
            }
        }
    }
    else if (rEquip) {
        var uOpCatIdBak = rMilestone.opcat_id;
        
        oEquipmentWindow = globals['scheduleUsingWorkPoolWorkFamily'](rMilestone, rEquip, dStartDate, dEndDate, sDirection);
        
        // if the opcat has changed because we switched to a diff equip in the pool then we need to reset bScheduleEmps + sRankOrBalance
        if (rMilestone.opcat_id != uOpCatIdBak) {
            rOpCat = rMilestone.sch_milestone_to_sys_operation_category.getRecord(1);
            bScheduleEmps = scopes.avUtils.getSysPrefNum('ScheduleEmployees') && rOpCat.opcat_schedule_employees == 1 && !bDontScheduleEmps;
            sRankOrBalance = rOpCat.emp_rank_or_balance == 'R' ? 'R' : 'B';
        }
    }
    // this ms doesnt use equip and emp sched is on - so we're just scheduling emps
    else if(scopes.avUtils.getSysPrefNum('ScheduleEmployees') && rOpCat.opcat_schedule_employees == 1){
        oEquipmentWindow.startDate = dStartDate;
        oEquipmentWindow.endDate = dEndDate;
        oEquipmentWindow.blockedDate = null;
    }
    else{
        scopes.avText.showWarning('msHasNoEquip', false, [rMilestone.ms_oper_name]);
        rMilestone.ms_date_scheduled = null;
        rMilestone.ms_date_due = null;
        return null;
    }
    
	if (bBackwardSSLag) {
        globals["sScheduleDirection"] = "B"; // switch it back to Backwards scheduling
	    
	    // if we are doing backwards scheduling and Start-Start lag and the desired start time isnt available then schedule backwards from nextJobDate.
	    // this will get us as close as we can to the ss lag.
	    if (oEquipmentWindow && oEquipmentWindow.startDate != dStartDate && oEquipmentWindow.nextJobDate && !bRecursiveCall) {
	        rMilestone.ms_date_due = oEquipmentWindow.nextJobDate;
			rMilestone.ms_date_scheduled = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, rMilestone.ms_time_budget * -1);
			return getNextFreeSpot(rMilestone, sDirection, null, null, null, null, null, null, null, true, null, null, null, true);
	    }
	}

    // couldnt find equipment window - probably shifts not setup or some validating for job grouping failed 
    if (!oEquipmentWindow || !oEquipmentWindow.startDate || !oEquipmentWindow.endDate) {
        if (!bInScheduleBoard) {
            rMilestone.ms_date_scheduled = null;
            rMilestone.ms_date_due = null;
        }

        return null;
    }

    if (bMustUseMSDates) {
        // if bMustUseMSDates then if we arrived at a diff date we have to return failure
        // sl-15040
        if((sDirection == 'F' && oEquipmentWindow.startDate != dStartDate)
            || (sDirection == 'B' && oEquipmentWindow.endDate != dEndDate)) {
            
            if (bInScheduleBoard) {
                rMilestone.ms_date_scheduled = rMilestone.ms_date_scheduled_bak;
                rMilestone.ms_date_due = rMilestone.ms_date_due_bak;
                
                if (bValidatingScheduleBoardMove && sMoveFailReason != MOVE_FAIL_REASON.StartOutsideOfShift) {
                    sMoveFailReason = MOVE_FAIL_REASON.InsufficientTimeAvailable;
                }
            }
            else {
                rMilestone.ms_date_scheduled = null;
                rMilestone.ms_date_due = null;
            }

            return null;
        }
        else {
            oEquipmentWindow.blockedDate = oEquipmentWindow.endDate;
        }
    }
    
	if (rMilestone.sequence_nr == nDebugMilestone) scopes.avUtils.devOutputTime("SCHEDULE EQUIPMENT");

	
	////////////////////////////// STEP 3 - SCHEDULE EMPLOYEES //////////////////////////////
    
	if (rMilestone.sequence_nr == nDebugMilestone) scopes.avUtils.stopWatchStart("SCHEDULE EMPLOYEES");
	
    if (bScheduleEmps && oEquipmentWindow.startDate && oEquipmentWindow.endDate) {
        nNumEmpsNeeded = 1;
    	
        // sl-16897 - i changed this condition to not check for aChosenEmps.length. if 'aChosenEmps != null' then we want to set nNumEmpsNeeded from length, even if zero. they may have 
        // removed all employees from the ms, and we want to maintain that. we dont want it to re-select an employee   
        // if we have specified emps use that for num needed - they may have manually added/removed emps
        if (aChosenEmps != null) {
            nNumEmpsNeeded = aChosenEmps.length;
            bUseChosenEmps = true;
        }
        // if no task then the milestone was manually added - just use 1 employee (set above) as we have no 
        // way of getting num helpers
        else if (utils.hasRecords(rMilestone.sch_milestone_to_sa_order_revds_task)) {
            var rTask = rMilestone.sch_milestone_to_sa_order_revds_task.getRecord(1);
            var rTaskQty = rTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(1);
            var nNumHelpers = 0;

            // used in rel in getNumHelpers_taskStd below
            globals.avSales_selectedRevisionDetailQtyUUID = rTaskQty.ordrevdqty_id;

            if (rTaskQty.ordrevdstqty_num_help_override != null) {
                nNumHelpers = rTaskQty.ordrevdstqty_num_help_override;
            }
            else if (scopes.avTask.isPressTask(rTask)) {
                nNumHelpers = scopes.avTask.getNumHelpers_taskStd(rTaskQty);
            }
            else {
                nNumHelpers = scopes.avTask.getNumHelpers(rTaskQty);
            }

            nNumEmpsNeeded += nNumHelpers;
        }
    }
    
    // even if nNumEmpsNeeded is zero we still have to do lookup up available emps to populate the emp selection dlg
    if (nNumEmpsNeeded > 0 || bUseChosenEmps) {
        var aEmpStartTimes = [];
        var aEmpEndTimes = [];
        var bBadEmpSetup = false;
        var aArgs = [rMilestone.opcat_id.toString(), globals.org_id, rMilestone.opcat_id.toString(), rMilestone.dept_id.toString()];

        // find emps that can work on this equip and that have shifts setup
        var sSQL = "SELECT DISTINCT ed.empl_id, ISNULL(oce.sequence_nr, 999) \
                    FROM sys_empl_dept ed \
                    INNER JOIN sch_employee_shift es ON es.empl_id = ed.empl_id \
                    INNER JOIN sys_employee e ON e.empl_id = ed.empl_id \
                    LEFT OUTER JOIN sys_op_cat_emps oce ON oce.empl_id = ed.empl_id AND oce.opcat_id = ? \
                    WHERE \
                        ed.org_id = ? \
                        AND e.empl_active = 1 \
                        AND e.empl_full_name IS NOT NULL \
                        AND (ed.opcat_id = ? OR (ed.dept_id = ? AND ed.opcat_id IS NULL)) ";

        // if we have passed in a list of emps we must consider only them
        if (aChosenEmps && aChosenEmps.length > 0) {
            var sEmps = "(" + scopes.avText.arrayToString(aChosenEmps, ',', "'") + ")";
            sSQL += " and ed.empl_id in " + sEmps;
        }

        var sDateClause = "";
        var sStartDateSQL = scopes.avDate.getDateTimeSQl(oEquipmentWindow.startDate, "M", true);
        var sEndDateSQL = scopes.avDate.getDateTimeSQl(oEquipmentWindow.endDate, "M", true);
        
        if (sDirection == 'F') {
            sDateClause = " and es.start_date <= " + sStartDateSQL + " and (es.end_date > " + sStartDateSQL + " or es.end_date is null) "; 
        }
        else {
            sDateClause = " and es.start_date <= " + sEndDateSQL + " and (es.end_date > " + sEndDateSQL + " or es.end_date is null) "; 
        }
        
        var sOrderBy = " order by isnull(oce.sequence_nr, 999) asc";
        var aEmps = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL + sDateClause + sOrderBy, aArgs);

        // there are enough emps that can potentially work on this ms
        if (aEmps && aEmps.length >= nNumEmpsNeeded) {
            /**@type {Array<UUID>} */
            var aFreeEmps = [];
            var e = 0;
            var nNumEmpsWithRequestedDate = 0;
            var nNumEmpsWithRequestedDateAndFullCapacity = 0;
            var nFullCapacityMins = rMilestone.ms_time_budget * 100;
            var bUseEquipDate = false;

            for (e = 0; e < aEmps.length; e++) {
                var uEmpID = aEmps[e];

                if (sDirection == 'F') {
                    oEmployeeWindow = getEarliestAdequateEmployeeWindow(rMilestone, uEmpID, oEquipmentWindow, null, bSplit);
                }
                else {
                    oEmployeeWindow = getLatestAdequateEmployeeWindow(rMilestone, uEmpID, oEquipmentWindow, null, bSplit);
                }
                
                if (oEmployeeWindow.startDate && oEmployeeWindow.endDate) {
                    aoEmployeeWindows.push(oEmployeeWindow);
                    aFreeEmps.push(uEmpID);
                    aEmpStartTimes.push(oEmployeeWindow.startDate.getTime());
                    aEmpEndTimes.push(oEmployeeWindow.endDate.getTime());

                    if (oEmployeeWindow.startDate == oEquipmentWindow.startDate && oEmployeeWindow.endDate == oEquipmentWindow.endDate) {

                        nNumEmpsWithRequestedDate++;

                        if (oEmployeeWindow.capacity == nFullCapacityMins) {
                            nNumEmpsWithRequestedDateAndFullCapacity++;
                        }
                    }

                    if (sRankOrBalance == 'R' && nNumEmpsWithRequestedDate == nNumEmpsNeeded) {
                        bUseEquipDate = true;
                    }
                    else if (sRankOrBalance == 'B' && nNumEmpsWithRequestedDateAndFullCapacity == nNumEmpsNeeded) {
                        bUseEquipDate = true;
                    }
                }
            }

            // we have enough emps - see if they can agree on a time
            if (aFreeEmps.length >= nNumEmpsNeeded) {
                // we have right num emps that can work at equip times
                if (bUseEquipDate) {
                    dChosenStartTime = oEquipmentWindow.startDate;
                    dChosenEndTime = oEquipmentWindow.endDate;
                }
                else {
                    // if forwards sched then sort emp end times as we arent concerned with who can
                    // start sooner, but who can end sooner. opposite for backwards sched
                    if (sDirection == 'F' && !bSplit) {
                        aEmpTimes = scopes.avUtils.mergeArrays(aEmpEndTimes, aEmpStartTimes, '|');
                    }
                    else {
                        aEmpTimes = scopes.avUtils.mergeArrays(aEmpStartTimes, aEmpEndTimes, '|');
                    }

                    aEmpTimes.sort();

                    // if going backwards its about who can start latest, rather than finish earliest (as in forwards)
                    if (sDirection == 'B') {
                        aEmpTimes.reverse();
                    }

                    // look thru time and see if we have enough emps that can agree on any time
                    for (e = 0; e < aEmpTimes.length; e++) {
                        if (scopes.avUtils.getNumElementsWithThisValue(aEmpTimes, aEmpTimes[e]) >= nNumEmpsNeeded) {
                            var aDates = aEmpTimes[e].split('|');

                            if (sDirection == 'F' && !bSplit) {
                                dChosenEndTime = new Date(parseInt(aDates[0]));
                                dChosenStartTime = new Date(parseInt(aDates[1]));
                            }
                            else {
                                dChosenStartTime = new Date(parseInt(aDates[0]));
                                dChosenEndTime = new Date(parseInt(aDates[1]));
                            }

                            break;
                        }
                    }

                    if (dChosenStartTime && dChosenEndTime) {
                        // if bMustUseMSDates then if we arrived at a diff date we have to return failure
                        if (bMustUseMSDates && ( dChosenStartTime != dStartDate || dChosenEndTime != dEndDate )) {
                            if (bInScheduleBoard) {
                                rMilestone.ms_date_scheduled = rMilestone.ms_date_scheduled_bak;
                                rMilestone.ms_date_due = rMilestone.ms_date_due_bak;
                            }
                            else {
                                rMilestone.ms_date_scheduled = null;
                                rMilestone.ms_date_due = null;
                            }

                            return null;
                        }
                    }
                }

                if (dChosenStartTime && dChosenEndTime) {
                    rMilestone.ms_date_scheduled = dChosenStartTime;
                    rMilestone.ms_date_due = dChosenEndTime;

                    if (sDirection == 'F') {
                        dNextFreeTime = rMilestone.ms_date_scheduled;
                    }
                    else {
                        dNextFreeTime = rMilestone.ms_date_due;
                    }

                    // loop thru emps to mark the ones that are using the chosen time
                    aFreeEmps = [];
                    
                    for (e = 0; e < aoEmployeeWindows.length; e++) {
                        oEmployeeWindow = aoEmployeeWindows[e];

                        var bDateMatches = oEmployeeWindow.startDate == rMilestone.ms_date_scheduled && oEmployeeWindow.endDate == rMilestone.ms_date_due;

                        if (bDateMatches) {
                            // these are the emps that can work at the chosen time
                            if(!(aChosenEmps && aChosenEmps.length > 0)){
                                createMsAvailableEmpsRec(rMilestone.ms_id, oEmployeeWindow.empID, rMilestone.emp_capacity_required);
                            }

                            // sql is sorted by emp rank - so, if using rank, as soon as we have the
                            // required num emps we can stop. if using balance then we push all free
                            // emps and sort them by capacity
                            if (nNumEmpsNeeded > 0 && (aFreeEmps.length < nNumEmpsNeeded || sRankOrBalance == 'B')) {
                                aFreeEmps.push(oEmployeeWindow.empID);
                            }
                        }
                    }

                    // if using Balance and we have more free emps than needed - we have to sort by capacity
                    if (sRankOrBalance == 'B' && aFreeEmps.length > nNumEmpsNeeded) {
                        var aFreeEmpCapacities = [];

                        for (e = 0; e < aFreeEmps.length; e++) {
                            var nEmpCapacity = getEmpCapacityForDateRange(aFreeEmps[e], dChosenStartTime, dChosenEndTime);

                            aFreeEmpCapacities.push(nEmpCapacity);
                        }

                        // this will sort emps by capacity and trim aFreeEmps to the required number
                        aFreeEmps = sortEmpsByCapacity(aFreeEmpCapacities, aFreeEmps, nNumEmpsNeeded);
                    }
                }
            }

            // at this point we have the emps selected in aFreeEmps
            
            // we didnt go with equip times - have to update equip cap recs - if equip used
            if (rEquip && aFreeEmps.length > 0 && !bUseEquipDate) {
                removeMSEquipCapacityRecs(rMilestone.ms_id);

                rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_start = rMilestone.ms_date_scheduled;
                rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_end = rMilestone.ms_date_due;
                
                // removed and put back
                var dStartBak = rMilestone.ms_date_scheduled;
                var dEndBak = rMilestone.ms_date_due;
                var dDurBak = rMilestone.ms_time_budget;

                updateEquipCapacityFromEmp(rMilestone, rEquip, aFreeEmps[0]);

                // updateEquipCapacityFromEmp sets dates on rMilestone - have to revert
                rMilestone.ms_date_scheduled = dStartBak;
                rMilestone.ms_date_due = dEndBak;
                rMilestone.ms_time_budget = dDurBak;                
            }
            
            var dEarliestDate = null;
            var nTimeStillNeeded = 0;

            for (e = 0; e < aoEmployeeWindows.length; e++) {
                oEmployeeWindow = aoEmployeeWindows[e];

                if (dChosenStartTime && dChosenEndTime) {
                    // have to revert capacity recs for emps we looked at but didnt use
                    if (aFreeEmps.indexOf(oEmployeeWindow.empID) == -1) {
                        removeMSEmpCapacityRecs(rMilestone.ms_id, oEmployeeWindow.empID, null, true);
                    }
                    else {
                        if (oEmployeeWindow.bContainsShiftBreak) {
                            if(bDontAllowShiftBreak){
                                rMilestone.ms_date_scheduled = null;
                                rMilestone.ms_date_due = null;
                                
                                return null;
                            }
                            else{
                                rMilestone.contains_shift_break = 1;
                            }
                        }

                        // mark this emp as selected in sch_ms_available_emps, used by clc col and emp dlg
                        /**@type {JSRecord<db:/avanti/sch_ms_available_emps>} */
                        var rMSEmp = scopes.avDB.getRec('sch_ms_available_emps', ['ms_id', 'empl_id'], [rMilestone.ms_id, oEmployeeWindow.empID], null, null, true);
                        
                        if (rMSEmp) {
                        	rMSEmp.is_selected = 1;
                        	databaseManager.saveData(rMSEmp);
                        	
                        }
                        
                        // if we've moved the ms from an opcat that doesnt schedule emps to one that does then there wont already be a sch_empl_schedule rec - have to create one.
                        // sl-17199 - added !bEmpChanged condition - we dont want to create a new sch_empl_schedule if moving a no-equip ms from one emp to another
                        if (bInScheduleBoard && !bEmpChanged && !isThereAnEmpSchedRec(rMilestone.ms_id, oEmployeeWindow.empID)) {
                            /**@type {JSRecord<db:/avanti/sch_empl_schedule>} */
                            var rEmpSched = scopes.avDB.newRecord('sch_empl_schedule');
                            
                            rEmpSched.ms_id = rMilestone.ms_id;
                            rEmpSched.empl_id = oEmployeeWindow.empID;
                            rEmpSched.emplsch_start = rMilestone.ms_date_scheduled;
                            rEmpSched.emplsch_end = rMilestone.ms_date_due;
                            rEmpSched.emp_workload_pct = rMilestone.emp_capacity_required;
                            rMilestone.emp_changed = 1;
                            
                            databaseManager.saveData(rEmpSched);
                        }
                        
                        if (bSplit && oEmployeeWindow.nTimeStillNeeded > 0) {
                            if (sDirection == 'F') {
                                if (dEarliestDate == null || oEmployeeWindow.startDate < dEarliestDate) {
                                    dEarliestDate = oEmployeeWindow.endDate;
                                    nTimeStillNeeded = oEmployeeWindow.nTimeStillNeeded;
                                }
                            }
                            else {
                                if (dEarliestDate == null || oEmployeeWindow.endDate > dEarliestDate) {
                                    dEarliestDate = oEmployeeWindow.startDate;
                                    nTimeStillNeeded = oEmployeeWindow.nTimeStillNeeded;
                                }
                            }
                        }
                    }
                }
                else {
                    removeMSEmpCapacityRecs(rMilestone.ms_id, oEmployeeWindow.empID, null, true);
                }
            }

            if (bSplit && nTimeStillNeeded && dEarliestDate) {
                /**@type {JSRecord<db:/avanti/sch_milestone>} */
                var rSplitMilestone = scopes.avDB.duplicateRecord(rMilestone);

                rSplitMilestone.split_parent_id = rMilestone.ms_id;
                rSplitMilestone.ms_time_budget = nTimeStillNeeded;
                rSplitMilestone.sequence_nr = rMilestone.sequence_nr + 1;
                rSplitMilestone.ms_pred_ms_id = rMilestone.ms_id;
                rSplitMilestone.is_split = 1;

                if (sDirection == 'F') {
                    rSplitMilestone.ms_date_scheduled = dEarliestDate;
                    rSplitMilestone.ms_date_due = scopes.avDate.addMinutesNoDSTChange(rSplitMilestone.ms_date_scheduled, rSplitMilestone.ms_time_budget);
                }
                else {
                    rSplitMilestone.ms_date_due = dEarliestDate;
                    rSplitMilestone.ms_date_scheduled = scopes.avDate.addMinutesNoDSTChange(rSplitMilestone.ms_date_due, rSplitMilestone.ms_time_budget * -1);
                }

                rMilestone.is_split = 1;
                rMilestone.ms_time_budget -= nTimeStillNeeded;

                databaseManager.saveData(rSplitMilestone);

                getNextFreeSpot(rSplitMilestone, sDirection, null, null, true, null, null, null, null, true, null, null, null, true);
            }
        }
        // dont have enough emps for date specified - see if we have enough emps for any dates - if not then bail
        else{
            // pop date args
            aArgs.pop();
            aArgs.pop()
            
            aEmps = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);
            
            if (!(aEmps && aEmps.length >= nNumEmpsNeeded)) {
                bBadEmpSetup = true;
                
                if (bValidatingScheduleBoardMove) {
                    sMoveFailReason = MOVE_FAIL_REASON.RequiredEmpsUnavailable;
                }
            }
        }
    }
    // no emps needed - use equip dates
    else {
        if (sDirection == 'F') {
            if (oEquipmentWindow.startDate) {
                dNextFreeTime = oEquipmentWindow.startDate;
                rMilestone.ms_date_scheduled = oEquipmentWindow.startDate;
                rMilestone.ms_date_due = oEquipmentWindow.endDate;
            }
        }
        else {
            if (oEquipmentWindow.endDate) {
                dNextFreeTime = oEquipmentWindow.endDate;
                rMilestone.ms_date_due = oEquipmentWindow.endDate;
                rMilestone.ms_date_scheduled = oEquipmentWindow.startDate;
            }
        }

        // no emps - delete all emp sched recs for this ms
        if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
            rMilestone.sch_milestone_to_sch_empl_schedule.deleteAllRecords();
            rMilestone.emp_changed = 1;
        }
    }

	if (rMilestone.sequence_nr == nDebugMilestone) scopes.avUtils.devOutputTime("SCHEDULE EMPLOYEES");
	
	
	//////////////////// STEP 4 - COULDNT FIND A FREETIME - CHANGE DATES AND TRY AGAIN ////////////////////
	
    if (!dNextFreeTime) {
    	// we only try again if using employee scheduling. if not, and we couldnt a spot for the equipment, then we exit after equipment scheduling code above    	
        if (bScheduleEmps && !bMustUseMSDates && !bBadEmpSetup && nRecursiveGetNextFreeSpotCalls < nMaxDaysToLookAheadInTheSchedule) {
    		var dNewDate = null;

        	if (sDirection == 'F') {
                if (aEmpStartTimes && aEmpStartTimes.length > 0 && aEmpStartTimes[aEmpStartTimes.length - 1] > rMilestone.ms_date_scheduled) {
                	dNewDate = aEmpStartTimes[aEmpStartTimes.length - 1];
                }
                else if (oEquipmentWindow.blockedDate > rMilestone.ms_date_scheduled) {
                	dNewDate = oEquipmentWindow.blockedDate;
                }
                else if (oEquipmentWindow.endDate > rMilestone.ms_date_scheduled) {
                	dNewDate = oEquipmentWindow.endDate;
                }
                // if we don't have any of these things then set ms_date_scheduled to now and try again
                else if (dTodayStart > rMilestone.ms_date_scheduled) {
                	dNewDate = application.getTimeStamp();
                }
            	
    			if (dNewDate) {
                    rMilestone.ms_date_scheduled = dNewDate;
                    rMilestone.ms_date_due = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
                    return getNextFreeSpot(rMilestone, sDirection, null, null, null, null, null, null, null, true, null, null, null, true);
    			}
            }
            else if (sDirection == 'B') {
                if (aEmpEndTimes && aEmpEndTimes.length > 0 && aEmpEndTimes[aEmpEndTimes.length - 1] && aEmpEndTimes[aEmpEndTimes.length - 1] < rMilestone.ms_date_due) {
                	dNewDate = aEmpEndTimes[aEmpEndTimes.length - 1];
                }
                else if (oEquipmentWindow.blockedDate && oEquipmentWindow.blockedDate < rMilestone.ms_date_due) {
                	dNewDate = oEquipmentWindow.blockedDate;
                }
                else if (oEquipmentWindow.startDate && oEquipmentWindow.startDate < rMilestone.ms_date_due) {
                	dNewDate = oEquipmentWindow.startDate;
                }
            	
    			if (dNewDate) {
                    rMilestone.ms_date_due = dNewDate;
                    rMilestone.ms_date_scheduled = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, -rMilestone.ms_time_budget);

                    // can't push it into past
    				if (rMilestone.ms_date_scheduled > dNow) {
    					return getNextFreeSpot(rMilestone, sDirection, null, null, null, null, null, null, null, true);
    				}
    			}
            }
        }

        if (bInScheduleBoard) {
            rMilestone.ms_date_scheduled = rMilestone.ms_date_scheduled_bak;
            rMilestone.ms_date_due = rMilestone.ms_date_due_bak;
        }
        else {
            rMilestone.ms_date_scheduled = null;
            rMilestone.ms_date_due = null;
            scopes.avText.showWarning('milestoneCantBeScheduled', false, [rMilestone.ms_oper_name], null, bInQuickSchedule);
        }
    }
    
    databaseManager.saveData(rMilestone);
    
    return dNextFreeTime;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {UUID} uEmpID
 *
 * @properties={typeid:24,uuid:"6C41B2D4-2C4A-4C5A-B284-EF111E5B8322"}
 */
function updateEquipCapacityFromEmp(rMilestone, rEquip, uEmpID) {
    var sSQL = "select ec.start_date, ec.end_date, ec.duration \
                from sch_ms_emp_capacity mec \
                inner join sch_emp_capacity ec on ec.ec_id = mec.ec_id \
                where ec.org_id = ? and mec.ms_id = ? and ec.empl_id = ?";
    var aArgs = [globals.org_id, rMilestone.ms_id.toString(), uEmpID.toString()];

    var dsEmpCaps = scopes.avDB.getDataset(sSQL, aArgs);

    if (dsEmpCaps) {
        for (var i = 1; i <= dsEmpCaps.getMaxRowIndex(); i++) {
            rMilestone.ms_date_scheduled = dsEmpCaps.getValue(i, 1);
            rMilestone.ms_date_due = dsEmpCaps.getValue(i, 2);
            rMilestone.ms_time_budget = dsEmpCaps.getValue(i, 3);

            // we dont need to make a distinction Forwards and Backwards here. we know the spot will be avail on equip
            getEarliestAdequateEquipmentWindow(rMilestone, rEquip, rMilestone.ms_date_scheduled);
        }
    }
}

/**
 * @public 
 * 
 * @param {UUID|String} sMsID
 * @param {UUID|String} sEmpID
 *
 * @return
 * @properties={typeid:24,uuid:"454011BA-65C0-4611-A0D5-E29063431CFA"}
 */
function isThereAnEmpSchedRec(sMsID, sEmpID){
    var sSQL = "SELECT COUNT(*) \
                FROM sch_empl_schedule \
                WHERE \
                    org_id = ? \
                    AND empl_id = ? \
                    AND ms_id = ?";
    var aArgs = [globals.org_id, sEmpID.toString(), sMsID.toString()];
    
    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * This function takes 2 arrays, one to sort and another to return. IE. we pass
 * an employee capacity array and an employee id array. we sort the employee capacity array from highest to
 * lowest capacity, the employee id array is sorted based on the capacity sort. so we return the employee
 * ids sorted by capacity. the nReturnTopNum param returns the specified num elements from the top
 * of the array. eg. i only want the 3 highest capacity emps.
 *
 * @public
 *
 * @param {Array<Number>} aCapacities
 * @param {Array} aEmps
 * @param {Number} nReturnTopNum
 *
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"42E93B05-D9D7-4BE8-826A-9CC1A49A8F5E"}
 */
function sortEmpsByCapacity(aCapacities, aEmps, nReturnTopNum) {
    var n = aCapacities.length;
    var arr = [];
    var aSorted = [];
    var i;

    for (i = 0; i < n; i++) {
        arr.push({ employee: aEmps[i], capacity: aCapacities[i] });
    }

    arr = arr.sort(function(a, b) {
        return ( a.capacity < b.capacity ) ? 1 : ( ( b.capacity < a.capacity ) ? -1 : 0 )
    });

    if (nReturnTopNum && arr.length > nReturnTopNum) {
        arr = arr.slice(0, nReturnTopNum);
    }

    n = arr.length;
    for (i = 0; i < n; i++) {
        aSorted.push(arr[i].employee)
    }

    return aSorted;
}

/**
 * @public
 *
 * @param {UUID} uEmpID
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"1FF6E331-7C33-4DEA-ACE7-287B75FC4EA5"}
 */
function getEmpWorkTimeForDate(uEmpID, dDate) {
    var sSQL = "select sum(duration) \
                from sch_emp_capacity \
                where org_id = ? and empl_id = ? and isnull(is_break, 0) = 0 and capacity < 100 \
                and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);
    var aArgs = [globals.org_id, uEmpID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {UUID} uEmpID
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"04A6E093-ECA3-49D3-A754-BAA109841465"}
 */
function getEmpCapacityForDate(uEmpID, dDate) {
    var sSQL = "select sum(duration * capacity) \
                from sch_emp_capacity \
                where org_id = ? and empl_id = ? and isnull(is_break, 0) = 0 and duration > 0 \
                and capacity > 0 and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);
    var aArgs = [globals.org_id, uEmpID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {UUID} uEmpID
 * @param {Date} dFromDate
 * @param {Date} dToDate
 *
 * @return
 * @properties={typeid:24,uuid:"C620398F-AC5B-42F3-8C17-CFB8876C8FFE"}
 */
function getEmpCapacityForDateRange(uEmpID, dFromDate, dToDate) {
    var nFromDate = scopes.avDate.getDateAsNum(dFromDate);
    var nToDate = scopes.avDate.getDateAsNum(dToDate);
    var sSQL = "select sum(duration * capacity) \
                from sch_emp_capacity \
                where org_id = ? and empl_id = ? and isnull(is_break, 0) = 0 and duration > 0 and capacity > 0 \
                and schedule_date_num >= " + nFromDate + " and schedule_date_num <= " + nToDate;
    var aArgs = [globals.org_id, uEmpID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone - the milestone to split
 * @param {String} sDirection - F or B
 *
 * @properties={typeid:24,uuid:"A0D9ECD2-DC48-44D0-81D3-59E1A85FD485"}
 */
function splitMilestone(rMilestone, sDirection) {
    if (rMilestone.contains_shift_break) {
        rMilestone.ms_date_scheduled_bak = rMilestone.ms_date_scheduled;
        rMilestone.ms_date_due_bak = rMilestone.ms_date_due;
        rMilestone.ms_time_budget_bak = rMilestone.ms_time_budget;

        if(bInScheduleBoard){
            clearMilestoneCapacity(rMilestone);
        }
        
        getNextFreeSpot(rMilestone, sDirection, null, null, true);

        if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$split_children)) {
            rMilestone.sch_milestone_to_sch_milestone$split_children.sort('sequence_nr desc');
            var rLastSplitMilestone = rMilestone.sch_milestone_to_sch_milestone$split_children.getRecord(1);

            if (sDirection == 'F' && rLastSplitMilestone.ms_date_due >= rMilestone.ms_date_due_bak) {
                if (scopes.avText.showYesNoQuestion('keepSplit?') == scopes.avText.no) {
                    undoSplit(rMilestone, sDirection);
                }
            }
            if (sDirection == 'B' && rLastSplitMilestone.ms_date_scheduled <= rMilestone.ms_date_scheduled_bak) {
                if (scopes.avText.showYesNoQuestion('keepSplit?') == scopes.avText.no) {
                    undoSplit(rMilestone, sDirection);
                }
            }

            if (sDirection == 'F' && utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$successors)) {
                adjustDownstreamMilestones(rLastSplitMilestone, rMilestone.sch_milestone_to_sch_milestone$successors, rMilestone.ms_id);
            }
            else if (sDirection == 'B' && utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$pred_ms_id)) {
                adjustUpstreamMilestones(rLastSplitMilestone, rMilestone.sch_milestone_to_sch_milestone$pred_ms_id, rMilestone.ms_id);
            }

            // have to clear capacity for split ms too
            for(var i=1; i<=rMilestone.sch_milestone_to_sch_milestone$split_children.getSize(); i++){
                var rSplitMS = rMilestone.sch_milestone_to_sch_milestone$split_children.getRecord(i);
                clearMilestoneCapacity(rSplitMS);
            }
        }
        else {
            rMilestone.is_split = 0;
            scopes.avText.showInfo('milestoneCouldNotBeSplit');
        }
    
        clearMilestoneCapacity(rMilestone);
    }
    else {
        scopes.avText.showInfo('noShiftBreak');
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone - the milestone to split
 * @param {String} sDirection - F or B
 *
 * @properties={typeid:24,uuid:"93149292-172E-436D-A427-17BC098DFCBB"}
 */
function undoSplit(rMilestone, sDirection) {
    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$split_children)) {
        for (var i = 1; i <= rMilestone.sch_milestone_to_sch_milestone$split_children.getSize(); i++) {
            var rSplitMS = rMilestone.sch_milestone_to_sch_milestone$split_children.getRecord(i)

            removeMSCapacityRecs(rSplitMS);

            if (utils.hasRecords(rSplitMS.sch_milestone_to_sch_equip_schedule)) {
                rSplitMS.sch_milestone_to_sch_equip_schedule.deleteAllRecords();
            }

            if (utils.hasRecords(rSplitMS.sch_milestone_to_sch_empl_schedule)) {
                rSplitMS.sch_milestone_to_sch_empl_schedule.deleteAllRecords();
            }

            // last split ms - hookup rMilestone and ms after last split ms
            if (i == rMilestone.sch_milestone_to_sch_milestone$split_children.getSize()) {
                if (sDirection == 'F' && utils.hasRecords(rSplitMS.sch_milestone_to_sch_milestone$successors)) {
                    rSplitMS.sch_milestone_to_sch_milestone$successors.sequence_nr = rMilestone.sequence_nr + 1;
                    rSplitMS.sch_milestone_to_sch_milestone$successors.ms_pred_ms_id = rMilestone.ms_id;
                }
                else if (sDirection == 'B' && utils.hasRecords(rSplitMS.sch_milestone_to_sch_milestone$pred_ms_id)) {
                    rMilestone.ms_pred_ms_id = rSplitMS.sch_milestone_to_sch_milestone$pred_ms_id.ms_id;
                }
            }
        }

        rMilestone.sch_milestone_to_sch_milestone$split_children.deleteAllRecords();
    }

    rMilestone.ms_date_scheduled = rMilestone.ms_date_scheduled_bak;
    rMilestone.ms_date_due = rMilestone.ms_date_due_bak;
    rMilestone.ms_time_budget = rMilestone.ms_time_budget_bak;
    
    clearMilestoneCapacity(rMilestone);
    
    getNextFreeSpot(rMilestone, sDirection);
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rPredecessor
 * @param {JSFoundset<db:/avanti/sch_milestone>} fsSuccessors
 * @param {UUID} uSplitParentID
 *
 * @properties={typeid:24,uuid:"D0C8582E-659D-4C7E-AC85-D636BBFFBBDD"}
 */
function adjustDownstreamMilestones(rPredecessor, fsSuccessors, uSplitParentID) {
    for (var i = 1; i <= fsSuccessors.getSize(); i++) {
        var rSuccessor = fsSuccessors.getRecord(i);

        // split children will appear as successors to org ms - we dont want to process these
        if(rSuccessor.split_parent_id != uSplitParentID){
            rSuccessor.sequence_nr = rPredecessor.sequence_nr + 1;
            rSuccessor.ms_pred_ms_id = rPredecessor.ms_id;

            // if the successor has successors, update them too
            if (utils.hasRecords(rSuccessor.sch_milestone_to_sch_milestone$successors)) {
                adjustDownstreamMilestones(rSuccessor, rSuccessor.sch_milestone_to_sch_milestone$successors, uSplitParentID);
            }
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rSuccessor
 * @param {JSFoundset<db:/avanti/sch_milestone>} fsPredecessors
 * @param {UUID} uSplitParentID
 *
 * @properties={typeid:24,uuid:"50749440-CB61-4BB2-B8E2-64ADDDA90611"}
 */
function adjustUpstreamMilestones(rSuccessor, fsPredecessors, uSplitParentID) {
    for (var i = 1; i <= fsPredecessors.getSize(); i++) {
        var rPredecessor = fsPredecessors.getRecord(i);

        // split children will appear as predecessors to org ms - we dont want to process these
        if(rPredecessor.split_parent_id != uSplitParentID){
            rSuccessor.ms_pred_ms_id = rPredecessor.ms_id;

            // if the predecessor has predecessors, update them too
            if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_milestone$pred_ms_id)) {
                adjustUpstreamMilestones(rPredecessor, rPredecessor.sch_milestone_to_sch_milestone$pred_ms_id, uSplitParentID);
            }
        }
    }
}

/**
 * @param {UUID} uMSID
 * @param {UUID|String} uEmpID
 * @param {Number} nCapacityRequired
 *
 * @properties={typeid:24,uuid:"D8726F19-BFB1-4BD6-AD54-88B3DC043C50"}
 */
function createMsAvailableEmpsRec(uMSID, uEmpID, nCapacityRequired) {
    var sSQL = "insert into sch_ms_available_emps (msae_id, org_id, ms_id, empl_id, emp_workload_pct) values (?,?,?,?,?)";
    var aArgs = [application.getUUID().toString(), globals.org_id, uMSID.toString(), uEmpID.toString(), nCapacityRequired];

    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @param {UUID} uMSID
 *
 * @properties={typeid:24,uuid:"48556462-FF86-4321-9E31-F6434EF591ED"}
 */
function deleteMsAvailableEmpRecs(uMSID) {
    var sSQL = "delete from sch_ms_available_emps where org_id = ? and ms_id = ?";
    var aArgs = [globals.org_id, uMSID.toString()];

    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @param {UUID} uMSID
 *
 * @properties={typeid:24,uuid:"01A531C4-B59B-4F09-A875-6B82EAAC0561"}
 */
function clearSelectedMsAvailableEmpRecs(uMSID) {
    var sSQL = "SELECT msae_id \
                FROM sch_ms_available_emps \
                WHERE ms_id = ?";
    var aArgs = [uMSID.toString()];

    scopes.avDB.updateFSWithSQLQuery('sch_ms_available_emps', ['is_selected'], [null], sSQL, aArgs);
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} uEmpID
 *
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"F7F7E5BF-3434-49CE-BFC2-F38EA175EB32"}
 */
function getMSEmpCapacityRequired(rMilestone, uEmpID) {
    /**@type {Number} */
    var nCapacityRequired = null;
    var sTable;

    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
        sTable = "sch_empl_schedule";
    }
    else if (utils.hasRecords(rMilestone.sch_milestone_to_sch_ms_available_emps)) {
        sTable = "sch_ms_available_emps";
    }

    if (sTable) {
        var sSQL = "select emp_workload_pct \
                    from " + sTable + " \
                    where org_id = ? and ms_id = ? and empl_id = ?";
        var aArgs = [globals.org_id, rMilestone.ms_id.toString(), uEmpID.toString()];

        nCapacityRequired = scopes.avDB.SQLQuery(sSQL, null, aArgs);
    }

    if (nCapacityRequired == null) {
        nCapacityRequired = rMilestone.emp_capacity_required;
    }

    return nCapacityRequired;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dCurDate
 * @param {Boolean} [bIncludeNonShiftDays]
 *
 * @return {JSRecord<db:/avanti/sch_shift_detail>}
 *
 * @properties={typeid:24,uuid:"8411C9F6-CB83-4F5D-9B4B-05BD4351B66C"}
 */
function getShiftDetailForEquip(rEquip, dCurDate, bIncludeNonShiftDays) {
    var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());

    for (var s = 1; s <= rEquip.eq_equipment_to_sch_equipment_shift.getSize(); s++) {
        var rEquipShift = rEquip.eq_equipment_to_sch_equipment_shift.getRecord(s);

        if (rEquipShift.start_date <= dCurDate && ( rEquipShift.end_date >= dCurDate || rEquipShift.end_date == null )) {
            if (utils.hasRecords(rEquipShift.sch_equipment_shift_to_sch_shift)) {
                var rShift = rEquipShift.sch_equipment_shift_to_sch_shift.getRecord(1);

                rShift.sch_shift_to_sch_shift_detail.sort('sequence_nr asc');

                for (var d = 1; d <= rShift.sch_shift_to_sch_shift_detail.getSize(); d++) {
                    var rShiftDet = rShift.sch_shift_to_sch_shift_detail.getRecord(d);

                    if (rShiftDet.shiftdet_startdate <= dCurDate && ( rShiftDet.shiftdet_enddate >= dCurDate || rShiftDet.shiftdet_enddate == null )) {
                        if (bIncludeNonShiftDays || isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
                            return rShiftDet;
                        }
                    }
                }
            }
        }
    }

    return null;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sys_employee>} rEmp
 * @param {Date} dCurDate
 * @param {Boolean} [bIncludeNonShiftDays]
 *
 * @return {JSRecord<db:/avanti/sch_shift_detail>}
 *
 * @properties={typeid:24,uuid:"F28DE6E3-06B5-4194-94EB-B520CCCE4B5D"}
 */
function getShiftDetailForEmp(rEmp, dCurDate, bIncludeNonShiftDays) {
    var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());

    for (var s = 1; s <= rEmp.sys_employee_to_sch_employee_shift.getSize(); s++) {
        var rEmpShift = rEmp.sys_employee_to_sch_employee_shift.getRecord(s);

        if (rEmpShift.start_date <= dCurDate && ( rEmpShift.end_date >= dCurDate || rEmpShift.end_date == null )) {
            if (utils.hasRecords(rEmpShift.sch_employee_shift_to_sch_shift)) {
                var rShift = rEmpShift.sch_employee_shift_to_sch_shift.getRecord(1);

                rShift.sch_shift_to_sch_shift_detail.sort('sequence_nr asc');

                for (var d = 1; d <= rShift.sch_shift_to_sch_shift_detail.getSize(); d++) {
                    var rShiftDet = rShift.sch_shift_to_sch_shift_detail.getRecord(d);

                    if (rShiftDet.shiftdet_startdate <= dCurDate && ( rShiftDet.shiftdet_enddate >= dCurDate || rShiftDet.shiftdet_enddate == null )) {
                        if (bIncludeNonShiftDays || isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
                            return rShiftDet;
                        }
                    }
                }
            }
        }
    }

    return null;
}

/**
 * @public
 *
 * @param {UUID} uShiftID
 * @param {Date} dCurDate
 * @param {Number} nAvailable
 * @param {UUID} [uEquipID]
 * @param {UUID} [uEmpID]
 *
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"5DDD9516-3D87-459B-A91F-1B2B5D9DE964"}
 */
function getExceptionsForDate(uShiftID, dCurDate, nAvailable, uEquipID, uEmpID) {
    var nDate = scopes.avDate.getDateAsNum(dCurDate);
	
    // Shift Exceptions
    var sSQL = "select starttime_num_mins, endtime_num_mins \
               from sch_shift_excep \
               where org_id = ? and shift_id = ? and isnull(shiftexcep_avail, 0) = ? and duration > 0 \
               and starttime_num_mins is not null and endtime_num_mins is not null \
               and shiftexcep_startdate_num is not null and " + nDate + " >= shiftexcep_startdate_num \
               and (" + nDate + " <= shiftexcep_enddate_num or shiftexcep_enddate_num is null)";
    var aArgs = [globals.org_id, uShiftID.toString(), nAvailable];
    

    // Employee Exceptions
    if (uEmpID) {
        var sEmpExSQL = "select starttime_num_mins, endtime_num_mins \
            from sch_empl_excep \
            where org_id = ? and empl_id = ? and isnull(empschexcep_avail, 0) = ? and duration > 0 \
            and starttime_num_mins is not null and endtime_num_mins is not null \
            and empschexcep_start_num is not null and " + nDate + " = empschexcep_start_num";
        var aEmpExArgs = [globals.org_id, uEmpID.toString(), nAvailable];

        sSQL += " union " + sEmpExSQL;
        aArgs = aArgs.concat(aEmpExArgs);
    }

    // Equipment Exceptions
    if (uEquipID) {
        var sEquipExSQL = "select starttime_num_mins, endtime_num_mins \
            from sch_equip_excep \
            where org_id = ? and equip_id = ? and isnull(equipschexcep_avail, 0) = ? and duration > 0 \
            and starttime_num_mins is not null and endtime_num_mins is not null \
            and equipschexcep_start_num is not null and " + nDate + " = equipschexcep_start_num";
        var aEquipExArgs = [globals.org_id.toString(), uEquipID.toString(), nAvailable];

        sSQL += " union " + sEquipExSQL;
        aArgs = aArgs.concat(aEquipExArgs); 
    }

    /**@type {Array<String>} */
    var aTimes = [];
    var dsTimes = scopes.avDB.getDataset(sSQL, aArgs);

    if (dsTimes) {
        for (var i = 1; i <= dsTimes.getMaxRowIndex(); i++) {
            var sStartMins = dsTimes.getValue(i, 1);
            var sStopMins = dsTimes.getValue(i, 2);

            aTimes.push(utils.numberFormat(sStartMins, '0000') + '-' + utils.numberFormat(sStopMins, '0000'));
        }
    }

    return aTimes;
}

/**
 * @public
 *
 * @param {UUID|String} uEmpID
 * @param {Number} nActive - 1 (active) or 0 (inactive)
 *
 * @properties={typeid:24,uuid:"DEC286C2-7A6F-415E-ACDB-DEFC438FDFC1"}
 */
function setActiveScheduler(uEmpID, nActive) {
    if (uEmpID) {
        scopes.avDB.RunSQL("update sys_employee set active_scheduler = ? where org_id = ? and empl_id = ?",
            null, [nActive, globals.org_id, uEmpID.toString()]);
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 *
 * @properties={typeid:24,uuid:"256E3ACF-894D-49BF-99A4-2FA36A7CE132"}
 */
function confirmScheduleRelease(rSchedule) {
    notifyActiveSchedulers();
}

/**
 * @param {Boolean} [bFromRevertOptimization]
 * 
 * @properties={typeid:24,uuid:"4758D4C2-6E69-4911-8DD2-EA507DBEC0B2"}
 */
function notifyActiveSchedulers(bFromRevertOptimization) {
    
    if (bFromRevertOptimization) {
        scopes.avDB.RunSQL("update sys_employee set refresh_schedule = 1, refresh_schedule_from_database = 1 where org_id = ? and active_scheduler = 1", null, [globals.org_id]);
    }
    else {
        scopes.avDB.RunSQL("update sys_employee set refresh_schedule = 1 where org_id = ? and active_scheduler = 1", null, [globals.org_id]);
    }
    
}

/**
 * @public
 *
 * @param {UUID|String} uEmpID
 * @param {Boolean} [bClearRefreshScheduleFlag]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B0ED97D7-709E-4FD0-AF34-B7563E2E782A"}
 */
function doesScheduleNeedToBeRefreshed(uEmpID, bClearRefreshScheduleFlag) {
	var bRefresh = false;
	
    if (uEmpID) {
        var nRefresh = scopes.avDB.SQLQuery("select refresh_schedule from sys_employee where org_id = ? and empl_id = ?", null,
                [globals.org_id, uEmpID.toString()]) == 1;

        if (nRefresh == 1 && bClearRefreshScheduleFlag) {
            refreshScheduleFromDatabase(uEmpID);
            clearRefreshScheduleFlag(globals.avBase_employeeUUID);
        }

		if (nRefresh == 1) {
			bRefresh = true;
		}
    }
    
    return bRefresh;
}

/**
 *  
 * 
 * @param {UUID|String} uEmpID
 *
 * @properties={typeid:24,uuid:"681C15F2-345C-4161-A1F2-AEBAABFAFAC1"}
 */
function refreshScheduleFromDatabase(uEmpID) {
    if (uEmpID) {
        var nRefresh = scopes.avDB.SQLQuery("select refresh_schedule_from_database from sys_employee where org_id = ? and empl_id = ?", null, [globals.org_id, uEmpID.toString()]) == 1;

        if (nRefresh == 1) {
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, 'sch_milestone'), -1);
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, 'sch_emp_capacity'), -1);
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, 'sch_ms_emp_capacity'), -1);
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, 'sch_equip_schedule'), -1);
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, 'sch_empl_schedule'), -1);
            
            scopes.avDB.RunSQL("update sys_employee set refresh_schedule_from_database = 0 where org_id = ? and empl_id = ?", null, [globals.org_id, uEmpID.toString()]);
        }
    }
}

/**
 * @public
 *
 * @param {UUID|String} uEmpID
 *
 * @properties={typeid:24,uuid:"FDE045E1-A66B-44BE-AAF0-53980F2D95D7"}
 */
function clearRefreshScheduleFlag(uEmpID) {
    if (uEmpID) {
        scopes.avDB.RunSQL("update sys_employee set refresh_schedule = 0 where org_id = ? and empl_id = ?",
            null, [globals.org_id, uEmpID.toString()]);
    }
}

/**
 * @public
 *
 * @param {UUID} uMSID
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"197046A2-E70A-478F-B005-18A3F4D9A9DA"}
 */
function getEmpsNamesWorkingOnMS(uMSID) {
    var sSQL = "select empl_full_name \
               from sys_employee e \
               inner join sch_empl_schedule ae on ae.empl_id = e.empl_id \
               where e.org_id = ? and ae.ms_id = ? \
               order by 1 asc";
    var aArgs = [globals.org_id, uMSID.toString()];
    var aEmps = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);

    if (aEmps && aEmps.length > 0) {
        return scopes.avText.arrayToString(aEmps, ',');
    }
    else {
        return null;
    }
}

/**
 * @param {UUID} uMSID
 *
 * @return
 * @properties={typeid:24,uuid:"12BF8C58-20FE-418B-BD3E-7EBCFA11A220"}
 */
function getEmpsNamesSelectedForMS(uMSID) {
    var sSQL = "select empl_full_name \
                from sys_employee e \
                inner join sch_ms_available_emps ae on ae.empl_id = e.empl_id \
                where e.org_id = ? and ae.ms_id = ? and ae.is_selected = 1 \
                order by 1 asc";
    var aArgs = [globals.org_id, uMSID.toString()];
    var aEmps = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);

    if (aEmps && aEmps.length > 0) {
        return scopes.avText.arrayToString(aEmps, ',');
    }
    else {
        return null;
    }
}

/**
 * @public
 *
 * @param {UUID|String} uMSID
 *
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"98E91EC5-1B03-4EF8-A43E-EC3919D06429"}
 */
function getEmpsSelectedForMS(uMSID) {
    var sSQL = "select empl_id \
                from sch_ms_available_emps \
                where ms_id = ? and is_selected = 1";
    var aArgs = [uMSID.toString()];
    return scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);
}

/**
 * @public
 *
 * @param {UUID|String} uMSID
 *
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"A25D25A4-1B96-4ECD-AD4A-A052ACA4E107"}
 */
function getEmpsWorkingOnMS(uMSID) {
    var sSQL = "select empl_id \
                from sch_empl_schedule \
                where ms_id = ?";
    var aArgs = [uMSID.toString()];
    return scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);
}

/**
 * @public
 *
 * @param {String|UUID} sMsID
 * @param {Date} dStartDate
 * @param {Date} dEndDate
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"6508E338-BB50-4492-AA63-9652B45C5E06"}
 */
function canMSEmployeesBeMovedToTime(sMsID, dStartDate, dEndDate) {
    /**@type {JSRecord<db:/avanti/sch_milestone>} */
    var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [sMsID]);

    if (rMilestone) {
        var aEmps = getEmpsWorkingOnMS(sMsID);

        if (aEmps && aEmps.length > 0) {
            for (var i = 0; i < aEmps.length; i++) {
                if (!canEmployeeBeMovedToTime(rMilestone, aEmps[i], dStartDate, dEndDate)) {
                    return false;
                }
            }
        }
    }

    return true;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {String|UUID} uEmpID
 * @param {Date} dStartDate
 * @param {Date} dEndDate
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"465B79ED-74F7-4426-943E-04CF013231BA"}
 */
function canEmployeeBeMovedToTime(rMilestone, uEmpID, dStartDate, dEndDate) {
    /**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow;

    oEquipmentWindow.startDate = dStartDate;
    oEquipmentWindow.endDate = dEndDate;

    var dSchedStartDate = getEarliestAdequateEmployeeWindow(rMilestone, uEmpID, oEquipmentWindow);

    if (dSchedStartDate) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dStartDate
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"4436FECF-A9B0-4614-A558-C8FA9D6D278E"}
 */
function canEquipmentBeMovedToTime(rMilestone, rEquip, dStartDate) {
    var dSchedStartDate = getEarliestAdequateEquipmentWindow(rMilestone, rEquip, dStartDate);

    if (dSchedStartDate == dStartDate) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dStartDate
 * @param {Date} dEndDate
 * @param {Array} aEmps
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"71C79326-EF7D-4EBA-88F6-E5059C737295"}
 */
function changeMSTime(rMilestone, dStartDate, dEndDate, aEmps) {
    // when an equip ms is moved in sched board we call this func. rather than seeing if all related emps
    // can be moved, then calling separate code to move them, if we have determined they can be moved. just
    // try to move them, and if it turns out we cant - revert capacity rec changes, and return false,
    // indicating the equip ms move should be cancelled

    var dOldStartDate = rMilestone.ms_date_scheduled;
    var dOldEndDate = rMilestone.ms_date_due;
    /**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow = { };

    clearMilestoneCapacity(rMilestone);

    oEquipmentWindow.startDate = dStartDate;
    oEquipmentWindow.endDate = dEndDate;
    rMilestone.ms_date_scheduled = dStartDate;
    rMilestone.ms_date_due = dEndDate;

    var dSchedStartDate = getNextFreeSpot(rMilestone, 'F', oEquipmentWindow, true);

    // emps can be moved to this time
    if (dSchedStartDate == dStartDate) {
        updateEmpSchedWithNewTime(rMilestone);

        return true;
    }
    // emps cant be moved to this time - revert
    else {
        clearMilestoneCapacity(rMilestone);

        oEquipmentWindow.startDate = dOldStartDate;
        oEquipmentWindow.endDate = dOldEndDate;
        rMilestone.ms_date_scheduled = dOldStartDate;
        rMilestone.ms_date_due = dOldEndDate;

        getNextFreeSpot(rMilestone, 'F', oEquipmentWindow, true);

        return false;
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"5E4F271F-3147-462E-87A2-38477DB5163E"}
 */
function removeMSCapacityRecs(rMilestone) {
    var sMSID = rMilestone.ms_id.toString();
    
    // equip - add capacity back in to sch_emp_capacity recs
    var sSQL = "SELECT ec_id \
                FROM sch_emp_capacity \
                WHERE \
                    equip_id IS NOT NULL \
                    AND equip_ms_id = ?";
    
    scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity', 'equip_ms_id'], [100, null], sSQL, [sMSID]);

    // emp
    if (scopes.avUtils.getSysPrefNum('ScheduleEmployees')) {
        // add capacity back in to sch_emp_capacity recs
        sSQL = "SELECT ec_id \
                FROM sch_emp_capacity \
                WHERE \
                    empl_id IS NOT NULL \
                    AND ec_id IN (SELECT ec_id FROM sch_ms_emp_capacity WHERE ms_id = ?)";
 
        scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity'], [rMilestone.emp_capacity_required], 
            sSQL, [sMSID], ['+'], [0], [100]);
        
        // delete sch_ms_emp_capacity recs
        scopes.avDB.deleteRecs('sch_ms_emp_capacity', ['ms_id'], [sMSID])
    }
}

/**
 * @param {UUID|String} uMSID
 * @param {UUID|String} uEmpID
 * @param {Number} nCapacity
 * @param {Boolean} [bGetCapacityFromMS]
 *
 * @properties={typeid:24,uuid:"85EBE9E5-D022-4F0C-95EC-6502C7532D51"}
 */
function removeMSEmpCapacityRecs(uMSID, uEmpID, nCapacity, bGetCapacityFromMS) {
    var sMSID = uMSID.toString();
    var sEmpID = uEmpID.toString();
    var aArgs = [sEmpID, sMSID];

    if (bGetCapacityFromMS) {
        /**@type {JSRecord<db:/avanti/sch_milestone>} */
        var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [uMSID]);
        nCapacity = getMSEmpCapacityRequired(rMilestone, uEmpID)
    }

    // add capacity back in to sch_emp_capacity recs
    if (nCapacity) {
        var sSQL = "SELECT ec_id \
                    FROM sch_emp_capacity \
                    WHERE \
                        empl_id = ? \
                        AND ec_id IN (SELECT ec_id FROM sch_ms_emp_capacity WHERE ms_id = ?)";
        
        scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity'], [nCapacity], sSQL, aArgs, ['+'], [0], [100]);
    }

    // delete sch_ms_emp_capacity recs
    scopes.avDB.deleteRecs('sch_ms_emp_capacity', ['empl_id', 'ms_id'], aArgs);
}

/**
 * @param {UUID|String} uMSID
 * @param {UUID|String} uEmpID
 * @param {Number} nOldCapacityRequired
 * @param {Number} nNewCapacityRequired
 *
 * @properties={typeid:24,uuid:"06CED624-7F6C-4B3C-A498-6E280ED42F67"}
 */
function changeMSEmpCapacity(uMSID, uEmpID, nOldCapacityRequired, nNewCapacityRequired) {
    var nAdjustedCapacity = nOldCapacityRequired - nNewCapacityRequired;
    
    if (nAdjustedCapacity) {
        var sSQL = "SELECT ec_id \
                    FROM sch_emp_capacity \
                    WHERE \
                        empl_id = ? \
                        AND ec_id IN (SELECT ec_id FROM sch_ms_emp_capacity WHERE ms_id = ?)";
        var aArgs = [uEmpID.toString(), uMSID.toString()];

        scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity'], [nAdjustedCapacity], sSQL, aArgs, 
            ['+'], [0], [100]);
    }
}

/**
 * @param {UUID|String} uMSID
 *
 * @properties={typeid:24,uuid:"CD683061-E584-4B75-AF15-EB4F39942116"}
 */
function removeMSEquipCapacityRecs(uMSID) {
    if (uMSID) {
        var sMSID = uMSID.toString();

        // add capacity back in to sch_emp_capacity recs
        var sSQL = "SELECT ec_id \
                    FROM sch_emp_capacity \
                    WHERE \
                        equip_ms_id = ?";
        var aArgs = [sMSID];
        scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity', 'equip_ms_id'], [100, null], sSQL, aArgs);
    }
}

/**
 * @param {UUID|String} uMSID
 * @param {Number} nCapacity
 *
 * @properties={typeid:24,uuid:"F799060D-0155-4756-B310-CA8ABD4EAF6B"}
 */
function removeAllMSEmpCapacityRecs(uMSID, nCapacity) {
    var sMSID = uMSID.toString();

    if (nCapacity) {
        // add capacity back in to sch_emp_capacity recs
        var sSQL = "SELECT ec_id \
                    FROM sch_emp_capacity \
                    WHERE \
                        ec_id IN (SELECT ec_id FROM sch_ms_emp_capacity WHERE ms_id = ?)";

        scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity'], [nCapacity], sSQL, [sMSID], 
            ['+'], [0], [100]);
    }

    // delete sch_ms_emp_capacity recs
    scopes.avDB.deleteRecs('sch_ms_emp_capacity', ['ms_id'], [sMSID]);
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"0C6DDB64-F130-4857-87B0-6BB0C0FA8B9B"}
 */
function updateEmpSchedWithNewTime(rMilestone) {
    for (var i = 1; i <= rMilestone.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
        var rEmpSched = rMilestone.sch_milestone_to_sch_empl_schedule.getRecord(i);

        rEmpSched.emplsch_start = rMilestone.ms_date_scheduled;
        rEmpSched.emplsch_end = rMilestone.ms_date_due;

        databaseManager.saveData(rEmpSched);
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} [uEquipID]
 *
 * @properties={typeid:24,uuid:"DF20AF1D-9DA0-4D14-878D-3EC6DDBC51F5"}
 */
function updateEquipSchedWithNewTime(rMilestone, uEquipID) {
    for (var i = 1; i <= rMilestone.sch_milestone_to_sch_equip_schedule.getSize(); i++) {
        var rEquipSched = rMilestone.sch_milestone_to_sch_equip_schedule.getRecord(i);

        rEquipSched.equipsch_start = rMilestone.ms_date_scheduled;
        rEquipSched.equipsch_end = rMilestone.ms_date_due;
        
        if (uEquipID) {
            rEquipSched.equip_id = uEquipID;
        }

        databaseManager.saveData(rEquipSched);
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} [uEquipID]
 *
 * @properties={typeid:24,uuid:"9A644194-DBC3-44B6-AE73-F2D14FDDA630"}
 */
function updateSchedWithNewTime(rMilestone, uEquipID) {
    updateEquipSchedWithNewTime(rMilestone, uEquipID);
    updateEmpSchedWithNewTime(rMilestone);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID|String} uNewEmpID
 * @param {Number} nCapacity
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"54E26B62-FF73-4652-9D6E-1581A7F7BFB8"}
 */
function addScheduledEmp(rMilestone, uNewEmpID, nCapacity) {
    /**@type {{startDate:Date, endDate:Date, blockedDate:Date, nextJobDate:Date}} */
    var oEquipmentWindow = { };

    oEquipmentWindow.startDate = rMilestone.ms_date_scheduled;
    oEquipmentWindow.endDate = rMilestone.ms_date_due;

    /**@type {{empID:UUID, startDate:Date, endDate:Date, capacity:Number, bContainsShiftBreak:Boolean, nTimeStillNeeded:Number, dSoonestDateAnyTimeIsAvail:Date}} */
    var oEmployeeWindow = getEarliestAdequateEmployeeWindow(rMilestone, uNewEmpID, oEquipmentWindow, nCapacity);

    // emp is free
    if (oEmployeeWindow && oEmployeeWindow.startDate == rMilestone.ms_date_scheduled && oEmployeeWindow.endDate == rMilestone.ms_date_due) {
        return true;
    }
    // emp is not free
    else {
        return false;
    }
}

/**
 * @param {UUID|String} uMSID
 * @param {UUID|String} uEMPID
 *
 * @properties={typeid:24,uuid:"8E1AC462-A79B-4E5A-B86A-6CC2B47D7A26"}
 */
function removeEmpFromMsAvailEmps(uMSID, uEMPID) {
    var sSQL = "delete from sch_ms_available_emps where org_id = ? and ms_id = ? and empl_id = ?";
    var aArgs = [globals.org_id, uMSID.toString(), uEMPID.toString()];
    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"1601F990-F36B-4130-AAB5-96CD0319E119"}
 */
function isMilestoneAtRisk(rMilestone) {
    return application.getTimeStamp() > rMilestone.ms_date_scheduled && !hasMilestoneBeenStarted(rMilestone.ms_id);
}

/**
 * @public
 *
 * @param {UUID|String} uMSID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"7B36DC07-8ED1-46FA-A7B7-4F7F3EFC3200"}
 */
function hasMilestoneBeenStarted(uMSID) {
    return scopes.avDB.SQLQuery("select count(*) from prod_job_cost where org_id = ? and ms_id = ?", null,
        [globals.org_id, uMSID.toString()]) > 0;
}

/**
 * @public
 *
 * @param {UUID|String} uID
 *
 * @return {String} - 'equipment' or 'employee'
 *
 * @properties={typeid:24,uuid:"975714CA-C6C9-4D2F-9742-9A1E6E953EE7"}
 */
function getResourceTypeFromID(uID) {
    var sEqSQL = "select count(*) from eq_equipment where org_id = ? and equip_id = ?"
    var sEmpSQL = "select count(*) from sys_employee where org_id = ? and empl_id = ?"
    var aArgs = [globals.org_id, uID.toString()];

    if (scopes.avDB.SQLQuery(sEqSQL, null, aArgs) > 0) {
        return 'equipment';
    }
    else if (scopes.avDB.SQLQuery(sEmpSQL, null, aArgs) > 0) {
        return 'employee';
    }
    else {
        return null;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dCurDate
 *
 * @return {JSRecord<db:/avanti/sch_shift_detail>}
 *
 * @properties={typeid:24,uuid:"ABEC8BE6-905A-4908-A37D-10EB51A1D867"}
 */
function getCurrentEquipShift(rEquip, dCurDate) {
    if (utils.hasRecords(rEquip.eq_equipment_to_sch_equipment_shift)) {
        var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());
        rEquip.eq_equipment_to_sch_equipment_shift.sort('sequence_nr asc');

        for (var s = 1; s <= rEquip.eq_equipment_to_sch_equipment_shift.getSize(); s++) {
            var rEquipShift = rEquip.eq_equipment_to_sch_equipment_shift.getRecord(s);

            if (rEquipShift.start_date <= dCurDate && ( rEquipShift.end_date >= dCurDate || rEquipShift.end_date == null )) {
                if (utils.hasRecords(rEquipShift.sch_equipment_shift_to_sch_shift)) {
                    var rShift = rEquipShift.sch_equipment_shift_to_sch_shift.getRecord(1);

                    if (utils.hasRecords(rShift.sch_shift_to_sch_shift_detail)) {
                        rShift.sch_shift_to_sch_shift_detail.sort('sequence_nr asc');

                        for (var d = 1; d <= rShift.sch_shift_to_sch_shift_detail.getSize(); d++) {
                            var rShiftDet = rShift.sch_shift_to_sch_shift_detail.getRecord(d);

                            if (isShiftDetailValid(rShiftDet, dCurDate) && isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
                                return rShiftDet;
                            }
                        }
                    }
                }
            }
        }
    }

    return null;
}

/**
 * @public
 *
 * @param {String} sResourceType = 'equipment' or 'employee'
 * @param {UUID} uID
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"B29CCF06-D8C1-4B17-8120-CFE5956750F1"}
 */
function areThereAnyValidShifts(sResourceType, uID) {
    if (sResourceType == 'equipment' || sResourceType == 'employee') {
        var sTableName = sResourceType == 'equipment' ? 'sch_equipment_shift' : 'sch_employee_shift';
        var sPKName = sResourceType == 'equipment' ? 'equip_id' : 'empl_id';
        var dDateSQL = scopes.avDate.getDateSQl(application.getTimeStamp(), true);
        var sSQL = "select count(sd.shiftdet_id) \
                    from " + sTableName + " es \
                    inner join sch_shift_detail sd on sd.shift_id = es.shift_id \
                    where sd.org_id = ? and es." + sPKName + " = ? and sd.shiftdet_duration > 0  \
                    and (es.end_date > " + dDateSQL + " or es.end_date is null) \
                    and (sd.shiftdet_enddate > " + dDateSQL + " or sd.shiftdet_enddate is null) \
                    and (sd.shiftdet_flg_monday = 1 or sd.shiftdet_flg_tuesday = 1 or \
                        sd.shiftdet_flg_wednesday = 1 or sd.shiftdet_flg_thursday = 1 or \
                        sd.shiftdet_flg_friday = 1 or sd.shiftdet_flg_saturday = 1 or sd.shiftdet_flg_sunday = 1)";

        return scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, uID.toString()]) > 0;
    }
    else {
        return false;
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 *
 * @return
 * @properties={typeid:24,uuid:"15C3611D-431F-4FA9-9AF1-C1C34487F3BC"}
 */
function getCurrentShift(rMilestone, dCurDate) {
    /**@type {JSRecord<db:/avanti/sch_shift_detail>} */
    var rShiftDet = null;
    var bScheduleEmps = scopes.avUtils.getSysPrefNum('ScheduleEmployees');

    if (bScheduleEmps) {
        var nOpCatSchedEmps = rMilestone.sch_milestone_to_sys_operation_category.opcat_schedule_employees;
        var bHasEmps = utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule);

        if (nOpCatSchedEmps && bHasEmps) {
            for (var i = 1; i <= rMilestone.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
                var rEmpSched = rMilestone.sch_milestone_to_sch_empl_schedule.getRecord(i);
                var rEmp = rEmpSched.sch_empl_schedule_to_sys_employee.getRecord(1);

                rShiftDet = getCurrentEmpShift(rEmp, dCurDate);

                if (rShiftDet) {
                    break;
                }
            }
        }

    }

    if (!rShiftDet && utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
        var rEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
        rShiftDet = getCurrentEquipShift(rEquip, dCurDate);
    }

    return rShiftDet
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 * @param {Boolean} [bValidateShiftWorkingOnCurDate]
 *
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"30692233-558B-441E-BA76-8AAAB2F6847E"}
 */
function getCurrentShiftStart(rMilestone, dCurDate, bValidateShiftWorkingOnCurDate) {
    var rShiftDet = getCurrentShift(rMilestone, dCurDate);

    if (rShiftDet) {
        var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());

        if (!bValidateShiftWorkingOnCurDate || isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
            var dShiftStart = scopes.avDate.copyDate(dCurDate, true);
            var dStartDate = scopes.avDate.addMinutesNoDSTChange(dShiftStart, rShiftDet.shiftdet_starttime_num_mins);

            return dStartDate;
        }
    }

    return null;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 * @param {Boolean} [bValidateShiftWorkingOnCurDate]
 *
 * @return {Array<Date>}
 *
 * @properties={typeid:24,uuid:"D2BB2AB9-00D0-4DF7-8C37-67621D5E1BFC"}
 */
function getCurrentShiftStartAndStop(rMilestone, dCurDate, bValidateShiftWorkingOnCurDate) {
    var aDates = [];
    var rShiftDet = getCurrentShift(rMilestone, dCurDate);

    if (rShiftDet) {
        var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());

        if (!bValidateShiftWorkingOnCurDate || isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
            var dShiftDate = scopes.avDate.copyDate(dCurDate, true);
            var dStartDate = scopes.avDate.addMinutesNoDSTChange(dShiftDate, rShiftDet.shiftdet_starttime_num_mins);
            var dStopDate = scopes.avDate.addMinutesNoDSTChange(dShiftDate, rShiftDet.shiftdet_endtime_num_mins);

            aDates = [dStartDate, dStopDate];
        }
    }

    return aDates;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 *
 * @return {Array<Date>}
 *
 * @properties={typeid:24,uuid:"F6124C7B-3DD4-40DA-BDA0-8B4631F5B92D"}
 */
function getNextShiftStartAndStop(rMilestone, dCurDate) {
    var aDates = getCurrentShiftStartAndStop(rMilestone, dCurDate, true);
    
    // if curdate not avail in this shift then iterate thru all the days of the week until we find one thats avail  
    for (var i=1; i<7 && aDates.length == 0; i++){
        dCurDate = plugins.DateUtils.addDays(dCurDate, 1);
        aDates = getCurrentShiftStartAndStop(rMilestone, dCurDate, true);
    }

    return aDates;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"DE2D854F-DD21-4736-9BAB-3D29673D93A2"}
 */
function doesCurDateTimeFallInShift(rMilestone, dCurDate) {
    var aDates = getCurrentShiftStartAndStop(rMilestone, dCurDate);

    if (aDates.length == 2) {
        if (dCurDate >= aDates[0] && aDates < aDates[1]) {
            return true;
        }
    }

    return false;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dCurDate
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"C431C0E6-0E6B-47B3-85E6-902747190AC9"}
 */
function isThereAShiftToday(rMilestone, dCurDate) {
    var rShiftDetail = getCurrentShift(rMilestone, dCurDate);

    if (rShiftDetail) {
        var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());
        return isWeekDayAvailableInShiftDet(rShiftDetail, sCurWeekDay);
    }

    return false;
}

/**
 * @param {JSRecord<db:/avanti/sys_employee>} rEmp
 * @param {Date} dCurDate
 *
 * @return
 * @properties={typeid:24,uuid:"C25CE808-E62E-455C-B42D-9A72180231C1"}
 */
function getCurrentEmpShift(rEmp, dCurDate) {
    if (utils.hasRecords(rEmp.sys_employee_to_sch_employee_shift)) {
        var sCurWeekDay = globals['avUtilities_dateShortDayName'](dCurDate.getDay());
        rEmp.sys_employee_to_sch_employee_shift.sort('sequence_nr asc');

        for (var s = 1; s <= rEmp.sys_employee_to_sch_employee_shift.getSize(); s++) {
            var rEmpShift = rEmp.sys_employee_to_sch_employee_shift.getRecord(s);

            if (rEmpShift.start_date <= dCurDate && ( rEmpShift.end_date >= dCurDate || rEmpShift.end_date == null )) {
                if (utils.hasRecords(rEmpShift.sch_employee_shift_to_sch_shift)) {
                    var rShift = rEmpShift.sch_employee_shift_to_sch_shift.getRecord(1);

                    if (utils.hasRecords(rShift.sch_shift_to_sch_shift_detail)) {
                        rShift.sch_shift_to_sch_shift_detail.sort('sequence_nr asc');

                        for (var d = 1; d <= rShift.sch_shift_to_sch_shift_detail.getSize(); d++) {
                            var rShiftDet = rShift.sch_shift_to_sch_shift_detail.getRecord(d);

                            if (isShiftDetailValid(rShiftDet, dCurDate) && isWeekDayAvailableInShiftDet(rShiftDet, sCurWeekDay)) {
                                return rShiftDet;
                            }
                        }
                    }
                }
            }
        }
    }

    return null;
}

/**
 * @public
 *
 * @param {UUID} sOpCatID
 * @param {UUID} sDeptID
 *
 * @return {Array<String>}
 *
 * @properties={typeid:24,uuid:"46673C93-F55B-4F45-8BB8-ECB21D0A7ADC"}
 */
function getEmpsThatCanWorkOnOpCat(sOpCatID, sDeptID) {
    var sSQL = "SELECT DISTINCT ed.empl_id, e.empl_full_name \
                FROM sys_empl_dept ed \
                INNER JOIN sys_employee e ON e.empl_id = ed.empl_id \
                WHERE \
                    ed.org_id = ? \
                    AND e.empl_active = 1 \
                    AND e.empl_full_name IS NOT NULL \
                    AND (opcat_id = ? OR (dept_id = ? AND opcat_id is null)) \
                ORDER BY e.empl_full_name ASC";
    var aArgs = [globals.org_id, sOpCatID.toString(), sDeptID.toString()];

    /**@type {Array<String>} */
    var aEmps = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);

    return aEmps;
}

/**
 * @param {UUID} uMSID
 *
 * @properties={typeid:24,uuid:"3954A295-D325-43E2-B626-F9364B1D86F6"}
 */
function removeMSFromSchedule(uMSID) {
    scopes.avDB.deleteRecs('sch_equip_schedule', ['ms_id'], [uMSID]);
    scopes.avDB.deleteRecs('sch_empl_schedule', ['ms_id'], [uMSID]);
}

/**
 * @param {Array<JSRecord<db:/avanti/sch_schedule>>} aOrderSchedules
 * @param {Boolean} bClearMilestoneDates
 * @param {Boolean} bDefragCapacityRecs
 *
 * @properties={typeid:24,uuid:"A41F1558-8791-47EE-8AE1-EB499553E2DF"}
 */
function clearOrderCapacity(aOrderSchedules, bClearMilestoneDates, bDefragCapacityRecs) {
    if (bDefragCapacityRecs) {
        _aResourceDatesProcessed = [];
    }
    
    for (var i = 0; i < aOrderSchedules.length; i++) {
        clearMilestonesCapacity(aOrderSchedules[i], true, null, null, bClearMilestoneDates, bDefragCapacityRecs);
    }
    
    if (bDefragCapacityRecs) {
        _aResourceDatesProcessed = [];
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 * @param {Boolean} [bClearAvailEmps]
 * @param {Boolean} [bBackupDates]
 * @param {UUID} [uDontBackupDatesForThisMilestoneID]
 * @param {Boolean} [bClearMilestoneDates]
 * @param {Boolean} [bDefragCapacityRecs]
 *
 * @properties={typeid:24,uuid:"44CE69DE-24A7-4FB5-9AAA-5FECB8EB9FB9"}
 */
function clearMilestonesCapacity(rSchedule, bClearAvailEmps, bBackupDates, uDontBackupDatesForThisMilestoneID, bClearMilestoneDates, bDefragCapacityRecs) {
    if (utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone)) {
        for (var i = 1; i <= rSchedule.sch_schedule_to_sch_milestone.getSize(); i++) {
            var rMS = rSchedule.sch_schedule_to_sch_milestone.getRecord(i);

            clearMilestoneCapacity(rMS, bClearAvailEmps, bDefragCapacityRecs);

            if (bBackupDates && ( !uDontBackupDatesForThisMilestoneID || rMS.ms_id != uDontBackupDatesForThisMilestoneID )) {
                rMS.ms_date_scheduled_bak = rMS.ms_date_scheduled;
                rMS.ms_date_due_bak = rMS.ms_date_due;
            }

            if (bClearMilestoneDates) {
                rMS.ms_date_scheduled = null;
                rMS.ms_date_due = null;
            }

            databaseManager.saveData(rMS);
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bClearAvailEmps]
 * @param {Boolean} [bDefragCapacityRecs]
 *
 * @properties={typeid:24,uuid:"4EC3BE63-865D-4470-A520-CBC5D3A1EA15"}
 */
function clearMilestoneCapacity(rMilestone, bClearAvailEmps, bDefragCapacityRecs) {
    var i;
    
    if (rMilestone.ms_date_scheduled) {
        var dStartDate = scopes.avDate.copyDate(rMilestone.ms_date_scheduled, true);
    }
    if (rMilestone.ms_date_due) {
        var dEndDate = scopes.avDate.copyDate(rMilestone.ms_date_due, true);
    }

    if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
        var uEQID = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.equip_id;
        
        removeMSEquipCapacityRecs(rMilestone.ms_id);
        defragCapRecs(RESOURCE_TYPE.Equipment, uEQID);
    }

    // have to do each emp individually as each could have a diff capacity required
    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
        for (i = 1; i <= rMilestone.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
            var rScheduledEmp = rMilestone.sch_milestone_to_sch_empl_schedule.getRecord(i);
            
            removeMSEmpCapacityRecs(rMilestone.ms_id, rScheduledEmp.empl_id, rScheduledEmp.emp_workload_pct);
            defragCapRecs(RESOURCE_TYPE.Employee, rScheduledEmp.empl_id);
        }
    }
    else if (utils.hasRecords(rMilestone.sch_milestone_to_sch_ms_available_emps$selected)) {
        for (i = 1; i <= rMilestone.sch_milestone_to_sch_ms_available_emps$selected.getSize(); i++) {
            var rSelectedEmp = rMilestone.sch_milestone_to_sch_ms_available_emps$selected.getRecord(i);

            removeMSEmpCapacityRecs(rMilestone.ms_id, rSelectedEmp.empl_id, rSelectedEmp.emp_workload_pct);
            defragCapRecs(RESOURCE_TYPE.Employee, rSelectedEmp.empl_id); 
        }
    }

    if (bClearAvailEmps && utils.hasRecords(rMilestone.sch_milestone_to_sch_ms_available_emps)) {
        rMilestone.sch_milestone_to_sch_ms_available_emps.deleteAllRecords();
    }

    function defragCapRecs(sResType, uResID) {
        if (bDefragCapacityRecs && sResType && uResID) {
            if (dStartDate) {
                defragCapacityRecs(sResType, uResID, dStartDate, true);
            }

            if (dEndDate && dEndDate != dStartDate) {
                defragCapacityRecs(sResType, uResID, dEndDate, true);
            }
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_schedule>} rSchedule
 *
 * @properties={typeid:24,uuid:"AE4636BB-E6F0-434A-B144-5951DB9D0CBB"}
 */
function restoreMilestonesBackedUpDates(rSchedule) {
    if (utils.hasRecords(rSchedule.sch_schedule_to_sch_milestone)) {
        for (var i = 1; i <= rSchedule.sch_schedule_to_sch_milestone.getSize(); i++) {
            var rMS = rSchedule.sch_schedule_to_sch_milestone.getRecord(i);

            rMS.ms_date_scheduled = rMS.ms_date_scheduled_bak;
            rMS.ms_date_due = rMS.ms_date_due_bak;

            databaseManager.saveData(rMS);
        }
    }
}

/**
 * @param {String} sType - shift, equipment, employee
 * @param {UUID} uID
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"AA36019A-E12E-4121-9F32-AB117A874968"}
 */
function areThereAnyCapRecsForThisDate(sType, uID, dDate) {
    var sPKName;

    if (sType == 'shift') {
        sPKName = 'shift_id';
    }
    else if (sType == 'equipment') {
        sPKName = 'equip_id';
    }
    else if (sType == 'employee') {
        sPKName = 'empl_id';
    }

    var sSQL = "select count(*) \
                from sch_emp_capacity \
                where org_id = ? and " + sPKName + " = ? \
                and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);

    var aArgs = [globals.org_id, uID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * @param {String} sType - EQ, EM
 * @param {UUID} uID
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"FE61F595-F812-4FB3-B4DC-BB2129E0E0F1"}
 */
function areThereAnyScheduledJobsForThisResourceDate(sType, uID, dDate) {
    var sPKName;

    if (sType == RESOURCE_TYPE.Equipment) {
        sPKName = 'equip_id';
    }
    else if (sType == RESOURCE_TYPE.Employee) {
        sPKName = 'empl_id';
    }
    else if (sType == 'SH') {
        sPKName = 'shift_id';
    }

    var sSQL = "select count(*) \
                from sch_emp_capacity \
                where org_id = ? and " + sPKName + " = ? \
                and isnull(is_break, 0) = 0 and duration > 0 and capacity < 100 \
                and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);

    var aArgs = [globals.org_id, uID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * @param {String} sType - EQ, EM
 * @param {UUID} uID
 * @param {Date} dFromDate
 * @param {Date} dToDate
 * @param {Boolean} [bKeepTime]
 *
 * @return
 * @properties={typeid:24,uuid:"7B1A1DDF-E4F4-4BA9-856C-A2E063714190"}
 */
function areThereAnyScheduledJobsBetweenDates(sType, uID, dFromDate, dToDate, bKeepTime) {
    if (dFromDate == dToDate) {
        return false;
    }
    else {
        var nFromDate = scopes.avDate.getDateAsNum(dFromDate);
        var nToDate = scopes.avDate.getDateAsNum(dToDate);
        var sPKName;

        if (sType == RESOURCE_TYPE.Equipment) {
            sPKName = 'equip_id';
        }
        else if (sType == RESOURCE_TYPE.Employee) {
            sPKName = 'empl_id';
        }

        var sSQL = "select count(*) \
                    from sch_emp_capacity \
                    where org_id = ? and " + sPKName + " = ? \
                    and isnull(is_break, 0) = 0 and duration > 0 and capacity < 100 \
                    and shiftexcep_id is null and equipschexcep_id is null and empschexcep_id is null \
                    and schedule_date_num > ? and schedule_date_num < ?";

        var aArgs = [globals.org_id, uID.toString(), nFromDate, nToDate];
        
        return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
    }
}

/**
 * @param {String} sType - SH, EQ, EM
 * @param {UUID} uID
 * @param {Date} dDate
 *
 * @properties={typeid:24,uuid:"1470BE37-DB6B-44DD-98FA-58B55CCC1933"}
 */
function deleteCapRecsForThisDate(sType, uID, dDate) {
    var sPKName;

    if (sType == 'SH') {
        sPKName = 'shift_id';
    }
    else if (sType == RESOURCE_TYPE.Equipment) {
        sPKName = 'equip_id';
    }
    else if (sType == RESOURCE_TYPE.Employee) {
        sPKName = 'empl_id';
    }

    var sSQL = "delete \
                from sch_emp_capacity \
                where org_id = ? and " + sPKName + " = ? \
                and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);

    var aArgs = [globals.org_id, uID.toString()];

    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * Checks if the milestone can be moved to a specific resource
 * @param {UUID} oEquipId
 * @param {UUID} nEquipId
 *
 * @return
 * @properties={typeid:24,uuid:"099CE86E-8391-4887-9EFC-FFD026EDBE67"}
 */
function canMilestoneBeMovedToEquipment(oEquipId, nEquipId) {
    /***@type {JSRecord<db:/avanti/sch_workpool_machine>} ***/
    var rOldMachine = scopes.avDB.getRec('sch_workpool_machine', ['equip_id'], [oEquipId]);
    /***@type {JSRecord<db:/avanti/sch_workpool_machine>} ***/
    var rNewMachine = scopes.avDB.getRec('sch_workpool_machine', ['equip_id'], [nEquipId]);
    var milestoneMove = false;

    if (rOldMachine && rNewMachine) {
        if (rOldMachine.wrkpool_id == rNewMachine.wrkpool_id) {
            milestoneMove = true;
        }
        else {
            /***@type {JSRecord<db:/avanti/sch_workpool>} ***/
            var rOldWorkPool = rOldMachine.sch_workpool_machine_to_sch_workpool.getRecord(1);
            /***@type {JSRecord<db:/avanti/sch_workpool>} ***/
            var rNewWorkPool = rNewMachine.sch_workpool_machine_to_sch_workpool.getRecord(1);
            
            //If both belong to the same work family
            if (rOldWorkPool.wrkfam_id && rNewWorkPool.wrkfam_id && rOldWorkPool.wrkfam_id == rNewWorkPool.wrkfam_id) {
                milestoneMove = true;
            }
        }
    }

    return milestoneMove;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Date} dOldStartDate
 * @param {Date} dOldEndDate
 * @param {Date} dNewStartDate
 * @param {Date} dNewEndDate
 * @param {UUID} uOldEquipID
 * @param {UUID} uNewEquipID
 * @param {UUID} uOldEmpID
 * @param {UUID} uNewEmpID
 *
 * @return
 * @properties={typeid:24,uuid:"58F468C1-B4B0-4EBD-B2EE-2F45C2178EC5"}
 */
function canMilestoneBeMoved(rMilestone, dOldStartDate, dOldEndDate, dNewStartDate, dNewEndDate, uOldEquipID, uNewEquipID, uOldEmpID, uNewEmpID) {
    var bEquipChanged = uOldEquipID && uNewEquipID && uOldEquipID != uNewEquipID;
    var bEmpChanged = uOldEmpID && uNewEmpID && uOldEmpID != uNewEmpID;
    var bTimeChanged = dOldStartDate != dNewStartDate || dOldEndDate != dNewEndDate;
    var bSuccess = true;
    
    if (bEquipChanged || bEmpChanged || bTimeChanged) {
    	/**@type {Array<UUID>} */
        var aEmps = getEmpsWorkingOnMS(rMilestone.ms_id);

        if (bEmpChanged) {
            aEmps = scopes.avUtils.replaceArrayElement(aEmps, uOldEmpID.toString(), uNewEmpID.toString());
        }

        rMilestone.ms_date_scheduled = dNewStartDate;
        rMilestone.ms_date_due = dNewEndDate;

        clearMilestoneCapacity(rMilestone);

        scopes.avScheduling.bValidatingScheduleBoardMove = true;
        var dSchedStart = getNextFreeSpot(rMilestone, 'F', null, null, null, true, aEmps, uNewEquipID, null, null, null, null, bEmpChanged);
        scopes.avScheduling.bValidatingScheduleBoardMove = false;

        // ms can be moved to this time
        if (dSchedStart == dNewStartDate) {
            bSuccess = true;
			updateSchedWithNewTime(rMilestone);
        }
        // ms cant be moved to this time - revert
        else {
            rMilestone.ms_date_scheduled = dOldStartDate;
            rMilestone.ms_date_due = dOldEndDate;

            clearMilestoneCapacity(rMilestone);

            getNextFreeSpot(rMilestone, 'F', null, null, null, true, aEmps, uNewEquipID);

            bSuccess = false;
        }

        defragCapRecs();
    }

    return bSuccess;

    // lots of little moves of an ms causes fragmentation - doesnt affect functionality, but its unnecessary
    function defragCapRecs() {
        // this prevents resource/date combinations from being processed more than once
        _aResourceDatesProcessed = [];

        var dOldStartDateCopy = scopes.avDate.copyDate(dOldStartDate, true);
        var dOldEndDateCopy = scopes.avDate.copyDate(dOldEndDate, true);
        var dNewStartDateCopy = scopes.avDate.copyDate(dNewStartDate, true);
        var dNewEndDateCopy = scopes.avDate.copyDate(dNewEndDate, true);

        // all possible combinations of equip/emp/dates that could have changed
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uOldEquipID, dOldStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uOldEquipID, dOldEndDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uOldEquipID, dNewStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uOldEquipID, dNewEndDateCopy, true);

        defragCapacityRecs(RESOURCE_TYPE.Equipment, uNewEquipID, dOldStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uNewEquipID, dOldEndDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uNewEquipID, dNewStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Equipment, uNewEquipID, dNewEndDateCopy, true);

        defragCapacityRecs(RESOURCE_TYPE.Employee, uOldEmpID, dOldStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uOldEmpID, dOldEndDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uOldEmpID, dNewStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uOldEmpID, dNewEndDateCopy, true);

        defragCapacityRecs(RESOURCE_TYPE.Employee, uNewEmpID, dOldStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uNewEmpID, dOldEndDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uNewEmpID, dNewStartDateCopy, true);
        defragCapacityRecs(RESOURCE_TYPE.Employee, uNewEmpID, dNewEndDateCopy, true);

        if (aEmps) {
            for (var i = 0; i < aEmps.length; i++) {
                defragCapacityRecs(RESOURCE_TYPE.Employee, aEmps[i], dOldStartDateCopy, true);
                defragCapacityRecs(RESOURCE_TYPE.Employee, aEmps[i], dOldEndDateCopy, true);
                defragCapacityRecs(RESOURCE_TYPE.Employee, aEmps[i], dNewStartDateCopy, true);
                defragCapacityRecs(RESOURCE_TYPE.Employee, aEmps[i], dNewEndDateCopy, true);
            }
        }

        _aResourceDatesProcessed = [];
    }
}

/**
 * @param {String} sResourceType - EQ or EM
 * @param {UUID} uResourceID
 * @param {Date} dDate
 * @param {Boolean} [bCheckResourceDatesProcessed]
 *
 * @properties={typeid:24,uuid:"A098D089-A7BA-480F-AE38-A2C6E044EF24"}
 */
function defragCapacityRecs(sResourceType, uResourceID, dDate, bCheckResourceDatesProcessed) {
    try {
        if (sResourceType && uResourceID && dDate) {
            var bProceed = false;

            if (bCheckResourceDatesProcessed) {
                var sKey = sResourceType + '|' + uResourceID + '|' + dDate;

                if (_aResourceDatesProcessed.indexOf(sKey) == -1) {
                    _aResourceDatesProcessed.push(sKey);
                    bProceed = true;
                }
            }
            else {
                bProceed = true;
            }

            if (bProceed) {
                var sPKName = sResourceType == RESOURCE_TYPE.Equipment ? "equip_id" : "empl_id";
                var nDate = scopes.avDate.getDateAsNum(dDate);
                var sSQL = "select ec_id from sch_emp_capacity where org_id = ? and " + sPKName + " = ? and schedule_date_num = " + nDate;
                var aArgs = [globals.org_id, uResourceID.toString()];
                /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
                var fsCapRecs = scopes.avDB.getFSFromSQL(sSQL, "sch_emp_capacity", aArgs);
                /**@type {JSRecord<db:/avanti/sch_emp_capacity>} */
                var rPrevCapRec = null;

                if (utils.hasRecords(fsCapRecs)) {
                    fsCapRecs.sort('start_date asc');

                    for (var i = 1; i <= fsCapRecs.getSize(); i++) {
                        var rCapRec = fsCapRecs.getRecord(i);
                        
                        // check for broken equip cap rec and fix
						if (sResourceType == RESOURCE_TYPE.Equipment 
								&& utils.hasRecords(rCapRec.sch_emp_capacity_to_sch_milestone)
								&& utils.hasRecords(rCapRec.sch_emp_capacity_to_sch_milestone.sch_milestone_to_sch_equip_schedule)) {

							var rEqSched = rCapRec.sch_emp_capacity_to_sch_milestone.sch_milestone_to_sch_equip_schedule.getRecord(1);
							
			                // if caprec rec doesnt fall within start and stop for equip sched then its broken
	                        if (rCapRec.start_date < rEqSched.equipsch_start || rCapRec.end_date > rEqSched.equipsch_end) {
	                        	rCapRec.equip_ms_id = null;
	                        	rCapRec.capacity = 100;
	                            databaseManager.saveData(rCapRec);
	                            scopes.avUtils.devLog("SL-22451", "Bad caprec - caprec dates are outside sch_equip_schedule dates. equip: " + rCapRec.equip_id + ", ms: " + rCapRec.equip_ms_id);
	                        }
						}

                        if (rPrevCapRec && areCapRecsMergeable(rPrevCapRec, rCapRec)) {
                            rCapRec.start_date = rPrevCapRec.start_date;
                            rCapRec.starttime_num_mins = rPrevCapRec.starttime_num_mins;
                            rCapRec.duration += rPrevCapRec.duration;
                            
                            databaseManager.saveData(rCapRec);

                            fsCapRecs.deleteRecord(rPrevCapRec);
                            i--; // decrement i or we'll skip an element, because getSize() has changed now
                        }

                        rPrevCapRec = rCapRec;
                    }
                }
            }
        }
    }
    catch (ex) {
        // if an error occurs in defragCapacityRecs log it, dont stop. any problem indefragCapacityRecs is not serious enough to stop
        // scheduling. defragCapacityRecs does some db housekeeping, it doesnt affect functionality 
        application.output("Error in defragCapacityRecs: " + ex.message, LOGGINGLEVEL.ERROR);
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_emp_capacity>} rCapRec1
 * @param {JSRecord<db:/avanti/sch_emp_capacity>} rCapRec2
 *
 * @return
 * @properties={typeid:24,uuid:"3B5A842C-4FBC-40A5-8B8D-EC4EB73DD343"}
 */
function areCapRecsMergeable(rCapRec1, rCapRec2) {
    return rCapRec1.capacity == rCapRec2.capacity && rCapRec1.is_break == rCapRec2.is_break && 
           rCapRec1.is_shift_break == rCapRec2.is_shift_break && 
           rCapRec1.equip_ms_id == rCapRec2.equip_ms_id && rCapRec1.shift_id == rCapRec2.shift_id && 
           rCapRec1.shiftdet_id == rCapRec2.shiftdet_id && 
           rCapRec1.shiftexcep_id == rCapRec2.shiftexcep_id && 
           rCapRec1.equipschexcep_id == rCapRec2.equipschexcep_id &&
           rCapRec1.empschexcep_id == rCapRec2.empschexcep_id && rCapRec1.end_date == rCapRec2.start_date;
}

/**
 * @param {String} sResType - EQ or EM
 * @param {UUID} uResID
 * @param {Date} [dDate]
 *
 * @return
 * @properties={typeid:24,uuid:"5FD2FFA9-06B3-48D6-89C6-DA1E512F7116"}
 */
function isResourceAvailable(sResType, uResID, dDate) {
    var sPKName;

    if (!dDate) {
        dDate = application.getTimeStamp();
    }

    if (sResType == RESOURCE_TYPE.Equipment) {
        sPKName = 'equip_id';

        // have to make sure caprecs have been created for this equip/day
        if (getNumExistingCapacityRecsForDate(dDate, RESOURCE_TYPE.Equipment, uResID) == 0) {
            /**@type {JSRecord<db:/avanti/eq_equipment>} */
            var rEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uResID]);
            
            if (rEquip) {
                createEquipmentCapacityRecords(rEquip, null, dDate, 'F', true);
            }
        }
    }
    else if (sResType == RESOURCE_TYPE.Employee) {
        sPKName = 'empl_id';

        // have to make sure caprecs have been created for this emp/day
        if (getNumExistingCapacityRecsForDate(dDate, RESOURCE_TYPE.Employee, uResID) == 0) {
            /**@type {JSRecord<db:/avanti/sys_employee>} */
            var rEmp = scopes.avDB.getRec('sys_employee', ['empl_id'], [uResID]);
            
            if (rEmp) {
                scopes.avScheduling.createEmployeeCapacityRecords(rEmp, null, dDate, null, 'F', true);
            }
        }
    }

    var sSQL = "select count(*) \
                from sch_emp_capacity \
                where org_id = ? and " + sPKName + " = ? \
                and isnull(is_break, 0) = 0 and duration > 0 \
                and schedule_date_num = " + scopes.avDate.getDateAsNum(dDate);

    var aArgs = [globals.org_id, uResID.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * @param {Date} dDate
 * @param {Number} nAvailable
 * @param {JSRecord<db:/avanti/sch_shift_detail>} rShiftDet
 *
 * @properties={typeid:24,uuid:"23ECA0B7-9E95-45AB-B7B2-DD62E1B16B98"}
 */
function createEquipmentExceptionForDate(rEquip, dDate, nAvailable, rShiftDet) {
    var rEquipEx = rEquip.eq_equipment_to_sch_equip_excep.getRecord(rEquip.eq_equipment_to_sch_equip_excep.newRecord());

    rEquipEx.equipschexcep_start = dDate;
    rEquipEx.equipschexcep_start_num = scopes.avDate.getDateAsNum(rEquipEx.equipschexcep_start);
    rEquipEx.equipschexcep_end = dDate;
    rEquipEx.equipschexcep_time_start = rShiftDet.shiftdet_starttime;
    rEquipEx.equipschexcep_time_end = rShiftDet.shiftdet_endtime;
    rEquipEx.starttime_num_mins = rShiftDet.shiftdet_starttime_num_mins;
    rEquipEx.endtime_num_mins = rShiftDet.shiftdet_endtime_num_mins;
    rEquipEx.equipschexcep_avail = nAvailable;
    rEquipEx.duration = rEquipEx.endtime_num_mins - rEquipEx.starttime_num_mins;
    rEquipEx.auto_created_from_sched_board = 1;
    
    databaseManager.saveData(rEquipEx);
}

/**
 * @param {JSRecord<db:/avanti/sys_employee>} rEmp
 * @param {Date} dDate
 * @param {Number} nAvailable
 * @param {JSRecord<db:/avanti/sch_shift_detail>} rShiftDet
 *
 * @properties={typeid:24,uuid:"049B1403-992E-4DF2-82EC-DE5301EA9E35"}
 */
function createEmployeeExceptionForDate(rEmp, dDate, nAvailable, rShiftDet) {
    var rEmpEx = rEmp.sys_employee_to_sch_empl_excep.getRecord(rEmp.sys_employee_to_sch_empl_excep.newRecord());

    rEmpEx.empschexcep_start = dDate;
    rEmpEx.empschexcep_start_num = scopes.avDate.getDateAsNum(rEmpEx.empschexcep_start);
    rEmpEx.empschexcep_end = dDate;
    rEmpEx.empschexcep_time_start = rShiftDet.shiftdet_starttime;
    rEmpEx.empschexcep_time_end = rShiftDet.shiftdet_endtime;
    rEmpEx.starttime_num_mins = rShiftDet.shiftdet_starttime_num_mins;
    rEmpEx.endtime_num_mins = rShiftDet.shiftdet_endtime_num_mins;
    rEmpEx.empschexcep_avail = nAvailable;
    rEmpEx.duration = rEmpEx.endtime_num_mins - rEmpEx.starttime_num_mins;
    rEmpEx.auto_created_from_sched_board = 1;
    
    databaseManager.saveData(rEmpEx);
}

/**
 * @param {String} sResType - EQ or EM
 * @param {UUID} uResID
 * @param {Boolean} [bAllDatesForward]
 *
 * @return
 * @properties={typeid:24,uuid:"78E9888F-2A71-4C70-8370-2FEA5800D930"}
 */
function clearMilestonesUsingResource(sResType, uResID, bAllDatesForward) {
    var nDate = scopes.avDate.getDateAsNum(application.getTimeStamp());
    var sDateCondition = bAllDatesForward 
    						? " AND CONVERT(DATE, ms.ms_date_due, 112) >= '" + nDate + "' "
    						: " AND '" + nDate + "' BETWEEN CONVERT(DATE, ms.ms_date_scheduled, 112) AND CONVERT(DATE, ms.ms_date_due, 112) ";
    var aArgs = [globals.org_id, uResID.toString()];
    var sSQL;
    var aMS = [];

    if (sResType == RESOURCE_TYPE.Equipment) {
        sSQL = "select es.ms_id, es.equipsch_id \
                from sch_equip_schedule es \
                inner join sch_milestone ms on ms.ms_id = es.ms_id \
                where \
                	es.org_id = ? \
                	and es.equip_id = ? \
                	and isnull(ms.ms_flg_completed, 0) = 0 " + sDateCondition + 
				"ORDER BY ms.ms_date_scheduled ASC";                	
    }
    else if (sResType == RESOURCE_TYPE.Employee) {
        sSQL = "select es.ms_id, es.emplsch_id \
                from sch_empl_schedule es \
                inner join sch_milestone ms on ms.ms_id = es.ms_id \
                where \
                	es.org_id = ? \
                	and es.empl_id = ? \
                	and isnull(ms.ms_flg_completed, 0) = 0 " + sDateCondition + 
                "ORDER BY ms.ms_date_scheduled ASC";                	
    }
    
    var dsMS = scopes.avDB.getDataset(sSQL, aArgs);

    if (dsMS) {
        for (var i = 1; i <= dsMS.getMaxRowIndex(); i++) {
            var uMSID = dsMS.getValue(i, 1);
            var uESID = dsMS.getValue(i, 2);
            
            /**@type {JSRecord<db:/avanti/sch_milestone>} */
            var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [uMSID]);
            
            clearMilestoneCapacity(rMilestone);
            
            aMS.push(uMSID);
            
            if (sResType == RESOURCE_TYPE.Equipment) {
                scopes.avDB.deleteRecs('sch_equip_schedule', ['equipsch_id'], [uESID]);
            }
            else if (sResType == RESOURCE_TYPE.Employee) {
                scopes.avDB.deleteRecs('sch_empl_schedule', ['emplsch_id'], [uESID]);
            }
            
        }
    }

    return aMS;
}

/**
 * @param {String} sResType - EQ or EM
 * @param {UUID} uResID
 * @param {Date} dDate
 *
 * @properties={typeid:24,uuid:"047ED003-B90E-4091-8B1B-52B65E167C05"}
 */
function deleteResourceCapRecsForDate(sResType, uResID, dDate){
    var nDate = scopes.avDate.getDateAsNum(dDate);
    var sPKName = sResType == RESOURCE_TYPE.Equipment ? 'equip_id' : 'empl_id';
    var sSQL = "delete from sch_emp_capacity where org_id = ? and " + sPKName + " = ? and schedule_date_num = ?";
    var aArgs = [globals.org_id, uResID.toString(), nDate];

    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @param {UUID} uEquipID
 * @param {Date} dDate
 * @param {Number} nAvailable
 *
 * @properties={typeid:24,uuid:"E737AE3D-3EA7-4AEA-9A7D-0D1AAF05DC34"}
 */
function deleteEquipExceptionsForDate(uEquipID, dDate, nAvailable){
	if (uEquipID && dDate) {		
	    scopes.avDB.deleteRecs('sch_equip_excep', 
	        ['equip_id', 'equipschexcep_start_num', 'equipschexcep_avail'], 
	        [uEquipID.toString(), scopes.avDate.getDateAsNum(dDate), nAvailable]);
	}
}

/**
 * @param {UUID} uEmpID
 * @param {Date} dDate
 * @param {Number} nAvailable
 *
 * @properties={typeid:24,uuid:"58D41A51-ED61-46AF-8E1C-DE0D433EE49D"}
 */
function deleteEmpExceptionsForDate(uEmpID, dDate, nAvailable){
	if (uEmpID && dDate) {
	    scopes.avDB.deleteRecs('sch_empl_excep', 
	        ['empl_id', 'empschexcep_start_num', 'empschexcep_avail'], 
	        [uEmpID.toString(), scopes.avDate.getDateAsNum(dDate), nAvailable]);
	}
}

/**
 * @param {UUID} uSchedID
 *
 * @return
 * @properties={typeid:24,uuid:"B7444233-A408-4F0F-B75F-7755A9E76FDD"}
 */
function doesScheduleContainASplitMS(uSchedID){
    var sSQL = "select count(*) from sch_milestone where org_id = ? and sch_id = ? and is_split = 1";
    return scopes.avDB.SQLQuery(sSQL, null, [globals.org_id, uSchedID.toString()]) > 0;
}

/**
 * @param {String} sResType - EQ or EM
 * @param {UUID} uResID
 * @param {UUID|String} uNewShiftID
 * @param {Date} dFromDate
 * @param {Date} dToDate
 *
 * @properties={typeid:24,uuid:"60F2727D-9995-4503-AC17-A90626B6CFA3"}
 */
function deleteUnusedDateCapRecsOnShiftChange(sResType, uResID, uNewShiftID, dFromDate, dToDate){
    var sFromDate = scopes.avDate.getDateAsNum(dFromDate);
    var sToDateSQL = dToDate ? " and schedule_date_num <= " + scopes.avDate.getDateAsNum(dToDate) : null;
    var sPKName = sResType == RESOURCE_TYPE.Equipment ? 'equip_id' : 'empl_id';

    // get dates that have scheduled jobs in this time period
    var sSchedJobsSQL = "select distinct schedule_date_num \
                         from sch_emp_capacity \
                         where org_id = '" + globals.org_id + "' \
                         and " + sPKName + " = '" + uResID.toString() + "' \
                         and shift_id != '" + uNewShiftID.toString() + "' \
                         and isnull(is_break, 0) = 0 and duration > 0 and capacity < 100 \
                         and schedule_date_num >= " + sFromDate + sToDateSQL;
    
    var sDeleteSQL = "select ec_id from sch_emp_capacity \
                      where org_id = '" + globals.org_id + "' \
                      and " + sPKName + " = '" + uResID.toString() + "' \
                      and shift_id != '" + uNewShiftID.toString() + "' \
                      and schedule_date_num >= " + sFromDate + sToDateSQL + " \
                      and schedule_date_num not in (" + sSchedJobsSQL + ")";

    var fs = scopes.avDB.getFSFromSQL(sDeleteSQL, 'sch_emp_capacity');
    
    if(fs && fs.getSize() > 0){
        fs.deleteAllRecords();
    }
}

/**
 * @public 
 * 
 * @param {UUID} uRevHID
 * @param {Array<UUID>} aOrderScheduleIDs
 * 
 * @return {Array<UUID>}
 *
 * @properties={typeid:24,uuid:"500E1A57-F64A-48DB-8171-E849B2EBE2FF"}
 */
function getOrderGroupJobsWorkPools(uRevHID, aOrderScheduleIDs){
    var sPressTaskTypes = scopes.avText.arrayToString([1,2,3,7,8,9,10,15,99], ',');
    var sOrderScheduleIDs = scopes.avText.arrayToString(aOrderScheduleIDs, ',', "'");

    var sSQL = "select wp.wrkpool_id\
                from sch_milestone ms \
                inner join eq_equipment eq on eq.opcat_id = ms.opcat_id AND eq.lag_parent IS NULL \
                inner join sch_workpool_machine wpm on wpm.wrkpoolmach_mach_uuid = eq.equip_id \
                inner join sch_workpool wp on wp.wrkpool_id = wpm.wrkpool_id \
                inner join sa_order_revision_detail_section sec on sec.ordrevds_id = ms.ordrevds_id \
                inner join sa_order_revision_detail det on det.ordrevd_id = sec.ordrevd_id \
                inner join sa_order_revds_task ot on ot.ordrevdstask_id = ms.ordrevdstask_id \
                inner join sa_task task on task.task_id = ot.task_id \
                where wp.org_id = ? and det.ordrevh_id = ? and wp.group_jobs_by_order = 1 \
                and task.tasktype_id in (" + sPressTaskTypes + ") \
                and ms.sch_id in (" + sOrderScheduleIDs + ") \
                group by wp.wrkpool_id having count(ms.ms_id) > 1";
    var aArgs = [globals.org_id, uRevHID.toString()];
    
    /**@type {Array<UUID>} */
    var aWorkPoolIDs = scopes.avDB.SQLQueryReturnMultiRecArray(sSQL, aArgs);
    
    return aWorkPoolIDs;
}

/**
 * @public 
 * 
 * @param {Boolean} [bJustCheckShiftSetup]
 * 
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"7AB0320F-A55F-44BD-9134-3CA1FD62070B"}
 */
function IsShiftSetupOK(bJustCheckShiftSetup) {
    var dNow = new Date();
    var nNumFutureEquipScheds = scopes.avDB.SQLQuery("select count(*) from sch_equip_schedule where org_id = ? and equipsch_end > ?",
        null, [globals.org_id, dNow]);
    var nNumEquipShifts = scopes.avDB.SQLQuery("select count(*) from sch_equipment_shift where org_id = ?",
        null, [globals.org_id]);
    var bFixShiftSetup = nNumEquipShifts == 0;
    var bRunConversion = !bJustCheckShiftSetup && nNumFutureEquipScheds > 0 && !hasScheduleConversionBeenRun();

    if (bFixShiftSetup && bRunConversion) {
        scopes.avText.showWarning('ShiftsNotSetupCorrectly_both', null, null, null, null, 500, 250);
        return false;
    }
    else if (bFixShiftSetup) {
        scopes.avText.showWarning('ShiftsNotSetupCorrectly_shifts', null, null, null, null, 500, 250);
        return false;
    }
    else if (bRunConversion) {
        scopes.avText.showWarning('ShiftsNotSetupCorrectly_conversion', null, null, null, null, 500, 250);
        return false;
    }
    // if we pass all tests then set 'conversion has been run' flag, otherwise if we add new jobs to sched
    // after this the next time we some here it will think a conversion needs to be run 
    else if(!hasScheduleConversionBeenRun()){
        setScheduleConversionHasBeenRunFlag();
    }

    return true;
}

/**
 * @properties={typeid:24,uuid:"2ACF2CEB-09F6-4143-886D-8FB1F6C9AA42"}
 */
function createCapacityRecsForExistingEquipSchedRecs(){
    if(IsShiftSetupOK(true)){
        if(hasScheduleConversionBeenRun()){
            if(scopes.avText.showYesNoQuestion('thisHasAlreadyBeenRun') == scopes.avText.no){
                return;
            }
        }
        
        bInQuickSchedule = true; // this ensures duplicate 'cant sched ms' msgs arent shown 
        scopes.avText.clearMsgsShown();
        var dNow = new Date();
        var sSQL = "select es.ms_id \
                    from sch_equip_schedule es \
                    inner join sch_milestone ms on ms.ms_id = es.ms_id \
                    left outer join sch_emp_capacity ec on ec.equip_ms_id = es.ms_id \
                    left outer join sch_ms_emp_capacity mec on mec.ms_id = es.ms_id \
                    where es.org_id = ? and es.equipsch_end > ? and ms.ms_date_due > ? \
                    and es.equipsch_start is not null and ms.ms_date_scheduled is not null \
                    and ec.ec_id is null and mec.msec_id is null";
        var aArgs = [globals.org_id, dNow, dNow];
        /**@type {JSFoundset<db:/avanti/sch_milestone>} */
        var fsMS = scopes.avDB.getFSFromSQL(sSQL, "sch_milestone", aArgs);
            
        if(utils.hasRecords(fsMS)){
            for(var i=1; i<=fsMS.getSize(); i++){
                var rMS = fsMS.getRecord(i);
                
                getNextFreeSpot(rMS, "F", null, null, null, null, null, null, true, null, null, true);
            }
            
            scopes.avText.showInfo("Schedule updated. Capacity records created for " + fsMS.getSize() + " milestones.", true);
        }
        else{
            scopes.avText.showInfo("Nothing to update. There were no milestones needing updating.", true);
        }
        
        setScheduleConversionHasBeenRunFlag();
        
        bInQuickSchedule = false;
        scopes.avText.clearMsgsShown();
    }
}

/**
 * @properties={typeid:24,uuid:"194EC2D7-B3C4-4502-A343-31B093B6202E"}
 */
function setScheduleConversionHasBeenRunFlag(){
    var sSQL = "insert into db_log (db_log_id, org_id, db_log_date, db_log) values (?, ?, ?, ?)";
    var aArgs = [application.getUUID().toString(), globals.org_id, application.getTimeStamp(), "createCapacityRecsForExistingEquipSchedRecs has been run"];
    scopes.avDB.RunSQL(sSQL, null, aArgs);
}

/**
 * @return
 * @properties={typeid:24,uuid:"A4BBA5AE-A644-4F28-AF6D-03939B02242F"}
 */
function hasScheduleConversionBeenRun(){
    var sSQL = "select count(*) from db_log where org_id = ? and db_log = ?";
    var aArgs = [globals.org_id, "createCapacityRecsForExistingEquipSchedRecs has been run"];
    return scopes.avDB.SQLQuery(sSQL, null,aArgs) > 0;
}

/**
 * @param {Boolean} [bShowSF_QS_Warning]
 * 
 * @properties={typeid:24,uuid:"C3CA950B-21D7-4021-8FF5-C573CB2EF636"}
 */
function showScheduleBoardLockedMessage(bShowSF_QS_Warning) {
    if (_to_sys_organization.org_sch_locked_empuuid) {
	    // By default always show the Unlock & Revert button if the user has privileges button
	    bShowSF_QS_Warning = typeof bShowSF_QS_Warning !== 'undefined' ? bShowSF_QS_Warning : false;
	
	    // Get the employee that locked the schedule board
	    var sSQL = "SELECT empl_full_name, empl_first_name \
	                FROM sys_employee \
	                WHERE org_id = ? \
	                    AND empl_id = ?";    
	    
	    var aArgs = [globals.org_id.toString(), _to_sys_organization.org_sch_locked_empuuid.toString()];
	    var dsData = databaseManager.getDataSetByQuery(scopes.globals.avBase_dbase_avanti, sSQL, aArgs, -1);
	    
	    var sEmployeeFullName = '';
	    var sEmployeeFirstName = '';
	
	    if (dsData && dsData.getMaxRowIndex() > 0) {
	        sEmployeeFullName = dsData.getValue(1,1);
	        sEmployeeFirstName = dsData.getValue(1,2);
	    }
	    
	    var nTimeDiff = 0;
	    var sTimeType = '';
	
	    var nOptimizeInProgress = _to_sys_organization.org_sch_optimize_in_progress;
	    
	    if (nOptimizeInProgress == 1) {
	        nTimeDiff = scopes.avDate.getDiffInMinutes(_to_sys_organization.org_sch_locked_datetime, application.getTimeStamp()).toFixed(0);
	    }
	    else {
	        nTimeDiff = scopes.avDate.getDiffInMinutes(_to_sys_organization.org_sch_optimize_cmpl_datetime, application.getTimeStamp()).toFixed(0);
	    }    
	
	    // if the difference in time is greater than 60 minutes we want the message to say hours
	    if (nTimeDiff > 60) {
	        nTimeDiff = ( nTimeDiff / 60 );
	
	        if ( ( nTimeDiff % 1 ).toFixed(1) > 0.0) {
	            nTimeDiff = nTimeDiff.toFixed(1);
	        }
	        else {
	            nTimeDiff = nTimeDiff.toFixed(0);
	        }
	
	        sTimeType = 'hour';        
	    }
	    else {
	        sTimeType = 'minute';        
	    }
	
	    if (nTimeDiff < 1) {
	        // Want the dialog to not show a number so changing it to string just before showing the message.
	        nTimeDiff = '';
	        sTimeType = 'less than 1 ' + sTimeType;
	    }
	    else if (nTimeDiff > 1) {
	        // if greater than 1 change type to plural
	        sTimeType = sTimeType + 's';
	    } 
	        
	    // Increase the size of the dialog to fit the message
	    globals.DIALOGS.setDialogWidth(650);
	    
	    if (bShowSF_QS_Warning == false && 
	            (globals["avSecurity_checkForUserRight"]("Schedule_Board", 'schedule_board_override_lock', globals.avBase_employeeUserID) 
	                    || _to_sys_employee$avbase_employeeuuid.empl_id.toString() == _to_sys_organization.org_sch_locked_empuuid)) {
	        
	        var answer;
	        
	        if (nOptimizeInProgress == 1) {
	            answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_optimize_locked_in_progress',
	                [sEmployeeFullName, nTimeDiff.toString(), sTimeType]), 
	                i18n.getI18NMessage('avanti.dialog.org_sch_revert_unlock'), i18n.getI18NMessage('avanti.dialog.ok'));
	        }
	        else {
	            answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_locked',
	                        [sEmployeeFullName, nTimeDiff.toString(), sTimeType, sEmployeeFirstName]), 
	                        i18n.getI18NMessage('avanti.dialog.org_sch_revert_unlock'), i18n.getI18NMessage('avanti.dialog.ok'));
	        }
			
	        if (answer == i18n.getI18NMessage('avanti.dialog.org_sch_revert_unlock')) {
	            
	            var sOptimizeSessionID = _to_sys_organization.org_sch_optimize_session_id;
	            
	            clearOrgScheduleOptimizeDetails();
	            
	            if (nOptimizeInProgress == 1) {
	                _to_sys_organization.org_sch_optimize_session_id = sOptimizeSessionID;
	                _to_sys_organization.org_sch_optimize_cancel_req = 1;
	                _to_sys_organization.org_sch_optimize_cancl_empuuid = _to_sys_employee$avbase_employeeuuid.empl_id.toString();
	                databaseManager.saveData(_to_sys_organization);            
	            }
	            
	            forms['sch_agenda_dtl'].revertOptimizedSchedule(sOptimizeSessionID);
	            
	            notifyActiveSchedulers(true);            
	            forms['sch_agenda_dtl'].loadView();
	        }
	        
	    }
	    else if (bShowSF_QS_Warning == true) {
	        // There is a seperate if statement because when this function is called the optimization process may have completed.
	        // Don't want it to go into else statement showing a message that schedule is locked for Shop Floor View and Quick Schedule
	        if (nOptimizeInProgress == 1) {
	            globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_optimize_in_progress', 
	                [sEmployeeFullName, nTimeDiff.toString(), sTimeType]), i18n.getI18NMessage('avanti.dialog.ok'));    
	        }        
	    }
	    else {        
	        if (nOptimizeInProgress == 1) {
	            globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_optimize_locked_in_progress', 
	                    [sEmployeeFullName, nTimeDiff.toString(), sTimeType]), i18n.getI18NMessage('avanti.dialog.ok'));
	        }
	        else {
	            globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.scheduleBoard'), i18n.getI18NMessage('avanti.dialog.org_sch_locked', 
	                [sEmployeeFullName, nTimeDiff.toString(), sTimeType, sEmployeeFirstName]), i18n.getI18NMessage('avanti.dialog.ok'));
	        }        
	    }
	    
	    globals.DIALOGS.resetDialogSize();
    }    
}

/**
 * Updates the organization record with information about the usage of the Schedule Board when Optimized
 *
 * @param {Number} nLocked
 * @param {UUID} uLockedEmpuuid
 * @param {Date} dLockedDate
 * @param {Number} nOptimizeInProgress
 * @param {Date} dOptimizeCmplDate
 * @param {UUID} uOptimizeSessionID
 * @param {Number} nOptimizeCancelReq
 * @param {UUID} uOptimizeCancelEmpuuid
 * @param {Boolean} [bSetCancelSessionID]
 *
 * @properties={typeid:24,uuid:"BDFAD942-0C0D-4A9A-9944-8B7E2AC10945"}
 */
function updateOrgScheduleOptimizeDetails(nLocked, uLockedEmpuuid, dLockedDate, nOptimizeInProgress, dOptimizeCmplDate, uOptimizeSessionID, nOptimizeCancelReq, uOptimizeCancelEmpuuid, bSetCancelSessionID) {    
    _to_sys_organization.org_sch_locked = nLocked;
    _to_sys_organization.org_sch_locked_empuuid = uLockedEmpuuid;
    _to_sys_organization.org_sch_locked_datetime = dLockedDate;
    _to_sys_organization.org_sch_optimize_in_progress = nOptimizeInProgress;
    _to_sys_organization.org_sch_optimize_cmpl_datetime = dOptimizeCmplDate;
    _to_sys_organization.org_sch_optimize_session_id = uOptimizeSessionID;
    _to_sys_organization.org_sch_optimize_cancel_req = nOptimizeCancelReq;
    _to_sys_organization.org_sch_optimize_cancl_empuuid = uOptimizeCancelEmpuuid;

    // sl-23886 - org_sch_cancel_session_id was being used for 2 different purposes: 
    // 1 - to indicate that the optimize routine is currently being run - alert other user trying to edit scheduleborad
    // 2 - to find/revert to old milestone positions after optimize/cancel
    // We cant have these 2 different function dependent on the same flag - created a new flag (org_sch_cancel_session_id) for the cancel/revert part
	if (bSetCancelSessionID) {
	    _to_sys_organization.org_sch_cancel_session_id = _to_sys_organization.org_sch_optimize_session_id;
	}
    
    databaseManager.saveData(_to_sys_organization);
}

/**
 * Clears the organization record with information about the usage of the Schedule Board when Optimized
 * 
 * @param {Boolean} [bClearCancelSessionID]
 * 
 * @properties={typeid:24,uuid:"F35FC510-C1D7-437F-B1C9-F58E5C36E844"}
 */
function clearOrgScheduleOptimizeDetails(bClearCancelSessionID) {
    updateOrgScheduleOptimizeDetails(0, null, null, 0, null, null, 0, null, bClearCancelSessionID);
}

/**
 * @public
 * 
 * Loops thru all a milestones predecessors until it finds one that is schedulable and returns it, or null if one not found
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return {JSRecord<db:/avanti/sch_milestone>}
 *
 * @properties={typeid:24,uuid:"E82B6421-1655-4702-A3F2-301456A1D454"}
 */
function getNextSchedulablePredecessor(rMilestone){
    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$pred_ms_id)) {
        var rPredecessor = rMilestone.sch_milestone_to_sch_milestone$pred_ms_id.getRecord(1);

        if (rPredecessor.sch_milestone_to_sys_department.dept_schedule_flag) {
            return rPredecessor;
        }
        else {
            return getNextSchedulablePredecessor(rPredecessor);
        }
    }

    return null;
}


/**
 * Get order detail line production duration in days
 *
 * <AUTHOR> Dol
 * @since Jul 16, 2018
 * @param {JSRecord<db:/avanti/sa_order_revision_detail>} rOrderDetail
 * @returns {Number} Duration in days
 * @public
 *
 * @properties={typeid:24,uuid:"28D8975D-DB13-43BE-9D13-88B86E7C24FC"}
 */
function getOrderLineProductionTimeInDays(rOrderDetail) {
    
    if (!rOrderDetail) {
        return null;
    }
    
    if (utils.hasRecords(rOrderDetail.sa_order_revision_detail_to_sch_schedule)
        && utils.hasRecords(rOrderDetail.sa_order_revision_detail_to_sch_schedule.sch_schedule_to_sch_milestone)) {
         
        /***@type {{sql:String,
         *          args:Array,
         *          server:String,
         *          maxRows:Number,
         *          table:String}}*/
        var oSQL = { };
        oSQL.args = [globals.org_id, rOrderDetail.ordrevd_id.toString(),globals.org_id, rOrderDetail.ordrevd_id.toString()];
        oSQL.sql = "SELECT datediff (day,\
                        (SELECT TOP (1) sch_milestone.ms_date_scheduled\
                            FROM sch_schedule\
                            INNER JOIN sch_milestone ON sch_schedule.sch_id = sch_milestone.sch_id\
                            WHERE (sch_schedule.org_id = ?) AND (sch_schedule.ordrevd_id = ?)\
                            ORDER BY sch_milestone.sequence_nr)\
                            ,\
                         (SELECT TOP (1) sch_milestone.ms_date_due\
                            FROM sch_schedule\
                            INNER JOIN sch_milestone ON sch_schedule.sch_id = sch_milestone.sch_id\
                             WHERE (sch_schedule.org_id = ?) AND (sch_schedule.ordrevd_id = ?)\
                             ORDER BY dbo.sch_milestone.sequence_nr DESC))\
                    ";
        /**@type {JSDataSet} **/
        var dsData = globals["avUtilities_sqlDataset"](oSQL);
        
        if (dsData && dsData.getMaxRowIndex() == 1) {
            var nDuration = dsData.getValue(1, 1);

            if (nDuration == 0) {
                nDuration = 1; //The same day is 1 day.
            }
            
            return nDuration;
        }
        else {
            return null;
        }
    }
    else {
        return null;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {String} [sDirection] - (F)orwards or (B)ackwards
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"89409299-36A6-4CF3-9727-95D042021E7A"}
 */
function areThereAnyActiveEquipmentForMilestone(rMilestone, sDirection) {
    var bActiveEquipFound = false;
    
    if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
        var rDefaultEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);

        if (rDefaultEquip.equip_active) {
            bActiveEquipFound = true;
        }
        else if (utils.hasRecords(rDefaultEquip.eq_equipment_to_sch_workpool_machine) 
                && utils.hasRecords(rDefaultEquip.eq_equipment_to_sch_workpool_machine.sch_workpool_machine_to_sch_workpool)) {

            var rDefaultWorkPool = rDefaultEquip.eq_equipment_to_sch_workpool_machine.sch_workpool_machine_to_sch_workpool.getRecord(1);

            if (areThereAnyActiveEquipmentInWorkPool(rDefaultWorkPool, sDirection)) {
                bActiveEquipFound = true;
            }
            else if (utils.hasRecords(rDefaultWorkPool.sch_workpool_to_sch_workfamily)) {
                var fsFamilyWorkPools = rDefaultWorkPool.sch_workpool_to_sch_workfamily.sch_workfamily_to_sch_workpool;

                for (var i = 1; i <= fsFamilyWorkPools.getSize(); i++) {
                    var rFamilyWorkPool = fsFamilyWorkPools.getRecord(i);

                    if (rFamilyWorkPool.wrkpool_id != rDefaultWorkPool.wrkpool_id) {
                        if (areThereAnyActiveEquipmentInWorkPool(rFamilyWorkPool, sDirection)) {
                            bActiveEquipFound = true;
                            break;
                        }
                    }
                }
            }

        }
    }
    
    return bActiveEquipFound;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2D3DB7D0-EB6E-4CD5-920D-F3053EAA6879"}
 */
function doesMilestoneScheduleEmps(rMilestone){
    if (scopes.avUtils.getSysPrefNum('ScheduleEmployees')
            && utils.hasRecords(rMilestone.sch_milestone_to_sys_operation_category)
            && rMilestone.sch_milestone_to_sys_operation_category.opcat_schedule_employees) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_workpool>} rWorkPool
 * @param {String} [sDirection] - (F)orwards or (B)ackwards
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2A79FD5C-FDFA-4BD7-A75F-D3CC012E7007"}
 */
function areThereAnyActiveEquipmentInWorkPool(rWorkPool, sDirection) {
    // check if it's a valid workpool - has to have either load balance or schedule forwards enabled
    if (rWorkPool.wrkpool_flg_load_bal || (rWorkPool.wrkpool_nr_shifts > 0 && sDirection != "B")) {
        for (var i = 1; i <= rWorkPool.sch_workpool_to_sch_workpool_machine.getSize(); i++) {
            var rMachine = rWorkPool.sch_workpool_to_sch_workpool_machine.getRecord(i);

            if (utils.hasRecords(rMachine.sch_workpool_machine_to_eq_equipment)) {
                var rEquip = rMachine.sch_workpool_machine_to_eq_equipment.getRecord(1);

                if (rEquip.equip_active) {
                    return true;
                }
            }
        }
    }
    
    return false;
}


/**
 * To calcualte Delivery date based on Lead time, plant schedule and order date
 * 
 * @param vdDate
 * @param nLeadDays
 * @param uPlantID
 *
 * @return
 * @properties={typeid:24,uuid:"0505A1F0-E83B-431C-BEA8-67D9174E5235"}
 */
function calculateAvgLeadDays(vdDate, nLeadDays, uPlantID) {
    //application.output(nLeadDays);
    vdDate = new Date(vdDate);
    var vfDate = new Date(vdDate).toLocaleDateString();
    var oSQL = {};
    /***@type {JSDataSet} */
    var dsData;

    oSQL.sql = "SELECT \
                shiftdet_flg_friday, shiftdet_flg_saturday, \
                shiftdet_flg_sunday, shiftdet_flg_monday, shiftdet_flg_tuesday, \
                shiftdet_flg_wednesday, shiftdet_flg_thursday, \
                shiftdet_startdate, shiftdet_enddate, DATENAME(dw, '"+ vfDate +"') mWeekDay, sp.shift_id  \
                FROM sch_plant_shift sp INNER JOIN sch_shift_detail sd ON sp.shift_id = sd.shift_id \
                WHERE  sp.org_id = ? AND sp.plant_id = ? AND ISNULL(shiftdet_startdate, '"+ vfDate +"') <= '"+ vfDate +"' AND ISNULL(shiftdet_enddate, '"+ vfDate +"') >= '"+ vfDate +"' ";

    oSQL.args = [globals.org_id, uPlantID.toString()];
    dsData = globals["avUtilities_sqlDataset"](oSQL);

    var nOffDays = 0, nExpDays = 0;
    var uShift_id;
    var dExpDate;
    dExpDate = scopes.avDate.addDaysNoDSTChange(vdDate, nLeadDays);
    if (dsData && dsData.getMaxRowIndex() > 0) {
        var sRow = dsData.getRowAsArray(1);
        nOffDays += getNoOfNonWorkingDays(sRow[9]);
        uShift_id = sRow[10];
        dExpDate = scopes.avDate.addDaysNoDSTChange(dExpDate, nOffDays);
        nExpDays = getExceptionDays(vdDate);
        dExpDate = scopes.avDate.addDaysNoDSTChange(dExpDate, nExpDays);
        application.output(nOffDays + ' ' + nExpDays +' '+ dExpDate);
        if (nOffDays || nExpDays) {            
        //Re-runing the method to check if calcualted Expected Date is non-working day for plant            
            dExpDate = calculateAvgLeadDays(dExpDate, 0, uPlantID);
        }
    }
    
    return dExpDate;
    
    function getNoOfNonWorkingDays(sStartDay) {
        var nNonWorkingDays = 0;
        var nLoopDay = 0;
        var nStartDay = 0;
        var nLoopLmt = nLeadDays;
        if (sStartDay == 'Friday') {
            nStartDay = 0;
        }
        else if (sStartDay == 'Saturday') {
            nStartDay = 1;
        }
        else if (sStartDay == 'Sunday') {
            nStartDay = 2;
        }
        else if (sStartDay == 'Monday') {
            nStartDay = 3;
        }
        else if (sStartDay == 'Tuesday') {
            nStartDay = 4;
        }
        else if (sStartDay == 'Wednesday') {
            nStartDay = 5;
        }
        else if (sStartDay == 'Thursday') {
            nStartDay = 6;
        }

        nLoopDay = nStartDay;
        if (nLoopLmt == 0) {
            nLoopLmt = 1 + nStartDay;
        }
        else {
            nLoopLmt = nLeadDays + nStartDay;
        }

        for (var iip = nStartDay; iip < nLoopLmt; iip++) {
            if (!sRow[nLoopDay]) {
                nNonWorkingDays += 1;
                iip--; //For every non working day means we have to find another working day
            }
            nLoopDay += 1; //check for next day
            //starts over from first day of the week
            if (nLoopDay > 6) {
                nLoopDay = 0; //friday
            }
        }
        return nNonWorkingDays;
    }

    function getExceptionDays(veDate) {
        veDate = veDate.toLocaleDateString();
        var vdExpDate = dExpDate.toLocaleDateString();
        var oSQLe = {};
        /***@type {JSDataSet} */
        var dsDatae;

        oSQLe.sql = "SELECT \
                    ISNULL(SUM(CASE WHEN ISNULL(shiftexcep_avail,0) = 0 THEN (DATEDIFF(d, \
                    CASE WHEN shiftexcep_startdate_num > ? THEN shiftexcep_startdate_num ELSE ? END, \
                    CASE WHEN shiftexcep_enddate_num > ? THEN ? ELSE shiftexcep_enddate_num END)+1) * 1 \
                    ELSE (DATEDIFF(d, \
                    CASE WHEN shiftexcep_startdate_num > ? THEN shiftexcep_startdate_num ELSE ? END, \
                    CASE WHEN shiftexcep_enddate_num > ? THEN ? ELSE shiftexcep_enddate_num END)+1) * -1 END), 0) \
                    FROM sch_shift_excep se \
                    WHERE  se.org_id = ? AND se.shift_id = ? AND shiftexcep_enddate_num >= ? AND shiftexcep_startdate_num <= ? ";

        oSQLe.args = [veDate, veDate, vdExpDate, vdExpDate, veDate, veDate, vdExpDate, vdExpDate, globals.org_id, uShift_id, veDate, vdExpDate];
        dsDatae = globals["avUtilities_sqlDataset"](oSQLe);

        var nCountEDays = 0;
        if (dsDatae && dsDatae.getMaxRowIndex() > 0) {
            nCountEDays = dsDatae.getRowAsArray(1)[0];
        }
        return nCountEDays;
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_workpool>} rWorkPool
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {UUID} uCurEquipID
 * @param {String} sDirection
 * @param {Date} dScheduleDateTime
 *
 * @return
 * @properties={typeid:24,uuid:"678E84F7-912E-4CFE-B17B-E68EC4640E9F"}
 */
function loadBalanceUsingWorkPool(rWorkPool, rMilestone, uCurEquipID, sDirection, dScheduleDateTime) {
    /**@type {{startDate:Date, endDate:Date}} */
    var oEquipmentWindow = null;
    
    if (rWorkPool.sch_workpool_to_sch_workpool_machine && rWorkPool.sch_workpool_to_sch_workpool_machine.getSize() > 1) {
        /**@type {JSRecord<db:/avanti/eq_equipment>} */
        var rChosenEquipment = null
        var nMaxFreeMins = 0;
        var uMaxFreeEquipID = null;
        var nCurEquipFreeMins = 0;
        var bEquipmentChanged = false;
        var bFoundWindow = false;
        var sDebugInfo = "";
        var auEquipIDs = [];
        var i;
        var rWPMachine;
        /**@type {JSRecord<db:/avanti/eq_equipment>} */
        var rEquip;

        for (i = 1; i <= rWorkPool.sch_workpool_to_sch_workpool_machine.getSize(); i++) {
        	rWPMachine = rWorkPool.sch_workpool_to_sch_workpool_machine.getRecord(i);
            rEquip = rWPMachine.sch_workpool_machine_to_eq_equipment.getRecord(1);
            
			if (isEquipActive(rEquip)) {
				// have to call createEquipmentCapacityRecords() in case cap recs havent been created for this date yet
				createEquipmentCapacityRecords(rEquip, rMilestone.ms_time_budget, dScheduleDateTime, 'F');

				auEquipIDs.push(rEquip.equip_id)
			}
        }

        // sl-20170 - when scheduling many jobs at once we start the scheduling process at the same point (dScheduleDateTime) for all. but at some point all the time slots for today
        // get used up, and we have to start on the next day. but dScheduleDateTime is always the starting day, so we are always checking the load on that day. so we have to update
        // dScheduleDateTime to the next day that has availability, so we are checking the load on the day we will actually schedule it. However, this causes a problem with
        // 'group jobs by order'. in that case all the jobs on the order have to use the same press, and we pick the press using the least busy on the FIRST day only. we dont care
        // how busy the resources are on subsequent days. so we dont change dScheduleDateTime if doing _bValidateLoadBalanced 
		if (!_bValidateLoadBalanced) {
			if (sDirection == 'F') {
				var dEarliestFreeTime = getEarliestFreeTimeFromEquipments(auEquipIDs, dScheduleDateTime);

				if (dEarliestFreeTime > dScheduleDateTime) {
					dScheduleDateTime = dEarliestFreeTime
				}
			}
			else if (sDirection == 'B') {
				var dLatestFreeTime = getLatestFreeTimeFromEquipments(auEquipIDs, dScheduleDateTime);

				if (dLatestFreeTime && dLatestFreeTime < dScheduleDateTime) {
					dScheduleDateTime = dLatestFreeTime
				}
			}
		}
        
        if (_bValidateLoadBalanced) {
			if (application.isInDeveloper()) {
	            application.output('*******************************************************');
	            application.output('************************* uCurEquipID: ' + uCurEquipID);
	            application.output('************************* rMilestone: ' + rMilestone.ms_id);
	            application.output('************************* dScheduleDateTime: ' + dScheduleDateTime);
			}
            
			if (_to_sys_organization.org_name == 'Franklin Templeton Investments') {
				sDebugInfo = 'uCurEquipID: ' + uCurEquipID + ', rMilestone: ' + rMilestone.ms_id + ', dScheduleDateTime: ' + dScheduleDateTime;
				scopes.avUtils.devLog("SL-17857", sDebugInfo);
			}
        }
        
        rWorkPool.sch_workpool_to_sch_workpool_machine.sort('sequence_nr asc');

        for (i = 1; i <= rWorkPool.sch_workpool_to_sch_workpool_machine.getSize(); i++) {
            rWPMachine = rWorkPool.sch_workpool_to_sch_workpool_machine.getRecord(i);

            if (utils.hasRecords(rWPMachine.sch_workpool_machine_to_eq_equipment) && isEquipActive(rWPMachine.sch_workpool_machine_to_eq_equipment.getRecord(1))) {
                rEquip = rWPMachine.sch_workpool_machine_to_eq_equipment.getRecord(1);

                var uGroupByOrderID = null;
                
                if (_bValidateLoadBalanced) {
                    uGroupByOrderID = rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.ordh_id;
                }
                
                var nFreeMins = getFreeCapacityForDate(dScheduleDateTime, RESOURCE_TYPE.Equipment, rWPMachine.equip_id, 100, uGroupByOrderID);

                if (_bValidateLoadBalanced) {
					if (application.isInDeveloper()) {
	                    application.output('************************* rEquip: ' + rEquip.equip_id + ' - ' + rEquip.equip_code);
	                    application.output('************************* nFreeMins: ' + nFreeMins);
					}
					
					if (_to_sys_organization.org_name == 'Franklin Templeton Investments') {
						sDebugInfo = 'uCurEquipID: ' + uCurEquipID + ', rMilestone: ' + rMilestone.ms_id + ', dScheduleDateTime: ' + dScheduleDateTime + ', rEquip: ' 
							+ rEquip.equip_id + ' - ' + rEquip.equip_code + ', nFreeMins: ' + nFreeMins;
						scopes.avUtils.devLog("SL-17857", sDebugInfo);
					}
                }
                
                if (nFreeMins > nMaxFreeMins) {
                    nMaxFreeMins = nFreeMins;
                    uMaxFreeEquipID = rWPMachine.equip_id;
                }

                if (rWPMachine.equip_id == uCurEquipID) {
                    nCurEquipFreeMins = nFreeMins;
                }
            }
        }

        if (application.isInDeveloper() && _bValidateLoadBalanced) {
            application.output('*******************************************************');
        }
        
        if (nMaxFreeMins > 0) {
            // current equip is one of the most free equip - use it
            if (nCurEquipFreeMins == nMaxFreeMins) {
                rChosenEquipment = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uCurEquipID]);
                
                // SL-17096 - there was a conflict between the ‘group jobs by press’ functionality and the load balancing 
                // functionality. When both these options are used it chooses the press that is least busy for the
                // given day, and uses that press for all the jobs. The problem we ran into is some of the press 
                // milestone’s were scheduled on 1 day and others spill over into the next day. And the least busy press on day 1 
                // was not the same as the least busy press on day 2. So they cant agree on a press to use. so we decided to use 
                // the least busy press from day 1 in this case. that being the case, once we have agreed on the least busy press 
                // for the first milestone, we have to go with this one, there is no point in looking at the others, regardless
                // of whether some milestones spill over into the next day or not. so set the equip over here, and stop doing
                // load balancing for this work pool.            
                if (_bValidateLoadBalanced) {
                    _bValidateLoadBalanced = false;
                    _uValidateLoadBalancedEquipIDOverride = uCurEquipID;
                    
                    if (application.isInDeveloper()) {
	                    application.output('************************* rChosenEquipment: ' + rChosenEquipment.equip_id + ' - ' + rChosenEquipment.equip_code);
                    }

					if (_to_sys_organization.org_name == 'Franklin Templeton Investments' && rChosenEquipment) {
						sDebugInfo = 'uCurEquipID: ' + uCurEquipID + ', rMilestone: ' + rMilestone.ms_id + ', dScheduleDateTime: ' + dScheduleDateTime + ', rChosenEquipment: ' 
							+ rChosenEquipment.equip_id + ' - ' + rChosenEquipment.equip_code;
						scopes.avUtils.devLog("SL-17857", sDebugInfo);
					}
                }
            }
            // SL-17096 - if we're validating the currently selected equip is the load balance selected one and we find it isnt - then exit
            else if (_bValidateLoadBalanced) {
                return null;
            }
            else {
                rChosenEquipment = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uMaxFreeEquipID]);
                bEquipmentChanged = true;
                
                // SL-14039 - if pref on - have to recalc ms time needed for new equip
                if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RecalcMilestoneTimeNeeded)) {
                    setTimeNeededForNewEquip(rMilestone, rChosenEquipment);
                }
            }

            if (rChosenEquipment) {
                if (sDirection == 'F') {
                    oEquipmentWindow = scopes.avScheduling.getEarliestAdequateEquipmentWindow(rMilestone, rChosenEquipment, dScheduleDateTime);
                    bFoundWindow = oEquipmentWindow && oEquipmentWindow.startDate;
                }
                else {
                    oEquipmentWindow = scopes.avScheduling.getLatestAdequateEquipmentWindow(rMilestone, rChosenEquipment, dScheduleDateTime);
                    bFoundWindow = oEquipmentWindow && oEquipmentWindow.endDate;
                }
                
                if (bEquipmentChanged && bFoundWindow) {
                    changeMilestoneEquipment(rMilestone, rChosenEquipment, uCurEquipID);
                }
            }
        }
        // none of pool machines have capacity today - try tomorrow, or yesterday if backwards scheduling
        else {
            if (sDirection == 'F') {
                dScheduleDateTime = scopes.avDate.addDaysNoDSTChange(dScheduleDateTime, 1, true);
            }
            else {
                dScheduleDateTime = scopes.avDate.addDaysNoDSTChange(dScheduleDateTime, -1, false, true);
            }

            return loadBalanceUsingWorkPool(rWorkPool, rMilestone, uCurEquipID, sDirection, dScheduleDateTime);
        }
    }
    // no wp machine or only 1 - use current equip
    else {
        rChosenEquipment = scopes.avDB.getRec('eq_equipment', ['equip_id'], [uCurEquipID]);

        if (rChosenEquipment) {
            if (sDirection == 'F') {
                oEquipmentWindow = scopes.avScheduling.getEarliestAdequateEquipmentWindow(rMilestone, rChosenEquipment, dScheduleDateTime);
            }
            else {
                oEquipmentWindow = scopes.avScheduling.getLatestAdequateEquipmentWindow(rMilestone, rChosenEquipment, dScheduleDateTime);
            }
        }
    }

    return oEquipmentWindow;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"E1408C98-539D-466F-9F3B-781413DC9697"}
 */
function isEquipActive(rEquip) {
	var bEquipActive = false;
	
	if (rEquip 
			&& rEquip.equip_active
			&& utils.hasRecords(rEquip.eq_equipment_to_sys_department)
			&& rEquip.eq_equipment_to_sys_department.dept_active
			&& utils.hasRecords(rEquip.eq_equipment_to_sys_operation_category)
			&& rEquip.eq_equipment_to_sys_operation_category.opcat_active) {
				
		bEquipActive = true;
	}
	
	return bEquipActive;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rNewEquip
 * @param {UUID} uOldEquipID
 *
 * @properties={typeid:24,uuid:"635D90BB-6CD2-4080-99F6-51DA1D796E39"}
 */
function changeMilestoneEquipment(rMilestone, rNewEquip, uOldEquipID) {
    var frmSchedBoard = forms['sch_agenda_dtl'];
    var uScheduleEditID = bInScheduleBoard ? frmSchedBoard['_uScheduleEditID'] : null;
    
    rMilestone.dept_id = rNewEquip.dept_id;
    rMilestone.opcat_id = rNewEquip.opcat_id;
    rMilestone.ms_opcat_modified = 1;
    rMilestone.ms_opcat_modified_by = "changeMilestoneEquipment";
    
    var rNewTask = scopes.avScheduling.getEquipmentTask(rNewEquip);
    
    // sl-16112 - have to update ms group recs too - or shopfloor shows ops from old resource
    if (rNewTask && utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone_group)) {
        for (var i = 1; i <= rMilestone.sch_milestone_to_sch_milestone_group.getSize(); i++) {
            var rMSGroup = rMilestone.sch_milestone_to_sch_milestone_group.getRecord(i);

            if (utils.hasRecords(rMSGroup.sch_milestone_group_to_sa_task_cost_link)) {
                var rOldCostLink = rMSGroup.sch_milestone_group_to_sa_task_cost_link.getRecord(1);
                /**@type {JSRecord<db:/avanti/sa_task_cost_link>} */
                var rNewCostLink = frmSchedBoard['getTaskCostLinkForTaskOperation'](rNewTask, rOldCostLink.taskoper_id, rMilestone.div_id, rMilestone.plant_id);

                if (rNewCostLink) {
                    // have to backup the old cc and costlink - in case they cancel schedule board
                    if (bInScheduleBoard && rMSGroup.sch_edit_id != uScheduleEditID) {
                        rMSGroup.sch_edit_id = uScheduleEditID;
                        rMSGroup.taskcostlink_id_bak = rMSGroup.taskcostlink_id;
                        rMSGroup.cc_id_bak = rMSGroup.cc_id;
                    }

                    rMSGroup.taskcostlink_id = rNewCostLink.taskcostlink_id;
                    rMSGroup.taskcostlink_set_by = "changeMilestoneEquipment";
                    rMSGroup.cc_id = rNewCostLink.cc_id;

                    databaseManager.saveData(rMSGroup);
                }
            }
        }
    }

    rMilestone.ms_oper_name = getNewMilestoneOperName(rMilestone);
    	
	if (bInScheduleBoard && rMilestone.sch_edit_id != uScheduleEditID) {
		rMilestone.sch_edit_id = uScheduleEditID;
		rMilestone.equip_id_bak = uOldEquipID;

		frmSchedBoard['_bResourceChanged'] = true;
	}
	else if (bReservationScheduling) {
		if (utils.hasRecords(rMilestone.sch_milestone_to_sch_schedule)
				&& utils.hasRecords(rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail)
				&& utils.hasRecords(rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted)) {

	        scopes.avVL.set_vl_MilestoneCostCentersBak(rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);
		}
	}
    // if in RTS have to reload op fallback vl, or op doesnt show in changed press ms, when ms doesnt have focus  
    else if (bInReadyToSchedule) {
        scopes.avVL.load_vl_MilestoneCostCentersBak(rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.getRecord(1));
    }

    databaseManager.saveData(rMilestone);
}

/**
 * This gets new ms_oper_name after the milestone was changed to a different resource
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"BACE82AE-**************-FD57AAFEB11A"}
 */
function getNewMilestoneOperName(rMilestone) {
	// initialize to current oper name - if we cant find a new name from msgroup logic below then leave as is, rather than change to something incorrect
	var sNewMilestoneOperName = rMilestone.ms_oper_name;
	
	if (utils.hasRecords(rMilestone.sch_milestone_to_sys_operation_category)) {
		var rOpCat = rMilestone.sch_milestone_to_sys_operation_category.getRecord(1);
	}
	
	if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone_group)) {
		var fsMSG = rMilestone.sch_milestone_to_sch_milestone_group;
		
    	// only 1 msg rec - use its cc
        if (fsMSG.getSize() == 1 && utils.hasRecords(fsMSG.sch_milestone_group_to_sys_cost_centre)) {
        	sNewMilestoneOperName = fsMSG.sch_milestone_group_to_sys_cost_centre.cc_desc;
        }
        // more than 1 msg rec - we are grouping - append Group to opcat desc
        else if (fsMSG.getSize() > 1 && rOpCat) {
        	sNewMilestoneOperName = rOpCat.opcat_desc + ' Group';
        }
	}
	// there should always be msg recs, but if there isnt for whatever reason - we cant use opcat cc, if there is only 1 cc
	else if (rOpCat && utils.hasRecords(rOpCat.sys_operation_category_to_sys_cost_centre) && rOpCat.sys_operation_category_to_sys_cost_centre.getSize() == 1) {
    	sNewMilestoneOperName = rOpCat.sys_operation_category_to_sys_cost_centre.cc_desc;
	}
	
	return sNewMilestoneOperName;
}

/**
 * @public 
 * 
 * @param {UUID|String} uOpCatID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"410F3C1B-1EDB-44DA-B612-A65C11827D88"}
 */
function doesOpCatUseGroup(uOpCatID) {
	var bDoesOpCatUseGroup = false;
	
	if (uOpCatID) {
		var sSQL = "SELECT COUNT(*) \
					FROM sys_cost_centre CC \
					WHERE \
						CC.org_id = ? \
						AND CC.opcat_id = ? \
						AND CC.cc_group_by_category = 1";
		var aArgs = [globals.org_id, uOpCatID.toString()];
		
		bDoesOpCatUseGroup = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}
	
	return bDoesOpCatUseGroup;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/eq_equipment>} rNewEquip
 * 
 * @return {Boolean} - returns false if an error occurs
 *
 * @properties={typeid:24,uuid:"E8C0E356-1F40-4594-B19A-ADF8799DDE3C"}
 */
function setTimeNeededForNewEquip(rMilestone, rNewEquip) {
    try {
    	var bSuccess = true;
        var bDebug = inDebugMode();
        
        if (bDebug) {
            forms['sch_debug_milestone'].clearVars();
            forms['sch_debug_milestone'].uMilestoneID = rMilestone.ms_id;
        }
        
        /**@type {JSRecord<db:/avanti/sa_task>} */
        var rNewTask = getEquipmentTask(rNewEquip);
        
        if (rNewTask) {
            if (bDebug) {
                forms['sch_debug_milestone'].sTaskUsed = rNewTask.task_description;
            }
            
            var iTaskType = rNewTask.tasktype_id;
            var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
            var rLineItem = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
            
            if (bInScheduleBoard && rMilestone.sch_edit_id != forms['sch_agenda_dtl']._uScheduleEditID) {
                rMilestone.ms_time_budget_org = rMilestone.ms_time_budget;
            }
            
            globals.avSales_selectedRevisionHeaderID = rLineItem.ordrevh_id;
            scopes.avSales.oEstOrd_initialize(rSection);
            
            if (scopes.avTask.isPressTaskType(iTaskType)) {
                setTimeNeededForNewPress(rMilestone, rNewTask);
            }
            else {
                setTimeNeededForNewTask(rMilestone, rNewTask);
            }

            if (bDebug) {
                forms['sch_debug_milestone'].sWasTimeRecalced = 'yes';
            }
        }
        else if (bDebug) {
            forms['sch_debug_milestone'].sWasTimeRecalced = 'no';
            forms['sch_debug_milestone'].sWhyNot = 'no task found';
        }
    }
    catch (ex) {
        forms['sch_debug_milestone'].sWasTimeRecalced = 'no';
        forms['sch_debug_milestone'].sWhyNot = 'error';
        forms['sch_debug_milestone'].sError = "err in setTimeNeededForNewEquip: " + ex.message;
        
		if (application.isInDeveloper()) {
			application.output(ex.message);
			application.output(ex.stack);
		}
		
		scopes.avUtils.devLogError("SL-27594", ex, rMilestone.ms_id);
		bSuccess = false;
    }
    
    return bSuccess;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sa_task>} rNewPress
 *
 * @properties={typeid:24,uuid:"1B5B2518-4024-4FF0-9A86-FD27E653FB27"}
 */
function setTimeNeededForNewPress(rMilestone, rNewPress) {
    try {
        var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
        var rLineItem = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
        var rMPressSelected = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected.getRecord(1);
        var nTotTimeNeeded = 0;
        var fsMSGRPs = rMilestone.sch_milestone_to_sch_milestone_group;
        var TT = scopes.avTask.TASKTYPEID;
        var aConventionalGroup = [TT.ConventionalPress, TT.WebPress, TT.FlexoPress, TT.StampingPress, TT.DiePress];
        var aDigitalGroup = [TT.DigitalSheetPress, TT.DigitalRollPress];
        var aWideFormatGroup = [TT.WideFormatPress, TT.GrandFormatPress];
        var bIsOriginalPress = isOriginalPress(rSection, rNewPress);
        var bDebug = inDebugMode();
        
        // press times
        var nSetupPress = 0;
        var nRun = 0;
        var nFirstMR = 0;
        var nRepeatMR = 0;
        var nWorkTurnMR = 0;
        var nExtraPlMR = 0;
        var nPerfScoreMR = 0;
        var nWashup = 0;
        var nHelper = 0;   
        var nTotal = 0;   
        
        globals.avSales_selectedRevisionHeaderID = rLineItem.ordrevh_id;
        globals.avSales_selectedRevisionDetailQtyUUID = rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_qty.ordrevdqty_id;
        bEquipChangeTimeRecalc = true;
        
        scopes.avSales.oEstOrd_initialize(rSection);
        
		// DIGITAL - existing logic
        if (aDigitalGroup.indexOf(rNewPress.tasktype_id) > -1) {
            if (bIsOriginalPress) {
            	nSetupPress = (rMPressSelected.mpress_over_setup ? rMPressSelected.mpress_over_setup : rMPressSelected.mpress_setup_time) * 60;
            	nRun = (rMPressSelected.mpress_over_run ? rMPressSelected.mpress_over_run : rMPressSelected.mpress_run_time) * 60;
            }
            else {
            	nSetupPress = globals['avCalcs_press_getSetupTime_digitalPress'](rNewPress, rSection) * 60;
            	nRun = Math.ceil(scopes.avPress.calcPressRunTime(rSection, rNewPress) * 60);
            }
        }
        // CONVENTIONAL - new method - create hidden press pool rec and call computePresses_new to get press values
        else if (aConventionalGroup.indexOf(rNewPress.tasktype_id) > -1) {
			var rMPress = null;
			
			globals.avSales_selectedRevisionSectionPressTaskID = rNewPress.task_id

			if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$avsales_selectedrevisionsectionpresstaskid)) {
				rMPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$avsales_selectedrevisionsectionpresstaskid.getRecord(1);
			}
			else {
				var rPressPool = null;
				
				if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool$avsales_selectedrevisionsectionpresstaskid)){
					rPressPool = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool$avsales_selectedrevisionsectionpresstaskid.getRecord(1);
				}
				else {
					var rTaskStd = rNewPress.sa_task_to_sa_task_standard.getRecord(1);
					
					rPressPool = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getRecord(rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.newRecord(false, true));
					
					rPressPool.sequence_nr = rSection.sa_order_revision_detail_section_to_sa_order_revds_press_pool.getSize();
					rPressPool.task_id = rNewPress.task_id;
					rPressPool.is_schedule_recalc_record = 1;

					if (rTaskStd.taskstd_turning_bar === 1 && rSection.ordrevds_nr_webs === 1) {
						rNewPress.sa_task_to_sa_task_turning_bar.sort("sequence_nr asc");
						rPressPool.taskturn_id = rNewPress.sa_task_to_sa_task_turning_bar.getRecord(1).taskturn_id;
						rPressPool.spress_turningbar = rTaskStd.taskstd_turning_bar;
					}

					databaseManager.saveData(rPressPool);
				}
				
				if (rPressPool) {
					scopes.avSales.oEstOrd.refreshSections(rSection.ordrevd_id);

					globals["avCalcs_init_getListPriceObject"]();
					forms["sa_order_revision_detail_section_dlg"].computePresses_new(rSection, true, rNewPress.task_id, null, null, null, true);

					if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$avsales_selectedrevisionsectionpresstaskid)) {
						rMPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$avsales_selectedrevisionsectionpresstaskid.getRecord(1);
					}
				}
			}
			
			if (rMPress) {
	        	nSetupPress = (rMPress.mpress_over_setup ? rMPress.mpress_over_presssetup : rMPress.mpress_presssetup_time) * 60;
	            nFirstMR = (rMPress.mpress_over_firstmr ? rMPress.mpress_over_firstmr : rMPress.mpress_firstmr_time) * 60;
	            nRepeatMR = (rMPress.mpress_over_addmr ? rMPress.mpress_over_addmr : rMPress.mpress_addmr_time) * 60;
	            nWorkTurnMR = (rMPress.mpress_over_wtmr ? rMPress.mpress_over_wtmr : rMPress.mpress_wtmr_time) * 60;
	            nExtraPlMR = (rMPress.mpress_over_explatemr ? rMPress.mpress_over_explatemr : rMPress.mpress_explatemr_time) * 60;
	        	nRun = (rMPress.mpress_over_run ? rMPress.mpress_over_run : rMPress.mpress_run_time) * 60;
	            nPerfScoreMR = (rMPress.mpress_over_perfmr ? rMPress.mpress_over_perfmr : rMPress.mpress_perfscoremr_time) * 60;
	            nWashup = (rMPress.mpress_over_wash ? rMPress.mpress_over_wash : rMPress.mpress_wash_time) * 60;
	            nHelper = (rMPress.mpress_over_help ? rMPress.mpress_over_help : rMPress.mpress_help_time) * 60;
	            nTotal = rMPress.mpress_total_time * 60;
			}
		}
        // we only support digital and conventional right now - exit
		else {
			// Reset bEquipChangeTimeRecalc as it done at the end of this function
			bEquipChangeTimeRecalc = false;
			return;
		}

        rMilestone.ms_time_budget_no_ur = 0;
        rMilestone.ms_time_budget = 0;
		
        for (var i = 1; i <= fsMSGRPs.getSize(); i++) {
            var rMSGRP = fsMSGRPs.getRecord(i);
            var rATO = rMSGRP.sch_milestone_group_to_sa_task_cost_link.sa_task_cost_link_to_app_task_operation.getRecord(1);
    		var rCostCenter = null;
    		
    		// if bInScheduleBoard we havent updated the group to the new cc's yet so have to call getNewMSGroupCC to get correct cc 
			if (bInScheduleBoard) {
				rCostCenter = getNewMSGroupCC(rMilestone, rMSGRP, rNewPress);
			}
			// in RTS we update the group before getting here so we can use sa_task_cost_link_to_sys_cost_centre
			else {
	    		rCostCenter = rMSGRP.sch_milestone_group_to_sa_task_cost_link.sa_task_cost_link_to_sys_cost_centre.getRecord(1);
			}
			
			// if we couldnt get a cost center then we cant do the logic below
			if (!rCostCenter) {
				continue;
			}
            
            if (rATO.taskoper_key == 'i18n:avanti.lbl.task_setupPress') {
                rMSGRP.msgrp_time_budget = nSetupPress;
                
                if (bDebug) {
                    forms['sch_debug_milestone'].sSetupTime = Math.ceil(rMSGRP.msgrp_time_budget).toString();
                }
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_firstMR') {
                rMSGRP.msgrp_time_budget = nFirstMR;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_repeatMR') {
                rMSGRP.msgrp_time_budget = nRepeatMR;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_workTurnMR') {
                rMSGRP.msgrp_time_budget = nWorkTurnMR;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_extraPlMR') {
                rMSGRP.msgrp_time_budget = nExtraPlMR;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_run') {
                rMSGRP.msgrp_time_budget = nRun;
                
                if (bDebug) {
                    forms['sch_debug_milestone'].sRunTime = Math.ceil(rMSGRP.msgrp_time_budget).toString();
                }
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_perfScoreMR') {
                rMSGRP.msgrp_time_budget = nPerfScoreMR;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_washup') {
                rMSGRP.msgrp_time_budget = nWashup;
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.helper') {
                rMSGRP.msgrp_time_budget = nHelper;
            }

            rMSGRP.msgrp_time_budget_no_ur = rMSGRP.msgrp_time_budget;
            rMSGRP.msgrp_time_budget = globals["calcTimeNeededWithUR"](rMSGRP.msgrp_time_budget_no_ur, rCostCenter.cc_utilization_rate);
		
            rMilestone.ms_time_budget_no_ur += rMSGRP.msgrp_time_budget_no_ur;
            rMilestone.ms_time_budget += rMSGRP.msgrp_time_budget;

            rMSGRP.msgrp_time_budget_no_ur = globals["avUtilities_roundNumber"](rMSGRP.msgrp_time_budget_no_ur, 0, true);
            rMSGRP.msgrp_time_budget = globals["avUtilities_roundNumber"](rMSGRP.msgrp_time_budget, 0, true);
        }

        rMilestone.ms_utilization_rate = globals["calcURFromTimeNeededs"](rMilestone.ms_time_budget_no_ur, rMilestone.ms_time_budget);

        rMilestone.ms_time_budget_no_ur_no_round = rMilestone.ms_time_budget_no_ur;
        rMilestone.ms_time_budget_no_round = rMilestone.ms_time_budget;

        rMilestone.ms_time_budget_no_ur = globals["avUtilities_roundNumber"](rMilestone.ms_time_budget_no_ur, 0, true);
        rMilestone.ms_time_budget = globals["avUtilities_roundNumber"](rMilestone.ms_time_budget, 0, true);
        
		if (application.isInDeveloper()) {
			application.output(rNewPress.task_description + ": " + rMilestone.ms_time_budget);
		}
        
		if (rMilestone.ms_date_scheduled) {
			rMilestone.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
		}
        
        databaseManager.saveData(rMilestone);
        databaseManager.saveData(fsMSGRPs);
        bEquipChangeTimeRecalc = false;
    }
    catch (ex) {      
        bEquipChangeTimeRecalc = false;
        throw ex;
    }
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sch_milestone_group>} rMSGroup
 * @param {JSRecord<db:/avanti/sa_task>} rNewTask
 * 
 * @return {JSRecord<db:/avanti/sys_cost_centre>}
 *
 * @properties={typeid:24,uuid:"C582CEE3-AF25-4315-9070-CE08025A47F8"}
 */
function getNewMSGroupCC(rMilestone, rMSGroup, rNewTask) {
	/**@type {JSRecord<db:/avanti/sys_cost_centre>} */
	var rCC = null;

	if (utils.hasRecords(rMSGroup.sch_milestone_group_to_sa_task_cost_link)) {
		var rOldCostLink = rMSGroup.sch_milestone_group_to_sa_task_cost_link.getRecord(1);
		/**@type {JSRecord<db:/avanti/sa_task_cost_link>} */
		var rNewCostLink = forms["_sch_base"].getTaskCostLinkForTaskOperation(rNewTask, rOldCostLink.taskoper_id, rMilestone.div_id, rMilestone.plant_id);

		if (rNewCostLink && utils.hasRecords(rNewCostLink.sa_task_cost_link_to_sys_cost_centre)) {
			return rNewCostLink.sa_task_cost_link_to_sys_cost_centre.getRecord(1);
		}
	}

	return rCC;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sa_task>} rNewTask
 *
 * @properties={typeid:24,uuid:"28A15333-1A94-465E-B730-EF0D8470DD3A"}
 */
function setTimeNeededForNewTask(rMilestone, rNewTask) {
    try {
        var nTotTimeNeeded = 0;
        var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
        var rMPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected.getRecord(1);
        var fsMSGRPs = rMilestone.sch_milestone_to_sch_milestone_group;
        /**@type {scopes.avSales.oEstOrdTask} */
        var oEstOrdTask = { };
        var rLineItem = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
        var bDebug = inDebugMode();

        globals.avSales_selectedRevisionHeaderID = rLineItem.ordrevh_id;
        globals.avSales_selectedRevisionDetailQtyUUID = rLineItem.sa_order_revision_detail_to_sa_order_revision_detail_qty.ordrevdqty_id;
        bEquipChangeTimeRecalc = true;
        
        scopes.avSales.oEstOrd_initialize(rSection);

        oEstOrdTask.rEstStdTask = rNewTask;
        oEstOrdTask.rEstStdTaskStd = rNewTask.sa_task_to_sa_task_standard.getRecord(1);
        oEstOrdTask.iTaskTypeId = rNewTask.tasktype_id;
        oEstOrdTask.fsMachine = rNewTask.sa_task_to_sa_task_machine;
        oEstOrdTask.iSelectedQtyIdx = 0;
        oEstOrdTask.iBaseQtyIdx = 0;
        
        for (var i = 1; i <= fsMSGRPs.getSize(); i++) {
            var rMSGRP = fsMSGRPs.getRecord(i);
            var rATO = rMSGRP.sch_milestone_group_to_sa_task_cost_link.sa_task_cost_link_to_app_task_operation.getRecord(1);
            var rOrdTask = rMSGRP.sch_milestone_group_to_sa_order_revds_task.getRecord(1);
            var rTaskQty = rOrdTask.sa_order_revds_task_to_sa_order_revds_task_qty.getRecord(1);
            var bIsOriginalTask = isOriginalTask(rMSGRP, rNewTask);
            
            oEstOrdTask.rTask = rOrdTask;
            oEstOrdTask.aTaskQtys = [rTaskQty];

            if (rATO.taskoper_key == 'i18n:avanti.lbl.task_setup') {
                if (bIsOriginalTask) {
                    rMSGRP.msgrp_time_budget = rTaskQty.ordrevdstqty_time_setup_over ? rTaskQty.ordrevdstqty_time_setup_over : rTaskQty.ordrevdstqty_time_setup;
                }
                else {
                    rMSGRP.msgrp_time_budget = globals['avCalcs_task_getTimeSetup'](rTaskQty, rMPress, oEstOrdTask);
                }
                
                if (bDebug) {
                    forms['sch_debug_milestone'].sSetupTime = Math.ceil(rMSGRP.msgrp_time_budget).toString();
                }
            }
            else if (rATO.taskoper_key == 'i18n:avanti.lbl.task_run') {
                if (bIsOriginalTask) {
                    rMSGRP.msgrp_time_budget = rTaskQty.ordrevdstqty_time_run_over ? rTaskQty.ordrevdstqty_time_run_over : rTaskQty.ordrevdstqty_time_run;
                }
                else {
                    rMSGRP.msgrp_time_budget = globals['avCalcs_task_getTimeRun'](rTaskQty, rMPress, oEstOrdTask);
                }
                
                if (bDebug) {
                    forms['sch_debug_milestone'].sRunTime = Math.ceil(rMSGRP.msgrp_time_budget).toString();
                }
            }

            // total unrounded msgrp time needed, then round it. this is in keeping with hows its done when milestones are created
            nTotTimeNeeded += rMSGRP.msgrp_time_budget;
            rMSGRP.msgrp_time_budget = scopes.avMath.roundNumber(rMSGRP.msgrp_time_budget, 0, true);
        }
        
        // round ms time needed, this is in keeping with hows its done when milestones are created. 
        // 3rd param ensures its will always round up to 1 if betwen 0 and 1
        rMilestone.ms_time_budget = scopes.avMath.roundNumber(nTotTimeNeeded, 0, true);
        
		if (rMilestone.ms_date_scheduled) {
			rMilestone.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_time_budget);
		}
        
        databaseManager.saveData(rMilestone);
        databaseManager.saveData(fsMSGRPs);
        bEquipChangeTimeRecalc = false;
    }
    catch (ex) {      
        bEquipChangeTimeRecalc = false;
        throw ex;
    }
}


/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * 
 * @return {JSRecord<db:/avanti/sa_task>}
 *
 * @properties={typeid:24,uuid:"B2801775-FC4F-4D48-8E95-4810E3D64C39"}
 */
function getEquipmentTask(rEquip) {
    // if they have set the 'Default Estimating Standard to use for recalculating milestone time needed' use that
    if (utils.hasRecords(rEquip.eq_equipment_to_sa_task)) {
        return rEquip.eq_equipment_to_sa_task.getRecord(1)
    }
    // otherwise use old logic of finding task with equip opcat
    else {
    	// sl-27262 - removed 't.task_active = 1' condition - josh said they should be able to select inactive tasks
        var sSQL = "SELECT t.task_id \
                    FROM eq_equipment eq \
                    INNER JOIN sys_operation_category oc ON eq.opcat_id = oc.opcat_id \
                    INNER JOIN sys_cost_centre cc ON cc.opcat_id = oc.opcat_id \
                    INNER JOIN sa_task_cost_link cl ON cl.cc_id = cc.cc_id \
                    INNER JOIN app_task_operation ato ON ato.taskoper_id = cl.taskoper_id \
                    INNER JOIN sa_task t ON cl.task_id = t.task_id \
                    WHERE \
                        t.org_id = ? \
                        AND eq.equip_id = ? \
                        AND ato.taskoper_key = 'i18n:avanti.lbl.task_run'";
        var aArgs = [globals.org_id, rEquip.equip_id.toString()];
        
        /**@type {JSRecord<db:/avanti/sa_task>} */
        var rTask = scopes.avDB.getRecFromSQL(sSQL, 'sa_task', aArgs);
        
        return rTask;
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSection
 * @param {JSRecord<db:/avanti/sa_task>} rPress
 *
 * @return
 * @properties={typeid:24,uuid:"216A991F-1F11-4415-A1B3-A694B87A821D"}
 */
function isOriginalPress(rSection, rPress) {
    if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected)) {
        var rOrdPress = rSection.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected.getRecord(1);

        if (rOrdPress.task_id == rPress.task_id) {
            return true;
        }
    }
    
    return false;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone_group>} rMilestoneGroup
 * @param {JSRecord<db:/avanti/sa_task>} rTask
 *
 * @return
 * @properties={typeid:24,uuid:"39641D77-06C7-4620-9330-26F3246E73D8"}
 */
function isOriginalTask(rMilestoneGroup, rTask) {
    if (utils.hasRecords(rMilestoneGroup.sch_milestone_group_to_sa_order_revds_task)) {
        var rOrdTask = rMilestoneGroup.sch_milestone_group_to_sa_order_revds_task.getRecord(1);

        if (rOrdTask.task_id == rTask.task_id) {
            return true;
        }
    }
    
    return false;
}

/**
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"3AEB842A-931E-4C7C-B5FB-E481C0A27953"}
 */
function inDebugMode() {
    var aAvantiDebugUsers = ['gbarker', 'harry'];
    
    if (application.isInDeveloper()) {
        return true;
    }
    else if (scopes.avUtils.isThisAvanti() && aAvantiDebugUsers.indexOf(_to_sec_user$user_id.user_name) > -1) {
        return true;
    }
    else {
        return false;
    }
    
}

/**
 * @public 
 * 
 * @properties={typeid:24,uuid:"7F86C0AD-6B35-4F18-B4F5-82D5C37E11C8"}
 */
function showMoveFailMsg() {
    if (sMoveFailReason) {
        // dont display a msg for this case
        if (sMoveFailReason != MOVE_FAIL_REASON.StartOutsideOfShift) {
            var sReason = scopes.avText.getDlgMsg('moveFail_' + sMoveFailReason);

            if (sReason) {
                scopes.avText.showError('MSCantBeMovedBecause', null, [sReason]);
            }
        }

        sMoveFailReason = null;
    }
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"030A9C66-D5AC-4122-A4FE-FE8828215107"}
 */
function isCapRecBroken(rMilestone) {
    var bIsCapRecBroken = false;
    var dTodayStart = scopes.avDate.copyDate(application.getTimeStamp(), true);
    
    if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
        var rEqSched = rMilestone.sch_milestone_to_sch_equip_schedule.getRecord(1);

        // i saw a case where there were recs in db, but relation didnt have them
        if (rMilestone.sch_milestone_to_sch_emp_capacity) {
            rMilestone.sch_milestone_to_sch_emp_capacity.loadAllRecords();
        }
        
        if (utils.hasRecords(rMilestone.sch_milestone_to_sch_emp_capacity)) {
            rMilestone.sch_milestone_to_sch_emp_capacity.sort('start_date');
            
            for (var i = 1; i <= rMilestone.sch_milestone_to_sch_emp_capacity.getSize(); i++) {
                var rCapRec = rMilestone.sch_milestone_to_sch_emp_capacity.getRecord(i);
                
                // if caprec rec doesnt fall within start and stop for equip sched then its broken
                if (rCapRec.start_date < rEqSched.equipsch_start || rCapRec.end_date > rEqSched.equipsch_end) {
                    bIsCapRecBroken = true;
                }
            }
        }
        // if there is no cap rec for ms, then its broken - but not if rEqSched is in the past - we delete caprecs in past
        else if (rEqSched.equipsch_end >= dTodayStart) {
            bIsCapRecBroken = true;
        }
    }
    
    return bIsCapRecBroken;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bSkipCompletedMS]
 * 
 * @return {JSRecord<db:/avanti/sch_milestone>}
 *
 * @properties={typeid:24,uuid:"6BD60A1B-1080-4714-A650-B1E1C9AA34D2"}
 */
function getPredecessor(rMilestone, bSkipCompletedMS) {
	/***@type {JSRecord<db:/avanti/sch_milestone>} ***/
	var rPredMilestone = null;
	
	// sl-16701 - it had been using pred_ms_id to getPredecessor, changed it to use dependencies
	if (rMilestone.sequence_nr > 1 && rMilestone.sch_milestone_to_sch_milestone_dep) {
		/***@type {Array<UUID>} ***/
		var auPredMilestoneIDs = [];
		var bIsFirstMSOnAssembly = rMilestone.sch_milestone_to_sa_order_revision_detail_section.ordrevds_is_assembly && scopes.avSection.isThisFirstMilestoneInSection(rMilestone);
		var dAssemblyHasMultiplePredecessors = false;

		for (var i = 1; i <= rMilestone.sch_milestone_to_sch_milestone_dep.getSize(); i++) {
			var rMSDep = rMilestone.sch_milestone_to_sch_milestone_dep.getRecord(i);

			if (utils.hasRecords(rMSDep.sch_milestone_dep_to_sch_milestone$dep)) {
				rPredMilestone = rMSDep.sch_milestone_dep_to_sch_milestone$dep.getRecord(1);

				if (bIsFirstMSOnAssembly) {
					if (auPredMilestoneIDs.indexOf(rPredMilestone.ms_id) == -1) {
						auPredMilestoneIDs.push(rPredMilestone.ms_id);
					}
					
					if (auPredMilestoneIDs.length > 1) {
						dAssemblyHasMultiplePredecessors = true;
						break;
					}
				}
				else {
					break;
				}
			}
		}
		
		if (dAssemblyHasMultiplePredecessors) {
			rPredMilestone = getLatestAssemblyPredecessor(rMilestone);
		}
		else if (rPredMilestone) {
			// if the Predecessor is non-sched then get the Predecessor of that Predecessor and so on
			if (!isMilestoneSchedulable(rPredMilestone, null, !bSkipCompletedMS)) {
				rPredMilestone = getPredecessor(rPredMilestone, bSkipCompletedMS);
			}
		}
	}
	
	return rPredMilestone;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [hideMsg]
 * 
 * @return {Boolean} - returns false if blank dependencies encountered, otherwise returns true
 *
 * @properties={typeid:24,uuid:"DB9696B4-D570-4464-BFA3-70252FE65967"}
 */
function setMilestoneLags (rMilestone, hideMsg) {
	if (rMilestone) {
		var nMaxStartLagMins = 0;
		var nMaxEndLagMins = 0;
		var rSuccessor = getSuccessor(rMilestone);
	
		if (rSuccessor && rSuccessor.sch_milestone_to_sch_milestone_dep) {
			for (var j = 1; j <= rSuccessor.sch_milestone_to_sch_milestone_dep.getSize(); j++) {
				var rSuccessorDep = rSuccessor.sch_milestone_to_sch_milestone_dep.getRecord(j);
				
				// if we dont have any recs in relation try loadAllRecords
				if (rSuccessorDep.ms_id_dep && !utils.hasRecords(rSuccessorDep.sch_milestone_dep_to_sch_milestone$dep)) {
					rSuccessorDep.sch_milestone_dep_to_sch_milestone$dep.loadAllRecords();
				}
				
				if (utils.hasRecords(rSuccessorDep.sch_milestone_dep_to_sch_milestone$dep)) {
					var rSuccessorDepMS = rSuccessorDep.sch_milestone_dep_to_sch_milestone$dep.getRecord(1);
					
					// if the rSuccessor is the first MS of an assembly then it could have multiple predecessor milestones. only process dependencies if they belong to the current rMilestone
					if (rSuccessorDepMS && rSuccessorDepMS.ms_id == rMilestone.ms_id) {
				        var nLagMins = globals["getLagTime"](rSuccessorDep);
		
			    		if (rSuccessorDep.msdep_lag_type == "SS" && nLagMins > nMaxStartLagMins) {
			    			nMaxStartLagMins = nLagMins;
			    		}
			    		else if (rSuccessorDep.msdep_lag_type == "FS" && nLagMins > nMaxEndLagMins) {
			    			nMaxEndLagMins = nLagMins;
			    		}
					}
				}
				// sl-22314 - we have a rSuccessorDep.ms_id_dep but no matching milestone then the ms got deleted somehow, dont know how. alert user to problem and log info 
				else if (rSuccessorDep.ms_id_dep) {
					if (!hideMsg) {
						var sJobNum = rMilestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_number;
						scopes.avText.showWarning("milestoneHasBlankDependencies", null, [rSuccessor.ms_oper_name], scopes.avText.getDlgMsg("problemSchedulingJob") + sJobNum);
					}
					
					scopes.avUtils.devLog("SL-22314", "setMilestoneLags: Blank Dependencies for ms_id: " + rSuccessorDep.ms_id);
					return false;
				}
			}
		}
		
		rMilestone.ms_date_scheduled_lag = nMaxStartLagMins;
		rMilestone.ms_date_due_lag = nMaxEndLagMins;
		rMilestone.ms_lag_set = 1;
	}
	
	return true;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sch_milestone>} rSuccessor
 * @param {String} sDirection - F or B
 * 
 * @properties={typeid:24,uuid:"5B85FCB1-FBC4-4866-AD64-3B0076E3A87B"}
 */
function scheduleShiftLag(rMilestone, rSuccessor, sDirection) {
	rMilestone.ms_lag_date = null; 
		
	if (!sDirection) {
		sDirection = "F";
	}
	
	var bForwardsValid = sDirection == "F" && rMilestone != null && rMilestone.equip_id != null && rMilestone.ms_date_scheduled != null && rMilestone.ms_date_due != null;
	var bBackwardsValid = sDirection == "B" && rMilestone != null && rMilestone.equip_id != null && rSuccessor != null && rSuccessor.ms_date_scheduled != null && rSuccessor.ms_date_due != null;
	
	if (bForwardsValid || bBackwardsValid) {
		var sSQL = "SELECT CC.cc_id \
					FROM sch_milestone_group MSG \
					INNER JOIN sys_cost_centre CC ON MSG.cc_id = CC.cc_id \
					WHERE \
						CC.org_id = ? \
						AND MSG.ms_id = ? \
						AND CC.cc_lag_uses_shift_time = 1 \
						AND CC.cc_dflt_lag > 0 \
						AND CC.cc_dflt_lag_units IS NOT NULL \
						AND CC.cc_dflt_lag_type IS NOT NULL";
		var aArgs = [globals.org_id, rMilestone.ms_id.toString()];
		
	    /**@type {JSFoundSet<db:/avanti/sys_cost_centre>} */
		var fsCC = scopes.avDB.getFSFromSQL(sSQL, "sys_cost_centre", aArgs);
		
		if (utils.hasRecords(fsCC)) {
			fsCC.sort("cc_dflt_lag DESC");
			
			var rMilestoneEquip = null;
			var rSuccessorEquip = null;
			
			if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment)) {
				rMilestoneEquip = rMilestone.sch_milestone_to_eq_equipment.getRecord(1);
			}
			else if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
				rMilestoneEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
			}

			if (utils.hasRecords(rSuccessor.sch_milestone_to_eq_equipment)) {
				rSuccessorEquip = rSuccessor.sch_milestone_to_eq_equipment.getRecord(1);
			}
			else if (utils.hasRecords(rSuccessor.sch_milestone_to_eq_equipment$dept_opcat)) {
				rSuccessorEquip = rSuccessor.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
			}

			if (rMilestoneEquip && rSuccessorEquip) {
				var rLagEquipment = getLagEquipment(rMilestoneEquip);
				var rLagEquipmentSuccessor = getLagEquipment(rSuccessorEquip);
			}
			
			if (rLagEquipment && rLagEquipmentSuccessor) {
				var nLagMins = fsCC.cc_dflt_lag_units == "h" ? fsCC.cc_dflt_lag * 60 : fsCC.cc_dflt_lag;

			    /**@type {JSRecord<db:/avanti/sch_milestone>} */
				var rLagMilestone = scopes.avDB.duplicateRecord(rMilestone);
				
				rLagMilestone.ms_is_lag = 1;
				rLagMilestone.ms_oper_name = "LAG";
				rLagMilestone.sch_id = null;
				rLagMilestone.ordrevds_id = null;
				rLagMilestone.ordrevdstask_id = null;
				rLagMilestone.ms_time_budget = nLagMins;
				rLagMilestone.ms_pred_ms_id = null;
				rLagMilestone.ms_pred_ms_id_2 = null;
				rLagMilestone.ms_successor_ms_id = null;
				rLagMilestone.equip_id = rLagEquipment.equip_id;
				rLagMilestone.equip2_id = rLagEquipmentSuccessor.equip_id;
				
				// with forwards scheduling we schedule the lag after we schedule the milestone the lag is attached to
				if (sDirection == "F") {
					if (fsCC.cc_dflt_lag_type == "FS") {
						rLagMilestone.ms_date_scheduled = rMilestone.ms_date_due;
					}
					else if (fsCC.cc_dflt_lag_type == "SS") {
						rLagMilestone.ms_date_scheduled = rMilestone.ms_date_scheduled;
					}
					
					rLagMilestone.ms_date_due = scopes.avDate.addMinutesNoDSTChange(rLagMilestone.ms_date_scheduled, nLagMins);
				}
				// with backwards scheduling we schedule the lag before we schedule the milestone the lag is attached to
				else if (sDirection == "B") {
					if (fsCC.cc_dflt_lag_type == "FS") {
						rLagMilestone.ms_date_due = rSuccessor.ms_date_scheduled;
					}
					else if (fsCC.cc_dflt_lag_type == "SS") {
						rLagMilestone.ms_date_due = rSuccessor.ms_date_scheduled;
					}
					
					rLagMilestone.ms_date_scheduled = scopes.avDate.addMinutesNoDSTChange(rLagMilestone.ms_date_due, -nLagMins);
				}
				
				databaseManager.saveData(rLagMilestone);
				
				if (getNextFreeSpot(rLagMilestone, sDirection, null, null, null, null, null, rLagEquipment.equip_id)) {
					if (sDirection == "F") {
						rMilestone.ms_lag_date = rLagMilestone.ms_date_due;
					}
					else if (sDirection == "B") {
						rMilestone.ms_lag_date = rLagMilestone.ms_date_scheduled;
					}
				}
				
				databaseManager.saveData(rMilestone);
				
				scopes.avDB.RunSQL("DELETE FROM sch_milestone WHERE org_id = ? AND ms_id = ?", null, [globals.org_id, rLagMilestone.ms_id.toString()]);
			}
		}
	}
}

/**
 * @private  
 * 
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 * 
 * @return {JSRecord<db:/avanti/eq_equipment>}
 *
 * @properties={typeid:24,uuid:"2D663757-130B-485D-B725-061C33D44082"}
 */
function getLagEquipment(rEquip) {
	/**@type {JSRecord<db:/avanti/eq_equipment>} */
	var rLagEquip = null;
	/**@type {JSRecord<db:/avanti/sch_equipment_shift>} */
	var rLagEquipShift = null;
	/**@type {JSRecord<db:/avanti/sch_equip_excep>} */
	var rLagEquipEx = null;
	var rEquipShift = null;
	var rEquipEx = null;
	var i = 0;
	
	if (rEquip) {
		rLagEquip = scopes.avDB.getRecFromSQL("SELECT equip_id FROM eq_equipment WHERE org_id = ? AND lag_parent = ?", "eq_equipment", [globals.org_id, rEquip.equip_id.toString()]);
		
		if (!rLagEquip) {
			rLagEquip = scopes.avDB.duplicateRecord(rEquip);
			rLagEquip.equip_code = "LAG";
			rLagEquip.lag_parent = rEquip.equip_id;
			databaseManager.saveData(rLagEquip);
			
			if (rEquip.eq_equipment_to_sch_equipment_shift) {
				for (i = 1; i <= rEquip.eq_equipment_to_sch_equipment_shift.getSize(); i++) {
					rEquipShift = rEquip.eq_equipment_to_sch_equipment_shift.getRecord(i);					
					rLagEquipShift = scopes.avDB.duplicateRecord(rEquipShift);
					rLagEquipShift.equip_id = rLagEquip.equip_id;
					databaseManager.saveData(rLagEquipShift);
				}
			}
			
			if (rEquip.eq_equipment_to_sch_equip_excep) {
				for (i = 1; i <= rEquip.eq_equipment_to_sch_equip_excep.getSize(); i++) {
					rEquipEx = rEquip.eq_equipment_to_sch_equip_excep.getRecord(i);					
					rLagEquipEx = scopes.avDB.duplicateRecord(rEquipEx);
					rLagEquipEx.equip_id = rLagEquip.equip_id;
					databaseManager.saveData(rLagEquipEx);
				}
			}
		}
	}
	
	return rLagEquip;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bScheduble]
 * @param {Boolean} [bUnDone]
 * 
 * @return {JSRecord<db:/avanti/sch_milestone>}
 *
 * @properties={typeid:24,uuid:"554D5E0B-7B98-4C79-B356-076AB63FFFC7"}
 */
function getSuccessor(rMilestone, bScheduble, bUnDone) {
	var rSuccessor = null;
	
	// uses ms_pred_ms_id - used by most milestones
	if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$successors)) {
		rSuccessor = rMilestone.sch_milestone_to_sch_milestone$successors.getRecord(1);
	}
	// uses ms_successor_ms_id - used by assmebly ms with multiple predecessors
	else if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$successor_id)) {
		rSuccessor = rMilestone.sch_milestone_to_sch_milestone$successor_id.getRecord(1);
	}
	
	if (rSuccessor) {
		if (bScheduble && !isMilestoneSchedulable(rSuccessor)) {
			rSuccessor = getSuccessor(rSuccessor, bScheduble, bUnDone);
		}
		else if (bUnDone && getSuccessor.ms_flg_completed) {
			rSuccessor = getSuccessor(rSuccessor, bScheduble, bUnDone);
		}
	}
	
	return rSuccessor;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {UUID}
 *
 * @properties={typeid:24,uuid:"9554535C-3EF3-4063-BB92-1ECC415FF0AA"}
 */
function getSuccessorID(rMilestone) {
	/** @type {UUID} */
	var uSuccessorID = null;
	
	// uses ms_pred_ms_id - used by most milestones
	if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$successors)) {
		uSuccessorID = rMilestone.sch_milestone_to_sch_milestone$successors.ms_id;
	}
	// uses ms_successor_ms_id - used by assmebly ms with multiple predecessors
	else if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$successor_id)) {
		uSuccessorID = rMilestone.sch_milestone_to_sch_milestone$successor_id.ms_id;
	}
	
	return uSuccessorID;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {JSRecord<db:/avanti/sch_milestone>}
 *
 * @properties={typeid:24,uuid:"8A1E67A5-1A46-4723-9954-4FAC83DAE2A0"}
 */
function getLatestAssemblyPredecessor(rMilestone) {
	/***@type {JSRecord<db:/avanti/sch_milestone>} ***/
	var rLatestPredecessor = null;
	/***@type {Array<JSRecord<db:/avanti/sch_milestone>>} ***/
	var arPredecessorMilestones = getAssemblySchedulablePredecessors(rMilestone);
	
	if (arPredecessorMilestones.length > 0) {
		/**@type {Date} */
		var dLatestPredecessorTime = null;
		
		for (var i = 0; i < arPredecessorMilestones.length; i++) {
			var rPredecessorMS = arPredecessorMilestones[i];
			var dMaxDateWithLag = getMilestoneMaxDateWithLag(rPredecessorMS);
			
			if (dMaxDateWithLag > dLatestPredecessorTime) {
				dLatestPredecessorTime = dMaxDateWithLag;
				rLatestPredecessor = rPredecessorMS;
			}
		}
	}
	
	return rLatestPredecessor;
}

/**
 * returns an array of all print section predecessor milestones for an assembly section MS
 * 
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bSkipCompletedMS]
 * 
 * @return {Array<JSRecord<db:/avanti/sch_milestone>>}
 *
 * @properties={typeid:24,uuid:"717601A6-EDF0-4A04-9BE9-0B6E0A43B371"}
 */
function getAssemblySchedulablePredecessors(rMilestone, bSkipCompletedMS) {
	/***@type {Array<JSRecord<db:/avanti/sch_milestone>>} ***/
	var arSchedulablePredecessors = [];

	if (rMilestone && rMilestone.sch_milestone_to_sa_order_revision_detail_section.ordrevds_is_assembly && rMilestone.sch_milestone_to_sch_milestone_dep) {
		/***@type {Array<UUID>} ***/
		var auPredecessorMilestoneIDs = [];
		
		for (var i = 1; i <= rMilestone.sch_milestone_to_sch_milestone_dep.getSize(); i++) {
			var rDep = rMilestone.sch_milestone_to_sch_milestone_dep.getRecord(i);			
			var rPredecessorMS = utils.hasRecords(rDep.sch_milestone_dep_to_sch_milestone$dep) 
				? rDep.sch_milestone_dep_to_sch_milestone$dep.getRecord(1)
				: null;			

			if (rPredecessorMS && auPredecessorMilestoneIDs.indexOf(rPredecessorMS.ms_id) == -1) {
				auPredecessorMilestoneIDs.push(rPredecessorMS.ms_id);

				// if this predecessor isnt schedulable then get the next one that is
				if (!isMilestoneSchedulable(rPredecessorMS, null, !bSkipCompletedMS)) {
					rPredecessorMS = getPredecessor(rPredecessorMS, bSkipCompletedMS);
				}
				
				if (rPredecessorMS) {
					arSchedulablePredecessors.push(rPredecessorMS);
				}
}
		}
	}
	
	return arSchedulablePredecessors;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"7980D597-82D3-439F-98BD-A1CC00B018B9"}
 */
function isFirstMilestoneInAssembly(rMilestone) { 
	return rMilestone.sch_milestone_to_sa_order_revision_detail_section.ordrevds_is_assembly && scopes.avSection.isThisFirstMilestoneInSection(rMilestone);
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bSkipCompletedMS]
 * 
 * @return {Array<JSRecord<db:/avanti/sch_milestone>>}
 *
 * @properties={typeid:24,uuid:"AF9F3BF7-8B45-40F1-9EAA-83F04E429DEC"}
 */
function getSchedulablePredecessors(rMilestone, bSkipCompletedMS) {
	var aPredecessors = []
	
    if (rMilestone) {
		if (isFirstMilestoneInAssembly(rMilestone)) {
			aPredecessors = getAssemblySchedulablePredecessors(rMilestone, bSkipCompletedMS)
		}
		else {
			var rPredecessor = getPredecessor(rMilestone, bSkipCompletedMS)

			if (rPredecessor) {
				aPredecessors.push(rPredecessor);
			}
		}
    }

    return aPredecessors;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"F03E03B0-A150-46BC-A81C-448545B3AC75"}
 */
function getMilestoneStartDateLag(rMilestone) {
	// in case this is an old milestone, added to schedule before we started using ms lag cols
	if (!rMilestone.ms_lag_set) {
		setMilestoneLags(rMilestone)
	}
	
	return rMilestone.ms_date_scheduled_lag;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"9D972E77-A33A-4699-AB88-0CDA37059D08"}
 */
function getMilestoneEndDateLag(rMilestone) {
	// in case this is an old milestone, added to schedule before we started using ms lag cols
	if (!rMilestone.ms_lag_set) {
		setMilestoneLags(rMilestone)
	}
	
	return rMilestone.ms_date_due_lag;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"15BDA52A-40DF-419D-8B51-82062AFAFBF4"}
 */
function getMilestoneStartDateWithLag(rMilestone) {
	var dStartDatePlusLag = rMilestone.ms_date_scheduled;
	var nMilestoneStartDateLag = getMilestoneStartDateLag(rMilestone);
	
	if (nMilestoneStartDateLag) {
		dStartDatePlusLag = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_scheduled, nMilestoneStartDateLag);
	}
	
	return dStartDatePlusLag;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"2C159DB6-BE34-4129-B0C7-D67044BA68CF"}
 */
function getMilestoneEndDateWithLag(rMilestone) {
	var dEndDatePlusLag = rMilestone.ms_date_due;
	var nMilestoneEndDateLag = getMilestoneEndDateLag(rMilestone);
	
	if (nMilestoneEndDateLag) {
		dEndDatePlusLag = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, nMilestoneEndDateLag);
	}
	
	return dEndDatePlusLag;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"1DB5F5E5-B53E-493F-AB6E-216ED5AADCE9"}
 */
function getMilestoneMaxDateWithLag(rMilestone) {
	if (rMilestone.ms_lag_date) {
		return rMilestone.ms_lag_date;
	}
	else {
		var nStartDateLag = getMilestoneStartDateLag(rMilestone);
		var nEndDateLag = getMilestoneEndDateLag(rMilestone);
		var dStartDateWithLag = getMilestoneStartDateWithLag(rMilestone);
		var dEndDateWithLag = getMilestoneEndDateWithLag(rMilestone);
		var dMaxDateWithLag = null;
		
		// if we have a nStartDateLag and a nEndDateLag then we have to use the one that produces the latest date 
		if (nStartDateLag && nEndDateLag) {
			if (dStartDateWithLag > dEndDateWithLag) {
				dMaxDateWithLag = dStartDateWithLag;
			}
			else {
				dMaxDateWithLag = dEndDateWithLag;
			}
		}
		// if there is a SS lag and no FS lag then we need to use start date plus SS lag, regardless of whether the end date is later than the start date plus SS lag
		else if (nStartDateLag) {
			dMaxDateWithLag = dStartDateWithLag;
		}
		// otherwise use dEndDatePlusLag, regardless of whether there is an FS lag. even if there isnt the end time will always be later than the start time
		else {
			dMaxDateWithLag = dEndDateWithLag;
		}
		
		return dMaxDateWithLag;
	}
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {Boolean} [bSkipCompletedMS]
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"EF87E981-0C7F-482C-BCEB-D03A20CE2300"}
 */
function getMaxPredecessorDate(rMilestone, bSkipCompletedMS) {
    var arPredecessors = getSchedulablePredecessors(rMilestone, bSkipCompletedMS);
    var dMaxDate = null;
    
    for (var i=0; i<arPredecessors.length; i++) {
    	var rPredecessor = arPredecessors[i];
    	var dPredecessorDate = getMilestoneMaxDateWithLag(rPredecessor);
    	
		if (dPredecessorDate > dMaxDate) {
			dMaxDate = dPredecessorDate;
		}
    }
	
    return dMaxDate;
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"********-C956-4647-989D-AAC7F45B233A"}
 */
function defragMilestoneCapRecs(rMilestone) {
	if (rMilestone && rMilestone.ms_date_scheduled && rMilestone.ms_date_due && utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
		var rEquip = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.getRecord(1);
        var dStartDate = scopes.avDate.copyDate(rMilestone.ms_date_scheduled, true);
        var dEndDate = scopes.avDate.copyDate(rMilestone.ms_date_due, true);
        /**@type {Array<UUID>} */
        var aEmps = getEmpsWorkingOnMS(rMilestone.ms_id);
		
        defragCapacityRecs(RESOURCE_TYPE.Equipment, rEquip.equip_id, dStartDate);
        
		if (dEndDate != dStartDate) {
	        defragCapacityRecs(RESOURCE_TYPE.Equipment, rEquip.equip_id, dEndDate);
		}
		
		if (aEmps) {
			for (var e = 0; e < aEmps.length; e++) {
				var uEmpID = aEmps[e];

		        defragCapacityRecs(RESOURCE_TYPE.Employee, uEmpID, dStartDate);
		        
				if (dEndDate != dStartDate) {
			        defragCapacityRecs(RESOURCE_TYPE.Employee, uEmpID, dEndDate);
				}
			}
		}
	}
}

/**
 * @public 
 * 
 * @param {Array<UUID>} auEquipIDs
 * @param {Date} dStartingFrom
 *
 * @return {Date}
 * 
 * @properties={typeid:24,uuid:"ACDFD33F-196C-4049-A6EE-69B48190F9B4"}
 */
function getEarliestFreeTimeFromEquipments(auEquipIDs, dStartingFrom) {
	/**@type {Date} */
	var dEarliestFreeTime = null;
	
	if (auEquipIDs && auEquipIDs.length > 0) {
		var sDateTimeSQL = scopes.avDate.getDateTimeSQl(dStartingFrom, "M");
		var sSQL = "SELECT MIN(start_date) \
					FROM sch_emp_capacity \
					WHERE \
						org_id = ? \
						AND capacity = 100 \
						AND end_date > " + sDateTimeSQL + " \
						AND equip_id IN (";
						
		var aArgs = [globals.org_id];
		
		for (var i = 0; i < auEquipIDs.length; i++) {
			var uEquipID = auEquipIDs[i];
			
			if (i > 0) {
				sSQL += ",";
			}

			sSQL += "?";
			aArgs.push(uEquipID.toString());
		}

		sSQL += ")";
		
		dEarliestFreeTime = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	
	return dEarliestFreeTime;
}

/**
 * @public 
 * 
 * @param {Array<UUID>} auEquipIDs
 * @param {Date} dStartingFrom
 *
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"9E7FCB2B-F395-41C0-8B17-4620E4D92E35"}
 */
function getLatestFreeTimeFromEquipments(auEquipIDs, dStartingFrom) {
	/**@type {Date} */
	var dLatestFreeTime = null;
	
	if (auEquipIDs && auEquipIDs.length > 0) {
		var sDateTimeSQL = scopes.avDate.getDateTimeSQl(dStartingFrom, "M");
		var sSQL = "SELECT MAX(end_date) \
					FROM sch_emp_capacity \
					WHERE \
						org_id = ? \
						AND capacity = 100 \
						AND start_date < " + sDateTimeSQL + " \
						AND equip_id IN (";
						
		var aArgs = [globals.org_id];
		
		for (var i = 0; i < auEquipIDs.length; i++) {
			var uEquipID = auEquipIDs[i];
			
			if (i > 0) {
				sSQL += ",";
			}

			sSQL += "?";
			aArgs.push(uEquipID.toString());
		}

		sSQL += ")";
		
		dLatestFreeTime = scopes.avDB.SQLQuery(sSQL, null, aArgs);
	}
	
	return dLatestFreeTime;
}

/**
 * This used to load scheduled equipment miletones in Schedule Board and Quick Schedule
 * 
 * @public 
 * 
 * @param {UUID} [uEquipID]
 * @param {UUID} [uDeptID]
 * @param {Boolean} [bFilterByEmployeeRights] - filter by current employees right in sys_empl_dept
 * 
 * @return {{sql:String, args:Array}} query and args
 *
 * @properties={typeid:24,uuid:"6B2E467A-4ECB-4860-B4AB-04EF2113E7F1"}
 */
function getScheduledEquipQuery(uEquipID, uDeptID, bFilterByEmployeeRights) {
	/***@type {{sql:String, args:Array}}*/
	var oSQL = new Object();
	
	oSQL.sql = " FROM sch_milestone ms \
				INNER JOIN sch_equip_schedule es ON es.ms_id = ms.ms_id \
				INNER JOIN sch_schedule sch ON ms.sch_id = sch.sch_id \
				INNER JOIN prod_job j ON sch.job_id = j.job_id \
				INNER JOIN sa_order_revision_detail_section s ON ms.ordrevds_id = s.ordrevds_id \
				INNER JOIN sa_order_revision_detail d ON s.ordrevd_id = d.ordrevd_id \
				INNER JOIN sa_order_revision_header h ON h.ordrevh_id = d.ordrevh_id \
				INNER JOIN sa_order o ON h.ordh_id = o.ordh_id \
				INNER JOIN sa_customer c ON o.cust_id = c.cust_id \
				INNER JOIN sys_department sd ON ms.dept_id = sd.dept_id \
				INNER JOIN eq_equipment eq ON eq.equip_id = es.equip_id \
				LEFT JOIN sys_empl_dept ed ON ms.dept_id = ed.dept_id AND (ed.opcat_id IS NULL OR ms.opcat_id = ed.opcat_id) \
				WHERE \
					ms.org_id = ? \
					AND ISNULL(ms.ms_flg_completed, 0) = 0 \
					AND ISNULL(h.ordrevh_order_status, '') != 'Shipped' \
					AND ISNULL(j.jobstat_id, '') != 'Completed' \
					AND sd.dept_schedule_flag = 1 ";
	oSQL.args = [globals.org_id];
	
	if (uEquipID) {
		oSQL.sql += " AND es.equip_id = ? ";
		oSQL.args.push(uEquipID.toString());
	}

	if (uDeptID) {
		oSQL.sql += " AND ms.dept_id = ? ";
		oSQL.args.push(uDeptID.toString());
	}
	
	if (bFilterByEmployeeRights && globals.avBase_employeeUUID) {
		oSQL.sql += " AND ed.empl_id = ? ";
		oSQL.args.push(globals.avBase_employeeUUID);
	}
	
	oSQL.sql += " AND " + scopes.avDB.getSafeConditionsClause('ms');
	
	return oSQL;
}

/**
 * @public 
 * 
 * @param {UUID} uEquipID
 * @param {Date} [dFromDate]
 * @param {Date} [dToDate]
 * 
 * @return {JSFoundSet<db:/avanti/sch_equip_schedule>}
 *
 * @properties={typeid:24,uuid:"87BDA348-4F5B-4B25-B69E-97ED8A16D17F"}
 */
function getEquipSchedule(uEquipID, dFromDate, dToDate) {
    /**@type {JSFoundSet<db:/avanti/sch_equip_schedule>} */
    var fsEquipSched = null;
    
	if (uEquipID) {
		/***@type {{sql:String, args:Array}}*/
		var oSQL = getScheduledEquipQuery(uEquipID);
		
		oSQL.sql = "SELECT equipsch_id " + oSQL.sql;  
		
	    // return records where start date falls in date range or end date falls in date range 
		if (dFromDate && dToDate) {
		    var sFromDate = scopes.avDate.getDateString(dFromDate, true);
		    var sToDate = scopes.avDate.getDateString(dToDate, true);
		    
		    oSQL.sql += "AND ( \
							(equipsch_start >= CONVERT ( datetime , '" + sFromDate + "', 112) AND DATEADD(DAY, 0, DATEDIFF(DAY, 0, equipsch_start))  <= CONVERT ( datetime , '" + sToDate + "' , 112)) \
							OR \
							(equipsch_end > CONVERT ( datetime , '" + sFromDate + "', 112) AND DATEADD(DAY, 0, DATEDIFF(DAY, 0, equipsch_end))  <= CONVERT ( datetime , '" + sToDate + "' , 112)) \
							)";		    
		}
	    
	    fsEquipSched = scopes.avDB.getFSFromSQL(oSQL.sql, 'sch_equip_schedule', oSQL.args);
	}
    
    return fsEquipSched;
}

/**
 * This used to load scheduled employee miletones in Schedule Board and Quick Schedule
 * 
 * @public 
 * 
 * @param {UUID} uEmpID
 * @param {UUID} [uDeptID] - filter by dept too
 * @param {UUID} [uEquipID] - filter by equip too
 * @param {Boolean} [bFilterByEmployeeRights] - filter by current employees right in sys_empl_dept
 * 
 * @return {{sql:String, args:Array}} query and args
 *
 * @properties={typeid:24,uuid:"2852F856-ECD8-41E5-B83B-511BFD3D4321"}
 */
function getScheduledEmpQuery(uEmpID, uDeptID, uEquipID, bFilterByEmployeeRights) {
	/***@type {{sql:String, args:Array}}*/
	var oSQL = new Object();
	
	oSQL.sql = " FROM sch_milestone ms \
				INNER JOIN sch_empl_schedule es ON es.ms_id = ms.ms_id \
				INNER JOIN sch_schedule sch ON ms.sch_id = sch.sch_id \
				INNER JOIN prod_job j ON sch.job_id = j.job_id \
				INNER JOIN sa_order_revision_detail_section s ON ms.ordrevds_id = s.ordrevds_id \
				INNER JOIN sa_order_revision_detail d ON s.ordrevd_id = d.ordrevd_id \
				INNER JOIN sa_order_revision_header h ON h.ordrevh_id = d.ordrevh_id \
				INNER JOIN sa_order o ON h.ordh_id = o.ordh_id \
				INNER JOIN sa_customer c ON o.cust_id = c.cust_id \
				INNER JOIN sys_department sd ON ms.dept_id = sd.dept_id \
                LEFT OUTER JOIN eq_equipment eq ON eq.opcat_id = ms.opcat_id AND eq.lag_parent IS NULL \
				LEFT JOIN sys_empl_dept ed on ms.dept_id = ed.dept_id AND (ed.opcat_id IS NULL OR ms.opcat_id = ed.opcat_id) \
				WHERE \
					ms.org_id = ? \
					AND ISNULL(ms.ms_flg_completed, 0) = 0 \
					AND ISNULL(h.ordrevh_order_status, '') != 'Shipped' \
					AND ISNULL(j.jobstat_id, '') != 'Completed' \
					AND sd.dept_schedule_flag = 1 ";
	oSQL.args = [globals.org_id];
	
	if (uEmpID) {
		oSQL.sql += " AND es.empl_id = ? ";
		oSQL.args.push(uEmpID.toString());
	}
	
	if (uDeptID) {
		oSQL.sql += " AND ms.dept_id = ? ";
		oSQL.args.push(uDeptID.toString());
	}

	if (uEquipID) {
		oSQL.sql += " AND eq.equip_id = ? ";
		oSQL.args.push(uEquipID.toString());
	}
	
	if (bFilterByEmployeeRights && globals.avBase_employeeUUID) {
		oSQL.sql += " AND ed.empl_id = ? ";
		oSQL.args.push(globals.avBase_employeeUUID);
	}
	
	oSQL.sql += " AND " + scopes.avDB.getSafeConditionsClause('ms');
	
	return oSQL;
}

/**
 * @public 
 * 
 * @param {UUID} uEmpID
 * @param {Date} [dFromDate]
 * @param {Date} [dToDate]
 * @param {UUID} [uDeptID]
 * @param {UUID} [uEquipID]
 * 
 * @return {JSFoundSet<db:/avanti/sch_empl_schedule>}
 *
 * @properties={typeid:24,uuid:"38816CFC-AC63-4800-810B-FC9D0AA7AA14"}
 */
function getEmpSchedule(uEmpID, dFromDate, dToDate, uDeptID, uEquipID) {
    /**@type {JSFoundSet<db:/avanti/sch_empl_schedule>} */
    var fsEmpSched = null;
    
	if (uEmpID) {
		/***@type {{sql:String, args:Array}}*/
		var oSQL = getScheduledEmpQuery(uEmpID, uDeptID, uEquipID);
		
		oSQL.sql = "SELECT emplsch_id " + oSQL.sql;  
		
	    // return records where start date falls in date range or end date falls in date range 
		if (dFromDate && dToDate) {
		    var sFromDate = scopes.avDate.getDateString(dFromDate, true);
		    var sToDate = scopes.avDate.getDateString(dToDate, true);
		    
		    oSQL.sql += "AND ( \
							(emplsch_start >= CONVERT ( datetime , '" + sFromDate + "', 112) AND DATEADD(DAY, 0, DATEDIFF(DAY, 0, emplsch_start))  <= CONVERT ( datetime , '" + sToDate + "' , 112)) \
							OR \
							(emplsch_end > CONVERT ( datetime , '" + sFromDate + "', 112) AND DATEADD(DAY, 0, DATEDIFF(DAY, 0, emplsch_end))  <= CONVERT ( datetime , '" + sToDate + "' , 112)) \
							)";		    
		}
	    
	    fsEmpSched = scopes.avDB.getFSFromSQL(oSQL.sql, 'sch_empl_schedule', oSQL.args);
	}
    
    return fsEmpSched;
}

/**
 * This function doesnt acutally lock any database tables or records. It uses the sys_organization.org_schedule_lock column as a token, whichever sales order has the token
 * is allowed to add itself to the schedule. the order id is assigned to sys_organization.org_schedule_lock to lock it for this order. when the order is done it releases the
 * 'lock' by nulling out sys_organization.org_schedule_lock. then another order can grab the lock/token.
 * 
 * @public 
 * 
 * @param {UUID} uLockID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F5C15C0D-ECB8-4F29-9FD3-8FCAEEBDC292"}
 */
function getScheduleLock(uLockID) {
    var procedure_declaration = '{call sp_getScheduleLock(?, ?)}';
    var typesArray = [0, 0];
    var args = [uLockID.toString(), globals.org_id];
    var dataset = plugins.rawSQL.executeStoredProcedure(globals.avBase_dbase_avanti, procedure_declaration, args, typesArray, 1);

    if (dataset && dataset.getMaxRowIndex() == 1 && dataset.getMaxColumnIndex() == 1 && dataset.getValue(1, 1) == 1) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * @public 
 * 
 * @param {UUID} uLockID
 * @param {Number} [nWaitTimeOutMins]
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"01E11CEC-5579-42CE-B6A7-90FC2ABFE310"}
 */
function waitForScheduleLock(uLockID, nWaitTimeOutMins) {
	var dWaitStart = application.getTimeStamp();
	var bScheduleLock = getScheduleLock(uLockID);
	var iLoops = 0;
	
	while (!bScheduleLock) {
		iLoops++;
		application.sleep(1000);
		bScheduleLock = getScheduleLock(uLockID);
		
		// see if time out has expired if there is one
		if (nWaitTimeOutMins && !bScheduleLock && iLoops % 60 == 0 && scopes.avDate.getDiffInMinutes(dWaitStart, application.getTimeStamp()) > nWaitTimeOutMins) {
			scopes.avUtils.devLog("SL-20416", "waitForScheduleLock didnt get lock", null, rRevHeader.ordh_id);
			return false;
		}
	}
	
	// log if we waited for lock for more than 1 minute
	if (scopes.avDate.getDiffInMinutes(dWaitStart, application.getTimeStamp()) > 1) {
		scopes.avUtils.devLog("SL-24955", "waitForScheduleLock more than 1 minute");
	}
	
	return true;
}

/**
 * @public 
 * 
 * @param {UUID} uLockID
 *
 * @properties={typeid:24,uuid:"B063B199-638D-4E31-9FF5-645997362D53"}
 */
function releaseScheduleLock(uLockID) {
    var procedure_declaration = '{call sp_releaseScheduleLock(?, ?)}';
    var typesArray = [0, 0];
    var args = [uLockID.toString(), globals.org_id];
    
    plugins.rawSQL.executeStoredProcedure(globals.avBase_dbase_avanti, procedure_declaration, args, typesArray, 1);
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {String} [sDirection] - (F)orwards or (B)ackwards
 * @param {Boolean} [bAllowCompletedMS]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"498231B1-D9BA-41B4-B57B-A741849E14E1"}
 */
function isMilestoneSchedulable(rMilestone, sDirection, bAllowCompletedMS) {
	if (!sDirection) {
		sDirection = SCHEDULE_DIRECTION.Forwards;
	}
	
	// took this logic from avCalcs_schedule_buildTimeline
	if (rMilestone.sch_milestone_to_sys_department.dept_schedule_flag
	        && (!rMilestone.ms_flg_completed || bAllowCompletedMS)
	        && rMilestone.sch_milestone_to_sys_department.dept_active
	        && rMilestone.sch_milestone_to_sys_operation_category.opcat_active
	        && (!utils.hasRecords(rMilestone.sch_milestone_to_sys_cost_centre) || rMilestone.sch_milestone_to_sys_cost_centre.cc_active)
	        && (areThereAnyActiveEquipmentForMilestone(rMilestone, sDirection) || doesMilestoneScheduleEmps(rMilestone)) ) {

      	return true;
    }
    else {
    	return false;
    }
}

/**
 * @public 
 * 
 * @param {UUID|String} uEquipID
 *
 * @properties={typeid:24,uuid:"01261D57-FE2B-4B73-99BF-29A8E9135EBD"}
 */
function clearCapacityRecsOutsideEquipScheduleDates(uEquipID) {
	if (uEquipID) {
		uEquipID = uEquipID.toString();
		
	    var sSQL = "SELECT EC.ec_id \
					FROM sch_emp_capacity EC \
					INNER JOIN sch_equip_schedule ES ON EC.equip_ms_id = ES.ms_id \
					WHERE \
						EC.org_id = ? \
						AND EC.equip_id = ? \
						AND (EC.start_date < ES.equipsch_start OR EC.end_date > ES.equipsch_end)";    

		scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['capacity', 'equip_ms_id'], [100, null], sSQL, [globals.org_id, uEquipID]);
	}
}

/**
 * sl-26145 - if you click 'schedule milestones' in RTS then close browser before it finishes (maybe scheduling is taking too long)
 * then we are left with orphan capacity recs, blocking parts of the schedule. clear them here.
 * 
 * @public 
 * 
 * @properties={typeid:24,uuid:"C27E032F-8EBD-4A8D-861E-998EB34BE4AA"}
 */
function clearOrphanCapacityRecs() {
	var nNumMinsRecordStale = 0.006; // record stale and can be cleared after one hour
	var nStaleMins = nNumMinsRecordStale + 0.004; // have to add 40 to nNumMinsRecordStale for math to work as mins only goes to 60 
	// temp_schedule_date is stored in db using server time, so use new Date() here  
	var nCurDate = scopes.avDate.getDateAsNum(new Date(), true);
    var sSQL = "SELECT sch_id FROM sch_schedule WHERE org_id = ? AND ? > temp_schedule_date + ?";
    var aArgs = [globals.org_id, nCurDate, nStaleMins]; 
    /**@type {JSFoundSet<db:/avanti/sch_schedule>} */
	var fsSchedule = scopes.avDB.getFSFromSQL(sSQL, "sch_schedule", aArgs);
    
	if (utils.hasRecords(fsSchedule)) {
		for (var i = 1; i <= fsSchedule.getSize(); i++) {
			var rSchedule = fsSchedule.getRecord(1);
			
			// check for sch_emp_capacity recs with no corresponding sch_equip_schedule recs, to ensure we only correct recs where there really is a problem.			
			if (doesScheduleHaveOrphanCapacityRecs(rSchedule.sch_id)) {
				clearMilestonesCapacity(rSchedule);
				rSchedule.temp_schedule_date = null;
				scopes.avUtils.devLog("SL-26145", "cleared Orphan Capacity Recs", null, rSchedule.sch_id, rSchedule.job_id, null, null, true);
			}
		}
		
		databaseManager.saveData(fsSchedule);
	}
}

/**
 * @properties={typeid:24,uuid:"A70447CB-B048-452E-BE97-BCB7C6A373D5"}
 */
function deleteDuplicateNonShiftCapRecs() {
	var sSQL = "SELECT equip_id, empl_id, schedule_date_num, COUNT(ec_id) \
				FROM sch_emp_capacity \
				WHERE \
					org_id = ? \
					AND duration = 1440 \
					AND is_break = 1 \
					AND schedule_date_num IS NOT NULL \
					AND (equip_id IS NOT NULL OR empl_id IS NOT NULL) \
				GROUP BY equip_id, empl_id, schedule_date_num \
				HAVING COUNT(ec_id) > 1";
	var ds = scopes.avDB.getDataset(sSQL, [globals.org_id]);
	
    if (ds) {
        for (var i = 1; i <= ds.getMaxRowIndex(); i++) {
            var sEquipID = ds.getValue(i, 1);
            var sEmpID = ds.getValue(i, 2);
            var nScheduleDate = ds.getValue(i, 3);
        	var sDeleteSQL = "DELETE FROM sch_emp_capacity WHERE org_id = ? AND schedule_date_num = ?";
        	var aArgs = [globals.org_id, nScheduleDate];
            
			if (sEquipID) {
				sDeleteSQL += " AND equip_id = ?";
				aArgs.push(sEquipID);
			}
			else {
				sDeleteSQL += " AND empl_id = ?";
				aArgs.push(sEmpID);
			}
			
			scopes.avDB.RunSQL(sDeleteSQL, null, aArgs);
        }
    }
}

/**
 * @public 
 * 
 * @param {UUID} uScheduleID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"1B02F5B3-B23C-4AB3-AA09-F95B0800B192"}
 */
function doesScheduleHaveOrphanCapacityRecs(uScheduleID) {
	var bDoesScheduleHaveOrphanCapacityRecs = false;
	
	if (uScheduleID) {
	    var sSQL = "SELECT COUNT(C.ec_id) \
					FROM sch_milestone M \
					INNER JOIN sch_emp_capacity C ON C.equip_ms_id = M.ms_id \
					LEFT JOIN sch_equip_schedule EQ ON EQ.ms_id = M.ms_id \
					WHERE \
						M.sch_id = ? \
						AND EQ.equipsch_id IS NULL";
		var aArgs = [uScheduleID.toString()];
		
		bDoesScheduleHaveOrphanCapacityRecs = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}
	
	return bDoesScheduleHaveOrphanCapacityRecs;
}

/**
 * @private 
 * 
 * @param {Date} dDate
 * 
 * @return {Date}
 *
 * @properties={typeid:24,uuid:"7AB12EF2-3392-43F4-B597-1FEE2F0CD8DB"}
 */
function clientTime(dDate) {
	return scopes.avDate.convertServerTimeToClientTime(dDate);
}

/**
 * This deletes all capacity recs for a shift where the whole day is free (has no jobs scheduled)  
 * 
 * @public 
 * 
 * @param {UUID} uShiftID
 *
 * @properties={typeid:24,uuid:"C2E38826-**************-22CEC7409B4E"}
 */
function deleteShiftFreeResourceDayCapacityRecs(uShiftID) {
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
	var fsFreeCapacityRecs = getShiftFreeResourceDayCapacityRecs(uShiftID);
	
	if (utils.hasRecords(fsFreeCapacityRecs)) {
		scopes.avUtils.devLog("SL-26145", "deleted Shift Free Day Capacity Recs: " + databaseManager.getFoundSetCount(fsFreeCapacityRecs), 
				null, uShiftID, null, null, null, true);
		fsFreeCapacityRecs.deleteAllRecords();
	}

	// sl-28281 - set shift_setup_changed flag for all remaining sch_emp_capacity for this shift. when they do optimize schedule it will delete and recreate these
	
	scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['shift_setup_changed'], [1], "SELECT ec_id FROM sch_emp_capacity WHERE shift_id = ? AND schedule_date_num >= ?", 
		[uShiftID.toString(), scopes.avDate.getDateAsNum(application.getTimeStamp())]);
}

/**
 * This deletes all capacity recs for an equipment where the whole day is free (has no jobs scheduled)  
 * 
 * @public 
 * 
 * @param {UUID} uEquipID
 *
 * @properties={typeid:24,uuid:"C5569294-6C1C-49E8-A43B-631A17368E8C"}
 */
function deleteEquipFreeResourceDayCapacityRecs(uEquipID) {
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
	var fsFreeCapacityRecs = getEquipFreeResourceDayCapacityRecs(uEquipID);
	
	if (utils.hasRecords(fsFreeCapacityRecs)) {
		scopes.avUtils.devLog("SL-28902", "deleted Equip Free Day Capacity Recs: " + databaseManager.getFoundSetCount(fsFreeCapacityRecs), 
				null, uEquipID, null, null, null, true);
		fsFreeCapacityRecs.deleteAllRecords();
	}

	// set shift_setup_changed flag for all remaining sch_emp_capacity for this shift. when they do optimize schedule it will delete and recreate these	
	scopes.avDB.updateFSWithSQLQuery('sch_emp_capacity', ['shift_setup_changed'], [1], "SELECT ec_id FROM sch_emp_capacity WHERE equip_id = ? AND schedule_date_num >= ?", 
		[uEquipID.toString(), scopes.avDate.getDateAsNum(application.getTimeStamp())]);
}

/**
 * This returns all capacity recs for a shift where the whole resource/day is free (has no jobs scheduled)  
 * 
 * @public 
 * 
 * @param {UUID} uShiftID
 *
 * @return {JSFoundSet<db:/avanti/sch_emp_capacity>}
 *
 * @properties={typeid:24,uuid:"8899F2B3-79B0-417E-8B27-933AFDB7856E"}
 */
function getShiftFreeResourceDayCapacityRecs(uShiftID) {
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
	var fsCapacityRecs = null;
	
	if (uShiftID) {
		var sSQL = "SELECT freeCapacity.ec_id \
					FROM sch_emp_capacity freeCapacity \
					WHERE \
						freeCapacity.shift_id = ? \
						AND ISNULL(CONVERT(VARCHAR(36), freeCapacity.equip_id), '') + ',' + \
							ISNULL(CONVERT(VARCHAR(36), freeCapacity.empl_id), '') + ',' + \
							ISNULL(CONVERT(VARCHAR(36), freeCapacity.schedule_date_num), '') \
							NOT IN ( \
								SELECT ISNULL(CONVERT(VARCHAR(36), usedCapacity.equip_id), '') + ',' + \
									   ISNULL(CONVERT(VARCHAR(36), usedCapacity.empl_id), '') + ',' + \
									   ISNULL(CONVERT(VARCHAR(36), usedCapacity.schedule_date_num), '') \
								FROM sch_emp_capacity usedCapacity \
								WHERE \
									usedCapacity.shift_id = ? \
									AND ISNULL(usedCapacity.is_break, 0) = 0 \
									AND usedCapacity.duration > 0 \
									AND usedCapacity.capacity < 100 \
							)";
		
		fsCapacityRecs = scopes.avDB.getFSFromSQL(sSQL, "sch_emp_capacity", [uShiftID.toString(), uShiftID.toString()]);
	}
	
	return fsCapacityRecs;
}

/**
 * This returns all capacity recs for a shift where the whole resource/day is free (has no jobs scheduled)  
 * 
 * @public 
 * 
 * @param {UUID} uEquipID
 *
 * @return {JSFoundSet<db:/avanti/sch_emp_capacity>}
 *
 * @properties={typeid:24,uuid:"11508EC8-C77A-40D6-BC23-6806E0F464A7"}
 */
function getEquipFreeResourceDayCapacityRecs(uEquipID) {
    /**@type {JSFoundSet<db:/avanti/sch_emp_capacity>} */
	var fsCapacityRecs = null;
	
	if (uEquipID) {
		var sSQL = "SELECT freeCapacity.ec_id \
					FROM sch_emp_capacity freeCapacity \
					WHERE \
						freeCapacity.equip_id = ? \
						AND ISNULL(CONVERT(VARCHAR(36), freeCapacity.schedule_date_num), '') \
							NOT IN ( \
								SELECT ISNULL(CONVERT(VARCHAR(36), usedCapacity.schedule_date_num), '') \
								FROM sch_emp_capacity usedCapacity \
								WHERE \
									usedCapacity.equip_id = ? \
									AND ISNULL(usedCapacity.is_break, 0) = 0 \
									AND usedCapacity.duration > 0 \
									AND usedCapacity.capacity < 100 \
							)";
		
		fsCapacityRecs = scopes.avDB.getFSFromSQL(sSQL, "sch_emp_capacity", [uEquipID.toString(), uEquipID.toString()]);
	}
	
	return fsCapacityRecs;
}

/**
 * @properties={typeid:24,uuid:"C93DD65C-C7AC-44B2-987B-B8F55C1A252B"}
 * @param {Number} ms_time_budget
 * @return {String}
 */
function calculate_ms_time_budget_formatted(ms_time_budget) {
    // sl-952 - display in hh:nn format

    if (ms_time_budget) {
        var nHours = Math.floor(ms_time_budget / 60)
        var nMins = ms_time_budget % 60
        return nHours + ':' + utils.numberFormat(nMins, '00')
    }
    return '0:00'
}

/**
 * @public 
 * 
 * @param {UUID} uEquipID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"FF1C9575-5D7C-46AA-89BF-4D994AC631E9"}
 */
function doesEquipUseLoadBalancing(uEquipID) {
	var bDoesEquipUseLoadBalancing = false;
	
	if (uEquipID) {
		var sSQL = "SELECT COUNT(wp.wrkpool_id) \
					FROM sch_workpool_machine wpm \
					INNER JOIN sch_workpool wp ON wpm.wrkpool_id = wp.wrkpool_id \
					INNER JOIN sch_workpool_machine wpm2 ON wpm2.wrkpool_id = wpm.wrkpool_id AND wpm2.equip_id != wpm.equip_id \
					WHERE \
						wp.org_id = ? \
						AND wpm.equip_id = ? \
						AND wp.wrkpool_flg_load_bal = 1";
		var aArgs = [globals.org_id, uEquipID.toString()];
		
		bDoesEquipUseLoadBalancing = (scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0);
	}
	
	return bDoesEquipUseLoadBalancing;
}

/**
 * @public 
 * 
 * @param {UUID} uEquipID
 * 
 * @return {Array<UUID>}
 *
 * @properties={typeid:24,uuid:"9EB8B6B3-2F22-445D-A6E4-DA416C778E92"}
 */
function getLoadBalancingWorkPoolEquipment(uEquipID) {
	var aWorkPoolEquipment = [];

	if (uEquipID) {
		var sSQL = "SELECT wpm2.equip_id \
					FROM sch_workpool_machine wpm \
					INNER JOIN sch_workpool wp ON wpm.wrkpool_id = wp.wrkpool_id \
					INNER JOIN sch_workpool_machine wpm2 ON wpm2.wrkpool_id = wpm.wrkpool_id \
					WHERE \
						wp.org_id = ? \
						AND wpm.equip_id = ? \
						AND wp.wrkpool_flg_load_bal = 1";
		var aArgs = [globals.org_id, uEquipID.toString()];

		aWorkPoolEquipment = scopes.avDB.SQLQuery(sSQL, null, aArgs, null, null, null, null, true);
		
		if (aWorkPoolEquipment == null) {
			aWorkPoolEquipment = [];
		}
	}

	return aWorkPoolEquipment;
}


/**
 * This function will calculate the posted time based on the number of minutes of leeway time allowed
 * @param {JSRecord<db:/avanti/sch_shift_detail>} rShiftDetail
 * @param {String} shiftType
 * @param {Date} shiftDate
 * @param {Date} employeeClockedShift
 * @param {Number} minutesToRoundBeforeShift
 * @param {Number} minutesToRoundAfterShift
 *
 * @return
 * @properties={typeid:24,uuid:"D3A1B671-22F4-4A5F-9C48-18070DB7B865"}
 */

function getPostedShiftDateTime(rShiftDetail, shiftType, shiftDate, employeeClockedShift, minutesToRoundBeforeShift, minutesToRoundAfterShift) {
	
	if (!rShiftDetail) {
		return employeeClockedShift;
	}
	
	var shiftBracket = {
		start: new Date(shiftDate),
		end: new Date(shiftDate)
	};
	
	var shiftTimeNumMinsStart;
	var shiftTimeNumMinsEnd;
	//Calculate the shift bracket for rounding. time before and after shift time.
	if (shiftType == 'START') {
		shiftTimeNumMinsStart = rShiftDetail.shiftdet_starttime_num_mins - minutesToRoundBeforeShift;
		shiftTimeNumMinsEnd = rShiftDetail.shiftdet_starttime_num_mins + minutesToRoundAfterShift;
	}
	else if (shiftType == 'END') {
		shiftTimeNumMinsStart = rShiftDetail.shiftdet_endtime_num_mins - minutesToRoundBeforeShift;
		shiftTimeNumMinsEnd = rShiftDetail.shiftdet_endtime_num_mins + minutesToRoundAfterShift;
	}
	
	shiftBracket.start = scopes.avDate.setTimeToDate(shiftBracket.start,0,0,0);
	shiftBracket.end = scopes.avDate.setTimeToDate(shiftBracket.end,0,0,0);
	
	//Calculate the start of the shift bracket for an employee's shift date. 
	//if start of a shift bracket is negative that mean it's in previous day so use math.ceil to round the negative else use math.floor
	if (shiftTimeNumMinsStart < 0) {
		shiftBracket.start = scopes.avDate.setTimeToDate(shiftBracket.start,Math.ceil(shiftTimeNumMinsStart / 60),shiftTimeNumMinsStart % 60,0);
	}
	else {
		shiftBracket.start = scopes.avDate.setTimeToDate(shiftBracket.start,Math.floor(shiftTimeNumMinsStart / 60),shiftTimeNumMinsStart % 60,0);
	}
	
	//calculate the end of the shift bracket for an emplyee's shift date
	shiftBracket.end = scopes.avDate.setTimeToDate(shiftBracket.end,Math.floor(shiftTimeNumMinsEnd / 60),shiftTimeNumMinsEnd % 60,0);
	
	var postedTime = employeeClockedShift;
	//check if employee shift falls within the shift bracket
	if (employeeClockedShift >= shiftBracket.start && employeeClockedShift <= shiftBracket.end) {
		if (shiftType == 'START') {
			postedTime = scopes.avDate.setTimeToDate(shiftDate,Math.floor(rShiftDetail.shiftdet_starttime_num_mins / 60),rShiftDetail.shiftdet_starttime_num_mins % 60,0);
		}
		else if (shiftType == 'END') {
			postedTime = scopes.avDate.setTimeToDate(shiftDate,Math.floor(rShiftDetail.shiftdet_endtime_num_mins / 60),rShiftDetail.shiftdet_endtime_num_mins % 60,0);
		}
	}
	
	return postedTime;
}

/**
 * Update connected lag equipment if there is one
 * 
 * @param {JSRecord<db:/avanti/eq_equipment>} rEquip
 *
 * @properties={typeid:24,uuid:"F477AB8A-A723-4942-9598-C684D6353CBA"}
 */
function updateLagEquipment(rEquip) {
	if (utils.hasRecords(rEquip.eq_equipment_to_eq_equipment$lag)) {
		var rLagEquip = rEquip.eq_equipment_to_eq_equipment$lag.getRecord(1);
		/**@type {JSRecord<db:/avanti/sch_equipment_shift>} */
		var rLagEquipShift = null;
		/**@type {JSRecord<db:/avanti/sch_equip_excep>} */
		var rLagEquipEx = null;
		var rEquipShift = null;
		var rEquipEx = null;
		var i = 0;
		
		if (utils.hasRecords(rLagEquip.eq_equipment_to_sch_equipment_shift)) {
			rLagEquip.eq_equipment_to_sch_equipment_shift.deleteAllRecords();
		}
		
		if (utils.hasRecords(rLagEquip.eq_equipment_to_sch_equip_excep)) {
			rLagEquip.eq_equipment_to_sch_equip_excep.deleteAllRecords();
		}
		
		if (rEquip.eq_equipment_to_sch_equipment_shift) {
			for (i = 1; i <= rEquip.eq_equipment_to_sch_equipment_shift.getSize(); i++) {
				rEquipShift = rEquip.eq_equipment_to_sch_equipment_shift.getRecord(i);					
				rLagEquipShift = scopes.avDB.duplicateRecord(rEquipShift);
				rLagEquipShift.equip_id = rLagEquip.equip_id;
				databaseManager.saveData(rLagEquipShift);
			}
		}
		
		if (rEquip.eq_equipment_to_sch_equip_excep) {
			for (i = 1; i <= rEquip.eq_equipment_to_sch_equip_excep.getSize(); i++) {
				rEquipEx = rEquip.eq_equipment_to_sch_equip_excep.getRecord(i);					
				rLagEquipEx = scopes.avDB.duplicateRecord(rEquipEx);
				rLagEquipEx.equip_id = rLagEquip.equip_id;
				databaseManager.saveData(rLagEquipEx);
			}
		}
	}
}

/**
 * @public
 *
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @return {JSFoundSet<db:/avanti/sch_milestone>}
 *
 * @properties={typeid:24,uuid:"7B0A512F-AF2A-430B-A56F-F5C4676D4FCE"}
 */
function getPredecessors(rMilestone) {
    /**@type {JSFoundSet<db:/avanti/sch_milestone>} */
	var fsPredecessors = null;
	
	if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$predecessors_using_successor_ids)){
		fsPredecessors = rMilestone.sch_milestone_to_sch_milestone$predecessors_using_successor_ids;
	}
	else if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$pred_ms_id)) {
		fsPredecessors = rMilestone.sch_milestone_to_sch_milestone$pred_ms_id;
	}
	
	return fsPredecessors;
}

/**
 * @public 
 * 
 * @param {UUID|String} uOpCatID
 * 
 * @return {JSRecord<db:/avanti/eq_equipment>}
 *
 * @properties={typeid:24,uuid:"52D7A64E-FA7C-4C1F-9CE9-1B55C036521D"}
 */
function getEquipmentFromOpCat(uOpCatID) {
	/***@type {JSRecord<db:/avanti/eq_equipment>} ***/
	var rEquip = null;
	
	if (uOpCatID) {
		rEquip = scopes.avDB.getRecFromSQL("SELECT equip_id FROM eq_equipment WHERE org_id = ? AND opcat_id = ? AND lag_parent IS NULL", "eq_equipment", 
			[globals.org_id, uOpCatID.toString()]);
	}
	
	return rEquip;
}
