/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A54EB32E-DD93-4846-A188-0A8733E4560F",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"2195A2FC-C80D-4088-9C81-F6E6C2BDEDC1"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"89C77C8C-D9F1-40A0-94DA-1C49C07FD86C"}
 */
function onShow(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

}


