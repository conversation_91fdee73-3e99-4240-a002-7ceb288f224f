/**
 * @properties={typeid:35,uuid:"D359A1D3-2FF3-4FC4-A6F0-AB9FEB57419D",variableType:-4}
 */
var _bHoldButtonPressed = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"248459B1-F1E6-4BFB-99D4-58C30DFA5694"}
 */
var _StatusFilter = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F5190D35-0753-4664-BDF1-************"}
 */
var _CustomerFilter = "";

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A07A96E1-1A7A-423B-84EC-0B9988A58331",variableType:4}
 */
var _bWorkatoUseInvoiceRegister;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"7279FF9C-4DDA-498A-90B4-FFAB7C5A576D",variableType:-4}
 */
var _bWorkatoIntegration = false;

/**
 * @type {Boolean}
 *
 * @properties={typeid:35,uuid:"12E5BD2C-F191-421E-BC31-A74E9E5E26C8",variableType:-4}
 */
var _bIsNetSuiteIntegration = false;

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"********-FAEA-405B-8E37-38A46D2A318D"}
 */
function onRecordSelection(event, _form) {
	var result = _super.onRecordSelection(event, _form);
	
	scopes.avBilling.$sa_cash_receipt_id = sa_cash_receipt_id;
	scopes.avBilling.$cash_receipt_cust_id = cust_id;
	scopes.avBilling.$cash_receipt_status = sa_cash_receipt_status;
	
	forms.sa_cash_receipt_dtl.bUpdateAppliedAmount = false;
	forms.sa_cash_receipt_dtl.refreshUI();
	forms.sa_cash_receipt_invoices_dtl_tbl.loadRecords();
	
	setNoteSource();
	refreshUI();
	return result;
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"509C3D5E-712D-4A95-9F39-4CC6C1DDF101"}
*/
function dc_new(_event, _triggerForm) {
	var sReceiptType = globals.avBase_getSystemPreference_String(162);
	
	if (sReceiptType == 'C'){
		var result =  _super.dc_new(_event, _triggerForm);
		
		forms.sa_cash_receipt_invoices_dtl_tbl.loadRecords();
		
		// GD - Apr 13, 2016: Changed this to onHold (H) from (O)
		sa_cash_receipt_status = 'H';
		
		return result;
	}
	else{
		
		//Show Dialog
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.dialog.notification'),i18n.getI18NMessage('avanti.dialog.cashReceiptsNotEnabled_msg'),i18n.getI18NMessage('avanti.dialog.ok'))
		return null;
	}
	
}

/**
 * @properties={typeid:24,uuid:"CD5D9E1B-14C9-46EE-A55B-E8C960D52589"}
 */
function setNoteSource()
{
    forms.sa_cash_receipt_note_tbl.setNoteSource(cust_id, sa_cash_receipt_id, globals.$NoteObjectRelation_CashReceipt, controller.getName());
}

/**
 *
 * @param {JSEvent} _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"AE0AC9BB-1BD4-4EAC-BC61-3BCDFB972104"}
 */
function dc_save(_event, _triggerForm) {
	forms.sa_cash_receipt_dtl.updateStatus(1);
	
	var aGoodDeps = [];

	if(utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_order_payment$payment)){
		for(var i=1; i<=sa_cash_receipt_to_sa_cash_receipt_order_payment$payment.getSize(); i++){
			var rCROrdPayment = sa_cash_receipt_to_sa_cash_receipt_order_payment$payment.getRecord(i) ;
			createDepositAndPOSTrans(rCROrdPayment, foundset.getSelectedRecord());
			aGoodDeps.push(rCROrdPayment.ordrevhdep_id);
		}
	}
	
	// Deposits created from sales orders must not be deleted
	if (utils.hasRecords(foundset.sa_cash_receipt_to_sa_order_revh_deposit$not_entered_thru_cr)) {
	    aGoodDeps.push(foundset.sa_cash_receipt_to_sa_order_revh_deposit$not_entered_thru_cr.ordrevhdep_id);
	}
	
	// if they have cleared out any payments - have to delete deposits
	deleteOrphanDeposits(aGoodDeps);
	_bHoldButtonPressed = false;
	
	var result =  _super.dc_save(_event, _triggerForm)
	return result
}

/**
 * @param {Array<String>} aGoodDeps
 *
 * @properties={typeid:24,uuid:"7F494975-07BE-4860-BA9F-0F03D6D66795"}
 */
function deleteOrphanDeposits(aGoodDeps){
	/**@type {JSFoundset<db:/avanti/sa_order_revh_deposit>} */
	var fsCRDeps = scopes.avDB.getFS('sa_order_revh_deposit', ['sa_cash_receipt_id'], [sa_cash_receipt_id]);
	
	for(var i=fsCRDeps.getSize(); i>=1; i--){
		var rCRDeps = fsCRDeps.getRecord(i);
		
		if(aGoodDeps.indexOf(rCRDeps.ordrevhdep_id) == -1){
			if(utils.hasRecords(rCRDeps.sa_order_revh_deposit_to_sa_invoice_tender_trans)){
				rCRDeps.sa_order_revh_deposit_to_sa_invoice_tender_trans.deleteRecord()
			}

			rCRDeps.sa_order_revh_deposit_to_sa_order_revision_header.ordrevh_deposit_total -= rCRDeps.ordrevhdep_amount;
			
			fsCRDeps.deleteRecord(rCRDeps);
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_cash_receipt_order_payment>} rCROrdPayment
 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceipt
 * @properties={typeid:24,uuid:"8D7174AA-1939-418B-A57A-690366243C1B"}
 */
function createDepositAndPOSTrans(rCROrdPayment, rCashReceipt){
	if(utils.hasRecords(rCROrdPayment.sa_cash_receipt_order_payment_to_sa_order.sa_order_to_sa_order_revision_header$first_rev)){
		var rOrderRev = rCROrdPayment.sa_cash_receipt_order_payment_to_sa_order.sa_order_to_sa_order_revision_header$first_rev.getRecord(1);
		/**@type {JSRecord<db:/avanti/sa_order_revh_deposit>} */
		var rDeposit;
		/**@type {JSRecord<db:/avanti/sa_invoice_tender_trans>} */
		var rTT;

		// updating existing dep
		if(utils.hasRecords(rCROrdPayment.sa_cash_receipt_order_payment_to_sa_order_revh_deposit)){
			rDeposit  = rCROrdPayment.sa_cash_receipt_order_payment_to_sa_order_revh_deposit.getRecord(1);
			
			// this dep amt hasnt changed - dont do anything
			if(rDeposit.ordrevhdep_amount == rCROrdPayment.crop_payment_amount){
				return;
			}

			rDeposit.sequence_nr = scopes.avDB.getNextSequenceNr(rOrderRev.sa_order_revision_header_to_sa_order_revh_deposit);
			rTT = rDeposit.sa_order_revh_deposit_to_sa_invoice_tender_trans.getRecord(1);
		}
		// creating new dep
		else{
			rDeposit  = scopes.avDB.newRecord('sa_order_revh_deposit');
			databaseManager.saveData(rDeposit); //forcing save so we can execute the onRecordChange Event.
			rCROrdPayment.ordrevhdep_id = rDeposit.ordrevhdep_id;
		}
		
		/// DEPOSIT
		rDeposit.ordrevh_id = rOrderRev.ordrevh_id;
		rDeposit.ordrevhdep_amount = rCROrdPayment.crop_payment_amount;
		rDeposit.ordrevhdep_date_added = application.getTimeStamp();
		rDeposit.paymethod_id = rCashReceipt.paymethod_id;
		rDeposit.sa_cash_receipt_id = rCashReceipt.sa_cash_receipt_id;
		rDeposit.ordrevhdep_added_thru_cr = 1;
		rDeposit.ordrevhdep_record_type = scopes.avUtils.ENUM_DEPOSIT_RECORD_TYPE.ReceiveDeposit;
		
	  	var sPayMethodType = '';
		if(utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_payment_method) 
		        && rCashReceipt.sa_cash_receipt_to_sa_payment_method.paymethod_type) {
			sPayMethodType = rCashReceipt.sa_cash_receipt_to_sa_payment_method.paymethod_type; 
		}
		
		if(sPayMethodType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount 
		        && !rCashReceipt.sa_cash_receipt_transfer_cda) {
			rDeposit.ordrevdstaskpostitem_id = rCROrdPayment.ordrevdstaskpostitem_id;
			rDeposit.ordrevhdep_record_type = scopes.avUtils.ENUM_DEPOSIT_RECORD_TYPE.ApplyDeposit;
			rDeposit.ordrevhdep_is_postage_dep = 1;
		}
		
        if (sPayMethodType == scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount) {
            rDeposit.ordrevhdep_record_type = scopes.avUtils.ENUM_DEPOSIT_RECORD_TYPE.ApplyDeposit;
        }
		
		databaseManager.saveData(rDeposit);
		
		rDeposit.sa_order_revh_deposit_to_sa_order_revision_header.ordrevh_deposit_total = rDeposit.sa_order_revh_deposit_to_sa_order_revision_header.clc_tot_ord_and_cr_deposits; 
		
        if (sPayMethodType != scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount 
                && sPayMethodType != scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount) {
            /// TENDER TRANSACTION
            rTT = scopes.avDB.newRecord('sa_invoice_tender_trans');
            rTT.sa_cash_receipt_id = sa_cash_receipt_id;
            rTT.tender_amount = rCROrdPayment.crop_payment_amount;
            rTT.tender_trans_date = application.getTimeStamp();
            rTT.paymethod_id = paymethod_id;
            rTT.ordrevhdep_id = rDeposit.ordrevhdep_id;
            rTT.tender_amount_received = rCROrdPayment.crop_payment_amount;
            rTT.tender_change_amount = 0;
            rTT.tender_added_thru_cr = 1;

            databaseManager.saveData(rTT);
        }
	}
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * @param {Boolean} bHideWarning
 *
 * @return
 * @properties={typeid:24,uuid:"F3AF017D-1035-4D0A-8277-90D7C702C6C1"}
 */
function dc_delete(_event, _triggerForm, bHideWarning) {
	if(scopes.avAccounting.isCashReceiptsPeriodClosedForDate(sa_cash_receipt_date,org_id)){
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'),i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'),i18n.getI18NMessage('avanti.dialog.ok'));
		return i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod');
	}
	
    if (sa_cash_receipt_status == 'P') {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.deleteNotification'), i18n.getI18NMessage('avanti.dialog.deletePostedCashReceipt_msg'), i18n.getI18NMessage('avanti.dialog.ok'));
    }
    else if (inv_id) {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.deleteNotification'), i18n.getI18NMessage('avanti.dialog.deleteInvoicedCashReceipt_msg'), i18n.getI18NMessage('avanti.dialog.ok'))
    }
    else if (Boolean(foundset.sa_cash_receipt_auto_apply)) {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.deleteNotification'), i18n.getI18NMessage('avanti.dialog.deleteAutoApplyCashReceipt_msg'), i18n.getI18NMessage('avanti.dialog.ok'));
    }
    else {
        forms.sa_cash_receipt_invoices_dtl_tbl.loadRecords();
        forms.sa_cash_receipt_invoices_dtl_tbl.clearAll();
        
        deleteSalesOrderDeposit();
        
        return _super.dc_delete(_event, _triggerForm, bHideWarning);
    }

	return '';
}

/**
 * @properties={typeid:24,uuid:"19F94DFA-9B3C-45BB-97F2-80584E6773F7"}
 */
function deleteSalesOrderDeposit() {
    if (utils.hasRecords(forms.sa_cash_receipt_dtl.sa_cash_receipt_to_sa_order_revh_deposit)) {

        /**@type {JSRecord<db:/avanti/sa_order_revh_deposit>} */
        var rSOD = forms.sa_cash_receipt_dtl.sa_cash_receipt_to_sa_order_revh_deposit.getRecord(1);

        rSOD.sa_order_revh_deposit_to_sa_order_revision_header.ordrevh_deposit_total -= rSOD.ordrevhdep_amount;

        forms.sa_cash_receipt_dtl.sa_cash_receipt_to_sa_order_revh_deposit.deleteRecord(rSOD);
    }
}

/**
 * Manually Apply Deposits
 *
 * <AUTHOR> Dol
 * @since Feb 26, 2019
 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceipt
 * @public
 *
 * @properties={typeid:24,uuid:"82ED019E-6AA1-43B8-8E6C-12F4A8AF92FC"}
 */
function applyDeposits(rCashReceipt) {
    
    if (!rCashReceipt) {
        return;
    }
    //Only delete cash receipts applied to invoiced and transfers.
    rCashReceipt.sa_cash_receipt_to_sa_customer_deposit_apply$invoices.deleteAllRecords();
    rCashReceipt.sa_cash_receipt_to_sa_customer_deposit_apply$transfers.deleteAllRecords();
    
    rCashReceipt.sa_cash_receipt_to_sa_customer_postage_apply$invoices.deleteAllRecords();
    rCashReceipt.sa_cash_receipt_to_sa_customer_postage_apply$transfers.deleteAllRecords();

    if (utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_payment_method)) {
        switch (rCashReceipt.sa_cash_receipt_to_sa_payment_method.paymethod_type) {
            case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.PostageEscrowAccount:
                applyPostageDeposit(rCashReceipt);
                break;
                
            case scopes.avUtils.ENUM_PAYMENT_METHOD_TYPE.CustomerDepositAccount:
                applyCustomerDeposit(rCashReceipt);
                break;
        }
    }
}

/**
 * Apply Postage Deposit
 *
 * <AUTHOR> Dol
 * @since Feb 26, 2019
 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceipt
 * @private
 *
 * @properties={typeid:24,uuid:"43F8B52B-2ADA-4290-B9DD-3793A284EE9D"}
 */
function applyPostageDeposit(rCashReceipt) {
    if (rCashReceipt 
            && utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_customer) 
            && utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_customer_postage)) {
        
        var rPostage = rCashReceipt.sa_cash_receipt_to_sa_customer_postage.getRecord(1);
        
        /**@type {JSRecord<db:/avanti/sa_customer_postage_apply>} */
        var rPostageApply;
        
        if (rCashReceipt.sa_cash_receipt_transfer_cda == 1 && rCashReceipt.sa_cash_receipt_amount > 0) {
          //Create the apply, basically a transfer
            rPostageApply = rPostage.sa_customer_postage_to_sa_customer_postage_apply.getRecord(rPostage.sa_customer_postage_to_sa_customer_postage_apply.newRecord());
            rPostageApply.cust_id = rPostage.cust_id;
            rPostageApply.custpostapply_amount = rCashReceipt.sa_cash_receipt_amount;
            rPostageApply.sa_cash_receipt_id = rCashReceipt.sa_cash_receipt_id;
            rPostageApply.custpostapply_reference = scopes.avUtils.ENUM_DEPOSIT_APPLY_REF_TYPE.DepositTransfer; //Deposit transfer
            rPostage.balance_at_time -= rCashReceipt.sa_cash_receipt_amount;
            
            if (scopes.avUtils.trackCurrencyExchange(rCashReceipt.curr_id)) {
            	rPostageApply.exchange_rate = rPostage.exchange_rate;
            	rPostageApply.custpostapply_originating_amount = rCashReceipt.sa_cash_receipt_amount_exchanged;
            	rPostage.originating_balance_at_time = globals["avUtilities_roundNumber"](rPostage.originating_balance_at_time - rCashReceipt.sa_cash_receipt_amount_exchanged, 2);
            }

            databaseManager.saveData(rPostage);
            databaseManager.saveData(rPostageApply);
        }
        else {
            for (var i = 1; i <= rCashReceipt.sa_cash_receipt_to_sa_cash_receipt_detail.getSize(); i++) {
                var rCashReceiptDetail = rCashReceipt.sa_cash_receipt_to_sa_cash_receipt_detail.getRecord(i);

                if (rCashReceiptDetail.invoice_payment_amount > 0) {
                    //Create the apply
                    rPostageApply = rPostage.sa_customer_postage_to_sa_customer_postage_apply.getRecord(rPostage.sa_customer_postage_to_sa_customer_postage_apply.newRecord());
                    rPostageApply.cust_id = rPostage.cust_id;
                    rPostageApply.sa_cash_receipt_detail_id = rCashReceiptDetail.sa_cash_receipt_detail_id;
                    rPostageApply.inv_id = rCashReceiptDetail.inv_id;
                    rPostageApply.custpostapply_amount = rCashReceiptDetail.invoice_payment_amount;
                    rPostageApply.sa_cash_receipt_id = rCashReceipt.sa_cash_receipt_id;
                    rPostageApply.custpostapply_reference = scopes.avUtils.ENUM_DEPOSIT_APPLY_REF_TYPE.CashReceiptInvoice; //Deposit applied at cash receipt
                    rPostage.balance_at_time -= rCashReceiptDetail.invoice_payment_amount;
                    
	                if (scopes.avUtils.trackCurrencyExchange(rCashReceipt.curr_id)) {
	                	rPostageApply.exchange_rate = rPostage.exchange_rate;
	                	rPostageApply.custpostapply_originating_amount = rCashReceiptDetail.invoice_payment_amount_exchanged;
	                	rPostage.originating_balance_at_time = globals["avUtilities_roundNumber"](rPostage.originating_balance_at_time - rCashReceiptDetail.invoice_payment_amount_exchanged, 2);
	                }

                    databaseManager.saveData(rPostage);
                    databaseManager.saveData(rPostageApply);
                }
            }
        }
    }
}

/**
 * Apply Customer Deposit
 *
 * <AUTHOR> Dol
 * @since Feb 26, 2019
 * @param {JSRecord<db:/avanti/sa_cash_receipt>} rCashReceipt
 * @private
 *
 * @properties={typeid:24,uuid:"242E6CC8-DABF-4DA4-9CC4-460F8B5FAE7A"}
 */
function applyCustomerDeposit(rCashReceipt) {
    if (rCashReceipt 
            && utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_customer) 
            && utils.hasRecords(rCashReceipt.sa_cash_receipt_to_sa_customer_deposit)) {

        var rDeposit = rCashReceipt.sa_cash_receipt_to_sa_customer_deposit.getRecord(1);

        for (var i = 1; i <= rCashReceipt.sa_cash_receipt_to_sa_cash_receipt_detail.getSize(); i++) {
            var rCashReceiptDetail = rCashReceipt.sa_cash_receipt_to_sa_cash_receipt_detail.getRecord(i);

            if (rCashReceiptDetail.invoice_payment_amount > 0) {
                //Create the apply
                var rDepositApply = rDeposit.sa_customer_deposit_to_sa_customer_deposit_apply.getRecord(rDeposit.sa_customer_deposit_to_sa_customer_deposit_apply.newRecord());
                rDepositApply.cust_id = rDeposit.cust_id;
                rDepositApply.sa_cash_receipt_detail_id = rCashReceiptDetail.sa_cash_receipt_detail_id;
                rDepositApply.inv_id = rCashReceiptDetail.inv_id;
                rDepositApply.custdepapply_amount = rCashReceiptDetail.invoice_payment_amount;
                rDepositApply.sa_cash_receipt_id = rCashReceipt.sa_cash_receipt_id;
                rDepositApply.custdepapply_reference = scopes.avUtils.ENUM_DEPOSIT_APPLY_REF_TYPE.CashReceiptInvoice; // Deposit applied from cash receipt
                rDeposit.balance_at_time -= rCashReceiptDetail.invoice_payment_amount;
                    
                if (scopes.avUtils.trackCurrencyExchange(rCashReceipt.curr_id)) {
                	rDepositApply.exchange_rate = rDeposit.exchange_rate;
                	rDepositApply.custdepapply_originating_amount = rCashReceiptDetail.invoice_payment_amount_exchanged;
                	rDeposit.originating_balance_at_time = globals["avUtilities_roundNumber"](rDeposit.originating_balance_at_time - rCashReceiptDetail.invoice_payment_amount_exchanged, 2);
                }

                databaseManager.saveData(rDeposit);
                databaseManager.saveData(rDepositApply);
            }
        }
    }
}

/**
*
* @param {JSFoundSet} _foundset
*
 * @return
* @properties={typeid:24,uuid:"FD9F9557-767D-45E6-B476-5D034680C935"}
*/
function dc_save_post(_foundset) {
   applyDeposits(foundset.getSelectedRecord());
   
   // SL-16953: if applicable, force register review if cash receipt saved in the ready to be posted status.
   if (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.ReadyToBePosted 
		   && utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_register)
		   && sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_date_reviewed) {
	   sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_date_reviewed = null;
	   sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_reviewed_by_id = null;
   }
   
   // SL-16953: If cash receipt status is on hold and linked to a register, remove the link and force a register refresh.
   if (sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.OnHold 
   			&& utils.hasRecords(sa_cash_receipt_to_sa_cash_receipt_register)) {
	   sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_date_reviewed = null;
	   sa_cash_receipt_to_sa_cash_receipt_register.sacr_reg_reviewed_by_id = null;
	   sacr_reg_id = null;
   }
   
   return _super.dc_save_post(_foundset);
}

/**
 * refreshUI
*
*
* @properties={typeid:24,uuid:"0080B21F-8397-4CF3-9A20-F1E4331883F4"}
*/
function refreshUI() {
	application.executeLater(setToolBarOptions,500);
}

/**
 * setToolBarOptions
 *
 * @properties={typeid:24,uuid:"E135071D-04FB-4448-A00C-1D64E4E9E45E"}
 */
function setToolBarOptions() {
    if (scopes.avUtils.isNavModeReadOnly()) {
        if (sa_cash_receipt_id && sa_cash_receipt_status == scopes.avUtils.ENUM_CASH_RECEIPT_STATUS.Posted) {
			forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = false;
			forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = false;
			if (globals.nav.program[globals.nav_program_name]) {
				globals.nav.program[globals.nav_program_name].delete_mode = 0;
				globals.nav.program[globals.nav_program_name].update_mode = 0;
			}
		}
		else {
			forms.svy_nav_fr_buttonbar_browser.elements.btn_edit.enabled = true;
			forms.svy_nav_fr_buttonbar_browser.elements.btn_delete.enabled = true;
			if (globals.nav.program[globals.nav_program_name]) {
				globals.nav.program[globals.nav_program_name].delete_mode = 1;
				globals.nav.program[globals.nav_program_name].update_mode = 1;
			}
		}
	}
}
