/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D8223394-02AF-4434-AF91-1B3EFAF864FC",variableType:4}
 */
var _showRollDetails = 1;

/**
 * @type {Date}
 * @properties={typeid:35,uuid:"CF421953-8BD3-48DC-9455-BE7F044EED90",variableType:93}
 */
var fromDate = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"870B69D5-F055-4E22-97E8-AD81774584D2",variableType:4}
 */
var _onHandOnly = 1;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A283E603-0C8F-4C2E-82B0-3CFE84293CE1",variableType:4}
 */
var _onlyInvoicedandPostedShipments = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"9B4F56AE-AB0D-46DB-A596-98D6994F5F1D",variableType:4}
 */
var _onSelectAllWarehouses = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"9691A39C-600B-4BEE-A323-2C54716C2F42",variableType:4}
 */
var _includeAllItemClass = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7ABFCFE3-2994-4971-903E-3F0C137D85B1"}
 */
var _toItemClass = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"6434D543-3603-4801-A363-040F344C9E22"}
 */
var _fromItemClass = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"804E2163-A737-46B2-9EF1-00DEBCF897E7",variableType:4}
 */
var _includeAllItems = 1;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E54B54B6-5835-4549-B795-EB53767AF7D7"}
 */
var _toItem = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D3A6AB58-799D-343F-AD3F-C3BCB0B40B0F"}
 */
var _fromItem = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0386F992-A333-4DDB-BB51-BB2F5FBAE407"}
 */
var _fromWarehouse = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"8C16814C-3720-42F7-B421-85CDFB7D59AE",variableType:4}
 */
var _includeAllItemGroups = 1;

/**
 * @properties={typeid:35,uuid:"FDBC06A6-BF7D-4D7C-A78C-52C1484926F5",variableType:-4}
 */
var _fromItemGroup = null;

/**
 * @properties={typeid:35,uuid:"6ACF41BA-2856-4186-959B-05B0979D549D",variableType:-4}
 */
var _toItemGroup = null;

/**
 * @properties={typeid:35,uuid:"11C06005-F029-46DE-9881-45CDEB83E2CB",variableType:-4}
 */
var _bProfileModified = false;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"D250F146-0EFC-4A7E-888F-53E7A143060C",variableType:-4}
 */
var _selectedItemGroupArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"062239D2-DA9B-47D5-8ACC-0031D4CC5B62",variableType:4}
 */
var _includeSelectedItemClassCode = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"5C2F5496-2A58-4C7A-9213-3244F4D7FED5",variableType:4}
 */
var _includeSelectedItem = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3B7C5C66-37BE-4CEB-92F4-67F4D491C69F",variableType:4}
 */
var _includeSelectedItemGroups = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"BDDD6DB3-7656-4F4B-9707-BDC4BC5A814C",variableType:4}
 */
var includeSelectedItemClasses = 0;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"80733DCE-7A8B-4B0A-9268-E6ED734FF8B7",variableType:-4}
 */
var _selectedItemClassArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"7811EDAD-3E78-45AA-9754-8DB946E833C4",variableType:4}
 */
var includeSelectedItems = 0;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"28503CF0-4DD3-413B-8C5F-5B6B43B434CB",variableType:-4}
 */
var _selectedItemArray = new Array();

/**
 * @type {String}
 *
 * 
 *
 * @properties={typeid:35,uuid:"2C87C006-6CB1-474A-A882-CE273B0C7123"}
 */
var fromPlant = "";

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"B61A7ACB-3DDD-4127-B03C-39DEC368C569"}
 */
var fromWhse = "";

/**
 * 
 *
 * @properties={typeid:35,uuid:"798D342C-1F29-40D3-981A-BA9B06736BEA",variableType:-4}
 */
var _aPlantsReturn = [];

/**
 * 
 *
 * @properties={typeid:35,uuid:"A2142151-D2FD-4B8A-8C57-B9EC38B59B80",variableType:-4}
 */
var _aPlantsDisplay = [];

/**
 * @properties={typeid:35,uuid:"AE66EC65-C5D5-47A4-9D72-A1CE2EA53266",variableType:-4}
 */
var _aDivsReturn = [];

/**
 * @properties={typeid:35,uuid:"B4DA4A75-6EDC-43D4-85DB-45494D449589",variableType:-4}
 */
var _aDivsDisplay = [];

/**
 * @properties={typeid:35,uuid:"6B0FCB18-5BCC-4927-84E0-D1948961B7D0",variableType:-4}
 */
var _aWhsesReturn = [];

/**
 * @properties={typeid:35,uuid:"5F87201A-A5EF-423F-A2D0-6B39721B4D82",variableType:-4}
 */
var _aWhsesDisplay = [];

/**
 * @type {String}
 *
 * 
 *
 * @properties={typeid:35,uuid:"6F9DBA0C-6F0F-4631-AE90-4C7CF80048F2"}
 */
var fromDiv = "";

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"682D7BE2-F5DC-435C-8DCF-1A5FC05D4933",variableType:8}
 */
var includeAllParentCustomers = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"999DE428-441E-4D17-8D06-45D0CF40034E",variableType:8}
 */
var includeSelectedParentCustomers = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"8619904E-1FF1-441F-BB21-5C199C76BC57"}
 */
var fromParentCustomer = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"1E994015-B338-405F-8515-DB25ED224BF5"}
 */
var toParentCustomer = null;

/**
*
* @type {Array}
*
*
* @properties={typeid:35,uuid:"48FF4B80-F7F4-471D-B93F-40F313C1425C",variableType:-4}
*/
var _selectedParentCustomerArray = new Array();

/**
 * @type {Number}
 * 
 *
 * @properties={typeid:35,uuid:"88316EC3-8143-4858-B457-621571523E8E",variableType:8}
 */
var includeAllCustomerParts = 1;

/**
 * @type {Number}
 *
 *
 * @properties={typeid:35,uuid:"681C6B14-1AEF-4D8A-BB16-64BAA959B381",variableType:8}
 */
var includeSelectedCustomerParts = 0;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"CB1CFF1A-64BD-4052-825C-E7DE021B162C"}
 */
var fromCustomerParts = null;

/**
 * @type {String}
 *
 *
 * @properties={typeid:35,uuid:"2AD689CC-1BA8-4E62-AA70-91A038248D09"}
 */
var toCustomerParts = null;

/**
*
* @type {Array}
*
*
 * @properties={typeid:35,uuid:"61DAE021-9E71-48AD-8EB2-68285394E055",variableType:-4}
 */
var _selectedCustomerPartsArray = new Array();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"F348C06E-7A1C-450E-98A6-291077A00185",variableType:4}
 */
var _showInReportParentCustomer = 1;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3753CBEC-3D9A-4467-BFD7-8FFC1E492466",variableType:4}
 */
var _showInReportCustomerParts = 1;

/**
 * @properties={typeid:35,uuid:"D2A261B3-847F-4604-959A-58B88A06FDBF",variableType:-4}
 */
var fromParentCustomerCode = null;

/**
 * @properties={typeid:35,uuid:"B1A371F5-C5F2-4955-B771-A510C88DBE91",variableType:-4}
 */
var toParentCustomerCode = null;

/**
 * Standard Method for getting the Parameters from this filter form
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-08-14
 *
 * @returns {{aParamNames: Array, aParamValues: Array, whereClause: String}} Returns an object containing the param names you need and values
 *
 *
 * @properties={typeid:24,uuid:"31ABC92B-8904-4C8C-A0A3-0D2A126F80A6"}
 */
function getFilterParams()
{
	var oParams = new Object();
	var sPPlants = "";
	var sPWhse = "";
	
	scopes.globals.avBase_rptInventorySimpliedWithRolls = 0;
	

	var sWhere = " HAVING i.org_id = '" + globals.org_id + "'";
	var sNewWhere = " HAVING org_id = '" + globals.org_id + "'";
	
    //preparing for query - Item Groups
	var sPSelectedItemGroupsSelection ="";
	for (var i = 0; i <= _selectedItemGroupArray.length - 1; i++) {
		if (i != 0) {
			sPSelectedItemGroupsSelection += ',';
		}
		sPSelectedItemGroupsSelection += "'" + _selectedItemGroupArray[i] + "'";
	}
	
	//preparing for query - Item Class
	var sPSelectedItemClassSelection ="";
	for (var j = 0; j <= _selectedItemClassArray.length - 1; j++) {
		if (j != 0) {
			sPSelectedItemClassSelection += ',';
		}
		sPSelectedItemClassSelection += "'" + _selectedItemClassArray[j] + "'";
	}
    
	//preparing for query - Item Class
	var sPSelectedItemSelection ="";
	for (var k = 0; k <= _selectedItemArray.length - 1; k++) {
		if (k != 0) {
			sPSelectedItemSelection += ',';
		}
		sPSelectedItemSelection += "'" + _selectedItemArray[k] + "'";
	}
	
	
    var fromDateParam = null;
	
	if (_selectedItemClassArray.length > 0) {
		sWhere += " AND i.itemclass_id IN (" + sPSelectedItemClassSelection + ") ";
		sNewWhere += " AND itemclass_id IN (" + sPSelectedItemClassSelection + ") ";
	}
	else {
		if (_fromItemClass != null)  {
			sWhere += " AND ic.itemclass_code >= '" + _fromItemClass + "'";
			sNewWhere += " AND itemclass_code >= '" + _fromItemClass + "'";
		}
		if (_toItemClass != null) {
			sWhere += " AND ic.itemclass_code <= '" + _toItemClass + "'";
			sNewWhere += " AND itemclass_code <= '" + _toItemClass + "'";
		}
	}
	
	if (_selectedItemArray.length > 0) {
		sWhere += " AND i.item_id IN (" + sPSelectedItemSelection + ") ";
		sNewWhere += " AND Main.item_id IN (" + sPSelectedItemSelection + ") ";
	} 
	else {
		if (_fromItem != null) {
			sWhere += " AND i.item_code >= '" + _fromItem + "'";
			sNewWhere += " AND item_code >= '" + _fromItem + "'";
		}
		if (_toItem != null) {
			sWhere += " AND i.item_code <= '" + _toItem + "'";
			sNewWhere += " AND item_code <= '" + _toItem + "'";
		}
	}
	
	if (_selectedItemGroupArray.length > 0) {
		sWhere += " AND grp.ingroup_id IN (" + sPSelectedItemGroupsSelection + ") ";
		sNewWhere += " AND ingroup_id IN (" + sPSelectedItemGroupsSelection + ") ";
	} 
	else {
		if (_fromItemGroup != null) {
			sWhere += " AND grp.ingroup_code >= '" + _fromItemGroup + "'";
			sNewWhere += " AND ingroup_code >= '" + _fromItemGroup + "'";
		}
		if (_toItemGroup != null) {
			sWhere += " AND grp.ingroup_code <= '" + _toItemGroup + "'";
			sNewWhere += " AND ingroup_code <= '" + _toItemGroup + "'";
		}
	}

	if (_onHandOnly == 1) {
		sWhere += " AND iiw.itemwhse_openingbal_qty + SUM(CASE WHEN itt.intranstype_adjustment_type = 'Q' THEN ISNULL(id.itemtransd_qty,0) ELSE 0 END) != 0 ";
		
		if (_onlyInvoicedandPostedShipments == 1) {
			sNewWhere +=  " AND ROUND(MAX(itemwhse_openingbal_qty) + SUM(CASE WHEN intranstype_adjustment_type = 'Q' THEN ISNULL(itemtransd_qty,0) - ISNULL(qty_shipped,0)  ELSE 0 END),MAX(item_decimal_places)) != 0 ";
		}
		else {
			sNewWhere +=  " AND ROUND(MAX(itemwhse_openingbal_qty) + SUM(CASE WHEN intranstype_adjustment_type = 'Q' THEN ISNULL(itemtransd_qty,0) ELSE 0 END),MAX(item_decimal_places)) != 0 ";	
		}
		
		
	}
	
	// Preparing for Parent Customer
	var sPSelectedParentCustomerSelection = "";
	for (var l = 0; l <= _selectedParentCustomerArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedParentCustomerSelection += ',';
		}
		sPSelectedParentCustomerSelection += "'" + _selectedParentCustomerArray[k] + "'";
	}
	
	if (_selectedParentCustomerArray.length > 0 && globals.avBase_selectedParentCustomersViewOption == 0) {
		sWhere += " AND parent_cust_id IN (" + sPSelectedParentCustomerSelection + ") ";
		sNewWhere += " AND parent_cust_id IN (" + sPSelectedParentCustomerSelection + ") ";
	}
	else {
		if (fromParentCustomer != null) sWhere += " AND parent_cust_id >= '" + fromParentCustomer + "' ";
		if (toParentCustomer != null) sWhere += " AND parent_cust_id <= '" + toParentCustomer + "' ";
	}

	//Preparing for Customer Part
	var sPSelectedCustomerPartsSelection = "";
	for (l = 0; l <= _selectedCustomerPartsArray.length - 1; l++) {
		if (l != 0) {
			sPSelectedCustomerPartsSelection += ',';
		}
		sPSelectedCustomerPartsSelection += "'" + _selectedCustomerPartsArray[l] + "'";
	}
	
	if (_selectedCustomerPartsArray.length > 0 && globals.avBase_selectedCustomerPartsViewOption == 0) {
		sWhere += " AND item_cust_part_number IN (SELECT item_cust_part_number FROM in_item WHERE item_id IN( " + sPSelectedCustomerPartsSelection + ")) ";
		sNewWhere += " AND item_cust_part_number IN (SELECT item_cust_part_number FROM in_item WHERE item_id IN( " + sPSelectedCustomerPartsSelection + ")) ";
	}
	else {
		if (fromCustomerParts != null) {
			sWhere += " AND item_cust_part_number >= '" + fromCustomerParts + "' ";
			sNewWhere += " AND item_cust_part_number >= '" + fromCustomerParts + "' ";
		}
		if (toCustomerParts != null) {
			sWhere += " AND item_cust_part_number <= '" + toCustomerParts + "' ";	
			sNewWhere += " AND item_cust_part_number <= '" + toCustomerParts + "' ";
		}
	}

	
	if (fromDate) {
		fromDateParam = plugins.DateUtils.dateFormat(fromDate, 'yyyy-MM-dd');
	}
	
	
	if (fromPlant) {
		var aFromPlants = fromPlant.split('\n');
		for (i = 0; i <= aFromPlants.length - 1; i++) {
			if (i != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + aFromPlants[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aPlantsReturn.length - 1; j++) {
			if (j != 0) {
				sPPlants += ',';
			}
			sPPlants += "'" + _aPlantsReturn[j] + "'";
		}		
	}
	
	if (sPPlants) {
		sWhere += " AND plant.plant_id IN (" + sPPlants + ") ";
		sNewWhere += " AND plant_id IN (" + sPPlants + ") ";
	}
	
	if (fromWhse) {
		var aFromWhses = fromWhse.split('\n');
		for (i = 0; i <= aFromWhses.length - 1; i++) {
			if (i != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + aFromWhses[i] + "'";
		}
	} 
	else {
		for (j = 0; j <= _aWhsesReturn.length - 1; j++) {
			if (j != 0) {
				sPWhse += ',';
			}
			sPWhse += "'" + _aWhsesReturn[j] + "'";
		}		
	}
	
	
	
	if (sPWhse) {
		sWhere += " AND (iw1.whse_id IN (" + sPWhse + ")  OR iw.whse_id IN (" + sPWhse + ")) ";
		sNewWhere += " AND (whse_id IN (" + sPWhse + ")) ";
	}
	
	if (_showRollDetails == 1){
		scopes.globals.avBase_rptInventorySimplifiedWithRolls = 1;
	}
	else {
		scopes.globals.avBase_rptInventorySimplifiedWithRolls = 0;
	}
	
	oParams.aParamNames = ["pFromItem","pToItem","pFromWarehouse","pFromItemClass","pToItemClass","whereClause","pFromDate", "pShowRollDetails", "pFromItemGroup", "pToItemGroup", "pSelectedItemGroups", "pSelectedItemClass", "pSelectedItems", "whereNewClause","pShowCustomerParts", "pShowParentCustomers","pOnlyInvoiced"];
	oParams.aParamValues = [_fromItem, _toItem,_fromWarehouse,_fromItemClass,_toItemClass,sWhere,fromDateParam, _showRollDetails, _fromItemGroup, _toItemGroup, sPSelectedItemGroupsSelection, sPSelectedItemClassSelection, sPSelectedItemSelection, sNewWhere, _showInReportCustomerParts, _showInReportParentCustomer, _onlyInvoicedandPostedShipments];
	
	return oParams;
}

/** *
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"75F1788A-FF94-4145-BDED-C27D8AC73992"}
 */
function onShow(firstShow, event) {
	 _super.onShow(firstShow, event)
	 
	 globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	 
	 _includeAllItems = 1;
	 _includeSelectedItem = 0;
	 _fromItem = null;
	 _toItem = null;
	 _selectedItemArray = new Array();
	 includeSelectedItems = 0;
	 
	 _includeAllItemClass = 1;
	 _includeSelectedItemClassCode = 0;
	 _fromItemClass = null;
	 _toItemClass = null;
	 _selectedItemClassArray = new Array();
	 includeSelectedItemClasses = 0
	 
	 _includeAllItemGroups = 1;
	 _includeSelectedItemGroups = 0;
	 _fromItemGroup = null;
	 _toItemGroup = null;
	 _selectedItemGroupArray = new Array();
	 
	 
	 _onSelectAllWarehouses = 0;
	 elements.fromWhse.enabled = true;
	 _fromWarehouse = _to_in_warehouse$avbase_employeedefaultwarehouse.whse_code;
	 _showRollDetails = 1;
	 scopes.globals.avBase_rptInventorySimpliedWithRolls = _showRollDetails;
	 
	 globals.avBase_selectedParentCustomersViewOption = 0;
	 _showInReportParentCustomer = 1;
	 includeAllParentCustomers = 1;
	 includeSelectedParentCustomers = 0;
	 fromParentCustomer = null;
	 fromParentCustomerCode = null;
	 toParentCustomer = null;
	 toParentCustomerCode = null;
	 _selectedParentCustomerArray = new Array();
	 
	 
	 globals.avBase_selectedCustomerPartsViewOption = 0;
	 _showInReportCustomerParts = 1;
	 includeAllCustomerParts = 1;
	 includeSelectedCustomerParts = 0;
	 fromCustomerParts = null;
	 toCustomerParts = null;
	 _selectedCustomerPartsArray = new Array();
	 
	 fromDate = application.getServerTimeStamp();
	  elements.fromDate.format = globals.avBase_dateFormat;
	
	 refreshUI();
	 load_vl_DivisionshasWarehouses()
	 loadPlantsForDiv('From');
	 setDefaultDivisionFilter();
	 loadWarehousesForPlant('From');
	 
	 _onlyInvoicedandPostedShipments = 0;
	 
	 
	 
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"24CF30CA-3BBF-479D-8AC9-60C127718815"}
 */
function onDataChangeFromDate(oldValue, newValue, event) {
    if (newValue > application.getServerTimeStamp()) {
        globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidDate', i18n.getI18NMessage('avanti.dialog.ok'));
    }
    return true
}

/**
 * 
 * @properties={typeid:24,uuid:"1BC403B6-24DF-45E7-9205-03A7BE32DB05"}
 */
function refreshUI()
{
	if (_includeAllItemClass == 1)
	{
		elements._fromItemClass.enabled = false;
		elements._toItemClass.enabled = false;
		elements.btnLookupItemClass_From.enabled = false;
		elements.btnLookupItemClass_To.enabled = false;
	}
	else
	{
		elements._fromItemClass.enabled = true;
		elements._fromItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements._toItemClass.enabled = true;
		elements.btnLookupItemClass_From.enabled = true;
		elements.btnLookupItemClass_To.enabled = true;
	}
	
	
	if (_includeAllItems == 1)
	{
		elements._fromItem.enabled = false;
		elements._toItem.enabled = false;
		elements.btnLookupItem_From.enabled = false;
		elements.btnLookupItem_To.enabled = false;
	}
	else
	{
		elements._fromItem.enabled = true;
		elements._fromItem.enabled = true;
		elements._toItem.enabled = true;
		elements._toItem.enabled = true;
		elements.btnLookupItem_From.enabled = true;
		elements.btnLookupItem_To.enabled = true;
	}
	

	elements._fromWarehouse.enabled = true;
	elements._fromWarehouse.enabled = true;
	elements.btnLookupWarehouse_From.enabled = true;
	
    //Item Group
    if (_includeAllItemGroups ==1 ||  _includeSelectedItemGroups == 1) {
        elements._fromItemGroup.enabled = false;
        elements._toItemGroup.enabled = false;
        elements.btnLookupItemGroup_From.enabled = false;
        elements.btnLookupItemGroup_To.enabled = false;
        
        if (_includeSelectedItemGroups == 1){
            elements._selectedItemGroupMessage_label.visible = true;
            _includeAllItemGroups = 0;
        }
    }
    else {
        elements._fromItemGroup.enabled = true;
        elements._toItemGroup.enabled = true;
        elements.btnLookupItemGroup_From.enabled = false;
        elements.btnLookupItemGroup_To.enabled = false;
        elements._selectedItemGroupMessage_label.visible = true;
        _includeSelectedItemGroups = 0;
        _includeAllItemGroups = 0;
        
    }
    
	 elements._selectedItemGroupMessage_label.text = 
     (_selectedItemGroupArray && _includeSelectedItemGroups == 1 ?
     "<html><a href='#'>(" + _selectedItemGroupArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemGroupsSelected") +") </a></html>" : null);
     
     //Item Class
     if (_includeAllItemClass == 1 || includeSelectedItemClasses == 1) {
         elements._fromItemClass.enabled = false;
         elements._toItemClass.enabled = false;
         elements.btnLookupItemClass_From.enabled = false;
         elements.btnLookupItemClass_To.enabled = false;
         
         if (includeSelectedItemClasses == 1){
             elements._selectedItemClassMessage_label.visible = true;
             _includeAllItemClass = 0;
         }
     }
     else {
         elements._fromItemClass.enabled = true;
         elements._toItemClass.enabled = true;
         elements.btnLookupItemClass_From.enabled = true;
         elements.btnLookupItemClass_To.enabled = true;
         elements._selectedItemClassMessage_label.visible = false;
     }
     
     elements._selectedItemClassMessage_label.text = 
         (_selectedItemClassArray && includeSelectedItemClasses == 1 ?
         "<html><a href='#'>(" + _selectedItemClassArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemClassesSelected") +") </a></html>" : null); 
	
       //Item 
         if (_includeAllItems == 1 || includeSelectedItems == 1) {
             elements._fromItem.enabled = false;
             elements._toItem.enabled = false;
             elements.btnLookupItem_From.enabled = false;
             elements.btnLookupItem_To.enabled = false;
             
             if (includeSelectedItems == 1){
                 elements._selectedItemMessage_label.visible = true;
                 _includeAllItems = 0;
             }
         }
         else {
             elements._fromItem.enabled = true;
             elements._toItem.enabled = true;
             elements.btnLookupItem_From.enabled = true;
             elements.btnLookupItem_To.enabled = true;
             elements._selectedItemMessage_label.visible = false;
         }
         
         elements._selectedItemMessage_label.text = 
             (_selectedItemArray && includeSelectedItems == 1 ?
             "<html><a href='#'>(" + _selectedItemArray.length + " " + i18n.getI18NMessage("avanti.lbl.itemsSelected") +") </a></html>" : null);         

           //Parent Customers
             bFlag = (includeAllParentCustomers == 1 || includeSelectedParentCustomers == 1 ? false : true);
             elements.fromParentCustomer.enabled = bFlag;
             elements.toParentCustomer.enabled = bFlag;
             elements.btnLookupFromParentCustomer.enabled = bFlag;
             elements.btnLookupToParentCustomer.enabled = bFlag;
             elements._selectedParentCustomerMessage_label.visible = !bFlag;
             
             elements._selectedParentCustomerMessage_label.text =
                 (_selectedParentCustomerArray && includeSelectedParentCustomers == 1 ?
                 "<html><a href='#'>(" + _selectedParentCustomerArray.length + " " +  i18n.getI18NMessage("avanti.lbl.parentCustomersSelected") +") </a></html>": null);
                 
                 
            // Customer Parts
             bFlag = (includeAllCustomerParts == 1 || includeSelectedCustomerParts == 1 ? false : true);
             elements.fromCustomerParts.enabled = bFlag;
             elements.toCustomerParts.enabled = bFlag;
             elements.btnLookupFromCustomerParts.enabled = bFlag;
             elements.btnLookupToCustomerParts.enabled = bFlag;
             elements._selectedCustomerPartsMessage_label.visible = !bFlag;
             
             elements._selectedCustomerPartsMessage_label.text =
                 (_selectedCustomerPartsArray && includeSelectedCustomerParts == 1 ?
                 "<html><a href='#'>(" + _selectedCustomerPartsArray.length + " " +  i18n.getI18NMessage("avanti.lbl.CustomerPartsSelected") +") </a></html>": null);     
             
             
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3F487A82-EE73-44FF-93D9-7F7AC9D7AA49"}
 */
function onDataChange_includeAllItemClasses(oldValue, newValue, event) {
	if(newValue == 1)
	{
		includeSelectedItemClasses = 0;
		_fromItemClass = null;
		_toItemClass = null;
		elements._selectedItemClassMessage_label.text = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"BCFD65A6-0A17-4C46-B60A-E82A020D103A"}
 */
function onDataChangeIncludeAllWarehouses(oldValue, newValue, event) {
	if(newValue == 1)
	{
		_fromWarehouse = null;
		//_toWarehouse = null;
	}
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"41C5ED8F-7D28-4085-A529-3DF5A6FCBAF6"}
 */
function onDataChangeIncludeAllItems(oldValue, newValue, event) {
	if(newValue == 1)
	{
		includeSelectedItems = 0;
	    elements._fromItem.enabled = false;
	    elements._toItem.enabled = false;
	    elements._selectedItemMessage_label.text = null;
	}

	_fromItem = null;
	_toItem = null;
	
	refreshUI();
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"28500A4E-2828-4A82-BB88-EEEA196889B5"}
 */
function onDataChange_fromItemClass(oldValue, newValue, event) {
	_toItemClass = _fromItemClass
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"A2DE3D3F-568A-4747-8318-670E867C1A6F"}
 */
function onDataChange_fromItem(oldValue, newValue, event) {
	_toItem = _fromItem
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"356AE854-A6C0-4605-A0B0-95F6F84D0020"}
 */
function onDataChange_toItemClass(oldValue, newValue, event) {
	if(_fromItemClass>_toItemClass)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"********-6E0D-494D-B1D9-FD2A12BA0E83"}
 */
function onDataChange_toItem(oldValue, newValue, event) {
	if(_fromItem>_toItem)
	{
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"9BDB18BF-9092-4D85-A234-461930E5761A"}
 */
function onDataChange_toWarehouse(oldValue, newValue, event) {
//	if(_fromWarehouse>_toWarehouse)
//	{
//		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
//	}
	return true
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_warehouse>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"********-BEC2-4D0E-A107-7B9489C00BC7"}
 */
function afterFromWarehouseLookup(record)
{
	if (record) _fromWarehouse = record.whse_code;
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"C96CC800-2E17-4BCB-B6B2-B268D3F58222"}
 */
function afterFromItemClassLookup(record)
{
	if (record) _fromItemClass = record.itemclass_code;
	onDataChange_fromItemClass(null,_fromItemClass,null)
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item_class>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"E63C253D-DF26-428F-BF67-9F15505A380E"}
 */
function afterToItemClassLookup(record)
{
	if (record) _toItemClass = record.itemclass_code;
}

/**
 * From Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"5FBAB0A4-F1C0-4F56-B614-3129FE620187"}
 */
function lookupFilter_ItemFrom(fs) 
{
	fs.addFoundSetFilterParam('item_code','>','^','PlannedPurchasing_FromItemLookupFilter');
	
	return fs;
}

/**
 * Get selected from supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"09EA407E-E4BE-439C-B90D-6A52C284813E"}
 */
function afterFromItemLookup(record)
{
	if (record) _fromItem = record.item_code;
	onDataChange_fromItem(null,_fromItem,null);
	
}

/**
 * To Item lookup filter
 * 
 * @param {JSFoundSet<db:/avanti/in_item>} fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 * @properties={typeid:24,uuid:"DE5FEF03-D47F-4739-94D5-4D90C5BBA706"}
 */
function lookupFilter_ItemTo(fs) 
{
	fs.addFoundSetFilterParam('item_code','>=',_fromItem,'PlannedPurchasing_ToItemLookupFilter');
	
	return fs;
}

/**
 * Get selected To supplier lookup result.
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"C4E5DFB6-7A14-49AD-BD36-F70C6975BF15"}
 */
function afterToItemLookup(record)
{
	if (record) _toItem = record.item_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"3A55E4DE-3579-43DE-81FE-9E21BB506951"}
 */
function onDataChange_fromItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;		
	}
	_toItemGroup = _fromItemGroup;
	refreshUI();
// 	elements._toItemGroup.requestFocus();
	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"269B9E70-3835-4489-BDBE-D74833EB5F1A"}
 */
function onDataChange_toItemGroup(oldValue, newValue, event) {
	if(oldValue != newValue){
		_bProfileModified = true;
		refreshUI();
	}
	return true;
}

/**
 * 
 * @param aSelectedItemGroups
 *
 * @properties={typeid:24,uuid:"55805118-7AC9-4DCA-8ADE-728D0A0BE1CA"}
 */
function setSelectedItemGroups(aSelectedItemGroups) {
	_selectedItemGroupArray = aSelectedItemGroups;
	refreshUI();
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"0CC666B7-691D-430D-94E1-EDBB17E13DE2"}
 */
function onAction_selectedItemGroup(event) {
	showSelectedItemGroupDialog();
}

/**
 * @properties={typeid:24,uuid:"D1BDD245-6206-409E-8687-8F1D8AC8DE92"}
 */
function showSelectedItemGroupDialog() {
    forms.sysDialog_selected_item_groups._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_groups._sCallBackMethod = "setSelectedItemGroups";
    forms.sysDialog_selected_item_groups._aSelectedItemGroups = _selectedItemGroupArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_groups, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemGroups"), true, false, "sysDialogSelectedItemGroups", true);
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"82068D3C-A2A8-4C84-9135-76091F044A3F"}
 */
function onIncludeAllItemGroupDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        _includeSelectedItemGroups = 0;
        elements._fromItemGroup.enabled = false;
        elements._toItemGroup.enabled = false;
        elements._selectedItemGroupMessage_label.text = null;
        elements._chkSelectedItemGroups
    }
    else {
    	 _includeSelectedItemGroups = 0;
         elements._fromItemGroup.enabled = true;
         elements._toItemGroup.enabled = true;
         elements._selectedItemGroupMessage_label.text = null;
    }
    
    _fromItemGroup = null;
    _toItemGroup = null;

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"3A0C73A6-18E4-4690-A60C-D25E30EBACB9"}
 */
function onIncludeSelectedItemGroupToDataChange(oldValue, newValue, event) {
	if (newValue == 1) {
        _includeAllItemGroups = 0;
        _fromItemGroup = null;
        _toItemGroup = null;
        showSelectedItemGroupDialog();
        elements._selectedItemGroupMessage_label.enabled = true;
    }
    else {
        elements._selectedItemGroupMessage_label.text = null;
        _selectedItemGroupArray = [];
        _includeAllItemGroups = 1;
    }

    refreshUI();
    return true;
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"D1A5810F-C0D7-4CD8-9D30-039A8A400F15"}
 */
function onIncludeSelectedItemClassDataChange(oldValue, newValue, event) {

    if (newValue == 1) {
        _includeAllItemClass = 0;
        _fromItemClass = null;
        _toItemClass = null;
        showSelectedItemClassDialog();
        elements._selectedItemClassMessage_label.enabled = true;
    }
    else {
        elements._selectedItemClassMessage_label.text = null;
        _selectedItemClassArray = [];
        _includeAllItemClass = 1;
    }

    refreshUI();
    
    return true;
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"CA6F12E2-1C9A-4EFC-8140-3B210E116EAC"}
 */
function onAction_selectedItemClasses(event) {
    showSelectedItemClassDialog();
    //updateUI();
}

/**
 * @properties={typeid:24,uuid:"3B063467-D025-4D7A-895D-57E709E1027B"}
 */
function showSelectedItemClassDialog() {
    forms.sysDialog_selected_item_classes._sCallBackForm = controller.getName();
    forms.sysDialog_selected_item_classes._sCallBackMethod = "setSelectedItemClasses";
    forms.sysDialog_selected_item_classes._aSelectedItemClasses = _selectedItemClassArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_item_classes, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItemClasses"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItemClasses
 *
 * @properties={typeid:24,uuid:"1B053B78-BAB4-4D7C-9A7D-BD2D684D0D61"}
 */
function setSelectedItemClasses(aSelectedItemClasses) {
    _selectedItemClassArray = aSelectedItemClasses;
    refreshUI();
}

// Item

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @return
 * @properties={typeid:24,uuid:"D67DF68B-14C2-4DBA-B927-A014C0DB6E69"}
 */
function onIncludeSelectedItemsDataChange(oldValue, newValue, event) {
	
    if (newValue == 1) {
        _includeAllItems = 0;
        _fromItem = null;
        _toItem = null;
        showSelectedItemDialog();
    }
    else {
        elements._selectedItemMessage_label.text = null;
        _selectedItemArray = [];
        _includeAllItems = 1;
    }

    refreshUI();
    return true;
}

/**
 * @properties={typeid:24,uuid:"B229C0A2-EC43-42DF-9BE9-61AADFAE180D"}
 */
function showSelectedItemDialog() {
    forms.sysDialog_selected_items._sCallBackForm = controller.getName();
    forms.sysDialog_selected_items._sCallBackMethod = "setSelectedItems";
    forms.sysDialog_selected_items._aSelectedItems = _selectedItemArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_items, -1, -1, 695, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedItems"), true, false, "sysDialogSelectedItems", true);
}

/**
 * 
 * @param aSelectedItems
 *
 * @properties={typeid:24,uuid:"6EDC279E-EFCD-4D7F-B349-92E41C0B7BB6"}
 */
function setSelectedItems(aSelectedItems) {
    _selectedItemArray = aSelectedItems;
    refreshUI();
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"CF0BCB0A-9A4A-4B7B-BC83-BE1456F18C23"}
 */
function onAction_selectedItems(event) {
    showSelectedItemDialog();
}

/**
 *
 * @properties={typeid:24,uuid:"85A5188D-5F4B-4189-B85B-FA65AE3DE2B3"}
 */
function setDefaultDivisionFilter() {
    
    elements.fromDiv.enabled = true;
    
	if (utils.hasRecords(_to_sys_division$org)) {
		var rDivision = _to_sys_division$org.getRecord(1);
		fromDiv = rDivision.div_id;
	}
}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * 
 *
 * @properties={typeid:24,uuid:"A34CB6F9-7FB0-489C-B22E-3B15632A4DCD"}
 */
function onDataChange_fromDiv (oldValue, newValue, event) {
	loadPlantsForDiv('From');
	onDataChange_fromPlant (oldValue, newValue, event);

}

/**
 * 
 * @param oldValue
 * @param newValue
 * @param event
 *
 * @properties={typeid:24,uuid:"8E3B5735-B289-4B8A-89F3-01D736066ADA"}
 */
function onDataChange_fromPlant (oldValue, newValue, event) {
	fromWhse = "";
	loadWarehousesForPlant('From');
}

/**
 * @properties={typeid:24,uuid:"BF09F23A-CA17-4A49-8FF0-0B5A6B7A5ED2"}
 */
function load_vl_DivisionshasWarehouses() {

	_aDivsReturn = [];
	_aDivsDisplay = [];
	
	fromDiv = "";

	var iMax = 0,
		/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
		oSQL = { },
		/***@type {JSDataSet} ***/
		dsData;

	oSQL.sql = "SELECT DISTINCT CONCAT(div.div_code,': ',div.div_name) AS div , div.div_id \
			    FROM  sys_division AS div \
			    INNER JOIN in_warehouse AS whse ON whse.div_id = div.div_id \
			    WHERE div.org_id = ? \
			    AND div.div_id IN (SELECT div_id FROM sys_employee_div WHERE empl_id = ?) \
			    ORDER BY CONCAT(div.div_code,': ',div.div_name) ASC ";

	oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
	dsData = globals["avUtilities_sqlDataset"](oSQL);

	iMax = dsData.getMaxRowIndex();
	for (var i = 1; i <= iMax; i++) {
		_aDivsDisplay.push(dsData.getValue(i, 1));
		_aDivsReturn.push(dsData.getValue(i, 2));
	}

	application.setValueListItems("vl_DivisionshasWarehouses", _aDivsDisplay, _aDivsReturn);
}

/**
 * 
 * @param sDivType
 *
 * @properties={typeid:24,uuid:"BC9622B9-761F-4912-85D8-BD7199C5BE71"}
 */
function loadPlantsForDiv(sDivType) {

	var aFromDiv = [];
	var sFromDiv = "";

	_aPlantsReturn = [];
	_aPlantsDisplay = [];

	fromPlant = "";
	fromWhse = "";
	
	if (!fromDiv) {
		setDefaultDivisionFilter();
	}
		aFromDiv = fromDiv.split('\n');

	for (var i = 0; i <= aFromDiv.length - 1; i++) {
		if (i != 0) {
			sFromDiv += ',';
		}
		sFromDiv += "'" + aFromDiv[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";

		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
					INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
					WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
					AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ?) \
					ORDER BY div.div_code ASC, plant.plant_code ASC";
		
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	}
	iMax = dsData.getMaxRowIndex();
	
	if (iMax == 0) {
		oSQL.sql = "SELECT CONCAT(plant.plant_code,': ',plant.plant_name) AS plant, plant.plant_id, plant.sequence_nr FROM sys_plant AS plant  \
			INNER JOIN sys_division AS div ON div.div_id = plant.div_id \
			WHERE div.org_id = ? AND div.div_id IN ( " + sFromDiv + " ) \
			ORDER BY div.div_code ASC, plant.plant_code ASC";
	
			oSQL.args = [globals["org_id"]];
			dsData = globals["avUtilities_sqlDataset"](oSQL);
			iMax = dsData.getMaxRowIndex();
	}
	
	for (i = 1; i <= iMax; i++) {
		_aPlantsDisplay.push(dsData.getValue(i, 1));
		_aPlantsReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromPlant =  dsData.getValue(i, 2);
		}
	}
		
	application.setValueListItems("vl_Plants_rpt_dlg_base_on_div", _aPlantsDisplay, _aPlantsReturn);
}

/**
 * 
 * @param sPlantType
 *
 * @properties={typeid:24,uuid:"551BFC68-84C1-433C-8D81-54BA4C3A9602"}
 */
function loadWarehousesForPlant(sPlantType) {

	var aFromPlant = [];
	var sFromPlant = "";

	_aWhsesReturn = [];
	_aWhsesDisplay = [];

	
	aFromPlant = fromPlant.split('\n');

	for (var i = 0; i <= aFromPlant.length - 1; i++) {
		if (i != 0) {
			sFromPlant += ',';
		}
		sFromPlant += "'" + aFromPlant[i] + "'";

	}
		var iMax = 0,
			/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
			oSQL = { },
			/***@type {JSDataSet} ***/
			dsData;

	if ((globals.avBase_plantID != 'ALL' && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.DivPlantFilter)) ) {

	oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, div.div_id, plant.plant_id \
				 FROM in_warehouse AS whse \
					INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
					INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
				 WHERE div.org_id = ? \
					AND plant.plant_id IN ( " + sFromPlant + " ) \
				 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? AND plant_id = ?) \
				 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";	
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"], globals.avBase_plantID.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);

	} 
	else {
			oSQL.sql = "SELECT CONCAT(whse.whse_code,': ',whse.whse_desc) AS whse, whse.whse_id, plant.plant_id \
						 FROM in_warehouse AS whse \
							INNER JOIN sys_division AS div ON whse.div_id = div.div_id \
							INNER JOIN sys_plant AS plant ON whse.plant_id= plant.plant_id \
						 WHERE div.org_id = ? \
							AND plant.plant_id IN ( " + sFromPlant + " ) \
						 AND plant.plant_id IN (SELECT plant_id FROM sys_employee_plant WHERE empl_id = ? ) \
						 ORDER BY CONCAT(whse.whse_code,': ',whse.whse_desc) ASC";
		
		oSQL.args = [globals["org_id"], globals["avBase_employeeUUID"]];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		
	}
	iMax = dsData.getMaxRowIndex();
	for (i = 1; i <= iMax; i++) {
		_aWhsesDisplay.push(dsData.getValue(i, 1));
		_aWhsesReturn.push(dsData.getValue(i, 2));
		
		if (i==1) {
			fromWhse =  dsData.getValue(i, 2);
		}
		else if (i>1 && _onSelectAllWarehouses == 1) {
			fromWhse += "\n";
			fromWhse +=  dsData.getValue(i, 2);
			 
		}
	}
		
	application.setValueListItems("vl_Warehouses_rpt_dlg_base_on_plant", _aWhsesDisplay, _aWhsesReturn);
	
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7FB68681-7C45-4D3C-A1FC-B106D9F549C9"}
 */
function onAction_SelectAllWarehouses(event) {
	
	if (_onSelectAllWarehouses == 1) {
		elements.fromWhse.enabled = false;
  	    loadWarehousesForPlant('From');
	}
	else {
		 elements.fromWhse.enabled = true;
		 loadWarehousesForPlant('From');
	}
}

/**
 * @properties={typeid:24,uuid:"1E54F584-B31F-445C-9E75-3E6DE27C906C"}
 */
function showSelectedParentCustomerDialog() {
    forms.sysDialog_selected_parent_customers._sCallBackForm = controller.getName();
    forms.sysDialog_selected_parent_customers._sCallBackMethod = "setSelectedParentCustomers";
    forms.sysDialog_selected_parent_customers._aSelectedCustomers = _selectedParentCustomerArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_parent_customers, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.includeSelectedParentCustomers"), true, false, "sysDialogSelectedParentCustomer", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"D844DB45-B004-4B41-8FF1-B260ADF83EE8"}
 */
function onShowFromParentCustomerLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'cust_code', 'Customers', 'afterFromParentCustomerLookup', 'lookupFilter_Customers', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item_supplier>} _fs
 * @return {JSFoundSet<db:/avanti/in_item_supplier>}
 *
 *
 *
 * @properties={typeid:24,uuid:"91589E67-526E-4D49-BA38-5485F5FDEFFD"}
 */
function lookupFilter_Customers(_fs)
{
	//_fs.addFoundSetFilterParam('cust_parent_cust_id','!=',null,'LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"6C2F7226-1BA8-484B-BA72-0F1834FC9C2E"}
 */
function afterToParentCustomerLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"A7D7340E-0924-4EAE-9996-AB3541087D73"}
 */
function afterFromParentCustomerLookup(record) {
	if (record) fromParentCustomer = record.cust_code;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"A3C895AA-C286-44F8-BC09-E511F21890A3"}
 */
function onFromParentCustomerDataChange(oldValue, newValue, event) {
	if (!toParentCustomer) {
		toParentCustomer = fromParentCustomer;
	} else if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"45C85924-8836-43EB-AA55-329B829811D5"}
 */
function onToParentCustomerDataChange(oldValue, newValue, event) {
	if (fromParentCustomer > toParentCustomer) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}

	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"3154A64B-4D2A-4600-A396-A4EC0B7D2166"}
 */
function onIncludeAllParentCustomerDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        elements._selectedParentCustomerMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"7B716DC1-7F0F-4846-9FAE-FDD5FF3EA3B0"}
 */
function onIncludeSelectedParentCustomerDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedParentCustomersViewOption = 0;
	
    if (newValue == 1) {
        includeAllParentCustomers = 0;
        fromParentCustomer = null;
        toParentCustomer = null;
        showSelectedParentCustomerDialog();
    }
    else if (newValue == 0) {
    	includeAllParentCustomers = 1;
        fromParentCustomer = null;
        toParentCustomer = null;
        _selectedParentCustomerArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedParentCustomers
 * public
 *
 * @properties={typeid:24,uuid:"A1121DA1-D7E8-4F6C-A59E-A569DFC58E5A"}
 */
function setSelectedParentCustomers(aSelectedParentCustomers) {
    _selectedParentCustomerArray = aSelectedParentCustomers;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0F93DD4B-EA3F-42E2-8F9C-CD4D15E851A3"}
 */
function onAction_selectedParentCustomer(event) {
    showSelectedParentCustomerDialog();
}

/// FOR CUSTOMER PARTS ONLY

/**
 * 
 *  *
 * @properties={typeid:24,uuid:"9C5F5131-A0FB-43E2-B8C9-131827384642"}
 */
function showSelectedCustomerPartsDialog() {
    forms.sysDialog_selected_customers_parts._sCallBackForm = controller.getName();
    forms.sysDialog_selected_customers_parts._sCallBackMethod = "setSelectedCustomerParts";
    forms.sysDialog_selected_customers_parts._aSelectedCustomerParts = _selectedCustomerPartsArray;
    globals.DIALOGS.showFormInModalDialog(forms.sysDialog_selected_customers_parts, -1, -1, 600, 500, i18n.getI18NMessage("avanti.lbl.IncludeSelectedCustomerPartNo"), true, false, "sysDialogSelectedCustomerParts", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 * 
 *
 * @properties={typeid:24,uuid:"9F70CE98-0034-4EC8-9F3A-94A254E30DEF"}
 */
function onShowFromCustomerPartsLookupWin(event) {
	globals.svy_nav_showLookupWindow(event, 'item_cust_part_number', 'Items', 'afterFromCustomerPartsLookup', 'lookupFilter_CustomerParts', { mode: "rptlookup" });
}

/**
 * addLookupFilters
 * @param {JSFoundSet<db:/avanti/in_item>} _fs
 * @return {JSFoundSet<db:/avanti/in_item>}
 *
 *
 * @properties={typeid:24,uuid:"85F273B3-5289-4D3C-BA5F-6D151D0F2E20"}
 */
function lookupFilter_CustomerParts(_fs)
{
	_fs.addFoundSetFilterParam('item_cust_part_number','!=',null,'LookupFilter')
	_fs.addFoundSetFilterParam('item_cust_part_number','!=','','LookupFilter')
	return _fs;
}

/**
 * @param {JSRecord<db:/avanti/sa_customer>} record - the selected record
 *
 * @properties={typeid:24,uuid:"EC9E37B8-7C65-4F08-8DF4-C2AAE92D886D"}
 */
function afterToCustomerPartsLookup(record) {
	if (record) toParentCustomer = record.cust_code;
}

/**
 *
 * @param {JSRecord<db:/avanti/in_item>} record - the selected record
 *
 *
 * @properties={typeid:24,uuid:"3FE2B110-D0CB-48DC-9F43-74953BAE4867"}
 */
function afterFromCustomerPartsLookup(record) {
	if (record) fromCustomerParts = record.item_cust_part_number;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"4DEAC9AF-EB3D-4EB2-9BE5-B917877601DB"}
 */
function onFromCustomerPartsDataChange(oldValue, newValue, event) {
	if (!toCustomerParts) {
		toCustomerParts = fromCustomerParts;
	} else if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"53A53E94-5789-42E7-BBF7-6EAC4466F9F5"}
 */
function onToCustomerPartsDataChange(oldValue, newValue, event) {
	if (fromCustomerParts > toCustomerParts) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', 'i18n:svy.fr.lbl.ok');
	}


	return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 *
 * @properties={typeid:24,uuid:"6069086C-0AEA-40F2-B193-57E15E002C48"}
 */
function onIncludeAllCustomerPartsDataChange(oldValue, newValue, event) {
    if (newValue == 1) {
        includeSelectedCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        elements._selectedCustomerPartsMessage_label.text = null;
    }
    
    refreshUI();

    return true;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"9D5498B2-5EF2-4F4F-9BCF-3E7BCB80FBBC"}
 */
function onIncludeSelectedCustomerPartsDataChange(oldValue, newValue, event) {
	
	globals.avBase_selectedCustomerPartsViewOption = 0;
	
    if (newValue == 1) {
        includeAllCustomerParts = 0;
        fromCustomerParts = null;
        toCustomerParts = null;
        showSelectedCustomerPartsDialog();
    }
    else if (newValue == 0) {
    	includeAllCustomerParts = 1;
        fromCustomerParts = null;
        toCustomerParts = null;
        _selectedCustomerPartsArray = new Array();
    }
    
    refreshUI();
    return true;
}

/**
 *
 * @param {Array} aSelectedCustomerParts
 * public
 
 *
 * @properties={typeid:24,uuid:"EDC4EBDC-6FFB-4969-9A5A-17E80E093028"}
 */
function setSelectedCustomerParts(aSelectedCustomerParts) {
    _selectedCustomerPartsArray = aSelectedCustomerParts;
    refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 *
 * @properties={typeid:24,uuid:"5E96CF86-B2E9-4347-95CF-F01EEAE8F731"}
 */
function onAction_selectedCustomerParts(event) {
    showSelectedParentCustomerDialog();
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B32B7443-06CE-496A-8C2E-3ACA3A254A4B"}
 */
function onAction_showFromSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	fromCustomerParts = rItem.item_cust_part_number;
	
	if (fromCustomerParts < toCustomerParts) {
		fromCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"DDA7721A-F258-47FA-92C8-C238FA57C484"}
 */
function onAction_showFromSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	fromParentCustomer = rCustomer.cust_id;
	fromParentCustomerCode = rCustomer.cust_code;
	
	if (fromParentCustomerCode < toParentCustomerCode) {
		fromParentCustomer = null;
		fromParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * @properties={typeid:24,uuid:"AEE6DC0C-292A-4E4C-BEC1-65F03514BDF8"}
 */
function onAction_showToSelectedCustomerPartsDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedCustomerPartsViewOption = 2;
	showSelectedCustomerPartsDialog();
	
	/*** @type {JSRecord<db:/avanti/in_item>} */
	var rItem = scopes.avDB.getRec("in_item", ["item_id"], [_selectedCustomerPartsArray[0]], null, true);
	toCustomerParts = rItem.item_cust_part_number;
	
	if (toCustomerParts < fromCustomerParts) {
		toCustomerParts = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * 
 * @param event
 *
 * 
 *
 * @properties={typeid:24,uuid:"A491415B-C283-48E1-AEC4-E8CBD4C2FD33"}
 */
function onAction_showToSelectedParentCustomersDialog(event) {
	
	// 0 OR 1: User Selection Include Selected Option / Multiselect Option
	// 2: User Selection Source: Lookup Window
	globals.avBase_selectedParentCustomersViewOption = 2;
	showSelectedParentCustomerDialog();
	
	/*** @type {JSRecord<db:/avanti/sa_customer>} */
	var rCustomer = scopes.avDB.getRec("sa_customer", ["cust_id"], [_selectedParentCustomerArray[0]], null, true);
	toParentCustomer = rCustomer.cust_id;
	toParentCustomerCode = rCustomer.cust_code;
	
	if (toParentCustomerCode < fromParentCustomerCode) {
		 toParentCustomer = null;
		 toParentCustomerCode = null;
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
	}
}
