/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B11E7B60-35D8-41B6-8E34-443702E32C25",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"AD7B6012-046B-4972-8372-B5C8AB2FCE4F"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @protected
 *
 * @properties={typeid:24,uuid:"5E291101-A84B-4C17-A46B-9A652517C585"}
 */
function onShow(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	elements.grid.getColumn(elements.grid.getColumnIndex("ordrevd_extended_price")).format = globals.avBase_currencyFormat;
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D509F8DC-3603-4A8D-9495-B82EF8546BF4"}
 */
var tmp_multiple_qty_flag_tooltip = i18n.getI18NMessage('i18n:avanti.tooltip.multipleQuantities');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F23A2FB7-4797-4950-B241-C6E1FFE7B745"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "tmp_select_flag") {
		onAction_selectFlag(event);
	}
}

/**
 * @properties={typeid:35,uuid:"FDF0E273-D5F7-4699-B20F-74256446DF33",variableType:-4}
 */
var _cachedOrderFormat = [];

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0A9E0A57-04DA-4957-8C4E-C98534607E3C"}
 */
function onAction_btnCheckAll(event)
{
	setRecordSelection(1);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F56EF92A-E7CF-4F75-8A6D-F9008B3F1D89"}
 */
function onAction_btnUnCheckAll(event)
{
	setRecordSelection(0);
}

/**
 * setRecordSelection
 * @param {Number} value
 *
 * @properties={typeid:24,uuid:"595C1594-35FE-4AD0-BEA2-2A7D4BD43D18"}
 */
function setRecordSelection(value) {
	
	if (value < 0 || value > 1)
	{
		return;
	}
	
	
	for (var i = 1; i <= foundset.getSize(); i++)
	{
		var rRec = foundset.getRecord(i);
		
		rRec.tmp_select_flag = value
	}
	
	setCopyButtonVisibility();
	
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4C3A4D50-6155-4D2C-AF7D-D51C086C83E4"}
 */
function onAction_selectFlag(event) {
    
    var sLineGangId = tmp_in_line_gang_id,
        iFlag = tmp_select_flag;
    
	if (sLineGangId) {
	    
	    for (var i = 1; i <= foundset.getSize(); i++)
	    {
	        var rRec = foundset.getRecord(i);
	        
	        if (rRec.tmp_in_line_gang_id == sLineGangId) {
	            
	            rRec.tmp_select_flag = iFlag;
	        }
	        
	    }
	}
	setCopyButtonVisibility();
}

/**
 * setCopyButtonVisibility
 *
 * @properties={typeid:24,uuid:"CCE815BF-14FE-43C0-AD42-E77E02D4929F"}
 */
function setCopyButtonVisibility()
{
	forms.sa_order_copy_estimate_dlg.refreshUI();
}

/**
 * Called when the columns data is changed.
 *
 * @param {Number} foundsetindex
 * @param {Number} [columnindex]
 * @param [oldvalue]
 * @param [newvalue]
 * @param {JSEvent} [event]
 * @param {JSRecord} [record]
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"4705D6BF-4227-4B32-9246-F839B04382C7"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldvalue, newvalue, event, record) {
	// TODO Auto-generated method stub
	return true;
}
