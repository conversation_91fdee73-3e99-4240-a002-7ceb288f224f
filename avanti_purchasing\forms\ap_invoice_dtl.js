/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"615F0B41-CFA7-4980-B21E-885691BB863C"}
 */
function onShowForm(firstShow, event) {
	var result =  _super.onShowForm(firstShow, event);
	_super._bOverridePurchaseAllocations = forms.po_receipt_detail_tbl_for_ap_invoices._bAllocatePurchaseVariances;
	_super._bOverrideFreightAllocations = forms.po_receipt_detail_tbl_for_ap_invoices._bAllocateFreightVariances;
	
	_bWorkatoIntegration = globals.isWorkatoIntegration();
	_bWorkatoUseInvoiceRegister = globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.WorkatoUsePayableInvoiceRegister);
	
	setAmountLeft(true);
	setNoteSource();
	refreshUI();
	setToolBarOptions();
	return result;
}

/**
 * Validate invoice date
 * @param _foundset
 * @param _program
 * @param {Date} oldValue
 * @param {String} sField 
 * 
 *
 * @return
 * @properties={typeid:24,uuid:"B9179B3E-146C-41D0-8816-37F25A990783"}
 * @override
 */
function dc_save_validate(_foundset, _program, oldValue, sField) {
	if (ap_invoice_date &&
		ap_invoice_date.getFullYear() &&
		ap_invoice_date.getFullYear() < 1900) {
		scopes.avText.showWarning('invoiceDatevalidationErrorMessage');
		if(oldValue && sField === 'ap_invoice_date') {
			ap_invoice_date = oldValue;
			return 0;
		}
		return -1;
	}

	if (ap_invoice_due_date &&
		ap_invoice_due_date.getFullYear() &&
		ap_invoice_due_date.getFullYear() < 1900) {
		scopes.avText.showWarning('invoiceDueDateValidationErrorMessage');
		if(oldValue && sField === 'ap_invoice_due_date') {
			ap_invoice_due_date = oldValue;
			return 0;
		}
		return -1;
	}

	if (created_date &&
		created_date.getFullYear() &&
		created_date.getFullYear() < 1900) {
		scopes.avText.showWarning('invoiceTransactionDateValidationErrorMessage');
		if(oldValue && sField === 'created_date') {
			created_date = oldValue;
			return 0;
		}
		return -1;
	}

	return _super.dc_save_validate(_foundset, _program);
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"6C731E85-E3F2-457D-8BAA-91B5067B60B0"}
 */
function onDataChangeAmount(oldValue, newValue, event) {
	resetStatus();
	setAmountLeft(true);
	return true;
}

/**
 * @properties={typeid:24,uuid:"2CEC49CE-9087-4AA7-9511-1544429BAFBD"}
 */
function setAmountLeft(bSaveData) {
	foundset.setAmountLeft();
	if (bSaveData) {
		databaseManager.saveData(foundset);
	}
	setMatchStatus();
}

/**
 * @properties={typeid:24,uuid:"E8732D43-A472-48B5-8BBF-827A230E8605"}
 */
function setMatchStatus() {
    foundset.setMatchStatus();
    databaseManager.saveData();
    refreshUI();
}

/**
 * 
 * @public 
 * @properties={typeid:24,uuid:"2E87FE79-F344-4492-8639-12C5BF8B6455"}
 */
function refreshUI() {
	
	setTabVisibility();
	
    elements.apinv_reg_number.visible = (utils.hasRecords(ap_invoice_to_ap_invoice_register) ? true: false);
    elements.ap_invoice_amount.format = globals.avBase_currencyFormat;
    elements.amount_left.format = globals.avBase_currencyFormat;
    elements.ap_invoice_date.format = globals.avBase_dateFormat;
    elements.ap_invoice_due_date.format = globals.avBase_dateFormat;
    elements.created_date.format = globals.avBase_dateFormat;
    
    // Check security keys for user access.
    if (globals.avSecurity_checkForUserRight('AP_Invoice', 'btn_authorize', globals.avBase_employeeUserID)) {
        elements.btn_authorize.visible = true;
    }
    else {
        elements.btn_authorize.visible = false;
    }

    if (ap_invoice_status == 'X') {
        elements.btn_outsideTolerance.visible = true;
        elements.btn_authorize.enabled = true;
    }
    else {
        elements.btn_outsideTolerance.visible = false;
        elements.btn_authorize.enabled = false;
    }

    if (foundset.ap_invoice_status == 'P') {
        elements.btn_onHold.enabled = false;
        elements.btn_open.enabled = false;

        elements.amount_left.enabled = false;
        elements.ap_invoice_amount.enabled = false;
        elements.ap_invoice_date.enabled = false;
    }
    else {
        elements.btn_onHold.enabled = true;
        elements.btn_open.enabled = true;

        elements.amount_left.enabled = false;
        elements.ap_invoice_amount.enabled = false;
        elements.ap_invoice_date.enabled = true;
    }
    
    if (ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.ReadyToPost) {
    	elements.btnReadyToPost.visible = false;
    }
    else {
    	elements.btnReadyToPost.visible = (!_bWorkatoUseInvoiceRegister & _bWorkatoIntegration ? true : false);
    }
    
    forms.ap_invoice_dtl_tbl.refreshUI();
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F0342C2A-8B8A-4FC5-A44F-B6FA6547E1BB"}
 */
function onDataChangeSupplier(oldValue, newValue, event) {
    if (utils.hasRecords(ap_invoice_to_ap_supplier)) {
        curr_id = ap_invoice_to_ap_supplier.curr_id;
        terms_id = ap_invoice_to_ap_supplier.terms_id;

        if (utils.hasRecords(ap_invoice_to_sys_currency)) {
            ap_invoice_exchange_rate = ap_invoice_to_sys_currency.curr_current_rate;
        }
        else {
            ap_invoice_exchange_rate = 1;
        }
        
		if (isInvoiceNumberAlreadyBeenUsed(ap_invoice_num, newValue)) {
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("servoy.general.error"),
				i18n.getI18NMessage("avanti.dialog.APInvoiceNumberExists"),
				i18n.getI18NMessage("avanti.dialog.ok"));
			supplier_id = oldValue;
		}
    }
    return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"84AF6B38-F4E3-416B-8EA3-BC470469390B"}
 */
function onActionOnHold(event) {
	if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit) {
        if (ap_invoice_status != scopes.avAccounting.AP_INVOICE_STATUS.Posted) {
            ap_invoice_status = scopes.avAccounting.AP_INVOICE_STATUS.OnHold;
            refreshUI();
        }
        else {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.postedData_msg'));
        }
    }
    else {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'));
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"06BF416F-036F-4DFD-99A9-534DE0F23BA0"}
 */
function onActionOpen(event) {
    if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit) {
        if (ap_invoice_status != scopes.avAccounting.AP_INVOICE_STATUS.Posted) {
            ap_invoice_status = scopes.avAccounting.AP_INVOICE_STATUS.Open;
            setMatchStatus();
            refreshUI();
        }
        else {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.postedData_msg'));
        }

    }
    else {
		if (ap_invoice_status != scopes.avAccounting.AP_INVOICE_STATUS.Reviewed) {
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'));
		} else {
			var sResponse = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.accountsPayableInvoiceReviewStatusReverted'),
				i18n.getI18NMessage('avanti.dialog.yes'), i18n.getI18NMessage('avanti.dialog.no'));

			if (sResponse == i18n.getI18NMessage('avanti.dialog.yes')) {
				if (utils.hasRecords(ap_invoice_to_ap_invoice_register)) {
					removeFromRegister(foundset.getSelectedRecord());
				}

				apinv_reg_id = null;
				ap_invoice_status = scopes.avAccounting.AP_INVOICE_STATUS.Open;
				setMatchStatus();
			
				databaseManager.saveData();
			}
		}
    }
	
	refreshUI();
	setToolBarOptions();
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {String} _form
 *
 * @return
 * @properties={typeid:24,uuid:"1A99FE36-CD51-4A11-82DA-1ACBB25828E0"}
 */
function onRecordSelection(event, _form) {
	var result = _super.onRecordSelection(event, _form);
	
	refreshUI();
	setToolBarOptions();
	
	return result; 
}

/**
 * @properties={typeid:24,uuid:"788B8A86-0135-4092-8D54-D495BFE8F56E"}
 */
function resetStatus() {
	if (ap_invoice_status == scopes.avAccounting.APMatchStatus.AuthorizedMatch) {
		ap_invoice_status = scopes.avAccounting.APMatchStatus.Unmatched;
	}
	
	// no matched lines, reset to open.
    if (!utils.hasRecords(ap_invoice_to_ap_invoice_detail) 
            && (ap_invoice_status == scopes.avAccounting.APMatchStatus.Matched 
                    || ap_invoice_status == scopes.avAccounting.APMatchStatus.Unmatched 
                    || ap_invoice_status == scopes.avAccounting.APMatchStatus.OnHold)) {
        ap_invoice_status = scopes.avAccounting.APMatchStatus.Open;
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"********-FA4F-4F03-BCA2-C2161BBA72D6"}
 */
function onActionAuthorize(event) {
    if (!scopes.avUtils.isNavModeReadOnly()) {
        if (utils.hasRecords(foundset)) {
            var invoice_rec = foundset.getSelectedRecord();
            invoice_rec.authorized_by_id = globals.avBase_employeeUUID;
            invoice_rec.authorized_date = application.getServerTimeStamp();
            invoice_rec.ap_invoice_status = 'A';
            databaseManager.saveData(invoice_rec);
            refreshUI();
        }
        else {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.lbl.authorizationFailed_noRecords'));
        }
    }
    else {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'));
    }
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"B3EB8538-7521-4174-924A-58BA6899E456"}
 */
function onDataChange_ap_invoice_date(oldValue, newValue, event) {
    var bResult = true;

    //Check if the new date is within a closed period.
    if (scopes.avAccounting.isPurchasingClosedForDate(newValue, org_id)) {
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'), i18n.getI18NMessage('avanti.dialog.ok'));
        ap_invoice_date = oldValue;
        bResult = false;
    }

    return bResult;
}

/**
 * @param {JSFoundSet} _foundset
 *
 * @return
 * @properties={typeid:24,uuid:"3678CEC0-660D-4F30-B6AF-74DCD900B0AE"}
 */
function dc_save_pre(_foundset) {
	/** @type {JSRecord<db:/avanti/ap_invoice>} **/
	var rRec = _foundset.getSelectedRecord();
    if (rRec && rRec.ap_invoice_doc_number == null) {
        /** @type {JSFoundSet<db:/avanti/sys_document_number> } */
        var rDocStream = scopes.avDB.getRec("sys_document_number", ["doctype_code", "docnum_active", "docnum_is_default"], [scopes.avUtils.DOCUMENT_TYPE_CODE.APInvoice, 1, 1]);
        if (rDocStream) {
            rRec.ap_invoice_doc_number = globals.GetNextDocumentNumber(scopes.avUtils.DOCUMENT_TYPE_CODE.APInvoice, rDocStream.docnum_stream);
        }
    }
	if (scopes.avAccounting.isPurchasingClosedForDate(rRec.created_date, rRec.org_id)) {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
	
	return _super.dc_save_pre(_foundset);
}



/**
 * @properties={typeid:24,uuid:"02F46029-C95D-4222-B917-AC32E2AAE4DE"}
 */
function showAmountDialog() {
        // set _orig values for cancel screen
        forms.ap_invoice_dtl_invoice_amount.ap_purchase_amount_orig = foundset.ap_purchase_amount;
        forms.ap_invoice_dtl_invoice_amount.ap_freight_amount_orig = foundset.ap_freight_amount;

        // Display Invoice Amount details
        forms.ap_invoice_dtl_invoice_amount.controller.loadRecords(foundset);
        var sTitle = i18n.getI18NMessage('avanti.lbl.totalInvoiceAmount');
        globals.DIALOGS.showFormInModalDialog(forms.ap_invoice_dtl_invoice_amount, -1, -1, -1, -1, sTitle, true, false, "dlgInvoiceDetailInvoiceAmount", true);

        // update values
        resetStatus();
}

/**
 * Open the Invoice Amount Details dialog
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5ADC19CC-66B3-49DB-BF74-E1E4E3FCFC8B"}
 */
function onActionInvoiceAmountDetails(event) {
	showAmountDialog();
}

/**
 * enable / disable supplier field
 * @public
 *
 * @properties={typeid:24,uuid:"29F44913-F881-4BAE-B77E-************"}
 */
function lockSupplierField() {
    if (elements.supplier_id.enabled || !scopes.avUtils.isNavModeReadOnly()) {
        elements.supplier_id.enabled = utils.hasRecords(foundset.getSelectedRecord().ap_invoice_to_ap_invoice_detail);
    }
    
    if(globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Add && ap_invoice_num == null && !utils.hasRecords(ap_invoice_to_ap_invoice_detail) ){
    	elements.supplier_id.enabled = true;
    }else if(globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit && elements.supplier_id.enabled || !scopes.avUtils.isNavModeReadOnly()){
    	elements.supplier_id.enabled = utils.hasRecords(foundset.getSelectedRecord().ap_invoice_to_ap_invoice_detail);
    }else{
    	elements.supplier_id.enabled = false;
    }
    
//    elements.supplier_id.bgcolor = (elements.supplier_id.enabled ? scopes.avUtils.BACKGROUND_COLORS.White : scopes.avUtils.BACKGROUND_COLORS.Yellow);
    scopes.globals.avUtilities_setStyleClass(elements.supplier_id, (elements.supplier_id.enabled ? scopes.avUtils.BACKGROUND_COLORS.Yellow :  scopes.avUtils.BACKGROUND_COLORS.White));
}

/**
 * Handle changed data.
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F6E05625-E540-4DDC-85E7-DC48C4631629"}
 */
function onDataChange_transactionDate(oldValue, newValue, event) {
	onDataChange_ValidateDate(oldValue, newValue, event);
    var bResult = true;

    //Check if the new date is within a closed period.
    if (scopes.avAccounting.isPurchasingClosedForDate(newValue, org_id)) {
        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), i18n.getI18NMessage('avanti.dialog.transactionDateInClosedPeriod'), i18n.getI18NMessage('avanti.dialog.ok'));
        created_date = oldValue;
        bResult = false;
    }

    return bResult;
}

/**
 * Validates the input date fields in the form
 *
 * @param {Date} oldValue old value
 * @param {Date} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"C1E2A15E-4998-4091-989F-D6E8B09559ED"}
 */
function onDataChange_ValidateDate(oldValue, newValue, event) {
	var sField = event.getElementName();
	dc_save_validate(null, null, oldValue, sField);
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"97D237DC-790E-40F3-981E-C4AFEC4F0AD2"}
 */
function onDataChangeInvoiceNumber(oldValue, newValue, event) {
	if (oldValue != newValue) {
		if (isInvoiceNumberAlreadyBeenUsed(newValue, supplier_id)) {
			globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("servoy.general.error"),
				i18n.getI18NMessage("avanti.dialog.APInvoiceNumberExists"),
				i18n.getI18NMessage("avanti.dialog.ok"));
			ap_invoice_num = oldValue;
		}
	}
	return true;
}

/**
 * Check if the AP invoice number has already been used for the supplier
 * @param invoiceNumber
 * @param supplierId
 *
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"7B41A857-97D7-4BBB-8F91-904DE071746B"}
 */
function isInvoiceNumberAlreadyBeenUsed (invoiceNumber, supplierId) {
	if (invoiceNumber) {
		invoiceNumber = invoiceNumber.trim();
	}
	
	if (invoiceNumber && supplierId) {
		/***@type {JSDataSet} */
		var dsData;
		var oSQL = {
			server: globals.avBase_dbase_avanti,
			args: [globals.org_id.toString(), foundset.ap_invoice_id.toString(), supplierId.toString(), invoiceNumber.toString()]
		};
		oSQL.sql = "SELECT ap_invoice_num \
		  			  FROM ap_invoice \
		  			 WHERE org_id = ? \
		  			   AND ap_invoice_id != ? \
		  			   AND supplier_id = ? \
		  			   AND ap_invoice_num = ?";
		dsData = globals["avUtilities_sqlDataset"](oSQL);
		if (dsData && dsData.getMaxRowIndex() > 0) {
			return true;
		}
	}
	return false;
}

/**
 * Remove Invoice From Register
 * 
 * @param {JSRecord<db:/avanti/ap_invoice>} rAPInvoice
 *
 * @properties={typeid:24,uuid:"331E9853-628F-4145-A905-A271F7E87E24"}
 */
function removeFromRegister(rAPInvoice) {
	if (rAPInvoice) {
		//Message indicating removed from register 
		if (rAPInvoice.ap_invoice_to_ap_invoice_register.apinv_reg_date_reviewed) {
			rAPInvoice.ap_invoice_to_ap_invoice_register.apinv_reg_date_reviewed = null;
			rAPInvoice.ap_invoice_to_ap_invoice_register.apinv_reg_reviewed_by_id = null;
			databaseManager.saveData(ap_invoice_to_ap_invoice_register);
		}
	}
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"DEEA34C5-1189-4B84-86FB-99D820BE542B"}
 */
function onAction_btnReadyToPost(event) {
	if (globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Edit || globals.nav.mode == scopes.avUtils.ENUM_NAV_MODE.Add) {
        if (ap_invoice_status != scopes.avAccounting.AP_INVOICE_STATUS.Posted) {
            ap_invoice_status = scopes.avAccounting.AP_INVOICE_STATUS.ReadyToPost;
            refreshUI();
        }
        else {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.postedData_msg'));
        }
    }
    else {
    	globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.browseMode_msg'));
    }
}

/**
 * Set tab visibility
 *
 * @properties={typeid:24,uuid:"9416474C-**************-B99F4CCB5EAC"}
 */
function setTabVisibility() {
    if (!_bWorkatoUseInvoiceRegister 
    		&& _bWorkatoIntegration
    		&& (ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.ReadyToPost
			|| ap_invoice_status == scopes.avAccounting.AP_INVOICE_STATUS.Posted)) {
        globals.avUtilities_tabAdd("ap_invoice_dtl", "tabs", "ap_invoice_dtl_integration", "avanti.lbl.IntegrationDetails", "_to_ap_invoice", null);
    }
    else {
        globals.avUtilities_tabRemove("ap_invoice_dtl", "tabs", "avanti.lbl.IntegrationDetails");
    }
}
