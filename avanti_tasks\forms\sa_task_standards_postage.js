/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A5CF6DA1-6296-41C2-A0C1-4773DCE9E6A4"}
 */
function onShow(firstShow, event) {
    if (globals.avUtilities_formOnShow(event.getFormName())) {
        return;
    }

    forms.sa_task_dtl.createTaskStandardRecord(event.getFormName());

    _super.onShowForm(firstShow, event);
    refreshUI();
}

/**
 * <PERSON><PERSON> changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"084C4727-5F34-46FC-962D-46B581B2A8DD"}
 */
function onDataChange_numberOfFeeders(oldValue, newValue, event) {
    var i;
    if (oldValue != newValue) {
        var iNew = newValue;

        // Check to see if there are any records
        var aField = ["taskmachine_description", "taskspoil_description"];
        var iExist;

        var fs;
        for (var k = 1; k <= 2; k++) {
            if (k == 1) {
                fs = forms.sa_task_dtl.sa_task_to_sa_task_machine;
            }
            else {
                fs = forms.sa_task_dtl.sa_task_to_sa_task_spoil;
            }
            if (utils.hasRecords(fs)) {
                fs.sort(aField[k - 1] + " asc");
                iExist = fs.getSize();

                // Do we need to delete some records?
                if (iNew < iExist) {
                    for (i = iNew + 1; i <= iExist; i++) {
                        fs.deleteRecord(iNew + 1);
                    }
                }

                // Do we need to add some records?
                if (iNew > iExist) {
                    for (i = iExist + 1; i <= iNew; i++) {
                        fs.newRecord(false, true);
                        fs[aField[k - 1]] = i;
                        databaseManager.saveData(fs.getSelectedRecord());
                    }
                    fs.setSelectedIndex(iExist + 1);
                }
            }
            else if (globals.avBase_selectedTaskID) {
                // Add the records
                for (i = 1; i <= iNew; i++) {
                    fs.newRecord(false, true);
                    fs[aField[k - 1]] = i;
                    databaseManager.saveData(fs.getSelectedRecord());
                }
            }
        }
    }
    return true;
}

/**
 * Handle changed data.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"0F76EDA3-9374-4207-878D-7B2579249880"}
 */
function onDataChange_autoCreatePostage(oldValue, newValue, event) {
    if (newValue && isThereAnotherTaskWithAutoCreatePostageOn()) {
        auto_create_postage_task = null;
        scopes.avText.showWarning('only1autoCreatePostageTask');
    }
    return true;
}

/**
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"9417C4F0-B21C-435D-B3DF-C8112FDCD553"}
 */
function isThereAnotherTaskWithAutoCreatePostageOn() {
    var sSQL = "select count(*) \
                from sa_task_standard \
                where org_id = ? and auto_create_postage_task = 1 and taskstd_id != ?";
    var aArgs = [globals.org_id, taskstd_id.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"FD490AE6-61F4-480B-A676-FABD7E40AF1A"}
 */
function onDataChange_useForMailingItems(oldValue, newValue, event) {
    if (newValue && isThereAnotherTaskWithUseForMailingItemsOn()) {
        use_for_mailing_items = null;
        scopes.avText.showWarning('only1UseForMailingItems');
    }
    return true;
}

/**
 * @return
 * @properties={typeid:24,uuid:"A4EF5C94-1335-4919-8515-416C956DAA9D"}
 */
function isThereAnotherTaskWithUseForMailingItemsOn() {
    var sSQL = "select count(*) \
                        from sa_task_standard \
                        where org_id = ? and use_for_mailing_items = 1 and taskstd_id != ?";
    var aArgs = [globals.org_id, taskstd_id.toString()];

    return scopes.avDB.SQLQuery(sSQL, null, aArgs) > 0;
}

/**
 * Refresh UI
 *
 * @properties={typeid:24,uuid:"29FC6E8F-CBF8-41D3-861C-4D929397AA9A"}
 */
function refreshUI() {
    elements.taskstd_tax_additional_charges_postage.visible = Boolean(scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseEnhancedPostageTaxRules));
    
    if (!elements.taskstd_tax_additional_charges_postage.visible && taskstd_tax_additional_charges_postage) {
        taskstd_tax_additional_charges_postage = false;
        databaseManager.saveData();
    }
}