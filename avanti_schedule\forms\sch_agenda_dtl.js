/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"0CECC3AC-1516-4FC7-B875-D29F668E7B88",variableType:4}
 */
var nConvertType = 0;

/**
 * @properties={typeid:35,uuid:"6A0AE17B-875E-42C7-AF3C-7C0592160B76",variableType:-4}
 */
var _bLoadedForAllTime = false;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"6989856C-A6BC-4296-A923-C812972C6CF8"}
 */
var _sLastLoadViewMode = null;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"6ACD235B-A37C-4AA0-96FF-13F6C6ADD3F5",variableType:93}
 */
var _dLastLoadViewDate = null;

/**
 * @type {Array<JSRecord<db:/avanti/sch_milestone>>}
 * 
 * @properties={typeid:35,uuid:"09B0CB44-E67B-4E55-BAF2-A80779CDA340",variableType:-4}
 */
var arMovePredecessors = [];

/**
 * @type {Array<JSRecord<db:/avanti/sch_milestone>>}
 * 
 * @properties={typeid:35,uuid:"DD9797A0-0D29-4B69-B839-3463A6AA6753",variableType:-4}
 */
var arMoveSuccessors = [];

/**
 * @properties={typeid:35,uuid:"5619188A-73E1-4199-89E7-A96D1F46DEBE",variableType:-4}
 */
var _bResourceChanged = false;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"3F218489-EE9F-4131-9AB3-204FFDE07C8C",variableType:93}
 */
var _dLastOnShow = null;

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"FAE3BF21-6C71-4BDB-9A24-468F49722037",variableType:-4}
 */
var _bSchedEmps = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"5D3A274B-9D6E-41F6-8342-8D06FE88A6B0",variableType:4}
 */
var _nAutoRefresh = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"25805C6D-C808-4C6B-9DF9-C23436F79F4F",variableType:4}
 */
var _nRefreshSeconds = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"A00FECEA-13C1-4D1D-A0AF-6A60C29FEB15"}
 */
var _zoomFactor = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"1D89C2C4-D782-4234-8077-983D31D912B3"}
 */
var _viewID = null;

/**
 * @properties={typeid:35,uuid:"258E3092-9B77-4E5F-A077-DF6C8696F995",variableType:-4}
 */
var _aRes = null;

/**
 * @properties={typeid:35,uuid:"42C807D2-B7E2-4AF2-A2EB-2CC8791C32BD",variableType:-4}
 */
var _aResType = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2DA0A734-B32E-4627-B4AA-F6080298C5C5"}
 */
var _msID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"BC88C4AC-A31C-4667-AAA4-26900106A03A"}
 */
var sMilestoneId = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"942BE324-5C70-4FB7-A56B-A6016EADE95F"}
 */
var _emplID = null;

/**
 * @type {Array<String>}
 * 
 * @properties={typeid:35,uuid:"22EB07D1-D29C-4E14-9B94-BB1737E8B319",variableType:-4}
 */
var _aMS = [];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"35C6C44A-555C-4012-BAAA-CE2E3544EF44"}
 */
var _deptID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D2913287-265D-4FE5-8EF6-3AEB84520B28"}
 */
var _html = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0CC62871-1593-4789-9EDE-D155A5080731"}
 */
var _equipID = null;

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"654226A9-5047-4850-B966-DFB2BDD1FC92",variableType:-4}
 */
var _aUnits = [];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"64BCE543-D169-4757-B996-F17225504F6E",variableType:-4}
 */
var _aJSON = [];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"BE434D93-ABF0-43F1-A5C4-CB79DA286EFA",variableType:-4}
 */
var _aExcep = [];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"A769A6B5-82F9-4977-9693-37FC97158DF7",variableType:-4}
 */
var _aExcepNew = [];

/**
 * @type {Array}
 *
 * @properties={typeid:35,uuid:"F1A6390A-5EE1-4D0E-AB71-571642C7A01E",variableType:-4}
 */
var _aDatesNonShiftTimeExceptionsAddedFor = [];

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"61B7F9E5-AD35-4E57-9C51-DBD0723109C6",variableType:4}
 */
var _pullPredecessors = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"DC4DD5EA-0B20-4C67-8EF4-40054C195680",variableType:4}
 */
var _pullSuccessors = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"2BA8F4F4-DD73-49F2-9AF4-1E5D345921F1",variableType:4}
 */
var _nDebugLoadAllData = 0;

/**
 * @type {Number}
 * 
 * this is here for debugging purposes for turning off moving of Predecessors + Successors
 *
 * @properties={typeid:35,uuid:"245BF25E-6E9F-43C5-983B-352DD9A1F2E4",variableType:4}
 */
var _dontMovePandS = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"7D48790C-4578-4F1A-862E-360CD56BB598"}
 */
var _unMoveablePredecessor = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2ADEC5E8-9075-457A-BD64-C673015B6913"}
 */
var _cur_mode = null;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"93325BE6-074F-4CC8-BB27-7BF2E432A923",variableType:93}
 */
var _dCurDate = null;

/**
 * @properties={typeid:35,uuid:"E3596B77-9A02-4BB4-B5C4-0C5366002CCF",variableType:-4}
 */
var bCancelled = false;

/**
 * @properties={typeid:35,uuid:"233D9DB6-DF0B-41D0-B2E2-652FCEAE91C6",variableType:-4}
 */
var aMilestoneExcep = [];

/**
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"ABA22E24-9746-4FEE-9306-036526E32BB4",variableType:93}
 */
var dOptimizeClearFromDate = null;

/**
 * @properties={typeid:35,uuid:"7DABEA6E-3140-46B0-91DC-067F7B4767E3",variableType:-4}
 */
var _bNeedShowWarningDialog = false;

/**
 * @properties={typeid:35,uuid:"3AE29CEC-90CB-4AF3-B9C0-094BE1B14DF1",variableType:-4}
 */
var _bScheduleBoardLockedWarning = false;

/**
 * @properties={typeid:35,uuid:"2C1FE865-4CA4-47DF-B0B7-9695B9AEAF01",variableType:-4}
 */
var _bShowErrorRecalculatingTimeNeeded = false;

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"2359284F-88D0-4E25-8BD3-B94ED3F5906D"}
 */
function lockMilestone(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone) {
			rMilestone.ms_flg_locked = 1;
			databaseManager.saveData(rMilestone);
		}
	}
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"985D0BDC-883C-45CC-95C3-E1923566EC35"}
 */
function lockSection(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone && utils.hasRecords(rMilestone.sch_milestone_to_sa_order_revision_detail_section)) {
			var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
			var fsMilestones = rSection.sa_order_revision_detail_section_to_sch_milestone;
			
			rSection.ordrevds_flg_locked = 1;
			
			for (var i = 1; i <= fsMilestones.getSize(); i++) {
				fsMilestones.getRecord(i).ms_flg_locked = 1;
			}
			
			databaseManager.saveData(rSection);
			databaseManager.saveData(fsMilestones);
		}
	}
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"98DAF56D-3B9B-4F45-B48A-70A125F4047B"}
 */
function lockJob(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone && utils.hasRecords(rMilestone.sch_milestone_to_sch_schedule)) {
			var rSchedule = rMilestone.sch_milestone_to_sch_schedule.getRecord(1);
			var fsMilestones = rSchedule.sch_schedule_to_sch_milestone;
			
			rSchedule.sch_flg_locked = 1;
			
			for (var i = 1; i <= fsMilestones.getSize(); i++) {
				fsMilestones.getRecord(i).ms_flg_locked = 1;
			}
			
			databaseManager.saveData(rSchedule);
			databaseManager.saveData(fsMilestones);
		}
	}
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"41D8C4AF-387B-4FED-86BD-837EBFD35F02"}
 */
function unlockTask(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone) {
			rMilestone.ms_flg_locked = 0;
			databaseManager.saveData(rMilestone);
		}
	}
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"A35737DA-B007-468C-86A3-66D617799656"}
 */
function unlockSection(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone && utils.hasRecords(rMilestone.sch_milestone_to_sa_order_revision_detail_section)) {
			var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
			var fsMilestones = rSection.sa_order_revision_detail_section_to_sch_milestone;
			
			rSection.ordrevds_flg_locked = 0;
			
			for (var i = 1; i <= fsMilestones.getSize(); i++) {
				fsMilestones.getRecord(i).ms_flg_locked = 0;
			}
			
			databaseManager.saveData(rSection);
			databaseManager.saveData(fsMilestones);
		}
	}
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"5F2BD472-F91C-4E74-B594-7A699D2388A2"}
 */
function unlockJob(ms_id){
	if (ms_id) {
		/***@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
		
		if (rMilestone && utils.hasRecords(rMilestone.sch_milestone_to_sch_schedule)) {
			var rSchedule = rMilestone.sch_milestone_to_sch_schedule.getRecord(1);
			var fsMilestones = rSchedule.sch_schedule_to_sch_milestone;
			
			rSchedule.sch_flg_locked = 0;
			
			for (var i = 1; i <= fsMilestones.getSize(); i++) {
				fsMilestones.getRecord(i).ms_flg_locked = 0;
			}
			
			databaseManager.saveData(rSchedule);
			databaseManager.saveData(fsMilestones);
		}
	}
}

/**
 * @param {String} ms_id
 * @param {Number} nDiffMins
 * @param {Number} nStartDate
 * @param {Number} nEndDate
 *
 * @properties={typeid:24,uuid:"59A5C340-52DD-4A61-AF25-D0A14F0C0053"}
 * @AllowToRunInFind
 */
function pushSuccessorsForward(ms_id, nDiffMins, nStartDate, nEndDate) {
    // this is here for debugging purposes for turning off moving of Predecessors + Successors
    if (!_dontMovePandS) { 
        /***@type {JSRecord<db:/avanti/sch_milestone>} */
        var rec = getRec('sch_milestone', 'ms_id', ms_id)
        if (rec && milestoneMove) {
        	arMoveSuccessors = [];
            pushSuccessorForward(rec);
        	arMoveSuccessors = [];
        }
    }
}

/**
 * returns monday week date
 * 
 * @return {Date}
 * 
 * @properties={typeid:24,uuid:"CFFC92F2-286B-44F7-AAFA-13B29065B9AE"}
 */
function getFirstDayOfWeek() {
	if (!_dCurDate) {
		_dCurDate = application.getTimeStamp();
	}

	var dSunday = scopes.svyDateUtils.getFirstDayOfWeek(_dCurDate);
	var dFirstDay = scopes.avDate.addDaysNoDSTChange(dSunday, 1);

	return dFirstDay;
}

/**
 * @param {String|UUID} ms_id
 *
 * @properties={typeid:24,uuid:"55B1D5F6-B1AB-48F5-8D4B-4CBEB1B13C31"}
 */
function pullPredecessorsForward(ms_id) {
    if (_pullPredecessors && milestoneMove) {
        /***@type {JSRecord<db:/avanti/sch_milestone>} */
        var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);

        if (rMilestone) {
        	var aPredecessors = scopes.avScheduling.getSchedulablePredecessors(rMilestone, true);
        	
			if (aPredecessors.length > 0) {
				for (var i = 0; i < aPredecessors.length; i++) {
					pullPredecessorForward(rMilestone, aPredecessors[i]);
				}
			}
        }
    }
}

/**
 * @param {String} ms_id
 * @param {Number} nDiffMins
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"DB88B728-E9A1-4C2A-931E-DFC92C8784CC"}
 */
function pullSuccessorsBack(ms_id, nDiffMins) {
	var bSuccess = true;
	
    if (_pullSuccessors && milestoneMove) {
        /***@type {JSRecord<db:/avanti/sch_milestone>} */
        var rec = getRec('sch_milestone', 'ms_id', ms_id);

        if (rec) {
        	arMoveSuccessors = [];
            
            // if one of the successors isFirstMilestoneInAssembly, then it may have multiple aPredecessors. in which a different branch of predecessors will have to be pushed back.
            // in which case its possible they are pushed into the past, which will cause pullSuccessorBack to fail
            if (!pullSuccessorBack(rec)) {
                /***@type {JSRecord<db:/avanti/sch_milestone>} */
                var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);
				var dOldStartDate = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_scheduled, nDiffMins * -1)
				var dOldEndDate = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, nDiffMins * -1)

				// clear successor moves
				if (dOldStartDate != rMilestone.ms_date_scheduled || dOldEndDate != rMilestone.ms_date_due) {
					rMilestone.ms_date_scheduled = dOldStartDate;
					rMilestone.ms_date_due = dOldEndDate;

					if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
						rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_start = rMilestone.ms_date_scheduled;
						rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_end = rMilestone.ms_date_due;
					}
					
					// have to call getNextFreeSpot to update caprecs
					scopes.avScheduling.clearMilestoneCapacity(rMilestone);
			        scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, null, null, null, null, true, null, true);
					
		            updateEventStartEndTime(rMilestone.ms_id, rMilestone.ms_date_scheduled, rMilestone.ms_date_due);
				}
				
				revertMovePredecessors();
				revertMoveSuccessors();
				
				bSuccess = false;
            }
            
        	arMoveSuccessors = [];
        }
    }
    
    return bSuccess;
}

/**
 * @param {String|UUID} ms_id
 * @param {Number} nDiffMins
 *
 * @properties={typeid:24,uuid:"27D6DA4C-275E-4F0E-9BB3-AC57DDB430D8"}
 */
function pushPredecessorsBack(ms_id, nDiffMins) {
    // this is here for debugging purposes for turning off moving of Predecessors + Successors
    if (!_dontMovePandS && milestoneMove) { 
        /***@type {JSRecord<db:/avanti/sch_milestone>} */
        var rMilestone = getRec('sch_milestone', 'ms_id', ms_id);

        if (rMilestone) {
            msID = rMilestone.ms_id;
            
        	var aPredecessors = scopes.avScheduling.getSchedulablePredecessors(rMilestone, true);
        	
			if (aPredecessors.length > 0) {
				var nMovePredecessorFailed = false;
				
	            arMovePredecessors = [];
				
				for (var i = 0; i < aPredecessors.length; i++) {
		            if (!pushPredecessorBack(rMilestone, aPredecessors[i])) {
		            	nMovePredecessorFailed = true;
		            	break;
		            }
				}
				
				if (nMovePredecessorFailed) {
					var dOldStartDate = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_scheduled, nDiffMins * -1)
					var dOldEndDate = scopes.avDate.addMinutesNoDSTChange(rMilestone.ms_date_due, nDiffMins * -1)
					
					if (dOldStartDate != rMilestone.ms_date_scheduled || dOldEndDate != rMilestone.ms_date_due) {
						rMilestone.ms_date_scheduled = dOldStartDate;
						rMilestone.ms_date_due = dOldEndDate;

						if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
							rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_start = rMilestone.ms_date_scheduled;
							rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_end = rMilestone.ms_date_due;
						}
						
						// have to call getNextFreeSpot to update caprecs
						scopes.avScheduling.clearMilestoneCapacity(rMilestone);
				        scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, null, null, null, null, true, null, true);
						
			            updateEventStartEndTime(rMilestone.ms_id, rMilestone.ms_date_scheduled, rMilestone.ms_date_due);
					}
					
					revertMovePredecessors();
				}

				arMovePredecessors = [];
			}
        }
    }
}

/**
 * @properties={typeid:24,uuid:"CA0E7130-D402-4F0F-A943-8DCDBDAA90E4"}
 */
function revertMovePredecessors() {
	for (var i = 0; i < arMovePredecessors.length; i++) {
		var rPredecessor = arMovePredecessors[i];

		if (rPredecessor.ms_date_scheduled != rPredecessor.ms_date_scheduled_bak || rPredecessor.ms_date_due != rPredecessor.ms_date_due_bak) {
			rPredecessor.ms_date_scheduled = rPredecessor.ms_date_scheduled_bak;
			rPredecessor.ms_date_due = rPredecessor.ms_date_due_bak;
		    rPredecessor.ms_time_budget = rPredecessor.ms_time_budget_bak;
			
			if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_equip_schedule)) {
				rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_start = rPredecessor.ms_date_scheduled;
				rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_end = rPredecessor.ms_date_due;
			}
			
			// have to call getNextFreeSpot to update caprecs
			scopes.avScheduling.clearMilestoneCapacity(rPredecessor);
	        scopes.avScheduling.getNextFreeSpot(rPredecessor, 'F', null, true, null, null, null, null, null, true, null, true);
	       
			updateEventStartEndTime(rPredecessor.ms_id, rPredecessor.ms_date_scheduled, rPredecessor.ms_date_due);
			// have to revert emps too
            if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_empl_schedule)) {
                for (var j = 1; j <= rPredecessor.sch_milestone_to_sch_empl_schedule.getSize(); j++) {
                    var rEmpSched = rPredecessor.sch_milestone_to_sch_empl_schedule.getRecord(j);

                    rEmpSched.emplsch_start = rPredecessor.ms_date_scheduled;
                    rEmpSched.emplsch_end = rPredecessor.ms_date_due;

                    databaseManager.saveData(rEmpSched);
                }
            }

            moveMsEmps(rPredecessor);
		}
	}
}

/**
 * @properties={typeid:24,uuid:"955683AF-1078-49EF-B5E2-1AE6DD32B2AF"}
 */
function revertMoveSuccessors() {
	for (var i = 0; i < arMoveSuccessors.length; i++) {
		var rSuccessor = arMoveSuccessors[i];

		if (rSuccessor.ms_date_scheduled != rSuccessor.ms_date_scheduled_bak || rSuccessor.ms_date_due != rSuccessor.ms_date_due_bak) {
			rSuccessor.ms_date_scheduled = rSuccessor.ms_date_scheduled_bak;
			rSuccessor.ms_date_due = rSuccessor.ms_date_due_bak;
			rSuccessor.ms_time_budget = rSuccessor.ms_time_budget_bak;
			
			if (utils.hasRecords(rSuccessor.sch_milestone_to_sch_equip_schedule)) {
				rSuccessor.sch_milestone_to_sch_equip_schedule.equipsch_start = rSuccessor.ms_date_scheduled;
				rSuccessor.sch_milestone_to_sch_equip_schedule.equipsch_end = rSuccessor.ms_date_due;
			}
			
			// have to call getNextFreeSpot to update caprecs
			scopes.avScheduling.clearMilestoneCapacity(rSuccessor);
	        scopes.avScheduling.getNextFreeSpot(rSuccessor, 'F', null, true, null, null, null, null, null, true, null, true);
	       
			updateEventStartEndTime(rSuccessor.ms_id, rSuccessor.ms_date_scheduled, rSuccessor.ms_date_due);
			// have to revert emps too
            if (utils.hasRecords(rSuccessor.sch_milestone_to_sch_empl_schedule)) {
                for (var j = 1; j <= rSuccessor.sch_milestone_to_sch_empl_schedule.getSize(); j++) {
                    var rEmpSched = rSuccessor.sch_milestone_to_sch_empl_schedule.getRecord(j);

                    rEmpSched.emplsch_start = rSuccessor.ms_date_scheduled;
                    rEmpSched.emplsch_end = rSuccessor.ms_date_due;

                    databaseManager.saveData(rEmpSched);
                }
            }

            moveMsEmps(rSuccessor);
		}
	}
}

/**
 * @param {String} sDate
 *
 * @returns {Date} new date
 * 
 * @properties={typeid:24,uuid:"7EBD4890-8B26-4616-B198-A4212BC65D65"}
 */
function parseDateString(sDate)
{
	var nYear;
	var nMnth;
	var nDay;
	var nHour;
	var nMin;
	
	var dDate = new Date();
	var aDate = sDate.split(" ");
	var aDateDate = aDate[0].split("-");
	var aDateTime = aDate[1].split(":");
	nYear = Number(aDateDate[0]);
	nMnth = Number(aDateDate[1]) - 1;
	nDay = Number(aDateDate[2]);
	nHour = Number(aDateTime[0]);
	nMin = Number(aDateTime[1]);
	
	dDate.setFullYear(nYear, nMnth, nDay);
	dDate.setHours(nHour);
	dDate.setMinutes(nMin,0,0);
	
	if (dDate.getFullYear() == 1969)
	{
		application.output("Data corruption");
	}
	
	return dDate;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"C75874C5-F119-4A39-98D8-BE1D756F32AB"}
 */
function pushSuccessorForward(rMilestone) {
    var rSuccessor = scopes.avScheduling.getSuccessor(rMilestone, true, true);
    
    if (rSuccessor) {
        // need these baks for revertMoveSuccessors()
    	rSuccessor.ms_date_scheduled_bak = rSuccessor.ms_date_scheduled;
    	rSuccessor.ms_date_due_bak = rSuccessor.ms_date_due;
    	rSuccessor.ms_time_budget_bak = rSuccessor.ms_time_budget;

    	var bMoveSuccessor = false;
    	var bFirstMilestoneInAssembly = scopes.avScheduling.isFirstMilestoneInAssembly(rSuccessor)
    	
    	// have to use different logic for bFirstMilestoneInAssembly
		if (bFirstMilestoneInAssembly) {
	    	var dMaxPredecessorDate = scopes.avScheduling.getMaxPredecessorDate(rSuccessor);

	        if (dMaxPredecessorDate > rSuccessor.ms_date_scheduled) {
	            bMoveSuccessor = true;
	            createTemporaryMilestone(rSuccessor); // create temp milestone that will be reverted on cancel
	            rSuccessor.ms_date_scheduled = dMaxPredecessorDate;
	        }
		}
    	else if (rMilestone.ms_date_due_lag && rMilestone.ms_date_scheduled_lag) {
            bMoveSuccessor = true;
            createTemporaryMilestone(rSuccessor); // create temp milestone that will be reverted on cancel
            rSuccessor.ms_date_scheduled = scopes.avScheduling.getMilestoneMaxDateWithLag(rMilestone);
        }
        // 1. FS lag
        else if (rMilestone.ms_date_due_lag) {
            bMoveSuccessor = true;
            createTemporaryMilestone(rSuccessor); // create temp milestone that will be reverted on cancel
            rSuccessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_due, rMilestone.ms_date_due_lag);
        }
        // 2. SS lag
        else if (rMilestone.ms_date_scheduled_lag) {
            bMoveSuccessor = true;
            createTemporaryMilestone(rSuccessor); // create temp milestone that will be reverted on cancel
            rSuccessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_date_scheduled_lag);
        }
        // 3. moved ms impinging on successor time
        else if (rMilestone.ms_date_due > rSuccessor.ms_date_scheduled) {
            bMoveSuccessor = true;
            createTemporaryMilestone(rSuccessor); // create temp milestone that will be reverted on cancel
            rSuccessor.ms_date_scheduled = rMilestone.ms_date_due;
        }
    	
        if (bMoveSuccessor) {
        	arMoveSuccessors.push(rSuccessor);
			rSuccessor.ms_date_due = plugins.DateUtils.addMinutes(rSuccessor.ms_date_scheduled, rSuccessor.ms_time_budget);

            scopes.avScheduling.clearMilestoneCapacity(rSuccessor);
            scopes.avScheduling.getNextFreeSpot(rSuccessor, 'F', null, true, null, null, null, null, null, true, null, true);

            if (utils.hasRecords(rSuccessor.sch_milestone_to_sch_equip_schedule)) {
                // update sch_equip_schedule (this is the table the sched brd reads from)
                rSuccessor.sch_milestone_to_sch_equip_schedule.equipsch_start = rSuccessor.ms_date_scheduled;
                rSuccessor.sch_milestone_to_sch_equip_schedule.equipsch_end = rSuccessor.ms_date_due;
                databaseManager.saveData(rSuccessor.sch_milestone_to_sch_equip_schedule);
            }

            if (utils.hasRecords(rSuccessor.sch_milestone_to_sch_empl_schedule)) {
                for (var i = 1; i <= rSuccessor.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
                    var rEmpSched = rSuccessor.sch_milestone_to_sch_empl_schedule.getRecord(i);

                    rEmpSched.emplsch_start = rSuccessor.ms_date_scheduled;
                    rEmpSched.emplsch_end = rSuccessor.ms_date_due;

                    databaseManager.saveData(rEmpSched);
                }
            }

            moveMsEmps(rSuccessor);
            updateEventStartEndTime(rSuccessor.ms_id, rSuccessor.ms_date_scheduled, rSuccessor.ms_date_due);
            
			scopes.avScheduling.defragMilestoneCapRecs(rSuccessor);
            
            // if this rSuccessor isFirstMilestoneInAssembly, then it may have multiple aPredecessors. in which case they (other than rMilestone) have to be pull forwards (if pull forwards is on) 
			if (_pullPredecessors && bFirstMilestoneInAssembly) {
				var aPredecessors = scopes.avScheduling.getAssemblySchedulablePredecessors(rSuccessor, true);

				if (aPredecessors.length > 1) {
					for (var p = 0; p < aPredecessors.length; p++) {
						var rPredecessor = aPredecessors[p];
						
						if (rPredecessor.ms_id != rMilestone.ms_id) {
							pullPredecessorForward(rSuccessor, rPredecessor);
						}
					}
				}
			}

            // now update this successor's successor
            pushSuccessorForward(rSuccessor);
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"587A82D5-82F7-4A4A-9EB3-64364C986916"}
 */
function moveMsEmps(rMilestone){
    var aEmps = scopes.avScheduling.getEmpsWorkingOnMS(rMilestone.ms_id);
    
    if(aEmps){
        for(var i=0; i<aEmps.length; i++){
            var sEventID = rMilestone.ms_id + ',' + aEmps[i];
            
            updateEventStartEndTime(sEventID, rMilestone.ms_date_scheduled, rMilestone.ms_date_due);
        }
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"0B62B574-ECB9-4524-91F2-D6BFC0217954"}
 */
function pullSuccessorBack(rMilestone) {
    var rec_successor = scopes.avScheduling.getSuccessor(rMilestone, true, true);
    
    if (rec_successor) {
        // need these baks for revertMoveSuccessors()
    	rec_successor.ms_date_scheduled_bak = rec_successor.ms_date_scheduled;
    	rec_successor.ms_date_due_bak = rec_successor.ms_date_due;
    	rec_successor.ms_time_budget_bak = rec_successor.ms_time_budget;
    	
    	arMoveSuccessors.push(rec_successor);
    	
        // create temp milestone that will be reverted on cancel
        createTemporaryMilestone(rec_successor);

        if (rMilestone.ms_date_due_lag && rMilestone.ms_date_scheduled_lag) {
        	rec_successor.ms_date_scheduled = scopes.avScheduling.getMilestoneMaxDateWithLag(rMilestone);
        }
        else if (rMilestone.ms_date_due_lag) {
        	rec_successor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_due, rMilestone.ms_date_due_lag);
        }
        else if (rMilestone.ms_date_scheduled_lag) {
        	rec_successor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, rMilestone.ms_date_scheduled_lag);
        }
        else {
            rec_successor.ms_date_scheduled = rMilestone.ms_date_due;
        }
        
        rec_successor.ms_date_due = plugins.DateUtils.addMinutes(rec_successor.ms_date_scheduled, rec_successor.ms_time_budget);
		
		scopes.avScheduling.clearMilestoneCapacity(rec_successor);
		
		// was using backwards, which isnt right, should use forwards. we pull the successor back to where we want it to be, but
		// if it needs to be pushed into future because of sched constraints thats what should happen
        scopes.avScheduling.getNextFreeSpot(rec_successor, 'F', null, true, null, null, null, null, null, true, null, true);

		// update sch_equip_schedule (this is the table the sched brd reads from)
		if(utils.hasRecords(rec_successor.sch_milestone_to_sch_equip_schedule)){
	        rec_successor.sch_milestone_to_sch_equip_schedule.equipsch_start = rec_successor.ms_date_scheduled;
	        rec_successor.sch_milestone_to_sch_equip_schedule.equipsch_end = rec_successor.ms_date_due;
            databaseManager.saveData(rec_successor.sch_milestone_to_sch_equip_schedule);
		}
		
        if(utils.hasRecords(rec_successor.sch_milestone_to_sch_empl_schedule)){
            for(var i=1; i<=rec_successor.sch_milestone_to_sch_empl_schedule.getSize(); i++){
                var rEmpSched = rec_successor.sch_milestone_to_sch_empl_schedule.getRecord(i);
                
                rEmpSched.emplsch_start = rec_successor.ms_date_scheduled;
                rEmpSched.emplsch_end = rec_successor.ms_date_due;
                
                databaseManager.saveData(rEmpSched);
            }
        }
		
		moveMsEmps(rec_successor);
		updateEventStartEndTime(rec_successor.ms_id, rec_successor.ms_date_scheduled, rec_successor.ms_date_due);
		scopes.avScheduling.defragMilestoneCapRecs(rec_successor);
		
        // if this rSuccessor isFirstMilestoneInAssembly, then it may have multiple aPredecessors. in which case they (other than rMilestone) have to be pushed back 
		if (scopes.avScheduling.isFirstMilestoneInAssembly(rec_successor)) {
			var aPredecessors = scopes.avScheduling.getAssemblySchedulablePredecessors(rec_successor, true);

			if (aPredecessors.length > 1) {
				for (var p = 0; p < aPredecessors.length; p++) {
					var rPredecessor = aPredecessors[p];
					
					if (rPredecessor.ms_id != rMilestone.ms_id) {
						// if we couldnt push a predecessor back then have to revert all moves
						if (!pushPredecessorBack(rec_successor, rPredecessor)) {
							return false;
						}
					}
				}
			}
		}

		// now update this successor's successor
		return pullSuccessorBack(rec_successor);
	}
    
	return true;
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sch_milestone>} rPredecessor
 *
 * @properties={typeid:24,uuid:"F90D0675-413A-4462-92D0-621979CBA313"}
 */
function pullPredecessorForward(rMilestone, rPredecessor) {
	if (rPredecessor) {
		// create temp milestone that will be reverted on cancel
		createTemporaryMilestone(rPredecessor);
		
        // sl-15558 - got rid of adding num minutes moved, didnt work if eith milestone had a break in it.
        // add lag logic here as well.
        /***@type {{sLagType:String,nLagTime:Number}}*/
        var oLag = scopes.avScheduling.getMilestoneLag(rPredecessor);
        
        if (oLag && oLag.sLagType && oLag.nLagTime) {
            if (oLag.sLagType == 'FS') {
            	rPredecessor.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, -oLag.nLagTime);
            	rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rPredecessor.ms_date_due, -rPredecessor.ms_time_budget);
            }
            else if (oLag.sLagType == 'SS') {
            	rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, -oLag.nLagTime);
            	rPredecessor.ms_date_due = plugins.DateUtils.addMinutes(rPredecessor.ms_date_scheduled, rPredecessor.ms_time_budget);
            }
        }
        else {
        	rPredecessor.ms_date_due = rMilestone.ms_date_scheduled;
        	rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rPredecessor.ms_date_due, -rPredecessor.ms_time_budget); 
        }
		
        scopes.avScheduling.clearMilestoneCapacity(rPredecessor);
        
        // sl-15558 - change from Forward to Backward scheduling. we want to pull predecessor up to start time 
        // of cur ms, then find soonest in the past it can be scheduled  
        var dNextFreeSpot = scopes.avScheduling.getNextFreeSpot(rPredecessor, 'B', null, true, null, null, null, null, null, null, null, true);

		if (dNextFreeSpot) {
			// update sch_equip_schedule (this is the table the sched brd reads from)
			if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_equip_schedule)) {
				rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_start = rPredecessor.ms_date_scheduled;
				rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_end = rPredecessor.ms_date_due;
				databaseManager.saveData(rPredecessor.sch_milestone_to_sch_equip_schedule);
			}

			if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_empl_schedule)) {
				for (var i = 1; i <= rPredecessor.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
					var rEmpSched = rPredecessor.sch_milestone_to_sch_empl_schedule.getRecord(i);

					rEmpSched.emplsch_start = rPredecessor.ms_date_scheduled;
					rEmpSched.emplsch_end = rPredecessor.ms_date_due;

					databaseManager.saveData(rEmpSched);
				}
			}

			moveMsEmps(rPredecessor);
			updateEventStartEndTime(rPredecessor.ms_id, rPredecessor.ms_date_scheduled, rPredecessor.ms_date_due)
			scopes.avScheduling.defragMilestoneCapRecs(rPredecessor);

			// now update this predecessor's predecessor
			var aPredecessors = scopes.avScheduling.getSchedulablePredecessors(rPredecessor, true);

			if (aPredecessors.length > 0) {
				for (var p = 0; p < aPredecessors.length; p++) {
					pullPredecessorForward(rPredecessor, aPredecessors[p]);
				}
			}
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 * @param {JSRecord<db:/avanti/sch_milestone>} rPredecessor
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"237B9BAE-2CAF-41FA-B8D6-7E6F8571AFD0"}
*/
function pushPredecessorBack(rMilestone, rPredecessor) {
    /***@type {{sLagType:String,nLagTime:Number}}*/
    var oLag = scopes.avScheduling.getMilestoneLag(rPredecessor);
    var bMovePredecessor = false;
    var dNow = application.getTimeStamp();

    // need these baks for revertMovePredecessors()
    rPredecessor.ms_date_scheduled_bak = rPredecessor.ms_date_scheduled;
    rPredecessor.ms_date_due_bak = rPredecessor.ms_date_due;
    rPredecessor.ms_time_budget_bak = rPredecessor.ms_time_budget;
    
    // 3 reasons to move predecessor:

    // 1. FS lag
    if (oLag.nLagTime && oLag.sLagType == 'FS') {
        bMovePredecessor = true;
        createTemporaryMilestone(rPredecessor); // create temp milestone that will be reverted on cancel
        rPredecessor.ms_date_due = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, oLag.nLagTime * -1);
        rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rPredecessor.ms_date_due, rPredecessor.ms_time_budget * -1);
    }
    // 2. SS lag
    else if (oLag.nLagTime && oLag.sLagType == 'SS') {
        bMovePredecessor = true;
        createTemporaryMilestone(rPredecessor); // create temp milestone that will be reverted on cancel
        rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rMilestone.ms_date_scheduled, oLag.nLagTime * -1);
        rPredecessor.ms_date_due = plugins.DateUtils.addMinutes(rPredecessor.ms_date_scheduled, rPredecessor.ms_time_budget);
    }
    // 3. moved ms impinging on successor time
    else if (rMilestone.ms_date_scheduled < rPredecessor.ms_date_due) {
        bMovePredecessor = true;
        createTemporaryMilestone(rPredecessor); // create temp milestone that will be reverted on cancel
        rPredecessor.ms_date_due = rMilestone.ms_date_scheduled;
        rPredecessor.ms_date_scheduled = plugins.DateUtils.addMinutes(rPredecessor.ms_date_due, rPredecessor.ms_time_budget * -1);
    }

     if (bMovePredecessor) {
		// cant push predecessor into the past - only do this check if we have actually changed predecessor dates above (bMovePredecessor) 
		if (rPredecessor.ms_date_scheduled < dNow) {
			return false;
		}
    	 
        scopes.avScheduling.clearMilestoneCapacity(rPredecessor);
        arMovePredecessors.push(rPredecessor);
        
        var dNextFreeSpot = scopes.avScheduling.getNextFreeSpot(rPredecessor, 'B', null, true, null, null, null, null, null, true, null, true);

        // cant push predecessor into the past - getNextFreeSpot may have modified ms_date_scheduled
    	if (rPredecessor.ms_date_scheduled < dNow) {
            return false;
    	}
        if (dNextFreeSpot) {
            if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_equip_schedule)) {
                rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_start = rPredecessor.ms_date_scheduled;
                rPredecessor.sch_milestone_to_sch_equip_schedule.equipsch_end = rPredecessor.ms_date_due;
                databaseManager.saveData(rPredecessor.sch_milestone_to_sch_equip_schedule);
            }

            if (utils.hasRecords(rPredecessor.sch_milestone_to_sch_empl_schedule)) {
                for (var i = 1; i <= rPredecessor.sch_milestone_to_sch_empl_schedule.getSize(); i++) {
                    var rEmpSched = rPredecessor.sch_milestone_to_sch_empl_schedule.getRecord(i);

                    rEmpSched.emplsch_start = rPredecessor.ms_date_scheduled;
                    rEmpSched.emplsch_end = rPredecessor.ms_date_due;

                    databaseManager.saveData(rEmpSched);
                }
            }

            moveMsEmps(rPredecessor);
            updateEventStartEndTime(rPredecessor.ms_id, rPredecessor.ms_date_scheduled, rPredecessor.ms_date_due);
            
			scopes.avScheduling.defragMilestoneCapRecs(rPredecessor);

            // now update this predecessor's predecessor
        	var aPredecessors = scopes.avScheduling.getSchedulablePredecessors(rPredecessor, true);
        	
			if (aPredecessors.length > 0) {
				for (var p = 0; p < aPredecessors.length; p++) {
					if (!pushPredecessorBack(rPredecessor, aPredecessors[p])) {
			            return false;
					}
				}
			}
        }
        else {
            return false;
        }
    }

    return true;
}

/**
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"05895797-ED96-4973-86B0-ACB7B1781FD8"}
 */
function getDate(dDate){
	if(dDate){
		return dDate.getFullYear().toString() + utils.numberFormat(dDate.getMonth()+1, '00') + utils.numberFormat(dDate.getDate(), '00')  
	}
	// GD - 2014-02-12: Returning null to remove build marker
	return null;
}

/**
 * @param {Date} dDate
 *
 * @return
 * @properties={typeid:24,uuid:"FEC60351-4D00-466F-A348-D05A7DCC8759"}
 */
function getFormattedDate(dDate){
	if(dDate){
		return utils.numberFormat(dDate.getMonth()+1, '00') + '/' + utils.numberFormat(dDate.getDate(), '00') + '/' + dDate.getFullYear().toString()  
	}
	// GD - 2014-02-12: Returning null to remove build marker
	return null;
}

/**
 * @param {Boolean} [bUseDHTMLXFormat]
 * 
 * @return
 * @properties={typeid:24,uuid:"A0562463-FCA4-4E4C-AF5B-8687B277A5F3"}
 */
function getTimeFormat(bUseDHTMLXFormat) {
    var sTimeFormat = null;
    
    if (utils.hasRecords(_to_sys_employee$avbase_employeeuuid)) {
        sTimeFormat = _to_sys_employee$avbase_employeeuuid.empl_time_format_txt;
    }
    
    if (!sTimeFormat && utils.hasRecords(_to_sys_organization)) {
        sTimeFormat = _to_sys_organization.org_time_format_txt;
    }

    // if we couldn't get a time format from emp or org then default to military time
    if (!sTimeFormat) {
        sTimeFormat = 'HH:mm'; 
    }
    
    // convert to format used by dhtmlx scheduler
    if (bUseDHTMLXFormat) {
        if (sTimeFormat == 'h:mm a') {
            sTimeFormat = '%h:%i %A';
        }
        else {
            sTimeFormat = '%H:%i';
        }

        sTimeFormat = "'" + sTimeFormat + "'"
    }
    
    return sTimeFormat;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"0CFD7B4D-0E99-40DF-8077-7AA886F8FE54"}
 */
function onShow(firstShow, event)
{
    var dNow = new Date;
    
    if(_dLastOnShow && scopes.avDate.getDiffInSeconds(_dLastOnShow, dNow) < 2){
        return;
    }
    else{
        _dLastOnShow = dNow;
    }

    elements.chkLoadAllData.visible = globals.svy_sec_username == 'sysadmin';
    
    // default mode is week
	if (!_cur_mode) {
		_cur_mode = scopes.avScheduling.SCHEDULE_BOARD_MODE.week;
		_dCurDate = getFirstDayOfWeek();
	}

    controller.enabled = true;
    scopes.avScheduling.bInScheduleBoard = true;
    scopes.avScheduling.setActiveScheduler(globals.avBase_employeeUUID, 1);
    _bSchedEmps = scopes.avUtils.getSysPrefNum('ScheduleEmployees') == 1;
    elements.fldEmplID.visible = _bSchedEmps;
    
    if(!_zoomFactor){
        _zoomFactor = globals.getLblMsg('defaultTimeScale') 
    }
    
	loadCalendar();
    
    var startDate = new Date();
    startDate.setTime(startDate.getTime() + 2000);
    
	if (application.isInDeveloper()) {
		_pullPredecessors = 1;
		_pullSuccessors = 1;
	}
	
    loadView();
}

/**
 * @properties={typeid:24,uuid:"17B02FC4-**************-ED0206F2DB23"}
 */
function loadCalendar() {
	globals.avSchedule_html = 'loaded';
	
	// These options can be found in fullcalendar.io documentation
	var options = {
		height: '100%',
		navLinks: true,
		eventDurationEditable: false,
		slotDuration: '00:30',
		snapDuration: '00:05',
		headerToolbar:  {
			left: 'prev,next,today',
			center: 'title',
			right: 'resourceTimeGridDay,resourceTimelineDay,timeGridDay,timeGridWeek,dayGridMonth'
		},
		defaultView: 'timeGridWeek',
		initialView: 'timeGridWeek',
		views:{
			resourceTimeGridDay:{
				type: 'resourceTimeGrid',
				duration:{days:1},
				buttonText:"Resource",
				allDaySlot:false
			},
			resourceTimelineDay:{
				type: 'resourceTimeLine',
				duration:{days:1},
				buttonText:"Timeline",
				allDaySlot:false
			},
			timeGridDay:{
				type: 'timeGridDay',
				allDaySlot:false
			},
			timeGridWeek:{
				type: 'timeGridWeek',
				allDaySlot:false
			}
		},
	    initialDate: utils.dateFormat(new Date(), 'yyyy-MM-dd'),
		/* Dummy license key to be able to use the premium features, it must be replaced in production */ 
		schedulerLicenseKey: '**********-000-**********',
	    nowIndicator: true,
		eventSources: [{
			events: []
		}]

		
		
	};
	
	elements.calendar.fullCalendar(options);
}

/**
 * @param {Date} startTime
 * @param {Date} endTime
 * @param {String} title
 * @param {String} id
 * @param {String} tooltip
 * @param {String} resourceType
 * @param {String} resourceId
 * @param {String} predecessorId
 * @param {String} successorId
 * @param {String} color
 *
 * @return
 * @properties={typeid:24,uuid:"AE1646D9-0E4D-4C25-B180-0A1E7DD091B7"}
 */
function generateEventObject(startTime, endTime, title, id, tooltip, resourceType, resourceId, predecessorId, successorId, color){
	var vFormEvents = plugins.DateUtils.DateTimeFormatter();
	
	var editable = false;
	if (globals.nav.mode == 'edit'){
		editable= true;
	}
	return {
		editable: editable,
		resourceEditable: editable,
		start:vFormEvents.format(startTime) , end:vFormEvents.format(endTime),
		title:title, id:id.toString(), 
		resourceId: resourceId.toString(),
		backgroundColor: color,
		eventOverlap: function(stillEvent, movingEvent){
			if(stillEvent.display =="background"){
				return false;
			}
			return true;
		},
		extendedProps: 
		{
			tooltip:tooltip,
			resourceType: resourceType, 
			resourceId: resourceId.toString(),
			predecessorId:predecessorId,
			successorId:successorId
		}
	}
}

/**
 * @param {Array} aNewEvents
 *
 * @properties={typeid:24,uuid:"867D9302-FE57-4196-A6E5-FE6F45D3E5C7"}
 */
function reloadEvents(aNewEvents){
	var aNewIds = [];	
	var oldEvents = elements.calendar.getCalendarEvents();
	var i = 0;
	
	if (oldEvents) {
		for (i = 0; i < oldEvents.length; i++) {
			elements.calendar.removeEvent(oldEvents[i].id);
		}
	}
	
	for (i = 0; i < aNewEvents.length; i++) {
		elements.calendar.addEvent(aNewEvents[i]);
		aNewIds.push(aNewEvents[i].id.toString());
	}
	
	_aMS = aNewIds;
}

/**
 * @param {Array<UUID>} resources
 *
 * @properties={typeid:24,uuid:"89341FF6-7656-40BB-A1D2-40C734543561"}
 */
function updateCalendarResources(resources){

	/**@type {Array<CustomType<svy-fullcalendar2.ResourceObject>>} */
	var oldResources = elements.calendar.getResources();
	
	for (var i = 0; i < oldResources.length; i++) {

		elements.calendar.removeResource(oldResources[i].id.toString());
	}
	
	for (i = 0; i < resources.length; i++) {
		if (resources[i].id) {
			var newResource = { id: resources[i].id.toString(), title: resources[i].title, extendedProps: resources[i].extendedProps };
			elements.calendar.addResource(newResource);
		}
	}
	
	elements.calendar.refetchResources();

}

/**
 * @param {UUID} eventId
 * @param {Date} startTime
 * @param {Date} endTime
 *
 * @return
 * @properties={typeid:24,uuid:"E623BA30-FFCF-455F-A043-33CAAC41F5A4"}
 */
function updateEventStartEndTime(eventId, startTime, endTime){
	/**@type {CustomType<svy-fullcalendar2.EventObject>} */
	var event = elements.calendar.getEventById(eventId);
	
	event.start = startTime;
	event.end = endTime;
	
	return event;
}

/**
 * @param {UUID|String} eventId
 * @param {UUID|String} resourceId
 *
 * @return
 * @properties={typeid:24,uuid:"392DA1A4-C6B4-4EE2-A9A1-894301E5CDE0"}
 */
function updateEventResource(eventId, resourceId){
	/**@type {CustomType<svy-fullcalendar2.EventObject>} */
	var event = elements.calendar.getEventById(eventId);
	var newEvent = {
		editable: event.editable,
		start:event.start , end:event.end,
		title:event.title, id:event.id.toString(), 
		resourceId: resourceId,
		backgroundColor: event.backgroundColor,
		extendedProps: event.extendedProps
	}

	elements.calendar.removeEvent(eventId);
	elements.calendar.addEvent(newEvent);
	elements.calendar.refetchEvents();
	
	return event;
}

/**
 * @param {Boolean} editable
 *
 * @properties={typeid:24,uuid:"536EE149-50A4-488D-9BA0-8C4D7D082DCE"}
 */
function setCalendarEventsEditable(editable){

	//there is a bug in full calendar component that event does not contains editable info in the event creation, 
	//so I have to replace the whole event instead

	/**@type {Array<CustomType<svy-fullcalendar2.EventObject>>} */
	var events = elements.calendar.getCalendarEvents();
	
	for(var i = 0; i< events.length; i++){
		//skip exception background events
		if(events[i].display=='background'){
			continue;
		}
		
		var id = events[i].id.toString();

		var newEvent = {
			editable: editable,
			resourceEditable: editable,
			start:events[i].start , end:events[i].end,
			title:events[i].title, id:events[i].id.toString(), 
			resourceId: events[i].extendedProps.resourceId,
			backgroundColor: events[i].backgroundColor,
			extendedProps: events[i].extendedProps
		}

		elements.calendar.removeEvent(id);
		elements.calendar.addEvent(newEvent);
	}	
	elements.calendar.refetchEvents();

}

/**
 * Perform the element default action.
 * 
 * @param {Boolean} [bLoadAllTime]
 *
 * @properties={typeid:24,uuid:"C331BDB9-1069-4B6F-8D59-475E4DDB1D97"}
 */
function loadView(bLoadAllTime) {
	scopes.avUtils.stopWatchStart("loadView");

	if (globals.svy_sec_username == 'sysadmin' && elements.chkLoadAllData.visible && _nDebugLoadAllData) {
		bLoadAllTime = true
	}
    
	_bLoadedForAllTime = bLoadAllTime;
	
	if (!globals.avSchedule_html){
    	loadCalendar();
	}

	
    // default mode is week
	if (!_cur_mode) {
		_cur_mode = scopes.avScheduling.SCHEDULE_BOARD_MODE.week;
		_dCurDate = getFirstDayOfWeek();
	}
	
	_sLastLoadViewMode = _cur_mode;
	_dLastLoadViewDate = _dCurDate;

	// Get the formatter
	var vFormEvents = plugins.DateUtils.DateTimeFormatter();
	var aRes = [];
	var aResType = [];
	var rUnit;
	var rResSch;
	var dToday = application.getTimeStamp();
	var sColor = "";
	var i, k;
	var sCust;
	var sJobName;
	var sSalesMan;
	var sPriority;
	/**@type {String} */
	var sSectionDesc;
	var sTaskDesc;
	var sFuncDesc;
	var sOperName;
	var sResource;
	/**@type {Number} */
	var hrs = 0;
	/**@type {Number} */
	var mins = 0;
    var dStartDate = null;
    var dEndDate = null;
    var sSectionColor1 = null; // "rgb(77, 130, 184)"; // blue #4d82b8
    var sSectionColor2 = null; // "rgb(118, 167, 151)"; // green #7a797
    var sSectionColor3 = null; // "rgb(237, 200, 126)"; // yellow #edc87e
	var sWorkTemplate = null;
	var sWorkType = null;
	
	if (!bLoadAllTime) {
		setDates();
	}
    
	/// initialize these
	_aUnits = [];
	_aJSON = [];
	_aExcep = [];
	_aDatesNonShiftTimeExceptionsAddedFor = [];
	aMilestoneExcep = [];
	var resourceList = [];
	
    vFormEvents.dateTimePattern = 'yyyy-MM-dd HH:mm';
	
	if (_to_sch_view$avschedule_selectedviewid && _to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource) {
		for (i = 1; i <= _to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource.getSize(); i++) {
			//  for the Units view
			rUnit = _to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource.getRecord(i);
			sResource = rUnit.clc_resource_name;
			
			if (sResource){
				sResource = sResource.replace(/\//g,"_");
				sResource = sResource.replace(/\\/g,"_");
				sResource = sResource.replace(/\'/g,"");
				sResource = sResource.replace(/\"/g,"");
				sResource = sResource.replace(/\n/g,"");
				sResource = sResource.replace(/\r/g,"");
				sResource = sResource.replace(/\r\n/g,"");
			}
			
			_aUnits.push('{key:' + i + ', label:"' + sResource + '"}');
			var resource;
			
			if (rUnit.empl_id){		    
			    processEmpResource();
			    resource = {id: rUnit.empl_id, title: sResource, extendedProps:{resourceType: scopes.avScheduling.RESOURCE_TYPE.Employee}};
			}
			else{		
			    processEquipResource();		    
			    resource = {id: rUnit.equip_id, title: sResource, extendedProps:{resourceType: scopes.avScheduling.RESOURCE_TYPE.Equipment}};
			}
			resourceList.push(resource);
		}
	}

	// Save the data for updates
	_aRes = aRes;
	_aResType = aResType;
	
	bCancelled = false;
	scopes.avScheduling.clearRefreshScheduleFlag(globals.avBase_employeeUUID);
	
	updateCalendarResources(resourceList);
	addNonShiftTimeExceptonsForAllResourcesForThisDate(elements.calendar.getDate());

	reloadEvents(_aJSON);
	
	scopes.avUtils.devOutputTime("loadView");
	
	function setDates() {
		var eMode = scopes.avScheduling.SCHEDULE_BOARD_MODE; 

		// sl-22011 - customer had a problem where it was only showing june jobs for jul, indicating maybe the board thought it was a different month. also day views not showing 
		// expected jobs. i couldnt duplicate either issue, but this tries to fix them by loading the prev/next day/week/month too.
		switch (_cur_mode) {
			case eMode.day: 
				dStartDate = plugins.DateUtils.addDays(_dCurDate, -1);
				dEndDate = plugins.DateUtils.addDays(_dCurDate, 1);
				break;
			case eMode.resource: 
				dStartDate = plugins.DateUtils.addDays(_dCurDate, -1);
				dEndDate = plugins.DateUtils.addDays(_dCurDate, 1);
				break;
			case eMode.timeline: 
				dStartDate = plugins.DateUtils.addDays(_dCurDate, -1);
				dEndDate = plugins.DateUtils.addDays(_dCurDate, 1);
				break;
			case eMode.week: 
				var dPrevWeekDate = plugins.DateUtils.addWeeks(_dCurDate, -1);
				var dNextWeekDate = plugins.DateUtils.addWeeks(_dCurDate, 1);			

				dStartDate = dPrevWeekDate;
				dEndDate = scopes.avDate.addDaysNoDSTChange(dNextWeekDate, 6);
				break;
			case eMode.month: 
				// sl-22011 - the month view may show a few days of the prev and next month, so load data for those months too, so we see jobs on those days.
				var dPrevMonthDate = plugins.DateUtils.addMonths(_dCurDate, -1);
				var dNextMonthDate = plugins.DateUtils.addMonths(_dCurDate, 1);			
				var nNumDaysInMonth = scopes.avDate.getNumDaysInMonth(dNextMonthDate);
				
				dStartDate = dPrevMonthDate;
				dEndDate = scopes.avDate.addDaysNoDSTChange(dNextMonthDate, nNumDaysInMonth-1);
				
				break;
		}
	}
	
	function processEquipResource(){
    
        // Save the resource for event updating
        aRes.push(rUnit.equip_id);
        aResType.push(1);
        
		var fsEquip = scopes.avScheduling.getEquipSchedule(rUnit.equip_id, dStartDate, dEndDate);
        
        if (fsEquip)
        {
            for (k = 1; k <= fsEquip.getSize(); k++)
            {
                rResSch = fsEquip.getRecord(k);
                sCust = null;
                sJobName = null;
                sSalesMan = null;
                sPriority = null;
                sWorkType = null;
                sWorkTemplate = null;
                sOperName = null;
                sFuncDesc = null;
                sTaskDesc = null;
                
                var rMilestone = rResSch.sch_equip_schedule_to_sch_milestone.getRecord(1);
                if (!rMilestone) {
                	continue;
                }
                var rSchedule = rMilestone.sch_milestone_to_sch_schedule.getRecord(1);
                if (!rSchedule) {
                	continue;
                }
                var rJob = rSchedule.sch_schedule_to_prod_job.getRecord(1);
                if (!rJob) {
                	continue;
                }
                var rLineItem = null;
                
				if (utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail)) {
					rLineItem = rJob.prod_job_to_sa_order_revision_detail.getRecord(1);
				}
				else if (utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail$reservation)) {
					rLineItem = rJob.prod_job_to_sa_order_revision_detail$reservation.getRecord(1);
				}
                
                var rOrder = null;
                if (rLineItem) {
                	rOrder = rLineItem.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.getRecord(1); 
                }
    			var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
    			
    			sWorkType = getWorkType(rJob);    			
    			sWorkTemplate = getWorkTemplate(rJob);
                
                if(rOrder && (utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer') || 
                		utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer'))){
                    sCust = rOrder.sa_order_to_sa_customer.cust_name;
                }
                sJobName = rJob.job_description;
                if(utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person')){
                    sSalesMan = rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person.salesper_name;
                }
                else if(utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person')){
                    sSalesMan = rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person.salesper_name;
                }
                sPriority = rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_priority;
                
                sSectionDesc = scopes.avDB.getDeepRelVal(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sa_order_revision_detail_section', 'ordrevds_description'); // sl-5035
                
                if(utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas')){
                    sFuncDesc = rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc;
                }
                if(utils.hasRecords(rResSch, 'sch_equip_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task')){
                    sTaskDesc = rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.task_description;
                }
                sOperName = rResSch.sch_equip_schedule_to_sch_milestone.ms_oper_name;
                
                removeIllegalCharacters();
                
                // caprec is broken - for debugging purposes
                if (scopes.avScheduling.inDebugMode() && scopes.avScheduling.isCapRecBroken(rMilestone)) {
                    sColor = "#fc03db";//"rgb(252, 3, 219)"; // violet
                }
                // for debugging purposes
                else if (application.isInDeveloper() && sSectionColor1 && rSection.sequence_nr == 1) {
                    sColor = sSectionColor1;
                }
                // for debugging purposes
                else if (application.isInDeveloper() && sSectionColor2 && rSection.sequence_nr == 2) {
                    sColor = sSectionColor2;
                }
                // for debugging purposes
                else if (application.isInDeveloper() && sSectionColor3 && rSection.sequence_nr == 3) {
                    sColor = sSectionColor3;
                }
                // late
                else if (rResSch.sch_equip_schedule_to_sch_milestone.ms_flg_completed != 1 && rResSch.equipsch_end && rResSch.equipsch_end < dToday) {
                    sColor = "#d86344";//"rgb(216, 99, 68)"; // red
                }
                // at risk?
                else if (scopes.avScheduling.isMilestoneAtRisk(rMilestone)) {
                    sColor = "#edc87e";//"rgb(237, 200, 126)"; // yellow
                }
                // reservation job - goes before 'ready'
                else if (rJob.job_is_reservation) {
                    sColor = '#808080';//rgb(128, 128, 128)';
                }
                // ready
                else if (rMilestone.ms_flg_ready) {
                    sColor = "#74a797";//"rgb(118, 167, 151)"; // green
                }
                // job color
                else if (rJob.job_color) {
                    sColor = rJob.job_color;
                }
                // def color
                else {
                    sColor = '#4d82b8';//"rgb(77, 130, 184)"; // "#4d82b8" // blue
                }
                
                hrs = Math.floor(rResSch.sch_equip_schedule_to_sch_milestone.ms_time_budget / 60).toFixed(0);
                hrs = scopes.avUtils.padNumber(hrs,2);
                mins = Math.round((rResSch.sch_equip_schedule_to_sch_milestone.ms_time_budget) - (hrs*60)).toFixed(0);
                mins = scopes.avUtils.padNumber(mins,2);
                
                if (rResSch.equipsch_end && rResSch.equipsch_start){
                    var tooltip = "Job Name: " + rResSch.sch_equip_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_number +"\n"
	        			+ "Job No: " + sJobName +"\n"
	        			+"Customer: " + sCust +"\n"
	        			+"Salesman: " + sSalesMan +"\n"
	        			+"Section: " + sSectionDesc +"\n"
	        			+"Operation: " + sOperName +"\n"
	        			+"Task Desc: " + sTaskDesc +"\n"
	        			+"Priority: " + sPriority +"\n"
	        			+"Sequence: " + rMilestone.sequence_nr +"\n"
	        			+"Start Date: " + vFormEvents.format(rResSch.equipsch_start) +"\n"
	        			+"End Date: " + vFormEvents.format(rResSch.equipsch_end) +"\n"
	        			+"Duration: " + hrs + ":" + mins + " (hh:mm)" +"\n";        
                    var dataObj = generateEventObject(rResSch.equipsch_start, rResSch.equipsch_end, sOperName, rResSch.ms_id.toString(), 
                    	tooltip, scopes.avScheduling.RESOURCE_TYPE.Equipment, rUnit.equip_id, 
						rMilestone.clc_predecessors, scopes.avScheduling.getSuccessorID(rMilestone), sColor);
                            
                    _aJSON.push(dataObj);
                }
            }
        }
        
	}
	
	function processEmpResource(){    
	    
        // Save the resource for event updating
        aRes.push(rUnit.empl_id);
        aResType.push(0);

		var fsEmpSched = scopes.avScheduling.getEmpSchedule(rUnit.empl_id, dStartDate, dEndDate, rUnit.dept_id, rUnit.equip_id);
        
        if (utils.hasRecords(fsEmpSched)){
            for (k = 1; k <= fsEmpSched.getSize(); k++){
                rResSch = fsEmpSched.getRecord(k);
                sCust = null;
                sJobName = null;
                sSalesMan = null;
                sPriority = null;
                sWorkType = null;
                sWorkTemplate = null;
                sOperName = null;
                sFuncDesc = null;
                sTaskDesc = null;

                var rMilestone = rResSch.sch_empl_schedule_to_sch_milestone.getRecord(1);
                if (!rMilestone) {
                	continue;
                }
                var rSchedule = rMilestone.sch_milestone_to_sch_schedule.getRecord(1);
                if (!rSchedule) {
                	continue;
                }
                var rJob = rSchedule.sch_schedule_to_prod_job.getRecord(1);
                if (!rJob) {
                	continue;
                }
                var rLineItem = null;
                
				if (utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail)) {
					rLineItem = rJob.prod_job_to_sa_order_revision_detail.getRecord(1);
				}
				else if (utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail$reservation)) {
					rLineItem = rJob.prod_job_to_sa_order_revision_detail$reservation.getRecord(1);
				}
                
				var rOrder = null;
				if (rLineItem) {
					rOrder = rLineItem.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.getRecord(1);
				}
                var nHasEquip = 0;
    			var rSection = rMilestone.sch_milestone_to_sa_order_revision_detail_section.getRecord(1);
    			
    			sWorkType = getWorkType(rJob);
    			sWorkTemplate = getWorkTemplate(rJob);
                
                if(utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)){
                    nHasEquip = 1;
                }                
                
                if(rOrder && (utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer') || 
                		utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_customer'))){
                    sCust = rOrder.sa_order_to_sa_customer.cust_name;
                }
            
                sJobName = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_description;
                
                if(utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person')){
                    sSalesMan = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person.salesper_name;
                }
                else if(utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person')){
                    sSalesMan = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail$reservation.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_sales_person.salesper_name;
                }
                
                sPriority = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_priority;
                sSectionDesc = scopes.avDB.getDeepRelVal(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sa_order_revision_detail_section', 'ordrevds_description'); // sl-5035
                
                if(utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas')){
                    sFuncDesc = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.sa_task_to_sa_task_standard.sa_task_standard_to_sys_task_functional_areas.systaskfunc_desc;
                }
                if(utils.hasRecords(rResSch, 'sch_empl_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task')){
                    sTaskDesc = rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sa_order_revds_task.sa_order_revds_task_to_sa_task.task_description;
                }

                sOperName = rResSch.sch_empl_schedule_to_sch_milestone.ms_oper_name;
                
                removeIllegalCharacters();
                
                // for debugging purposes
                if (application.isInDeveloper() && sSectionColor1 && rSection.sequence_nr == 1) {
                    sColor = sSectionColor1;
                }
                // for debugging purposes
                else if (application.isInDeveloper() && sSectionColor2 && rSection.sequence_nr == 2) {
                    sColor = sSectionColor2;
                }
                // for debugging purposes
                else if (application.isInDeveloper() && sSectionColor3 && rSection.sequence_nr == 3) {
                    sColor = sSectionColor3;
                }
                // late
                else if (rMilestone.ms_flg_completed != 1 && rResSch.emplsch_end && rResSch.emplsch_end < dToday)
                {
                    sColor = "#d86344";//"rgb(216, 99, 68)"; // red
                }
                // at risk?
                else if(scopes.avScheduling.isMilestoneAtRisk(rMilestone)){
                    sColor = "#edc87e";//"rgb(237, 200, 126)"; // yellow
                }
                // reservation job - goes before 'ready'
                else if (rJob.job_is_reservation) {
                    sColor = "#808080";//'rgb(128, 128, 128)';
                }
                // ready
                else if(rMilestone.ms_flg_ready){
                    sColor = "#76a797";//"rgb(118, 167, 151)"; // green
                }
                // job color
                else if (rJob.job_color) 
                {
                    sColor = rJob.job_color;
                }
                // def color
                else
                {
                    sColor = "#9868b9";//"rgb(152, 104, 185)"; // "#9868b9" // purple
                }
                
                hrs = Math.floor(rResSch.sch_empl_schedule_to_sch_milestone.ms_time_budget / 60).toFixed(0);
                hrs = scopes.avUtils.padNumber(hrs,2);
                mins = Math.round((rResSch.sch_empl_schedule_to_sch_milestone.ms_time_budget) - (hrs*60)).toFixed(0);
                mins = scopes.avUtils.padNumber(mins,2);
                
                if (rResSch.emplsch_end && rResSch.emplsch_start){
                    var tooltip = "Job Name: " + rResSch.sch_empl_schedule_to_sch_milestone.sch_milestone_to_sch_schedule.sch_schedule_to_prod_job.job_number +"\n"
						+ "Job No: " + sJobName +"\n"
						+"Customer: " + sCust +"\n"
						+"Salesman: " + sSalesMan +"\n"
						+"Section: " + sSectionDesc +"\n"
						+"Operation: " + sOperName +"\n"
						+"Task Desc: " + sTaskDesc +"\n"
						+"Priority: " + sPriority +"\n"
						+"Sequence: " + rMilestone.sequence_nr +"\n"
						+"Start Date: " + vFormEvents.format(rResSch.emplsch_start) +"\n"
						+"End Date: " + vFormEvents.format(rResSch.emplsch_end) +"\n"
						+"Duration: " + hrs + ":" + mins + " (hh:mm)" +"\n";
					
                    var dataObj = generateEventObject(rResSch.emplsch_start, rResSch.emplsch_end, sOperName, rResSch.ms_id.toString(),
                    	tooltip, scopes.avScheduling.RESOURCE_TYPE.Employee, rUnit.empl_id, rMilestone.clc_predecessors, 
						scopes.avScheduling.getSuccessorID(rMilestone), sColor);
                    
                    _aJSON.push(dataObj);
                }
            }
        }
        
	}
	
	/**
	 * removes illegal characters
	 *
	 * <AUTHOR> Dotzlaw
	 * @since 2013-03-28
	 *
	 */
	function removeIllegalCharacters() {
		sCust = removeIllegalCharactersFromField(sCust);
		sJobName = removeIllegalCharactersFromField(sJobName);
		sSalesMan = removeIllegalCharactersFromField(sSalesMan);
		sSectionDesc = removeIllegalCharactersFromField(sSectionDesc);
		sFuncDesc = removeIllegalCharactersFromField(sFuncDesc);
		sTaskDesc = removeIllegalCharactersFromField(sTaskDesc);
		sOperName = removeIllegalCharactersFromField(sOperName);
		sWorkTemplate = removeIllegalCharactersFromField(sWorkTemplate);
		sWorkType = removeIllegalCharactersFromField(sWorkType);
	}

	/**
	 * @param {String} sField
	 */
	function removeIllegalCharactersFromField(sField) {
		if (sField) {
			sField = sField.replace(/\//g, "_");
			sField = sField.replace(/\\/g, "_");
			sField = sField.replace(/\'/g, "");
			sField = sField.replace(/\"/g, "");
			sField = sField.replace(/\n/g, "");
			sField = sField.replace(/\r/g, "");
			sField = sField.replace(/\r\n/g, "");
		}
		
		return sField;
	}
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/prod_job>} rJob
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"4EDC40E1-858B-43CA-A14B-B0A6F20BAD85"}
 */
function getWorkType(rJob) {
	var sWorkType = "";
	
	if (rJob && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail) && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sys_task_worktype)) {
		sWorkType = rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sys_task_worktype.sysworktype_code;
	}
	
	return sWorkType;
}

/**
 * @public 
 * 
 * @param {JSRecord<db:/avanti/prod_job>} rJob
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C7BD8B71-D553-4AD5-A803-2F6605CCC99F"}
 */
function getWorkTemplate(rJob) {
	var sWorkTemplate = "";
	
	if (rJob && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail) && utils.hasRecords(rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_task_worktype)) {
		sWorkTemplate = rJob.prod_job_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_task_worktype.worktype_code
	}
	
	return sWorkTemplate;
}

/**
 * @param {Date} dDate
 * @param {Boolean} [bComingFromAvailCheckbox]
 *
 * @properties={typeid:24,uuid:"D9C60121-40AC-47C3-B91A-7628A8DBB9EE"}
 */
function addNonShiftTimeExceptonsForAllResourcesForThisDate(dDate, bComingFromAvailCheckbox){	
	// always remove time portion 
	if (bComingFromAvailCheckbox) {
		dDate.setHours(0, 0, 0, 0);
	}
	else {
		dDate = new Date(dDate);
		dDate = scopes.avDate.convertServerTimeToClientTime(dDate);
		dDate.setHours(0, 0, 0, 0);				
	}

	_dCurDate = dDate; 
	_aExcepNew = []
	
	if (utils.hasRecords(_to_sch_view$avschedule_selectedviewid) 
	    && utils.hasRecords(_to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource)){
		    
		var iNumRecs = _to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource.getSize()
		for (var i = 1; i <= iNumRecs; i++){
			var rUnit = _to_sch_view$avschedule_selectedviewid.sch_view_to_sch_view_resource.getRecord(i);
			if (rUnit.empl_id){		    
				addNonShiftTimeExceptons(rUnit, rUnit.empl_id.toString(), dDate);
			    
			}
			else{		
				
				addNonShiftTimeExceptons(rUnit, rUnit.equip_id.toString(), dDate);	    
			    
			}
		}		
		
		if(_aExcepNew.length > 0){
			updateBackgroundExceptions(_aExcepNew);
		}
	}
}

/**
 * @param {Array} events
 *
 * @properties={typeid:24,uuid:"01B4E231-14B7-4DFD-9FFA-04B69C8CC029"}
 */
function updateBackgroundExceptions(events){
	/**@type {Array<CustomType<svy-fullcalendar2.EventObject>>} */
	var oldEvents = elements.calendar.getCalendarEvents();
	if(!oldEvents){
		return;
	}
	//remove old except background events
	for(var i = 0; i< oldEvents.length; i++){
		if(oldEvents[i].display =="background"){
			elements.calendar.removeEvent(oldEvents[i].id);
		}
	}
	
	/**@type { CustomType<svy-fullcalendar2.ViewType>} */
	var viewType = elements.calendar.getView();
	
	//if it is resource view or timeline view, add exceptions as backgound events
	if(viewType.type == "resourceTimeGridDay"||viewType.type ==  "resourceTimelineDay"){
		for(i = 0; i< events.length; i++){
			var newEvent = createBackgroundException(events[i].start_date, events[i].end_date,events[i].resource_id, events[i].excepid);
			
			elements.calendar.addEvent(newEvent);
		}
	}	
}

/**
 * @param {Array} events
 *
 * @properties={typeid:24,uuid:"1A5755E6-7F25-448F-A016-04825B49DB17"}
 */
function clearBackgroundExceptions(){
	/**@type {Array<CustomType<svy-fullcalendar2.EventObject>>} */
	var oldEvents = elements.calendar.getCalendarEvents();
	if(!oldEvents){
		return;
	}
	//remove old except background events
	for(var i = 0; i< oldEvents.length; i++){
		if(oldEvents[i].display =="background"){
			elements.calendar.removeEvent(oldEvents[i].id);
		}
	}	
}

/**
 * @return
 * @properties={typeid:24,uuid:"45566D10-3C24-4A69-BACD-419650F6E08B"}
 */
function createBackgroundException(startTime, endTime, resourceId, id){
	var vFormEvents = plugins.DateUtils.DateTimeFormatter();
	return {
		editable: false,
		resourceEditable: false,
		start:vFormEvents.format(startTime) , end:vFormEvents.format(endTime),
		title:"",
		id:id.toString(), 
		resourceId: resourceId.toString(),
		display:'background',
		backgroundColor: "#a0a0a0"
	}
}

/**
 * SL-1935 - PUT RESOURCE SHIFTS ON SCHED BOARD
 * 
 * @param {JSRecord<db:/avanti/sch_view_resource>} rUnit
 * @param {String} section_id
 * @param {Date} [dDate]
 *
 * @properties={typeid:24,uuid:"86F545B5-65B6-42F7-83A9-497B9D2974D1"}
 */
function addNonShiftTimeExceptons(rUnit, section_id, dDate){
    var dCurDate = dDate ? dDate : application.getTimeStamp();
    var rShiftDetail;
    var sStartMins;
    var sEndMins;
    /***@type {Array<String>} */
    var aShifts = [];
    /***@type {Array<String>} */
    var aUnavailExceps = [];
    
    // EMP
    if(utils.hasRecords(rUnit.sch_view_resource_to_sys_employee)){
        var rEmp = rUnit.sch_view_resource_to_sys_employee.getRecord(1);

        // emp shifts
        rShiftDetail = scopes.avScheduling.getShiftDetailForEmp(rEmp, dDate);
        
        if(rShiftDetail){
            sStartMins = utils.numberFormat(rShiftDetail.shiftdet_starttime_num_mins, '0000');
            sEndMins = utils.numberFormat(rShiftDetail.shiftdet_endtime_num_mins, '0000');
            aShifts.push(sStartMins + '-' + sEndMins);
            
            // emp shift exceptions
            aShifts = aShifts.concat(scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 1, null, rUnit.empl_id));
    
            // unavailable exceptions - shift + emp 
            aUnavailExceps = scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 0, null, rUnit.empl_id);
        }
        // sl-27565 - if no rShiftDetail works this day then see if we have any available exceptions for this date  
        else {
	        rShiftDetail = scopes.avScheduling.getShiftDetailForEmp(rEmp, dDate, true);
	        
			if (rShiftDetail) {
				aShifts = scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 1, null, rUnit.empl_id);
			}
        }
    }
    
    // EQUIP
    else if(utils.hasRecords(rUnit.sch_view_resource_to_eq_equipment)){
        var rEquip = rUnit.sch_view_resource_to_eq_equipment.getRecord(1);
        
        // equip shifts
        rShiftDetail = scopes.avScheduling.getShiftDetailForEquip(rEquip, dDate);

        if(rShiftDetail){
            sStartMins = utils.numberFormat(rShiftDetail.shiftdet_starttime_num_mins, '0000');
            sEndMins = utils.numberFormat(rShiftDetail.shiftdet_endtime_num_mins, '0000');
            aShifts.push(sStartMins + '-' + sEndMins);

            // equip shift exceptions
            aShifts = aShifts.concat(scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 1, rUnit.equip_id));

            // unavailable exceptions - shift + equip 
            aUnavailExceps = scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 0, rUnit.equip_id);
        }
        // sl-27565 - if no rShiftDetail works this day then see if we have any available exceptions for this date  
        else {
	        rShiftDetail = scopes.avScheduling.getShiftDetailForEquip(rEquip, dDate, true);
	        
			if (rShiftDetail) {
				aShifts = scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dDate, 1, rUnit.equip_id);
			}
        }
    }

    if(aUnavailExceps && aUnavailExceps.length > 0){            
        for(var u=0;u<aUnavailExceps.length;u++){
            var aExcepMins = aUnavailExceps[u].split('-')
            /***@type {Number} */
            var nExcepStartMins = aExcepMins[0] 
            /***@type {Number} */
            var nExcepStopMins = aExcepMins[1] 

            _aExcepNew.push(createNonShiftTimeException(dDate, nExcepStartMins, nExcepStopMins, section_id));
        }
    }
    
	if(aShifts.length > 0){
		//if the range pass midnight, stop shift at midnight
		aShifts.forEach( function (i,j) {
		    var aShiftParts = aShifts[j].split('-');
		    
		    //if pass midnight, set stop time at midnight
		    if ( aShiftParts[0] > aShiftParts[1] ) {
		        aShiftParts[1] = 24*60;
		    }
		    
		    aShifts[j] = aShiftParts.join("-");
		});
		
		//now search for all shifts from yesterday that pass midnight
		/***@type {Array<String>} */
		var aYesterdayShifts = [];
		/***@type {Array<String>} */
		var aYesterdayOvernightShifts = [];
		var dYesterday = plugins.DateUtils.addDays(dCurDate, -1);
		
	    if(utils.hasRecords(rUnit.sch_view_resource_to_sys_employee)){
	        // emp shifts
	        rShiftDetail = scopes.avScheduling.getShiftDetailForEmp(rEmp, dYesterday);

	        if(rShiftDetail){
	            sStartMins = utils.numberFormat(rShiftDetail.shiftdet_starttime_num_mins, '0000');
	            sEndMins = utils.numberFormat(rShiftDetail.shiftdet_endtime_num_mins, '0000');
	            aYesterdayShifts.push(sStartMins + '-' + sEndMins);
	            
	            // emp shift exceptions
	            aYesterdayShifts = aYesterdayShifts.concat(scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dYesterday, 1, null, rUnit.empl_id));
	        }
	    }
	    // EQUIP
	    else if(utils.hasRecords(rUnit.sch_view_resource_to_eq_equipment)){
	        // equip shifts
	        rShiftDetail = scopes.avScheduling.getShiftDetailForEquip(rEquip, dYesterday);

            if(rShiftDetail){
                sStartMins = utils.numberFormat(rShiftDetail.shiftdet_starttime_num_mins, '0000');
                sEndMins = utils.numberFormat(rShiftDetail.shiftdet_endtime_num_mins, '0000');
                aYesterdayShifts.push(sStartMins + '-' + sEndMins);

                // equip shift exceptions
                aYesterdayShifts = aYesterdayShifts.concat(scopes.avScheduling.getExceptionsForDate(rShiftDetail.shift_id, dYesterday, 1, rUnit.equip_id));
            }
	    }
		
		//if the range pass midnight, start shift at midnight
		aYesterdayShifts.forEach( function (i,j) {
		
		    var aShiftParts = aYesterdayShifts[j].split('-');
		    
		    //if pass midnight, set stop time at midnight
		    if ( aShiftParts[0] > aShiftParts[1] ) {
		        aShiftParts[0] = 0;
			    aYesterdayOvernightShifts.push(aShiftParts.join("-"));
		    }
		});	
		
		//now concat yesterday's shifts that pass midnight too 
		aShifts = aShifts.concat(aYesterdayOvernightShifts);
		
		if(aShifts && aShifts.length >0){			
			aShifts.sort() // sort shifts so the ones earlier in day come fst
			/***@type {Number} */
			var nLastShiftEndMins = getEndTimeOfLastShift(aShifts) // sl-2203
			var nFirstShiftStartMins = getStartTimeOfFirstShift(aShifts)
			var nLastMinuteOfTheDay = 24*60

			// sl-3131 - see if we have some non-shift time between start of day and start of fst shift
			if(nFirstShiftStartMins > 0 && nFirstShiftStartMins != nLastShiftEndMins){
				if(nLastShiftEndMins < nFirstShiftStartMins){
					_aExcepNew.push(createNonShiftTimeException(dDate, nLastShiftEndMins, nFirstShiftStartMins, section_id));
				}
				else{
					_aExcepNew.push(createNonShiftTimeException(dDate, 0, nFirstShiftStartMins, section_id));
				}
			}

			for(var s=0;s<aShifts.length;s++){
				var aShiftMins = aShifts[s].split('-') 
				/***@type {Number} */
				var nShiftStartMins = aShiftMins[0] 
				/***@type {Number} */
				var nShiftStopMins = aShiftMins[1] 
				if(nShiftStartMins > nLastShiftEndMins){ // we have a gap between shifts - this is non-shift-time - an exception needs to be created for it
					_aExcepNew.push(createNonShiftTimeException(dDate, nLastShiftEndMins, nShiftStartMins, section_id));
				}
				nLastShiftEndMins = nShiftStopMins 
			}
			
			// see if there is any space between end of last shift and midnights
			if(nShiftStopMins==0){
				nShiftStopMins = nLastMinuteOfTheDay
			}
			
			// sl-2203 - added 'nShiftStartMins < nShiftStopMins' option - in this case the last shift goes around midnight - so not gap
			if(nShiftStopMins < nLastMinuteOfTheDay && nShiftStartMins <= nShiftStopMins){
				_aExcepNew.push(createNonShiftTimeException(dDate, nShiftStopMins, nLastMinuteOfTheDay, section_id));
			}
		}
		else{
			_aExcepNew.push(createNonShiftTimeException(dDate, 0, 1439, section_id)) // sl-2203 - create non shift exception to day where no shifts are available
		}
	}
	// no shift for this day
	else {
		_aExcepNew.push(createNonShiftTimeException(dDate, 0, 1440, section_id));
	}
}

/**
 * @param {Array<String>} aShifts
 *
 * @return
 * @properties={typeid:24,uuid:"093EB585-FC60-4E3B-BFA6-96D059C269D9"}
 */
function getEndTimeOfLastShift(aShifts){
	var aShiftMins = aShifts[aShifts.length-1].split('-') 
	var nShiftStopMins = aShiftMins[1] 
	return nShiftStopMins
}

/**
 * @param {Array<String>} aShifts
 *
 * @return
 * @properties={typeid:24,uuid:"EE5C98A6-7FA5-4968-B0EE-31D560DCEE49"}
 */
function getStartTimeOfFirstShift(aShifts){
	var aShiftMins = aShifts[0].split('-') 
	var nShiftStartMins = aShiftMins[0] 
	return nShiftStartMins
}

/**
 * @param {Date} dDate
 * @param {Number} nNonShiftTimeStartMins
 * @param {Number|String} nNonShiftTimeStopMins
 * @param {String} section_id
 *
 * @return
 * @properties={typeid:24,uuid:"EAA950EA-3BA3-47ED-BE06-5FC54E440EBA"}
 */
function createNonShiftTimeException(dDate, nNonShiftTimeStartMins, nNonShiftTimeStopMins, section_id){
	var dNonShiftStartDate = new Date(dDate.getTime())						
	dNonShiftStartDate.setHours(0, 0, 0, 0) // set to midnight
	dNonShiftStartDate = plugins.DateUtils.addMinutes(dNonShiftStartDate, nNonShiftTimeStartMins)

	var dNonShiftStopDate = new Date(dDate.getTime())						
	dNonShiftStopDate.setHours(0, 0, 0, 0) // set to midnight
	dNonShiftStopDate = plugins.DateUtils.addMinutes(dNonShiftStopDate, nNonShiftTimeStopMins)
	
	// all non-shift exceptions are done 1 day at a time - if adding the minutes has pushed us into a different day by 59 mins that means we tried to hit end of previous day 
	// but Daylight Saving Time pushed us inot next day. just go back 60 mins
	if(dNonShiftStopDate.getDay() != dNonShiftStartDate.getDay() && dNonShiftStopDate.getMinutes() == 59){
		dNonShiftStopDate = plugins.DateUtils.addMinutes(dNonShiftStopDate, -60)
	}

		var sData = { start_date:dNonShiftStartDate, 
			end_date:dNonShiftStopDate,
			resource_id:section_id ,
			excepid:application.getUUID().toString() 
		}	

		
	return sData
}

/**
 * Refreshes the UI
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-02-12
 *
 *
 * @properties={typeid:24,uuid:"6B2371AC-4B49-4046-8A39-B054FA7E67CB"}
 */
function refreshUI()
{
	// Show the proper edit view fields
	var rViewResource = forms.sch_view_resources_tbl.foundset.getSelectedRecord();
	if (rViewResource)
	{
        _equipID = rViewResource.equip_id;
        _emplID = rViewResource.empl_id;
		
		// Set the VL with the necessary data
		scopes.avVL.load_avSchedule_viewResourceEquip();
        scopes.avVL.load_avSchedule_viewResourceEmpl();
	}
	
	
}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"8368BF7B-5775-4BCE-A1BC-B44B3C2C89D8"}
 */
function onHide(event)
{
	_viewID = forms.sch_views_tbl.view_id;

	scopes.avScheduling.bInScheduleBoard = false;
    scopes.avScheduling.setActiveScheduler(globals.avBase_employeeUUID, 0);
	
	return _super.onHide(event)
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"496FC8CC-D321-4E01-9519-DC2D117D68B8"}
 */
function dc_edit(_event, _triggerForm) {
    // SL-14103
    if (_to_sys_organization.org_sch_locked == 1) {        
        scopes.avScheduling.showScheduleBoardLockedMessage();        
    }
    
    // SL-14513
    // If the user unlocks the schedule board in the showScheduleBoardLockedMessage then enable the board
    if (_to_sys_organization.org_sch_locked != 1) {
        _uScheduleEditID = application.getUUID();
        _bResourceChanged = false;
        _bAvailChanged = false;
        _viewID = forms.sch_views_tbl.view_id;
        
        _super.dc_edit(_event, _triggerForm);

        forms.sch_views_tbl.foundset.selectRecord(_viewID);

        var sSQL = 'delete from sch_milestone_tmp where org_id = ? and modified_by_id = ?';
        var aArgs = [globals.org_id, globals.avBase_employeeUUID.toString()];
        scopes.avDB.RunSQL(sSQL, null, aArgs);
    }
    
    setCalendarEventsEditable(true);
}

/**
 * @AllowToRunInFind
 * 
 * @param {String} vsTable
 * @param {String} vsSearchColName
 * @param {Object} vsSearchColValue
 * @param {String} [vsServer]
 *
 * @return
 * @properties={typeid:24,uuid:"DAAF5F35-E0D2-4F44-BBCF-871C45AEC3CB"}
 */
function getRec(vsTable, vsSearchColName, vsSearchColValue, vsServer){
	if(!vsServer){
		vsServer = globals.avBase_dbase_avanti
	}
	/** @type {JSFoundSet} */
	var toFoundset = databaseManager.getFoundSet(vsServer, vsTable)
		
	if(toFoundset.find() || toFoundset.find()){
		toFoundset.setDataProviderValue(vsSearchColName, vsSearchColValue)
		if(toFoundset.search(true, false) > 0){
			return toFoundset.getRecord(1)
		}
	}
	// GD - 2014-02-12: Returning null to remove build marker
	return null;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 * @param {String} _answer
 *
 * @return
 * @properties={typeid:24,uuid:"BE7602BF-0D5F-440B-8A10-70D778BBE477"}
 */
function dc_cancel(_event, _triggerForm, _answer) {
	setCalendarEventsEditable(false);
	return _super.dc_cancel(_event, _triggerForm, _answer);
}

/**
 * @param {JSFoundSet} _foundset
 *
 * @properties={typeid:24,uuid:"0896628B-EFCA-4053-A823-856E06090DF3"}
 */
function dc_cancel_pre(_foundset) {
    if (!application.isInDeveloper()) {
        _super.dc_cancel_pre(_foundset);
    }

    if (globals.bOptimizedSchedule) {
        revertOptimizedSchedule(_to_sys_organization.org_sch_cancel_session_id);
        cleanupScheduleBoardBackup(_to_sys_organization.org_sch_optimize_session_id);
        globals.bOptimizedSchedule = false;
        dOptimizeClearFromDate = null;

        scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);
        scopes.avScheduling.notifyActiveSchedulers(true);        
    }
    else {
        revertManuallyEditedSchedule();
    }

    // DC - SL-11468 - reload the schedule view to display reverted changes
    bCancelled = true;
    _uScheduleEditID = null;
    _bResourceChanged = false;
    _bAvailChanged = false;
    
    loadView();
}

/**
*
* @param {JSEvent} _event
* @param _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"5DCC8987-FCE1-43BC-8CEF-588A27BC314F"}
*/
function dc_save(_event, _triggerForm) {
    if (globals.bOptimizedSchedule) {
        cleanupScheduleBoardBackup(_to_sys_organization.org_sch_optimize_session_id);
        globals.bOptimizedSchedule = false;                
        scopes.avScheduling.clearOrgScheduleOptimizeDetails(true);        
    }
   
    saveAndDeleteTempRecords();
    _uScheduleEditID = null;
    _bResourceChanged = false;
    _bAvailChanged = false;
    
    setCalendarEventsEditable(false);
    
    return _super.dc_save(_event, _triggerForm)
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"150A1145-84E7-4444-9461-3A2314EB2480"}
 */
function onAction_showOptimizeScheduleDialog(event) {
	if(globals.nav.mode == 'edit'){
		populateScheduleSequences();
		
		globals.DIALOGS.showFormInModalDialog(forms.sch_schedule_board_optimize_schedule_dlg, -1, -1, 1100, 640, i18n.getI18NMessage("avanti.dialog.optimizeSchedule"), true, false, "dlgOptimizeSchedule", true);		
	}
	else{
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('servoy.general.warning'), 
			i18n.getI18NMessage('avanti.dialog.cannotOptimizeSchedule'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
}

/**
 * @properties={typeid:24,uuid:"********-3BF1-4EE1-B47A-F264B13D79D2"}
 */
function populateScheduleSequences(){
	/** @type{JSFoundSet<db:/avanti/sch_schedule>} */
	var fsSchedule = forms.sch_schedule_board_optimize_schedule_jobs_tbl.foundset;
	fsSchedule.loadAllRecords();
	filterScheduledFoundset(fsSchedule);
	
	var sql = 'select sch.sch_id, min(mile.ms_date_scheduled) as earliest, mile.ms_flg_completed \
			   from sch_schedule sch \
			   inner join sch_milestone mile on (sch.sch_id = mile.sch_id)'
		
	sql = scopes.avDB.safeSQL(sql, "sch.jobstat_id = ? AND mile.ms_flg_completed = ?", "sch_schedule", 
		"group by sch.sch_id, mile.ms_flg_completed order by earliest asc", "sch");
	
	var args = ['Scheduled', 0];
	
	var dsSch = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sql, args, -1);
	var nSeq = 1;
	for(var i = 1; i <= dsSch.getMaxRowIndex(); i++){
		fsSchedule.loadRecords(application.getUUID(dsSch.getValue(i,1)));
		var rSch = fsSchedule.getRecord(1);
		if(rSch){
			rSch.schedule_sequence = nSeq;
			nSeq++;
			databaseManager.saveData(rSch);
		}		
	}
	databaseManager.saveData(fsSchedule);

	fsSchedule.loadAllRecords();
	filterScheduledFoundset(fsSchedule);

}

/**
 * Revert changes made by optimizing the schedule board
 * 
 * @param {UUID} sSessionID
 * 
 * @properties={typeid:24,uuid:"2D3A43F8-F3EC-4524-8982-178270680C7E"}
 */
function revertOptimizedSchedule(sSessionID) {
    
    // In case an empty or invalid sSessionID
	if (sSessionID) {
		sSessionID = sSessionID.toString();
		
		if (sSessionID.length != 36) {
			return;
		}
	}
	else {
		return;
	}
    
    clearScheduleBoard(sSessionID, dOptimizeClearFromDate);
    
    var sSQL = '';
    var aArgs = [];

    var bOk = false;
    var sMsg = "";
    
    // Here you will have to Update existing recordds and Insert missing records
    
    // sch_milestone
    sSQL = "UPDATE sch_milestone \
            SET ordrevds_id = mo.ordrevds_id \
                , ordrevdstask_id = mo.ordrevdstask_id \
                , org_id = mo.org_id \
                , ms_flg_ready = mo.ms_flg_ready \
                , ms_flg_completed = mo.ms_flg_completed \
                , ms_time_budget = mo.ms_time_budget \
                , ms_cost_budget = mo.ms_cost_budget \
                , sequence_nr = mo.sequence_nr \
                , sequence_icon = mo.sequence_icon \
                , ms_oper_name = mo.ms_oper_name \
                , sch_id = mo.sch_id \
                , ms_date_scheduled = mo.ms_date_scheduled \
                , ms_date_completed = mo.ms_date_completed \
                , ms_date_due = mo.ms_date_due \
                , dept_id = mo.dept_id \
                , opcat_id = mo.opcat_id \
                , ms_pred_ms_id = mo.ms_pred_ms_id \
                , created_by_id = mo.created_by_id \
                , modified_by_id = mo.modified_by_id \
                , created_date = mo.created_date \
                , modified_date = mo.modified_date \
                , po_id = mo.po_id \
                , ms_order = mo.ms_order \
                , ms_time_budget_formatted = mo.ms_time_budget_formatted \
                , ms_date_due_bak = mo.ms_date_due_bak \
                , ms_date_scheduled_bak = mo.ms_date_scheduled_bak \
                , ms_running_time_budget = mo.ms_running_time_budget \
                , ms_time_budget_no_round = mo.ms_time_budget_no_round \
                , ms_time_budget_no_ur_no_round = mo.ms_time_budget_no_ur_no_round \
                , ms_time_budget_no_ur = mo.ms_time_budget_no_ur \
                , ms_utilization_rate = mo.ms_utilization_rate \
                , shift_id = mo.shift_id \
                , cc_id = mo.cc_id \
                , completed_by_id = mo.completed_by_id \
                , ms_is_plating = mo.ms_is_plating \
                , ms_opcat_modified = mo.ms_opcat_modified \
                , ms_flg_locked = mo.ms_flg_locked \
                , ms_opcat_original = mo.ms_opcat_original \
                , div_id = mo.div_id \
                , plant_id = mo.plant_id \
                , ms_is_reservation = mo.ms_is_reservation \
                , contains_shift_break = mo.contains_shift_break \
                , emp_capacity_required = mo.emp_capacity_required \
                , ms_time_budget_bak = mo.ms_time_budget_bak \
                , split_parent_id = mo.split_parent_id \
                , is_split = mo.is_split \
                , scheduled_direction = mo.scheduled_direction \
            OUTPUT ?, 1, 'UPDATE_ACTION', INSERTED.ms_id, ?, 'sch_milestone', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_milestone m \
                INNER JOIN sch_milestone_optimize mo ON m.ms_id = mo.ms_id AND m.org_id = mo.org_id \
            WHERE mo.optimize_session_id = ? \
                AND m.org_id = ?";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, 'sch_milestone', sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - UPDATE sch_milestone, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');        
    }
    
    sSQL = "INSERT INTO sch_milestone \
                (ms_id, ordrevds_id, ordrevdstask_id, org_id, ms_flg_ready, ms_flg_completed, ms_time_budget, ms_cost_budget \
                , sequence_nr, sequence_icon, ms_oper_name, sch_id, ms_date_scheduled, ms_date_completed, ms_date_due, dept_id \
                , opcat_id, ms_pred_ms_id, created_by_id, modified_by_id, created_date, modified_date, po_id, ms_order \
                , ms_time_budget_formatted, ms_date_due_bak, ms_date_scheduled_bak, ms_running_time_budget, ms_time_budget_no_round \
                , ms_time_budget_no_ur_no_round, ms_time_budget_no_ur, ms_utilization_rate, shift_id, cc_id, completed_by_id \
                , ms_is_plating, ms_opcat_modified, ms_flg_locked, ms_opcat_original, div_id, plant_id, ms_is_reservation \
                , contains_shift_break, emp_capacity_required, ms_time_budget_bak, split_parent_id, is_split, scheduled_direction) \
            OUTPUT ?, 2, 'INSERT_ACTION', INSERTED.ms_id, ?, 'sch_milestone', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            SELECT ms_id, ordrevds_id, ordrevdstask_id, org_id, ms_flg_ready, ms_flg_completed, ms_time_budget, ms_cost_budget \
                , sequence_nr, sequence_icon, ms_oper_name, sch_id, ms_date_scheduled, ms_date_completed, ms_date_due, dept_id \
                , opcat_id, ms_pred_ms_id, created_by_id, modified_by_id, created_date, modified_date, po_id, ms_order \
                , ms_time_budget_formatted, ms_date_due_bak, ms_date_scheduled_bak, ms_running_time_budget, ms_time_budget_no_round \
                , ms_time_budget_no_ur_no_round, ms_time_budget_no_ur, ms_utilization_rate, shift_id, cc_id, completed_by_id \
                , ms_is_plating, ms_opcat_modified, ms_flg_locked, ms_opcat_original, div_id, plant_id, ms_is_reservation \
                , contains_shift_break, emp_capacity_required, ms_time_budget_bak, split_parent_id, is_split, scheduled_direction \
            FROM sch_milestone_optimize mo \
            WHERE mo.optimize_session_id = ? \
                AND mo.org_id = ? \
                AND NOT EXISTS (SELECT NULL FROM sch_milestone WHERE ms_id = mo.ms_id AND org_id = mo.org_id)";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - INSERT sch_milestone, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');        
    }    
    
    // sch_emp_capacity    
    sSQL = "UPDATE sch_emp_capacity \
            SET capacity = eco.capacity \
                , duration = eco.duration \
                , empl_id = eco.empl_id \
                , empschexcep_id = eco.empschexcep_id \
                , end_date = eco.end_date \
                , endtime_num_mins = eco.endtime_num_mins \
                , equip_id = eco.equip_id \
                , equip_ms_id = eco.equip_ms_id \
                , equipschexcep_id = eco.equipschexcep_id \
                , is_break = eco.is_break \
                , org_id = eco.org_id \
                , schedule_date_num = eco.schedule_date_num \
                , shiftdet_id = eco.shiftdet_id \
                , shiftexcep_id = eco.shiftexcep_id \
                , start_date = eco.start_date \
                , starttime_num_mins = eco.starttime_num_mins \
                , shift_id = eco.shift_id \
                , is_shift_break = eco.is_shift_break \
            OUTPUT ?, 3, 'UPDATE_ACTION', INSERTED.ec_id, ?, 'sch_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_emp_capacity ec \
                INNER JOIN sch_emp_capacity_optimize eco ON ec.ec_id = eco.ec_id AND ec.org_id = eco.org_id \
            WHERE eco.optimize_session_id = ? \
                AND ec.org_id = ?";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - UPDATE sch_emp_capacity, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    sSQL = "INSERT INTO sch_emp_capacity \
            (ec_id, capacity, duration, empl_id, empschexcep_id, end_date, endtime_num_mins, equip_id \
            , equip_ms_id, equipschexcep_id, is_break, org_id, schedule_date_num, shiftdet_id, shiftexcep_id \
            , start_date, starttime_num_mins, shift_id, is_shift_break) \
        OUTPUT ?, 4, 'INSERT_ACTION', INSERTED.ec_id, ?, 'sch_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
        SELECT ec_id, capacity, duration, empl_id, empschexcep_id, end_date, endtime_num_mins, equip_id \
            , equip_ms_id, equipschexcep_id, is_break, org_id, schedule_date_num, shiftdet_id, shiftexcep_id \
            , start_date, starttime_num_mins, shift_id, is_shift_break \
        FROM sch_emp_capacity_optimize eco \
        WHERE eco.optimize_session_id = ? \
            AND eco.org_id = ? \
            AND NOT EXISTS (SELECT NULL FROM sch_emp_capacity WHERE ec_id = eco.ec_id AND org_id = eco.org_id)";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - INSERT sch_emp_capacity, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    
    // sch_ms_emp_capacity    
    sSQL = "UPDATE sch_ms_emp_capacity \
            SET ec_id = mseco.ec_id \
                , empl_id = mseco.empl_id \
                , is_backup = mseco.is_backup \
                , ms_id = mseco.ms_id \
                , org_id = mseco.org_id\
                , sequence_nr = mseco.sequence_nr \
            OUTPUT ?, 5, 'UPDATE_ACTION', INSERTED.msec_id, ?, 'sch_ms_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_ms_emp_capacity msec \
                INNER JOIN sch_ms_emp_capacity_optimize mseco ON msec.msec_id = mseco.msec_id AND msec.org_id = mseco.org_id \
            WHERE mseco.optimize_session_id = ? \
                AND msec.org_id = ?";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - UPDATE sch_ms_emp_capacity, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    sSQL = "INSERT INTO sch_ms_emp_capacity \
                (msec_id, ec_id, empl_id, is_backup, ms_id, org_id, sequence_nr) \
            OUTPUT ?, 6, 'INSERT_ACTION', INSERTED.msec_id, ?, 'sch_ms_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            SELECT msec_id, ec_id, empl_id, is_backup, ms_id, org_id, sequence_nr \
            FROM sch_ms_emp_capacity_optimize mseco \
            WHERE mseco.optimize_session_id = ? \
                AND mseco.org_id = ? \
                AND NOT EXISTS (SELECT NULL FROM sch_ms_emp_capacity WHERE msec_id = mseco.msec_id AND org_id = mseco.org_id)";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - INSERT sch_ms_emp_capacity, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    
    // sch_equip_schedule
    sSQL = "UPDATE sch_equip_schedule \
            SET equip_id = eso.equip_id \
                , equipsch_start = eso.equipsch_start \
                , equipsch_end = eso.equipsch_end \
                , created_by_id = eso.created_by_id \
                , modified_by_id = eso.modified_by_id \
                , created_date = eso.created_date \
                , modified_date = eso.modified_date \
                , ms_id = eso.ms_id \
                , equipschexcep_id = eso.equipschexcep_id \
                , spot_tentatively_held_by_jobid = eso.spot_tentatively_held_by_jobid \
                , sch_id = eso.sch_id \
                , empl_id = eso.empl_id \
            OUTPUT ?, 7, 'UPDATE_ACTION', INSERTED.equipsch_id, ?, 'sch_equip_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_equip_schedule es \
                INNER JOIN sch_equip_schedule_optimize eso ON es.equipsch_id = eso.equipsch_id AND es.org_id = eso.org_id \
            WHERE eso.optimize_session_id = ? \
                AND es.org_id = ?";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - UPDATE sch_equip_schedule, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    sSQL = "INSERT INTO sch_equip_schedule \
                (equipsch_id, org_id, equip_id, equipsch_start, equipsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, equipschexcep_id, spot_tentatively_held_by_jobid, sch_id, empl_id) \
            OUTPUT ?, 8, 'INSERT_ACTION', INSERTED.equipsch_id, ?, 'sch_equip_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            SELECT equipsch_id, org_id, equip_id, equipsch_start, equipsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, equipschexcep_id, spot_tentatively_held_by_jobid, sch_id, empl_id \
            FROM sch_equip_schedule_optimize eso \
            WHERE eso.optimize_session_id = ? \
                AND eso.org_id = ? \
                AND NOT EXISTS (SELECT NULL FROM sch_equip_schedule WHERE equipsch_id = eso.equipsch_id AND org_id = eso.org_id)";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - INSERT sch_equip_schedule, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }  
    
    // sch_empl_schedule
    sSQL = "UPDATE sch_empl_schedule \
            SET empl_id = eso.empl_id \
                , emplsch_start = eso.emplsch_start \
                , emplsch_end = eso.emplsch_end \
                , created_by_id = eso.created_by_id \
                , modified_by_id = eso.modified_by_id \
                , created_date = eso.created_date \
                , modified_date = eso.modified_date \
                , ms_id = eso.ms_id \
                , empschexcep_id = eso.empschexcep_id \
                , emp_workload_pct = eso.emp_workload_pct \
                , empl_id_bak = eso.empl_id_bak \
                , sch_edit_id = eso.sch_edit_id \
            OUTPUT ?, 9, 'UPDATE_ACTION', INSERTED.emplsch_id, ?, 'sch_empl_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_empl_schedule es \
                INNER JOIN sch_empl_schedule_optimize eso ON es.emplsch_id = eso.emplsch_id AND es.org_id = eso.org_id \
            WHERE eso.optimize_session_id = ? \
                AND es.org_id = ?";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - UPDATE sch_empl_schedule, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
    
    sSQL = "INSERT INTO sch_empl_schedule \
                (emplsch_id, empl_id, org_id, emplsch_start, emplsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, empschexcep_id, emp_workload_pct, empl_id_bak, sch_edit_id) \
            OUTPUT ?, 10, 'INSERT_ACTION', INSERTED.emplsch_id, ?, 'sch_empl_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            SELECT emplsch_id, empl_id, org_id, emplsch_start, emplsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, empschexcep_id, emp_workload_pct, empl_id_bak, sch_edit_id \
            FROM sch_empl_schedule_optimize eso \
            WHERE eso.optimize_session_id = ? \
                AND eso.org_id = ? \
                AND NOT EXISTS (SELECT NULL FROM sch_empl_schedule WHERE emplsch_id = eso.emplsch_id AND org_id = eso.org_id)";
    
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(sSessionID, globals.org_id.toString());
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, 'sch_empl_schedule', sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - revertOptimizedSchedule exception - INSERT sch_empl_schedule, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'revertOptimizedSchedule exception: ' + sMsg, 'Ok');
    }
        
    scopes.avUtils.broadcastDataChanges(sSessionID);      
}

/**
 * @properties={typeid:24,uuid:"77B0E179-47D5-4672-96D6-6656A553776D"}
 */
function revertManuallyEditedSchedule(){
    var sSQL = "select milestone_id from sch_milestone_tmp where modified_by_id = ?";
    var aArgs = [globals.avBase_employeeUUID.toString()]
    /***@type {JSFoundset<db:/avanti/sch_milestone_tmp>} */
    var fsTempMile = scopes.avDB.getFSFromSQL(sSQL, 'sch_milestone_tmp', aArgs);

    databaseManager.refreshRecordFromDatabase(fsTempMile, -1);
    fsTempMile.loadAllRecords();
    
    // revert emp changes to other emps if any
    if (_bResourceChanged && _uScheduleEditID) {
        sSQL = "UPDATE sch_empl_schedule \
                SET \
                    empl_id = empl_id_bak, \
                    empl_id_bak = NULL, \
                    sch_edit_id = NULL \
                WHERE \
                    org_id = ? \
                    AND sch_edit_id = ? \
                    AND empl_id_bak IS NOT NULL";   
        aArgs = [globals.org_id, _uScheduleEditID.toString()];
        scopes.avDB.RunSQL(sSQL, null, aArgs);

        sSQL = "UPDATE sch_milestone_group \
                SET \
                    cc_id = cc_id_bak, \
                    taskcostlink_id = taskcostlink_id_bak, \
                    cc_id_bak = NULL, \
                    taskcostlink_id_bak = NULL, \
                    sch_edit_id = NULL \
                WHERE \
                    org_id = ? \
                    AND sch_edit_id = ? \
                    AND cc_id_bak IS NOT NULL \
                    AND taskcostlink_id_bak IS NOT NULL";   
        scopes.avDB.RunSQL(sSQL, null, aArgs);

        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sch_empl_schedule');
        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sch_milestone_group');
    }

    // revert resource 'avail' changes if any
    if (_bAvailChanged && _uScheduleEditID) {
        /**@type {JSFoundSet<db:/avanti/sch_view_resource>} */
        var fsChangedViews = scopes.avDB.getFSFromSQL("SELECT viewres_id FROM sch_view_resource WHERE org_id = ? AND sch_edit_id = ? AND avail_bak IS NOT NULL", 
            "sch_view_resource", [globals.org_id, _uScheduleEditID.toString()]);
        
        if (fsChangedViews) {
            for (var v = 1; v <= fsChangedViews.getSize(); v++) {
                var rChangedView = fsChangedViews.getRecord(v);

                // 3rd paarm - call this to delete and create equip exceptions + recreate cap recs from them - dont move ms tho, that will be done further down 
                forms.sch_view_resources_tbl.changeEquipAvailability(rChangedView.equip_id, rChangedView.avail_bak, true);
                
                rChangedView.avail_bak = null;
                
                databaseManager.saveData(rChangedView);
            }

            // avail is calc col
            databaseManager.recalculate(forms.sch_view_resources_tbl.foundset);
        }
    }
    
    if(fsTempMile && fsTempMile.getSize() > 0){
        // have to clear capacity for all ms fst 
        for(var i = 1; i <= fsTempMile.getSize(); i++){
            var rTempMilestone = fsTempMile.getRecord(i);
            /***@type {JSRecord<db:/avanti/sch_milestone>} */
            var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [rTempMilestone.milestone_id]);
            
            if(rMilestone){
                scopes.avScheduling.clearMilestoneCapacity(rMilestone);
            }
        }

        // then do another round of adding them to sched
        for(i = 1; i <= fsTempMile.getSize(); i++){
            rTempMilestone = fsTempMile.getRecord(i);
            rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [rTempMilestone.milestone_id]);
            
            if(rMilestone){
                var uEquipID = null;
                
                if (rMilestone.sch_edit_id == _uScheduleEditID && rMilestone.equip_id_bak) {
                    uEquipID = rMilestone.equip_id_bak;
                    rMilestone.equip_id_bak = null;
                    rMilestone.sch_edit_id = null;
                    
                    if (rMilestone.ms_time_budget_org) {
                        rMilestone.ms_time_budget = rMilestone.ms_time_budget_org;
                        rMilestone.ms_time_budget_org = null
                    }
                }
                else if (utils.hasRecords(rMilestone.sch_milestone_to_eq_equipment$dept_opcat)) {
                    uEquipID = rMilestone.sch_milestone_to_eq_equipment$dept_opcat.equip_id;
                }
                
                rMilestone.ms_date_scheduled = rTempMilestone.ms_date_scheduled;
                rMilestone.ms_date_due = rTempMilestone.ms_date_due;
                rMilestone.opcat_id = rTempMilestone.opcat_id;
                rMilestone.dept_id = rTempMilestone.dept_id;
                rMilestone.ms_oper_name = rTempMilestone.ms_oper_name;
                rMilestone.ms_opcat_modified = rTempMilestone.ms_opcat_modified;
                
                databaseManager.saveData(rMilestone);
                
                scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, true, null, uEquipID);
                scopes.avScheduling.updateSchedWithNewTime(rMilestone, uEquipID);
            }
            // this hasnt been implemented yet, but should be done in the future
            // this milestone was deleted - eg when a split ms is unsplit - recreate it
//            else{
//                rMilestone = scopes.avDB.newRecord('sch_milestone');
//                
//                copyTempMSToMSRec(rTempMilestone, rMilestone)
//                
//                scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, true, null, uEquipID);
//                
//                // have to create sched recs
//                updateResourceSchedule(rMilestone);
//                updateEmpSchedule(rMilestone);
//            }
        }
    
        sSQL = 'delete from sch_milestone_tmp where org_id = ? and modified_by_id = ?';   
        aArgs = [globals.org_id, globals.avBase_employeeUUID.toString()];
        scopes.avDB.RunSQL(sSQL, null, aArgs);
    }
}

/**
 * 
 * @param {String} sSessionID
 * @param {Date} dClearFromDate
 *
 * @return
 * @properties={typeid:24,uuid:"6C10A97C-0E52-47F4-923C-9255ECB6AC92"}
 */
function backupScheduleBoard(sSessionID, dClearFromDate) {
    
    // In case an empty or invalid sSessionID
	if (sSessionID) {
		sSessionID = sSessionID.toString();
		
		if (sSessionID.length != 36) {
	        return false;
		}
	}
	else {
        return false;
	}
    
    var sSQL = '';
    var aArgs = [];

    var bOk = false;
    var sMsg = "";
    
    // sl-26721 - getSafeConditionsClause returns a where clause with org_id, and div/plant IDs if appropriate
    var sSafeConditionsClause = scopes.avDB.getSafeConditionsClause("m");
    
    sSQL = "INSERT INTO sch_milestone_optimize \
                (ms_id, ordrevds_id, ordrevdstask_id, org_id, ms_flg_ready, ms_flg_completed, ms_time_budget, ms_cost_budget \
                , sequence_nr, sequence_icon, ms_oper_name, sch_id, ms_date_scheduled, ms_date_completed, ms_date_due, dept_id \
                , opcat_id, ms_pred_ms_id, created_by_id, modified_by_id, created_date, modified_date, po_id, ms_order \
                , ms_time_budget_formatted, ms_date_due_bak, ms_date_scheduled_bak, ms_running_time_budget, ms_time_budget_no_round \
                , ms_time_budget_no_ur_no_round, ms_time_budget_no_ur, ms_utilization_rate, shift_id, cc_id, completed_by_id \
                , ms_is_plating, ms_opcat_modified, ms_flg_locked, ms_opcat_original, div_id, plant_id, ms_is_reservation \
                , contains_shift_break, emp_capacity_required, ms_time_budget_bak, split_parent_id, is_split, scheduled_direction \
                , optimize_session_id) \
            SELECT ms_id, ordrevds_id, ordrevdstask_id, org_id, ms_flg_ready, ms_flg_completed, ms_time_budget, ms_cost_budget \
                , sequence_nr, sequence_icon, ms_oper_name, sch_id, ms_date_scheduled, ms_date_completed, ms_date_due, dept_id \
                , opcat_id, ms_pred_ms_id, created_by_id, modified_by_id, created_date, modified_date, po_id, ms_order \
                , ms_time_budget_formatted, ms_date_due_bak, ms_date_scheduled_bak, ms_running_time_budget, ms_time_budget_no_round \
                , ms_time_budget_no_ur_no_round, ms_time_budget_no_ur, ms_utilization_rate, shift_id, cc_id, completed_by_id \
                , ms_is_plating, ms_opcat_modified, ms_flg_locked, ms_opcat_original, div_id, plant_id, ms_is_reservation \
                , contains_shift_break, emp_capacity_required, ms_time_budget_bak, split_parent_id, is_split, scheduled_direction \
                , ? \
            FROM sch_milestone m \
            WHERE " + sSafeConditionsClause + " \
                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                AND NOT EXISTS (SELECT NULL \
                                FROM prod_job_cost pjc \
                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                    AND pjc.ms_id = m.ms_id) \
                AND EXISTS (SELECT NULL \
                            FROM sch_schedule s \
                            WHERE s.org_id = ? \
                                AND s.jobstat_id = 'Scheduled' \
                                AND s.sch_id = m.sch_id) \
                AND m.ms_date_scheduled >= ?";
    
    aArgs = [sSessionID.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true)];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - BackupScheduleBoard exception - sTableName: sch_milestone_optimize, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'BackupScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }
    
    sSQL = "INSERT INTO sch_emp_capacity_optimize \
                (ec_id, capacity, duration, empl_id, empschexcep_id, end_date, endtime_num_mins, equip_id \
                , equip_ms_id, equipschexcep_id, is_break, org_id, schedule_date_num, shiftdet_id, shiftexcep_id \
                , start_date, starttime_num_mins, shift_id, is_shift_break \
                , optimize_session_id) \
            SELECT ec_id, capacity, duration, empl_id, empschexcep_id, end_date, endtime_num_mins, equip_id \
                , equip_ms_id, equipschexcep_id, is_break, org_id, schedule_date_num, shiftdet_id, shiftexcep_id \
                , start_date, starttime_num_mins, shift_id, is_shift_break \
                , ? \
            FROM sch_emp_capacity ec \
            WHERE ec.org_id = ? \
                AND EXISTS (SELECT m.ms_id \
                            FROM sch_milestone m \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND ec.equip_ms_id = m.ms_id)";
    
    aArgs = [sSessionID.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true)];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - BackupScheduleBoard exception - sTableName: sch_emp_capacity_optimize, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'BackupScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }
    
    sSQL = "INSERT INTO sch_ms_emp_capacity_optimize \
                (msec_id, ec_id, empl_id, is_backup, ms_id, org_id, sequence_nr \
                , optimize_session_id) \
            SELECT msec_id, ec_id, empl_id, is_backup, ms_id, org_id, sequence_nr \
                , ? \
            FROM sch_ms_emp_capacity mec \
            WHERE mec.org_id = ? \
                    AND EXISTS (SELECT m.ms_id \
                                FROM sch_milestone m \
                                WHERE " + sSafeConditionsClause + " \
                                    AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                    AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                    AND NOT EXISTS (SELECT NULL \
                                                    FROM prod_job_cost pjc \
                                                        INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                    WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                        AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                        AND pjc.ms_id = m.ms_id) \
                                    AND EXISTS (SELECT NULL \
                                                FROM sch_schedule s \
                                                WHERE s.org_id = ? \
                                                    AND s.jobstat_id = 'Scheduled' \
                                                    AND s.sch_id = m.sch_id) \
                                    AND m.ms_date_scheduled >= ? \
                                    AND mec.ms_id = m.ms_id)";
    
    aArgs = [sSessionID.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true)];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - BackupScheduleBoard exception - sTableName: sch_ms_emp_capacity_optimize, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'BackupScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }
    
    sSQL = "INSERT INTO sch_equip_schedule_optimize \
                (equipsch_id, org_id, equip_id, equipsch_start, equipsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, equipschexcep_id, spot_tentatively_held_by_jobid, sch_id, empl_id \
                , optimize_session_id) \
            SELECT equipsch_id, org_id, equip_id, equipsch_start, equipsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, equipschexcep_id, spot_tentatively_held_by_jobid, sch_id, empl_id \
                , ? \
            FROM sch_equip_schedule es \
            WHERE es.org_id = ? \
                AND EXISTS (SELECT NULL \
                            FROM sch_milestone m \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                                FROM sch_schedule s \
                                                WHERE s.org_id = ? \
                                                    AND s.jobstat_id = 'Scheduled' \
                                                    AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND m.ms_id = es.ms_id)";
    
    aArgs = [sSessionID.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true)];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - BackupScheduleBoard exception - sTableName: sch_equip_schedule_optimize, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'BackupScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }
    
    sSQL = "INSERT INTO sch_empl_schedule_optimize \
                (emplsch_id, empl_id, org_id, emplsch_start, emplsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, empschexcep_id, emp_workload_pct, empl_id_bak, sch_edit_id \
                , optimize_session_id) \
            SELECT emplsch_id, empl_id, org_id, emplsch_start, emplsch_end, created_by_id, modified_by_id \
                , created_date, modified_date, ms_id, empschexcep_id, emp_workload_pct, empl_id_bak, sch_edit_id \
                , ? \
            FROM sch_empl_schedule es \
            WHERE org_id = ? \
                AND EXISTS (SELECT NULL \
                            FROM sch_milestone m \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                                FROM sch_schedule s \
                                                WHERE s.org_id = ? \
                                                    AND s.jobstat_id = 'Scheduled' \
                                                    AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND m.ms_id = es.ms_id)";
    
    aArgs = [sSessionID.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true)];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - BackupScheduleBoard exception - sTableName: sch_empl_schedule_optimize, sSessionID: ' + sSessionID + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'BackupScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }
    
    return true;
}

/**
 * Removes all milestones from the schedule board prior to Optimization
 * 
 * @param {UUID} sSessionID
 * @param {Date} dClearFromDate
 *
 * @return
 * @properties={typeid:24,uuid:"BBE5D338-672F-425E-AEDC-DFAC85F11543"}
 */
function clearScheduleBoard(sSessionID, dClearFromDate) {

    // In case an empty or invalid sSessionID
	if (sSessionID) {
		sSessionID = sSessionID.toString();
		
		if (sSessionID.length != 36) {
	        return false;
		}
	}
	else {
        return false;
	}
    
    // Only clear milestones that are not locked, not completed and not in progress.
    
    var sSQL = '';
    var aArgs = [];

    var bOk = false;
    var sMsg = "";
    
    // sl-26721 - getSafeConditionsClause returns a where clause with org_id, and div/plant IDs if appropriate
    var sSafeConditionsClause = scopes.avDB.getSafeConditionsClause("m");
    
    // ***********************************************

    sSQL = "UPDATE sch_emp_capacity \
            SET capacity = 100, equip_ms_id = null \
            OUTPUT ?, 2, 'UPDATE_ACTION', INSERTED.ec_id, ?, 'sch_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_emp_capacity ec \
            WHERE ec.org_id = ? \
                AND EXISTS (SELECT m.ms_id, eq.equip_id \
                            FROM sch_milestone m \
                                INNER JOIN eq_equipment eq ON m.dept_id = eq.dept_id AND m.opcat_id = eq.opcat_id AND eq.lag_parent IS NULL \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND ec.equip_id = eq.equip_id AND ec.equip_ms_id = m.ms_id)";

    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true));

    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_emp_capacity, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    // ***********************************************

    sSQL = "UPDATE sch_emp_capacity \
            SET capacity = capacity + es.emp_workload_pct \
            OUTPUT ?, 3, 'UPDATE_ACTION', INSERTED.ec_id, ?, 'sch_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            FROM sch_emp_capacity ec \
                INNER JOIN (SELECT m.ms_id, es.empl_id, es.emp_workload_pct \
                            FROM sch_milestone m \
                                INNER JOIN sch_empl_schedule es ON m.ms_id = es.ms_id \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                ) es ON ec.empl_id = es.empl_id \
            WHERE ec.org_id = ? \
                AND EXISTS (SELECT NULL \
                            FROM sch_ms_emp_capacity mec \
                            WHERE mec.org_id = ? \
                                AND mec.ms_id = es.ms_id \
                                AND mec.ec_id = ec.ec_id)";

    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true), globals.org_id.toString(), globals.org_id.toString());

    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_emp_capacity 2, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    // ***********************************************

    sSQL = "DELETE FROM sch_ms_emp_capacity \
            OUTPUT ?, 4, 'DELETE_ACTION', DELETED.msec_id, ?, 'sch_ms_emp_capacity', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            WHERE org_id = ? \
                AND EXISTS (SELECT m.ms_id, es.empl_id, es.emp_workload_pct \
                            FROM sch_milestone m \
                                INNER JOIN sch_empl_schedule es ON m.ms_id = es.ms_id \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND sch_ms_emp_capacity.ms_id = m.ms_id \
                                AND sch_ms_emp_capacity.empl_id = es.empl_id)";

    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true));

    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_ms_emp_capacity, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    // ***********************************************

    sSQL = "DELETE FROM sch_equip_schedule \
            OUTPUT ?, 5, 'DELETE_ACTION', DELETED.equipsch_id, ?, 'sch_equip_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            WHERE org_id = ? \
                AND EXISTS (SELECT NULL \
                            FROM sch_milestone m \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND m.ms_id = sch_equip_schedule.ms_id)";
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true));

    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_equip_schedule, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    // ***********************************************

    sSQL = "DELETE FROM sch_empl_schedule \
            OUTPUT ?, 6, 'DELETE_ACTION', DELETED.emplsch_id, ?, 'sch_empl_schedule', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
            WHERE org_id = ? \
                AND EXISTS (SELECT NULL \
                            FROM sch_milestone m \
                            WHERE " + sSafeConditionsClause + " \
                                AND ISNULL(m.ms_flg_locked, 0) = 0 \
                                AND ISNULL(m.ms_flg_completed, 0) = 0 \
                                AND NOT EXISTS (SELECT NULL \
                                                FROM prod_job_cost pjc \
                                                    INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                                                WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                                    AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                                    AND pjc.ms_id = m.ms_id) \
                                AND EXISTS (SELECT NULL \
                                            FROM sch_schedule s \
                                            WHERE s.org_id = ? \
                                                AND s.jobstat_id = 'Scheduled' \
                                                AND s.sch_id = m.sch_id) \
                                AND m.ms_date_scheduled >= ? \
                                AND m.ms_id = sch_empl_schedule.ms_id)";
    // Arguments for OUTPUT clause
    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true));

    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_empl_schedule, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    //**************************************************************************

    sSQL = "UPDATE sch_milestone \
        SET ms_date_scheduled = NULL, ms_date_due = NULL \
        OUTPUT ?, 1, 'UPDATE_ACTION', INSERTED.ms_id, ?, 'sch_milestone', ?, GetDate() INTO [dbo].[sys_servoy_broadcast] \
        FROM sch_milestone m \
        WHERE " + sSafeConditionsClause + " \
            AND ISNULL(m.ms_flg_locked, 0) = 0 \
            AND ISNULL(m.ms_flg_completed, 0) = 0 \
            AND NOT EXISTS (SELECT NULL \
                            FROM prod_job_cost pjc \
                                INNER JOIN prod_job_cost_labour pjcl ON pjc.jc_id = pjcl.jc_id \
                            WHERE pjc.org_id = ? AND pjcl.org_id = ? \
                                AND pjcl.jcl_start_datetime IS NOT NULL AND pjcl.jcl_end_datetime IS NULL \
                                AND pjc.ms_id = m.ms_id) \
            AND EXISTS (SELECT NULL \
                        FROM sch_schedule s \
                        WHERE s.org_id = ? \
                            AND s.jobstat_id = 'Scheduled' \
                            AND s.sch_id = m.sch_id) \
            AND m.ms_date_scheduled >= ?";

    aArgs = [globals.org_id.toString(), sSessionID, globals.avBase_employeeUUID.toString()];
    aArgs.push(globals.org_id.toString(), globals.org_id.toString(), globals.org_id.toString(), scopes.avUtils.formatDateTime(dClearFromDate, true));
    
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - ClearScheduleBoard exception - sTableName: sch_milestone, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
        plugins.dialogs.showErrorDialog('Error', 'ClearScheduleBoard exception: ' + sMsg, 'Ok');
        return false;
    }

    //**************************************************************************
    
    scopes.avUtils.broadcastDataChanges(sSessionID);

    return true;
}

/**
 * @param {UUID} sSessionID
 * 
 * @return
 * @properties={typeid:24,uuid:"B9CDE9B4-EF18-4D99-8734-5C319B8A91BC"}
 */
function cleanupScheduleBoardBackup(sSessionID) {
    // In case an empty or invalid sSessionID
	if (sSessionID) {
		sSessionID = sSessionID.toString();
		
		if (sSessionID.length != 36) {
	        return false;
		}
	}
	else {
        return false;
	}
    
    var sSQL = '';
    var aArgs = [];

    var bOk = false;
    var sMsg = "";
    
    sSQL = "DELETE FROM sch_milestone_optimize WHERE org_id = ? AND optimize_session_id = ?";
    aArgs = [globals.org_id.toString(), sSessionID];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - CleanupScheduleBoardBackup exception - sTableName: sch_milestone_optimize, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
    }
    
    sSQL = "DELETE FROM sch_emp_capacity_optimize WHERE org_id = ? AND optimize_session_id = ?";
    aArgs = [globals.org_id.toString(), sSessionID];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - CleanupScheduleBoardBackup exception - sTableName: sch_emp_capacity_optimize, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
    }
    
    sSQL = "DELETE FROM sch_ms_emp_capacity_optimize WHERE org_id = ? AND optimize_session_id = ?";
    aArgs = [globals.org_id.toString(), sSessionID];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - CleanupScheduleBoardBackup exception - sTableName: sch_ms_emp_capacity_optimize, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
    }
    
    sSQL = "DELETE FROM sch_equip_schedule_optimize WHERE org_id = ? AND optimize_session_id = ?";
    aArgs = [globals.org_id.toString(), sSessionID];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - CleanupScheduleBoardBackup exception - sTableName: sch_equip_schedule_optimize, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
    }
    
    sSQL = "DELETE FROM sch_empl_schedule_optimize WHERE org_id = ? AND optimize_session_id = ?";
    aArgs = [globals.org_id.toString(), sSessionID];
    bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sSQL, aArgs);
    if (!bOk) {
        sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
        application.output('Error - CleanupScheduleBoardBackup exception - sTableName: sch_empl_schedule_optimize, sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
    }
}

/**
 * @param {JSRecord<db:/avanti/sch_milestone_tmp>} rTempMilestone
 * @param {JSRecord<db:/avanti/sch_milestone>} rMilestone
 *
 * @properties={typeid:24,uuid:"D062ACE6-F8D5-43F3-8A07-634DB6E523D6"}
 */
function copyTempMSToMSRec(rTempMilestone, rMilestone){
    var aTempColNames = scopes.avDB.getColumnNames(rTempMilestone.foundset);
    var aMSColNames = scopes.avDB.getColumnNames(rMilestone.foundset);
    
    for(var i=0; i<aTempColNames.length; i++){
        var sColName = aTempColNames[i];
        
        if(aMSColNames.indexOf(sColName) > -1){
            rMilestone[sColName] = rTempMilestone[sColName];
        }
    }
    
    rMilestone.ms_id = rTempMilestone.milestone_id;
    
    databaseManager.saveData(rMilestone);
}

/**
 * Once the schedule is saved, remove the temporary records for milestone and schedule
 * @properties={typeid:24,uuid:"A922F8ED-CC74-4942-A950-B50C0C20F506"}
 */
function saveAndDeleteTempRecords(){
	var oSQL = new Object();
	oSQL.sql = 'delete from sch_milestone_tmp where modified_by_id = ?';
	oSQL.args = ['' + globals.avBase_employeeUUID];
	globals["avUtilities_sqlRaw"](oSQL);
	
	oSQL.sql = 'delete from sch_equip_schedule_tmp where created_by_id = ?';
	oSQL.args = ['' + globals.avBase_employeeUUID];
	globals["avUtilities_sqlRaw"](oSQL);
	
	/***@type {JSRecord<db:/avanti/sys_organization>} */
	var rOrg = scopes.avDB.getRec('sys_organization', ['org_id'], [globals.org_id]);
	if(rOrg){
		rOrg.org_schedule_last_update = application.getTimeStamp();
		databaseManager.saveData(rOrg);
	}
	
    // clear empl_id_bak sch_edit_id
    if (_bResourceChanged && _uScheduleEditID) {
        var sSQL = "UPDATE sch_empl_schedule \
                    SET \
                        empl_id_bak = NULL, \
                        sch_edit_id = NULL \
                    WHERE \
                        org_id = ? \
                        AND sch_edit_id = ?";   
        var aArgs = [globals.org_id, _uScheduleEditID.toString()];
        scopes.avDB.RunSQL(sSQL, null, aArgs);

        sSQL = "UPDATE sch_milestone \
                SET \
                    equip_id_bak = NULL, \
                    ms_time_budget_org = NULL, \
                    sch_edit_id = NULL \
                WHERE \
                    org_id = ? \
                    AND sch_edit_id = ?";   
        scopes.avDB.RunSQL(sSQL, null, aArgs);
        
        sSQL = "UPDATE sch_milestone_group \
                SET \
                    cc_id_bak = NULL, \
                    taskcostlink_id_bak = NULL, \
                    sch_edit_id = NULL \
                WHERE \
                    org_id = ? \
                    AND sch_edit_id = ?"
        scopes.avDB.RunSQL(sSQL, null, aArgs);
    
        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sch_empl_schedule');
        plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sch_milestone_group');
    }
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"00ECD3CD-D589-413E-866B-389734CED9C0"}
 */
function onLoad(event) {
	return _super.onLoad(event);
}

/**
 * Callback method when form is destroyed.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @return
 * @properties={typeid:24,uuid:"31F06E30-3A0D-4435-A311-BB0900AE67AB"}
 */
function onUnload(event) {
    return _super.onUnload(event);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BEA17E6D-7B71-46BD-BE59-5AA506BC2D47"}
 */
function onAction_refresh(event) {
	loadView();
}

/**
 * @param {UUID|String} ms_id
 *
 * @properties={typeid:24,uuid:"A4E56203-8573-490B-84FD-0A9E6DF529CF"}
 */
function splitMilestone(ms_id){
    if(scopes.avText.showYesNoQuestion('splitMS?') == scopes.avText.yes){
        /**@type {JSRecord<db:/avanti/sch_milestone>} */
        var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [ms_id]);
        
        if(rMilestone){
            createTemporaryMilestone(rMilestone);

            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
                rMilestone.sch_milestone_to_sch_equip_schedule.deleteAllRecords();
            }
            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
                rMilestone.sch_milestone_to_sch_empl_schedule.deleteAllRecords();
            }
            
            scopes.avScheduling.splitMilestone(rMilestone, 'F');

            updateResourceSchedule(rMilestone);
            updateEmpSchedule(rMilestone);
            
            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_milestone$split_children)) {
                deleteTemporaryMilestone(rMilestone);
                
                for(var i=1; i<=rMilestone.sch_milestone_to_sch_milestone$split_children.getSize(); i++){
                    var rSplitMS = rMilestone.sch_milestone_to_sch_milestone$split_children.getRecord(i);
                    
                    updateResourceSchedule(rSplitMS);
                    updateEmpSchedule(rSplitMS);
                }
            }
            
            loadView();
        }    
    }
}

/**
 * @param {String} ms_id
 *
 * @properties={typeid:24,uuid:"166F26FD-09C8-4BFA-9277-2A76AF22AA50"}
 */
function debugMilestone(ms_id){
    /***@type {JSRecord<db:/avanti/sch_milestone>} */
    var rMS = scopes.avDB.getRec('sch_milestone', ['ms_id'], [ms_id]);
    
    if (rMS) {
        forms.sch_debug_milestone.loadMSData(rMS);
        globals.DIALOGS.showFormInModalDialog(forms.sch_debug_milestone, -1, -1, 1100, -1, 
            'Debug milestone: ' + rMS.ms_oper_name, true, false, "dlgDebugMilestone", true);
    }
}

/**
 * Perform the element double-click action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2A7A5E48-77C8-46F4-ABE2-D7470D333292"}
 */
function onDoubleClick_SBLabel(event) {
	var sMsg = "_cur_mode: " + _cur_mode + "\n" +
			   "_dCurDate: " + _dCurDate;
	scopes.avText.showInfo(sMsg, true);
	
}

/**
 * <b>onEventMouseEnterMethodID</b> will be called when the user mouses over an event. Similar to the native mouseenter.
 *
 * @param element
 * @param {CustomType<svy-fullcalendar2.EventObject>} eventObject
 * @param {JSEvent} jsEvent
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @properties={typeid:24,uuid:"CF0D13B8-7030-4540-A1AC-7EE74209BBB5"}
 */
function onEventMouseEnter(element, eventObject, jsEvent, view) {
	if (eventObject.extendedProps.tooltip) {
		forms.tooltip.show("ScheduleBoardToolTip", eventObject.id, eventObject.extendedProps.tooltip, jsEvent.getX() - 300, jsEvent.getY() - 250);
	}
}

/**
 * <b>onEventMouseLeaveMethodID</b> will be called when the user mouses out of an event. Similar to the native mouseleave.
 *
 * @param element
 * @param {CustomType<svy-fullcalendar2.EventObject>} eventObject
 * @param {JSEvent} event
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @properties={typeid:24,uuid:"5BE401C7-08C5-4442-B337-49A66822E3F7"}
 */
function onEventMouseLeave(element, eventObject, event, view) {
	forms.tooltip.hide();
}

/**
 * <b>onEventDropMethodID</b> will be called when dragging stops and the event has moved to a different day/time.
 *
 * @param {CustomType<svy-fullcalendar2.EventObject>} event
 * @param {Array<CustomType<svy-fullcalendar2.EventObject>>} relatedEvents
 * @param {CustomType<svy-fullcalendar2.EventObject>} oldEvent
 * @param {CustomType<svy-fullcalendar2.ResourceObject>} oldResource
 * @param {CustomType<svy-fullcalendar2.ResourceObject>} newResource
 * @param delta
 * @param element
 * @param {JSEvent} jsEvent
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @return {Boolean} if it returns false, the event drop action will be reverted, otherwise (true) the action is considered valid
 *
 * @properties={typeid:24,uuid:"3751440C-C335-4112-84CA-FE40282F8652"}
 */
function onEventDrop(event, relatedEvents, oldEvent, oldResource, newResource, delta, element, jsEvent, view) {
	
	relatedEvents.length;
	
	var sMsID = event.id;
	if (sMsID) {
        /**@type {JSRecord<db:/avanti/sch_milestone>} */
        var rMilestone = scopes.avDB.getRec('sch_milestone', ['ms_id'], [sMsID]);
        

        var bIsEquip = event.extendedProps.resourceType == scopes.avScheduling.RESOURCE_TYPE.Equipment;
        var bIsEmp = event.extendedProps.resourceType == scopes.avScheduling.RESOURCE_TYPE.Employee;
        
        var dOldStartDate = new Date(oldEvent.start);
        var dOldEndDate = new Date(oldEvent.end);
        var dNewStartDate = new Date(event.start);
        var dNewEndDate = new Date(event.end);
        var dNewStartDateBAK = new Date(event.start);
        var dNewEndDateBAK = new Date(event.end);
        
		dOldStartDate = scopes.avDate.convertServerTimeToClientTime(dOldStartDate);
		dOldEndDate = scopes.avDate.convertServerTimeToClientTime(dOldEndDate);
		dNewStartDate = scopes.avDate.convertServerTimeToClientTime(dNewStartDate);
		dNewEndDate = scopes.avDate.convertServerTimeToClientTime(dNewEndDate);
		dNewStartDateBAK = scopes.avDate.convertServerTimeToClientTime(dNewStartDateBAK);
		dNewEndDateBAK = scopes.avDate.convertServerTimeToClientTime(dNewEndDateBAK);
		
        /**@type {UUID} */
        var uOldEquipID = bIsEquip ? oldEvent.extendedProps.resourceId : null;
        /**@type {UUID} */
        var uNewEquipID = bIsEquip ? newResource.id : null;
        /**@type {UUID} */
        var uOldEmpID = bIsEmp ? oldEvent.extendedProps.resourceId : null;
        /**@type {UUID} */
        var uNewEmpID = bIsEmp ? newResource.id : null;
        
        var bTimeChanged = dOldStartDate != dNewStartDate || dOldEndDate != dNewEndDate;
        var bEquipChanged = uOldEquipID && uNewEquipID && uOldEquipID != uNewEquipID;
        var bEmpChanged = uOldEmpID && uNewEmpID && uOldEmpID != uNewEmpID;
        var bEquipChangedBack = false;
        var sOldResType = oldEvent.extendedProps.resourceType;
        var sNewResType = newResource.extendedProps? newResource.extendedProps.resourceType : sOldResType;
        var bCanMilestoneBeMoved = true;
        var sOldResID = bIsEmp ? uOldEmpID : uOldEquipID;
        var sNewResID = bIsEmp ? uNewEmpID : uNewEquipID;
        var iResType = bIsEmp ? 0 : 1;
        var bAttemptedMove = false;
        var bTimeNeededRecalced = false;
        
        //cannot move to earlier than current time
    	if (dNewStartDate < new Date()){
    		return false;
    	}
    	
    	// cant move emp ms to diff time if it has equip
    	if(bIsEmp && uNewEquipID != null && (dNewEndDate != dOldStartDate || dNewEndDate != dOldEndDate)){
    		return false;
    	}
        //if ms_flg_locked, move not allowed
		if (rMilestone.ms_flg_locked) {
	        milestoneMove = false;
            return false;
		}
        
        // calc end date from start date and ms time needed. it will be diff from the sEndDate param if moving
        // into or out of an exception
        dNewEndDate = plugins.DateUtils.addMinutes(dNewStartDate, rMilestone.ms_time_budget);
        
        createTemporaryMilestone(rMilestone);
        
        // SL-14103 
        if (_to_sys_organization.org_sch_locked == 1) {
            bCanMilestoneBeMoved = false;
            _bNeedShowWarningDialog = true;
          
        }
        
        if (bCanMilestoneBeMoved) {
            if (sOldResType == scopes.avScheduling.RESOURCE_TYPE.Employee && sNewResType == scopes.avScheduling.RESOURCE_TYPE.Equipment) {
                bCanMilestoneBeMoved = false;
                scopes.avScheduling.sMoveFailReason = scopes.avScheduling.MOVE_FAIL_REASON.CantMoveEmpToEquip; 
            }
            else if (sOldResType == scopes.avScheduling.RESOURCE_TYPE.Equipment && sNewResType == scopes.avScheduling.RESOURCE_TYPE.Employee) {
                bCanMilestoneBeMoved = false;
                scopes.avScheduling.sMoveFailReason = scopes.avScheduling.MOVE_FAIL_REASON.CantMoveEquipToEmp; 
            }
        }
        
        if (bCanMilestoneBeMoved && bEquipChanged && !scopes.avScheduling.canMilestoneBeMovedToEquipment(uOldEquipID, uNewEquipID)) {
            bCanMilestoneBeMoved = false;
            scopes.avScheduling.sMoveFailReason = scopes.avScheduling.MOVE_FAIL_REASON.NotInSameWorkPool; 
        }
        
        //to change in the resource view
        if (bCanMilestoneBeMoved) {
            // SL-14039 - if they have moved ms to a diff equip then RecalcMilestoneTimeNeeded if pref on
            if (bEquipChanged && globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RecalcMilestoneTimeNeeded)) {
                /**@type {JSRecord<db:/avanti/eq_equipment>} */
                var rNewEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [sNewResID.toString()]);
                
                if (rNewEquip) {
                	var dDueDateBeforeTimeNeededRecalc = rMilestone.ms_date_due;
                	var nTimeNeededBeforeTimeNeededRecalc = rMilestone.ms_time_budget;
                	var uOldDeptID = rMilestone.dept_id;
                	var uOldOpCatID = rMilestone.opcat_id;
                	
					if (scopes.avScheduling.setTimeNeededForNewEquip(rMilestone, rNewEquip)) {
	                    dNewEndDate = plugins.DateUtils.addMinutes(dNewStartDate, rMilestone.ms_time_budget);
	                    bTimeNeededRecalced = true;
					}
					// an error occurred - have to revert to org resource
					else {
						_bShowErrorRecalculatingTimeNeeded = true;
						bCanMilestoneBeMoved = false;
					}
                }
            }
            
			if (bCanMilestoneBeMoved) {
	            bAttemptedMove = true;
	            bCanMilestoneBeMoved = scopes.avScheduling.canMilestoneBeMoved(rMilestone, dOldStartDate, dOldEndDate, 
	                dNewStartDate, dNewEndDate, uOldEquipID, uNewEquipID, uOldEmpID, uNewEmpID);
			}
            
            // sl-17905 - if moving ms to a new resource cause the time needed to change then have to push or pull successors 
			if (bCanMilestoneBeMoved && bTimeNeededRecalced) {
                // rMilestone expanded - have to push successors forwards
				if (rMilestone.ms_date_due > dDueDateBeforeTimeNeededRecalc && !bTimeChanged) {
					pushSuccessorForward(rMilestone);
				}
                // rMilestone contracted - have to pull successors backwards
				else if (rMilestone.ms_date_due < dDueDateBeforeTimeNeededRecalc) {
					var nDiffMins = rMilestone.ms_time_budget - nTimeNeededBeforeTimeNeededRecalc;
					
					// if one of the successors is an assembly ms with another predecessor chain, then that predecessor chain will be pushed back, which may result in a predecessor
					// being pushed into the past, which would cause pullSuccessorBack to fail. in which case we disallow/revert original milestone move to a different resource. as per GB   
					if (!pullSuccessorsBack(rMilestone.ms_id.toString(), nDiffMins)) {
						scopes.avUtils.devOutput("######################################### REVERTING RESOURCE MOVE");
						
						rMilestone.ms_date_scheduled = dOldStartDate;
						rMilestone.ms_date_due = dOldEndDate;
						rMilestone.ms_time_budget = nTimeNeededBeforeTimeNeededRecalc;
						rMilestone.dept_id = uOldDeptID;
						rMilestone.opcat_id = uOldOpCatID;

						if (utils.hasRecords(rMilestone.sch_milestone_to_sch_equip_schedule)) {
							rMilestone.sch_milestone_to_sch_equip_schedule.equip_id = uOldEquipID;
							rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_start = rMilestone.ms_date_scheduled;
							rMilestone.sch_milestone_to_sch_equip_schedule.equipsch_end = rMilestone.ms_date_due;
						}
						
						// have to call getNextFreeSpot to update caprecs
						scopes.avScheduling.clearMilestoneCapacity(rMilestone);
				        scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, null, null, null, null, true, null, true);
											
			            bEquipChangedBack = true;
					}
				}
				
	            if (utils.hasRecords(rMilestone.sch_milestone_to_sch_empl_schedule)) {
					rMilestone.sch_milestone_to_sch_empl_schedule.emplsch_start = rMilestone.ms_date_scheduled;
					rMilestone.sch_milestone_to_sch_empl_schedule.emplsch_end = rMilestone.ms_date_due;
	            }

			}
        }

        if (bCanMilestoneBeMoved) {
            if (bEquipChanged && !bEquipChangedBack) {
				changeResource(rMilestone, sOldResID, sNewResID, iResType);
				updateEventResource(rMilestone.ms_id.toString(), sNewResID);
            }
            else if (bEmpChanged) {
				changeResource(rMilestone, sOldResID, sNewResID, iResType);
				updateEventResource(rMilestone.ms_id.toString(), sNewResID);
            }
        }
        
        milestoneMove = bCanMilestoneBeMoved;
    }

    if (!bCanMilestoneBeMoved) {
    	_bNeedShowWarningDialog = true;

        // if bTimeNeededRecalced we need to revert to old resource time needed - since the move failed 
        if (bTimeNeededRecalced) {
            /**@type {JSRecord<db:/avanti/eq_equipment>} */
            var rOldEquip = scopes.avDB.getRec('eq_equipment', ['equip_id'], [sOldResID.toString()]);
            
            if (rOldEquip) {
                scopes.avScheduling.setTimeNeededForNewEquip(rMilestone, rOldEquip);
            }
        }
        
        // if we attempted move and it failed have to make sure capacity recs are cleared, then re-schedule so it updates cap recs to old time 
        if (bAttemptedMove) {
            scopes.avScheduling.clearMilestoneCapacity(rMilestone);
            scopes.avScheduling.getNextFreeSpot(rMilestone, 'F', null, true, null, true, null, sOldResID);
        }
    }
	
    databaseManager.saveData();

    // move predecessors and successors - event has been moved back
	
    nDiffMins=0;
	if (dNewStartDate < dOldStartDate) { 
		// if there is a predecessor push it back
		//TODO
		if (event.extendedProps.predecessorId) {
			nDiffMins = getDiffInMinutes(new Date(oldEvent.end), new Date(event.end));
			pushPredecessorsBack(event.id, nDiffMins);
		}
	
		// if there is a successor pull it back - if checkbox is on
		if (event.extendedProps.successorId) {	
			pullSuccessorsBack(event.id, nDiffMins);
		}
	}

	// event has been moved forward
	else if (dNewEndDate > dOldEndDate) { 
		// if there is a successor push it forward
		
		if (event.extendedProps.successorId) {
			nDiffMins = getDiffInMinutes(new Date(oldEvent.end), new Date(event.end));
			pushSuccessorsForward(event.id, nDiffMins, new Date(event.start).getTime(), new Date(event.end).getTime());
		}
	
		// if there is a predecessor pull it forward - if checkbox is on
		if (event.extendedProps.predecessorId) {
			pullPredecessorsForward(event.id);
		}
	}

	//false means revert to original position
	return bCanMilestoneBeMoved;
	
	function getDiffInMinutes(dStartDate, dStopDate){
		var nMillisecondsDiff = dStopDate - dStartDate;
		if(nMillisecondsDiff==0){
			return 0;
		}
		else{
			return nMillisecondsDiff / 1000 / 60;
		}
	}
}

/**
 * <b>onEventChangeMethodID</b> will be called after an event has been modified in some way.
 *
 * @param {CustomType<svy-fullcalendar2.EventObject>} event
 * @param {CustomType<svy-fullcalendar2.EventObject>} oldEvent
 * @param {Array<CustomType<svy-fullcalendar2.EventObject>>} relatedEvents
 *
 * @return {Boolean} if it returns false, the event change action will be reverted, otherwise (true) the action is considered valid
 *
 * @properties={typeid:24,uuid:"F741A8FD-D96E-4483-835D-9304CCE71D97"}
 */
function onEventChange(event, oldEvent, relatedEvents) {
	
	if(_bNeedShowWarningDialog){
      scopes.avScheduling.showMoveFailMsg();
      _bNeedShowWarningDialog = false;
	}
	if(_bScheduleBoardLockedWarning){
        scopes.avScheduling.showScheduleBoardLockedMessage();
        _bScheduleBoardLockedWarning = false;
	}
	
	if(_bShowErrorRecalculatingTimeNeeded){
		scopes.avText.showWarning("errorRecalculatingTimeNeeded")
        _bShowErrorRecalculatingTimeNeeded = false;
	}

	loadView();
	
	return true;
}

/**
 * <b>onDatesSetMethodID</b> will be called after the calendar’s date range has been initially set or changed in some way and the DOM has been updated.
 *
 * @param {date} start
 * @param {date} end
 * @param {String} startStr
 * @param {String} endStr
 * @param {String} timeZone
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @properties={typeid:24,uuid:"0395B69F-657E-4D39-85DB-907278B3347C"}
 */
function onDatesSet(start, end, startStr, endStr, timeZone, view) {
	var view = elements.calendar.getView();
	
	if (view.type != null && view.type == "resourceTimeGridDay" || view.type == "resourceTimelineDay") {
		addNonShiftTimeExceptonsForAllResourcesForThisDate(start);
	} else {
		clearBackgroundExceptions();
	}

	scopes.avUtils.devOutputTime("set date");

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"DEAEFB30-5B86-4FDD-8E8F-6B937EC3DD3E"}
 */
function onDataChange_deptID(oldValue, newValue, event)
{
	var rViewResource = forms.sch_view_resources_tbl.foundset.getSelectedRecord();
	if (rViewResource)
	{
		// Set the VL with the necessary data
        scopes.avVL.load_avSchedule_viewResourceEquip();
        scopes.avVL.load_avSchedule_viewResourceEmpl();
	}
	
	forms.sch_view_resources_tbl.dept_id = newValue;
	
	return true
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"C0DEDA25-**************-897FD517A023"}
 */
function onDataChange_equipID(oldValue, newValue, event) {
    var rViewResource = forms.sch_view_resources_tbl.foundset.getSelectedRecord();

    if (rViewResource) {
        rViewResource.equip_id = newValue;
        createCapRecsForViewResource(rViewResource);
    }
    
    return true;
}

/**
 * @param {JSRecord<db:/avanti/sch_view_resource>} rViewResource
 *
 * @properties={typeid:24,uuid:"48F74B66-407D-413B-AADD-AE752BD2338F"}
 */
function createCapRecsForViewResource(rViewResource){
    var dCurDateTime = application.getTimeStamp();

    // create capacity recs for today - so the 'available' cbox will display correctly
    if (utils.hasRecords(rViewResource.sch_view_resource_to_eq_equipment)) {
        var rEquip = rViewResource.sch_view_resource_to_eq_equipment.getRecord(1);
        scopes.avScheduling.createEquipmentCapacityRecords(rEquip, null, dCurDateTime, 'F', true);
    }
    
    if (utils.hasRecords(rViewResource.sch_view_resource_to_sys_employee)) {
        var rEmp = rViewResource.sch_view_resource_to_sys_employee.getRecord(1);
        scopes.avScheduling.createEmployeeCapacityRecords(rEmp, null, dCurDateTime, null, 'F', true);
    }

    databaseManager.recalculate(rViewResource);
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"F4CFE41D-AE69-4822-A1EC-EDE37B55E243"}
 */
function onDataChange_emplID(oldValue, newValue, event) {
    var rViewResource = forms.sch_view_resources_tbl.foundset.getSelectedRecord();

    if (rViewResource) {
        rViewResource.empl_id = newValue;
        createCapRecsForViewResource(rViewResource);
    }
    
    return true;
}


/**
 * <b>onNavLinkDayClickMethodID</b> when navLinks setting is true, will be called when the user clicks on a day. onDateClickMethodID will not be called in this scenario.
 *
 * @param {date} date
 * @param {JSEvent} jsEvent
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BDEC1879-5042-4CAA-A0BF-514D5E02FC7C"}
 */
function onNavLinkDayClick(date, jsEvent) {
	elements.calendar.changeView("timeGridDay", date);
}

/**
 * <b>onEventResizeStartMethodID</b> will be called when event resizing begins.
 *
 * @param {CustomType<svy-fullcalendar2.EventObject>} event
 * @param {JSEvent} jsEvent
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @private
 *
 * @properties={typeid:24,uuid:"74599920-4D13-4ADB-8DD8-57BC6EE6B359"}
 */
function onEventResizeStart(event, jsEvent, view) {
	return false;
}

/**
 * @param {CustomType<svy-fullcalendar2.EventObject>} event
 * @param {JSEvent} jsEvent
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BCF028EB-9CC2-4010-B3D7-D22777DD2EA9"}
 */
function onEventRightClick(event, jsEvent, view) {
	if (globals.nav.mode != "browse" && event.id) {
		/**@type {JSRecord<db:/avanti/sch_milestone>} */
		var rMS = scopes.avDB.getRec("sch_milestone", ["ms_id"], [event.id]);
		var bUseContextMenu = false;
		
		if (rMS) {
			if (bUseContextMenu) {
				createContextMenu(rMS, jsEvent);
			} 
		}
	}
}

/**
 * @private 
 * 
 * @param {JSRecord<db:/avanti/sch_milestone>} rMS
 * @param {JSEvent} jsEvent
 *
 * @properties={typeid:24,uuid:"3139EF7E-5534-488B-80B2-B1C5C639EEF6"}
 */
function createContextMenu(rMS, jsEvent) {
    /** @type {RuntimeComponent} */
    var _source = jsEvent.getSource();
    
	if (rMS && _source) {
	    var menu = plugins.window.createPopupMenu()
	    var menuParent = null;
	    var menuItem = null;

		if (rMS.ms_flg_locked) {
		    menuParent = menu.addMenu("Unlock");
		    
		    menuItem = menuParent.addMenuItem("Task", unlockTask);
		    menuItem.methodArguments = [rMS.ms_id];
		    
		    menuItem = menuParent.addMenuItem("Section", unlockSection);
		    menuItem.methodArguments = [rMS.ms_id];
		    
		    menuItem = menuParent.addMenuItem("Job", unlockJob);
		    menuItem.methodArguments = [rMS.ms_id];
		} 
		else {
			menuParent = menu.addMenu("Lock");
		    
		    menuItem = menuParent.addMenuItem("Task", lockMilestone);
		    menuItem.methodArguments = [rMS.ms_id];
		    
		    menuItem = menuParent.addMenuItem("Section", lockSection);
		    menuItem.methodArguments = [rMS.ms_id];
		    
		    menuItem = menuParent.addMenuItem("Job", lockJob);
		    menuItem.methodArguments = [rMS.ms_id];
		}
		
        menu.show(_source);
	}
}

/**
 * <b>onEventDblClickMethodID</b> will be called when the user dbl click an event.
 *
 * @param {CustomType<svy-fullcalendar2.EventObject>} event
 * @param {JSEvent} jsEvent
 * @param {CustomType<svy-fullcalendar2.ViewType>} view
 *
 * @private
 *
 * @properties={typeid:24,uuid:"6E04D719-2AA0-4C22-8907-87F2609B0F09"}
 */
function onEventDblClick(event, jsEvent, view) {
	if (globals.nav.mode != "browse" && event.extendedProps.tooltip) {
		forms.tooltip.hide();
		forms.sch_sb_context_menu.show(jsEvent.getX() - 250, jsEvent.getY() - 300, event.id.toString());
	}
}
