/**
 * @properties={typeid:35,uuid:"6110F07F-A7D3-473B-B684-C937FF9E686C",variableType:-4}
 */
var bHitOK = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"0EB4A325-872F-4128-A0AE-902AF8FB7360",variableType:4}
 */
var _iLastCheckNumAdded = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4F582878-0794-4028-9C77-669D2E1BD992"}
 */
var _sRenderedItems = '';

/**
 * @properties={typeid:35,uuid:"0D5335EB-EB7C-4F7F-B535-E8983826BC59",variableType:-4}
 */
var _aCheckNames = [];

/**
 * @properties={typeid:35,uuid:"72C87808-11E8-4BD3-9BC6-B74DA62BE3AF",variableType:-4}
 */
var aRenderedJobs = [];

/**
 * @properties={typeid:35,uuid:"E0052DE2-A361-4D17-B71B-03DD00905281",variableType:-4}
 */
var aRenderedSections = [];

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3367B815-CFF7-4BCC-9930-0C55578EB289"}
 */
function onActionOK(event) {
//	var continueResource = false
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			resetPlaceHolder();
			return false;
		}
		
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			resetPlaceHolder();
			return false;
		}
		
		if(foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'));
			resetPlaceHolder();
			return false;
		}
	}
	resetPlaceHolder();
	bHitOK=true;
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
//	forms.sf_main_dtl_labour.continueResourceComplete()
//	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5EF1CDD6-5F1D-45A5-9FC4-E8B010D89BEE"}
 */
function onActionCancel(event) {
	resetPlaceHolder();
	bHitOK=false;
	globals.DIALOGS.closeForm(event);
	return false;
}

/**
 * @properties={typeid:24,uuid:"920B4933-45F4-4190-BE92-8F16256F625D"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0;
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0;
		
	}
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @protected
 *
 * @properties={typeid:24,uuid:"EA567FCC-C472-4E39-8EDC-01F85CCDB488"}
 */
function onShow(firstShow, event) {
	_iLastCheckNumAdded = 0;
	_sRenderedItems = '';
	_aCheckNames = [];
	elements.qa_check_1.visible = true;
	elements.qa_check_field_1.visible = true;
	elements.qa_check_2.visible = true;
	elements.qa_check_field_2.visible = true;
	elements.qa_check_field_3.visible = true;
	elements.qa_check_3.visible = true;
	
	if(!qa_check_pos_1){
		elements.qa_check_1.visible = false;
		elements.qa_check_field_1.visible = false;
	}
	if(!qa_check_pos_2){
		elements.qa_check_2.visible = false;
		elements.qa_check_field_2.visible = false;
	}
	if(!qa_check_pos_3){
		elements.qa_check_3.visible = false;
		elements.qa_check_field_3.visible = false;
	}
}
