borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"70,-1,-1,13,140,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"13",
right:"-1",
top:"70",
width:"140"
},
enabled:true,
formIndex:3,
labelFor:"_sPaymentsBasedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.paymentsBasedOn",
visible:true
},
name:"_sPaymentsBasedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"06148F5C-3D02-4925-B43A-6440F40772D0"
},
{
cssPosition:"14,-1,-1,13,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"13",
right:"-1",
top:"14",
width:"140"
},
enabled:true,
formIndex:1,
labelFor:"asDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.dateAsOf",
visible:true
},
name:"component_41C70B1B",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2338808B-1AFB-4DE6-9E4C-CCC6FE6A8292"
},
{
cssPosition:"70,-1,-1,158,140,22",
formIndex:2,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"70",
width:"140"
},
dataProviderID:"_sPaymentsBasedOn",
enabled:true,
formIndex:2,
onDataChangeMethodID:"A771D753-8582-43E5-A166-A279E9D8B856",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"EAB5FF47-F115-4943-A3F4-95ABD141AF3A",
visible:true
},
name:"_sPaymentsBasedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2920EF75-AEC2-4725-8368-52496FE77370"
},
{
cssPosition:"41,-1,-1,158,140,22",
formIndex:1,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"41",
width:"140"
},
dataProviderID:"_basedOn",
enabled:true,
formIndex:1,
onDataChangeMethodID:"02DD51DA-D2CC-4548-BC98-7AFA3AFDE74E",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2AEB8639-FBAA-4F6B-B650-07B708369F0B",
visible:true
},
name:"_basedOn",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"387E3C9F-28CB-4F58-8B0A-DE189CD7D825"
},
{
cssPosition:"7,-1,-1,5,690,114",
json:{
cssPosition:{
bottom:"-1",
height:"114",
left:"5",
right:"-1",
top:"7",
width:"690"
},
enabled:true,
formIndex:0,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_5F957725",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"404D50E7-5C4C-4288-9CBC-C739C61B47A3"
},
{
cssPosition:"41,-1,-1,13,140,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"13",
right:"-1",
top:"41",
width:"140"
},
enabled:true,
formIndex:3,
labelFor:"_basedOn",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.basedOn",
visible:true
},
name:"_basedOn_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9409B116-15FE-43FC-9FF8-B603D29D94CF"
},
{
height:200,
partType:5,
typeid:19,
uuid:"BF33E46B-D363-4091-86D1-C67A35577264"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:281,
typeid:19,
uuid:"D25A8D22-A76E-4B35-BDA6-80D7522B2EA9"
},
{
cssPosition:"14,-1,-1,158,140,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"158",
right:"-1",
top:"14",
width:"140"
},
dataProviderID:"asAtDate",
enabled:true,
formIndex:2,
format:"yyyy-MM-dd",
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"asDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"FF022596-239B-464C-9383-8C659D7073AD"
}
],
name:"rpt_report_dlg_ar_summary",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"14CB6BA7-5AB3-4574-8DED-8CE4A6214A21"