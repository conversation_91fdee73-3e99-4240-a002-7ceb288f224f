/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D0D3C9CA-E6C2-45F4-A6FD-E2DCDA95FC5C",variableType:4}
 */

/**
 * @properties={typeid:35,uuid:"3AB3D49A-B07F-47E2-B57B-E7E79ABCFA9C",variableType:-4}
 */
var bQtyProducedChanged = false;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"CF59F913-B1BA-4117-9108-8C8D6E1FF28D",variableType:4}
 */
var $hidden_row_1_y_value = 58;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"4EA5B2CF-DF45-419A-B880-25DC2E3632D6",variableType:4}
 */
var $hidden_row_2_y_value = 85;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"EDC9F3BC-6A78-41B4-ACF9-34CEDE846556",variableType:4}
 */
var $hidden_row_3_y_value = 112;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"626706FF-FCF1-4D00-A314-7EC58DFF57ED",variableType:4}
 */
var $hidden_row_4_y_value = 139;

///////////////////////////////////////////////////////////////////////////////////////////

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"78608EF9-4215-4CEB-9560-362081FE67F7",variableType:4}
 */
var $hidden_row_5_y_value = 166;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"4218F9BD-AEFE-4475-A60C-6B5F47897A7C",variableType:4}
 */
var $showing_row_1_y_value = 103;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"67E99CAD-F57A-403B-9A37-5ECDE920384F",variableType:4}
 */
var $showing_row_2_y_value = 130;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"B03B0098-CC8E-43B6-810B-4789F16BB8A6",variableType:4}
 */
var $showing_row_3_y_value = 157;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"D004638F-CA2E-4437-AD6B-A93CAA64B024",variableType:4}
 */
var $showing_row_4_y_value = 184;

///////////////////////////////////////////////////////////////////////////////////////////

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C91A69F6-5523-4FB1-803F-DB46D724393D",variableType:4}
 */
var $showing_row_5_y_value = 211;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4DB81DB3-5B50-42E9-8BCA-4F97A1B26651"}
 */
var selected_rush_code = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8D3C3B6D-5835-45FA-A673-E50C956F8D01"}
 */
var selected_extra_code = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"53184CF2-4008-42A4-8B42-B47675F5EBE6"}
 */
var selected_documents = '';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"0EDCB455-0362-47AB-8292-803424D7D209",variableType:4}
 */
var total_elapsed_in_minutes;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"9B089069-4325-4160-AE63-972DF8D6F8CD"}
 */
var start_date_to_locale = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"00E3DBC2-0A16-4965-AC6C-AAE7E5F26449"}
 */
var end_date_to_locale = '';

/**
 * Complete an operation and reload operations in progress.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"F999722A-BF58-4A66-97CA-FA2F726BB079"}
 */
function onActionOperationComplete(event) {
	
    if(!validateQtyProduced()) {
        return;
    }
    
    if (checkQAChecks()) {
    	/**@type {Array<JSRecord<db:/avanti/prod_job_cost_labour>>} */
    	var arLabours = [];
    	
		if (scopes.globals.sf_main_dtl_material_button_validateEntries(forms.sf_main_dtl_labour_material.foundset)) {
			if (scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(foundset)) {
				arLabours = completeOperationMultipleJobs('Complete')
			}
			else {
				var bJcmeMatchesPickList = forms.sf_main_dtl.checkAndUpdatePickList();
				if (!bJcmeMatchesPickList) {
					var answer = globals.DIALOGS.showQuestionDialog('Confirmation', i18n.getI18NMessage('avanti.dialog.ShopFloorMatChange_msg'),
						i18n.getI18NMessage('avanti.dialog.yes'),
						i18n.getI18NMessage('avanti.dialog.no'));
					if (answer == i18n.getI18NMessage('avanti.dialog.yes')) {
						forms.sf_main_dtl.checkAndUpdatePickList(true);
					}
				}

				var dataParams = {
					status: 'Complete',
					selected_job_id: forms.sf_main_dtl.selected_job_id,
					selected_ordrevds_id: forms.sf_main_dtl.selected_ordrevds_id,
					current_employee_uuid: forms.sf_main_dtl.current_employee_uuid
				}
				
				if (scopes.globals.completeOperationFunction(foundset, dataParams, forms.sf_main_dtl_labour_material.foundset, null, null, null)) {
					arLabours = [foundset.getSelectedRecord()];
				}
			}
			
			forms.sf_main_dtl.loadEmployeeOperations();
			forms.sf_login.continueLoginProcess();
			
			if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackStagingLocation) && arLabours.length > 0) {
				forms.sf_staging_locations.loadData(arLabours);
				globals.DIALOGS.showFormInModalDialog(forms.sf_staging_locations, -1, -1, 750, 500, i18n.getI18NMessage("avanti.lbl.stagingLocation"), true, false, "dlgStagingLocation", true);
			}
		}
	} 
	else {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
			i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks'),
			i18n.getI18NMessage('avanti.dialog.ok'))
	}

}

/**
 * @return {Array<JSRecord<db:/avanti/prod_job_cost_labour>>}
 * 
 * @properties={typeid:24,uuid:"1DA2CDE4-3064-4BFF-B7AD-EC2AE8BF4785"}
 */
function completeOperationMultipleJobs(sStatus){
	
    //SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

        jcl_end_datetime = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
    }
    else {
        
        jcl_end_datetime = application.getTimeStamp();
    }
	var nDuration = null;
	var nNumJobs = databaseManager.getFoundSetCount(prod_job_cost_labour_to_prod_shopfloor_labor_job);
	/**@type {JSRecord<db:/avanti/prod_job_cost_labour>} */
	var rLabor;
	var aAddlJobLabs = [];
	var i;
	var rCurLabor = foundset.getSelectedRecord();
    var nShopfloorTimeDistMethod = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ShopfloorTimeDistributionMethod);
    var nTotBudgetedOpTime = null;
    var nTotBudgetedImpressions = null;
    var nTotOrderedQty = null;
    var sAddlStatus = sStatus;
    
	// create lab recs for addl jobs - have to do this before completing main lab rec or addl job lab recs (which are duplicated
	// from main lab rec) will be populated with values (like duration) that we dont want  
	prod_job_cost_labour_to_prod_shopfloor_labor_job.sort('sequence_nr asc');
	
	if (nNumJobs > 1) {
		rCurLabor.jcl_qty_produced = prod_job_cost_labour_to_prod_shopfloor_labor_job.getRecord(1).quantity_produced;
	}
	
    for (i = 2; i <= nNumJobs; i++) {
        var rAddlJob = prod_job_cost_labour_to_prod_shopfloor_labor_job.getRecord(i);
        rLabor = scopes.globals.sf_main_dtl_labour_createLaborRecFromAddlJob(foundset, rAddlJob, nNumJobs, i);
        rLabor.jcl_qty_produced = rAddlJob.quantity_produced;
        aAddlJobLabs.push(rLabor);
    }

	// if pro-rate by budgeted time then getTotBudgetedOpTime
    if (nShopfloorTimeDistMethod == 2) {
        nTotBudgetedOpTime = scopes.globals.sf_main_dtl_labour_getTotBudgetedOpTime(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedOpTime then revert to default dist
        if (!nTotBudgetedOpTime) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    else if (nShopfloorTimeDistMethod == 3) {
        nTotBudgetedImpressions = scopes.globals.sf_main_dtl_labour_getTotBudgetedImpressions(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedImpressions then revert to default dist
        if (!nTotBudgetedImpressions) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    else if (nShopfloorTimeDistMethod == 4) {
        nTotOrderedQty = scopes.globals.sf_main_dtl_labour_getTotLineItemOrderedQty(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedImpressions then revert to default dist
        if (!nTotOrderedQty) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    
    // now complete addl lab recs
	for (i = 0; i < aAddlJobLabs.length; i++) {
		rLabor = aAddlJobLabs[i];
		foundset.loadRecords(rLabor.jcl_id);
		nDuration = scopes.globals.sf_main_dtl_labour_getProRatedDuration(foundset, rLabor, nTotBudgetedOpTime, nTotBudgetedImpressions, nNumJobs, nShopfloorTimeDistMethod, false, nTotOrderedQty);

		// if we are pausing a time entry with addl jobs then we want to call completeOperation() with an Unfinished status, otherwise it will create paused recs for these addl jobs, which we dont want
		if (sStatus == "Paused") {
			sAddlStatus = "Unfinished";
		}
		completeOperation(sAddlStatus, jcl_end_datetime, rLabor, nDuration);
	}
    
    
    // complete main lab rec
	foundset.loadRecords(rCurLabor.jcl_id);
	nDuration = scopes.globals.sf_main_dtl_labour_getProRatedDuration(foundset, rCurLabor, nTotBudgetedOpTime, nTotBudgetedImpressions, nNumJobs, nShopfloorTimeDistMethod, true, nTotOrderedQty);
	completeOperation(sStatus, jcl_end_datetime, null, nDuration);

	aAddlJobLabs.push(rCurLabor);
	
	return aAddlJobLabs;
}

/**
 * @return
 * @properties={typeid:24,uuid:"F44A91F1-66BA-4238-AE2C-F9B787779786"}
 */
function checkQAChecks() {
	if (elements.qa_check_field_1.visible && !foundset.jcl_qa_field_1_complete) {
		return false
	}

	if (elements.qa_check_field_2.visible && !foundset.jcl_qa_field_2_complete) {
		return false
	}

	if (elements.qa_check_field_3.visible && !foundset.jcl_qa_field_3_complete) {
		return false
	}
	return true
}

/**
 * Complete an operation by setting end date time and duration.
 *
 * @param {String} status
 * @param {Date} [end_datetime_param]
 * @param {JSRecord<db:/avanti/prod_job_cost_labour>} [rLabor]
 * @param {Number} [nDuration]
 *  
 * @return
 * @properties={typeid:24,uuid:"EFA16096-853E-4625-9100-AC3E6F48B94A"}
 * @AllowToRunInFind
 */
function completeOperation(status, end_datetime_param, rLabor, nDuration) {
	var bReplaceMS = globals["avSecurity_checkForUserRight"]('Shop_Floor', 'replace_budgeted_milestone', 
			globals.getUserFromEmp(globals.avShopFloor_employeeID));
	
	if (foundset.jcl_start_datetime == null) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.dialog.ok'))
		return false
	} 
	else {
		var dStop = application.getTimeStamp();
		
		//SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
	    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

	        dStop = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
	    }
	    else {
	        
	        dStop = application.getTimeStamp();
	    }
	    
		var bAddlJob = false;
		var fsLabor = null;
		
		if(rLabor){
			bAddlJob = true;
		}
		else{
			rLabor = foundset.getSelectedRecord();
		}
		
		if (end_datetime_param != null) {
			rLabor.jcl_end_datetime = end_datetime_param;
		} 
		else if(!end_datetime_param && dStop >= rLabor.jcl_start_datetime){
			
			
		    //SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
	        if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

	            rLabor.jcl_end_datetime =  scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
	        }
	        else {
	            
	            rLabor.jcl_end_datetime = application.getTimeStamp();
	        }
	        
	        
		}

		if(nDuration){
			rLabor.jcl_duration = nDuration;
		}
		else{
			rLabor.jcl_duration = rLabor.jcl_end_datetime - rLabor.jcl_start_datetime;
		}

		if (status == 'Paused') {
			// sl-4332 - then create a new duplicate paused rec
			var pausedRec = foundset.getRecord(foundset.duplicateRecord(false, false))

			// sl-4332 - just save this rec as complete
			jcl_is_paused = 0
			//jcl_is_complete = 1
			jcl_is_unfinished = 1
			jcl_cur_lab_rec_id = pausedRec.jcl_id
			databaseManager.saveData(foundset)

			pausedRec.jcl_is_paused = true
			pausedRec.jcl_is_complete = false
			pausedRec.jcl_end_datetime = null
			pausedRec.jcl_duration = null

			databaseManager.saveData(pausedRec)
			
			// sl-21318 - if this time entry has addl jobs they have to be copied over to the new pausedRec
			if (utils.hasRecords(rLabor.prod_job_cost_labour_to_prod_shopfloor_labor_job)) {
				for (var j = rLabor.prod_job_cost_labour_to_prod_shopfloor_labor_job.getSize(); j >= 1; j--) {
					var rAddlJob = rLabor.prod_job_cost_labour_to_prod_shopfloor_labor_job.getRecord(j);
					rAddlJob.jcl_id_parent = pausedRec.jcl_id;
				}
			}

			/** @type{JSFoundSet<db:/avanti/sf_message>} */
			var msg_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sf_message')
			var msg_rec = msg_fs.getRecord(msg_fs.newRecord())
			var jobNum = prod_job_cost_labour_to_prod_job.job_number
			var sectionDesc = prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.ordrevds_description
			var opName
			if (prod_job_cost_labour_to_prod_job_cost.msgrp_id) {
				opName = prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sys_cost_centre.cc_desc
			} 
			else if (prod_job_cost_labour_to_prod_job_cost.cc_id) {
				opName = prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_cost_centre.cc_desc
			}

			msg_rec.message_main = scopes.avText.createParamMsg('avanti.lbl.shopFloorPauseJob', [opName, sectionDesc, jobNum])

			msg_rec.job_id = forms.sf_main_dtl.selected_job_id
			msg_rec.ordrevds_id = forms.sf_main_dtl.selected_ordrevds_id
			msg_rec.empl_id = forms.sf_main_dtl.current_employee_uuid
			msg_rec.jcl_id = pausedRec.jcl_id
			msg_rec.is_pause_msg = 1
			databaseManager.saveData(msg_rec)

		} 
		else { //Set the buttons and status of the job if it is not a paused-operation
			if (rLabor.jcl_qty_produced) {
				if (!utils.hasRecords(rLabor.prod_job_cost_labour_to_prod_job_milestone_progress)) {
					var bSuccess;
					
					if(bAddlJob){
						bSuccess = scopes.globals.createJobProgressEntry(foundset, true, rLabor);
					}
					else{
						bSuccess = scopes.globals.createJobProgressEntry(foundset, true);
					}
					
					if(!bSuccess){
						return false;
					}
				}
			}

			//Calculate the number of clicks and create a cost entry when operation is unfinished or completed
			if (status === 'Unfinished') {
				scopes.globals.calculateClicks(rLabor, 2);
			} else {
				scopes.globals.calculateClicks(rLabor, 1);
			}

			if(bAddlJob){
				if (status === 'Unfinished') {
					rLabor.jcl_is_paused = false;
					rLabor.jcl_is_unfinished = true;
					rLabor.jcl_is_complete = false;
				} 
				else {
					rLabor.jcl_is_paused = false;
					rLabor.jcl_is_unfinished = false;
					rLabor.jcl_is_complete = true;
				}
			}
			else{
				for (var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
					if (status === 'Unfinished') {
						foundset.getRecord(fs_idx).jcl_is_paused = false;
						foundset.getRecord(fs_idx).jcl_is_unfinished = true;
						foundset.getRecord(fs_idx).jcl_is_complete = false;
					} 
					else {
						foundset.getRecord(fs_idx).jcl_is_paused = false;
						foundset.getRecord(fs_idx).jcl_is_unfinished = false;
						foundset.getRecord(fs_idx).jcl_is_complete = true;
					}
					
					//9332 We are trying to find the paused entries  that correspond to the
					//current prod_job_cost_labour that is  going to be  put on complete
					// if  there exist paused foundsets that correspond to the current labour
					// we  iterate through them in  order  to set jcl_cur_lab_rec_id to null
					// and we set  has_been_paused to true in order for them to not be completed
					// by the complete-milestone and complete-category buttons 
					var  pausedFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
					if(pausedFoundset.find()){
						pausedFoundset.job_id = foundset.getRecord(fs_idx).job_id;
						pausedFoundset.jcl_cur_lab_rec_id ='!^';
					    pausedFoundset.jc_id =  foundset.getRecord(fs_idx).jc_id;
						if(pausedFoundset.search()){
							  for(var i = 1 ; i<=pausedFoundset.getSize();i++){
								  pausedFoundset.getRecord(i).jcl_cur_lab_rec_id = null;
	                              pausedFoundset.getRecord(i).has_been_paused  = true;							  
	                              databaseManager.saveData(pausedFoundset);
							  }
						}
					
					}
				}
			}

			if(bAddlJob){
				scopes.globals.commitMatTrans(forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_labour_material.foundset, rLabor);
			}
			else{
				scopes.globals.commitMatTrans(forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_labour_material.foundset);
			}

			// checkIfMilestoneCompleted() requires a foundset - covert rLabor to fs
			if(bAddlJob){
				databaseManager.saveData(rLabor);
				fsLabor = scopes.avDB.convertRecordToFoundset(rLabor, 'prod_job_cost_labour');
				fsLabor.jcl_is_complete = rLabor.jcl_is_complete;
			}
			else{
				fsLabor = foundset;
			}
			
			if (utils.hasRecords(rLabor, 'prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group')) {
				/** @type{JSRecord<db:/avanti/sch_milestone_group>} */
				var operation_rec = rLabor.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.getSelectedRecord()
				operation_rec.msgrp_date_started = rLabor.jcl_start_datetime
				operation_rec.msgrp_date_ended = rLabor.jcl_end_datetime
				
				// sl-2408 - the only diff between complete and unfinished is marking he msgrp as complete
				if (status == 'Complete') {
					operation_rec.msgrp_flg_completed = 1
					operation_rec.completed_by_id = globals.avShopFloor_employeeID;
					scopes.avShopFloor.checkIfMilestoneCompleted(rLabor.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group,
					    fsLabor, null, false, false, null, null);
				}
			}
			else if(status=='Complete' && bReplaceMS){
				var rJobCost = rLabor.prod_job_cost_labour_to_prod_job_cost.getRecord(1);
				/** @type{JSFoundSet<db:/avanti/sch_milestone> }*/
				var milestone_fs = scopes.avDB.getFS('sch_milestone', 
					['cc_id', 'ordrevds_id', 'opcat_id', 'dept_id'], 
					[rJobCost.cc_id, rJobCost.ordrevds_id, rJobCost.opcat_id, rJobCost.dept_id]);
				
				if(milestone_fs.getSize() > 0){
					scopes.avShopFloor.checkIfMilestoneAddedIsCompleted(milestone_fs, fsLabor);
				}
				// addl job would have an ms rec yet - have to create here
				else if(bAddlJob){
					var rMS = scopes.avShopFloor.createMilestoneForOtherOpGlobal(rJobCost.ordrevds_id, rJobCost.cc_id);
					
					if(rMS){
						milestone_fs = scopes.avDB.convertRecordToFoundset(rMS, 'sch_milestone');
						scopes.avShopFloor.checkIfMilestoneAddedIsCompleted(milestone_fs, fsLabor);
					}
				}
			}

			forms.sf_main_dtl_labour_buttons.elements.operation_complete_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.milestone_complete_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.resource_complete_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.section_complete_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.pause_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.unfinished_btn.visible = false
			forms.sf_main_dtl_labour_buttons.elements.btn_start_and_exit.visible = false
		}

		databaseManager.saveData()

		return true
	}
}

/**
 * @param {JSFoundSet<db:/avanti/sch_milestone>} milestone_fs
 *
 * @return
 * @properties={typeid:24,uuid:"B1F09FFB-DEDE-4D04-B9CB-4548406EAE7A"}
 */
function islastMilestone(milestone_fs) {
	return (milestone_fs.sequence_nr = milestone_fs.sch_milestone_to_sch_schedule.sch_schedule_to_sch_milestone.getSize())
}

/**
 * Complete the operations related to this operation category.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"4E1608EF-25B8-4105-BE5D-A570D26A4127"}
 * @AllowToRunInFind
 */
function onActionResourceComplete(event) {

	// Check that an operation category can be completed.
	if (foundset.jcl_start_datetime == null) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.dialog.ok'))
	} 
	else if (!utils.hasRecords(foundset.prod_job_cost_labour_to_prod_job_cost)) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.shopFloorResourceError'),
			i18n.getI18NMessage('avanti.lbl.shopFloorResourceError'),
			i18n.getI18NMessage('avanti.dialog.ok'))
	} 
	else if (scopes.globals.sf_main_dtl_material_button_validateEntries(forms.sf_main_dtl_labour_material.foundset)) {
		var fsRecsWithQAChecks = scopes.globals.sf_main_dtl_labour_getRecsWithQAChecks(foundset);
		var bContinue = true;
		
		if (utils.hasRecords(fsRecsWithQAChecks)) {
			forms.sf_main_dtl_labour_qa_checks.foundset.loadRecords(fsRecsWithQAChecks)
			globals.DIALOGS.showFormInModalDialog(forms.sf_main_dtl_labour_qa_checks, -1, -1, 900, 400, 'QA Checks', true, false, 'QA Checks', true)
			var bHitOK = forms.sf_main_dtl_labour_qa_checks.bHitOK
			//sl-2351 - have to close here too - the closeform call from within the form didnt take
			globals.DIALOGS.closeForm(null, 'QA Checks');
			
			//sl-2351 - moved this call here from onActionOK in qa check form
			if (!bHitOK) {
				bContinue = false;
			}
		} 
		
		if(bContinue){
			var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('servoy.general.confirm'),
				i18n.getI18NMessage('avanti.lbl.shopFloorResourceCompleteQuestion'),
				i18n.getI18NMessage('avanti.dialog.ok'),
				i18n.getI18NMessage('avanti.dialog.cancel'))

			if (answer == i18n.getI18NMessage('avanti.dialog.ok')) {
				if(scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(foundset)){
					completeResourceMultipleJobs();
				}
				else{
					var bJcmeMatchesPickList = forms.sf_main_dtl.checkAndUpdatePickList();
					if (!bJcmeMatchesPickList) {
						var ans = globals.DIALOGS.showQuestionDialog('Confirmation', i18n.getI18NMessage('avanti.dialog.ShopFloorMatChange_msg'),
							i18n.getI18NMessage('avanti.dialog.yes'),
							i18n.getI18NMessage('avanti.dialog.no'));
						if (ans == i18n.getI18NMessage('avanti.dialog.yes')) {
							forms.sf_main_dtl.checkAndUpdatePickList(true);
						}
					}
					continueResourceComplete();
				}
				
				forms.sf_main_dtl.loadEmployeeOperations();
				forms.sf_login.continueLoginProcess();
			}
		}
	}
}

/**
 * @properties={typeid:24,uuid:"BF26A607-E328-4D37-908B-530973E1A04E"}
 */
function completeResourceMultipleJobs(){
    
    //SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

        jcl_end_datetime = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
    }
    else {
        
        jcl_end_datetime = application.getTimeStamp();
    }
	
	var nDuration = null;
	var nNumJobs = databaseManager.getFoundSetCount(prod_job_cost_labour_to_prod_shopfloor_labor_job);
	/**@type {JSRecord<db:/avanti/prod_job_cost_labour>} */
	var rLabor;
	var aAddlJobLabs = [];
	var rCurLabor = foundset.getSelectedRecord();
	var i;
    var nShopfloorTimeDistMethod = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.ShopfloorTimeDistributionMethod);
    var nTotBudgetedOpTime = null;
    var nTotBudgetedImpressions = null;
    var nTotOrderedQty = null;

	// create lab recs for addl jobs - have to do this before completing main lab rec or addl job lab recs (which are duplicated
	// from main lab rec) will be populated with values (like duration) that we dont want  
	prod_job_cost_labour_to_prod_shopfloor_labor_job.sort('sequence_nr asc');
	
	if (nNumJobs > 1) {
		rCurLabor.jcl_qty_produced = prod_job_cost_labour_to_prod_shopfloor_labor_job.getRecord(1).quantity_produced;
	}

    for (i = 2; i <= nNumJobs; i++) {
        var rAddlJob = prod_job_cost_labour_to_prod_shopfloor_labor_job.getRecord(i);
        rLabor = scopes.globals.sf_main_dtl_labour_createLaborRecFromAddlJob(foundset, rAddlJob, nNumJobs, i);
        rLabor.jcl_qty_produced = rAddlJob.quantity_produced;
        aAddlJobLabs.push(rLabor);
    }

    // if pro-rate by budgeted time then getTotBudgetedOpTime
    if (nShopfloorTimeDistMethod == 2) {
        nTotBudgetedOpTime = scopes.globals.sf_main_dtl_labour_getTotBudgetedOpTime(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedOpTime then revert to default dist
        if (!nTotBudgetedOpTime) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    else if (nShopfloorTimeDistMethod == 3) {
        nTotBudgetedImpressions = scopes.globals.sf_main_dtl_labour_getTotBudgetedImpressions(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedImpressions then revert to default dist
        if (!nTotBudgetedImpressions) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    else if (nShopfloorTimeDistMethod == 4) {
        nTotOrderedQty = scopes.globals.sf_main_dtl_labour_getTotLineItemOrderedQty(rCurLabor, aAddlJobLabs);

        // if we dont have a nTotBudgetedImpressions then revert to default dist
        if (!nTotOrderedQty) {
            nShopfloorTimeDistMethod = 1;
        }
    }
    
	// complete main lab rec
	foundset.loadRecords(rCurLabor.jcl_id);
    nDuration = scopes.globals.sf_main_dtl_labour_getProRatedDuration(foundset, rCurLabor, nTotBudgetedOpTime, nTotBudgetedImpressions, nNumJobs, nShopfloorTimeDistMethod, true, nTotOrderedQty);
	continueResourceComplete(null, jcl_end_datetime, nDuration);
	
	// now complete addl lab recs
    for (i = 0; i < aAddlJobLabs.length; i++) {
        rLabor = aAddlJobLabs[i];
        foundset.loadRecords(rLabor.jcl_id);
        nDuration = scopes.globals.sf_main_dtl_labour_getProRatedDuration(foundset, rLabor, nTotBudgetedOpTime, nTotBudgetedImpressions, nNumJobs, nShopfloorTimeDistMethod, false, nTotOrderedQty);
        continueResourceComplete(rLabor, jcl_end_datetime, nDuration);
    }
}

/**
 * Complete the operations related to this operation milestone.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"C4EDC99D-55FA-44E9-9C0F-BC00B0394D72"}
 * @AllowToRunInFind
 */
function onActionMilestoneComplete(event) {

    if(!validateQtyProduced()) {
        return;
    }
    
    var result = scopes.globals.sf_main_dtl_labour_onActionMilestoneComplete(foundset, forms.sf_main_dtl_labour_material.foundset);
   
    if(!result.success) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage(result.message),
			i18n.getI18NMessage(result.message),
			i18n.getI18NMessage('avanti.dialog.ok'))
    	return;
    }
    
    
	var fsRecsWithQAChecks = scopes.globals.sf_main_dtl_labour_getRecsWithQAChecks(foundset)
	var bContinue = true;
	
	if (utils.hasRecords(fsRecsWithQAChecks)) {
		forms.sf_main_dtl_labour_qa_checks.foundset.loadRecords(fsRecsWithQAChecks)
		globals.DIALOGS.showFormInModalDialog(forms.sf_main_dtl_labour_qa_checks, -1, -1, 760, 400, 'QA Checks', true, false, 'QA Checks', true)
		var bHitOK = forms.sf_main_dtl_labour_qa_checks.bHitOK
		//sl-2351 - have to close here too - the closeform call from within the form didnt take
		globals.DIALOGS.closeForm(null, 'QA Checks');
		
		//sl-2351 - moved this call here from onActionOK in qa check form
		if (!bHitOK) {
			bContinue = false;
		}
	}
	
	if(bContinue){
		var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('servoy.general.confirm'),
			i18n.getI18NMessage('avanti.lbl.shopFloorMileStoneCompleteQuestion'),
			i18n.getI18NMessage('avanti.dialog.ok'),
			i18n.getI18NMessage('avanti.dialog.cancel'));
	
        if (answer == i18n.getI18NMessage('avanti.dialog.ok')) {
        	// sl-1885 - we are creating a progress entry record in continueMilestoneComplete() now, so we need to validate comments entered if pref on 
        	if (jcl_qty_produced 
					&& !utils.hasRecords(prod_job_cost_labour_to_prod_job_milestone_progress)
					&& globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.QtyProducedCommentsRequired) 
					&& !globals.getComments()) {
						
				globals.showWarning('YouMustEnterAComment');
				globals.showComments(globals.getLblMsg('MilestoneProgressComments'), true);
			}
			else {
		    	/**@type {Array<JSRecord<db:/avanti/prod_job_cost_labour>>} */
		    	var arLabours = [];
		    	
				if (scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(foundset)) {
					arLabours = scopes.globals.sf_main_dtl_labour_completeMilestoneMultipleJobs(foundset, forms.sf_main_dtl_labour_material.foundset, forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id);
				}
				else {
					scopes.globals.sf_main_dtl_labour_continueMilestoneComplete(foundset, forms.sf_main_dtl_labour_material.foundset, forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, null, null, null);
					arLabours = [foundset.getSelectedRecord()];
				}
				
				forms.sf_main_dtl.loadEmployeeOperations();
				forms.sf_login.continueLoginProcess();
				
				if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackStagingLocation) && arLabours.length > 0) {
					forms.sf_staging_locations.loadData(arLabours);
					globals.DIALOGS.showFormInModalDialog(forms.sf_staging_locations, -1, -1, 750, 500, i18n.getI18NMessage("avanti.lbl.stagingLocation"), true, false, "dlgStagingLocation", true);
				}
			}
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/prod_job_cost_labour>} [rLabor]
 * @param {Date} [end_datetime_param]
 * @param {Number} [nDuration]
 * 
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"817D63F8-7582-44EC-9C38-D669DB746452"}
 */
function continueResourceComplete(rLabor, end_datetime_param, nDuration) {
	var bAddlJob = false;
	/**@type {JSFoundSet<db:/avanti/prod_job_cost_labour>} */
	var fsLabor = null;
	
	if(rLabor){
		bAddlJob = true;
		fsLabor = scopes.avDB.convertRecordToFoundset(rLabor, 'prod_job_cost_labour');
	}
	else{
		rLabor = foundset.getSelectedRecord();
		fsLabor = foundset;
	}
	
	// Setup the variables for start date time, end date time and total time spent.
	var completedTime;
	
    if (end_datetime_param) {
        completedTime = end_datetime_param;
    }
    else {
        //SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
        if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

            completedTime = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
        }
        else {

            completedTime = application.getTimeStamp();
        }
    }
	var startTime = rLabor.jcl_start_datetime;
	var totalMillisecondsSpent = nDuration ? nDuration : (completedTime - startTime);
	/** @type{JSFoundSet<db:/avanti/sch_milestone>} */
	var milestone_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sch_milestone');
	var aSiblingWebPressMilestoneIds = scopes.avShopFloor.getSiblingWebPressMilestoneIds(fsLabor);
	/** @type{JSDataSet} */
	var display_ds;
	var rJobCost = rLabor.prod_job_cost_labour_to_prod_job_cost.getRecord(1);
	var rSection = rJobCost.prod_job_cost_to_sa_order_revision_detail_section.getRecord(1);
	var rJob = rLabor.prod_job_cost_labour_to_prod_job;
	var rMS = null;
    var bReplaceMS = globals["avSecurity_checkForUserRight"]('Shop_Floor', 'replace_budgeted_milestone', 
            globals.getUserFromEmp(globals.avShopFloor_employeeID));

    if(bAddlJob && bReplaceMS && !utils.hasRecords(rJobCost.prod_job_cost_to_sch_milestone)){
        rMS = scopes.avShopFloor.createMilestoneForOtherOpGlobal(rJobCost.ordrevds_id, rJobCost.cc_id);
        
        if(rMS){
            rJobCost.ms_id = rMS.ms_id;
        }
    }
    
	// Loop through milestones of the current section that have the same category to find if there are cost entries for them and create them if there isn't one.
	if (milestone_fs.find()) {
		milestone_fs.ordrevds_id = rJobCost.ordrevds_id;
		milestone_fs.opcat_id = rJobCost.opcat_id

		if (milestone_fs.search()) {
			display_ds = createMultipleCostEntriesByLabourRecord2(rLabor, startTime, totalMillisecondsSpent)

			for (var h = 1; h <= milestone_fs.getSize(); h++) {
				rMS = milestone_fs.getRecord(h);
				
				rMS.ms_flg_completed = 1;
				rMS.ms_date_completed = completedTime;
				
				if (rMS.completed_by_id == null) {
					rMS.completed_by_id = globals.avShopFloor_employeeID;
				}
				
				databaseManager.saveData(rMS);
				scopes.avCost.updateLastCompletedMilestone(rMS);
			}
			
			scopes.avShopFloor.setReadyFlagOnSuccessorMilestones(milestone_fs);
		}
	}
	
	//Calculating clicks when the resource is completed
	scopes.globals.calculateClicks(rLabor, 1);

	// Create a dataset to load into the multiple labour entry view.
	scopes.globals.sf_main_dtl_labour_showMultipleEntryDataset(false, display_ds, totalMillisecondsSpent, rJob.job_number, rSection.ordrevds_description);

	forms.sf_main_dtl_cost.updateCompleteButtons(false);
	
	if(bAddlJob){
		scopes.globals.commitMatTrans(forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_labour_material.foundset, rLabor);
	}
	else{
		scopes.globals.commitMatTrans(forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_labour_material.foundset);
	}
	
	databaseManager.saveData(milestone_fs);

	//now complete and save the sibling web press milestones, if any
	if (aSiblingWebPressMilestoneIds !== null && aSiblingWebPressMilestoneIds.length > 0) {
		/** @type{JSFoundSet<db:/avanti/sch_milestone>} */
		var fsSiblingMilestones = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sch_milestone');

		if (fsSiblingMilestones.find()) {
			for (var i = 0; i < aSiblingWebPressMilestoneIds.length; i++) {
				fsSiblingMilestones.newRecord();
				fsSiblingMilestones.ms_id = aSiblingWebPressMilestoneIds[i];
			}

			if (fsSiblingMilestones.search() > 0) {
				for (var j = 1; j <= fsSiblingMilestones.getSize(); j++) {
					var rSiblingMilestone = fsSiblingMilestones.getRecord(j);
					
					rSiblingMilestone.ms_flg_completed = 1;
					rSiblingMilestone.ms_date_completed = completedTime;
					
					if (rSiblingMilestone.completed_by_id == null) {						
						rSiblingMilestone.completed_by_id = globals.avShopFloor_employeeID;
					}
				}

				scopes.avShopFloor.setReadyFlagOnSuccessorMilestones(fsSiblingMilestones);
				databaseManager.saveData(fsSiblingMilestones);
			}
		}
	}

	// this is done in sch_milestone_afterRecordUpdate() now
	scopes.avShopFloor.createFinishedGoodsTransactions(milestone_fs, fsLabor); 
}

/**
 * Complete the Section selected by creating cost entries for all of the operations.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B6BDCDF3-74C0-4A5E-8184-C01D40433156"}
 * @AllowToRunInFind
 */
function onActionSectionComplete(event) {
	if (foundset.jcl_start_datetime == null) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.lbl.shopFloorStartError'),
			i18n.getI18NMessage('avanti.dialog.ok'))
	} else {
		var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('servoy.general.confirm'),
			i18n.getI18NMessage('avanti.lbl.shopFloorSectionCompleteQuestion'),
			i18n.getI18NMessage('avanti.dialog.ok'),
			i18n.getI18NMessage('avanti.dialog.cancel'))

		if (answer == i18n.getI18NMessage('avanti.dialog.ok')) {

			// Setup the variables for start date time, end date time and total time spent.
			//			var incompleteOperations = new Array()
			
		    var completedTime = null;
		    //SL-21957 Using organizational timezone for organizations who have Atlantic Timezone set
            if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

                completedTime = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
            }
            else {

                completedTime = application.getTimeStamp() //new Date()
            }
			var startTime = foundset.jcl_start_datetime
			var totalMillisecondsSpent = (completedTime - startTime)

				// Loop through milestones to find if there are cost entries for them and create them if there isn't one.
				/** @type{JSFoundSet<db:/avanti/sch_milestone>} */
			var milestone_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sch_milestone')

			if (milestone_fs.find()) {
				milestone_fs.ordrevds_id = forms.sf_main_dtl.selected_ordrevds_id
				if (milestone_fs.search() > 0) {
					createMultipleCostEntries(milestone_fs, startTime, totalMillisecondsSpent)
				}
			}

			// Create a dataset to load into the multiple labour entry view.
			//showMultipleEntryDataset(true, milestone_fs)

			scopes.globals.commitMatTrans(forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_labour_material.foundset);

			// Hide the complete buttons
			forms.sf_main_dtl_cost.updateCompleteButtons(false)

			// Reload the operations in progress.
			forms.sf_main_dtl.loadEmployeeOperations()
			forms.sf_login.continueLoginProcess()
		}
	}
}

/**
 * @param {JSRecord<db:/avanti/prod_job_cost_labour>} rSelectedLabour
 * @param {Date} dStartTime
 * @param {Number} totalMillisecondsSpent
 *
 * @return {JSDataSet}
 *
 * @properties={typeid:24,uuid:"007020C2-1863-4463-B055-8E72E515383F"}
 * @AllowToRunInFind
 */
function createMultipleCostEntriesByLabourRecord2(rSelectedLabour, dStartTime, totalMillisecondsSpent) {
	/** @type{JSDataSet} */
	var dsOperationsList = null;
	var sSelectedOperationCategoryId = null;
	/** @type {JSRecord<db:/avanti/prod_job_cost_labour>} **/
	var rLabour
	var rJobCost = rSelectedLabour.prod_job_cost_labour_to_prod_job_cost.getRecord(1);

	//get the following labours: 1) the selected labour 2) unfinished labours from the same operation category and section 3) create labours if none were created for a given milestone group from the same operation category and section

	if (utils.hasRecords(rSelectedLabour, "prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone")) { //if there is no milestone associated to the cost, try the milestone group and then the milestone
		sSelectedOperationCategoryId = rSelectedLabour.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone.opcat_id;
	} 
	else if (utils.hasRecords(rSelectedLabour, "prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sch_milestone")) {
		sSelectedOperationCategoryId = rSelectedLabour.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sch_milestone.opcat_id;
	}

	if (utils.hasRecords(rSelectedLabour, "prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sch_milestone")) {

		var fsSectionMilestones = rSelectedLabour.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sa_order_revision_detail_section.sa_order_revision_detail_section_to_sch_milestone;

		//all milestones
		for (var i = 1; (i <= fsSectionMilestones.getSize() && sSelectedOperationCategoryId !== null); i++) {

			var rMilestone = fsSectionMilestones.getRecord(i);

			if (rMilestone.opcat_id !== sSelectedOperationCategoryId) {
				continue;
			}

			var fsMilestoneGroup = rMilestone.sch_milestone_to_sch_milestone_group;
			fsMilestoneGroup.sort('sequence_nr asc');

			//all milestone groups
			for (var j = 1; j <= fsMilestoneGroup.getSize(); j++) {

				var rMilestoneGroup = fsMilestoneGroup.getRecord(j);

				/** @type{JSFoundSet<db:/avanti/prod_job_cost>} */
				var fsCost = rMilestoneGroup.sch_milestone_group_to_prod_job_cost$labour;
				/** @type{JSFoundSet<db:/avanti/prod_job_cost_labour>} */
				var fsCostLabour;

				if ( (fsCost === null || fsCost.getSize() === 0) && rMilestoneGroup.msgrp_flg_completed != 1) { //no costs associated and milestone group not completed, create new cost center and labour

					fsCostLabour = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
					rLabour = fsCostLabour.getRecord(fsCostLabour.newRecord());
					scopes.globals.sf_main_dtl_labour_createCostRecForMsGrp(rMilestoneGroup, rJobCost.job_id, rJobCost.ordrevds_id);
					rLabour.jc_id = rMilestoneGroup.sch_milestone_group_to_prod_job_cost$labour.jc_id;
					scopes.globals.sf_main_dtl_labour_updateCompletedLabour(rLabour, dStartTime, totalMillisecondsSpent, 1);

					if (dsOperationsList === null) {
						dsOperationsList = databaseManager.convertToDataSet([rLabour.jcl_id]);
					} else {
						dsOperationsList.addRow(dsOperationsList.getMaxRowIndex() + 1, new Array(rLabour.jcl_id));
					}

					databaseManager.saveData(rLabour);

				} else {

					//all costs
					for (var k = 1; k <= fsCost.getSize(); k++) {

						//from this cost, search all labours that are unfinished, or in progress, or the selected labour
						var rCost = fsCost.getRecord(k);

						/** @type{JSFoundSet<db:/avanti/prod_job_cost_labour>} */
						fsCostLabour = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour')
						if (fsCostLabour.find()) {

							fsCostLabour.newRecord();
							fsCostLabour.prod_job_cost_labour_to_prod_job_cost.jc_id = rSelectedLabour.prod_job_cost_labour_to_prod_job_cost.jc_id; //current labour
							fsCostLabour.jcl_id = rSelectedLabour.jcl_id;
							fsCostLabour.jcl_is_posted = 0;
							fsCostLabour.jcl_cur_lab_rec_id =  '^'
							fsCostLabour.has_been_paused  =    '^' 
							fsCostLabour.created_by_id = globals.avShopFloor_employeeID;


							fsCostLabour.newRecord();
							fsCostLabour.prod_job_cost_labour_to_prod_job_cost.jc_id = rCost.jc_id; //in progress labours
							fsCostLabour.jcl_is_complete = 0;
							fsCostLabour.jcl_is_paused = 0;
							fsCostLabour.jcl_is_unfinished = 0;
							fsCostLabour.jcl_is_posted = 0;
							fsCostLabour.jcl_cur_lab_rec_id =  '^'
							fsCostLabour.has_been_paused  =    '^' 
							fsCostLabour.created_by_id = globals.avShopFloor_employeeID;

							if (fsCostLabour.search()>0) {

								//all labours
								for (var l = 1; l <= fsCostLabour.getSize(); l++) {

									rLabour = fsCostLabour.getRecord(l);
									//9332 We are trying to find the paused entries  that corespond to the
									//current prod_job_cost_labour that is  going to be  put on complete
									// if  there exist paused foundsets that coresspond to the current labour
									// we  iterate through them in  order  to set jcl_cur_lab_rec_id to null
									// and we set  has_beem_paused to true in order for them to not be completed
									// by the complete-milestone and complete-category buttons 
									var pausedFoundset = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour')
									if (pausedFoundset.find()) {
										pausedFoundset.job_id = rLabour.job_id;
										pausedFoundset.jcl_cur_lab_rec_id = '!^'
										pausedFoundset.jc_id = rLabour.jc_id
										if (pausedFoundset.search()) {
											for (var i1 = 1; i1 <= pausedFoundset.getSize(); i1++) {
												pausedFoundset.getRecord(i1).jcl_cur_lab_rec_id = null
												pausedFoundset.getRecord(i1).has_been_paused = true;
												databaseManager.saveData(pausedFoundset)
											}
										}

									}

									scopes.globals.sf_main_dtl_labour_updateCompletedLabour(rLabour, dStartTime, totalMillisecondsSpent, fsCostLabour.getSize());

									if (dsOperationsList === null) {
										dsOperationsList = databaseManager.convertToDataSet([rLabour.jcl_id]);
									} else {
										dsOperationsList.addRow(dsOperationsList.getMaxRowIndex() + 1, new Array(rLabour.jcl_id));
									}

									databaseManager.saveData(rLabour);
								}
							}
						}
					}

					//now that all unfinished and in progress are finished, check if for a given milestone group there is at least one completed labour, if not, create one new cost and one new labour associated to it
					if (!rMilestoneGroup.clc_has_a_completed_labour) {

						fsCostLabour = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
						rLabour = fsCostLabour.getRecord(fsCostLabour.newRecord());
						var rNewCost = scopes.globals.sf_main_dtl_labour_createCostRecForMsGrp(rMilestoneGroup, rJobCost.job_id, rJobCost.ordrevds_id);
						rLabour.jc_id = rNewCost.jc_id;
						
                        //create prod_job_cost_transactions record for WIP
                        var rNewProdJobCostTrans = rNewCost.prod_job_cost_to_prod_job_cost_transactions.getRecord(rNewCost.prod_job_cost_to_prod_job_cost_transactions.newRecord());
                        rNewProdJobCostTrans.jct_transaction_type = scopes.avUtils.JOB_COST_TRANSACTION_TYPE.WorkInProgress;
                        databaseManager.saveData(rNewProdJobCostTrans);
                        
                        scopes.globals.sf_main_dtl_labour_updateCompletedLabour(rLabour, dStartTime, totalMillisecondsSpent, 1);

						if (dsOperationsList === null) {
							dsOperationsList = databaseManager.convertToDataSet([rLabour.jcl_id]);
						} else {
							dsOperationsList.addRow(dsOperationsList.getMaxRowIndex() + 1, new Array(rLabour.jcl_id));
						}

						databaseManager.saveData(rLabour);
					}
				}

				scopes.globals.sf_main_dtl_labour_updateCompletedMilestoneGroup(rMilestoneGroup);
			}//milestone groups loop
		}
	}

	if (dsOperationsList !== null) {
		scopes.globals.sf_main_dtl_labour_distributeDurations(dsOperationsList, totalMillisecondsSpent);
	}

	return dsOperationsList;
}

/**
 * Create multiple cost entries based on milestones
 *
 * @param {JSFoundSet<db:/avanti/sch_milestone>} milestone_fs
 * @param {Date} startTime
 * @param {Number} totalMillisecondsSpent
 *
 * @properties={typeid:24,uuid:"94A8FC29-E51F-41F9-A24D-70EFFDC01058"}
 */
function createMultipleCostEntries(milestone_fs, startTime, totalMillisecondsSpent) {
	milestone_fs.sort('sequence_nr asc');
	for (var milestone_idx = 1; milestone_idx <= milestone_fs.getSize(); milestone_idx++) {

		/** @type{JSRecord<db:/avanti/prod_job_cost_labour>} */
		var cost_labour_rec;
		/** @type{JSFoundSet<db:/avanti/prod_job_cost_labour>} */
		var cost_labour_fs;
		/** @type{JSFoundSet<db:/avanti/sch_milestone_group>} */
		var op_fs = milestone_fs.getRecord(milestone_idx).sch_milestone_to_sch_milestone_group;
		op_fs.sort('sequence_nr asc');

		if (utils.hasRecords(milestone_fs.getRecord(milestone_idx).sch_milestone_to_prod_job_cost) 
		        && !utils.hasRecords(milestone_fs.getRecord(milestone_idx).sch_milestone_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour)) {
			cost_labour_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
			cost_labour_rec = cost_labour_fs.getRecord(cost_labour_fs.newRecord());
			cost_labour_rec.jc_id = milestone_fs.getRecord(milestone_idx).sch_milestone_to_prod_job_cost.jc_id;
			cost_labour_rec.jcl_start_datetime = new Date(startTime);
			cost_labour_rec.sys_employee_shift_id = forms.sf_main_dtl.current_shift_id;
			databaseManager.saveData(cost_labour_rec);
		}

		for (var op_idx = 1; op_idx <= op_fs.getSize(); op_idx++) {
		    
		    var msgrp_rec = op_fs.getRecord(op_idx);
		    
			// There exists a cost and labour entry so use this one.
			if (utils.hasRecords(msgrp_rec.sch_milestone_group_to_prod_job_cost) 
			        && utils.hasRecords(msgrp_rec.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour)) {
				cost_labour_rec = msgrp_rec.sch_milestone_group_to_prod_job_cost.prod_job_cost_to_prod_job_cost_labour.getSelectedRecord();
			} // There isn't a cost entry so create one to reference in the labour entry.
			else if (!utils.hasRecords(msgrp_rec.sch_milestone_group_to_prod_job_cost)) {
				/** @type{JSFoundSet<db:/avanti/prod_job_cost>} */
				var cost_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost');
				var cost_rec = cost_fs.getRecord(cost_fs.newRecord());
				cost_rec.msgrp_id = msgrp_rec.msgrp_id;
				cost_rec.job_id = forms.sf_main_dtl.selected_job_id;
				cost_rec.ordrevds_id = forms.sf_main_dtl.selected_ordrevds_id;
				cost_rec.empl_id = globals.avShopFloor_employeeID;
				
			    //SL-21957 Using organizational timezone for organzations who have Atlantic Timezone set
			    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

			        cost_rec.jc_date = scopes.avUtils.convertTimeStampToOrgTZ(application.getServerTimeStamp());
			    }
			    else {
			        
			        cost_rec.jc_date = application.getTimeStamp(); //new Date()
			    }
				cost_rec.ordrevdstask_id = msgrp_rec.sch_milestone_group_to_sch_milestone.ordrevdstask_id;
				cost_rec.opcat_id = msgrp_rec.sch_milestone_group_to_sch_milestone.opcat_id;
				cost_rec.dept_id = msgrp_rec.sch_milestone_group_to_sch_milestone.dept_id;

				// GD - May 27, 2019: Some prod job cost records are being created without an ordrevdstask_id, causing problems on budget vs actual report. 
			    // I think this is happening because the ms_id is not getting set on the record, so added some code here to do that
				cost_rec.ms_id = msgrp_rec.ms_id;
				cost_rec.ordrevdstask_id = (!cost_rec.ordrevdstask_id && msgrp_rec.ordrevdstask_id) ? msgrp_rec.ordrevdstask_id : cost_rec.ordrevdstask_id;
			    
				databaseManager.saveData(cost_rec);

				cost_labour_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
				cost_labour_rec = cost_labour_fs.getRecord(cost_labour_fs.newRecord());
				cost_labour_rec.jc_id = cost_rec.jc_id;

			} // There is a cost entry so use this for the labour entry.
			else {
				cost_labour_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour');
				cost_labour_rec = cost_labour_fs.getRecord(cost_labour_fs.newRecord());
				cost_labour_rec.jc_id = msgrp_rec.sch_milestone_group_to_prod_job_cost.jc_id;
			}

			// Set the start time, duration and end date time for the labour entry for this operation.
			cost_labour_rec.jcl_start_datetime = new Date(startTime);
			cost_labour_rec.sys_employee_shift_id = forms.sf_main_dtl.current_shift_id;
			var duration;
			if (milestone_fs.totalBudgetedTime != 0) {
				duration = ( (msgrp_rec.msgrp_time_budget / milestone_fs.totalBudgetedTime) * totalMillisecondsSpent);
			} else {
				duration = 0;
			}

			// sl-2763 - this was producing something that wasnt a date - so it defaulted to 'dec 31, 1969'
			//			startTime.getTime() += duration
			startTime = new Date(startTime.getTime() + duration);

			cost_labour_rec.jcl_end_datetime = new Date(startTime);
			cost_labour_rec.jcl_duration = duration;
			cost_labour_rec.jcl_is_complete = true;
			cost_labour_rec.jcl_is_paused = false;
			cost_labour_rec.jcl_is_unfinished = false;

			if (utils.hasRecords(cost_labour_rec.prod_job_cost_labour_to_prod_job_cost) 
			        && utils.hasRecords(cost_labour_rec.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group)) {
				/** @type{JSRecord<db:/avanti/sch_milestone_group>} */
				var operation_rec = cost_labour_rec.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.getSelectedRecord();
				operation_rec.msgrp_date_started = cost_labour_rec.jcl_start_datetime;
				operation_rec.msgrp_date_ended = cost_labour_rec.jcl_end_datetime;
				operation_rec.msgrp_flg_completed = 1;
				databaseManager.saveData(operation_rec);
			}

			databaseManager.saveData(cost_labour_rec);
		}
	}
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"63C3943B-A5EB-489E-BC4F-835E2290C46B"}
 */
function onShowForm(firstShow, event) {
	databaseManager.recalculate(foundset.getSelectedRecord());
	calculateTotalElapsedTime();
	convertDatesToLocale();
	databaseManager.setAutoSave(false);
	
	scopes.globals.avUtilities_tabRemoveAt(controller.getName(), "material_tab", 3);
	if (globals["avSecurity_checkForUserRight"]('Shop_Floor', 'shop floor_box_labels_access', globals.getUserFromEmp(globals.avShopFloor_employeeID))) {
		scopes.globals.avUtilities_tabAdd(controller.getName(), "material_tab", "sf_box_labels", "i18n:avanti.lbl.BoxLabels", null, null, null, 3, null);
	}

	if (doesOperationTaskUseLinearFeetScrap()) {
		elements.lblScrapQty.text = i18n.getI18NMessage("avanti.lbl.ScrapQuantityLF");
	} 
	else {
		elements.lblScrapQty.text = i18n.getI18NMessage("avanti.lbl.ScrapQuantity");
	}
	
	var result = _super.onShowForm(firstShow, event);
	controller.readOnly = false;

	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackStagingLocation)) {
		elements.clc_last_staging_location.visible = true;
	}
	else {
		elements.clc_last_staging_location.visible = false;
	}

	return result;
}

/**
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"4C895E2E-D523-4A0A-ABE7-3EBD95CCDFBB"}
 */
function doesOperationTaskUseLinearFeetScrap() {
	var bDoesOperationTaskUseLinearFeetScrap = false;
	
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.UseFlexoLinearFootageAndCount)) {
		var eTaskType = scopes.avTask.TASKTYPEID;
		var aValidTasks = [eTaskType.FlexoPress, eTaskType.WebPress, eTaskType.DigitalRollPress, eTaskType.DiePress, eTaskType.Rewinder, eTaskType.Finishing, 
			eTaskType.OfflineCoating, eTaskType.Shrinkwrapping];
		var rTask = getOperationTask();
		
		if (rTask && aValidTasks.includes(rTask.tasktype_id)) {
			bDoesOperationTaskUseLinearFeetScrap = true;
		}
	}
	
	return bDoesOperationTaskUseLinearFeetScrap;
}

/**
 * @return {JSRecord<db:/avanti/sa_task>}
 * 
 * @properties={typeid:24,uuid:"E7136113-CA7F-4AE1-8B97-4120589B6C3C"}
 */
function getOperationTask () {
    /***@type {JSRecord<db:/avanti/sa_task>} */
	var rTask = null; 
	
	if (forms.sf_main_dtl_cost.selected_msgrp_id) {
	    /** @type{JSFoundSet<db:/avanti/sch_milestone_group>} */
		var rMSG = scopes.avDB.getRec("sch_milestone_group", ["msgrp_id"], [forms.sf_main_dtl_cost.selected_msgrp_id]);
		
		if (rMSG) {
			if (utils.hasRecords(rMSG.sch_milestone_group_to_sa_task_cost_link) && utils.hasRecords(rMSG.sch_milestone_group_to_sa_task_cost_link.sa_task_cost_link_to_sa_task)) {
				rTask = rMSG.sch_milestone_group_to_sa_task_cost_link.sa_task_cost_link_to_sa_task.getRecord(1);
			}
		} 
		else {
		    /** @type{JSFoundSet<db:/avanti/sys_cost_centre>} */
			var rCC = scopes.avDB.getRec("sys_cost_centre", ["cc_id"], [forms.sf_main_dtl_cost.selected_msgrp_id]);

			if (rCC) {
				if (utils.hasRecords(rCC.sys_cost_centre_to_sa_task_cost_link)
						&& utils.hasRecords(rCC.sys_cost_centre_to_sa_task_cost_link.sa_task_cost_link_to_sa_task)) {

					rTask = rCC.sys_cost_centre_to_sa_task_cost_link.sa_task_cost_link_to_sa_task.getRecord(1);
				}
			}
		}
	}
	
	return rTask;
}

/**
 * @properties={typeid:24,uuid:"9D3E3B9B-FC13-40E7-96E4-517B202A3A33"}
 */
function convertDatesToLocale() {

    //SL-21957 Using organizational timezone for organzations who have Atlantic Timezone set
    if (utils.hasRecords(_to_sys_organization) && _to_sys_organization.org_timezone_id == "Canada/Atlantic") {

        /***@type {JSRecord<db:/avanti/sys_organization>} */
        var rOrg = scopes.avDB.getRec('sys_organization', ['org_id'], [globals.org_id]);

        if (rOrg.org_timezone_id == 'Canada/Atlantic') {

            start_date_to_locale = utils.dateFormat(convertDateToLocale(jcl_start_datetime), globals.avBase_dateFormat + ' ' + globals.avBase_timeFormat) + ' ' + 'ADT';
        }
        else {

            start_date_to_locale = utils.dateFormat(convertDateToLocale(jcl_start_datetime), globals.avBase_dateFormat + ' ' + globals.avBase_timeFormat) + ' ' + utils.dateFormat(application.getTimeStamp(), 'zzz');
        }
    }
    else {

        start_date_to_locale = utils.dateFormat(convertDateToLocale(jcl_start_datetime), globals.avBase_dateFormat + ' ' + globals.avBase_timeFormat) + ' ' + utils.dateFormat(application.getTimeStamp(), 'zzz');
    }

    end_date_to_locale = utils.dateFormat(convertDateToLocale(jcl_end_datetime), globals.avBase_dateFormat + ' ' + globals.avBase_timeFormat);
}

/**
 * Load the documents based on the section selected.
 *
 * @properties={typeid:24,uuid:"9C1E5AD2-ED6A-475E-A6A1-E3E077B69096"}
 * @AllowToRunInFind
 */
function populateDocumentsList() {
	var result = scopes.globals.sf_main_dtl_labour_searchDocumentsList(forms.sf_main_dtl.selected_ordrevds_id);
	application.setValueListItems('vl_jobDocuments', result.vlFilterDisplayValues, result.vlFilterRealValues);

	/** @type{JSFoundSet<db:/avanti/sa_order_revh_doc>} */
	var fsRevDocuments = result.fsRevDocuments;
	
	if(fsRevDocuments.getSize() === 0 || !utils.hasRecords(fsRevDocuments)) {
		// no docs - clear foundset
		forms.sf_main_dtl_documents.foundset.clear();
		hideDocuments();
	} else {
		showDocuments();
	}
}

/**
 * @properties={typeid:24,uuid:"4DE9A97A-F090-451E-B978-CEFF8598F445"}
 */
function hideDocuments() {
	elements.documents.visible = false
	elements.documents_lbl.text = i18n.getI18NMessage('avanti.lbl.documentMissingAssociation')

	scopes.globals["avUtilities_setComponentLocation"](elements.startdate_lbl, scopes.globals["avUtilities_getComponentX"](elements.startdate_lbl), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.startdate, scopes.globals["avUtilities_getComponentX"](elements.startdate), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.enddate_lbl, scopes.globals["avUtilities_getComponentX"](elements.enddate_lbl), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.enddate, scopes.globals["avUtilities_getComponentX"](elements.enddate), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.total_elapsedtime_lbl, scopes.globals["avUtilities_getComponentX"](elements.total_elapsedtime_lbl), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.totalelapsedtime, scopes.globals["avUtilities_getComponentX"](elements.totalelapsedtime), $hidden_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.info_icon, scopes.globals["avUtilities_getComponentX"](elements.info_icon), $hidden_row_1_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.quantity_produced_lbl, scopes.globals["avUtilities_getComponentX"](elements.quantity_produced_lbl), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.quantity_produced, scopes.globals["avUtilities_getComponentX"](elements.quantity_produced), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.lblScrapQty, scopes.globals["avUtilities_getComponentX"](elements.lblScrapQty), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.fldScrapQty, scopes.globals["avUtilities_getComponentX"](elements.fldScrapQty), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnProgressComments, scopes.globals["avUtilities_getComponentX"](elements.btnProgressComments), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnImpressions, scopes.globals["avUtilities_getComponentX"](elements.btnImpressions), $hidden_row_2_y_value);
	scopes.globals["avUtilities_setComponentLocation"](elements.btnAddJobProgressEntry, scopes.globals["avUtilities_getComponentX"](elements.btnAddJobProgressEntry), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnDistributeQuantity, scopes.globals["avUtilities_getComponentX"](elements.btnDistributeQuantity), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.lbl_clc_last_staging_location, scopes.globals["avUtilities_getComponentX"](elements.lbl_clc_last_staging_location), $hidden_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.clc_last_staging_location, scopes.globals["avUtilities_getComponentX"](elements.clc_last_staging_location), $hidden_row_2_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.rushcode_lbl, scopes.globals["avUtilities_getComponentX"](elements.rushcode_lbl), $hidden_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.rushcode, scopes.globals["avUtilities_getComponentX"](elements.rushcode), $hidden_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btn_rush_code_lookup, scopes.globals["avUtilities_getComponentX"](elements.btn_rush_code_lookup), $hidden_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.extracode_lbl, scopes.globals["avUtilities_getComponentX"](elements.extracode_lbl), $hidden_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.extracode, scopes.globals["avUtilities_getComponentX"](elements.extracode), $hidden_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btn_extra_code_lookup, scopes.globals["avUtilities_getComponentX"](elements.btn_extra_code_lookup), $hidden_row_3_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_lbl, scopes.globals["avUtilities_getComponentX"](elements.qa_check_lbl), $hidden_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_1, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_1), $hidden_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_2, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_2), $hidden_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_3, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_3), $hidden_row_4_y_value)
	
	scopes.globals["avUtilities_setComponentLocation"](elements.material_tab, scopes.globals["avUtilities_getComponentX"](elements.material_tab), $hidden_row_5_y_value)
}

/**
 * @properties={typeid:24,uuid:"C34508BF-4B48-4AF7-9B96-A88278467BFB"}
 */
function showDocuments() {
	elements.documents.visible = true
	elements.documents_lbl.text = i18n.getI18NMessage('avanti.lbl.documents')

	scopes.globals["avUtilities_setComponentLocation"](elements.startdate_lbl, scopes.globals["avUtilities_getComponentX"](elements.startdate_lbl), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.startdate, scopes.globals["avUtilities_getComponentX"](elements.startdate), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.enddate_lbl, scopes.globals["avUtilities_getComponentX"](elements.enddate_lbl), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.enddate, scopes.globals["avUtilities_getComponentX"](elements.enddate), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.total_elapsedtime_lbl, scopes.globals["avUtilities_getComponentX"](elements.total_elapsedtime_lbl), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.totalelapsedtime, scopes.globals["avUtilities_getComponentX"](elements.totalelapsedtime), $showing_row_1_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.info_icon, scopes.globals["avUtilities_getComponentX"](elements.info_icon), $showing_row_1_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.quantity_produced_lbl, scopes.globals["avUtilities_getComponentX"](elements.quantity_produced_lbl), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.quantity_produced, scopes.globals["avUtilities_getComponentX"](elements.quantity_produced), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.lblScrapQty, scopes.globals["avUtilities_getComponentX"](elements.lblScrapQty), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.fldScrapQty, scopes.globals["avUtilities_getComponentX"](elements.fldScrapQty), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnProgressComments, scopes.globals["avUtilities_getComponentX"](elements.btnProgressComments), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnImpressions, scopes.globals["avUtilities_getComponentX"](elements.btnImpressions), $showing_row_2_y_value);
	scopes.globals["avUtilities_setComponentLocation"](elements.btnAddJobProgressEntry, scopes.globals["avUtilities_getComponentX"](elements.btnAddJobProgressEntry), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btnDistributeQuantity, scopes.globals["avUtilities_getComponentX"](elements.btnDistributeQuantity), $showing_row_2_y_value);
	scopes.globals["avUtilities_setComponentLocation"](elements.lbl_clc_last_staging_location, scopes.globals["avUtilities_getComponentX"](elements.lbl_clc_last_staging_location), $showing_row_2_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.clc_last_staging_location, scopes.globals["avUtilities_getComponentX"](elements.clc_last_staging_location), $showing_row_2_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.rushcode_lbl, scopes.globals["avUtilities_getComponentX"](elements.rushcode_lbl), $showing_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.rushcode, scopes.globals["avUtilities_getComponentX"](elements.rushcode), $showing_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btn_rush_code_lookup, scopes.globals["avUtilities_getComponentX"](elements.btn_rush_code_lookup), $showing_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.extracode_lbl, scopes.globals["avUtilities_getComponentX"](elements.extracode_lbl), $showing_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.extracode, scopes.globals["avUtilities_getComponentX"](elements.extracode), $showing_row_3_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.btn_extra_code_lookup, scopes.globals["avUtilities_getComponentX"](elements.btn_extra_code_lookup), $showing_row_3_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_lbl, scopes.globals["avUtilities_getComponentX"](elements.qa_check_lbl), $showing_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_1, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_1), $showing_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_2, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_2), $showing_row_4_y_value)
	scopes.globals["avUtilities_setComponentLocation"](elements.qa_check_field_3, scopes.globals["avUtilities_getComponentX"](elements.qa_check_field_3), $showing_row_4_y_value)

	scopes.globals["avUtilities_setComponentLocation"](elements.material_tab, scopes.globals["avUtilities_getComponentX"](elements.material_tab), $showing_row_5_y_value)
}

/**
 * Pause the operation.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"8B1E6414-4BB6-4DB6-9086-8D11CB20A355"}
 */
function onActionPause(event) {
	// this check is in completeOperation(). this is the only reason that function will return false for a Paused status. copied here, so we know here whether completeOperation will return 
	// true and can go ahead and do the things below completeOperation().    
	if (foundset.jcl_start_datetime == null) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.shopFloorStartError'), i18n.getI18NMessage('avanti.lbl.shopFloorStartError'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
	// Complete the operation as a paused operation and then create a message for the employee that the message was paused.
	else {
		if (scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(foundset)) {
			completeOperationMultipleJobs('Paused')
		}
		else {
			var dataParams = {
				status: 'Paused',
				selected_job_id: forms.sf_main_dtl.selected_job_id,
				selected_ordrevds_id: forms.sf_main_dtl.selected_ordrevds_id,
				current_employee_uuid: forms.sf_main_dtl.current_employee_uuid
			}
			
			scopes.globals.completeOperationFunction(foundset, dataParams, null, null, null, null);
		}

		forms.sf_main_dtl._pausingOp = true;
		forms.sf_main_dtl.loadEmployeeOperations();
		forms.sf_login.continueLoginProcess();
		forms.sf_main_dtl._pausingOp = false;
	}
}

/**
 * Reset the selected extra code and rush code and hide the materials tab.
 *
 * @properties={typeid:24,uuid:"091016D1-8B49-418C-9A16-C8A74D69B49B"}
 */
function resetSelectedOptions() {
	selected_extra_code = ''
	selected_rush_code = ''

}

/**
 * Toggle the materials screen in the labour entry screen.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"6E97C33D-4DCE-491F-863A-E76D202B693E"}
 * @AllowToRunInFind
 */
function onActionAddNote(event) {
	forms.sf_main_dtl_labour_comment.foundset.loadRecords(foundset);
	globals.DIALOGS.showFormInModalDialog(forms.sf_main_dtl_labour_comment, -1, -1, 650, 500, i18n.getI18NMessage('avanti.lbl.comments'), false, false, i18n.getI18NMessage('avanti.lbl.comments'), true);
}

/**
 * Save data after selecting a document that was worked on based on operation.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"DD77ACC5-513D-4EB9-8501-31B8246B9C48"}
 */
function onDataChangeSave(oldValue, newValue, event) {
	if (foundset.getSize() > 0) {
		foundset.jcl_selected_documents = selected_documents
		databaseManager.saveData(foundset)
	}
	return true
}

/**
 * Get QA Check field labels from setup in Cost Centre.
 *
 * @properties={typeid:24,uuid:"220401C3-49DC-434A-8268-************"}
 * @AllowToRunInFind
 */
function updateQALabel() {
    /** @type{JSFoundSet<db:/avanti/sch_milestone_group>} */
    var operation_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sch_milestone_group');

    // Flag to determine if we should show the QA label or not.  Don't show when no QA checks associated to Cost Centre.
    var showQALabel = false;
    if (operation_fs.find()) {
        operation_fs.msgrp_id = forms.sf_main_dtl_cost.selected_msgrp_id;
        if (operation_fs.search() > 0) {
            if (utils.hasRecords(operation_fs.sch_milestone_group_to_sys_cost_centre)) {

                if (operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled) {
                    elements.qa_check_field_1.titleText = operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1;
                    elements.qa_check_field_1.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_1.visible = false;
                }

                if (operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled) {
                    elements.qa_check_field_2.titleText = operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2;
                    elements.qa_check_field_2.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_2.visible = false;
                }

                if (operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled) {
                    elements.qa_check_field_3.titleText = operation_fs.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3;
                    elements.qa_check_field_3.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_3.visible = false;
                }

            }
            else {
                elements.qa_check_field_1.titleText = '';
                elements.qa_check_field_2.titleText = '';
            }
        }
        else {
            var oSQL = {};
            oSQL.server = globals.avBase_dbase_avanti;
            oSQL.args = [];
            oSQL.table = 'sys_cost_centre';
            oSQL.sql = 'select cc_id  from sys_cost_centre where cc_id = ? ';
            oSQL.args.push(forms.sf_main_dtl_cost.selected_msgrp_id);
            /** @type{JSFoundSet<db:/avanti/sys_cost_centre>} */
            var costCenterFoundset = globals.avUtilities_sqlFoundset(oSQL);
            if (costCenterFoundset) {
                var rCostCenter = costCenterFoundset.getRecord(1);
                if (rCostCenter && rCostCenter.cc_qa_check_field_1_enabled) {
                    elements.qa_check_field_1.titleText = rCostCenter.cc_qa_check_field_1;
                    elements.qa_check_field_1.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_1.visible = false;
                }

                if (rCostCenter && rCostCenter.cc_qa_check_field_2_enabled) {
                    elements.qa_check_field_2.titleText = rCostCenter.cc_qa_check_field_2;
                    elements.qa_check_field_2.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_2.visible = false;
                }

                if (rCostCenter && rCostCenter.cc_qa_check_field_3_enabled) {
                    elements.qa_check_field_3.titleText = rCostCenter.cc_qa_check_field_3;
                    elements.qa_check_field_3.visible = true;
                    showQALabel = true;
                }
                else {
                    elements.qa_check_field_3.visible = false;
                }

            }
            else {
                elements.qa_check_field_1.titleText = '';
                elements.qa_check_field_2.titleText = '';
            }
        }
    } 

    if (showQALabel) {
        elements.qa_check_lbl.visible = true;
    }
    else {
        elements.qa_check_lbl.visible = false;
        elements.qa_check_field_1.visible = false;
        elements.qa_check_field_2.visible = false;
        elements.qa_check_field_3.visible = false;
    }
}

/**
 *
 * Function to check if an operation was paused and if it was, then ask the employee if they would like to resume it.
 *
 * @properties={typeid:24,uuid:"058675BD-19A4-4E9E-A111-BB5B1A61A2AC"}
 * @AllowToRunInFind
 */
function checkPaused() {
	// NO LONGER USED

	//	/** @type{JSFoundSet<db:/avanti/prod_job_cost_labour>} */
	//	var cost_labour_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'prod_job_cost_labour')
	//
	//	if (forms.sf_main_dtl_cost.selected_msgrp_id != null && forms.sf_main_dtl_cost.selected_msgrp_id != '' && forms.sf_main_dtl_cost.current_jc_id != null && forms.sf_main_dtl_cost.current_jc_id != '' && cost_labour_fs.find()) {
	//		cost_labour_fs.jc_id = forms.sf_main_dtl_cost.current_jc_id
	//		cost_labour_fs.jcl_is_paused = 1
	//		if (cost_labour_fs.search() > 0) {
	//
	//			var answer = globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage('servoy.general.confirm'),
	//				i18n.getI18NMessage('avanti.lbl.shopFloorResumeQuestion'),
	//				i18n.getI18NMessage('avanti.dialog.ok'),
	//				i18n.getI18NMessage('avanti.dialog.cancel'))
	//			if (answer == i18n.getI18NMessage('avanti.dialog.ok')) {
	//
	//				for (var idx = 1; idx <= cost_labour_fs.getSize(); idx++) {
	//					cost_labour_fs.getRecord(idx).jcl_is_paused = false
	//					cost_labour_fs.getRecord(idx).jcl_is_complete = true
	//				}
	//
	//				databaseManager.saveData(cost_labour_fs)
	//				var labour_rec = cost_labour_fs.getRecord(cost_labour_fs.newRecord())
	//				forms.sf_main_dtl_cost.saveLabourRecord(labour_rec, forms.sf_main_dtl_cost.jc_id)
	//			}
	//		}
	//	}
}

/**
 * Checks if the selected operation:
 * 1) Has been already started by another employee
 * or
 * 2) Has been completed before
 * and gives the user the option to start/resume it or not.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"2337D5A0-AA14-49E3-9C9A-51427F83B116"}
 */
function onActionStartOperationAndExit(event) {
	var isOperationPaused = forms.sf_main_dtl_labour_buttons.elements.btn_start_and_exit.text === globals.getLblMsg('ResumeOperation');
	
	/**@type
	 *	{{
	 *	continueBoolean: boolean,
	 *	sOtherEmpNameStartedOp: String,
	 *	sEmpNameCompletedOp: String,
	 *	bOtherOperation: boolean,
	 *	_nStayLoggedIn: String,
	 *	bPrecedingOperationsComplete: boolean,
	 *	sPrecedingOperationsCheck: String
	 *	}}
	 * */
	
	var dataObject = scopes.globals.onActionStartOperationAndExitLogic(event, forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl_cost.selected_msgrp_id, foundset, isOperationPaused);
	
	var continueBoolean = dataObject.continueBoolean;
	var sOtherEmpNameStartedOp = dataObject.sOtherEmpNameStartedOp;
	var sEmpNameCompletedOp = dataObject.sEmpNameCompletedOp;
	var bOtherOperation = dataObject.bOtherOperation;
	var _nStayLoggedIn = dataObject._nStayLoggedIn;
	var bPrecedingOperationsComplete = dataObject.bPrecedingOperationsComplete;
	var sPrecedingOperationsCheck = dataObject.sPrecedingOperationsCheck;
	var messageObject = dataObject.messageObject;

	if(messageObject.bool0) {
		if(globals.showYesNoQuestion(messageObject.message0, true) !== globals.yes) {
			return;
		}
	}
	

	if(!continueBoolean && sPrecedingOperationsCheck === "Prevent" && !bOtherOperation && !bPrecedingOperationsComplete) {
		return globals.showWarning(globals.getDlgMsg('opPredecessorsNotCompleted'), true);
	}
	
	forms.sf_main_dtl_labour_material.refreshUI();
	
	// sl-4332
	if (isOperationPaused && foundset && foundset.jcl_is_paused) {
		if (sOtherEmpNameStartedOp && globals.showYesNoQuestion(sOtherEmpNameStartedOp + ' ' + globals.getDlgMsg('opAlreadyStarted_resume'), true) !== globals.yes) {
			forms.sf_main_dtl_cost.selected_msgrp_id = null;
			forms.sf_main_dtl_cost.updateStartButtons(false);
		} else {
			scopes.globals.resumeOp(foundset);
			if (!_nStayLoggedIn) {
				forms.sf_main_dtl.onActionExit(event);
			} else {
				forms.sf_main_dtl.loadEmployeeOperations();
				forms.sf_main_dtl_cost.onDataChange('', '', event);
			}
		}
		return null;
	}
	
	
	if ((sOtherEmpNameStartedOp && globals.showYesNoQuestion(sOtherEmpNameStartedOp + ' ' + globals.getDlgMsg('opAlreadyStarted'), true) !== globals.yes) ||
	  (sEmpNameCompletedOp && globals.showYesNoQuestion(sEmpNameCompletedOp + ' ' + globals.getDlgMsg('opAlreadyCompleted'), true) !== globals.yes)) {
		forms.sf_main_dtl_cost.selected_msgrp_id = null;
		forms.sf_main_dtl_cost.updateStartButtons(false);
	} else {
		scopes.globals.startOperation(null, forms.sf_main_dtl.selected_ordrevds_id, forms.sf_main_dtl.selected_job_id, forms.sf_main_dtl_cost.selected_msgrp_id, forms.sf_main_dtl_labour_comment.comments, forms.sf_main_dtl_labour.selected_documents);

		if (!_nStayLoggedIn) {
			forms.sf_main_dtl.onActionExit(event);
		} else {
			forms.sf_main_dtl.loadEmployeeOperations();
			forms.sf_main_dtl_cost.onDataChange('', '', event);
		}
	}
	
}

/**
 * Show the details of the current labour entry (for multiple entries).
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"AD25650D-5682-44F3-9121-3AF02C5921D5"}
 */
function onActionShowDetails(event) {
	//	forms.sf_main_dtl_labour_multiple.foundset.clear();
	if (foundset.prod_job_cost_labour_to_prod_job_cost && foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group) {
		forms.sf_main_dtl_labour_multiple.foundset.loadRecords('SELECT a.jcl_id FROM prod_job_cost_labour as a inner join prod_job_cost as b on a.jc_id = b.jc_id and b.ordrevds_id = ? and b.opcat_id = ? inner join sch_milestone_group as c on b.msgrp_id = c.msgrp_id and c.ms_id = ? order by c.sequence_nr asc',
			[forms.sf_main_dtl.selected_ordrevds_id, globals.UUIDtoStringNew(foundset.prod_job_cost_labour_to_prod_job_cost.opcat_id), globals.UUIDtoStringNew(foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.ms_id)])
		forms.sf_main_dtl_labour_multiple.foundset.sort('jcl_start_datetime desc')
		globals.DIALOGS.showFormInModalDialog(forms.sf_main_dtl_labour_multiple, -1, -1, 1005, 500, i18n.getI18NMessage('avanti.lbl.shopFloorHistoricalCostEntries'), false, false, i18n.getI18NMessage('avanti.lbl.shopFloorHistoricalCostEntries'), true)
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"6B58DF03-B289-4F97-9B77-FA1133C4C238"}
 */
function onActionUnfinished(event) {
	// sl-22489 - completeOperation() posts mat entries too, so we have to validate then to make sure they are ok
	if (scopes.globals.sf_main_dtl_material_button_validateEntries(forms.sf_main_dtl_labour_material.foundset)) {
		// Complete the operation as a paused operation and then create a message for the employee that the message was paused.
		if(scopes.globals.sf_main_dtl_labour_labEntryHasAddlJobs(foundset)){
			completeOperationMultipleJobs('Unfinished')
		}
		else{
			var dataParams = {
				status: 'Unfinished',
				selected_job_id: forms.sf_main_dtl.selected_job_id,
				selected_ordrevds_id: forms.sf_main_dtl.selected_ordrevds_id,
				current_employee_uuid: forms.sf_main_dtl.current_employee_uuid
			}
			scopes.globals.completeOperationFunction(foundset, dataParams, forms.sf_main_dtl_labour_material.foundset, null, null, null);
		}
		
		forms.sf_main_dtl.loadEmployeeOperations();
		forms.sf_login.continueLoginProcess();
	}
}

/**
 * @properties={typeid:24,uuid:"2023B98E-1507-4C9A-9873-B0F921FB824E"}
 */
function calculateTotalElapsedTime() {
	if (foundset.jcl_is_complete != 1) {
		total_elapsed_in_minutes = foundset.total_elapsed + (foundset.total_duration / 60 / 1000)
	} else {
		total_elapsed_in_minutes = foundset.total_duration / 60 / 1000
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BA77E934-5B11-4AD0-A547-4D2901620F64"}
 */
function onAction_btnAddJobProgressEntry(event) {
	if (jcl_qty_produced < 1) {
		globals.showWarning('enterCurProdQty')
	} else {
		if (scopes.globals.createJobProgressEntry(foundset)) {
			forms.prod_milestone_progress.refreshScreen();
		}
	}
}

/**
 * Returns the press of the operation added by the user
 * @param {JSRecord<db:/avanti/prod_job_cost_labour>} rLabour - The labour record
 * @return
 * @properties={typeid:24,uuid:"B163D048-7629-49A4-8FCE-ABFEAAEA6C5C"}
 * @AllowToRunInFind
 */
function getPressForOtherOperation(rLabour) {

	/*** @type {JSFoundSet<db:/avanti/sf_other_oper>} */
	var fsOperation = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sf_other_oper');
	//Searches for the added operation record
	if (fsOperation.find() && utils.hasRecords(rLabour.prod_job_cost_labour_to_prod_job_cost) && !!rLabour.prod_job_cost_labour_to_prod_job_cost.cc_id) {
		fsOperation.ordrevds_id = rLabour.prod_job_cost_labour_to_prod_job_cost.ordrevds_id;
		fsOperation.cc_id = rLabour.prod_job_cost_labour_to_prod_job_cost.cc_id;
		if (fsOperation.search() > 0) {
			var rCostCentre = fsOperation.getRecord(1).sf_other_oper_to_sys_cost_centre;
			if (utils.hasRecords(rCostCentre.sys_cost_centre_to_sa_task_cost_link)) {
				var rPress = scopes.avTask.getOperationPress(rCostCentre.sys_cost_centre_to_sa_task_cost_link);
				return rPress;
			}
		}
	}
	return null;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"05555BB6-2C43-43B8-A136-1FB908D1C21D"}
 */
function onAction_btnProgressComments(event) {
	globals.showComments(globals.getLblMsg('MilestoneProgressComments'), globals.avBase_getSystemPreference_Number(117) == 1)
}

/**
 * TODO generated, please specify type and doc for the params
 * @param event
 *
 * @properties={typeid:24,uuid:"DCA3BA0A-A8CE-4FBF-926D-10466D57837F"}
 */
function onAction_btnImpressions(event) {
	globals.DIALOGS.showFormInModalDialog(forms.sf_main_dtl_labour_impressions_dlg, -1, -1, -1, -1, " ", false, false, "dlgImpressions", true);
}

/**
 * Handle focus lost event of the element.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B294EA46-2AFF-4856-A740-99E902413307"}
 */
function onFocusLost_qtyProd(event) {
	// comments mandatory for qty prod

	if (jcl_qty_produced && globals.avBase_getSystemPreference_Number(117) && !globals.getComments()) {
		globals.showComments(globals.getLblMsg('MilestoneProgressComments'), true)
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"0EA3FD5C-12C9-4DC3-A459-321E396700F3"}
 */
function onDataChange_qtyProduced(oldValue, newValue, event) {
	var sClicksPref = globals.avBase_getSystemPreference_String(142);
	if(newValue != oldValue && sClicksPref === 'Quantity Produced'){
		globals.avShopFloor_qtyProducedChanged = true;
	}
	
	if(newValue != oldValue) {
		forms.sf_distribute_quantity.calculateDistributeQuantitySection(newValue);
		foundset.jcl_previous_qty_produced = newValue;
		databaseManager.saveData(foundset);
	}
	return true
}

/**
 * 
 * @return {Boolean} 
 * 
 *
 * @properties={typeid:24,uuid:"758D9E0F-D661-4F3B-BD5F-19273B9439E0"}
 */
function validateQtyProduced() {
    
    var bCheck = true;
    
    if (utils.hasRecords(foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_cost_centre) 
            && foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sys_cost_centre.cc_qty_required 
            && !foundset.jcl_qty_produced) {

        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
            i18n.getI18NMessage('avanti.lbl.confirmationQtyproduced'),
            i18n.getI18NMessage('avanti.dialog.ok'));

        bCheck = false;

    }
    else if (utils.hasRecords(foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group) 
            && utils.hasRecords(foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sys_cost_centre) 
            && foundset.prod_job_cost_labour_to_prod_job_cost.prod_job_cost_to_sch_milestone_group.sch_milestone_group_to_sys_cost_centre.cc_qty_required 
            && !foundset.jcl_qty_produced) {

        globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
            i18n.getI18NMessage('avanti.lbl.confirmationQtyproduced'),
            i18n.getI18NMessage('avanti.dialog.ok'));

        bCheck = false;
    }

    
    return bCheck;
}

/**
 * Perform the element onclick action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"37CDD27A-2289-4F7B-8E2E-961CD3755CB5"}
 */
function onAction_btnDistributeQuantity(event) {
	if (scopes.globals.sf_distribute_quantity_areThereAddlJobs(forms.sf_distribute_quantity.foundset)) {
		globals.DIALOGS.showFormInModalDialog(forms.sf_distribute_quantity, -1, -1, 730, 500, i18n.getI18NMessage('avanti.lbl.jobs'), false, false, "dlgShopfloorDistributeQty", true);
	}
	else {
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.warning'),
            i18n.getI18NMessage('avanti.lbl.distributeQuantityWarning'),
            i18n.getI18NMessage('avanti.dialog.ok'));
	}
}
