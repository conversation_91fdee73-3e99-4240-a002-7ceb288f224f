customProperties:"methods:{\
onLoadMethodID:{\
arguments:null,\
parameters:null\
},\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/po_receipt",
extendsID:"3DF9114A-BDD8-4EF3-AEDD-59E7874347A7",
items:[
{
cssPosition:"30,-1,-1,135,180,22",
enabled:false,
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"135",
right:"-1",
top:"30",
width:"180"
},
dataProviderID:"inv_number_var",
editable:false,
enabled:false,
formIndex:8,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"ap_invoice_num",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"0F2F257F-A65B-4844-8711-D4BAC0DBACC5"
},
{
cssPosition:"30,-1,-1,5,125,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"30",
width:"125"
},
enabled:true,
formIndex:7,
labelFor:"ap_invoice_num",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceNumber",
visible:true
},
name:"ap_invoice_num_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"2F9EDF07-583D-4C8B-A5C9-FE97EA3CD685"
},
{
cssPosition:"-1,5,32,0,1095,145",
json:{
containedForm:"11E2D0CC-B39A-40C8-B1A2-3CC851FFF42D",
cssPosition:{
bottom:"32",
height:"145",
left:"0",
right:"5",
top:"-1",
width:"1095"
},
visible:true
},
name:"tabs_176",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"3595BACA-308A-4B18-8850-BF9429ADC9BF"
},
{
cssPosition:"5,-1,-1,135,180,22",
enabled:false,
formIndex:5,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"135",
right:"-1",
top:"5",
width:"180"
},
dataProviderID:"supplier_id_var",
editable:false,
enabled:false,
formIndex:5,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"31F530C8-144F-49FC-8A88-D9A39C311861",
visible:true
},
name:"supplier_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"*************-4BA4-B524-E3B1FA629191"
},
{
cssPosition:"5,-1,-1,330,130,22",
formIndex:32,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"5",
width:"130"
},
enabled:true,
formIndex:32,
labelFor:"po_freight_in_bill_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.freightMethod",
visible:true
},
name:"po_freight_in_bill_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4696BCA5-72E9-4BE9-B96D-735BB91C3B2A"
},
{
cssPosition:"70,0,182,0,1100,198",
json:{
cssPosition:{
bottom:"182",
height:"198",
left:"0",
right:"0",
top:"70",
width:"1100"
},
divLocation:-1,
divSize:5,
enabled:true,
onChangeMethodID:"E5C5ACD5-9235-4C66-AB9F-E54D2D58A83F",
pane1MinSize:30,
pane2MinSize:30,
responsiveHeight:300,
splitType:0,
visible:true
},
name:"split",
typeName:"servoyextra-splitpane",
typeid:47,
uuid:"5D581126-C5F3-41BB-9C64-FDB8AE51D6F1"
},
{
cssPosition:"30,-1,-1,465,188,22",
enabled:false,
formIndex:35,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"465",
right:"-1",
top:"30",
width:"188"
},
dataProviderID:"po_receipt_to_po_purchase.po_freight_in_cost_method",
editable:false,
enabled:false,
formIndex:35,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"C661C802-E5D6-41B2-8F5B-607E6EF5CCF3",
visible:true
},
name:"po_freight_in_cost_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"63A99539-9909-45D7-8B15-3AC36C1AE6AB"
},
{
cssPosition:"-1,95,7,-1,80,20",
json:{
cssPosition:{
bottom:"7",
height:"20",
left:"-1",
right:"95",
top:"-1",
width:"80"
},
enabled:true,
onActionMethodID:"047E5F52-37E9-44CD-ABDC-3277C8D78D0B",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.ok",
visible:true
},
name:"component_297BD2FD",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"84081F64-B393-441F-A634-88C4B86815D4"
},
{
cssPosition:"5,-1,-1,5,125,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"5",
width:"125"
},
enabled:true,
formIndex:4,
labelFor:"supplier_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.supplier",
visible:true
},
name:"supplier_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BD12F236-7706-49D6-9476-D871C06B991C"
},
{
cssPosition:"30,-1,-1,330,130,22",
formIndex:34,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"330",
right:"-1",
top:"30",
width:"130"
},
enabled:true,
formIndex:34,
labelFor:"po_freight_in_cost_method",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FreightCostMethod",
visible:true
},
name:"po_freight_in_cost_method_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C927EFE3-E3ED-4169-9588-B110F6F923C9"
},
{
cssPosition:"-1,5,7,-1,80,20",
json:{
cssPosition:{
bottom:"7",
height:"20",
left:"-1",
right:"5",
top:"-1",
width:"80"
},
enabled:true,
onActionMethodID:"1CA93846-4D57-4262-9499-3880C1776B41",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.dialog.cancel",
visible:true
},
name:"component_C783FCBF",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"D34B614C-0262-4F89-95A0-F2CAD6705BE0"
},
{
cssPosition:"5,-1,-1,465,188,22",
enabled:false,
formIndex:33,
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"465",
right:"-1",
top:"5",
width:"188"
},
dataProviderID:"po_receipt_to_po_purchase.po_freight_in_bill_method",
editable:false,
enabled:false,
formIndex:33,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"3D8F6C70-FB02-4311-8FE6-134242ED5880",
visible:true
},
name:"po_freight_in_bill_method",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D56D5DF4-FA01-4298-AB85-9EB747648D07"
},
{
height:450,
partType:5,
typeid:19,
uuid:"DE8D29D6-0079-4955-9736-5D99D7A2DF1D"
}
],
name:"po_receipt_invoice",
onLoadMethodID:"808BAD69-E728-4EDD-BE2B-72151857AAB6",
onShowMethodID:"EB338198-77DF-4EE7-AEEC-2F6B5D883639",
scrollbars:33,
size:"1100,300",
styleName:null,
typeid:3,
uuid:"A33C23B5-E921-4187-9BC4-128EA4228CAA",
view:0