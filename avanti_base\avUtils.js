
/**
 * An array that tracks processed form names to prevent recursive processing during data dictionary creation
 *
 * @private
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"F1D3B47E-C8A2-4D39-B6A8-D7CF36E8A291",variableType:-4}
 */
var aProcessedForms = [];

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"212F9DB9-D9CE-4FEB-AF98-428234B5862D",variableType:-4}
 */
var ENUM_INVOICE_REALLOCATION_METHOD = {
    NotApplicable: 'NA',
    CostOnly: 'CO',
    RevenueOnly: 'RO',
    BothCostAndRevenue: 'BO'
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"9AC8B40E-42A5-47DE-82A9-5EBFF50A35A4",variableType:-4}
 */
var ENUM_NETSUITE_INVOICE_INTEGRATION_METHOD = {
    WorkType: 1,
    DistributionAccount: 2,
    BillingCode: 3
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"724DE8E2-D64F-4DA5-B274-0BD97D1CACFA",variableType:-4}
 */
var INVOICE_STATUS_FILTER = {
    Updated: "U",
    Open: "O",
    PrintProForma: "PR",
    PrintFinal: "P",
	ReadyToPost: "RP",
	Locked: "L"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"4F7950F9-89A9-4CF0-B119-E3C5B4C109C4",variableType:-4}
 */
var ENUM_DEPOSIT_ACCOUNT_TYPE = {
    CustomerDepositAccount: 1,
    PostageEscrowAccount: 2
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"39DA50E4-6631-4A15-8702-287DE42AE80B",variableType:-4}
 */
var ENUM_REGISTER_DIALOG_MODE = {
    Create: "C",
    Edit: "E"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"03CEA6C7-DE79-44D5-9AB6-F37CA45850EB",variableType:-4}
 */
var ENUM_PAYMENT_DOCUMENT_TYPE = {
    DepositForOrder:        "D",
    Invoice:                "I",
    CreditNote:             "C",
    PostageDeposit:         "PDEP",
    CustomerDeposit:        "CDEP",
    CustomerCancelDeposit:  "CCDEP",
    PostageCancelDeposit:   "PCDEP"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"71B6F5BE-CD74-4E08-A216-7B40E6551387",variableType:-4}
 */
var ENUM_CASH_RECEIPT_CANCEL_STATUS = {
    ReadyToBePosted: "M",
    OnHold:          "H",
    Open:            "O",
    Posted:          "P",
    Reviewed:        "R"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"7243AD37-A64D-4B72-9C42-2BFB023BA138",variableType:-4}
 */
var ENUM_APPLY_CREDIT_NOTE_STATUS = {
    ReadyToBePosted: "M",
    OnHold:          "H",
    Posted:          "P"
};




/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"0065C221-298E-483D-9690-CBD810198FD9",variableType:-4}
 */
var GP_WEBSERVICE_CALL_RESPONSE = {
    getCustomerListResponse: "getCustomerListResponse",
    getCustomerAddressByKeyResponse: "getCustomerAddressByKeyResponse",
    getCustomerByKeyResponse: "getCustomerByKeyResponse",
    getSalespersonByKeyResponse: "getSalespersonByKeyResponse",
    getSalesTerritoryByKeyResponse: "getSalesTerritoryByKeyResponse",
    getShippingMethodByKeyResponse: "getShippingMethodByKeyResponse",
    getVendorListResponse: "getVendorListResponse",
    getVendorAddressByKeyResponse: "getVendorAddressByKeyResponse",
    getPaymentTermsByKeyResponse: "getPaymentTermsByKeyResponse",
    getCustomerReceivablesSummaryByKeyResponse: "getCustomerReceivablesSummaryByKeyResponse",
    getReceivablesInvoiceListResponse: "getReceivablesInvoiceListResponse",
    getGLAccountListResponse: "getGLAccountListResponse"
 };

/**
 * @enum 
 * @public 
 * @properties={typeid:35,uuid:"F2D88539-BFAF-4BFF-A723-210C56784A2C",variableType:-4}
 */
var GP_SYNC_TYPE = {
    customer: "CUSTOMER SYNC",
    supplier: "SUPPLIER SYNC",
    account: "ACCOUNT SYNC"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"0F3CD1B6-6A45-4680-94AF-86F0D790B07E",variableType:-4}
 */
var ENUM_FEDEX_FREIGHT_ACCOUNT_TYPE = {
	BillTo: "BILLTO",
    ShipTo: "SHIPTO"
};

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"44D34960-60E8-4692-91C8-0CEF51CFD670",variableType:-4}
 */
var ENUM_FEDEX_PAYMENT_TYPE = {
	Account: "ACCOUNT",
	Collect: "COLLECT",
	Recipient: "RECIPIENT",
    Sender: "SENDER",
	ThirdParty: "THIRD_PARTY"
};

/**
 * @enum 
 * @public 
 *
 *
 * @properties={typeid:35,uuid:"88DDD1E6-19B7-4F75-A2BA-F709103E388C",variableType:-4}
 */
var ENUM_PRODUCTION_RECEIPT_STATUS = {
    Open: "O",
    Updated: "U",
    Posted: "P"
};


/**
 * @properties={typeid:35,uuid:"45CBD6C2-F0A8-4A58-8CBD-559C47361F2F",variableType:-4}
 */
var ENUM_PURCHASE_RECEIPT_STATUS = {
	Open: "O",
	Updated: "U",
	ReadyToPost: "R",
	Posted: "P"
};


/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"71EBDC1E-CD97-4531-BA3F-C137E24F04A1",variableType:-4}
 */
var ENUM_FEDEX_FREIGHT_SHIPMENT_SERVICE_TYPE = {
	FedExFreightEconomy: "FEDEX_FREIGHT_ECONOMY",
    FedExFreightPriority: "FEDEX_FREIGHT_PRIORITY",
	FedExSmartPost: "SMART_POST",
	INTERNATIONAL_ECONOMY_FREIGHT: "INTERNATIONAL_ECONOMY_FREIGHT",
	INTERNATIONAL_PRIORITY_FREIGHT: "INTERNATIONAL_PRIORITY_FREIGHT",
	FEDEX_INTERNATIONAL_DEFERRED_FREIGHT: "FedEx International Deferred Freight"
};

/**
 * @properties={typeid:35,uuid:"B37A8F5B-08F2-4E70-A8DF-F97CA3BB24DB",variableType:-4}
 */
var GP_WEBSERVICE_CALL = {
    createPurchaseOrder: "createPurchaseOrder",
    createReceivablesCreditMemo: "createReceivablesCreditMemo",
    createReceivablesInvoice: "createReceivablesInvoice",
    createSalesReturn: "createSalesReturn",
    createSalesInvoice: "createSalesInvoice",
    createGLTransaction: "createGLTransaction",
    createPayablesInvoice: "createPayablesInvoice",
    createPurchaseReceipt: "createPurchaseReceipt",
    createCashReceipt: "createCashReceipt",
    getCustomerList: "getCustomerList",
    getCustomerByKey: "getCustomerByKey",
    getCustomerAddressByKey: "getCustomerAddressByKey",
    getShippingMethodByKey: "getShippingMethodByKey",
    getSalespersonByKey: "getSalespersonByKey",
    getSalesTerritoryByKey: "getSalesTerritoryByKey",
    getVendorList: "getVendorList",
    getVendorAddressByKey: "getVendorAddressByKey",
    getPaymentTermsByKey: "getPaymentTermsByKey",
    getCustomerReceivablesSummaryByKey: "getCustomerReceivablesSummaryByKey",
    getReceivablesInvoiceList: "getReceivablesInvoiceList",
    updateVendor: "updateVendor",
    createVendor: "createVendor",
    updateCustomer: "updateCustomer",
    createCustomer: "createCustomer",
    getGLAccountList: "getGLAccountList",
	getSupplierKeyFromGP: "getSupplierKeyFromGP",
	getItemVendorFromGP: "getItemVendorFromGP",
	applyCashReceipt: "applyCashReceipt",
	voidCashReceipt: "voidCashReceipt",
	getItemKeyFromGP: "getItemKeyFromGP",
	getWarehouseKeyFromGP: "getWarehouseKeyFromGP",
	getItemWarehouseFromGP: "getItemWarehouseFromGP",
	updatePurchaseOrder: "updatePurchaseOrder",
	voidPurchaseOrder: "voidPurchaseOrder"
};

/**
 * @properties={typeid:35,uuid:"DE66F3E7-6125-4579-AC07-9091D3F40B46",variableType:-4}
 */
var PRICERULE_PRICE_METHOD = {
    ActualPrice: "A",
	CostPlusMarkup: "C"
};

/**
 * @properties={typeid:35,uuid:"596F29AE-861F-4B8E-8395-CEB054DC3FDA",variableType:-4}
 */
var ENUM_DEFAULT_GUID = {
	Default: '********-0000-0000-0000-********0000'	
};


/**
 * @enum 
 * @public  
 *
 * @properties={typeid:35,uuid:"DD2C7D21-DA8D-4820-B914-DA6AA1F4A2E5",variableType:-4}
 */
var ENUM_DETAIL_LINE_ADD_ROW_MODE = {
    Add: "a",
    Update: "u",
    Copy: "c"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"08C95335-B62A-4A0E-8C84-333D007CFF47",variableType:-4}
 */
var ENUM_ESTIMATE_STATUS = {
    Incomplete: "Incomplete",
    Open: "Open",
    Won: "Won",
    Lost: "Lost",
    Hold: "Hold",
    Expired: "Expired",
    Cancelled: "Cancelled",
    RFQ: "RFQ",
    ReadyForSalesRep: "ReadyForSalesRep",
    ReadyForCustomer: "ReadyForCustomer",
    QuoteSent: "QuoteSent",
    Reserved: "Reserved",
    All: "All"
};


/**
 * @properties={typeid:35,uuid:"0CD60DDF-808C-4765-969C-B75BE5F84223",variableType:-4}
 */
var ENUM_WIDGET_TYPE = {
	Overlay: "Overlay",
	Table: "Table",
	Chart: "Chart"
};

/**
 *
 * @properties={typeid:35,uuid:"FB8BD3EA-1BCA-4ED3-AA5A-BD89E060B4BB",variableType:-4}
 */
var ENUM_TMP_SOURCE = {
    Copy: "COPY",
    Cancel: "CANCEL"
}

/**
 *
 * @properties={typeid:35,uuid:"F76C4042-3236-46CF-8799-E4A54F1964DF",variableType:-4}
 */
var ENUM_PROJECTPLAN_TYPE = {
    All: null,
    Standard: 0,
    Interbranch: 1
}

/**
 *
 * @properties={typeid:35,uuid:"011033BC-F8EA-4211-99A9-11EE53D53B7B",variableType:-4}
 */
var ENUM_PROJECTPLAN_WORKFLOW_ACTION = {
    Release: "RELEASE",
    Close: "CLOSE",
    CancelRelease: "CANCEL",
    Reopen: "REOPEN"
}

/**
 * @enum 
 * @public  
 *
 * @properties={typeid:35,uuid:"EAF03488-9E27-4307-B1F5-E25A1E83FECD",variableType:-4}
 */
var ENUM_PLANTREV_TYPE = {
    OriginatingPlant: "O",
    ProducingPlant: "P"
};

/**
 * @enum 
 * @public  
 *
 * @properties={typeid:35,uuid:"E9A55105-AC43-4764-9917-EAD79E0DB717",variableType:-4}
 */
var ENUM_PICK_STATUS = {
    Open: "O",
    InPicking: "IP",
    Picked: "P",
    PickedStaged: "PS",
	Shipped: "S",
	Used: "U"
};


/**
 * @enum 
 * @public  
 *
 * 
 *
 * @properties={typeid:35,uuid:"A7041FBD-3BA4-4E5E-B24D-24EA79D6AD4F",variableType:-4}
 */
var ENUM_BATCH_INVOICE_LEVEL = {
    LineItem: "LI",
    Invoice: "IN"
};

/**
 * An array of named stop watches - used by stopWatchStart() an stopWatchStop() 
 * 
 * @private 
 * @type {Array<StopWatch>}
 * 
 * @properties={typeid:35,uuid:"E9C5087E-B72A-49B6-AF75-460CB559B95D",variableType:-4}
 */
var aoStopWatches = [];

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"26036D66-0F5E-4F7D-869C-EA672289B4E3",variableType:-4}
 */
var ENUM_WEIGHT_BY = {
    BasisWeight: "b",
    GSM: "g"
};

/**
 * @properties={typeid:35,uuid:"3FF85447-1D03-4E73-97F2-C1B21C4C4AC6",variableType:-4}
 */
var nFTPDataTimeOut = application.getUserProperty('ftp.dataTimeout');

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"BBB0810B-20AD-473F-B054-F4B035F61507",variableType:-4}
 */
var ENUM_DIMENSION_UNITS = {
    Imperial: "I",
    Metric: "M"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"6069A944-D444-4B62-BEA2-24DD71A854DF",variableType:-4}
 */
var ENUM_WEIGHT_UOM = {
    GSM: "gsm",
    Imperial: "lbs",
    Metric: "kgs"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"C256611C-1253-4BC8-8EC0-************",variableType:-4}
 */
var ENUM_PACK_STATUS = {
    Open: "O",
    Confirmed: "C",
    ConfirmedPrinted: "P",
    Invoiced: "I",
	ChargebackPosted: "CP"
};

/**
 * @properties={typeid:35,uuid:"27DDB5FD-75C4-47D3-9792-E50A124A028D",variableType:-4}
 */
var ENUM_ENVIROMENTAL_REPORT_UOM_CONVERSION_OPTIONS = {
	UseExistingUOM: "E",
	ConvertIntoAnotherUOM: "C"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"577719AA-1043-4DF7-A22B-4DF23267C1C0",variableType:-4}
 */
var ENUM_INVOICE_DIST_TYPES = {
    CostOfGoodsSold: "C",
    Sales: "S",
    Miscellaneous: "M"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"F85FF85F-**************-C894C317D39D",variableType:-4}
 */
var ENUM_REFUND_TYPES = {
    CustomerDeposit: "CD",
    CustomerPostage: "PD",
    CreditNote: "CN"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"343E4A6C-00EA-4641-97A9-DF525423767A",variableType:-4}
 */
var ENUM_JOB_COST_TRANSACTION_STATUS = {
    Unposted: "U",
    Posted: "P"
};

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"84A98845-461A-44AF-9E8A-F9DFD4005CA3"}
 */
var sDevLogFunctionSource = null;

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"8FDFA92A-8A35-44C8-9C20-6B9735B97FF5",variableType:-4}
 */
var ENUM_LOGO_TYPE = {
    Organization: "O",
    Blank: "B",
    Default: "D"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"BC727002-A89E-4E71-97AE-36F3D95A293D",variableType:-4}
 */
var ENUM_SHIPMETHOD_TYPE = {
    Pickup: "P",
    Delivery: "D"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"8B548418-5531-4BCF-9669-910C6DF74F92",variableType:-4}
 */
var ENUM_ADDRESS_CODES_STANDARD = {
    Primary: "PRIMARY",
    OneTime: "ONE-TIME"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"45DE42F6-DA25-4D8B-A4D2-81E1FC504D4E",variableType:-4}
 */
var ENUM_AP_VARIANCE_TYPE = {
    PurchaseVariance: "P",
    FreightVariance: "F"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"39AD2132-8ECF-4FF1-9B9E-57CD3C7E87E4",variableType:-4}
 */
var ENUM_SALES_TAX_OPTION = {
    CustomerOrShipTo: "C",
    TaxType: "Y",
	Taxable: "T",
	NonTaxable: "N",
	Supplier: "S"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"D391325E-3A13-4297-A292-FCE523F7939B",variableType:-4}
 */
var ENUM_REGISTER_INTEGRATION_STATUS = {
    Pass: "P",
    Fail: "F",
	Waiting: "W"
};

/**
 * @enum 
 * @public 
 *
 * *!* WARNING: *!* Never assume that globals.nav.mode != Browse should mean that Edit behaviour should be applied, 
 *                  for "required_fields" this is not true!
 *              
 *              *!*  Use scopes.avUtils.isNavModeReadOnly() or scopes.isGivenNavModeReadOnly(sMode) to check if 
 *              we are in read-only mode instead *!*
 *              
 * Note: "required_fields" is nav mode user enters to make a field mandatory (or not) by clicking on it                
 *
 * @properties={typeid:35,uuid:"D1FD7A03-EB11-4FDE-B451-DBDF16357AFE",variableType:-4}
 */
var ENUM_NAV_MODE = {
    Browse: "browse",
    Add: "add",
    Edit: "edit",
    RequiredFields: "required_fields"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"CFEFB45A-7C32-4064-909D-0CEA68AD960B",variableType:-4}
 */
var ENUM_ACCOUNTING_ACCOUNT = {
    Debit: "D",
    Credit: "C"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"29C323D2-0AE2-4195-B573-B69D5A36D104",variableType:-4}
 */
var ENUM_ACBF_DATA_TYPE = {
    Currency: "currency"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"FAF19FCB-B133-4E46-9D0B-80BB15C94E92",variableType:-4}
 */
var ENUM_ACBF_FILE_FORMAT = {
    FixedPosition: "Fixed Position"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"EFD4C4F9-D1DA-4452-8E6A-716873D841D3",variableType:-4}
 */
var ENUM_CBF_DETAIL_LEVEL = {
    AccountDistribution: "AD"
};


/**
 * @properties={typeid:35,uuid:"********-9F6F-4B6B-856F-A871D657DB7A",variableType:-4}
 */
var ENUM_SHIFT_TIME_ACTION = {
	AutoRounding: 'AUTO_ROUNDING',
	ActualTime: 'ACTUAL_TIME'
}
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"A719871C-A02F-4B5D-964D-2779FBE0CAAB",variableType:-4}
 */
var ENUM_ACBF_FIELD_NAME = {
    DetailRecordNumber: "Detail Record Number",
    ChargebackPostTimehhmmss: "Chargeback Post Time (hhmmss)",
    ChargebackPostTimehhCmm: "Chargeback Post Time (hh:mm)"
};

/**
 * Returns whether or not current navigation mode should be interpreted as read-only or not.
 * 
 * <AUTHOR> Meixner
 * @since 2019-06-10
 * 
 * @global {String} globals.nav.mode - "browse"|"required_fields"|"add"|"edit"|"find"
 * 
 * @return
 * @properties={typeid:24,uuid:"E3C964B0-35BF-42E1-8769-269298A0CE58"}
 */
function isNavModeReadOnly() {
    return isGivenNavModeReadOnly(globals.nav.mode);   
}

/**
 * Returns whether or not navigation mode should be interpreted as read-only or not.
 * 
 * <AUTHOR> Meixner
 * @since 2019-06-11
 * 
 * @param {String} sNavMode - Nav Mode to check ("browse"|"required_fields"|"add"|"edit"|"find")
 *
 * @return
 * @properties={typeid:24,uuid:"D30311B4-70A7-4BB3-B323-604838F15920"}
 */
function isGivenNavModeReadOnly(sNavMode) {
    
    switch (sNavMode) {
        
        case ENUM_NAV_MODE.Browse:
            return true;
            break;
            
        case ENUM_NAV_MODE.RequiredFields:
            return true;
            break;
            
        case ENUM_NAV_MODE.Edit:
            return false;
            break;
            
        case ENUM_NAV_MODE.Add:
            return false;
            break;
            
        case 'find': // Note: This nav mode does not seem to be used anywhere in Slingshot but put this warning here just in case
            application.output('scopes.avUtils.isGivenNavModeReadOnly(): WARNING: encountenred globals.nav.mode == "find, assuming read-only.', LOGGINGLEVEL.WARNING);
            return true;
            break;
            
        default:
            application.output('scopes.avUtils.isGivenNavModeReadOnly(): ERROR: encountenred unhandled globals.nav.mode value: '+globals.nav.mode+', assuming read-only.', LOGGINGLEVEL.ERROR);
            return true;  
    }    
}

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"F62DDAAA-ABE1-42A5-BB21-FF79103C5C29",variableType:-4}
 */
var ENUM_SHIPMENT_TYPE = {
    Standard: "S",
    Proofs: "P",
    OutsideService: "OS",
    Other: "O"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"727E8737-7A51-4C12-A2F9-8EB83050B37C",variableType:-4}
 */
var ENUM_PURCHASE_RECEIPT_DISTTYPE = {
    BuyoutWorkInProgress: "BUYOUTWIP",
    BuyoutFinishedGoods: "BUYOUTFG",
    BuyoutCostOfGoodsSold: "BUYOUTCOGS",
    BuyOutSetup: "BUYOUTSETUP", //UDf4
    PurchaseClearing: "PURCHCLEAR",
    PurchaseExchange: "PURCHEXCH",
    FreightClearing: "FRTCLEAR",
    FreightExpense: "FRTEXP",
    FreightWIP: "FRTWIP",
    Inventory: "INV",
    Duty: "DUTY",
    Udf1: "UDF1",
    Udf2: "UDF2",
    Udf3: "UDF3"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"26F3CFB9-7447-461D-97B3-7EB4C9F8CF7A",variableType:-4}
 */
var ENUM_TAX_METHOD = {
    SalesTax: "S",
    ValueAddedTax: "V"
};




/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"B58C5600-0402-4157-B921-4C2EB1574209",variableType:-4}
 */
var ENUM_CASH_RECEIPT_STATUS = {
    ReadyToBePosted: "M",
    OnHold: "H",
    Updated: "U",
    Posted: "P",
    UnConfirmed: "U"
};
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"5DC5F8C8-869F-4982-8C79-655388571D7E",variableType:-4}
 */
var ENUM_DEPOSIT_APPLY_REF_TYPE = {
    SalesOrder: "SO",
    CashReceiptOrder: "CO",
    CashReceiptInvoice: "CI",
    DepositTransfer: "DT",
    Refund: "RE",
    CancelCashReceipt: "CR"
};
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"3A38CD52-C678-47FE-95E5-7C41491F9D95",variableType:-4}
 */
var ENUM_DEPOSIT_RECORD_TYPE = {
    ReceiveDeposit: "R",
    ApplyDeposit: "A"
};
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"9D93175E-D077-48D8-9ABA-2B3C527F3FE7",variableType:-4}
 */
var ENUM_PAYMENT_METHOD_TYPE = {
    PostageEscrowAccount: "PEA",
    Credit: "CREDIT",
    Cash: "CASH",
    Check: "CHECK",
    CustomerDepositAccount: "CDA"
};
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"1D0CD131-91A4-4BDE-8554-01FC637BC909",variableType:-4}
 */
var BACKGROUND_COLORS = {
    White: "#ffffff",
    Yellow: "#F5F6BE",
    LightYellow: "#ffffcc",
	LightGray: "#c0c0c0",
    DrkGray: "#cccccc",
    Blue: '#94bae1'
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"7A8DE1C0-EDBE-4D5C-BD68-0F1779F521B9",variableType:-4}
 */
var TAXCODE_QUICKBOOKS = {
    Taxable: "TAX",
    NonTaxable: "NON"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"6FC59651-6AE8-4627-B185-CB6D5CD0A4AE",variableType:-4}
 */
var INVOICE_RECORD_TYPE = {
    Invoice: "I",
    CreditNote: "C"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"DA0E955D-2A6E-4A2B-9695-9460DF263F18",variableType:-4}
 */
var JOB_COSTING_TYPE = {
    Actual: "Actual",
    Budget: "Budget"
};
/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"D01EA284-31C0-4ED8-9022-979292D6EB50",variableType:-4}
 */
var NOTE_RELATION_TYPE = {
    Estimate: "Estimate",
    Order: "Order",
    ProjectPlan: "ProjectPlan"
};
/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"884D40F5-7DF5-4D69-B826-3126674E444F",variableType:-4}
 */
var PROJECT_PLAN_STATUS = {
    Incomplete: "Incomplete",
    Open: "Open",
    InProgress: "InProgress",
    Closed: "Closed",
    All: null
};
/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"154C1C9C-F4A8-4E40-A8C5-B290C2C3825D",variableType:-4}
 */
var DOCUMENT_TYPE_CODE = {
    CashReceipt: "CR",
    APInvoice: "API",
    Refund: "REFUND",
    CashReceiptCancel: "CRC"
};

/**
 * @ enum 
 * @ public
 *
 * @properties={typeid:35,uuid:"6AF24BFB-2DB2-4547-8C4B-95F82EC5C2DB",variableType:-4}
 */
var PROJECT_PLAN_LINE_STATUS = {
    NotReleased: "O",
    Released: "R"
};
/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"01B22E12-D606-4A5B-BC2F-4496C5B402E9",variableType:-4}
 */
var ITEM_TYPE = {
    AssembledKit: "A",
    Account: "AC",
    BuildToOrderKit: "B",
    FinishedGood: "F",
    NonStock: "N",
    Product: "P",
    Stock: "S",
    Service : "SE",
    OutsourcedService : "ZZ"
};

/**
 * @properties={typeid:35,uuid:"6E80ABEC-8A22-4093-9FA4-8FDD43D4E0FA",variableType:-4}
 */
var ITEM_STATUS = {
	"Active": "A",
	"Inactive": "I",
	"Obsolete": "O",
	"ObsoleteAndReplace": "R",
	"ToBeDeleted": "D",
	"Expired": "E"
}

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"22A7FFBA-473A-4FA6-B2F9-9EA1523090D0",variableType:-4}
 */
var DEFAULT_INVOICE_QTY = {
    OrderedQty: "Ordered Qty",
    ShippedQty: "Shipped Qty"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"F1D4789E-38D1-4219-AB0E-3235DDC7578A",variableType:-4}
 */
var ACCOUNTING_INTEGRATION_TYPE = {
    MicrosoftDynamicsGP: "GP",
	QuickBooksIIF: "Q",
	QuickBooksWebConnector: "QWC",
	QuickBooksOnLine: "QBO",
	Sage50IMP: "S",
	Sage50CSV: "SC",
	Sage100Mas90: "M",
	Sage200: "S2",
	Sage300AccPacc: "P",
	Krammer: "K",
    None: "None",
	CSV:"CSV",
	WorkatoQuickBooksOnline: "WQBO",
	WorkatoMicrosoftDynamics365: "WD365",
	WorkatoNetSuiteCloudAccounting: "WNET",
	WorkatoSage200: "WS200"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"36E84F7F-2DE3-452B-B56B-7888489C383F",variableType:-4}
 */
var REGISTER_TYPE_CODE = {
    JobCostAdjustment: "JOBCOSTADJUST"
};

/**
 * @enum 
 * @public 
 *
 * @properties={typeid:35,uuid:"C8550122-1903-4C38-B27F-9DD8D82E414E",variableType:-4}
 */
var REGISTER_PROCESS_OPTION = {
    Review: "R",
    Update: "U"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"89C04159-9968-4964-AE04-C93758DD0551",variableType:-4}
 */
var JOB_INVOICE_STATUS = {
    NotInvoiced: "notInvoiced",
    PartiallyInvoiced: "partiallyInvoiced",
    FullyInvoiced: "fullyInvoiced"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"D37A1DBA-01EA-4413-9E69-69A6C224F0BF",variableType:-4}
 */
var JOB_COST_TRANSACTION_TYPE = {
    CostOfGoodsSold: "COGS",
    WorkInProgress: "WIP",
    FinishedGoods: "FG"
};

/**
 * @enum 
 * @public 
 * 
 * @properties={typeid:35,uuid:"4B4CF06B-2926-4F13-840A-E6351C4C2AF3",variableType:-4}
 */
var JOB_COST_TYPE = {
    Labour: "L",
    Material: "M",
    Purchase: "P",
    Other: "O"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"B05D1B52-C629-4520-AB61-5995F89E3D25",variableType:-4}
 */
var INVOICE_STATUS = {
    Updated: "U",
    Open: "O",
    PrintProForma: "PR",
    PrintFinal: "P",
	ReadyToPost: "RP"
};

/**
 * @enum 
 * 
 * @properties={typeid:35,uuid:"E3CC8FAA-F2E9-40EE-BDA0-F79BF04DFE71",variableType:-4}
 */
var INVOICE_FREIGHT_REVENUE_TYPES = {
    NEGOTIATED_CARRIER_COST : 1,
    PUBLISHED_CARRIER_COST : 2,
    SALESORDER_SHIPPING_COST : 3,
    MANUAL : 4,
    HAS_MULTIPLE_REVENUE_METHODS : 9
}

/**
 * @enum 
 * @public
 * 
 * @properties={typeid:35,uuid:"C25E8E5B-5B7B-4C19-906A-3EF2C4AFD33B",variableType:-4}
 */
var ENUM_CREDIT_NOTE_STATUS = {
    Applied:        "A",
    Open:           "O",
    Posted:         "U",
    Printed:        "P",
	ReadyToPost:	"RP",
    PrintProForma:  "PR"
}

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"2D2204E0-A77C-4BB4-9AA7-3347F79E2EBB",variableType:-4}
 */
var DOCUMENT_TYPE = {
    JobCostAdjustment: "JCA",
    Estimate: "EST",
    SalesOrder: "ORD",
    ComboJob: "CJO",
    ProjectPlan: "PP",
    Revision: "REV",
	WorkTemplate: "WT"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"6DFACAAC-65D0-4852-9709-84E670F765D8",variableType:-4}
 */
var ORDER_STATUS = {
    AccountingHold: "Accounting Hold",
    ApprovedChange: "Approved Change",
    Cancelled: "Cancelled",
    CancelledChange: "Cancelled Change",
    Completed: "Completed",
    DeletedChange: "Deleted Change",
	Importing: "Importing",
    Incomplete: "Incomplete",
    OnHold: "On Hold",
    Open: "Open",
    OpenChange: "Open Change",
    PartiallyShipped: "Partially Shipped",
    PendingChange: "Pending Change",
    Posted: "Posted",
    Released: "Released",
    ReleasedChange: "Released Change",
    ReleasedForPrep: "Released For Prep",
    Reserved: "Reserved",
    Shipped: "Shipped",
    Staged: "Staged"
};

/**
 * @properties={typeid:35,uuid:"1561EA28-7686-4F6E-B0C1-E6DDFFC0538C",variableType:-4}
 */
var ORDER_ACTION = {
    ReleaseOrder: "RO",
    ReleaseAndInvoiceOrder: "RAIO",
    ReleaseForPrep: "RFP",
    AccountingHold: "AH",
    CancelOrder: "CO",
    ReOpenOrder: "ROO",
    MoveToProjectPlan: "MPP",
    InvoiceOrder: "IO",
    ReleaseOrderAndInvoiceDeposits: "RAID",
    ReleaseAccountingHold: "RAH",
    ReleaseHold: "RH",
    CancelRelease: "CJ",
    CompleteOrder: "COM",
    InvoiceDeposit: "ID"
}

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"3D5EAE2B-AD7A-4752-8055-3E89E47D77DC",variableType:-4}
 */
var JOB_OTHER_COST_TYPES = {
    CostAdjustment: "CA"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"9A6B5CA6-823A-447F-904A-2FD509CA84A8",variableType:-4}
 */
var JOB_ADJUSTMENT_STATUS = {
    Open: "O",
    Updated: "U",
    Posted: "P"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"D14F72E7-2843-4540-82C7-BC550DE3506B",variableType:-4}
 */
var JOB_STATUS = {
    Open:       "Open",
    OnHold:     "OnHold",
    Comboed:    "Comboed",
    Shipped:    "Shipped",
    Completed:  "Completed",
    Cancelled:  "Cancelled",
	Scheduled:  "Scheduled",
	Staged:  	"Staged"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"93ADA2D6-FD0A-431F-80AA-98B1716EB3DD",variableType:-4}
 */
var IMPOSITION_TYPE_CODE = {
    Sheetwise:      "S",
    Perfecting:     "P",
    WorkAndTurn:      "W",
    WorkAndTumble:    "B"
};

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"86EDCB93-79F5-41DD-AB42-FB590C83CEF1"}
 */
var svyFrameworkRunning = null;

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"7AEE567B-24DF-467B-A4E3-43DBC50BD716",variableType:-4}
 */
var CONTROL_ACCOUNT_CODE = {
    AdvanceBilling: "ADVANCE BILLING",
    AdvanceBillingTax: "ADVANCE BILLING TAX"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"9F8A44F1-E06F-482B-B397-615FA623C54E",variableType:-4}
 */
var CREDIT_NOTE_TYPE = {
    Standard: "STD",
	Rebill: "RB",
    TaxOnly: "TAX",
    AdvanceBilling: "AB"
};

/**
 * @enum 
 * @public
 *
 * @properties={typeid:35,uuid:"*************-4B22-8F74-95702E4725E4",variableType:-4}
 */
var INVOICE_TYPE = {
    Standard: "R",
    AdvanceBilling: "AB",
    Contract: "C"
};

/**
 * @enum 
 * @public
 * @properties={typeid:35,uuid:"6C195BCE-3919-45B9-82E7-63866F982360",variableType:-4}
 */
var ITEM_QUANTITY_TYPE = {
    QtyOnHand: 1,
    QtyCommitted: 2,
    QtyUnavailable: 3,
    QtyUnusable: 4,
    QtyAvailable: 5,
    QtyReserved: 6,
    QtyBackordered: 7,
    QtyOnPo: 8,
    QtyOnPoReq: 9,
    QtyInTransit: 10,
    QtyInProduction: 11
};

/**
 * @enum 
 * @public
 * @properties={typeid:35,uuid:"71CF7BE9-78F1-43B0-A74F-B07AAAE6E130",variableType:-4}
 */
var FIFO_SOURCE_TYPE = {
    PickList: "PICK",
    PackingSlip: "PACK",
    ItemTransaction: "ITEM_TRANS"
};

/**
 * @properties={typeid:35,uuid:"6D6B8066-BBD2-4028-A622-13E8265FC86D",variableType:-4}
 */
var PRICERULE_BREAK_METHOD = {
    StepPricing: "SP",
    QuantityBreak: "QB",
    CostBreak: "CB",
    StepPricingCost: "CP" 
};

/**
 * @properties={typeid:35,uuid:"4397D0FB-CBEE-4FC5-914D-0C1AFDAE0FB8",variableType:-4}
 */
var DYNAMICS_GP_SYNC_TERMS = {
	Description: "DESC",
	TermsCode: "CODE"
}

/**
 * @properties={typeid:35,uuid:"F6D026CD-D677-41BA-894F-69C6864546A8",variableType:-4}
 */
var ENUM_AVANTI_CUSTOMER = {
	customerCode: 'AVANTI_WT_CUSTOMER',
	customerClass: 'AVANTI_CUST_CLASS'
}

/**
 * @properties={typeid:35,uuid:"CCB4A01A-EE04-43DB-999B-1E43719A269D",variableType:-4}
 */
var ENUM_PURCHASE_ORDER_RECEIPT_STATUS = {
	Open: 'O',
	Updated: 'U',
	Posted: 'P'
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"C0339536-73C8-4B92-B2AF-12D06A070E53",variableType:4}
 */
var um_client_user_id = 0;

/**
 * @properties={typeid:35,uuid:"ED29EE70-A35A-4EEC-ABDF-3DDABE4DEA91",variableType:-4}
 */
var um_client_popup = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"497A4C99-B980-449D-81D2-2153E1AC3864"}
 */
var um_clients_html = '';

/**
 * 
 * @type {Number}
 * @properties={typeid:35,uuid:"8B595214-DD1B-4077-B62D-5DD5D1523354",variableType:4}
 * 
 */
var intKey = null; 

/**
 * @type {String}
 * @public 
 *
 * @properties={typeid:35,uuid:"2DDA476B-DA45-43EB-920A-AC13C01994FF"}
 */
var UUID_2 = null;

/**
 * @type {String}
 * @public 
 *
 * @properties={typeid:35,uuid:"927ACD51-EFA7-49B5-9FFF-************"}
 */
var UUID_1 = null;

/**
 * @enum
 * @public
 *
 * @properties={typeid:35,uuid:"0DDEDE48-4CFC-4A6C-9F3D-F8FBB49DF8D8",variableType:-4}
 */
var LOGGER = {
	Accounting:			"Accounting",
	AccountingCost: 	"AccountingCost",
	AccountingApi:		"AccountingApi",
	Billing:			"Billing",
	CRM: 				"CRM",
	Customers:			"Customers",
	Estimating: 		"Estimating",
	Invoice:			"Invoice",
	Jobs:				"Jobs",
	Orders: 			"Orders",
	Pick:				"Pick",
	Reporting: 			"Reporting",
	Scheduling:			"Scheduling",
	Security:			"Security",
	Shipping:			"Shipping",
	Shipping_API:		"ShippingApi",
	ShopFloor:			"ShopFloor",
	Ship:				"Ship",
	System:				"System",
	Tasks: 				"Tasks",
	XmlLoader: 			"XmlLoader",
	avGrid:				"avGrid",
	avChart: 			"avChart"
}

/**
 * @enum 
 * @public
 * @properties={typeid:35,uuid:"AE4E5824-D920-4559-97A4-EA888120999C",variableType:-4}
 */
var SYS_PREF = {
    DefaultBottomLineMrkUp : 2,
    SectionBleedSize: 10,
    EstimateExpiry : 17,
	UseContactAddress: 37,
    AccountingIntegration: 68,
	DivPlantFilter: 79,
	ShippingLabelFormat: 81,
	DefaultNoOfTransitDays: 94,
	UseChangeOrders: 102,
	PressMakeReadySpoilsAlgorithm: 103,
    MatchingToleranceInDollars: 104,
    MatchingToleranceInPercent: 105,
	OrderCompleteWhen: 107,
	ValidateExternalOrders: 108,
    CreditLimitExceeded: 111,
    QtyProducedCommentsRequired: 117,
    OrderCustomerTypeCheck: 118,
    MeasurementSystem: 120,
    useChargBackCodeForShippingRef1: 127,
	ExcludeBleedsFromFeedLength: 132,
	GrandFormatDimensions: 139,
	ShopfloorClicksCalcMethod: 142,
	PointOfSaleEnabled: 144,
	ShowFunctionalGroupOnTaskVl: 148,
    LunchOperationCode:160,
    BreakOperationCode:161,
	PickRouteOrder: 177,
	ProductionJobCostingType: 188,
	SystemDataForCreditCheck: 189,
	PopulateDefaultLocation: 191,
	TransactionStatus: 192,
	AvalaraTaxTransaction: 193,
	ForceEntryOfPickQty: 194,
	EnvelopeNaming: 196,
	LockFinalInvoices: 197,
	ToteLength: 198,
	AutoPopTillNum: 199,
	ValidateOrderAddressUSPS: 200,
	ValidateShipmentAddressUSPS: 201,
	EnableRegisterAutoScheduler: 202,
	DefaultQuantityForInvoice: 203,
	HideInksOnJobTicket: 204,
	TaxDetailID: 205,	
	EnableReservationScheduling: 206,
	ReservationExpiryNotification: 207,
	HideColorOnJobTicket: 208,
	UseSectionPaper: 209,
	ContractOrderChargeServiceItem: 210,
	ContractLineItemChargeServiceItem: 211,
	ContractMinimumChargeServiceItem: 212,
	ContractReceiptChargeServiceItem: 213,
	ContractStorageChargeServiceItem: 214,
    DefaultInvoiceStatusForPrinting: 215,
    RecordLimitShipDialog: 216,
    EnvelopeMinSeamGlueGap: 217,
    EnvelopeMinPatchHeight: 218,
    ReportEstDetailShowValueAddedGrid: 219,
    DisableGLAccountNumberFieldAccountTypeRestriction: 220,
    EnableMailDetailsOnSalesOrders: 221,
    ReportJobTicket1_showOrderDesc: 222,
    ReportJobTicket1_showJobNr: 223,
    ReportJobTicket1_showJobTitle: 224,
    ReportJobTicket1_showWorkType: 225,
    ReportJobTicket1_showAddlInfoNotes: 226,
    ReportJobTicket1_showOrderDate: 227,
    ReportJobTicket1_showShipMethod: 228,
    ReportJobTicket1_showBarcodeOrderNr: 229,
    ReportJobTicket1_showClientInfo: 230,
    ReportJobTicket1_showSectionDescQty: 231,
    ReportJobTicket1_showPrepressDetails: 232,
    ReportJobTicket1_showPressSpoils: 234,
    ReportJobTicket1_showPressColor: 235,
    ReportJobTicket1_showSectionWeight: 237,
    ReportJobTicket1_showShippingDate: 238,
    AllowOverridingDivPlantGLSegmentCodes: 239,
    ShippingAutoApplyCustomerDetailsAsShipper: 240,
	ReportPurchaseOrder_summarizedShippingLoc: 242,
    OrderTypeRequired: 243,
    LineItemTypeRequired: 244,
    ReportInvoice_enableSalesTaxDetails: 246,
    ReportJobTicket_showShipToCustomerName: 249,
    ScheduleEmployees: 251,
    EnableAdvancedBilling: 252,
    UsePostageMethodWeight: 253,
    DefaultRegisterEmployeeName: 254,
    PostageReconDate: 255,
    AllocateFreightRevenueToJobs: 256,
    AdvanceBillingTax: 257,
    ContractBTOItemAsLineItem: 258,
    ShowEnvelopeWindowDieInfo: 259,
    PostageBaseUnits: 260,    
    AllowPOReceiptSelection: 261,
    AllocateFreightVarianceMethod: 262,
    AllocatePurchaseVarianceMethod: 263,
    AllocatePurchaseVariances: 264,
    JobCostAdjustmentOperation: 265,
    AllocateFreightVariances: 266,
    AllocateFreightCostToJobs: 267,
    UseSamePressForAllForms: 270,
    SendApplyRecordsToGP: 271,
    SendApplyCashReceiptsToInvoicesToGP: 272,
    AverageWarehousePickingLeadTime: 273,
    WorkFlowLeadTimeBuffer: 274,
    SendCostsToGPAsJournalEntry: 275,
    AutoConfimFromPostageRecon: 276,
    PreventShipConfirmUntilAllLinesConfirmed: 277,
    AutoCalcProductionDaysOffScheduleReservation: 278,
    LockInvoiceLineItem: 279,
    SetPackingSlipNumberToSalesOrderNumber: 280,
    UsePressDeptForInkGLAcct: 281,
    SendSalesPersonToGP: 282,
    DefaultChargebackCreditAccount: 283,
    DefaultChargebackCreditIndex: 284,
    DefaultChargebackDebitAccount: 285,
    DefaultChargebackDebitIndex: 286,
	WaybillNumberRequired: 287,
	AllowCashReceiptAdjustment: 288,
    ReceiptFromProdDefaultQty: 289,
    CopyCustomerClasstoGP: 290,
    CopySalesPersontoGP: 291,
    CopyCreditLimittoGP: 292,
    UseSectionQtyForMaterialTask: 293,
    DisableCostAccountingOnSalesInvoice: 294,
    AllowDefaultOffsetAccount: 295,
	UseSectionandOperationCode: 296,
	IgnoreMinChargeWithNegativeCalculatorAdjustments: 297,
	DistributeBLtoTasks: 298,
	ApplyDuplicatedItemsToPriceRules: 299,
	AllowRefundCreditNote: 305,
	AutoCreateCreditNoteFromCashReceiptOverpayment: 306,
	AutoCreateCreditNoteServiceItem: 307,
	RecalcMilestoneTimeNeeded: 308,
    ShopfloorTimeDistributionMethod: 309,
	ChargebackDebitControlAccount: 310,
	ChargebackCreditControlAccount: 311,
	LimitPostageTasktoOnePostageItem: 312,
	AllowEditOfInvoiceLinesWhenCustomerIsInvoiceComplete: 313,
	FinishedSizeAreaCalculationOn: 314,
	FinishedSizeAreaCalculationRoundingFactor: 315,
	AllowEditingOfDefinedSqFtValue: 316,
	EnableChildCustomersToUseParentCustomerPriceRules: 317,
	AddPostageCostToPostageTaskCost: 318,
	AddScheduledDateFieldToPlannedPurchasingSourceWindow: 319,
	DefaultChargebackDebitBusinessUnit: 320,
	DefaultChargebackCreditBusinessUnit: 321,
	DefaultChargebackDebitOrganization: 322,
	DefaultChargebackCreditOrganization: 323,
    AdvancedChangeOrders: 324,
	SwitchCustomerAddressToOneTimeDuringGLSalesInvoiceIntegration: 325,
	CashReceiptStatusForSalesOrderDeposits: 326,
	AllowBoxWeightToBeDefinedInBoxCountDialog: 327,
	UseProjectInventory: 328,
	SelectLabelCheckBoxInMobileApp: 329,
	UseInkTypeForInkAccounting: 330,
	QuantityAllocationPagingNumber: 331,
	VouchSalesTaxOnAPInvoice: 333,
	RetainCarrierPunchOutDetails: 334,
	TrackCurrencyOnEstSOInv: 335,
	UseStockingUOMForPick: 336,
	IncludePageBreakbySection: 337,
	ConsumePartialSheets: 338,
	ChargePartialSheetAsFullSheets: 339,
	IncludeFullyBOItemsOnPick: 340,
	ReturnFreightServiceItem: 341,
	ReturnUnappliedCreditNoteServiceItem: 342,
	DetectAndFixInventoryProblems: 999999, // sl-29182 - changed this from 343 to invalid number to turn it off. may turn it back on once we are certain it isnt causing problems
	Sage200RevenueAccount: 344,
	Sage200DateFormat: 345,
	TaskDetailReportIncludeSystemTasks: 346,
	FilteringShipToAddressFromStandardInvoice: 347,
	MinOrdChargeBehaviour: 348,
	HowtheMinimumOrderchargeistoappear: 349,
	AddDepartmentToThePO: 350,
	InventoryIssueAtPick: 351,
	ShowUpTo15SectionCommentsOnJobTicketStyle1and2: 352,
	AdditionalShippingMarkupForDomesticUPSShipments: 353,
	AdditionalShippingMarkupForIntlUPSShipments: 354,
	DueAtCustomerFormatInEstimates: 355,
	DueAtCustomerFormatInSalesOrders: 356,
	DevLoggingOn: 357,
	AddXmlToResponse: 358,
	UseWhseDefaultIssuelocToAllocRemainingQtyToPick: 360,
	NumLineItemsToUseHeadlessClientBatchShipping: 361,
	ShowtheGangFormSummaryonJobTickets: 362,
	OverrideTheRegisterNumberingValues: 364,
	ProdPickListBinAllocationBehaviorForRolls: 367,
	ChargebackNumRecordsToUseHeadlessClient: 368,
	RTSNumRecordsToUseHeadlessClient: 369,
	TurnOnVelocityDevLogging: 370,
	CSVFileToUseInIntegration: 371,
	MaximumNumberofJobsinRrangeforJobCostingreports: 372,
	PrintSalesOrderNumberon4x4ShippingLabels:373,
	PrintSalesOrderNumberBarcodeon4x4ShippingLabels: 374,
	PrintSummaryWorkOrderreportwhenPickListReportisSelected: 375,
	PrintSummaryWorkOrderreportwhenProductionPickListReportisSelected: 376,
	VerifyPOReceiptLandedCostMatchesPurchaseOrder: 377,
	JDFExportBudgetOrSchedule: 378,
	BatchInvoicingNumRecordsForAddlProcessor: 379,
	AddLaminatingMountingCaliperToCuttingLiftCalculation: 380,
	UseOperationCodeasbarcodeonOperationScanSheetReport: 381,
	PrintSummaryWorkOrderreportwithJobTicketReport: 383,
	UseFlexoLinearFootageAndCount: 385,
	WorkatoUseInvoiceRegister: 386,
	WorkatoUseCashReceiptsRegister: 387,
	WorkatoUsePayableInvoiceRegister: 388,
	WorkatoUsePurchaseReceiptsRegister: 389,
	RedistributeRevenueAndCostsUsingJournaEntry: 390,
	ShopfloorQuantityProducedMethod: 391,
   	RoundSartAndEndShiftTime: 392,
   	NumMinutesToRoundTimeBeforeShiftStarts: 393,
	NumMinutesToRoundTimeAfterShiftStarts: 394,
	NumMinutesToRoundTimeBeforeShiftEnds: 395,
	NumMinutesToRoundTimeAfterShiftEnds: 396,
	HandlingImportingOrders: 397,
	TrackStagingLocation: 398,
	UseBillingCodesInvoiceCreditNotes: 400,
	IncludeNonStandardPackingSlipInInvoice: 402,
	ExpectedShipDateFormat: 403,
	NetSuiteInvoiceIntegrationMethod: 404,
	Sage300InvoiceTextDescription: 405,
	RemoveCenturyFromDynamicsGPBatchNumber: 406,
	Sage300RevenueGLAccount: 407,
	DynamicsGpAppendSalesOrderToInvoiceDescription: 408,
	InvoiceCostBasedOnShipmentQuantityAndCost: 410,
	NumberOfDaysToScheduleAhead: 411,
	AllowCreditNotesForZeroBalanceInvoices: 414,
	UseEnhancedPostageTaxRules: 415,
	AddOperationsToProductionJobView: 417
};

/**
 * @properties={typeid:35,uuid:"AA1F6585-0CED-4929-B614-7B036CF04DF6",variableType:-4}
 */
var ePref142 = {
	SubstrateQuantityUsed: "Substrate Quantity Used", 
	QuantityProduced: "Quantity Produced"
};

/**
 * @enum 
 * @public
 * @properties={typeid:35,uuid:"ED7260DF-2C9D-40DB-A225-11DE25FF5F2F",variableType:-4}
 */
var e_spBroadcastRecordAction = {
	INSERT_ACTION : 'INSERT_ACTION',
	DELETE_ACTION : 'DELETE_ACTION',
	UPDATE_ACTION : 'UPDATE_ACTION'
};

/**
 * Enumeration for document object, avDocs_oDocs 
 * @properties={typeid:35,uuid:"53A026A6-E752-49B4-8693-C8AE092C5824",variableType:-4}
 */
var DOC_CALL_TYPE = {
    Estimate: 'e', 
    Order: 'o',
    ProjectPlan: 'pp',
    Invoice: 'iv',
    Customer: 'c',
    Contact: 'd',
    Job: 'j',
    PurchaseOrder: 'p',
    Item: 'i',
    WorkTemplate: 'w'
};

/**
 * @properties={typeid:35,uuid:"74249919-6B91-46CF-947C-C1DEDBCF4349",variableType:-4}
 */
var ENUM_REMOVE_UNSUPPORTED_CHARACTERS = {
    KeepNumbers: "number"
};

/**
 * @properties={typeid:35,uuid:"8C50073B-63EC-4E6F-9806-01BD012F330F",variableType:-4}
 */
var CURRENCY_EXCHANGE_METHOD = {
	Multiply: 'M',
	Divide: 'D'
};

/**
 * Used to cache the preferences in the system
 * 
 * @properties={typeid:35,uuid:"FFE14520-ED91-4053-A7A8-F3C67B6E774F",variableType:-4}
 */
var Pref = {};

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"5A5CAA20-BF3D-4D07-9D1D-25550B6DB648",variableType:4}
 */
var iINTEGRATION_VALIDATION_TIME = 180000;

/**
 * @param {Number} nValue - The number to be rounded
 * @param {Number} [nDecimalPlaces] - The number of decimal places for rounding; defaults to 2
 *
 * @return {Number} - The rounded value
 *
 * @properties={typeid:24,uuid:"7373BE06-1F71-498A-8D0F-83AADE60AD8F"}
 */
function roundNumber(nValue, nDecimalPlaces) {

	if (nValue == null || nValue == 0) {
		return nValue;
	}
	if (!nDecimalPlaces) nDecimalPlaces = 2; //default precision for now

	var _multiplier = Math.pow(10, nDecimalPlaces);
	var nReturn = +( Math.abs(nValue) * _multiplier ).toFixed(nDecimalPlaces);
	nReturn = Math.round(nReturn) / _multiplier * (nValue < 0 ? -1 : 1);

	return nReturn;
}

/**
 * Description
 *
 * <AUTHOR> Dotzlaw
 * @since Apr 18, 2014
 *
 * @param {Number} nValue - paramDescription
 *
 * @returns {Number} the squared number
 *
 * @properties={typeid:24,uuid:"C587D7E2-EE46-410A-BC8C-97FBF501A490"}
 */
function squareNumber (nValue){
	return nValue * nValue;
}

/**
 * Called for performing a conversion between a displayed value and a database value.
 *
 * @param displayedValue The displayed value.
 * @param {String} dbType The type of the database column. Can be one of "TEXT", "INTEGER", "NUMBER", "DATETIME" or "MEDIA".
 *
 * @returns {Object} the database value.
 *
 * @properties={typeid:24,uuid:"92657C94-BF18-417C-9F67-A24DC3C3BF94"}
 */
function globalConverterObj2DB(displayedValue, dbType) {
	
	return displayedValue;
}

/**
 * Called for performing a conversion between a database value and a displayed value.
 *
 * @param databaseValue The database value.
 * @param {String} dbType The type of the database column. Can be one of "TEXT", "INTEGER", "NUMBER", "DATETIME" or "MEDIA".
 *
 * @returns {Object} the displayed value.
 *
 * @properties={typeid:24,uuid:"E221974A-82E2-4EE8-80DC-42D48F15552D"}
 */
function globalConverterDB2Obj(databaseValue, dbType) {
	if(!databaseValue) {
		return utils.numberFormat(0, globals.avBase_currencySymbol + '#0.00')
	}
	return utils.numberFormat(databaseValue, globals.avBase_currencySymbol + '###0.00');

}

/**
 * @param {Date} dDate
 * @param {Boolean} [bConvertToServerTime]
  *
 * @return
 * @properties={typeid:24,uuid:"43F028CD-6FF1-469E-AA2B-FFA9BF932FF0"}
 */
function formatDateTime(dDate, bConvertToServerTime){
	if (dDate) {
		if (bConvertToServerTime) {
			dDate = scopes.avDate.convertClientTimeToServerTime(dDate);
		}

		// dDate.getMonth() is zero based, no such month as 0
		return (dDate.getMonth() + 1) + '/' + dDate.getDate() + '/' + dDate.getFullYear() + ' ' + formatTime(dDate.getHours(), dDate.getMinutes())
	}
	
	return null
}

/**
 * @param {Number} nHours
 * @param {Number} nMins
 *
 * @return
 * @properties={typeid:24,uuid:"9C30AE6A-4D44-45C7-9CFF-BD7CE94B7A8C"}
 */
function formatTime(nHours, nMins){
	var meridian = ''
	var sMins = ''
		
	if(nHours < 12){
		if(nHours==0){
			nHours=12
		}
		meridian = 'AM'
	}
	else{
		nHours -= 12
		if(nHours==0){
			nHours=12
		}
		meridian = 'PM'
	}
	
	if(nMins < 10){
		sMins = '0' + nMins.toString()
	}
	else{
		sMins = nMins.toString()
	}
	
	return nHours + ':' + sMins + ' ' + meridian
}

/**
 * Converts the given date to an ISO date string format:
 * 
 * YYYY-MM-DDThh:mm:ss:uuuZ
 * 
 * eg: "2016-03-07T18:01:42.032Z"
 * 
 * @param {Date} d - JavaScript Date() object
 * 
 * <AUTHOR> Meixner
 * @since Mar 07, 2016
 *
 * @properties={typeid:24,uuid:"9589B1FD-6109-4ABC-9DDD-53BCF7311172"}
 * 
 * @return {String} - the ISO date string
 */
function dateToISOString(d) {
	
	if (!d) {
		application.output('dateToString(): ERROR: date object is a required parameter.', LOGGINGLEVEL.ERROR);
		return "";
	}
	
	var outDate = 
		d.getFullYear()
		+'-'
		+('0'+(d.getMonth()+1)).slice(-2)
		+'-'
		+('0'+d.getDate()).slice(-2)
		+'T'
		+('0'+d.getHours()).slice(-2)
		+':'
		+('0'+d.getMinutes()).slice(-2)
		+':'
		+('0'+d.getSeconds()).slice(-2)
		+'.'
		+('00'+d.getMilliseconds()).slice(-3)
		+'Z';
	
	return outDate;
}

/**
 * Logging
 *
 * <AUTHOR> Dotzlaw
 * @since Aug 21, 2014
 *
 * @param {String} sMsg - The message
 * @param {String} sLogger - The Logger to use for the module (ENUM)
 * @param {Number} [nLevel] - The log level
 * 
 * @public
 *
 * @properties={typeid:24,uuid:"D67E36FB-AB2F-4E2F-8FDF-C75E2BF2CE7A"}
 */
function log (sMsg, sLogger, nLevel) {
	
	var logger = scopes.svyLogManager.getLogger('com.avanti.' + sLogger);
	
	switch ( nLevel ) {
		case LOGGINGLEVEL.DEBUG: // 1
			logger.debug(sMsg);
			break;
		case LOGGINGLEVEL.INFO: // 2
		default:
		logger.info(sMsg);
			break;
		case LOGGINGLEVEL.WARNING: // 3
			logger.warn(sMsg);
			break;
		case LOGGINGLEVEL.ERROR: // 4
			logger.error(sMsg);
			break;
		case LOGGINGLEVEL.FATAL: // 5
			logger.fatal(sMsg);
			break;
	}
	return;
}

/**
 * Replace Special HTML Characters in Strings
 *
 * <AUTHOR> Dol
 * @since Sep 12, 2014
 *
 * @param {String} sInputString - The input string to check
 *
 * @returns {String} String with replacement characters
 * 
 * @public 
 *
 *
 * @properties={typeid:24,uuid:"0A8D2190-124E-4F3C-948A-3817ABD87965"}
 */
function replaceHTMLCharsInString(sInputString)
{
	sInputString = sInputString.replace("<","&lt;")
	sInputString = sInputString.replace(">","&gt;")
	sInputString = sInputString.replace("<","&amp;")
	
	return sInputString;
}

/**
 * @param vs
 *
 * @return
 * @properties={typeid:24,uuid:"BBD5E5F9-6AC0-4B68-951C-4C91BF235F4A"}
 */
function isBlank(vs){
	if(vs == null || utils.stringTrim(vs) == '')
		return true;
	else
		return false;
}

/**
 * Return a date as a String
 * @param date_param
 *
 * @return
 * @properties={typeid:24,uuid:"4F1F4660-CADE-4B77-A868-3204FB36B67D"}
 */
function dateToString(date_param) {
    if (date_param == null) {
        return '';
    }
    else {
        return String(date_param.toString());
    }
}

/**
 * cuts off a number at the specified number of decimal places, doesnt round. eg. 12.6666666 will be truncated to 12.66, rather than rounded to 12.67
 * 
 * @param {Number} num
 * @param {Number} decimalPlaces
 *
 * @return
 * @properties={typeid:24,uuid:"BF6B8605-BF5D-417D-B691-F7E0C1A31223"}
 */
function truncateNum(num, decimalPlaces){
	var n10Powered = Math.pow(10, decimalPlaces);

	// sl-26612 - have to use calculateUsingSQL() as 0.0048 * 10000 was calculating as 47.99999999999999
	num = scopes.avMath.calculateUsingSQL(num, n10Powered, "*");
	num = Math.floor(num);
	num = scopes.avMath.calculateUsingSQL(num, n10Powered, "/");
	
	return num;
}

/**
 * Returns whether or not the user has permission to access the 
 * "Set Mandatory Field" button.
 * 
 * <AUTHOR> Meixner
 * @since 2019-06-05
 * 
 * @global {Number} - svy_nav_globals/globals.js:user_id
 * 
 * @return {Boolean} - true if user has permission, false otherwise
 * 
 * @properties={typeid:24,uuid:"CA59D10B-A38B-4C2D-A80B-C668EEDEF3B7"}
 */
function userHasPermissionToSetMandatoryFields() {
    
    var iHasAccess = scopes.avDB.getVal("sys_employee",['user_id'],[globals.user_id],"empl_allow_set_required_field");
    
    if (1 === iHasAccess) {
        return true;
    }
    else {
        return false;
    }
}

/**
 * Builds the preference object
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 12, 2014
 *
 * @public
 *
 *
 * @properties={typeid:24,uuid:"45C46129-BEB3-4B10-AA99-91A35862CEBD"}
 */
function getPrefs () {
    var tempPref = scopes.avCache.get(scopes.avCache.CACHE_KEYS.Sys_Prefs);
    if (tempPref) {
        Pref = tempPref;
        return;
    }
	
	var dsApp,
		rRecApp,
		i = 0;
	
	Pref = {};
	
	if( scopes.automationTasks && 
		scopes.automationTasks.bUsedAutomationTasks &&
		scopes.automationTasks.bUsedAutomationTasks == 1){
			
			var fsApp;
			fsApp = datasources.db.avanti.app_preference.getFoundSet();
			fsApp.loadAllRecords();
			fsApp.sort("pref_number asc");
			
			for ( i = 1; i <= fsApp.getSize(); i++ ) {
				
				rRecApp = fsApp.getRecord(i);
				
				if (utils.hasRecords(rRecApp.app_preference_to_sys_preference)) {
					Pref[rRecApp.pref_number.toString()] = rRecApp.app_preference_to_sys_preference.syspref_value;
					Pref[rRecApp.pref_number.toString() + "_modifiedDate"] = rRecApp.app_preference_to_sys_preference.syspref_modified_date;
				} else {
					Pref[rRecApp.pref_number.toString()] = rRecApp.pref_default_value;
					Pref[rRecApp.pref_number.toString() + "_modifiedDate"] = null;
				}
			}
			return;
	}
	
	var sql = "select pref_number, pref_default_value, syspref_value, syspref_id, syspref_modified_date  \
	   	   from app_preference as a \
	   	   left join sys_preference as b on b.pref_id = a.pref_id and b.org_id = '" + globals.org_id + "' \
	   	   order by pref_number asc";

	dsApp = scopes.avDB.SQLQueryDataset(sql);
		
	for ( i = 1; i <= dsApp.getMaxRowIndex(); i++ ) {
			
		rRecApp = dsApp.getRowAsArray(i);
			
		// sl-12730 - changed this to check if there is a syspref_id (ie there is a sys_preference record) instead of syspref_value.
		// they may have a sys_preference record, but have a null syspref_value, in which case we have to go with this val
		// and not the pref_default_value. this is in keeping with logic prior to changing from foundset to sql
		if (rRecApp[3]) {
			Pref[rRecApp[0].toString()] = rRecApp[2];
			Pref[rRecApp[0].toString() + "_modifiedDate"] = rRecApp[4];
		} else {
			Pref[rRecApp[0].toString()] = rRecApp[1];
			Pref[rRecApp[0].toString() + "_modifiedDate"] = null;
		}
	}
	
    scopes.avCache.set(scopes.avCache.CACHE_KEYS.Sys_Prefs, Pref, [scopes.avCache.TABLE.Sys_Preference]);
	
}

/**
 * Checks to see if the param is a number; handles null and empty strings
 *
 * <AUTHOR> Dotzlaw
 * @since Dec 6, 2014
 *
 * @param {String|Number} param 		
 * @returns {Boolean} True it is a number, False it is not a number
 * @public
 * @properties={typeid:24,uuid:"3B348217-A4D4-4833-B3BE-12ABCC3E77C4"}
 */
function isNumber (param) {
	
	return ! isNaN (param-0) && param !== null && param !== "" && param !== false;
}

/**
 * Add an attribute to the node of an XML document.
 * 
 * @param {Packages.org.w3c.dom.Document} xmldoc
 * @param {Packages.org.w3c.dom.Element} node
 * @param {String} attributeName
 * @param {String} attributeValue
 *
 * @properties={typeid:24,uuid:"DBE901CE-0E61-4A5C-BBF3-2D1E8F140FF6"}
 */
function addAttributeToXML(xmldoc, node, attributeName, attributeValue) {
	var attr = xmldoc.createAttribute(attributeName)
	attr.setValue(attributeValue)
	node.setAttributeNode(attr);
}


/**
 * Converts XML document to Indented string
 * @param {org.w3c.dom.Document} xmlDoc
 *
 * @return {String} Indented XML string
 * @properties={typeid:24,uuid:"9D60115C-4EE0-43B8-910A-D66BBCB5AB3D"}
 */
function writeXMLtoString(xmlDoc)
{
	var source = new Packages.javax.xml.transform.dom.DOMSource(xmlDoc);
	/** @type{Packages.java.io.StringWriter} */
	var stringWriter = new Packages.java.io.StringWriter();
	var result = new Packages.javax.xml.transform.stream.StreamResult(stringWriter);
	
	/**@type {Packages.javax.xml.transform.TransformerFactory} */
	var transformer_factory = new Packages.javax.xml.transform.TransformerFactory.newInstance()
	var transformer = transformer_factory.newTransformer();
	transformer.setOutputProperty(Packages.javax.xml.transform.OutputKeys.INDENT,'yes');
	transformer.transform(source,result);
	return stringWriter.getBuffer().toString();
}

/**
 * This function writes and XML Document to a File path
 * 
 * @param {org.w3c.dom.Document} xmlDoc the XML document to write to a file
 * @param {String} filePath the file path to create the new file to
 * @param {Boolean} uploadToFTP flag to determine if the files should be uploaded to FTP
 * @param {String} ftpFilePath path of file's FTP location.
 * @param {JSFoundSet<db:/avanti/sys_jdf_type>} jdf_type_foundset foundset need for exporting
 * @param {String} log_detail_line Job Details
 * @param {UUID} db_log_parent_id_param
 * @param {String} sFileType
 * @param {String} [dtd_param] path to DTD file if specifying System DOCTYPE 
 
 *
 * @properties={typeid:24,uuid:"65A6E078-1AE5-4C0D-B846-C36F4B320749"}
 */
function writeXMLtoFile(xmlDoc, filePath, uploadToFTP, ftpFilePath, jdf_type_foundset, log_detail_line, db_log_parent_id_param, sFileType, dtd_param)
{
	/**@type {Packages.javax.xml.transform.TransformerFactory} */
	var transformer_factory = new Packages.javax.xml.transform.TransformerFactory.newInstance()
	var transformer = transformer_factory.newTransformer()
	transformer.setOutputProperty(Packages.javax.xml.transform.OutputKeys.INDENT, 'yes')
	var source = new Packages.javax.xml.transform.dom.DOMSource(xmlDoc)
	var file = new Packages.java.io.File(filePath);
	
	// Check if the file exists.
	if(file != undefined){
		// Create the parent directories.
		file.getParentFile().mkdirs()
		
		if(dtd_param != null) {
			transformer.setOutputProperty(Packages.javax.xml.transform.OutputKeys.DOCTYPE_SYSTEM, dtd_param)
		}
		
		var result = new Packages.javax.xml.transform.stream.StreamResult(file)
		transformer.transform(source,result)	
		
		// Upload file to FTP.
		if(uploadToFTP){
			uploadFileToFTP(filePath,file, ftpFilePath, true, jdf_type_foundset)
		}
	} 
	else {
		plugins.dialogs.showErrorDialog(i18n.getI18NMessage('avanti.dialog.createFileError'),i18n.getI18NMessage('avanti.dialog.createFileErrorDetails'))
		if(db_log_parent_id_param != null) {
			globals.dbLog(log_detail_line + '\n' + i18n.getI18NMessage('avanti.dialog.createFileErrorDetails'), 'jdf_export', 'export', 'jdf', null, application.getUUID(globals.org_id), 'writing_xml', 'Detail', db_log_parent_id_param)
		}
	}
	
}

/**
 * This function uploads a file to an FTP site.
 * 
 * @param {String} filePath - The path to the file
 * @param {java.io.File} file the Java file representation
 * @param {String} directoryToMake The directory to create on the FTP site. If empty, don't create one.
 * @param {Boolean} isJDF if the file to upload is a JDF file or attachment
 * @param {JSFoundSet<db:/avanti/sys_jdf_type>} jdf_type_foundset foundset need for exporting
 *
 * @properties={typeid:24,uuid:"8AB46700-D1ED-4745-B53F-BD56359D393C"}
 * @AllowToRunInFind
 */
function uploadFileToFTP(filePath, file, directoryToMake, isJDF, jdf_type_foundset)
{
	/** @type {JSFoundSet<db:/avanti/jdf_queue>} */
	var jdf_queue_fs = databaseManager.getFoundSet(globals.avBase_dbase_avanti, "jdf_queue");
	
	jdf_queue_fs.newRecord();
	// Set the type of file to upload (FTPOUT is for attachment)
	if(isJDF){
		jdf_queue_fs.queue_type = 'JDFOUT'
	} else {
		jdf_queue_fs.queue_type = 'FTPOUT'
	}
	// Set dependency based on current selected UUID.
	if(!isJDF){
		jdf_queue_fs.dependent_of = _to_sa_order_revision_detail_section$avsales_selectedrevisionsectionid.sa_order_revision_detail_section_to_sa_order_revds_task.ordrevdstask_id
	}
	jdf_queue_fs.queue_start_time = application.getTimeStamp()
	jdf_queue_fs.queue_status = 'new'
	jdf_queue_fs.org_id = globals.org_id
	jdf_queue_fs.search_id = _to_sa_order_revision_detail_section$avsales_selectedrevisionsectionid.sa_order_revision_detail_section_to_sa_order_revds_task.ordrevdstask_id
	jdf_queue_fs.queue_step = '1'
	jdf_queue_fs.file_path = filePath.replace(/\//g, '\\')
	jdf_queue_fs.directory_to_make = directoryToMake
	jdf_queue_fs.sys_jdf_type_id = jdf_type_foundset.sys_jdf_type_id
	databaseManager.saveData(jdf_queue_fs)
		
}


/**
 * Skip the queue and import directly to FTP.
 * 
 * @param {String} sFilePath
 * @param {String} sFTPHost
 * @param {Number} sFTPPort
 * @param {String} sFTPUserName
 * @param {String} sFTPPassword
 * @param {String} sFTPHostPublicKey
 * @param {String} sFTPFolder
 * @param {Number} bUseSFTP
 * @param {UUID} oOrgID
 * @param {UUID} [oDBLogID]
 *
 * @return
 * @properties={typeid:24,uuid:"13C2E11C-775D-4086-A8F7-54381DD918BB"}
 * @AllowToRunInFind
 */
function uploadFileDirectlyToFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID) 
{
    if (oDBLogID) {
        globals.dbLog('Connecting to FTP host: ' + sFTPHost + ' with user ' + sFTPUserName + ' sending from filepath: ' + sFilePath, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
    }
	
	if(!sFTPPort) {
		sFTPPort = 21;
	}
	var bFTPStatus = false;
	
	if(bUseSFTP) {
		bFTPStatus = uploadFileUsingSFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID);
	} else {
		bFTPStatus = uploadFileUsingFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPFolder, bUseSFTP, oOrgID, oDBLogID);
	}	
	return bFTPStatus;
}

/**
 * Upload file to FTP site using Java FTP client.
 * @param {String}  sFilePath
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param [oDBLogID]
 *
 * @return
 * @properties={typeid:24,uuid:"C3F67560-B54A-4877-9C8E-68F87B1D958A"}
 */
function uploadFileUsingFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPFolder, bUseSFTP, oOrgID, oDBLogID) {
	var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
	// Create and connect to a client for the JFTP connection
	var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
	
	if(sFTPPort == null) {
		sFTPPort = 21;
	}
	
	
	try {
		ftpClient.setDefaultTimeout(scopes.avUtils.getFtpDefaultTimeout());
		ftpClient.connect(sFTPHost, sFTPPort);
		ftpClient.enterLocalPassiveMode();
	} catch (ex) {
		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP server for sending to Sales Order Export: ' + sFTPHost + ':' + sFTPPort, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export', ex);
		}
		if (ftpClient && ftpClient.isConnected()) {
            // Disconnect the client
            ftpClient.disconnect();
        }
		return false;
	} 
	
	var loginSuccess = ftpClient.login(sFTPUserName, ftppassword);
	if (loginSuccess) {
		var hasFTPFolder = false
		if(sFTPFolder != undefined && sFTPFolder != ''){
			hasFTPFolder = ftpClient.cwd(sFTPFolder);
			
			if(hasFTPFolder != 250){
                if (oDBLogID) {
                    globals.dbLog('Making dir ' + sFTPFolder, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
                }
			    
				ftpClient.mkd(sFTPFolder);
				ftpClient.cwd(sFTPFolder);
			} 
		}
		
		var fileType = sFilePath.substring(sFilePath.lastIndexOf('.') + 1).toLowerCase();
		if(fileType == 'txt' || fileType == 'text' || fileType == 'asp' || fileType == 'html' || fileType == 'shtml' || fileType == 'htm' || fileType == 'php'   || fileType == 'xml'  ) {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.ASCII_FILE_TYPE);
		} else {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.BINARY_FILE_TYPE);
		}
		
		var bis = new Packages.java.io.BufferedInputStream(new Packages.java.io.FileInputStream(sFilePath));
		
		// Upload the file
		ftpClient.storeFile(sFilePath.substring(sFilePath.lastIndexOf('\\') + 1),bis);
		bis.close();
		// Disconnect the client
		ftpClient.disconnect();

        if (oDBLogID) {
            globals.dbLogUpdate(oDBLogID, 'Completed sending of file to: ' + sFTPHost, 'Success', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
        }
        
		return true;
	} else {
        if (oDBLogID) {
            globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP OUT site: ' + sFTPHost, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
        }
	}
	return false;
}

/**
 * @param sFilePath
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPHostPublicKey
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param oDBLogID
 * @param sFilename
 *
 * @return
 * @properties={typeid:24,uuid:"4039C176-A571-4EB0-8A02-2D4D421AE801"}
 */
function uploadReportDirectlyToFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID, sFilename) {
	if (oDBLogID) {
		globals.dbLog('Connecting to FTP host: ' + sFTPHost + ' with user ' + sFTPUserName + ' sending to filepath: ' + sFTPFolder, 'xml_export', 'export', 'report', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
	}

	if (!sFTPPort) {
		sFTPPort = 21;
	}
	var bFTPStatus = false;

	if (bUseSFTP) {
		bFTPStatus = uploadReportUsingSFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID, sFilename);
	} 
	else {
		bFTPStatus = uploadReportUsingFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPFolder, bUseSFTP, oOrgID, oDBLogID, sFilename);
	}
	return bFTPStatus;
}

/**
 * @param sFilePath
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param oDBLogID
 * @param sFilename
 *
 * @return
 * @properties={typeid:24,uuid:"ADFC038D-32FE-4CF9-887B-A05CAC87EFD4"}
 */
function uploadReportUsingFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPFolder, bUseSFTP, oOrgID, oDBLogID, sFilename) {
	var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
	// Create and connect to a client for the JFTP connection
	var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();

	if (sFTPPort == null) {
		sFTPPort = 21;
	}
	
	ftpClient.connect(sFTPHost, sFTPPort);
	ftpClient.enterLocalPassiveMode();
	var loginSuccess = ftpClient.login(sFTPUserName, ftppassword);
	if (loginSuccess) {
		var hasFTPFolder = false
		if (sFTPFolder != undefined && sFTPFolder != '') {
			hasFTPFolder = ftpClient.cwd(sFTPFolder);

			if (hasFTPFolder != 250) {
				if (oDBLogID) {
					globals.dbLog('Making dir ' + sFTPFolder, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
				}

				ftpClient.mkd(sFTPFolder);
				ftpClient.cwd(sFTPFolder);
			}
		}

		var fileType = sFilePath.substring(sFilePath.lastIndexOf('.') + 1).toLowerCase();
		if (fileType == 'txt' || fileType == 'text' || fileType == 'asp' || fileType == 'html' || fileType == 'shtml' || fileType == 'htm' || fileType == 'php' || fileType == 'xml') {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.ASCII_FILE_TYPE);
		} 
		else {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.BINARY_FILE_TYPE);
		}

		var bis = new Packages.java.io.BufferedInputStream(new Packages.java.io.FileInputStream(sFilePath));

		// Upload the file
		ftpClient.storeFile(sFilePath.substring(sFilePath.lastIndexOf('\\') + 1), bis);
		bis.close();
		// Disconnect the client
		ftpClient.disconnect();

		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Completed sending file to: ' + sFTPHost, 'Success', '', 'xml', 'export', 'report', 'ftp', 'Upload report on FTP');
			globals.dbLog('File uploaded on: \\' + sFTPFolder + '\\' + sFilename, 'xml_export', 'export', 'report', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
		}

		return true;
	} 
	else {
		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP OUT site: ' + sFTPHost, 'Error', '', 'xml', 'export', 'report', 'ftp', 'Upload report on FTP');
		}
	}
	return false;
}

/**
 * @param sFilePath
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPHostPublicKey
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param oDBLogID
 * @param sFilename
 *
 * @return
 * @properties={typeid:24,uuid:"1CEEF49C-1411-4FB7-9F23-5581D81559EF"}
 */
function uploadReportUsingSFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID, sFilename) {
	var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
	var oSFTPSession = null;

	/** @type{Packages.com.jcraft.jsch.ChannelSftp} */
	var oSFTPChannel = null;
	try {
		var jsch = new Packages.com.jcraft.jsch.JSch();
		oSFTPSession = jsch.getSession(sFTPUserName, sFTPHost, sFTPPort);

		if (sFTPHostPublicKey) {
			var hostKeyInputStream = new java.io.ByteArrayInputStream(new Packages.java.lang.String(sFTPHostPublicKey).getBytes());
			jsch.setKnownHosts(hostKeyInputStream);
		} 
		else {
			oSFTPSession.setConfig("StrictHostKeyChecking", "no");
		}

		oSFTPSession.setPassword(ftppassword);
		oSFTPSession.connect();

		oSFTPChannel = new Packages.com.jcraft.jsch.ChannelSftp();

		oSFTPChannel = oSFTPSession.openChannel("sftp");
		oSFTPChannel.connect()
	} catch (ex) {
		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP OUT site: ' + sFTPHost, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
		}

		if (oSFTPChannel) {
			oSFTPChannel.exit();
			oSFTPChannel.disconnect();
		}
		if (oSFTPSession) {
			oSFTPSession.disconnect();
		}
		return false;
	}
	try {
		if (sFTPFolder != undefined && sFTPFolder != '') {
			try {
				oSFTPChannel.cd(sFTPFolder);
			} catch (ex) {
				if (oDBLogID) {
					globals.dbLog('Making dir ' + sFTPFolder, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
				}
				oSFTPChannel.mkdir(sFTPFolder);
				oSFTPChannel.cd(sFTPFolder);
			}
		}
		oSFTPChannel.put(sFilePath, sFilePath.substring(sFilePath.lastIndexOf('\\') + 1));

		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Completed sending file to: ' + sFTPHost, 'Success', '', 'xml', 'export', 'report', 'ftp', 'Upload report on SFTP');
			globals.dbLog('File uploaded on: \\' + sFTPFolder + '\\' + sFilename, 'xml_export', 'export', 'report', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
		}
		return true;
	} catch (ex) {
		if (oDBLogID) {
			globals.dbLogUpdate(oDBLogID, 'Failed to upload to file to the SFTP host: ' + sFTPHost + ' due to ' + ex.message, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Upload report on SFTP');
		}
	} finally {
		if (oSFTPChannel) {
			oSFTPChannel.exit();
			oSFTPChannel.disconnect();
		}
		if (oSFTPSession) {
			oSFTPSession.disconnect();
		}
	}
	return false;
}


/**
 * Upload a JS file to FTP site using Java FTP client.
 * @param {Array<byte>}  aBytesToUpload
 * @param {String} sPath
 * @param {String} sFileName
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param oDBLogID
 *
 * @return
 * @properties={typeid:24,uuid:"32DD19BA-AD0C-4584-A43D-43D7F9318F92"}
 */
function uploadJSFileUsingFTP(aBytesToUpload, sPath, sFileName, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPFolder, bUseSFTP, oOrgID, oDBLogID) {
	var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
	// Create and connect to a client for the JFTP connection
	var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
	
	if(sFTPPort == null) {
		sFTPPort = 21;
	}
	ftpClient.connect(sFTPHost, sFTPPort);
	ftpClient.enterLocalPassiveMode();
	var loginSuccess = ftpClient.login(sFTPUserName, ftppassword);
	if (loginSuccess) {
		var hasFTPFolder = false;
		if(sFTPFolder != undefined && sFTPFolder != '') {
			hasFTPFolder = ftpClient.cwd(sFTPFolder);
			
			if(!hasFTPFolder) {
				globals.dbLog('Making dir ' + sFTPFolder, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
				ftpClient.mkd(sFTPFolder);
				ftpClient.cwd(sFTPFolder);
			} 
		}
		
		var aFolders = sPath.split('/');
		for (var nFolderIndex = 0; nFolderIndex < aFolders.length; nFolderIndex++) {
			hasFTPFolder = false;
			if(aFolders[nFolderIndex]) {
				hasFTPFolder = ftpClient.cwd(aFolders[nFolderIndex]);
				
				if(hasFTPFolder != 250) {
					globals.dbLog('Making dir ' + aFolders[nFolderIndex], 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
					ftpClient.mkd(aFolders[nFolderIndex]);
					ftpClient.cwd(aFolders[nFolderIndex]);
				} 
			}
		}
		
		var fileType = sFileName.substring(sFileName.lastIndexOf('.') + 1).toLowerCase();
		if(fileType == 'txt' || fileType == 'text' || fileType == 'asp' || fileType == 'html' || fileType == 'shtml' || fileType == 'htm' || fileType == 'php'   || fileType == 'xml'  ) {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.ASCII_FILE_TYPE);
		} else {
			ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.BINARY_FILE_TYPE);
		}
		
		var bis = new Packages.java.io.ByteArrayInputStream(aBytesToUpload);
		
		// Upload the file
		ftpClient.storeFile(sFileName.substring(sFileName.lastIndexOf('\\') + 1),bis);
		bis.close();
		// Disconnect the client
		ftpClient.disconnect();
		globals.dbLogUpdate(oDBLogID, 'Completed sending of file to: ' + sFTPHost, 'Success', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
		return true;
	} else {
		globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP OUT site: ' + sFTPHost, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
	}
	return false;
}

/**
 * Upload file to FTP site using SFTP client.
 * @param sFilePath
 * @param sFTPHost
 * @param sFTPPort
 * @param sFTPUserName
 * @param sFTPPassword
 * @param sFTPHostPublicKey
 * @param sFTPFolder
 * @param bUseSFTP
 * @param oOrgID
 * @param [oDBLogID]
 *
 * @return
 * @properties={typeid:24,uuid:"CCA6F18E-15CC-40A4-B973-2F8C762BDC82"}
 */
function uploadFileUsingSFTP(sFilePath, sFTPHost, sFTPPort, sFTPUserName, sFTPPassword, sFTPHostPublicKey, sFTPFolder, bUseSFTP, oOrgID, oDBLogID) {
	var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
	var oSFTPSession = null;
	
	/** @type{Packages.com.jcraft.jsch.ChannelSftp} */	
    var oSFTPChannel = null;
    try {
        var jsch = new Packages.com.jcraft.jsch.JSch();
        oSFTPSession = jsch.getSession(sFTPUserName, sFTPHost, sFTPPort);
        
        if(sFTPHostPublicKey) {
        	var hostKeyInputStream = new java.io.ByteArrayInputStream(new Packages.java.lang.String(sFTPHostPublicKey).getBytes());
            jsch.setKnownHosts(hostKeyInputStream);
        } else {
        	oSFTPSession.setConfig("StrictHostKeyChecking", "no");
        }
        
        oSFTPSession.setPassword(ftppassword);
        oSFTPSession.setTimeout(scopes.avUtils.getSFtpDefaultTimeout());
        oSFTPSession.connect();
        
        oSFTPChannel = new Packages.com.jcraft.jsch.ChannelSftp();
        
        oSFTPChannel = oSFTPSession.openChannel("sftp");
        oSFTPChannel.connect()
	} catch (ex) {
        if (oDBLogID) {
            globals.dbLogUpdate(oDBLogID, 'Cannot connect to FTP OUT site: ' + sFTPHost, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
        }
	    
		if(oSFTPChannel) {
    		oSFTPChannel.exit();
        	oSFTPChannel.disconnect();
    	}
    	if(oSFTPSession) {
            oSFTPSession.disconnect();
    	}
		return false;
	}
	try {
		if(sFTPFolder != undefined && sFTPFolder != ''){
			try {
				oSFTPChannel.cd(sFTPFolder);
			} catch (ex) {
		        if (oDBLogID) {
	                globals.dbLog('Making dir ' + sFTPFolder, 'xml_export', 'export', 'xml', 'ftp', oOrgID, 'ftp', 'Detail', oDBLogID);
		        }
				oSFTPChannel.mkdir(sFTPFolder);
				oSFTPChannel.cd(sFTPFolder);
			}
		}
		oSFTPChannel.put(sFilePath,sFilePath.substring(sFilePath.lastIndexOf('\\') + 1));
		
        if (oDBLogID) {
            globals.dbLogUpdate(oDBLogID, 'Completed sending of file to: ' + sFTPHost, 'Success', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
        }
		return true;
	} catch (ex) {
        if (oDBLogID) {
            globals.dbLogUpdate(oDBLogID, 'Failed to upload to file to the SFTP host: ' + sFTPHost + ' due to ' + ex.message, 'Error', '', 'xml', 'export', 'xml', 'ftp', 'Send to Sales Order Export');
        }
	} finally{
    	if(oSFTPChannel) {
    		oSFTPChannel.exit();
        	oSFTPChannel.disconnect();
    	}
    	if(oSFTPSession) {
            oSFTPSession.disconnect();
    	}
    }
	return false;
}

/**
 * @public 
 * 
 * Send HTTP Post request with file to the URL setup in the JDF Type screen.
 * 
 * @param {String} file_type 
 * @param {String} url The URL to send the it.
 * @param {Number} needsAuthentication If authentication is needed.
 * @param {String} username The username if there is authentication set.
 * @param {String} password The password if there is authentication set.
 * @param {String} fileName The name of the file that will be attached to the HTTP Post
 * @param {String} fullFilePath The full path including file name and extension that will be attached to the HTTP Post
 * @param {String} log_detail_line
 * @param {UUID} db_log_parent_id_param log_id of parent log
 * @param {Boolean} bAutomaticExport - if true then this is an automatic JDF Export
 * @param {Array<String>} [aAdditionalHeaders]
 * @param {String} [sBody] - for passing in body as string, rather than using a file
 * 
 * @return {{bSuccess: Boolean, sResponseText: String}} oStatus
 *  
 * @properties={typeid:24,uuid:"78951F44-3E07-4AF1-8DA0-EC0E97FB19A4"}
 */
function sendHTTPPost(file_type, url, needsAuthentication, username, password, fileName, fullFilePath, log_detail_line, db_log_parent_id_param, bAutomaticExport, aAdditionalHeaders, sBody){
    /**@type {{bSuccess: Boolean, sResponseText: String}} */
    var oStatus = {bSuccess: false, sResponseText: null};
    
    if (url == null || url == '') {
        if (!bAutomaticExport) {
            globals.DIALOGS.showErrorDialog('URL Empty', 'URL Empty when sending information for: ' + fileName, i18n.getI18NMessage('avanti.dialog.ok'));
        }
        else {
            scopes.avJDF.OAutoJDFExportHelper['logStatus']('URL Empty', 'Error', null, db_log_parent_id_param, 'URL Empty when sending information for: ' + fileName, null, null, null);
        }
        if (file_type == 'xml') {
            globals.dbLog('URL Empty when sending information for: ' + fileName, 'xml_export', 'export', 'xml', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param);
        }
        else {
            globals.dbLog('URL Empty when sending information for: ' + fileName, 'jdf_export', 'export', 'jmf', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param);
        }
    }
    else {
        var httpClient = plugins.http.createNewHttpClient();
        var request = httpClient.createPostRequest(utils.stringTrim(url));
        var response;

        if (file_type == 'soap') {
            request.addHeader('Content-type', 'application/soap+xml');
        }
        else if (file_type == 'xml') {
            request.addHeader('Content-type', 'text/xml');
        }
        else {
            request.addHeader('Content-type', 'application/vnd.cip4-jmf+xml');
        }

        if (aAdditionalHeaders) {
            for (var i = 0; i < aAdditionalHeaders.length; i++) {
                var sHeader = aAdditionalHeaders[i];
                var sHeaderName = utils.stringTrim(sHeader.substring(0, sHeader.indexOf(':')));
                var sHeaderValue = utils.stringTrim(sHeader.substring(sHeader.indexOf(':') + 1));
                request.addHeader(sHeaderName, sHeaderValue);
            }
        }

        var sText;
        
        if (sBody) {
            request.setBodyContent(sBody);
            sText = sBody;
        }
        else if (fileName && fullFilePath) {
            request.addFile('', fileName, fullFilePath);
            sText = plugins.file.readTXTFile(fullFilePath);
        }
        // Send the request and create a JMF file for the response received.
        if (needsAuthentication) {
            var password_string = plugins.it2be_cryptor.BLOWFISHdecrypt(password);
            password_string = password;
            response = request.executeRequest(username, password_string);
        }
        else {
            response = request.executeRequest();
        }
        var db_log_id;

        if (response == undefined || response == '') {
            if (!bAutomaticExport) {
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.noResponse'), i18n.getI18NMessage('avanti.dialog.noResponseDetails') + ' ' + url + 
                    ' when sending ' + fileName, i18n.getI18NMessage('avanti.dialog.ok'));
            }
            else {
                scopes.avJDF.OAutoJDFExportHelper['logStatus'](i18n.getI18NMessage('avanti.dialog.noResponse'), '', '', db_log_parent_id_param, 
                        i18n.getI18NMessage('avanti.dialog.noResponseDetails') + ' ' + url + ' when sending ' + fileName, '', null, 'Detail');
            }
            if (!bAutomaticExport && file_type == 'xml') {
                db_log_id = globals.dbLog(log_detail_line + '\n' + i18n.getI18NMessage('avanti.dialog.noResponseDetails') + ' ' + url + ' when sending ' + fileName, 
                    'xml_export', 'export', 'xml', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param, 'Warning');
                globals.dbLogWriteDetails(db_log_id, 'jdf_export', null, sText);
            }
            else if (!bAutomaticExport) {
                db_log_id = globals.dbLog(log_detail_line + '\n' + i18n.getI18NMessage('avanti.dialog.noResponseDetails') + ' ' + url + ' when sending ' + fileName, 
                    'jdf_export', 'export', 'jmf', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param, 'Warning');
                globals.dbLogWriteDetails(db_log_id, 'jdf_export', null, sText);
            }
        }
        else {
            oStatus.sResponseText = response.getResponseBody();
            
            if (response.getStatusCode() && (response.getStatusCode() < 200 || response.getStatusCode() >= 300)) {
                db_log_id = globals.dbLog(log_detail_line + '\n' + 'Received a HTTP status code of ' + response.getStatusCode() + ' from ' + url + ' when sending ' + fileName, 
                    'jdf_export', 'export', 'jmf', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param, 'Warning');
                globals.dbLogWriteDetails(db_log_id, 'jdf_export', null, sText);
            }
            else {
                // Send xmpie shippeing details - check if it returned an error
                /**@type {String} */
                var sErrorMsg = null;
                
                if (aAdditionalHeaders && aAdditionalHeaders.length == 1 && aAdditionalHeaders[0] == "SOAPAction: uStoreWSAPI/CreateDeliveryWithDetailsByOrderProducts") {
                    var oXmlDoc = plugins.XML.XMLDocument([oStatus.sResponseText]);
                    
                    if (oXmlDoc) {
                        var aStatusNodes = oXmlDoc.getElements(['faultstring']);

                        if (aStatusNodes && aStatusNodes.length > 0) {
                            sErrorMsg = aStatusNodes[0].text;
                        }
                    }
                }
                
                if (sErrorMsg) {
                    // if 'Delivery Configuration' error then return a more user friendly error msg
                    if (sErrorMsg.indexOf('The Delivery Configuration is not valid') > -1) {
                        sErrorMsg = "The Shipping Method selected on the shipment is not supported by XMPie."
                    }
                    
                    db_log_id = globals.dbLog(log_detail_line + '\n' + 'Received an error message: ' + sErrorMsg + ' from ' + url + ' when sending ' + fileName, 
                        'jdf_export', 'export', 'xml', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param, 'Warning');
                    globals.dbLogWriteDetails(db_log_id, 'jdf_export', null, sText);

                    oStatus.bSuccess = false;
                }
                else if (file_type == 'xml') {
                    db_log_id = globals.dbLog(log_detail_line + '\nSending ' + fileName + ' request to ' + url + ' and received response status: ' + response.getStatusCode(), 
                        'xml_export', 'export', 'xml', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param);
                    globals.dbLogWriteDetails(db_log_id, 'xml_export', utils.stringReplace(oStatus.sResponseText, '><', '>\n<'), sText);

                    oStatus.bSuccess = true;
                }
                else {
                    db_log_id = globals.dbLog(log_detail_line + '\nSending ' + fileName + ' request to ' + url + ' and received response status: ' + response.getStatusCode(), 
                        'jdf_export', 'export', 'jmf', 'http', application.getUUID(globals.org_id), 'sending_http_post', 'Detail', db_log_parent_id_param);
                    globals.dbLogWriteDetails(db_log_id, 'jdf_export', utils.stringReplace(oStatus.sResponseText, '><', '>\n<'), sText);

                    oStatus.bSuccess = true;
                }
            }            
        }
        httpClient.close();
    }
    
    return oStatus;
}

/**
 * @param {Number} num
 * @param {Array<Number>} aNums
 *
 * @return
 * @properties={typeid:24,uuid:"924DD69E-48F2-40A0-B3A6-F20D23E1A4E3"}
 */
function isSmallestNum(num, aNums){
	var bIsSmallest = true
	
	for(var i=0;i<aNums.length;i++){
		if(aNums[i] < num){
			bIsSmallest = false
			break
		}
	}
	
	return bIsSmallest
}

/**
 * @public 
 * 
 * @param {String} [sSeparator]
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"BDD86FBD-D728-4D6A-B4E2-076E2E717CD2"}
 */
function getTimeStamp(sSeparator){
	if (!sSeparator) {
		sSeparator = '';
	}
	
	var d = application.getTimeStamp()
	var sDate = d.getFullYear().toString() + sSeparator + (d.getMonth()+1).toString() + sSeparator + d.getDate().toString() + sSeparator;
	var sTime = d.getHours().toString() + sSeparator + d.getMinutes().toString() + sSeparator + d.getSeconds().toString() + sSeparator + d.getMilliseconds().toString();
	
	return sDate + sTime;
}

/**
 * Does a numeric sort by the digits in each array entry (ignoring the non-digits)
 *
 * <AUTHOR> Dotzlaw
 * @since Oct 21, 2020
 * @param {Array} aArray
 * @return {Array} sorted array
 * @public
 * @SuppressWarnings(wrongparameters)
 * @properties={typeid:24,uuid:"F052ABB2-9060-4BAB-AC23-AE2E1DF4236D"}
 */
function arraySortNumericDigitsInString(aArray) {

    var re = /\D/g;

    aArray.sort(function(a, b) {
        return ( parseInt(a.replace(re, ""), 10) - parseInt(b.replace(re, ""), 10) );
    });

    return aArray;
}


/**
 * Copies all types of arrays, including arrays of objects
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 13, 2015
 * @param {Array} o 
 * @public
 * @SuppressWarnings(wrongparameters)
 * @return
 * @properties={typeid:24,uuid:"EA6BF542-B69C-409D-ADD7-52699CC13680"}
 */
function arrayCopy (o) {
	
	var out, v, key;
	   out = Array.isArray(o) ? [] : {};
	   for (key in o) {
	       v = o[key];
	       out[key] = (typeof v === "object") ? arrayCopy(v) : v;
	   }
	   return out;
}

/**
 * Removes one array from another array
 *
 * <AUTHOR> Dotzlaw
 * @since Nov 7, 2019
 * @param {Array} aBase
 * @param {Array} aRemove
 * @public
 * 
 * @return
 * @properties={typeid:24,uuid:"A4CCD0C2-C719-432B-883F-A488825C9A06"}
 */
function arrayRemove(aBase, aRemove) {

    var i = 0,
        iMax = 0;

    iMax = aRemove.length;
    for (i = 0; i < iMax; i++) {
        aBase = remove(aBase, aRemove[i]);
    }

    function remove(arr, value) {

        return arr.filter(function(ele) {
            return ele != value;
        });
    }
    
    return aBase;
}


/**
 * Rounds a number by a specific increment
 *
 * <AUTHOR> Dotzlaw
 * @since Jan 22, 2015
 * @param {Number} nBase - The base number
 * @param {Number} nIncrement - The increment amount
 * @returns {Number} The new number
 * @public
 *
 * @properties={typeid:24,uuid:"9A806E51-EDF9-44A9-9E4E-BDDFC462E6CD"}
 */
function roundByIncrement (nBase, nIncrement) {
	
	return  Math.ceil( nBase / nIncrement ) * nIncrement;
	
	
//	return (Math.round(nBase) - (Math.round(nBase) % nIncrement)) + nIncrement;
}

/**
 * Sets the version number in the UI
 *
 * <AUTHOR> Dotzlaw
 * @since Jun 4, 2015
 * @public
 *
 * @properties={typeid:24,uuid:"B4224FEC-DB82-4101-8154-9611CD144315"}
 */
function setVersionNumber () {
	
	/*** @type {JSFoundSet<db:/avanti/sys_system>} */
	var fsSys = datasources.db.avanti.sys_system.getFoundSet(),
		rSys = null;
	
	fsSys.loadAllRecords();
	
	if (utils.hasRecords(fsSys)){
		
		rSys = fsSys.getRecord(1); // there should only be one record in this table
		
		// Load the Framework UI with the version info
		globals.version_major = rSys.sys_major_version;
		globals.version_minor = rSys.sys_minor_version;
		globals.version_revision = rSys.sys_revision_version;
		globals.version_build = rSys.sys_build_version;
		
	}
	return;
}

/**
 * Returns the slingshot version currently installed
 * 
 * @public 
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"F80ABF86-738A-49BC-BB67-E61D4D014CC9"}
 */
function getVersionNumber() {
    var sVersionNum =  globals.version_major + '.' + globals.version_revision + '.' + globals.version_minor + '.' + globals.version_build;

    // setVersionNumber() hasnt been called yet
    if (sVersionNum == "00.00.00.00") {
        setVersionNumber();
        sVersionNum = globals.version_major + '.' + globals.version_revision + '.' + globals.version_minor + '.' + globals.version_build;
    }
    
    return sVersionNum;
}

/**
 * Get an attachment based on a URL, FTP or Local/Network Folder
 * 
 * @param {String} sAttachmentPath The URL of the attachment that needs to be downloaded.
 * @param {String} sAttachmentUsername
 * @param {String} sAttachmentPassword 
 * @param {Number} bIsLink
 * @param {Object} [jobDetails] Object to store job details regarding errors/warnings for web to print integration
 * @param {JSRecord<db:/avanti/sa_order>} [rOrder]
 * @param {String} [sOverrideFileName]
 * @param {Number} [bUseSFTP]
 * @param {String} [sSFTPHostKey]
 * @param {Number} [nSFTPPort]
 * @param {Boolean} [bJustReturnFileObject]
 * 
 * @return {JSRecord<db:/avanti/sys_document>|plugins.file.JSFile} - returns a plugins.file.JSFile if bJustReturnFileObject 
 * 
 * @properties={typeid:24,uuid:"5E0E048A-9539-44C0-B72B-6D6EB91531F4"}
 */
function downloadFile(sAttachmentPath, sAttachmentUsername, sAttachmentPassword, bIsLink, jobDetails, rOrder, sOverrideFileName, bUseSFTP, sSFTPHostKey, nSFTPPort, bJustReturnFileObject) {
	jobDetails.downloadFileInfo = 'Downloading at: ' + sAttachmentPath;
	var oAttachmentObject = getBytesByPath(sAttachmentPath, sAttachmentUsername, sAttachmentPassword, jobDetails, bUseSFTP, sSFTPHostKey, nSFTPPort);
	var aAttachmentBytes = oAttachmentObject.byteArray;
	var sAttachmentFilePath = '';
	if (sOverrideFileName) {
		sAttachmentFilePath = sOverrideFileName;
	}
	else {
		sAttachmentFilePath = oAttachmentObject.filePath;
	}

	if (aAttachmentBytes == null || aAttachmentBytes.length < 1) {
		if (oAttachmentObject.bFileCannotBeFoundOnServer && oAttachmentObject.sDocumentFilePath) {
			application.showURL(oAttachmentObject.sDocumentFilePath);
			return null;
		}
		throw new Error('There was no data found at: ' + sAttachmentPath);
	}

	if (plugins.file.writeFile(sAttachmentFilePath, aAttachmentBytes)) {
		var attachedFile = plugins.file.convertToJSFile(sAttachmentFilePath);
		
		if (bIsLink) {
			if (bJustReturnFileObject) {
				return attachedFile;
			}
			else if (sAttachmentFilePath) {
				var sTo = globals.avDocs_userDownloadDirectory + "\\" + sAttachmentFilePath;
				var fos = new Packages.java.io.FileOutputStream(sTo);
				try {
					fos.write(aAttachmentBytes);
				}
				finally {
					fos.close();
				}
			}
			return null;
		}
		else {
			forms["_docs_base"].init_avDocs_oDocs();
			globals.avDocs_oDocs.callType = 'o';
			if (scopes.avWebToPrint.sImportOrderRequestType === scopes.avWebToPrint.sNewEstimateRequestType) {
				globals.avDocs_oDocs.callType = 'e';
			}
			if (rOrder) {
				globals.avDocs_oDocs.callOrderRecord = rOrder;
			}
			else {
				globals.avDocs_oDocs.callOrderRecord = '';
			}

			return forms['_docs_base'].addFile(attachedFile);
		}
	}
	else {
		throw new Error('File could not be written to: ' + sAttachmentPath.substring(sAttachmentPath.lastIndexOf('\\') + 1));
	}
}

/**
 * <AUTHOR>
 * 
 * code copied from dc_export - put here because export btn doesnt work unless your top level form is a tbl
 * 
 * @param {String} sFormName
 * @param {String} sProgramName
 *
 * @properties={typeid:24,uuid:"A623FE8E-6AE6-4C42-A281-22941B1E21D7"}
 */
function exportTableForm(sFormName, sProgramName){
	var _form = sFormName;
    var _program = sProgramName;

    // get the names of the fields on the form
    var _colProps = forms.svy_nav_base.getExportFormFields(_form);

    /** @type {Array} */
    var _colList = _colProps[0];
    var _colValueList = _colProps[1];
    var _colFormats = _colProps[2];
    var _maxCols = _colList.length;
    var _format;

    if (_colList.length > 0) {
        //output the column names first
        var _output = _colList.join('\t');

        // sl-8954 - use getFoundSetCount() instead of getSize(), in case there are more than 200 recs
        var mx = databaseManager.getFoundSetCount(forms[_form].foundset);

        //loop through foundset of form and get all the columns and data
        for (var i = 1; i <= mx; i++) {
            var fsRec = forms[_form].foundset.getRecord(i);

            for (var c = 0; c < _maxCols; c++) {
                if (c == 0) {
                    //first column
                    _output += '\n"';
                }
                else {
                    //subsequent columns
                    _output += '\t"';
                }
                if (!_colValueList[c]) {
                    if (_colFormats[c]) {
                        if (/i18n/.test(_colFormats[c])) {
                            _format = i18n.getI18NMessage(_colFormats[c]);
                        }
                        else {
                            _format = _colFormats[c];
                        }
                        _output += utils.dateFormat(fsRec[_colList[c]], _format) + '"';
                    }
                    else {
                        _output += fsRec[globals.translateExportColumn(_colList[c])] + '"';
                    }
                }
                else {
                    _output += application.getValueListDisplayValue(_colValueList[c], fsRec[_colList[c]]) + '"';
                }
            }
        }

        var fileName = 'Export_' + _program + '_' + utils.dateFormat(new Date(), 'yyyy-MM-dd') + '.xls';
        //prompt for the fileName is smart client
        if (application.getApplicationType() == APPLICATION_TYPES.SMART_CLIENT) {
            fileName = plugins.file.showFileSaveDialog(fileName);
        }
        var success = plugins.file.writeTXTFile(fileName, _output);
        
        if (success) {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('svy.fr.lbl.export'), i18n.getI18NMessage('svy.fr.dlg.export_successful'), i18n.getI18NMessage('avanti.dialog.ok'));
        }
        else {
            globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.export'), i18n.getI18NMessage('svy.fr.dlg.export_failed'), i18n.getI18NMessage('avanti.dialog.ok'));
        }
    }
}

/**
 * Loads all the records for a foundset
 * @param {JSFoundset} fs
 *
 * @return
 * @properties={typeid:24,uuid:"E53EFC86-BA06-4014-9A8D-89715A5D8D89"}
 */
function loadAllRecords(fs){
	var bCheck = false;
	
	if(!fs){
		return bCheck;
	}
	try{
		bCheck = fs.loadAllRecords();
		if(bCheck){
			fs.getRecord(databaseManager.getFoundSetCount(fs));
		}
	}
	catch(e){
		//Display a warning dialog that alerts the user that something went wrong
		globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('svy.fr.lbl.export'), i18n.getI18NMessage('svy.fr.dlg.export_error'), i18n.getI18NMessage('avanti.dialog.ok'));
	}
	return bCheck;
}

/**
 * Pads a number with leading zeros
 *
 * <AUTHOR> Dotzlaw
 * @since Aug 17, 2015
 * @param {Number} iNum - The number to pad
 * @param {Number} iWidth - the # to pad to
 * @param {String} [sPad] - the character to use for padding (most often '0')
 * @returns {String} the padded string
 * @public
 *
 * @properties={typeid:24,uuid:"5D08B52C-8FFA-4531-ADF9-631E3D2E0E9A"}
 */
function padNumber(iNum, iWidth, sPad) {
	
	var sNum = "";
	
	sPad = sPad || '0';
	sNum = iNum + '';
	return sNum.length >= iWidth ? sNum : new Array(iWidth - sNum.length + 1).join(sPad) + sNum;
}

///**
// * Represents a simple stop watch.
// * 
// * @properties={typeid:35,uuid:"A5CED56C-98F1-42C8-AD5B-9A2D43BFE963",variableType:-4}
// */
//var StopWatch = function() {
//	var _nStart = 0; 
//	var _nStop = 0; 
//	var _nTotal = 0;
//	
//	/**
//	 * Start timing.
//	 */
//	this.start = function(bReset) {
//		if (bReset) {
//			_nStart = _nStart = _nTotal = 0;
//		}
//		
//		_nStart = new Date().getTime();
//	};
//	
//	/**
//	 * Pause the timer.
//	 */
//	this.stop = function() {
//		_nStop = new Date().getTime();
//		_nTotal += _nStop - _nStart;
//		
//		return _nTotal;
//		
//	}
//	
//	/**
//	 * Reset the stop watch.
//	 */
//	this.reset = function() {
//		_nStart = _nStart = _nTotal = 0;
//	}
//	
//	/**
//	 * Current total as seconds - or milliseconds if bReturnMilliseconds == true
//	 * 
//	 * @param {Boolean} [bReturnMilliseconds]
//	 * 
//	 * @return {Number} The number of seconds.
//	 */
//	this.total = function(bReturnMilliseconds) {
//        if (!_nTotal || _nTotal === 0) {
//            return 0;
//        }
//
//        if (bReturnMilliseconds) {
//            return _nTotal;
//        }
//        else {
//            return (_nTotal / 1000);
//        }
//	}
//};

/**
 * @constructor
 * @public 
 * @properties={typeid:24,uuid:"A5CED56C-98F1-42C8-AD5B-9A2D43BFE963"}
 */
function StopWatch() {
    // Instance check for proper construction
    if (!(this instanceof StopWatch)) {
        return new StopWatch();
    }
    
    /**
     * @protected
     * @type {Number}
     */
    this._nStart = 0;
    
    /**
     * @protected
     * @type {Number}
     */
    this._nStop = 0;
    
    /**
     * @protected
     * @type {Number}
     */
    this._nTotal = 0;

    /**
     * Start timing
     * @public
     * @param {Boolean} [bReset] Whether to reset the timer before starting
     * @this {StopWatch}
     */
    this.start = function(bReset) {
        if (bReset) {
            this._nStart = this._nStop = this._nTotal = 0;
        }
        this._nStart = new Date().getTime();
    };

    /**
     * Stop timing
     * @public
     * @return {Number} The total time elapsed in milliseconds
     * @this {StopWatch}
     */
    this.stop = function() {
        this._nStop = new Date().getTime();
        this._nTotal += this._nStop - this._nStart;
        return this._nTotal;
    };

    /**
     * Reset the stop watch
     * @public
     * @this {StopWatch}
     */
    this.reset = function() {
        this._nStart = this._nStop = this._nTotal = 0;
    };

    /**
     * Get the current total time
     * @public
     * @param {Boolean} [bReturnMilliseconds] If true, returns milliseconds instead of seconds
     * @return {Number} The total time elapsed (in seconds or milliseconds)
     * @this {StopWatch}
     */
    this.total = function(bReturnMilliseconds) {
        if (!this._nTotal || this._nTotal === 0) {
            return 0;
        }
        if (bReturnMilliseconds) {
            return this._nTotal;
        } else {
            return (this._nTotal / 1000);
        }
    };
}

/**
 * @public 
 * @properties={typeid:35,uuid:"B5CED56C-98F1-42C8-AD5B-9A2D43BFE963",variableType:-4}
 */
var STOPWATCH = StopWatch;

/**
 * @private
 * @SuppressWarnings(unused)
 * @properties={typeid:35,uuid:"C5CED56C-98F1-42C8-AD5B-9A2D43BFE963",variableType:-4}
 */
var init = (function() {
	StopWatch.prototype = Object.create(Object.prototype);
	StopWatch.prototype.constructor = StopWatch;
})();

/**
 * @param {String} sTime
 * @return {String}
 *
 * @properties={typeid:24,uuid:"B0D11CCE-8BE9-463B-8DCC-546498A9581B"}
 */
function createCronStringFromTime(sTime){
	// '0 30 14 ? * *' is 2:30 pm 
	
	if(sTime.indexOf(':') > -1){
		var aNums = sTime.split(':');
		/***@type {Number} */
		var hours = parseInt(aNums[0]);
		
		if(aNums[1].indexOf(' ') > -1){
			var aMinAndMeridian = aNums[1].split(' ');
			var mins = aMinAndMeridian[0];
			
			if(hours==12){
				if(aMinAndMeridian[1].toLowerCase() == 'am'){
					hours = 0;
				}
			}
			else if(aMinAndMeridian[1].toLowerCase() == 'pm'){
				hours += 12;
			}
			
			return '0 ' + mins  + ' ' + hours.toString() + ' ? * *';
		}
	}
	
	return null	
}


/**
 * Parse time string to integers
 * @param {String} sTime
 *
 * @return {{hours:Number, minutes:Number}}
 * @properties={typeid:24,uuid:"AB97278F-8EB9-4F54-A5AD-0892C3DE2475"}
 */
function parseTime(sTime) {
	if (sTime.indexOf(':') > -1) {
		var aNums = sTime.split(':');
		/***@type {Number} */
		var hours = parseInt(aNums[0]);

		if (aNums[1].indexOf(' ') > -1) {
			var aMinAndMeridian = aNums[1].split(' ');
			var mins = aMinAndMeridian[0];

			if (hours == 12) {
				if (aMinAndMeridian[1].toLowerCase() == 'am') {
					hours = 0;
				}
			} else if (aMinAndMeridian[1].toLowerCase() == 'pm') {
				hours += 12;
			}
			/** @type {{hours:Number, minutes:Number}} */
			var oResult = new Object();
			oResult.hours = hours;
			oResult.minutes = mins;
			return oResult;
		}
	}

	return null;
}


/**
 * Create a zip file based on array of file paths and return file location of a zip file.
 * 
 * @param {String} sZIPFilePath Absolute file path of zip file.
 * @param {Array<String>} aFilePaths Array of file paths
 *
 * @return
 * @properties={typeid:24,uuid:"46F72AA5-C495-4737-B5C7-7343CC54756A"}
 */
function createZIPFile(sZIPFilePath, aFilePaths) {
	try {
		
		var fFolderFile = new Packages.java.io.File(sZIPFilePath);
		var fParentFolder = fFolderFile.getParentFile();
		if (fParentFolder && !fParentFolder.exists()) {
			fParentFolder.mkdirs();
		}
		var fileOutputDestination = new Packages.java.io.FileOutputStream(fFolderFile);
		var zipOutputStream = new Packages.java.util.zip.ZipOutputStream(new Packages.java.io.BufferedOutputStream(fileOutputDestination));
        
    	for (var nFileIndex=0; nFileIndex < aFilePaths.length; nFileIndex++) {
    		var sLabelFilePath =  aFilePaths[nFileIndex];	
    		if(sLabelFilePath != null) {
    			addToZipFile(sLabelFilePath,zipOutputStream);
    		}
        }	
        
        zipOutputStream.flush();
		zipOutputStream.close();

	} catch (ex) {
		
		if (zipOutputStream) {
			zipOutputStream.flush();
			zipOutputStream.close();
		}
		
		application.output(ex.message, LOGGINGLEVEL.ERROR); 
		throw new Error(ex.message);
	}
	       
	return sZIPFilePath;
}

/**
 * Add file to zip file
 * 
 * @param {String} sLabelFilePath
 * @param {Packages.java.util.zip.ZipOutputStream} zipFileOutputStream
 *
 * @properties={typeid:24,uuid:"870B23DA-A380-4A2B-B356-F9A5CD9A88F3"}
 */
function addToZipFile(sLabelFilePath, zipFileOutputStream) {
	var fileToAdd = new Packages.java.io.File(sLabelFilePath);
	if (!fileToAdd.exists()) {
		globals.dbLog('SL-20030: File does not exist on a disk: ' + sLabelFilePath, 'printPackingLabels', null, null, null, null, 'logging', 'Summary', null, null, null, null);
	}
	var fileInputStreamToAdd = new Packages.java.io.FileInputStream(fileToAdd);
    var zipEntry = new Packages.java.util.zip.ZipEntry(fileToAdd.getName());
    zipFileOutputStream.putNextEntry(zipEntry);
   	
    var data = plugins.file.readFile(sLabelFilePath);
    
    var BUFFER = 2048;
    if (data.length < BUFFER) {
        BUFFER = data.length;
    }
    var count;
    while ( ( count = fileInputStreamToAdd.read(data, 0, BUFFER) ) != -1) {
        zipFileOutputStream.write(data, 0, count);
        zipFileOutputStream.flush();
    }

	zipFileOutputStream.closeEntry();
	fileInputStreamToAdd.close();
}

/**
 * Convert JSON Object representation to XML.
 * @param {Object} oObject
 * 
 * @return {String} result xml or error message
 *
 * @properties={typeid:24,uuid:"CC88B5B7-BAE2-49D2-ADBC-EC1C37596B22"}
 */
function convertObjectToXML(oObject) {
    try {    	
    	var sJSONString = plugins.serialize.toJSON(oObject);

    }
    catch (ex) {
        return 'Could not create XML object due to: ' + ex.message;
    }

    return convertJSONStringToXML(sJSONString);
    
    
}

/**
 * Converts Javascript object to XML with attributes
 * @param oObject
 *
 * @return
 * @properties={typeid:24,uuid:"73ABAE2A-9854-46C9-871F-52FC3A6C9C54"}
 */
function convertObjectToXMLWithAttributes(oObject) {
    try {
        var sJSONString = JSON.stringify(oObject);
    }
    catch (ex) {
        return 'Could not create XML object due to: ' + ex.message;
    }

    return convertJSONStringToXMLWithAttributes(sJSONString);
    
    
}

/**
 * Convert JSON String to XML.
 * @param sJSONString {String}
 *
 * @return {String} result xml or error message
 *
 * @properties={typeid:24,uuid:"DE4BC923-85C4-41B4-AF07-020F8CFA64B3"}
 */
function convertJSONStringToXML(sJSONString) {
    try {
        var oJSONObject = new Packages.org.json2.JSONObject(sJSONString);
        var sXMLString = Packages.org.json2.XML.toString(oJSONObject);

        try {
            var newXML = new XML(sXMLString);
            newXML.prettyPrinting = true;
            return newXML.toString();
        }
        catch (ex) {
            return sXMLString;
        }

    }
    catch (ex) {
        return 'Could not create XML object due to: ' + ex.message;
    }
}


/**
 * Convert JSON String to XML with attributes.
 * @param sJSONString {String}
 *
 * @return {String} result xml or error message
 *
 * @properties={typeid:24,uuid:"596A58FD-E9B6-4D56-95EA-81E34339836B"}
 */
function convertJSONStringToXMLWithAttributes(sJSONString) {
    try {
        var sXMLString = Packages.com.github.underscore.U.jsonToXml(sJSONString);

        try {
            var newXML = new XML(sXMLString);
            newXML.prettyPrinting = true;
            return newXML.toString();
        }
        catch (ex) {
            return sXMLString;
        }

    }
    catch (ex) {
        return 'Could not create XML object due to: ' + ex.message;
    }
}



/**
 * @param {Array<Number>} aNums
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"CE8FCE88-81C4-4C6B-812F-0C4E6048573A"}
 */
function getLargestNum(aNums){
	var nLargestNum = null;
	
	for(var i=0;i<aNums.length;i++){
		if(i == 0 || aNums[i] > nLargestNum){
			nLargestNum = aNums[i];
		}
	}
	
	return nLargestNum;
}

/**
 * @param {Array<Number>} aNums
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"CA753CDE-EA34-4997-9243-178F62142994"}
 */
function getSmallestNum(aNums){
	var nSmallestNum = null;
	
	for(var i=0;i<aNums.length;i++){
		if(i == 0 || aNums[i] < nSmallestNum){
			nSmallestNum = aNums[i];
		}
	}
	
	return nSmallestNum;
}

/**
 * Get bytes of input String.
 * @param {String} stringToConvert
 *
 * @return
 * @properties={typeid:24,uuid:"D896EF74-C86B-475C-B29E-6E71B5373771"}
 */
function getBytes(stringToConvert) {
	 var bytes = [];
	  for (var i = 0; i < stringToConvert.length; ++i) {
	    bytes.push(stringToConvert.charCodeAt(i));
	  }
	  return bytes;
}


/**
 * 
 * @param {Boolean} [bFlexoPlates] - use flexo plates instead of normal plates
 *
 * @properties={typeid:24,uuid:"AFC51FC0-F68A-4F04-9213-EAA1011A00F9"}
 */
function getVL_avSales_coatingBlanketsPlates(bFlexoPlates) {
	
	var aReturn = [],
		aDisplay = [],
		i = 0,
		k = 0,
		rRec,
		fsClass = null,
		fsItems = null;
	
	// Add blankets first
	fsClass = _to_in_item_class$blanket;
	addItems();
	
	if (!bFlexoPlates) {
		
		// Then add the plates
		fsClass = _to_in_item_class$plates;
		addItems();
		
	} else {
	
		fsClass = _to_in_item_class$flexoplates;
		addItems();
		
	}
	scopes.globals["avUtilities_arraySort"](aDisplay, aReturn);
	
	application.setValueListItems("avSales_coatingBlanketsPlates", aDisplay, aReturn);
	
	function addItems() {
		
		if ( utils.hasRecords(fsClass) ) {
			
			for ( k = 1; k <= fsClass.getSize(); k++ ) {
				
				fsItems = fsClass.getRecord(k).in_item_class_to_in_item;
				
				fsItems.sort("item_code asc");
			
				for ( i = 1; i <= fsItems.getSize(); i++ ) {
					
					rRec = fsItems.getRecord(i);
			
					aDisplay.push(rRec.item_code + ": " + rRec.item_desc1);
					aReturn.push(rRec.item_id);
				}
			}
		}
	}
}

/**
 * Update the system status for the wtp/dmi/jdf queue processes if needed.
 * @param sProcess Code of process to update the system status date with.  
 *
 * @properties={typeid:24,uuid:"5CA1EF08-7B24-4FFA-B825-E8BCD519A679"}
 */
function updateSystemStatus(sProcess) {
	/** @type {JSFoundSet<db:/avanti/sys_status>} */
	var fsSystemStatus = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_status');
	fsSystemStatus.loadAllRecords();
	var rSystemStatus
	if(fsSystemStatus.getSize() == 0) {
		rSystemStatus = fsSystemStatus.getRecord(fsSystemStatus.newRecord());
	} else {
		rSystemStatus = fsSystemStatus.getSelectedRecord();
	}
	
	switch (sProcess) {
		case 'wtp':
			rSystemStatus.status_wtp_queue_last_run = new Date();
			break;
		case 'dmi':
			rSystemStatus.status_dmi_queue_last_run = new Date();
			break;
		case 'jdf':
			rSystemStatus.status_jdf_queue_last_run = new Date();
			break;
		case 'xmpie':
			rSystemStatus.status_xmpie_queue_last_run = new Date();
			break;
		default:
			break;
	}
	
	var dSystemDate = new Date();
	var iTimeDifferenceInMS;
	var bDMIRunning = true;
	var bJDFRunning = true;
	var bWTPRunning = true;
	
	if(rSystemStatus.dmi_queue_is_active) {
		iTimeDifferenceInMS = dSystemDate.getTime() - rSystemStatus.status_dmi_queue_last_run.getTime();
		if(iTimeDifferenceInMS > iINTEGRATION_VALIDATION_TIME) {
			bDMIRunning = false;
		}
	}
	
	if(rSystemStatus.jdf_queue_is_active == 1) {
		iTimeDifferenceInMS = dSystemDate.getTime() - rSystemStatus.status_jdf_queue_last_run.getTime();
		if(iTimeDifferenceInMS > iINTEGRATION_VALIDATION_TIME) {
			bJDFRunning = false;
		}
	}
	
	if(rSystemStatus.wtp_queue_is_active) {
		iTimeDifferenceInMS = dSystemDate.getTime() - rSystemStatus.status_wtp_queue_last_run.getTime();
		if(iTimeDifferenceInMS > iINTEGRATION_VALIDATION_TIME) {
			bWTPRunning = false;
		}
	}
	
	rSystemStatus.overall_status = bDMIRunning && bJDFRunning && bWTPRunning;
	databaseManager.saveData(rSystemStatus);
}

/**
 * Test FTP connection based on host, username, password
 * 	
 * @param {String} sFTPHost The FTP host address
 * @param {Number} sFTPPort The FTP port
 * @param {String} sFTPUsername The FTP username to pass into the address
 * @param {String} sFTPPassword The FTP password to send into the address
 * @param {Boolean} bCheckFolder Check if the folder exists
 * @param {String} sFTPFolder The FTP inbound folder to connect to.
 * @param {Number} bUseSFTPClient
 * @param {String} sFTPHostPublicKey
 * @param {Boolean} [bHideDialogs] 
 * @param {Number} [nPasswordsDecrypted]
 
 *
 * @return
 * @properties={typeid:24,uuid:"940DAF58-F7D7-42A9-B12E-AC688312A9AA"}
 */
function onTestFTP(sFTPHost, sFTPPort, sFTPUsername, sFTPPassword, bCheckFolder, sFTPFolder, bUseSFTPClient, sFTPHostPublicKey, bHideDialogs, nPasswordsDecrypted) {
    var sPassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sFTPPassword);
    var bLoginSuccess = false;
    var bInboundFolderExists = false;

    if (!sFTPPort) {
        sFTPPort = 21;
    }

    var sMsgHost = " host: " + sFTPHost;
    var sMsgPort = ", port: " + sFTPPort;
    var sMsgUser = ", user: " + sFTPUsername;
    var sMsgEncPWD = ", enc pwd: " + sFTPPassword;
    var sMsgDecPWD = ", dec pwd: " + sPassword.substr(0, 3);
    var sMsgFolder = ", folder: " + sFTPFolder;
    var sMsgSFTP = ", sftp: " + bUseSFTPClient;
    var sMsgPublicKey = ", public key: " + sFTPHostPublicKey;
    var sMsgPwdsDecrypted = ", pwds decrypted: " + nPasswordsDecrypted;
    var sMsg = "cant connect to ftp site. " + sMsgHost + sMsgPort + sMsgUser + sMsgEncPWD + sMsgDecPWD + sMsgFolder + sMsgSFTP + sMsgPublicKey + sMsgPwdsDecrypted;

    if (!bUseSFTPClient) {
        // Create a client for the FTP connection
        var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();

        try {
            ftpClient.connect(sFTPHost, sFTPPort);
            ftpClient.setDefaultTimeout(scopes.avUtils.getFtpDefaultTimeout());
            ftpClient.enterLocalPassiveMode();
            bLoginSuccess = ftpClient.login(sFTPUsername, sPassword);

            if (bLoginSuccess) {
                if (sFTPFolder != undefined && sFTPFolder != '') {
                    if (bCheckFolder) {
                        bInboundFolderExists = ftpClient.cwd(sFTPFolder);
                        if (bInboundFolderExists < 200 || bInboundFolderExists > 300) {
                            throw new Error(i18n.getI18NMessage('avanti.lbl.ftpFolderDoesNotExist', [sFTPFolder]));
                        }
                    }
                    if (bHideDialogs == null || bHideDialogs == false) {
                        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.connected'), i18n.getI18NMessage('avanti.dialog.connectedDetails'), i18n.getI18NMessage('avanti.dialog.okay'));
                    }
                    return true;
                }
                else {
                    if (bHideDialogs == null || bHideDialogs == false) {
                        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.connected'), i18n.getI18NMessage('avanti.dialog.connectedDetails'), i18n.getI18NMessage('avanti.dialog.okay'));
                    }
                    return true;
                }
            }
            else {
                if (bHideDialogs == null || bHideDialogs == false) {
                    globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.connectionFailed'), i18n.getI18NMessage('avanti.dialog.connectionFailedDetails'), i18n.getI18NMessage('avanti.dialog.okay'));
                }

                scopes.avUtils.quickLog('SL-16607', sMsg);
                return false;
            }
        }
        catch (ex) {
            if (bHideDialogs == null || bHideDialogs == false) {
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.connectionFailed'), i18n.getI18NMessage('avanti.dialog.connectionFailedDetails') + '\n' + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
            }

            scopes.avUtils.quickLog('SL-16607', sMsg + ", error: " + ex.message);
            return false;
        }
    }
    else {
        var oSFTPSession = null;
        var oSFTPChannel = null;
        try {
            var jsch = new Packages.com.jcraft.jsch.JSch();
            oSFTPSession = jsch.getSession(sFTPUsername, sFTPHost, sFTPPort);

            if (sFTPHostPublicKey) {
                var hostKeyInputStream = new java.io.ByteArrayInputStream(new Packages.java.lang.String(sFTPHostPublicKey).getBytes());
                jsch.setKnownHosts(hostKeyInputStream);
            }
            else {
                oSFTPSession.setConfig("StrictHostKeyChecking", "no");
            }

            oSFTPSession.setPassword(sPassword);
            oSFTPSession.setTimeout(scopes.avUtils.getSFtpDefaultTimeout());
            oSFTPSession.connect();

            oSFTPChannel = new Packages.com.jcraft.jsch.ChannelSftp();

            oSFTPChannel = oSFTPSession.openChannel("sftp");
            oSFTPChannel.connect();

            if (sFTPFolder != undefined && sFTPFolder != '') {
                if (bCheckFolder) {
                    try {
                        if (sFTPFolder.indexOf('\\') >= 0) {
                            sFTPFolder = utils.stringReplace(sFTPFolder, '\\', '/');
                        }
                        oSFTPChannel.cd(sFTPFolder);
                    }
                    catch (ex) {
                        throw new Error('No inbound folder exists for: ' + sFTPFolder);
                    }
                }
                if (bHideDialogs == null || bHideDialogs == false) {
                    globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.connected'), i18n.getI18NMessage('avanti.dialog.connectedDetails'), i18n.getI18NMessage('avanti.dialog.okay'));
                }
                return true;
            }
            else {
                if (bHideDialogs == null || bHideDialogs == false) {
                    globals.DIALOGS.showInfoDialog(i18n.getI18NMessage('avanti.dialog.connected'), i18n.getI18NMessage('avanti.dialog.connectedDetails'), i18n.getI18NMessage('avanti.dialog.okay'));
                }
                return true;
            }
        }
        catch (ex) {
            if (bHideDialogs == null || bHideDialogs == false) {
                globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.dialog.connectionFailed'), i18n.getI18NMessage('avanti.dialog.connectionFailedDetails') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
            }

            scopes.avUtils.quickLog('SL-16607', sMsg + ", error: " + ex.message);
            return false;
        }
        finally {
            if (oSFTPChannel) {
                oSFTPChannel.exit();
                oSFTPChannel.disconnect();
            }
            if (oSFTPSession) {
                oSFTPSession.disconnect();
            }
        }
    }
}

/**
 * @public 
 * 
 * @param {String} sFormName
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"769C412A-6129-477E-97A0-077DC6726917"}
 */
function isFormShowing(sFormName){
	// if the form hasnt been loaded yet then 'forms[sFormName]' will load it and trigger the form's onLoad(), whcih we dont 
	// want to do, when all we want is to see if the form is currently showing
	
	/**@type {Form} */
	var oForm = forms[sFormName];

	if(oForm){
		var oWindow = oForm.controller.getWindow();
		
		if(oWindow){
			return oWindow.isVisible();
		}
	}
	
	return false;
}

/**
 * Parse CSV input from sData and return an array of an array of strings.
 * @param {String} sData
 * @param {Boolean} bTrim
 * @param {String} sSeparator
 * 
 * @return
 * @properties={typeid:24,uuid:"CE7C76F1-AE38-416B-9534-0053FB27333F"}
 */
function parseCSV(sData, bTrim, sSeparator) {
   var aParsedData;
   var aRow;
   var aSearchTemplate;
   var aSearch;
   var bInQuotes;
   var bSpecial;
   var sColumn;


   // If no string to parse was passed there is no reason to run this method so return
   if (!sData)
   {
      return null;
   }

   // If a separator has been defined, set it
   if (sSeparator)
   {
      aSearchTemplate = ['"', "\n", "\r", sSeparator];
   } else {
      aSearchTemplate = ['"', "\n", "\r"];
   }
   
   // Set the default character search array made up of the special characters that csv has to deal with
   aSearch = aSearchTemplate.slice(0);

   // Initialize the return array, the first column, and the first row array
   aParsedData = new Array();
   sColumn = "";
   aRow = new Array();

   // Loop through the characters in the string
   for (var i = 0; i < sData.length; i++) {
   		// Reset the special character marker to false
   		bSpecial = false;

   		// Loop through the current special character search array
   		for (var s = 0; s < aSearch.length; s++)
   		{
   			// And if we hit a speical character note that
			if ( sData[i] == aSearch[s]) {
				bSpecial = true;
				break;
			}
		}

		// If this is a special character that we are currently looking for it process it separately
		if ( bSpecial ) {
			// Switch on the current character (we can only get to separator characters, carriage returns, and line feeds if we aren't in double-quotes which is take care of by the aSearch array)
			switch (sData[i]) {
				// If the character is the separator OUTSIDE DOUBLE QUOTES then we know this is the end of a column of data, so push it to the row and reinitialize the column string
				case sSeparator:
					if ( !bInQuotes ) {
			            // If we are not inside double-quotes, are currently on the separator, AND the next character is a quote,
			            // then we know this is the end of a column of data so push it to the row and reinitialize the column string
			            if (sData[i+1] && '"' == sData[i+1])
			            {
			            	if (bTrim) {
			            		sColumn = utils.stringTrim(sColumn);
		            		}
	            			aRow.push(sColumn);
	            			sColumn = "";
	            			aSearch = aSearchTemplate.slice(0);
			            } // If we are not inside quotes but the next character is not another set of double quotes it means this is not really a separator and we just add the character to the data
			            else
			            {
			            	if (bTrim) {
			            		sColumn = utils.stringTrim(sColumn);
		            		}
	            			aRow.push(sColumn);
	            			sColumn = "";
		            		aSearch = aSearchTemplate.slice(0);
			            }
		            }
			        // If we are inside a set of double-quotes, this is not really a separator and we just add the character to the data
			        else {
			        	sColumn += sData[i];
			            aSearch = aSearchTemplate.slice(0);
		            }
		        break;
		         // If this is a carriage return then it is the end of a row so push the column to the row and then push the row to the ParsedData array
		         case "\r":
			         if (bTrim) {
			         	sColumn = utils.stringTrim(sColumn);
			         }
			         aRow.push(sColumn);
			         if (!isEmptyRow(aRow)) {
			         	aParsedData.push(aRow);
			         }
			         aRow = new Array();
			         sColumn = "";
			
			         // If the next character happens to be a new line then skip it because this is a csv whose rows end with \r\n
			         if ( sData[i+1] && "\n" == sData[i+1] ) {
			         	i++;
			         }
		         break;
		         // If this is a new line then it is the end of a row so push the column to the row and then push the row to the ParsedData array
		         case "\n":
			         if (bTrim) {
			            sColumn = utils.stringTrim(sColumn);
			         }
			         aRow.push(sColumn);
			         if (!isEmptyRow(aRow)) {
			         	aParsedData.push(aRow);
			         }
			         aRow = new Array();
			         sColumn = "";
		         break;     
		         // Quotes are highly special, read on
		         case '"':
			         // Check to see if we are already inside a set of double-quotes
			         if ( bInQuotes ) {
			            // If we are inside double-quotes, are currently on a quote, AND the next character is a quote it means we have found an escaped quote that we want to allow
			            // So add a single quote to the column and then skip the next character by incrementing i
			            if (sData[i+1] && '"' == sData[i+1]) {
			            	sColumn += '"';
		            		i++;
			            }
			            // If we are inside quotes but the next character is not another set of double quotes it means we have reached the quoted data so change the Search array back to our normal set and move on
			            else {
			            	aSearch = aSearchTemplate.slice(0);
		            		bInQuotes = false;
			            }
		            }
			        // If we are not inside a set of double-quotes, this is our first set and means that the next special characters that we care about are closing double-quotes and the separator character
			        else {
			        	bInQuotes = true;
			            aSearch = ['"', sSeparator];
			        }
	        	break;
	        }
      }
      // If this is NOT a special character just add it to the current column's data
      else {
         sColumn += sData[i];
      }
   }

   // Finalize the data by adding the last column and row to our return data
   if (bTrim)
   {
      sColumn = utils.stringTrim(sColumn);
   }
   aRow.push(sColumn);
   
   if (!isEmptyRow(aRow)) {
   	aParsedData.push(aRow);
   }

   // Enjoy your data
   return aParsedData;
}


/**
 * Checks if array has only empty string elements
 * @param {Array} aRow
 *
 * @return {Boolean} is empty or not
 * @properties={typeid:24,uuid:"8751AC61-308E-445B-80CD-11EE4DF7A1EE"}
 */
function isEmptyRow (aRow) {
	if (!aRow) {
		return true;
	}
	
	for (var index = 0; index < aRow.length; index++) {
		/** @type {String} */
		var sRowData = aRow[index];
		if (sRowData) {
			// if there are spaces in a string it doesn't count like a data
			if (sRowData.trim()) {
				return false;
			}
		}
	}
	
	return true;
}

/**
 * @AllowToRunInFind
 * 
 * Return tax group ID based on matching code or description.
 * @param sCode
 *
 * @return
 * @properties={typeid:24,uuid:"7B18997A-A3CC-485E-BA17-8276BDBC6F11"}
 */
function getTaxGroupIDByCode(sCode) {
	/** @type{JSFoundSet<db:/avanti/sys_sales_tax_group>} */
	var fsTaxGroup = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_sales_tax_group');
	
	if(sCode && fsTaxGroup.find()) {
		fsTaxGroup.taxgroup_code = sCode;
		fsTaxGroup.org_id = globals.org_id;
		fsTaxGroup.newRecord();
		fsTaxGroup.taxgroup_desc = sCode;
		fsTaxGroup.org_id = globals.org_id;
		if(fsTaxGroup.search() > 0) {
			return fsTaxGroup.taxgroup_id;
		}
	}
	
	return null;
}

/**
 * @param {String} shipmentType
 *
 * @return
 * @properties={typeid:24,uuid:"4D86C959-4AE7-400C-B1B0-0E4D13EE2E73"}
 */
function getShipmentTypeCode(shipmentType) {
    if (shipmentType == 'Proofs') {
        return 'P';
    }
    else if (shipmentType == 'Standard') {
        return 'S';
    }
    else if (shipmentType == 'Other') {
        return 'O';
    }
    else if (shipmentType == 'Outside Services') {
        return 'OS';
    }
    return 'S';
}


/**
 * @AllowToRunInFind
 * 
 * Return ship method ID based on matching code or description.
 * @param sCode
 *
 * @return
 * @properties={typeid:24,uuid:"A43D6E94-E976-4C7C-A3A2-0CE3116DCD25"}
 */
function getShipMethodIDByCode(sCode) {
	/** @type{JSFoundSet<db:/avanti/sys_shipping_method>} */
	var fsShippingMethod = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_shipping_method');
	
	if(sCode && fsShippingMethod.find()) {
		fsShippingMethod.shipmethod_code = sCode;
		fsShippingMethod.org_id = globals.org_id;
		fsShippingMethod.newRecord();
		fsShippingMethod.shipmethod_desc = sCode;
		fsShippingMethod.org_id = globals.org_id;
		if(fsShippingMethod.search() > 0) {
			return fsShippingMethod.shipmethod_id;
		}
	}
	
	return null;
}

/**
 * @param {Date} dDate
 * @param {String} [sSeparator]
 *
 * @return
 * @properties={typeid:24,uuid:"CB849AA3-5A51-454D-A8CF-9DE6C4FB9E04"}
 */
function formatDate(dDate, sSeparator){
	if(dDate){
		if(!sSeparator){
			sSeparator = '/';
		}
		
		var sMonth = dDate.getMonth() + 1;
		if(sMonth < 10){
			sMonth = '0' + sMonth.toString();
		}
		
		var sDay = dDate.getDate();
		if(sDay < 10){
			sDay = '0' + sDay.toString();
		}
		
		return sMonth + sSeparator + sDay + sSeparator + dDate.getFullYear();
	}
	
	return null;
}

/**
 * @param {String|Number|Date|UUID} testValue
 * @param {String|Number|Date} startCompareValue
 * @param {String|Number|Date} endCompareValue
 * @param {String} [sInclusiveOrExclusive] - i or e - 'i' by def
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"F0FF5E01-3908-4C12-8082-3B0D8F65BC4E"}
 */
function isBetween(testValue, startCompareValue, endCompareValue, sInclusiveOrExclusive){
	if(sInclusiveOrExclusive == 'e'){
		return testValue > startCompareValue && testValue < endCompareValue;
	}
	else{
		return testValue >= startCompareValue && testValue <= endCompareValue;
	}
}


/**
 * @param url {String} url to search for file name
 *
 * @return {String} file name if found, if not found return empty string
 *
 * @properties={typeid:24,uuid:"79DD6099-A33D-4D15-BB90-BF61DA99382A"}
 */
function getFilenameFrom(url) {
    if (url) {
        var urlObject = new java.net.URL(url);
        var urlPath = urlObject.getPath();
        if (!urlPath) {
            return "";
        }
        return urlPath.substring(urlPath.lastIndexOf('/') + 1);
    }
    return "";
}

/**
 * Get hostmae from file url
 * @param url {String} url to search for file name
 *
 * @return {String} hostname if exists, otherwise return empty string
 *
 * @properties={typeid:24,uuid:"6A789A34-CC02-4378-A775-C4F8F9EEF647"}
 */
function getHostname(url) {
    if (url) {
        var urlObject = new java.net.URL(url);
        return urlObject.getHost();
    }
    return "";
}

/**
 * @AllowToRunInFind
 * 
 * Returns a given URL param if found
 *
 * @param url {String} url to search for parameters
 * @param paramName {String} parameter name
 *
 * @return {String} if parameter found return parameter value, if not found return null
 * @properties={typeid:24,uuid:"383E398B-5759-43CF-888E-4C09478BC441"}
 */
function getParameterValue(url, paramName) {
    if (!url) {
        return null;
    }

    if (!paramName) {
        return null;
    }
    var urlObject = new java.net.URL(url);
    var query = urlObject.getQuery();
    if (!query) {
        return null;
    }
    var params = query.split("&");
    for (var i = 0; i < params.length; i++) {
        var idx = params[i].indexOf("=");
        var paramKey = java.net.URLDecoder.decode(params[i].substring(0, idx), "UTF-8");
        if (paramKey && paramKey.toLowerCase().indexOf(paramName.toLowerCase()) >= 0) {
            return java.net.URLDecoder.decode(params[i].substring(idx + 1), "UTF-8");
        }
    }
    return null;
}


/**
 * Return an object with attachment path on local folder and array of bytes of attachment.
 * @param {String} sAttachmentPath
 * @param {String} sAttachmentUsername
 * @param {String} sAttachmentPassword
 * @param {Object} oJobDetails
 * @param {Number} [bUseSFTP]
 * @param {String} [sSFTPHostKey]
 * @param {Number} [nSFTPPort]
 *
 * @return {{filePath:String, byteArray:Array<byte>, bFileCannotBeFoundOnServer: Boolean, sDocumentFilePath: String}}
 * @properties={typeid:24,uuid:"491C027D-15EC-44A6-87AD-82418460C88D"}
 */
function getBytesByPath(sAttachmentPath, sAttachmentUsername, sAttachmentPassword, oJobDetails, bUseSFTP, sSFTPHostKey, nSFTPPort) {
	var aAttachmentBytes;
	var sAttachmentFileName = '';
	
	/** @type {{filePath:String, byteArray:Array<byte>, bFileCannotBeFoundOnServer: Boolean, sDocumentFilePath: String}} */
	var returnObject = new Object();
	returnObject.bFileCannotBeFoundOnServer = false;
	
	// Process HTTP
    if (sAttachmentPath.indexOf('http://') >= 0 || sAttachmentPath.indexOf('https://') >= 0) {
        
        var oHTTPClient = plugins.http.createNewHttpClient();
        oJobDetails.downloadFileInfo = 'Create get request';
        var oHTTPRequest = oHTTPClient.createGetRequest(escapeSpacesOnly(sAttachmentPath));
        var oHTTPResponse = oHTTPRequest.executeRequest();
       
        sAttachmentFileName = getFileNameFromURLOrHTTPResponse(sAttachmentPath, oHTTPResponse);
        
        oJobDetails.downloadFileInfo = 'Executed request';

        if (oHTTPResponse == null) {
            oJobDetails.downloadFileInfo = 'Response null';
            return null;
        }
        try {
            aAttachmentBytes = oHTTPResponse.getMediaData();
        }
        catch (ex) {
            return null;
        } finally {
        	if (oHTTPClient){
        		oHTTPClient.close();
        	}
        	if( oHTTPResponse){
        	}
        }
    }
    // Process FTP
    else if (sAttachmentPath.indexOf('ftp://') >= 0 || sAttachmentPath.indexOf('ftps://') >= 0) {
        var sFTPPassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sAttachmentPassword);
        var sSFTPHost = sAttachmentPath.substring(sAttachmentPath.indexOf('://') + 3, sAttachmentPath.indexOf('/', 7));
        if (sSFTPHost.indexOf(":") > -1) {

            nSFTPPort = sSFTPHost.substring(sSFTPHost.indexOf(":") + 1);
            sSFTPHost = sSFTPHost.substring(0, sSFTPHost.indexOf(":"));
        }
        var sFTPFolder = sAttachmentPath.substring(sAttachmentPath.indexOf('/', 7) + 1, sAttachmentPath.lastIndexOf('/'));
        sAttachmentFileName = sAttachmentPath.substring(sAttachmentPath.lastIndexOf('/') + 1);
        if (bUseSFTP) {
            var oSFTPSession = null;

            /** @type{Packages.com.jcraft.jsch.ChannelSftp} */
            var oSFTPChannel = null;
            try {
                var jsch = new Packages.com.jcraft.jsch.JSch();
                if (!nSFTPPort) {
                    nSFTPPort = 21;
                }
                oSFTPSession = jsch.getSession(sAttachmentUsername, sSFTPHost, nSFTPPort);

                if (sSFTPHostKey) {
                    var hostKeyInputStream = new java.io.ByteArrayInputStream(new Packages.java.lang.String(sSFTPHostKey).getBytes());
                    jsch.setKnownHosts(hostKeyInputStream);
                }
                else {
                    oSFTPSession.setConfig("StrictHostKeyChecking", "no");
                }

                oSFTPSession.setPassword(sFTPPassword);
                oSFTPSession.connect();

                oSFTPChannel = new Packages.com.jcraft.jsch.ChannelSftp();

                oSFTPChannel = oSFTPSession.openChannel("sftp");
                oSFTPChannel.connect();

                if (sFTPFolder != undefined && sFTPFolder != '') {
                    try {
                        if (sFTPFolder.indexOf('\\') >= 0) {
                            sFTPFolder = utils.stringReplace(sFTPFolder, '\\', '/');
                        }
                        oSFTPChannel.cd(sFTPFolder);
                    }
                    catch (ex) {
                        throw new Error('No inbound folder exists for: ' + sFTPFolder);
                    }
                }
                var oFileInputReader = oSFTPChannel.get(sAttachmentFileName);
                aAttachmentBytes = Packages.org.apache.commons.io.IOUtils.toByteArray(oFileInputReader);
            }
            catch (ex) {
                throw new Error(ex.message);
            }
        }
        else {
            aAttachmentBytes = plugins.http.getMediaData(sAttachmentPath);
            if ( ( aAttachmentBytes == null || aAttachmentBytes.length < 1 ) && sAttachmentUsername != null && sAttachmentPassword != null) {
                // Create a client for the FTP connection
                var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
                try {
                    if (nSFTPPort) {
                        ftpClient.connect(sSFTPHost, nSFTPPort);
                    }
                    else {
                        ftpClient.connect(sSFTPHost, 21);
                    }
                    ftpClient.enterLocalPassiveMode();
                }
                catch (ex) {
                    throw new Error(ex.message);
                }

                var loginSuccess = ftpClient.login(sAttachmentUsername, sFTPPassword);
                if (loginSuccess) {
                    if (sFTPFolder != undefined && sFTPFolder != '') {
                        var inboundFolderExists = ftpClient.cwd(sFTPFolder);
                        if (inboundFolderExists < 200 || inboundFolderExists > 300) {
                            throw new Error('No inbound folder exists for: ' + sFTPFolder);
                        }
                        var ftpFile = sAttachmentPath.substring(sAttachmentPath.lastIndexOf('/') + 1);
                        var fileType = ftpFile.substring(ftpFile.lastIndexOf('.') + 1).toLowerCase();
                        if (fileType == 'txt' || fileType == 'text' || fileType == 'asp' || fileType == 'html' || fileType == 'shtml' || fileType == 'htm' || fileType == 'php') {
                            ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.ASCII_FILE_TYPE);
                        }
                        else {
                            ftpClient.setFileType(Packages.org.apache.commons.net.ftp.FTPClient.BINARY_FILE_TYPE);
                        }

                        var outputStream1 = new Packages.java.io.ByteArrayOutputStream();
                        ftpClient.retrieveFile(ftpFile, outputStream1);
                        aAttachmentBytes = outputStream1.toByteArray();
                        outputStream1.close();
                    }
                }
            }
        }
    }

	// Process File (network or local file)
	else {
		sAttachmentFileName = sAttachmentPath.substring(sAttachmentPath.lastIndexOf('\\')+1);

		// Added support for URL file formats like:
		// file:///C:/Users/<USER>/Box/Sturgeon/Sisense%20translations/wiki-DashboardSisenseTranslation.pdf
		// and
		// file://avsca-ltrink3/ftp/Xml/StoreData.XML
		var sFileURLBeginingTriple = 'file:///';
		var sFileURLBeginingDouble = 'file://';
		if (sAttachmentPath && sAttachmentPath.startsWith(sFileURLBeginingDouble)) {
			var nTripleIndex = sAttachmentPath.indexOf(sFileURLBeginingTriple);
			var nDoubleIndex = sAttachmentPath.indexOf(sFileURLBeginingDouble);
			if (nTripleIndex >= 0) {
				sAttachmentPath = sAttachmentPath.substring(sFileURLBeginingTriple.length);
			}
			else if (nDoubleIndex >= 0) {
				sAttachmentPath = sAttachmentPath.substring(sFileURLBeginingDouble.length);
				sAttachmentPath = '\\\\' + sAttachmentPath;
			}
			
			sAttachmentPath = scopes.avText.replaceAll(sAttachmentPath, '/', '\\');
			sAttachmentPath = unescapeSpacesOnly(sAttachmentPath);
			
			sAttachmentFileName = sAttachmentPath.substring(sAttachmentPath.lastIndexOf('\\')+1);
		}
	
		if (scopes.avUtils.doesFileExist(sAttachmentPath)) {
    		aAttachmentBytes = plugins.file.readFile(sAttachmentPath);
        }
        else {
        	returnObject.bFileCannotBeFoundOnServer = true;
        	returnObject.sDocumentFilePath = sAttachmentPath;
        }
	}
	
	returnObject.filePath = sAttachmentFileName;
	returnObject.byteArray = aAttachmentBytes;
	
	return returnObject;
}


/**
 * Get a file name from URL or from Http response
 * @param {String} sAttachmentPath
 * @param {plugins.http.Response} oHTTPResponse
 *
 * @return {String} file name never null
 * @properties={typeid:24,uuid:"3D60D3E7-14BF-4B08-A2F4-AD4713CA7D0C"}
 */
function getFileNameFromURLOrHTTPResponse (sAttachmentPath, oHTTPResponse) {
	var sAttachmentFileName = getFileNameFromResponseHeader(sAttachmentPath, oHTTPResponse);
    if (!sAttachmentFileName) {
    	var sFileName = getFilenameFrom(sAttachmentPath);
		if (sFileName) {
			sAttachmentFileName = sFileName;
		}
		else {
			sAttachmentFileName = getFileNameFromIncorrectURL(sAttachmentPath);
		}
    }
    // strip invalid characters from the file name
    sAttachmentFileName = sAttachmentFileName.replace(/[^a-zA-Z0-9-_\.]/g, '');
    
    return sAttachmentFileName;
}

/**
 * Returns file name if url doesn't contain proper file name
 * @param {String} sAttachmentPath url
 *
 * @return {String} file name otherwise null
 * @properties={typeid:24,uuid:"F4C57054-0C76-4BE6-8FD8-CC62B79ADD03"}
 */
function getFileNameFromIncorrectURL(sAttachmentPath) {
	var sAttachmentFileName = null;
	// SL-16265 Special case for University of Oregon. When there is no file name in URL we use DocumentID param value as a file name
	var documentIdParamValue = getParameterValue(sAttachmentPath, 'DocumentID');
	if (documentIdParamValue) {
		sAttachmentFileName = documentIdParamValue;
	} else {
		// if there is no filename in URL use hostname as a filename
		sAttachmentFileName = getHostname(sAttachmentPath);
	}

	return sAttachmentFileName;
}

/**
 * Escapes only space symbol in URL,when not full escape is necessary 
 * @param {String} sString
 *
 * @return {String}
 * @properties={typeid:24,uuid:"BCB62414-F283-40E8-B43F-2BFF7AE6CBD1"}
 */
function escapeSpacesOnly (sString) {
	if (!sString) {
		return null;
	}
	return utils.stringReplace(sString, ' ', '%20');
}


/**
 * Unescape spaces only
 * @param {String} sString
 *
 * @return {String}
 * @properties={typeid:24,uuid:"0EA9C4D9-F793-410E-9E03-CB25FD124652"}
 */
function unescapeSpacesOnly (sString) {
	if (!sString) {
		return null;
	}
	return utils.stringReplace(sString, '%20', ' ');
}


/**
 * @param {String} sAttachmentPath
 * @param {plugins.http.Response} oHTTPResponse
 *
 * @return {String} file name with extension or null if not defined
 * @properties={typeid:24,uuid:"066004A7-1345-41E0-A0FD-CB19C1BCD6FC"}
 */
function getFileNameFromResponseHeader(sAttachmentPath, oHTTPResponse) {

	var oHTTPClient = null;
	var newoHTTPResponse = null;
	if (!oHTTPResponse) {
		if (sAttachmentPath.indexOf('http://') >= 0 || sAttachmentPath.indexOf('https://') >= 0) {
	        
	        oHTTPClient = plugins.http.createNewHttpClient();
	        var oHTTPRequest = oHTTPClient.createGetRequest(escapeSpacesOnly(sAttachmentPath));
	        newoHTTPResponse = oHTTPRequest.executeRequest();
		}
		else {
			return null;
		}
	}
	
	var oContentHeaders = null;
	if (!oHTTPResponse) {
		if (newoHTTPResponse) {
			oContentHeaders = newoHTTPResponse.getResponseHeaders();
		}
	} else {
		oContentHeaders = oHTTPResponse.getResponseHeaders();
	}
	
	if (!oContentHeaders) {
		if (oHTTPClient) {
			oHTTPClient.close();
		}
		if (newoHTTPResponse) {
		}
		return null;
	}

	if (oHTTPClient) {
		oHTTPClient.close();
	}
	if (newoHTTPResponse) {
	}
	
	var sFileName = getFileNameFromContentDisposition(oContentHeaders);

	// if there is no extension in file name
	if (sFileName && sFileName.indexOf('.') < 0) {
		var sExtension = getFileExtensionByContentType(oContentHeaders);
		if (sExtension) {
			return sFileName + '.' + sExtension;
		}
		return sFileName;
	}
	
	return sFileName;
}



/**
 * Get a file extension from Content-Type Http response header if exists
 * @param {Object} oContentHeaders
 *
 * @return {String} file extension if exists otherwise null
 * @properties={typeid:24,uuid:"F5BB9582-F50C-4B09-8875-9036A0EDBB05"}
 */
function getFileExtensionByContentType(oContentHeaders) {
	var sExtension = null;
	/** @type {Array} */
	var oContentType = oContentHeaders['Content-Type'];
	if (oContentType && oContentType.length > 0) {
		for (var i = 0; i < oContentType.length; i++) {
			/** @type {String} */
			var sContentTypeValue = oContentType[i];
			if (sContentTypeValue.indexOf('/pdf') >= 0) {
				sExtension = 'pdf';
				break;
			} else if (sContentTypeValue.indexOf('/ps') >= 0) {
				sExtension = 'ps';
				break;
			} else if (sContentTypeValue.indexOf('/rdo') >= 0) {
				sExtension = 'rdo';
				break;
			} else if (sContentTypeValue.indexOf('/wfd') >= 0) {
				sExtension = 'wfd';
				break;
			} else if (sContentTypeValue.indexOf('/vps') >= 0) {
				sExtension = 'vps';
				break;
			}

		}
	}

	return sExtension;
}



/**
 * Get a filename from Content-Disposition Http response header if exists
 * @param {Object} oContentHeaders
 *
 * @return {String} string file name if exists otherwise null
 * @properties={typeid:24,uuid:"804E3F14-8675-4AD2-9749-BD56CEC27BC0"}
 */
function getFileNameFromContentDisposition(oContentHeaders) {
	var sFileName = null;

	/** @type {Array} */
	var oContentDisposition = getContentDispositionFromHeadetr(oContentHeaders);
	if (oContentDisposition && oContentDisposition.length > 0) {
		for (var j = 0; j < oContentDisposition.length; j++) {
			/** @type {String} */
			var sContentDispositionValue = oContentDisposition[j];
			var sFilenameTag = 'filename=';
			if (sContentDispositionValue.indexOf(sFilenameTag) >= 0) {
				sFileName = sContentDispositionValue.split(sFilenameTag)[1].split(';')[0];
				break;
			}
		}
	}

	return sFileName;
}

/**
 * Get Content Disposition header in different cases
 * @param {Object} oHeader
 *
 * @return {Array}
 * @properties={typeid:24,uuid:"FA71A973-FF7B-4214-950A-659F049BD4A4"}
 */
function getContentDispositionFromHeadetr (oHeader) {
	/** @type {Array} */
	var oContentDisposition = oHeader['Content-Disposition'];
	if (!oContentDisposition) {
		oContentDisposition = oHeader['content-disposition'];
		if (!oContentDisposition) {
			oContentDisposition = oHeader['Content-disposition'];
			if (!oContentDisposition) {
				oContentDisposition = oHeader['content-Disposition'];
				return oContentDisposition;
			}
		}
	}
	return oContentDisposition;
}



/**
 * Get file based on file path (could be HTTP, FTP or local file).
 * 
 * @param {String} sFilePath
 * @param {String} sAttachmentUsername
 * @param {String} sAttachmentPassword
 * @param {Boolean} [bIsSilent]
 * @return {plugins.file.JSFile}
 * @properties={typeid:24,uuid:"D5F74B47-85DB-4507-BC51-42DF3B36CCDE"}
 */
function getFileBasedOnPath(sFilePath, sAttachmentUsername, sAttachmentPassword, bIsSilent) {
	var jsFile;
	try {
		var oAttachmentObject = getBytesByPath(sFilePath, sAttachmentUsername, sAttachmentPassword, new Object());
		var aAttachmentBytes = oAttachmentObject.byteArray;
		var sAttachmentFilePath = oAttachmentObject.filePath;
			
		if(aAttachmentBytes == null || aAttachmentBytes.length < 1) {
			throw new Error('There was no data found at: ' + sFilePath);
		} 
		
		if(bIsSilent) {
			var sAttachmentFileName = sFilePath.substring(sFilePath.lastIndexOf('/')+1);
			jsFile = plugins.file.createFile(sAttachmentFileName);
			plugins.file.writeFile(jsFile,aAttachmentBytes);
		} else if(plugins.file.writeFile(sAttachmentFilePath, aAttachmentBytes)) {
			jsFile = plugins.file.convertToJSFile(sAttachmentFilePath);
		}	
	} catch (ex) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.fileNotFoundByPath') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
	}
	
	return jsFile;
}

/**
 * Get file path of file based on sFilePath (have to download if on FTP or is a HTTP link.
 * 
 * @param sFilePath
 * @param sAttachmentUsername
 * @param sAttachmentPassword
 *
 * @return
 * @properties={typeid:24,uuid:"EAB4D841-4EB1-4ADA-925C-DA04E9BF1F0E"}
 */
function getFilePath(sFilePath, sAttachmentUsername, sAttachmentPassword) {
	try {
		if(sFilePath.indexOf('http://') >= 0 || sFilePath.indexOf('https://') >= 0 || sFilePath.indexOf('ftp://') >= 0 || sFilePath.indexOf('ftps://') >= 0) {
			var oAttachmentObject = getBytesByPath(sFilePath, sAttachmentUsername, sAttachmentPassword, new Object());
			var aAttachmentBytes = oAttachmentObject.byteArray;
			var sAttachmentFilePath = oAttachmentObject.filePath;
		
			if(sAttachmentFilePath && aAttachmentBytes) {
				var sTo = globals.avDocs_userDownloadDirectory + "\\" + sAttachmentFilePath;
				var fos = new Packages.java.io.FileOutputStream(sTo);
				try {
					fos.write(aAttachmentBytes);
				} finally {
					fos.close();
				}
				return sTo;
			}
		}
	} catch (ex) {
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.filePathNotFound') + ex.message, i18n.getI18NMessage('avanti.dialog.okay'));
	}
	return sFilePath;
}

/**
 * Delete file from FTP Path
 * @param sAttachmentPath
 * @param sAttachmentUsername
 * @param sAttachmentPassword
 * @param {Number} nFtpPort
 *
 * @return {Boolean} success or failed
 * @properties={typeid:24,uuid:"4C29BBBE-7E97-488E-9F94-0150B57412A3"}
 */
function deleteFileFromFTP(sAttachmentPath, sAttachmentUsername, sAttachmentPassword, nFtpPort) {
	try {
		var aAttachmentBytes;
		if(sAttachmentPath.indexOf('ftp://') >= 0 || sAttachmentPath.indexOf('ftps://') >= 0) {
			aAttachmentBytes = plugins.http.getMediaData(sAttachmentPath);
			if((aAttachmentBytes == null || aAttachmentBytes.length < 1) && sAttachmentUsername != null && sAttachmentPassword != null) {
				var ftppassword = plugins.it2be_cryptor.BLOWFISHdecrypt(sAttachmentPassword);
				var ftphost =  sAttachmentPath.substring(sAttachmentPath.indexOf('://')+3,sAttachmentPath.indexOf('/', 7));
				var ftpfolder = sAttachmentPath.substring(sAttachmentPath.indexOf('/', 7)+1,sAttachmentPath.lastIndexOf('/'));
				/** @type {String} */
				
				// Create a client for the FTP connection
				var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
				try {
					if (!nFtpPort) {
						nFtpPort = 21;
					}
					ftpClient.connect(ftphost, nFtpPort);
					ftpClient.enterLocalPassiveMode();
				} catch (ex) {
					throw new Error(ex.message);
				}
				
				var loginSuccess = ftpClient.login(sAttachmentUsername, ftppassword);
				if (loginSuccess) {
					if(ftpfolder != undefined && ftpfolder != '') {
						var inboundFolderExists = ftpClient.cwd(ftpfolder);
						if(inboundFolderExists < 200 || inboundFolderExists > 300) {
							throw new Error('No inbound folder exists for: ' + ftpfolder);
						}
						var ftpFile = sAttachmentPath.substring(sAttachmentPath.lastIndexOf('/')+1);
						var bSuccess = ftpClient.deleteFile(ftpFile);
						return bSuccess;
					}
				}
			}
		}
	} catch (ex) {
		application.output('Did not purge file: ' + sAttachmentPath, LOGGINGLEVEL.WARNING);
	}
	return false;
}

/**
 * @public 
 * 
 * @param {Array} aArray
 * @param value
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"F1E11E81-AC03-45BD-9C1B-09E9903DD777"}
 */
function getNumTimesValueAppearsInArray(aArray, value){
	var nCount = 0;
	
	for(var i=0; i<aArray.length; i++){
		if(aArray[i] == value){
			nCount++;
		}
	}
	
	return nCount;
}

/**
 * @public 
 * 
 * @param {String} sSource
 * @param {String} sMsg
 * @param {String} [sCallStack]
 * @param {Number} [iPerformanceTime]
 *
 * @properties={typeid:24,uuid:"0820D330-494B-43CF-9CA7-BA827FAFE1AB"}
 */
function quickLog(sSource, sMsg, sCallStack, iPerformanceTime){
	globals.dbLog(sMsg, sSource, null, null, null, null, 'logging', 'Detail', null, null, sCallStack, iPerformanceTime);
}

/**
 * @param {Array} aArray
 * @param {Number} nLength
 * @param initVal
 * @param {Boolean} [bClearFirst]
 * 
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"68DF6449-EFC0-4787-84B8-A88A57D3C53C"}
 */
function initArray(aArray, nLength, initVal, bClearFirst){
	if(bClearFirst){
		aArray = [];
	}
	
	for(var i=0; i<nLength; i++){
		if(bClearFirst || i >= aArray.length){
			aArray.push(initVal);
		}
		else{
			aArray[i] = initVal;
		}
	}
	
	return aArray;
}

/**
 * @public 
 * 
 * @param {Boolean} [bRemoveCallingFunctionFromStack]
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"9FB4A97D-E0E2-43DD-B438-98D31BC24529"}
 */
function getCallStack(bRemoveCallingFunctionFromStack) {
	var e = new ServoyException;
	var sCallStack = e.getScriptStackTrace();
	var aCallStack = sCallStack.split('\n');
	var nFirstIdx = bRemoveCallingFunctionFromStack ? 2 : 1; // remove fst entry which is getCallStack()
	
	sCallStack = '';
	// remove fst entry which is getCallStack() 
	for (var i = nFirstIdx; i < aCallStack.length; i++) {
		if(aCallStack[i]){
			if(sCallStack != '') sCallStack += '\n';
			sCallStack += aCallStack[i];
		}
	}
	
	return sCallStack;
}

/**
 * @public 
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"4793A9B1-1D84-4C73-8A84-1F14A8C8D98E"}
 */
function getCallingFunction() {
	var e = new ServoyException;
	var sCallStack = e.getScriptStackTrace();
	var aCallStack = sCallStack.split('\n');
	var nCallingFunctionIdx = 2;
	var sCallingFunction = '';
	
	if (aCallStack.length >= nCallingFunctionIdx) {
		var sCallLine = aCallStack[nCallingFunctionIdx];
		var nOpeningBracket = sCallLine.indexOf("(");
		var nClosingBracket = sCallLine.indexOf(")");
		
		if (nOpeningBracket > -1 && nClosingBracket > nOpeningBracket) {
			sCallingFunction = sCallLine.slice(nOpeningBracket + 1, nClosingBracket);
		}
	}
	
	return sCallingFunction;
}

/**
 * Takes in a TimeStamp and converts it from the Server's timezone to the Organizations timezone.
 * 
 * @param {Date} dTimeStamp
 * @return {Date}
 * 
 * @properties={typeid:24,uuid:"873A7AFD-B7C3-441D-80EB-48AFDAF07465"}
 */
function convertTimeStampToOrgTZ(dTimeStamp){
	/** @type {Date} */
	var dOrgDateTime = new Date();
	
	if(dTimeStamp != null){
		var ldt = new Packages.java.time.LocalDateTime.of(dTimeStamp.getFullYear(), dTimeStamp.getMonth() + 1, dTimeStamp.getDate(), dTimeStamp.getHours(),dTimeStamp.getMinutes());
		
		var oSQL ={};		
		oSQL.server = globals.avBase_dbase_avanti;
	    oSQL.args = [];
	    
	    /** @type {JSDataSet} */
		var dsData = null;
		
	    oSQL.sql = "SELECT org_timezone_id FROM sys_organization WHERE org_id = ?";
	    oSQL.args = [globals.org_id.toString()];
		dsData = globals["avUtilities_sqlDataset"](oSQL);
	    	
		/**@type {String} */
		var sTimezoneID = null;
	    	
		if(dsData != null && dsData.getMaxRowIndex() == 1){
			sTimezoneID = dsData.getValue(1,1);
		}
		
		var zID_Org = null;
		var tzServerTimeZone = Packages.java.util.Calendar.getInstance().getTimeZone();
		var zID_Server = new Packages.java.time.ZoneId.of(tzServerTimeZone.getID());
		
		if(sTimezoneID != null && utils.stringTrim(sTimezoneID) != ""){
			zID_Org = new Packages.java.time.ZoneId.of(sTimezoneID);
		} else {
			zID_Org = new Packages.java.time.ZoneId.of(tzServerTimeZone.getID());
		}
		
		/** @type {*} */
		var zdt = ldt.atZone(zID_Server);
		var zdtOrg = zdt.withZoneSameInstant(zID_Org);
		
		dOrgDateTime = new Date();
		dOrgDateTime.setFullYear(dTimeStamp.getFullYear(),dTimeStamp.getMonth(),dTimeStamp.getDate());
		dOrgDateTime.setHours(zdtOrg.getHour(), zdtOrg.getMinute(),0);
		
	}
	
	return dOrgDateTime;
}



 /**
 * Write JS text to a file path and replace all end lines (CRLF) with Unix style end line (LF)
 *  
 * @param {String} sText  Text to write to file
 * @param {String} sFilePath File path to write text to.
 *
 * @properties={typeid:24,uuid:"9D3B0DA7-1E67-4233-A917-098D211BA61E"}
 */
function writeTextToFileWithUnixEndLine(sText, sFilePath) {
    var sUnixInvoiceLine = new java.lang.String(sText);
    sUnixInvoiceLine = sUnixInvoiceLine.replaceAll("\\r\\n", "\n");
    var os = new Packages.java.io.DataOutputStream(new Packages.java.io.FileOutputStream(sFilePath));
    os.write(new java.lang.String(sUnixInvoiceLine).getBytes());
}

/**
 * This utility method provides a way to attempt loading files when the underlying 
 * FTP server system type is unknown.  If an exception is thrown when listing the files we
 * try assuming the server is a UNIX based system.   If this fails it will continue
 * to barf as it did before.
 * 
 * The specific case we were trying to fix is SL-12819 where the client has a Mac FTP 
 * server.  In the case of MAC FTP-Servers they identify themselves to the FTPClient
 * as "MACOS Peter's Server" and the ftpClient does not know what to do with this.   
 * 
 * @param {Packages.org.apache.commons.net.ftp.FTPClient} ftpClient
 *
 * @return {Array}
 * 
 * @properties={typeid:24,uuid:"BC6FEF29-4AC0-4947-B058-5C4DDC1610C6"}
 */
function listFTPFiles(ftpClient) {

	var aList = []
	if (nFTPDataTimeOut && scopes.avMath.isInt(nFTPDataTimeOut)) {
		ftpClient.setDataTimeout(parseInt(nFTPDataTimeOut));
	}
	try {
		aList = ftpClient.listFiles();
	} catch (ex) {
		scopes.avUtils.devLog("SL-21955","listFTPFiles error: " + ex.message + ". Stack: " + ex.stack);
		try {
			var conf = new Packages.org.apache.commons.net.ftp.FTPClientConfig(Packages.org.apache.commons.net.ftp.FTPClientConfig.SYST_UNIX);
			ftpClient.configure(conf);
			aList = ftpClient.listFiles();
		} catch (e) {
			scopes.avUtils.devLog("SL-21955","listFTPFiles UNIX error: " + e.message + ". Stack: " + e.stack);
		}
	}

	return aList;
}


/**
 * sorts an array of numbers numerically, as opposed to default behaviour of array.sort() which does it alphabetically
 * 
 * @public 
 * 
 * @param {Array<Number>} aNums
 * 
 * @return {Array<Number>}
 *
 * @properties={typeid:24,uuid:"BD4A9C83-D8B8-443C-8406-AC741E08F605"}
 */
function sortNumericArray(aNums){
	return aNums.sort(function (a, b) {  return a - b;  });
}

/**
 * @public 
 * 
 * @param {String} sPrefName
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"27BEB7DB-D79D-4577-B507-813B88E9BBBA"}
 */
function getSysPrefNum(sPrefName){
	var sPrefValue = null;
	var nPrefNumber = scopes.avUtils.SYS_PREF[sPrefName];
	
	if(nPrefNumber){
		sPrefValue = globals.avBase_getSystemPreference_Number(nPrefNumber)
	}
	
	return sPrefValue;
}

/**
 * @public 
 * 
 * @param {String} sFormName
 * @param {String} sElementName
 * @param {Boolean} bEnabled
 * @param {String} [sBasedOnPrefName]
 * @param {String} [sSetJustEnabledOrEditable] - 'enabled' or 'editable'
 * @param {Boolean} [bGrayBkgnd]
 * @param {Boolean} [bDontChangeColor]
 *
 * @properties={typeid:24,uuid:"A0731364-CA6C-42C9-A601-6FBED0F16BBC"}
 */
function enableElement(sFormName, sElementName, bEnabled, sBasedOnPrefName, sSetJustEnabledOrEditable, bGrayBkgnd, bDontChangeColor) {
    if (sFormName && sElementName && forms[sFormName].elements[sElementName]) {
        if (sBasedOnPrefName) {
            bEnabled = getSysPrefNum(sBasedOnPrefName) == 1;
        }

        if (sSetJustEnabledOrEditable && sSetJustEnabledOrEditable.toLowerCase() == 'enabled') {
            forms[sFormName].elements[sElementName].enabled = bEnabled;
        }
        else if (sSetJustEnabledOrEditable && sSetJustEnabledOrEditable.toLowerCase() == 'editable') {
            forms[sFormName].elements[sElementName].enabled = bEnabled;
        }
        else {
            forms[sFormName].elements[sElementName].enabled = bEnabled;
        }

        if (!bDontChangeColor) {
            if (bEnabled) {
                scopes.globals["avUtilities_setStyleClass"](forms[sFormName].elements[sElementName], globals.ENUM_COLOR_MODE.enabled);
            }
            else {
                if (bGrayBkgnd) {
	                scopes.globals["avUtilities_setStyleClass"](forms[sFormName].elements[sElementName], globals.ENUM_COLOR_MODE.disabled);
                }
                else {
	                scopes.globals["avUtilities_setStyleClass"](forms[sFormName].elements[sElementName], globals.ENUM_COLOR_MODE.browse);
                }
            }
        }
    }
}

/**
 * @public 
 * 
 * @param {String} sFormName
 * @param {Boolean} bEnabled
 * @param {Array<String>} [asExceptThese]
 *
 * @properties={typeid:24,uuid:"C8708AF0-C24B-4EC8-8991-9DB432BA3F08"}
 */
function enableAllFormElements(sFormName, bEnabled, asExceptThese){
	if(sFormName && forms[sFormName]){
		for(var i=0; i<forms[sFormName].elements.length; i++){
			var bThisOneEnabled = bEnabled;
			
			if(asExceptThese.indexOf(forms[sFormName].elements[i].getName()) > -1){
				bThisOneEnabled = !bEnabled;
			}

			forms[sFormName].elements[i].enabled = bThisOneEnabled;
			
			if(bThisOneEnabled){
                scopes.globals["avUtilities_setStyleClass"](forms[sFormName].elements[i], globals.ENUM_COLOR_MODE.enabled);
			}
			else{
                scopes.globals["avUtilities_setStyleClass"](forms[sFormName].elements[i], globals.ENUM_COLOR_MODE.browse);
			}
		}
	}
}

/**
 * @public 
 * @param {Array} aArray
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"325C048F-B8CC-462F-BA7A-6F06F23612DA"}
 */
function areAllElementsTheSame(aArray){
	for(var i=1; i<aArray.length; i++){
		if(aArray[i] != aArray[0]){
			return false;
		}
	}
	
	return true;
}

/**
 * @public 
 * 
 * @param {Array} aArray
 * @param value
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"8917A858-D4FB-4025-A3E0-54B6DC005925"}
 */
function getNumElementsWithThisValue(aArray, value){
    var nNumMatches = 0;
    
    for(var i=0; i<aArray.length; i++){
        if(aArray[i] === value){
            nNumMatches++;
        }
    }
    
    return nNumMatches;
}

/**
 * Logs a message to the debug log with difference in time between nStartTime
 *
 * @param {string} sMessage - Message to log
 * @param {number} nStartTime - Start time in milliseconds 
 *
 * @properties={typeid:24,uuid:"EA0BE149-922B-4491-9BA9-04DCB1DDDF77"}
 */
function logDebugExecutionTime(sMessage, nStartTime)
{
	if (globals.avBase_getSystemPreference_Number(SYS_PREF.DevLoggingOn)) {
		var nEndTime = new Date().getTime();
		var nSeconds = (nEndTime - nStartTime) / 1000;
		application.output(sMessage + ' - Total Time: ' + nSeconds + ' (s)', LOGGINGLEVEL.DEBUG);
	}
	
}



/**
 * ****** WARNING: USE this with caution, you should always compare Foundset operation performance 
 * before using this with SQL as it could be slower sometimes. Also if you already know the primary keys of the data your are changing,
 * try to use plugins.rawSQL.notifyDataChange directly instead of this function
 * <p> 
 * Takes a session id and queries the sys_servoy_broadcast table to determine which records to broadcast to system
 * </P>
 * @param {String} sSessionID
 * @param {String} [sFoundsetTable] - pass in that table name you want back to load into a Foundset
 * @param {Boolean} [bClearDataBroadcastSessionID]
 *
 * @return {JSDataSet} - a dataset (with primarykeys of affected records) of the tablename you passed 
 * , you can use that dataset to load a foundset if needed.
 * 
 * @properties={typeid:24,uuid:"B3259652-1CC0-4D4E-B4F6-3BB47444037D"}
 */
function broadcastDataChanges(sSessionID, sFoundsetTable, bClearDataBroadcastSessionID) {
    
    /*** @type {JSDataSet} */
    var dsDataToReturn;
    
    if (plugins.UserManager.getSettingsProperty("servoy.disableDataChangeNotify") == "false" && sSessionID) {
        var bOk = false;
        var sMsg = "";
        
        sSessionID = sSessionID.toString();		

        var sSQL = "SELECT servoy_broadcast_sessionid, servoy_broadcast_tablename, servoy_broadcast_recordaction, COUNT(servoy_broadcast_recordprimarykey) 'RecordCount' \
                    FROM sys_servoy_broadcast \
                    WHERE servoy_broadcast_sessionid = ? \
                        AND org_id = ? \
                    GROUP BY servoy_broadcast_sessionid, servoy_broadcast_tablename, servoy_broadcast_recordaction, servoy_broadcast_order \
                ORDER BY servoy_broadcast_order";
        
        var aArgs = [sSessionID, globals.org_id.toString()];
        var dsBroadcast = databaseManager.getDataSetByQuery(scopes.globals.avBase_dbase_avanti, sSQL, aArgs, -1);

        var nDatasetSize = dsBroadcast.getMaxRowIndex();

        for (var i = nDatasetSize; i--;) {
            var sTableName = dsBroadcast.getValue(i + 1, 2);
            var sRecordAction = dsBroadcast.getValue(i + 1, 3);

            sSQL = "SELECT servoy_broadcast_recordprimarykey FROM sys_servoy_broadcast \
                        WHERE servoy_broadcast_sessionid = ? \
                                AND servoy_broadcast_tablename = ? \
                                AND servoy_broadcast_recordaction = ? \
                                AND org_id = ?";

            aArgs = [sSessionID, sTableName, sRecordAction, globals.org_id.toString()];

            var dsData = databaseManager.getDataSetByQuery(scopes.globals.avBase_dbase_avanti, sSQL, aArgs, -1);

            // Set the default value
            var satDataChangeAction = SQL_ACTION_TYPES.NO_ACTION;

            if (sRecordAction == scopes.avUtils.e_spBroadcastRecordAction.INSERT_ACTION) {
                satDataChangeAction = SQL_ACTION_TYPES.INSERT_ACTION;

            }
            else if (sRecordAction == scopes.avUtils.e_spBroadcastRecordAction.DELETE_ACTION) {
                satDataChangeAction = SQL_ACTION_TYPES.DELETE_ACTION;

            }
            else if (sRecordAction == scopes.avUtils.e_spBroadcastRecordAction.UPDATE_ACTION) {
                satDataChangeAction = SQL_ACTION_TYPES.UPDATE_ACTION;
            }
                        
            bOk = plugins.rawSQL.notifyDataChange(scopes.globals.avBase_dbase_avanti, sTableName, dsData, satDataChangeAction);
            databaseManager.refreshRecordFromDatabase(databaseManager.getFoundSet(scopes.globals.avBase_dbase_avanti, sTableName), -1);

            if (!bOk) {
                sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
                application.output('Error - notifyDataChange exception - sTableName: ' + sTableName + ', satDataChangeAction: ' + satDataChangeAction + ', sMsg: ' + sMsg, LOGGINGLEVEL.ERROR);
                plugins.dialogs.showErrorDialog('Error', 'notifyDataChange exception: ' + sMsg, 'Ok');
            }

            if (sTableName && sTableName == sFoundsetTable) {
                dsDataToReturn = dsData;
            }
        }
        
        // SL-13812 : If no records returned don't delete
        if (nDatasetSize > 0 && sSessionID && sSessionID.length > 0) {
            // Now Delete those Session records from Broadcast table
            var sQuery = "DELETE FROM sys_servoy_broadcast WHERE servoy_broadcast_sessionid = ? AND org_id = ?";
            bOk = plugins.rawSQL.executeSQL(scopes.globals.avBase_dbase_avanti, sQuery, [sSessionID, globals.org_id.toString()]);

            if (!bOk) {
                // If there is an error deleting records from the table,
                // the table has a CreatedDate column, a process should be put in place which will
                // delete all records from table older than a day
                sMsg = plugins.rawSQL.getException(); //see exception node for more info about the exception obj
                application.output('Error - sys_servoy_broadcast delete exception: ' + sMsg, LOGGINGLEVEL.ERROR);
            }
        }

    }
    
	if (bClearDataBroadcastSessionID) {
		scopes.avDB.uDataBroadcastSessionID = null;
	}

    return dsDataToReturn;
}

/**
 * @public 
 * 
 * @param {Array} a1
 * @param {Array} a2
 * @param {String} sSeparator
 * 
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"6B77F0B5-6715-4E73-B359-2A9CAF4D9B5A"}
 */
function mergeArrays(a1, a2, sSeparator){
    var aMerged = [];
    
    if(!sSeparator){
        sSeparator = '';
    }
    
    for(var i=0; i<a1.length; i++){
        if(i >= a2.length){
            break;
        }
        
        aMerged.push(a1[i] + sSeparator + a2[i]);
    }
    
    return aMerged;
}

/**
 * @param {Array} aArray
 * @param sFind
 * @param sReplace
 * 
 * @return {Array}
 *
 * @properties={typeid:24,uuid:"E25AC54A-F83C-4DB1-A426-8607CAC4F673"}
 */
function replaceArrayElement(aArray, sFind, sReplace){
    var idx = aArray.lastIndexOf(sFind);
    
    while(idx > -1){
        aArray[idx] = sReplace;
        idx = aArray.lastIndexOf(sFind);
    }
    
    return aArray;
}

/**
 * @param {Array<Array<String>>} aArrayOfArrays
 *
 * @return
 * @properties={typeid:24,uuid:"8BF4E7EA-F012-4C73-9FBC-57FA5FE891A8"}
 */
function getAllPermutationsOfArrays(aArrayOfArrays) {
    var r = [], max = aArrayOfArrays.length - 1;

    helper([], 0);

    return r;
    
    /** @param {Array} arr
     *   @param {Number} i */
    function helper(arr, i) {
        for (var j = 0, l = aArrayOfArrays[i].length; j < l; j++) {
            /** @type {Array} */
            var a = arr.slice(0); // clone arr

            a.push(aArrayOfArrays[i][j]);
            
            if (i == max) {
                r.push(a);
            }
            else {
                helper(a, i + 1);
            }
        }
    }
}

/**
 * @public
 * 
 * Returns true if the 2 arrays have any common elements
 * 
 * @param {Array} aArray1
 * @param {Array} aArray2
 *
 * @return
 * @properties={typeid:24,uuid:"05E8296B-DEF2-459E-BF15-0E21F2D14F3C"}
 */
function doArraysShareElements(aArray1, aArray2) {
    for (var i = 0; i < aArray2.length; i++) {
        if (aArray1.indexOf(aArray2[i]) > -1) {
            return true;
        }
    }

    return false;
}

/**
 * @public 
 * 
 * @param {String} sHost
 * @param {String} sUser
 * @param {String} sPwd
 * @param {String} sFolder
 * @param {Number} nPort
 * @param {Function} oFunction
 * @param {Array} aFunctionArgs
 * @param {Boolean} [bDecryptPwd]
 * @param {Boolean} [bDeleteFileAfterProcessing]
 *
 * @properties={typeid:24,uuid:"E159E661-4AF6-4460-ADAD-B1A29439B4A9"}
 */
function processFTPFiles(sHost, sUser, sPwd, sFolder, nPort, oFunction, aFunctionArgs, bDecryptPwd, bDeleteFileAfterProcessing) {
    try {
        // Create a client for the JFTP connection
        var ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
        
        ftpClient.setDefaultTimeout(scopes.avUtils.getFtpDefaultTimeout());
        
        if (nPort) {
            ftpClient.connect(sHost, nPort);
        }
        else {
            ftpClient.connect(sHost);
        }
        
        ftpClient.enterLocalPassiveMode();

        if (bDecryptPwd) {
            sPwd = plugins.it2be_cryptor.BLOWFISHdecrypt(sPwd);
        }
        
        var loginSuccess = ftpClient.login(sUser, sPwd);

        if (loginSuccess) {
            if (sFolder) {
                ftpClient.cwd(sFolder);
            }
        
            var aList = scopes.avUtils.listFTPFiles(ftpClient);

            if (aList != null) {
                for (var i = 0; i < aList.length; i++) {
                    /** @type{Packages.org.apache.commons.net.ftp.FTPFile} */
                    var oFile = aList[i];
                    
                    if (!oFile.isDirectory()) {
                        oFunction(oFile, ftpClient, aFunctionArgs);
                        
                        if (bDeleteFileAfterProcessing) {
                            try {
                                ftpClient.deleteFile(oFile.getName());
                            }
                            catch (ex) {
                                if (ftpClient && ftpClient.isConnected()) {
                                    ftpClient.disconnect();
                                }
                                
                                ftpClient = new Packages.org.apache.commons.net.ftp.FTPClient();
                                
                                ftpClient.setDefaultTimeout(scopes.avUtils.getFtpDefaultTimeout());
                                
                                if (nPort) {
                                    ftpClient.connect(sHost, nPort);
                                }
                                else {
                                    ftpClient.connect(sHost);
                                }
                                
                                ftpClient.enterLocalPassiveMode();
                                
                                var loginSuccess2 = ftpClient.login(sUser, sPwd);
                                
                                if (loginSuccess2) {
                                    if (sFolder) {
                                        ftpClient.cwd(sFolder);
                                    }
                                    
                                    ftpClient.deleteFile(oFile.getName());
                                }
                            }
                        }
                    }
                }
            }
        }
    } 
    catch (ex) {
        globals.dbLog('Error in processFTPFiles: ' + ex.message, 'processFTPFiles', null, 'report', 'ftp', globals.org_id, 'ftp', 'Summary', null, 'Error', ex.stack, null);
    }
    finally {
        if (ftpClient && ftpClient.isConnected()) {
            ftpClient.disconnect();
        }
    }
}

/**
 * @public 
 * 
 * @param {String} sHost
 * @param {String} sUser
 * @param {String} sPwd
 * @param {String} sFolder
 * @param {Number} nPort
 * @param {Function} oFunction
 * @param {Array} aFunctionArgs
 * @param {Boolean} [bDecryptPwd]
 * @param {Boolean} [bDeleteFileAfterProcessing]
 * @param {String} [sHostKey]
 *
 * @properties={typeid:24,uuid:"96E07323-F2D0-46A4-A43A-E2085864BFC6"}
 */
function processSFTPFiles(sHost, sUser, sPwd, sFolder, nPort, oFunction, aFunctionArgs, bDecryptPwd, bDeleteFileAfterProcessing, sHostKey) {
    try {
        var oJSch = new Packages.com.jcraft.jsch.JSch();
        var oSFTPSession;
        
        if (nPort) {
            oSFTPSession = oJSch.getSession(sUser, sHost, nPort);
        }
        else {
            oSFTPSession = oJSch.getSession(sUser, sHost);
        }

        if (sHostKey) {
            var oHostKeyInputStream = new java.io.ByteArrayInputStream(new Packages.java.lang.String(sHostKey).getBytes());
            oJSch.setKnownHosts(oHostKeyInputStream);
        }
        else {
            oSFTPSession.setConfig("StrictHostKeyChecking", "no");
        }
        
        if (bDecryptPwd) {
            sPwd = plugins.it2be_cryptor.BLOWFISHdecrypt(sPwd);
        }

        oSFTPSession.setPassword(sPwd);
        oSFTPSession.setTimeout(scopes.avUtils.getSFtpDefaultTimeout());
        oSFTPSession.connect();
        
        /** @type{Packages.com.jcraft.jsch.ChannelSftp} */
        var oSFTPChannel = new Packages.com.jcraft.jsch.ChannelSftp();
        oSFTPChannel = oSFTPSession.openChannel("sftp");
        oSFTPChannel.connect();

        if (sFolder) {
            try {
                if (sFolder.indexOf('\\') >= 0) {
                    sFolder = utils.stringReplace(sFolder, '\\', '/');
                }

                oSFTPChannel.cd(sFolder);
            }
            catch (ex) {
                globals.dbLog('No exists folder for: ' + sFolder, 'processSFTPFiles', null, 'report', 'ftp', globals.org_id, 'ftp', 'Summary', null, 'Error', null, null);
            }
        }

        var aFiles = oSFTPChannel.ls('*');
        
        if (aFiles != null) {
            for (var i = 0; i < aFiles.size(); i++) {
                /** @type{Packages.com.jcraft.jsch.ChannelSftp.LsEntry} */
                var oFile = aFiles.get(i);
                var oAttribs = oFile.getAttrs();
                
                if (!oAttribs.isDir()) {
                    oFunction(oFile, oSFTPChannel, aFunctionArgs, true);

                    if (bDeleteFileAfterProcessing) {
                        oSFTPChannel.rm(oFile.getFilename());
                    }
                }
            }
        }
    }
    catch (ex) {
        globals.dbLog('Error in processSFTPFiles: ' + ex.message, 'processSFTPFiles', null, 'report', 'ftp', globals.org_id, 'ftp', 'Summary', null, 'Error', null, null);
    }
    finally {
        if (oSFTPChannel) {
            oSFTPChannel.exit();
            oSFTPChannel.disconnect();
        }
        if (oSFTPSession) {
            oSFTPSession.disconnect();
        }
    }
}

/**
 * @public 
 * 
 * @param {String} sImportType
 * @param {String} sImportSubType
 * @param {String} sHost
 * @param {String} sUser
 * @param {String} sPwd
 * @param {String} [sFolder]
 * @param {Number} [nPort]
 * @param {Boolean} [bDeleteFileAfterProcessing]
 * @param {Number} [nReplaceExistingRecords]
 * @param {Number} [nUseSFTPClient]
 *
 * @properties={typeid:24,uuid:"8345CC44-8CEF-4365-9D99-25DC0A9BD91F"}
 */
function doDataImportFromFTP(sImportType, sImportSubType, sHost, sUser, sPwd, sFolder, nPort, bDeleteFileAfterProcessing, nReplaceExistingRecords, nUseSFTPClient) {
	if (nUseSFTPClient == 1) {
		processSFTPFiles(sHost, sUser, sPwd, sFolder, nPort, doFTPDataImport, [sImportType, sImportSubType, nReplaceExistingRecords], true, 
			bDeleteFileAfterProcessing);
	}
	else {
	    processFTPFiles(sHost, sUser, sPwd, sFolder, nPort, doFTPDataImport, [sImportType, sImportSubType, nReplaceExistingRecords], true, 
	    	bDeleteFileAfterProcessing);
	}
}

/**
 * @param {Packages.org.apache.commons.net.ftp.FTPFile|Packages.com.jcraft.jsch.ChannelSftp} oFile - fst type is for ftp, 2nd for sftp
 * @param {Packages.org.apache.commons.net.ftp.FTPClient|Packages.com.jcraft.jsch.ChannelSftp} ftpClient - fst type is for ftp, 2nd for sftp
 * @param {Array<String>} aDataImportParams
 * @param {Number} [bUseSFTPClient]
 *
 * @properties={typeid:24,uuid:"260C5DF7-036F-4E63-9BF3-461B6E79AB66"}
 */
function doFTPDataImport(oFile, ftpClient, aDataImportParams, bUseSFTPClient) {
    try {
        if (aDataImportParams.length > 0) {
            var outputStream1 = new Packages.java.io.ByteArrayOutputStream();
            var sFileName = bUseSFTPClient ? oFile.getFilename() : oFile.getName();
            
			if (bUseSFTPClient) {
				var oFileInputReader = ftpClient.get(sFileName);
				outputStream1 = Packages.org.apache.commons.io.IOUtils.toByteArray(oFileInputReader);
			}
			else {
				ftpClient.retrieveFile(sFileName, outputStream1);
			}
			
            if (outputStream1) {
                var sImportLines = bUseSFTPClient ? ByteArrayToString(outputStream1) : outputStream1.toString();
                
                // the file had a '\r' at the end of each line after downloading from ftp site, it wasnt there before uploading.
                // remove it as it causes problems in data import
                sImportLines = scopes.avText.replaceAll(sImportLines, '\r', '');
                var asImportLines = sImportLines.split('\n');
                
                if (asImportLines.length > 1) {
                    var sImportType = aDataImportParams[0];
                    var sImportSubType = aDataImportParams.length > 1 ? aDataImportParams[1] : null;
                    var nReplaceExistingRecords = aDataImportParams.length > 2 ? aDataImportParams[2] : null;
                    
                    forms['utils_dataImport_dtl'].doAutoImport(sImportType, sImportSubType, asImportLines, sFileName, nReplaceExistingRecords);
                }
            }
        }
    }
    catch (e) {
        globals.dbLog('Error in avUtils.doFTPDataImport(): ' + e.message, 'autoDataImport', null, 'report', 'ftp', globals.org_id, 'ftp', 'Summary', null, 'Error', null, null);
    }
    finally {
        if (outputStream1) {
            outputStream1.close();
        }
    }
}

/**
 * @public 
 * 
 * Converts a byte[] to String
 * 
 * @param {byte[]} bytes
 * @param {String} [encoding] Optional param to specify the encoding/chartset to use. See {@link scopes#svyIO#CHAR_SETS} for possible values. Default: scopes.svyIO.CHAR_SETS.UTF_8
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"C6D6F561-06F8-4B9A-AB70-115BABA9F5C1"}
 */
function ByteArrayToString(bytes, encoding) {
	return new java.lang.String(bytes, encoding|scopes.svyIO.CHAR_SETS.UTF_8).toString()
}

/**
 * @public 
 * 
 * @param {String} sImportType
 *
 * @return
 * @properties={typeid:24,uuid:"F9D458DF-8D4C-4197-B411-7191E2EFBB94"}
 */
function doesDataImportTypeUseSubType(sImportType){
    var aTypesWithSubTypes = ['Customers', 'Suppliers', 'Cost Centers', 'Inventory', 'CRM', 'Notes', 'UDF Answers', 'UDF Questions', 
        'Employees', 'Sales Orders (Historical)', 'Taxes'];
    
    if (aTypesWithSubTypes.indexOf(sImportType) > -1) {
        return true;
    }
    else {
        return false;
    }
}


/**
 * Returns whether or not the "Credit Note" tab in the "CRM Customers" program
 * is currently selected
 * 
 * @return {Boolean} -  true if it's selected, false otherwise
 * 
 * @properties={typeid:24,uuid:"75295481-3C3B-45E3-9DC8-8651AA065D2E"}
 */
function isCRMCustomers_CreditNoteTabSelected(){

    if (globals.nav.form_view_02 == "crm_customer_credit_note_tbl"
        && globals.nav.getTabProgram() == "CRM_Customer_Credit_Notes"
        && forms[globals.nav_program_name + '_tab'].elements['tabs']['tabIndex'] == 8) {

        return true;
    }
    else {
        return false;
    }

}

/**
 * @param {String} whse_id
 * @param {String} [org_id]
 *
 * @return
 * @properties={typeid:24,uuid:"3D96D51B-09D8-42F3-8841-CA050B385EA5"}
 */
function getWarehouseCode(whse_id, org_id) {
    if (whse_id) {
        if (!org_id) {
            org_id = scopes.globals.org_id;
        }
        var args = [whse_id.toString(), org_id];
        var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT whse_code FROM in_warehouse \
        WHERE whse_id = ? AND org_id = ?", args, 1);

        if (ds && ds.getMaxRowIndex() > 0) {
            return ds.getValue(1, 1);
        }
    }
    return null;
}


/**
 * Get Customer Code
 * 
 * @param {String} cust_id
 * @param {String} [org_id]
 *
 * @return
 * @properties={typeid:24,uuid:"F1BC98FF-5731-46F5-B507-9BEC44158A82"}
 */
function getCustomerCode(cust_id, org_id) {
    if(!org_id){
        org_id = scopes.globals.org_id;
    }
    var args = [cust_id, org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT cust_code FROM sa_customer \
        WHERE cust_id = ? AND org_id = ?"
        , args, 1);

    if (ds && ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * Get Item Code
 * 
 * @param {String} item_id
 * @param {String} [org_id]
 *
 * @return
 * @properties={typeid:24,uuid:"87F8897C-96E4-4949-814A-2F89B6FC3596"}
 */
function getItemCode(item_id, org_id) {
    if(!org_id){
        org_id = scopes.globals.org_id;
    }
    var args = [item_id, org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT item_code FROM in_item \
        WHERE item_id = ? AND org_id = ?"
        , args, 1);

    if (ds && ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * Get Item Class Code
 * 
 * @param {String} item_id
 * @param {String} [org_id]
 *
 * @return
 * @properties={typeid:24,uuid:"A14F50E0-58ED-4589-9A22-6FAF4801755F"}
 */
function getItemClassCode(item_id, org_id) {
    if(!org_id){
        org_id = scopes.globals.org_id;
    }
    var args = [item_id, org_id];
    var sSql = "SELECT itemclass_code FROM in_item_class \
                WHERE itemclass_id IN \
                (SELECT itemclass_id FROM in_item WHERE item_id = ? AND org_id = ?)";
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, sSql, args, 1);

    if (ds && ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * @param {String} worktype_id
 * @param {String} [org_id]
 *
 * @return
 * @properties={typeid:24,uuid:"50B12FF5-E0E5-488E-9AC9-ECC2E86F63B2"}
 */
function getWorkTypeCode(worktype_id, org_id) {
    if(!org_id){
        org_id = scopes.globals.org_id;
    }
    var args = [org_id, worktype_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT worktype_code \
				FROM sa_task_worktype WHERE org_id = ? and worktype_id= ?"
        , args, 1);

    if (ds && ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * Gets the server URL and ensures there is no trailing backslash
 *
 * <AUTHOR> Dotzlaw
 * @since May 8, 2019
 * @returns {String} The server path
 * @public
 *
 * @properties={typeid:24,uuid:"E65AA049-01BD-44C9-885A-FE8A7346F006"}
 */
function getServerURL() {
    
    var sURL = application.getServerURL();
    if (sURL.slice(sURL.length-1,sURL.length) == "/") {
        sURL = sURL.slice(0, sURL.length-1);
    }
    return sURL;
}

/**
 * @public - initializes the quick search to use sFormName
 * 
 * @param {String} sFormName
 *
 * @properties={typeid:24,uuid:"936D9C47-FBBA-4CD5-A3FB-3531B40AF246"}
 */
function initQuickSearch(sFormName) {
    globals.avBase_quickSearchCache.currentForm = null;
    globals.svy_nav_form_name = sFormName;
    globals.avBase_quickSearchCache[globals.svy_nav_form_name] = null;    
}

/**
 * @public 
 * 
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"59452C56-8917-49DD-8F13-7F34C3EBF2DF"}
 */
function isThisAvanti(){
    if (utils.hasRecords(_to_sec_owner$owner_id) && _to_sec_owner$owner_id.name == "Avanti Computer Systems") {
        return true;
    }
    else {
        return false;
    }
}

/**
 * This is a simplified version of devLog() for logging errors. 
 * It uses an error object, and it logs regardless of SYS_PREF.DevLoggingOn.
 * 
 * @public 
 * 
 * @param {String} sSource
 * @param {Object} ex - The error object (usually from Catch block) 
 * @param {UUID} [uObjectID]
 * @param {UUID} [uObjectID2]
 *
 * @properties={typeid:24,uuid:"BDBC7BEE-F286-4E6C-8E5B-686979C20AE0"}
 */
function devLogError(sSource, ex, uObjectID, uObjectID2) {
	try {
		devLog(sSource, ex["message"], false, uObjectID, uObjectID2, ex["stack"], null, true, true);
	}
	catch (e) {}
}

/**
 * @public 
 * 
 * @param {String} sTicket
 * @param {String} sInfo
 * @param {Boolean} [bCallStack]
 * @param {UUID} [uObjectID]
 * @param {UUID} [uObjectID2]
 * @param {String} [sCallStackOver]
 * @param {String} [sRunForThisCustOnly]
 * @param {Boolean} [bLogRegardlessOfPref]
 * @param {Boolean} [bIsError]
 * @param [binaryData]
 *
 * @properties={typeid:24,uuid:"23FBE14A-9787-4BF2-A157-6471B195B76E"}
 */
function devLog(sTicket, sInfo, bCallStack, uObjectID, uObjectID2, sCallStackOver, sRunForThisCustOnly, bLogRegardlessOfPref, bIsError, binaryData) {
	if (bLogRegardlessOfPref || application.isInDeveloper() || globals.avBase_getSystemPreference_Number(SYS_PREF.DevLoggingOn)) {
		var bProceed = true;
		
		if (sRunForThisCustOnly && _to_sys_organization.org_name.indexOf(sRunForThisCustOnly) == -1) {
			bProceed = false;
		}		
		
		if (bProceed) {
		    /**@type {JSRecord<db:/avanti/dev_log>} */
		    var rDevLog = scopes.avDB.newRecord('dev_log');

			// i've had a couple customer db's that were missing the seq_dev_log_dev_log_ie sequence, which resulted in an 'cant insert null into dev_log_id' error. i dont know if there is a
			// problem with the sequence not being restored when the backup is restored, but i added this just in case.
			if (!rDevLog.dev_log_id) {
				var uMaxID = scopes.avDB.SQLQuery("SELECT MAX(dev_log_id) FROM dev_log");
				
				if (uMaxID) {
					uMaxID++;
				}
				else {
					uMaxID = 1;
				}
				
				rDevLog.dev_log_id = uMaxID;
			}
		    
		    rDevLog.dev_log_ticket = sTicket;
		    rDevLog.dev_log_info = sInfo;
		    rDevLog.dev_log_sling_ver = scopes.avUtils.getVersionNumber();
		    rDevLog.dev_log_timestamp = application.getTimeStamp();
		    rDevLog.dev_log_is_error = bIsError ? 1 : 0;
		    
			if (binaryData) {
				rDevLog.dev_log_binary = binaryData;
			}
		    
		    // this could be any uuid in the system. EG. orderid, jobid, etc. then we can use it to join to other tables
			if (uObjectID) {
				rDevLog.dev_log_object_id = uObjectID;
			}
			if (uObjectID2) {
				rDevLog.dev_log_object_id_2 = uObjectID2;
			}

			if (sCallStackOver) {
		        rDevLog.dev_log_call_stack = sCallStackOver;
			}
			else if (bCallStack) {
		        rDevLog.dev_log_call_stack = scopes.avUtils.getCallStack(true);
		    }
		    
		    databaseManager.saveData(rDevLog);
		}
	}
}

/**
 * @public 
 * 
 * @param {String} sAddressType
 * @param {UUID} uRecordID
 * @param {String} sChangedColumn
 * @param {UUID} uChangedFromID
 * @param {UUID} uChangedToID
 *
 * @properties={typeid:24,uuid:"19BD2288-FE2B-487B-BBE7-7C365DC89538"}
 */
function logCommunicorpAddressChange(sAddressType, uRecordID, sChangedColumn, uChangedFromID, uChangedToID) {
    if (globals.org_name && globals.org_name.indexOf('Communicorp') > -1) {
        var sInfo = 'sAddressType=' + sAddressType + ', ' +
                    'uRecordID=' + uRecordID + ', ' +
                    'sChangedColumn=' + sChangedColumn + ', ' + 
                    'uChangedFromID=' + uChangedFromID + ', ' + 
                    'uChangedToID=' + uChangedToID;     
        
        scopes.avUtils.devLog('SL-17946', sInfo, true);
    }   
}

/**
 * @param {String} empl_id
 * @return {String}
 *
 *
 * @properties={typeid:24,uuid:"96C0DA07-3BAE-4F10-B978-99B02316DB0D"}
 */
function getEmplFullName(empl_id) {
    var args = [empl_id, scopes.globals.org_id];
    var ds = databaseManager.getDataSetByQuery(globals.avBase_dbase_avanti, "SELECT empl_full_name FROM sys_employee \
        WHERE empl_id=? AND org_id =?"
        , args, 1);

    if (ds.getMaxRowIndex() > 0) {
        return ds.getValue(1, 1);
    }
    return null;
}

/**
 * @public 
 * 
 * @param {String} swName
 * @param {Boolean} [bBypassDevCheck]
 *
 * @properties={typeid:24,uuid:"CC6CD66D-1D38-47CF-A97E-0FEAEA791B48"}
 */
function stopWatchStart(swName, bBypassDevCheck) {
	if (swName && (application.isInDeveloper() || bBypassDevCheck)) {
		var oStopWatch = aoStopWatches[swName];
		
		if (!oStopWatch) {
			oStopWatch = new scopes.avUtils.StopWatch();
		}

		oStopWatch.start(true);
		
		aoStopWatches[swName] = oStopWatch; 
	}
}

/**
 * @public 
 * 
 * @param {String} swName
 * @param {Boolean} [bBypassDevCheck]
 * 
 * @return {Number}
 *
 * @properties={typeid:24,uuid:"EFA49931-784C-401D-89B0-CB77E2F0A953"}
 */
function stopWatchStop(swName, bBypassDevCheck) {
	var nTime = null;
	
	if (swName && (application.isInDeveloper() || bBypassDevCheck)) {
		var oStopWatch = aoStopWatches[swName];
		
		if (oStopWatch) {
			nTime = oStopWatch.stop();
		}
	}
	
	return nTime;
}

/**
 * @public 
 * 
 * @param {String} sText
 *
 * @properties={typeid:24,uuid:"003CBC83-D82C-4BE8-8969-9AFDF73A9F24"}
 */
function devOutput(sText) {
	if (application.isInDeveloper()) {
		application.output(sText);
	}
}

/**
 * @public 
 * 
 * @param {String} swName
 * @param {Boolean} [bBypassDevCheck]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"B1FC74B7-02AB-48A9-A918-857198BEADC1"}
 */
function devOutputTime(swName, bBypassDevCheck) {
	var sTimeMsg = "";
	
	if (swName && (application.isInDeveloper() || bBypassDevCheck)) {
		var ms = stopWatchStop(swName, bBypassDevCheck);
		
		if (ms) {
			var s = scopes.avMath.roundNumber(ms / 1000, 2);
			var m = scopes.avMath.roundNumber(s / 60, 2);
			
			sTimeMsg = swName + ' TIME: ' + ms.toString() + "ms, " + s.toString() + "s, " + m.toString() + "m";

			application.output('################################################# ' + sTimeMsg);
		}
	}
	
	return sTimeMsg;
}

/**
 * 
 * @param value
 * @param mode
 *
 * @return
 * @properties={typeid:24,uuid:"6441FDFD-932B-46F9-A11A-EF0D81DEB825"}
 */
function removeUnsupportedCharacters(value, mode) {
	var result = '';

	if (value) {

		switch (mode) {
		case ENUM_REMOVE_UNSUPPORTED_CHARACTERS.KeepNumbers:
			result = value.replace(/[^0-9.]/g, "");
			break;
		}
	}

	return result;
}


/**
 * @public 
 * 
 * @param {String} sFilePath
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"0A9C17C3-4565-45A6-98D8-0AE21FD3636A"}
 */
function doesFileExist(sFilePath) {
	var bDoesFileExist = false;
	
	if (sFilePath) {
		var fsFile = plugins.file.convertToJSFile(sFilePath);
		
		if (fsFile && fsFile.exists()) {
			bDoesFileExist = true;
		}
	}
	
	return bDoesFileExist;
}

/**
 * @public 
 * 
 * @param {String} sFileName
 * @param {String} [sExt] - you can specify the sExt separately, if missing we will parse sFileName to find it 
 *
 * @return {plugins.file.JSFile}
 * 
 * @properties={typeid:24,uuid:"8EF43EFC-E986-407D-83E6-367E13947FBC"}
 */
function createTempFile(sFileName, sExt) {
	/**@type {plugins.file.JSFile} */
	var jsFile = null;
	
	if (sFileName) {
		if (!sExt) {
			sExt = getFiletExt(sFileName, true);
			sFileName = sFileName.substr(0, sFileName.length - sExt.length);
		}
		
		// createTempFile requires a separate ext param, it cant be included in in filename. and the ext has to include the '.'. 
		// Note: createTempFile appends a long random string to filename (presumably to ensure its always unique). eg MyFile8811561132067644343.csv.
		jsFile = plugins.file.createTempFile(sFileName, sExt);
	}
	
	return jsFile;
}

/**
 * @public 
 * 
 * @param {String} sFileName
 * @param {Boolean} [bIncludeDot]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"DD32719D-6F2C-4C3A-9FDB-CA5B2E0146CC"}
 */
function getFiletExt (sFileName, bIncludeDot) {
	var sExt = "";
	
	if (sFileName) {
		var iDotPos = sFileName.lastIndexOf(".");

		if (iDotPos > -1) {
			if (bIncludeDot) {
				sExt = sFileName.substr(iDotPos);
			}
			else {
				sExt = sFileName.substr(iDotPos + 1);
			}
		}
	}

	return sExt;
}

/**
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"31F226FE-729E-4D0C-8EB8-90360C3493CB"}
 */
function dialogUpdateExchangeRate() {
	return globals.DIALOGS.showQuestionDialog(i18n.getI18NMessage("avanti.dialog.updateExchangeRate_title"),
        i18n.getI18NMessage("avanti.dialog.updateExchangeRate_msg"),
        i18n.getI18NMessage("avanti.lbl.no"),
        i18n.getI18NMessage("avanti.lbl.yes"));
}

/**
 * 
 * @param {UUID} sCurrID
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"AB033464-78A9-472F-92EC-C2864B6A61ED"}
 */
function trackCurrencyExchange(sCurrID) {
	if (!_to_sys_organization.org_currency_exchange_feature_token || _to_sys_organization.org_currency_exchange_feature_token != 'TestPasswordLockSL8016') {
		return false;
	}
	
	if (sCurrID && scopes.globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.TrackCurrencyOnEstSOInv) == 1
			&& sCurrID != _to_sys_organization.org_default_curr_id) {
		return true;
	}
	return false;
}

/**
 * @public 
 * 
 * @param oObject
 * @param {String} sPath
 * 
 * @properties={typeid:24,uuid:"6B614338-C640-4E80-9A5A-4103F0B3EE8D"}
 */
function writeObjectToXMLFile (oObject, sPath) {
	if (oObject && sPath) {
	    var oFileOutputStream = new Packages.java.io.FileOutputStream(sPath);
	    var oXMLEncoder = new Packages.java.beans.XMLEncoder(oFileOutputStream);
	    
	    oXMLEncoder.writeObject(oObject);
	    oXMLEncoder.close();
	    oFileOutputStream.close();
	}
}

/**
 * @public 
 * 
 * @param oObject
 *
 * @return
 * @properties={typeid:24,uuid:"83308D11-571B-45D1-9AE2-B9BDE925B189"}
 */
function writeObjectToXML (oObject) {
	var sXML = "";
	
	if (oObject) {
		var jsf = plugins.file.createTempFile("aaa", "xml");
	    var oFileOutputStream = new Packages.java.io.FileOutputStream(jsf.getAbsolutePath());
	    var oXMLEncoder = new Packages.java.beans.XMLEncoder(oFileOutputStream);
	    
	    oXMLEncoder.writeObject(oObject);
	    oXMLEncoder.close();
	    oFileOutputStream.close();
	    
		sXML = plugins.file.readTXTFile(jsf.getAbsolutePath());	    
	}
	
	return sXML;
}

/**
 * @public 
 * 
 * @param oObject
 * @param {String} [sAddThisRootElement]
 * 
 * @return {plugins.XML.XMLDocument}
 *
 * @properties={typeid:24,uuid:"FF8D61C8-10D6-498C-B127-036906396EA7"}
 */
function convertObjectToXMLDocument (oObject, sAddThisRootElement) {
	/**@type {plugins.XML.XMLDocument} */
	var oXMLDocument = null;
	
	if (oObject) {
		var sXML = scopes.avUtils.convertObjectToXML(oObject);
		var oFile = plugins.file.createTempFile("aaa", ".xml");
		
		if (sAddThisRootElement) {
			sXML = "<" + sAddThisRootElement + ">" + sXML + "</" + sAddThisRootElement + ">";
		}
		
		plugins.file.writeTXTFile(oFile, sXML);		
		oXMLDocument = plugins.XML.XMLDocument([oFile.getAbsolutePath()]);
	}
	
	return oXMLDocument;
}

/**
 * @public 
 * 
 * @param {String} sXML
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"EE442D96-5D9C-4AEF-B2EF-DD07163ACEDB"}
 */
function convertXMLToJSON(sXML) {
	var sJSON = null;
	
	try {
		var oJSON = Packages.org.json.XML.toJSONObject(sXML);
		
		if (oJSON) {
			sJSON = plugins.serialize.toJSON(oJSON);
		}
	}
	catch (ex) {
		devOutput("Error in convertXMLToJSON: " + ex.message);
	}
	
	return sJSON;
}

/**
 * Converts database unit price to unit price which is shown to the user
 * @param {Number} nUomDecimalPrice
 * @param {Number} fUnitPrice
 *
 * @return {String} rounded and formatted unit price for showing to user
 * @properties={typeid:24,uuid:"295CB3A7-4404-416E-BC7F-3F193D9023A2"}
 */
function calculateUnitPrice(nUomDecimalPrice, fUnitPrice)
{
	var sFormat = null;
	var nDecimalPlaces = 2;
	if (nUomDecimalPrice && nUomDecimalPrice >= 0) {
		nDecimalPlaces = nUomDecimalPrice;
	}	

	sFormat = scopes.globals["avUtilities_ItemPriceDisplayFormat_WithCurrency"](nDecimalPlaces);
	
	// Avoid to have -0$ or -0.0$ as a Unit Price SL-21788 fix
	if (fUnitPrice < 0 &&
		fUnitPrice > -1 &&
		globals["avUtilities_roundNumber"](fUnitPrice, nDecimalPlaces) == 0) {
		return utils.numberFormat(-fUnitPrice, sFormat);
	}
		
	return utils.numberFormat(fUnitPrice, sFormat);

}


/**
 * Converts database unit price to unit price which is shown to the user
 * @param {Number} nUomDecimalPrice
 * @param {Number} fUnitPrice
 *
 * @return {String} rounded and formatted unit price for showing to user
 * @properties={typeid:24,uuid:"8F1E85BF-925E-40CA-8299-C6D0D5DBA46D"}
 */
function calculateUnitPriceWithoutFormat(nUomDecimalPrice, fUnitPrice)
{	
	var nDecimalPlaces = 2;
	if (nUomDecimalPrice && nUomDecimalPrice >= 0) {
		nDecimalPlaces = nUomDecimalPrice;
	}		
	
	// Avoid to have -0$ or -0.0$ as a Unit Price SL-21788 fix
	if (fUnitPrice < 0 &&
		fUnitPrice > -1 &&
		globals["avUtilities_roundNumber"](fUnitPrice, nDecimalPlaces) == 0) {
		return globals["avUtilities_roundNumber"](-fUnitPrice, nDecimalPlaces);
	}
		
	return globals["avUtilities_roundNumber"](fUnitPrice, nDecimalPlaces);
}


/**
 * This function is used to remove the invalid records created by users,
 * who close the browser tab while creating new records.
 * @properties={typeid:24,uuid:"04356D96-EBBC-435E-A61A-483C7E910FE2"}
 */
function deleteInvalidRecords() {
	try {
		var aWebClients = plugins.UserManager.getWebClients();

		var aLoggedInUsers = [];

		for (var index = 0; index < aWebClients.length; index++) {

			var webClient = aWebClients[index];
			if (webClient.userUid) {
				aLoggedInUsers.push(webClient.userUid);
			}
		}
		var oSQL = { };
		if (aLoggedInUsers.length > 0) {
			aLoggedInUsers = "'" + aLoggedInUsers.join("','") + "'";

			
			oSQL.args = [];
			oSQL.server = globals.avBase_dbase_framework;
			oSQL.sql = 'SELECT user_id \
				FROM sec_user_org \
				WHERE user_id IS NOT NULL AND organization_id IS NOT NULL AND user_org_id IN (' + aLoggedInUsers + ')';

			/**@type {JSDataSet} **/
			var dsData = globals["avUtilities_sqlDataset"](oSQL);

			if (dsData && dsData.getMaxRowIndex() > 0) {

				var aUserIds = dsData.getColumnAsArray(1);
				aUserIds = "'" + aUserIds.join("','") + "'";

				oSQL.server = globals.avBase_dbase_avanti;
				oSQL.sql = 'SELECT empl_id \
					FROM sys_employee \
					WHERE empl_active = 1 AND user_id IN (' + aUserIds + ')';

				/**@type {JSDataSet} **/
				var dsUData = globals["avUtilities_sqlDataset"](oSQL);

				if (dsUData && dsUData.getMaxRowIndex() > 0) {

					var aEmployees = dsUData.getColumnAsArray(1);
					aEmployees = "'" + aEmployees.join("','") + "'";
					
					oSQL.args = ['\*\* NEW \*\*']
					oSQL.server = globals.avBase_dbase_avanti;
					
					//packing slip
					oSQL.sql = "DELETE FROM sa_pack WHERE pack_doc_number LIKE ? AND created_by_id NOT IN (" + aEmployees + ") \
					AND NOT EXISTS (SELECT 1 FROM sa_pack_detail WHERE sa_pack_detail.pack_id = sa_pack.pack_id)";

					globals["avUtilities_sqlRaw"](oSQL);
					
					//invoices
					oSQL.sql = "DELETE FROM sa_invoice WHERE inv_number LIKE ? AND created_by_id NOT IN (" + aEmployees + ") \
					AND NOT EXISTS (SELECT 1 FROM sa_invoice_det WHERE sa_invoice_det.inv_id = sa_invoice.inv_id)";
					globals["avUtilities_sqlRaw"](oSQL);
					
					//crm leads
					oSQL.sql = "DELETE FROM crm_lead WHERE lead_number LIKE ? AND created_by_id NOT IN (" + aEmployees + ") \
					AND NOT EXISTS (SELECT 1 FROM crm_lead_stage_association WHERE crm_lead_stage_association.crm_lead_id = crm_lead.crm_lead_id)\
					AND NOT EXISTS (SELECT 1 FROM crm_lead_contact WHERE crm_lead_contact.crm_lead_id = crm_lead.crm_lead_id)";
					globals["avUtilities_sqlRaw"](oSQL);
					
					globals.dbLog('Successfully completed purging of **NEW** invalid records', 'monitoring_queue', null, null, null, null, 'logging', null, null, null, null, null);

				}
			}
		}
		else{
			
			oSQL.args = ['\*\* NEW \*\*']
			oSQL.server = globals.avBase_dbase_avanti;
			
			//packing slip
			oSQL.sql = "DELETE FROM sa_pack WHERE pack_doc_number LIKE ? \
			AND NOT EXISTS (SELECT 1 FROM sa_pack_detail WHERE sa_pack_detail.pack_id = sa_pack.pack_id)";

			globals["avUtilities_sqlRaw"](oSQL);
			
			//invoices
			oSQL.sql = "DELETE FROM sa_invoice WHERE inv_number LIKE ? \
			AND NOT EXISTS (SELECT 1 FROM sa_invoice_det WHERE sa_invoice_det.inv_id = sa_invoice.inv_id)";
			
			globals["avUtilities_sqlRaw"](oSQL);
			
			//crm leads
			oSQL.sql = "DELETE FROM crm_lead WHERE lead_number LIKE ? \
			AND NOT EXISTS (SELECT 1 FROM crm_lead_stage_association WHERE crm_lead_stage_association.crm_lead_id = crm_lead.crm_lead_id)\
			AND NOT EXISTS (SELECT 1 FROM crm_lead_contact WHERE crm_lead_contact.crm_lead_id = crm_lead.crm_lead_id)";
			
			globals["avUtilities_sqlRaw"](oSQL);
			
			globals.dbLog('Successfully completed purging of **NEW** invalid records', 'monitoring_queue', null, null, null, null, 'logging', null, null, null, null, null);
		}
	} catch (ex) {
		globals.dbLog('Could not purge **NEW** invalid records due to: ' + ex.message, 'monitoring_queue', null, null, null, null, 'logging', null, null, null, null, null);
	}

}

/**
 * @public 
 * 
 * @return {String}
 * 
 * @properties={typeid:24,uuid:"D24E5897-41A0-4E52-9705-A0EF83169AE8"}
 */
function getProgressBarHTML() {
	return "<html>\
				<head>"
				+ globals.sProgressBarScript + " \
				</head>\
				<style type='text/css' media='screen'>\
					#myProgress {\
					  width: 100%;\
					  background-color: grey;\
					}\
					#myBar {\
					  width: 0%;\
					  height: 20px;\
					  background-color: #4D82B8;\
					  text-align: center; \
					  line-height: 20px; \
					  color: white; \
					}\
				</style>\
				<body>\
					<div id='myProgress'>\
					  <div id='myBar'></div>\
					</div>\
				</body>\
			</html>";
}

/**
 * @public 
 * 
 * @param {String} sThisVal
 * @param {Array} aShowVals
 * @param {Array} aRealVals
 *
 * @return
 * @properties={typeid:24,uuid:"93E5E7AE-0C8D-4D03-ACE9-AA6FDA2ADE69"}
 */
function translate(sThisVal, aShowVals, aRealVals) {
	var retVal = null;
	var nIDX = aShowVals.indexOf(sThisVal);
	
	if (nIDX > -1 && aRealVals.length > nIDX) {
		retVal = aRealVals[nIDX];
	}
	
	return retVal;
}

/**
 * 
 *
 * @properties={typeid:24,uuid:"920F0582-3D56-4046-BAC9-C369C23857E6"}
 */
function setClientTimeZone() {
	
	plugins.WebClientUtils.executeClientSideJS("var tZone = Intl.DateTimeFormat().resolvedOptions().timeZone", onGetClientTimeZone, ['tZone'])
	
}


/**
 * 
 * @param timeZoneID
 *
 * @properties={typeid:24,uuid:"7B91E76F-2101-473A-A706-34FEE5F1A66A"}
 */
function onGetClientTimeZone(timeZoneID) {
	
	globals.avUserTimeZone = Packages.java.util.TimeZone.getTimeZone(timeZoneID);
	application.output("av_UserTimeZone: " + globals.avUserTimeZone,LOGGINGLEVEL.DEBUG);

}


/**
 * @return {Number}
 * @properties={typeid:24,uuid:"1CCCEE7F-AFAC-452B-9954-5B92585650A3"}
 */
function getFtpDefaultTimeout() {

	var nFTPDefaultTimeOut = application.getUserProperty('ftp.defaultTimeout');
	if (nFTPDefaultTimeOut && scopes.avMath.isInt(nFTPDefaultTimeOut)) {
		return parseInt(nFTPDefaultTimeOut);
	} else {
		return 20000;
	}

}

/**
 * @return
 * @properties={typeid:24,uuid:"AD89F6B3-02D2-4B8A-8EC2-A6A0903B5CD5"}
 */
function getSFtpDefaultTimeout() {

	var nFTPDefaultTimeOut = application.getUserProperty('sftp.defaultTimeout');
	if (nFTPDefaultTimeOut && scopes.avMath.isInt(nFTPDefaultTimeOut)) {
		return parseInt(nFTPDefaultTimeOut);
	} else {
		return 20000;
	}

}

/**
 * @public
 * 
 * @return {Boolean}
 *  
 * @properties={typeid:24,uuid:"5294CDF8-4126-4B44-A003-8BD4457AD4BC"}
 */
function canClientUseProgressBar() {
	var aValidClients = [APPLICATION_TYPES.NG_CLIENT, APPLICATION_TYPES.HEADLESS_CLIENT];
	
	if (aValidClients.indexOf(application.getApplicationType()) > -1) {
		return true;
	}
	else {
		return false;
	}
}

/**
 * @public 
 * 
 * @param {UUID} uBatchID
 * @param {String} [sBatchType]
 * @param {Boolean} [bGetLock]
 * 
 * @return {JSRecord<db:/avanti/sys_batch_job>}
 * 
 * @properties={typeid:24,uuid:"4E3E7498-DE9F-46B2-B00D-F763AB56FCA2"}
 */
function getBatchJob(uBatchID, sBatchType, bGetLock) {
	/**@type {JSRecord<db:/avanti/sys_batch_job>} */
	var rBatchJob = null;

	if (uBatchID) {
		var sSQL = "SELECT sys_batch_job_id FROM sys_batch_job WHERE org_id = ? AND sysbatch_fk_id = ?";
		var aArgs = [globals.org_id, uBatchID.toString()];
		
		if (sBatchType) {
			sSQL += " AND sysbatch_type = ?";
			aArgs.push(sBatchType);
		}
		
		if (bGetLock) {
			rBatchJob = scopes.avDB.getRecWithLock(sSQL, "sys_batch_job", scopes.avDB.FOUNDSET_LOCKS.BatchJob, aArgs);
		}
		else {
			rBatchJob = scopes.avDB.getRecFromSQL(sSQL, "sys_batch_job", aArgs);
		}
	}
	
	return rBatchJob;
}

/**
 * Returns whether a numeric flag is contained within a flagset. If each flag in a flagset is double the previous one (eg. 1, 2, 4, 8, 16, 32) 
 * then any sum can only be composed of 1 combination of flags. eg. 44 can only be composed of 4, 8 and 32, there are no other combinations 
 * that will add up to 40. this way we can hold many flags in a single flagset (eg. a database column).
 * 
 * @public 
 * 
 * @param {Number} nFlagset
 * @param {Number} nFlag
 * @param {Number} nMaxFlag
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"2F4B9639-44C1-4BE6-A90C-48F9A34CBF33"}
 */
function doesFlagsetContainFlag(nFlagset, nFlag, nMaxFlag) {
    if (nFlagset && nFlag && nMaxFlag) {
        // start at MAX, which is the highest flag in the set, and divide in half each time, to see if the next smallest flag is in the set
        for (var f = nMaxFlag; f > 0 && nFlagset > 0; f = f / 2) {
            // using the numbers in doubling sequence, if the remaining flag total is >= this flag, then the flag must be contained in the set
            if (nFlagset >= f) {
                if (f == nFlag) {
                    return true;
                }
                else {
                    nFlagset -= f;
                }
            }
        }
    }

    return false;
}

/**
 *  Base64 encode / decode
 *  
 *  I found this code at http://www.webtoolkit.info. I had tried to use avSales.encodeUserAndPassword() in avUPS.createToken(), but it was returning
 *  an encoded string with a line break in it, which wasnt happening when i used postman. because of the line break the authentication in the ups call failed. 
 *  This function returns the same string as postman.
 *  
 * @properties={typeid:35,uuid:"B1E35DE1-40A3-45C4-A2C5-8FA005B2BF90",variableType:-4}
 */
var Base64 = {
	// private property
	_keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",

	// public method for encoding
	encode: function(input) {
		var output = "";
		var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
		var i = 0;

		input = Base64._utf8_encode(input);

		while (i < input.length) {

			chr1 = input.charCodeAt(i++);
			chr2 = input.charCodeAt(i++);
			chr3 = input.charCodeAt(i++);

			enc1 = chr1 >> 2;
			enc2 = ( (chr1 & 3) << 4) | (chr2 >> 4);
			enc3 = ( (chr2 & 15) << 2) | (chr3 >> 6);
			enc4 = chr3 & 63;

			if (isNaN(chr2)) {
				enc3 = enc4 = 64;
			}
			else if (isNaN(chr3)) {
				enc4 = 64;
			}

			output = output + this._keyStr.charAt(enc1) + this._keyStr.charAt(enc2) + this._keyStr.charAt(enc3) + this._keyStr.charAt(enc4);
		}
		return output;
	},

	// public method for decoding
	decode: function(input) {
		var output = "";
		var chr1, chr2, chr3;
		var enc1, enc2, enc3, enc4;
		var i = 0;

		input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");

		while (i < input.length) {

			enc1 = this._keyStr.indexOf(input.charAt(i++));
			enc2 = this._keyStr.indexOf(input.charAt(i++));
			enc3 = this._keyStr.indexOf(input.charAt(i++));
			enc4 = this._keyStr.indexOf(input.charAt(i++));

			chr1 = (enc1 << 2) | (enc2 >> 4);
			chr2 = ( (enc2 & 15) << 4) | (enc3 >> 2);
			chr3 = ( (enc3 & 3) << 6) | enc4;

			output = output + String.fromCharCode(chr1);

			if (enc3 != 64) {
				output = output + String.fromCharCode(chr2);
			}
			if (enc4 != 64) {
				output = output + String.fromCharCode(chr3);
			}
		}

		output = Base64._utf8_decode(output);

		return output;
	},

	// private method for UTF-8 encoding
	_utf8_encode: function(string) {
		string = string.replace(/\r\n/g, "\n");
		var utftext = "";

		for (var n = 0; n < string.length; n++) {

			var c = string.charCodeAt(n);

			if (c < 128) {
				utftext += String.fromCharCode(c);
			}
			else if ( (c > 127) && (c < 2048)) {
				utftext += String.fromCharCode( (c >> 6) | 192);
				utftext += String.fromCharCode( (c & 63) | 128);
			}
			else {
				utftext += String.fromCharCode( (c >> 12) | 224);
				utftext += String.fromCharCode( ( (c >> 6) & 63) | 128);
				utftext += String.fromCharCode( (c & 63) | 128);
			}
		}
		return utftext;
	},

	// private method for UTF-8 decoding
	_utf8_decode: function(utftext) {
		var string = "";
		var i = 0;
		var c = 0;
		var c2 = 0;
		var c3 = 0;

		while (i < utftext.length) {

			c = utftext.charCodeAt(i);

			if (c < 128) {
				string += String.fromCharCode(c);
				i++;
			}
			else if ( (c > 191) && (c < 224)) {
				c2 = utftext.charCodeAt(i + 1);
				string += String.fromCharCode( ( (c & 31) << 6) | (c2 & 63));
				i += 2;
			}
			else {
				c2 = utftext.charCodeAt(i + 1);
				c3 = utftext.charCodeAt(i + 2);
				string += String.fromCharCode( ( (c & 15) << 12) | ( (c2 & 63) << 6) | (c3 & 63));
				i += 3;
			}
		}
		return string;
	}
}

/**
 * @public 
 * 
 * @param {plugins.XML.XMLDocument} oXMLDocument
 *
 * @return
 * @properties={typeid:24,uuid:"6802768B-5E07-42B3-93F2-84E404D100A9"}
 */
function ucaseStartOfEachXMLElement(oXMLDocument) {
	var aElements = oXMLDocument.getElements();
	
	for(var i=0; i<aElements.length; i++){
		var e = aElements[i];
		
		e.name = e.name.charAt(0).toUpperCase() + e.name.slice(1);
	}
	
	return oXMLDocument;
}

/**
 * @param {Number} nPCT
 *
 * @return
 * @properties={typeid:24,uuid:"1F969367-06FE-49A3-BB37-9A7BFE6A9A3F"}
 */
function getStaticProgressBar(nPCT) {
	var nProgressBarPCT = nPCT > 100 ? 100 : nPCT;
	var nProgressBarColor = nPCT > 100 ? globals.COLOR.yellow : "#4D82B8";
	var sProgressName = "myProgress" + application.getUUID();
	var sBarName = "myBar" + application.getUUID();
	var sHTML = "<html>\
					<style type='text/css' media='screen'>\
						#" + sProgressName + "{\
						  width: 90%;\
						  height: 80%;\
						  background-color: white;\
						  border:1px solid black;\
						  border-style: bevel;\
						  margin:auto;\
						}\
						#" + sBarName + " {\
						  width: 90%;\
						  height: 100%;\
						  background-color: " + nProgressBarColor + ";\
						  text-align: center; \
						  line-height: 20px; \
						  color: black; \
						}\
					</style>\
					<body>\
						<div id='" + sProgressName + "'>\
						  <div id='" + sBarName + "' style='width:" + nProgressBarPCT + "%'>" + nPCT + "%</div>\
						</div>\
					</body>\
				</html>";
	
	return sHTML;
}

/**
 * Create data dictionary
 * 
 * @param {String} sFormName
 * 
 * @properties={typeid:24,uuid:"1C99D6C5-A290-4A1E-B32F-1F41AB3E6AD8"}
 */
function createDataDictionary(sFormName) {
	if (sFormName) {
		var sView = "";
		scopes.avDB.deleteRecs("tmp_data_dictionary",["session_id"],[globals.av_CurrentUser_SessionUUID]);
		globals.avBase_dataDictionary_sequence = 0;
		
		createDataDictionaryData(sFormName);
		
		// Process Dialogs 
		switch (sFormName) {
			case "sa_order_dtl":
				createDataDictionaryData("sa_order_multi_ship", i18n.getI18NMessage("avanti.dialog.multiShip"));
				createDataDictionaryData("sa_order_revision_detail_dlg", i18n.getI18NMessage("avanti.lbl.lineDetails"));
				createDataDictionaryData("sa_order_revision_detail_section_dlg", i18n.getI18NMessage("avanti.lbl.sectionDetails"));
				break;
			case "in_item_dtl":
				createDataDictionaryData("in_item_warehouse_dtl", i18n.getI18NMessage("avanti.program.itemWarehouse"));
				break;
			case "po_purchase_dtl":
				createDataDictionaryData("po_purchase_detail_qty", i18n.getI18NMessage("avanti.lbl.quantityDetails"));
				createDataDictionaryData("po_purchase_landed_cost",i18n.getI18NMessage("avanti.lbl.LandedCost"));
				createDataDictionaryData("po_purchase_detail_items_dlg",  i18n.getI18NMessage("avanti.lbl.lineDetails")); 
				break;
			case "sa_invoice_dtl":
				createDataDictionaryData("sa_invoice_postage_dialog",i18n.getI18NMessage("avanti.lbl.postageDetails"));
				createDataDictionaryData("sa_invoice_freight_dialog",i18n.getI18NMessage("avanti.lbl.freightDetails"));
				createDataDictionaryData("sa_invoice_tax_detail_dtl",i18n.getI18NMessage("avanti.lbl.salesTaxDetails"));
				createDataDictionaryData("sa_invoice_tender_trans_dlg",i18n.getI18NMessage("avanti.lbl.transactions"));
				createDataDictionaryData("sa_invoice_advance_billing_dialog",i18n.getI18NMessage("avanti.dialog.advanceBillingDetails_title"));
				break;
			case "sa_pack_dtl":
				createDataDictionaryData("sa_pack_detail_info_dlg",i18n.getI18NMessage("avanti.lbl.lineDetails"));
				createDataDictionaryData("sa_pack_detail_dtl",i18n.getI18NMessage("avanti.lbl.BoxLocationDetails"));
				break;
			
		default:
			break;
		}
		
		databaseManager.saveData();
		
		var jsForm = solutionModel.getForm(sFormName);
		if (jsForm) {
			switch (jsForm.view) {
			case 0:
				sView = "DetailView";
				break;
			case 3:
				sView = "TableView";
				break;
			
			default:
				sView = "UnknownView_" + jsForm.view;
				break;
			}
		}
		
		exportDataDictionaryToCSV(sView);
	}
}

/**
 * create data dictionary form data
 * 
 * @param {String} sFormName
 * @param {String} [sDialogName]
 *
 * @properties={typeid:24,uuid:"44DE38B1-AEB6-4C9C-8D78-9227C2823CFC"}
 */
function createDataDictionaryData(sFormName, sDialogName) {
    if (!sFormName || (sFormName && sFormName == "utils_quickSearch")) {
        return;
    }
    
    var sCurrentProgram = globals.nav_program_name;
    
    if (!sDialogName) {
        sDialogName = null;
    }
    
    var sTypeDescription = "";

    // Make sure we can access the form
    if (!forms[sFormName]) {
        application.output("Cannot access form: " + sFormName, LOGGINGLEVEL.ERROR);
        return;
    }
    
    var jsForm = solutionModel.getForm(sFormName);
    if (!jsForm) {
        application.output("Cannot get solution model for form: " + sFormName, LOGGINGLEVEL.ERROR);
        return;
    }
    
    // Track processed forms to avoid recursion loops
    if (!aProcessedForms) {
    	aProcessedForms = [];
    }
    
    // Skip if already processed
    if (aProcessedForms.indexOf(sFormName) !== -1) {
        return;
    }
    
    // Add to processed forms list
    aProcessedForms.push(sFormName);
    
    // Get all components from the form
    var components = jsForm.getComponents();
    
    // Create maps to store field and label information
    var fieldMap = {};
    var labelMap = {};
    
    // First pass: collect all labels and fields
    for (var i = 0; i < components.length; i++) {
        var component = components[i];
        if (component instanceof JSWebComponent) {
            try {
                var typeName = component.typeName;
                var componentName = component.name;
                
                if (typeName === 'bootstrapcomponents-label') {
                    try {
                        var labelFor = component.getJSONProperty('labelFor');
                        var labelText = component.getJSONProperty('text');
                        
                        if (labelFor) {
                            labelMap[labelFor] = {
                                text: labelText,
                                name: componentName
                            };
                        }
                    } catch (e) {
                        // Silent error
                    }
                } 
                else if (typeName.indexOf('bootstrapcomponents-') === 0 && 
                         typeName !== 'bootstrapcomponents-label' && 
                         typeName !== 'bootstrapcomponents-tabpanel') {
                    try {
                        var dataProvider = component.getJSONProperty('dataProvider');
                        if (dataProvider) {
                            fieldMap[componentName] = {
                                dataProvider: dataProvider,
                                type: typeName,
                                component: component
                            };
                        }
                    } catch (e) {
                        // Silent error
                    }
                }
            } catch (e) {
                application.output("Error processing component: " + e.toString(), LOGGINGLEVEL.ERROR);
            }
        }
    }
    
    /** @type {JSFoundset<db:/avanti/tmp_data_dictionary>} */ 
    var fsDataDictionary = databaseManager.getFoundSet(globals.avBase_dbase_avanti, "tmp_data_dictionary");
    
    /** @type {JSRecord<db:/avanti/tmp_data_dictionary>} */ 
    var rDataDictionary;
    
    // Process program entry once
    if (globals.avBase_dataDictionary_sequence == 0) {
        //Program
        /** @type {JSRecord<db:/svy_framework/nav_program>} */
        var rProgram = scopes.avDB.getRec("nav_program", ["program_name"], [sCurrentProgram], null, null, null, globals.nav_db_framework);
        if (rProgram) {
            var sProgramDesc = rProgram.description;
            if (sProgramDesc && sProgramDesc.indexOf("i18n") >= 0) {
                sProgramDesc = i18n.getI18NMessage(sProgramDesc);
            }
            
            globals.avBase_dataDictionary_sequence += 1;
            rDataDictionary = fsDataDictionary.getRecord(fsDataDictionary.newRecord(false));
            rDataDictionary.dd_sequence = globals.avBase_dataDictionary_sequence;
            rDataDictionary.session_id = globals.av_CurrentUser_SessionUUID;
            rDataDictionary.dd_element_type = 'Program';
            rDataDictionary.dd_element_label = sProgramDesc;
            rDataDictionary.dd_element_datasource = (rProgram != null ? rProgram.table_name : null);
        }
    }
    
    // Form entry
    globals.avBase_dataDictionary_sequence += 1;
    rDataDictionary = fsDataDictionary.getRecord(fsDataDictionary.newRecord(false));
    rDataDictionary.dd_sequence = globals.avBase_dataDictionary_sequence;
    rDataDictionary.session_id = globals.av_CurrentUser_SessionUUID;
    rDataDictionary.dd_element_type = (sDialogName !== null ? "Dialog" : "Form");
    rDataDictionary.dd_element_label = (sDialogName !== null ? sDialogName : sFormName);
    rDataDictionary.dd_element_datasource = getDataSource(jsForm.dataSource);
    
    // Check if this form has a grid component and process it
    if (forms[sFormName].elements && forms[sFormName].elements.grid) {
        processDataGrid(sFormName, jsForm.dataSource, fsDataDictionary);
    }
    
    // Second pass: process fields and create dictionary entries
    for (var fieldName in fieldMap) {
        var field = fieldMap[fieldName];
        dataProvider = field.dataProvider;
        var componentType = field.type;
        var labelInfo = labelMap[fieldName];
        labelText = "";
        
        // Get the label text if available
        if (labelInfo && labelInfo.text) {
            if (labelInfo.text.indexOf("i18n") >= 0) {
                labelText = i18n.getI18NMessage(labelInfo.text);
            } else {
                labelText = labelInfo.text;
            }
        } else {
            // Fall back to field name if no label found
            labelText = fieldName;
        }
        
        // Map the bootstrap component type to a field type description
        sTypeDescription = mapBootstrapComponentType(componentType);
        
        // Create dictionary entry for field
        globals.avBase_dataDictionary_sequence += 1;
        rDataDictionary = fsDataDictionary.getRecord(fsDataDictionary.newRecord(false));
        rDataDictionary.dd_sequence = globals.avBase_dataDictionary_sequence;
        rDataDictionary.session_id = globals.av_CurrentUser_SessionUUID;
        rDataDictionary.dd_element_type = sTypeDescription;
        rDataDictionary.dd_element_label = removeFormatting(labelText);
        rDataDictionary.dd_element_datasource = dataProvider;
        
        // Flag PII fields
        if (rDataDictionary.dd_element_label && (
                rDataDictionary.dd_element_label.toLowerCase().indexOf("email") > 0 ||
                rDataDictionary.dd_element_label.toLowerCase().indexOf("first name") > 0 ||
                rDataDictionary.dd_element_label.toLowerCase().indexOf("full name") > 0)) {
            rDataDictionary.dd_pii_flag = 1;
        }
    }
    
    // Process child containers using the established pattern
    var childContainers = collectChildContainers(jsForm, sFormName);
    processChildContainers(sFormName, childContainers);
    
    /**
     * Process data grid columns for the data dictionary
     * 
     * @param {String} formName - The form containing the grid
     * @param {String} formDatasource - The datasource of the form
     * @param {JSFoundset<db:/avanti/tmp_data_dictionary>} dataFoundset - Data dictionary foundset
     */
    function processDataGrid(formName, formDatasource, dataFoundset) {
        try {
            application.output("Processing data grid component in " + formName, LOGGINGLEVEL.INFO);
            
            var jsFormGrid = solutionModel.getForm(formName);
            
            if (!forms[formName] || !forms[formName].elements || !forms[formName].elements.grid) {
                application.output("Grid not found in form: " + formName, LOGGINGLEVEL.ERROR);
                return;
            }
            
            /**@type {JSWebComponent} **/
            var jsGrid = jsFormGrid.getWebComponent('grid');
            
            // Get current grid state (which columns are hidden, etc.)
            var gridState = null;
            try {
                var stateString = forms[formName].elements.grid.getColumnState();
                gridState = JSON.parse(stateString);
            } catch (stateError) {
                application.output("Error parsing grid state: " + stateError, LOGGINGLEVEL.ERROR);
            }
            
            // Create a map of column visibility based on runtime state
            var columnVisibility = {};
            if (gridState && gridState.columnState) {
                for (var j = 0; j < gridState.columnState.length; j++) {
                    var stateColumn = gridState.columnState[j];
                    if (stateColumn && typeof stateColumn.colId !== 'undefined') {
                        columnVisibility[stateColumn.colId] = !stateColumn.hide;
                    }
                }
            }
            
            if (jsGrid) {
                var validColumnsCount = 0;
                
                /** @type {Array} */
                var aGridColumns = jsGrid.getJSONProperty('columns');
                
                // Create a "Grid" section entry
                globals.avBase_dataDictionary_sequence += 1;
                var rGridSection = dataFoundset.getRecord(dataFoundset.newRecord(false));
                rGridSection.dd_sequence = globals.avBase_dataDictionary_sequence;
                rGridSection.session_id = globals.av_CurrentUser_SessionUUID;
                rGridSection.dd_element_type = "Grid";
                rGridSection.dd_element_label = "Data Grid Columns";
                rGridSection.dd_element_datasource = formDatasource;
                
                for (var i = 0; i < aGridColumns.length; i++) {
                    var column = aGridColumns[i];
                    
                    // Skip columns without dataprovider
                    if (!column.dataprovider) {
                        continue;
                    }
                    
                    // Skip configured hidden columns
                    if (column.visible === false) {
                        continue;
                    }
                    
                    // Skip runtime hidden columns (check grid state)
                    var columnId = column.dataprovider;
                    if (typeof columnVisibility[columnId] !== 'undefined' && columnVisibility[columnId] === false) {
                        continue;
                    }
                    
                    // Get the header title (handle i18n if needed)
                    var headerTitle = column.headerTitle || columnId;
                    if (headerTitle && headerTitle.indexOf('i18n:') === 0) {
                        headerTitle = i18n.getI18NMessage(headerTitle.substring(5));
                    }
                    
                    // Create dictionary entry for grid column
                    globals.avBase_dataDictionary_sequence += 1;
                    var rGridColumn = dataFoundset.getRecord(dataFoundset.newRecord(false));
                    rGridColumn.dd_sequence = globals.avBase_dataDictionary_sequence;
                    rGridColumn.session_id = globals.av_CurrentUser_SessionUUID;
                    rGridColumn.dd_element_type = "Grid Column";
                    rGridColumn.dd_element_label = headerTitle;
                    rGridColumn.dd_element_datasource = column.dataprovider;
                    
                    // Flag PII fields
                    if (headerTitle && (
                            headerTitle.toLowerCase().indexOf("email") > 0 ||
                            headerTitle.toLowerCase().indexOf("first name") > 0 ||
                            headerTitle.toLowerCase().indexOf("full name") > 0)) {
                        rGridColumn.dd_pii_flag = 1;
                    }
                    
                    validColumnsCount++;
                }
                
                application.output("Added " + validColumnsCount + " grid columns to data dictionary", LOGGINGLEVEL.INFO);
            } else {
                application.output("Grid has no columns property", LOGGINGLEVEL.ERROR);
            }
            
        } catch (e) {
            application.output("Error processing data grid: " + e.toString(), LOGGINGLEVEL.ERROR);
        }
    }
    
    /**
     * Collects child containers from a form
     * @param {JSForm} jsChildForm - The form to analyze
     * @param {String} formName - Form name
     * @return {Array} Array of child container info
     */
    function collectChildContainers(jsChildForm, formName) {
        var foundContainers = [];
        
        var allComponents = jsForm.getComponents();
        for (var j = 0; j < allComponents.length; j++) {
            var childComponent = allComponents[j];
            if (childComponent instanceof JSWebComponent) {
                var childTypeName = childComponent.typeName;
                if (childTypeName === 'bootstrapcomponents-tabpanel' || childTypeName === 'servoyextra-splitpane') {
                	foundContainers.push({
                        name: childComponent.name,
                        type: childTypeName
                    });
                }
            }
        }
        
        return foundContainers;
    }
    
    /**
     * Processes child containers and their forms
     * @param {String} formName - Form name
     * @param {Array} foundContainers - Array of child container info
     */
    function processChildContainers(formName, foundContainers) {
        var formObj = forms[formName];
        
        for (var j = 0; j < foundContainers.length; j++) {
            var container = foundContainers[j];
            
            try {
                if (container.type === 'bootstrapcomponents-tabpanel') {
                    // Process ONLY the active tab
                    try {
                        var selectedIndex = scopes.globals["avUtilities_tabGetSelectedIndex"](formName, container.name);
                        
                        if (selectedIndex >= 0) {
                            var tabFormName = scopes.globals["avUtilities_tabGetFormName"](formName, container.name, selectedIndex);
                            var tabText = scopes.globals["avUtilities_tabGetText"](formName, container.name, selectedIndex);
                            
                            if (tabFormName && forms[tabFormName] && tabText != "utils_quickSearch") {
                                // Create tab entry
                                globals.avBase_dataDictionary_sequence += 1;
                                rDataDictionary = fsDataDictionary.getRecord(fsDataDictionary.newRecord(false));
                                rDataDictionary.session_id = globals.av_CurrentUser_SessionUUID;
                                rDataDictionary.dd_sequence = globals.avBase_dataDictionary_sequence;
                                rDataDictionary.dd_element_type = "Tab";
                                rDataDictionary.dd_element_label = removeFormatting(tabText);
                                rDataDictionary.dd_element_datasource = null;
                                
                                // Process only the selected tab's form
                                createDataDictionaryData(tabFormName);
                            }
                        }
                    } catch (e) {
                        application.output("Error processing tabpanel: " + e, LOGGINGLEVEL.ERROR);
                    }
                } else if (container.type === 'servoyextra-splitpane') {
                    // Process splitpane - both sides
                    try {
                        var rightForm = formObj.elements[container.name].getRightForm();
                        if (rightForm) {
                            var rightFormName = rightForm.controller.getName();
                            if (rightFormName && forms[rightFormName]) {
                                createDataDictionaryData(rightFormName);
                            }
                        }
                        
                        var leftForm = formObj.elements[container.name].getLeftForm();
                        if (leftForm) {
                            var leftFormName = leftForm.controller.getName();
                            if (leftFormName && forms[leftFormName]) {
                                createDataDictionaryData(leftFormName);
                            }
                        }
                    } catch (e) {
                        application.output("Error processing splitpane: " + e, LOGGINGLEVEL.ERROR);
                    }
                }
            } catch (e) {
                application.output('Error processing container ' + container.name + ': ' + e, LOGGINGLEVEL.ERROR);
            }
        }
    }
    
    /**
     * Maps bootstrap component type to field type description
     * @param {String} sComponentType - Full bootstrap component type (e.g., bootstrapcomponents-textbox)
     * @return {String} Field type description
     */
    function mapBootstrapComponentType(sComponentType) {
        if (sComponentType === 'bootstrapcomponents-textbox') {
            return "Text Field";
        } else if (sComponentType === 'bootstrapcomponents-textarea') {
            return "Text Area";
        } else if (sComponentType === 'bootstrapcomponents-typeahead') {
            return "Type Ahead";
        } else if (sComponentType === 'bootstrapcomponents-combobox') {
            return "Combo Box";
        } else if (sComponentType === 'bootstrapcomponents-checkbox') {
            return "Check Box";
        } else if (sComponentType === 'bootstrapcomponents-calendar') {
            return "Calendar";
        } else if (sComponentType === 'bootstrapcomponents-imagemedia') {
            return "Image";
        } else if (sComponentType.indexOf('bootstrapcomponents-') === 0) {
            // Return cleaned component type for other bootstrap components
            return sComponentType.replace('bootstrapcomponents-', '');
        } else {
            // Unknown component type
            return sComponentType || "Unknown";
        }
    }
    
    /**
     * @param {String} sDatasource
     */
    function getDataSource(sDatasource) {
        try {
            if (sDatasource && sDatasource.indexOf("/") > 0 ) {
                var n = sDatasource.lastIndexOf('/');
                return sDatasource.substring(n + 1);
            }
            else {
                return "";
            }
        } catch (e) {
            return "";
        }
    }
    
    /**
     * removeFormatting
     * @param {String} sName
     */
    function removeFormatting(sName) {
        if (!sName) return "";
        
        sName = scopes.avText.replaceAll(sName, "%" , "");
        sName = sName.replace(/(<([^>]+)>)/gi, "");
        
        if (sName.indexOf("globals.$space") > -1) {
            sName = "";
        }
        
        return sName.replace(/&nbsp;/g, " ");
    }
}

/**
 * exportDataDictionaryToCSV
 * 
 * @param {String} sView
 * @param {String} [sDialogName]
 *
 * @properties={typeid:24,uuid:"7178C2EE-A40C-49FA-B599-9EDA25ECCEB0"}
 */
function exportDataDictionaryToCSV(sView, sDialogName) {
	var uSessionId = globals.av_CurrentUser_SessionUUID;
	
	if (!sDialogName) {
		sDialogName = null;
	}

	//Create file
	var sFileName = (sDialogName != null ? "SlingshotDataDictionary_" + sView + "_" + globals.nav_program_name + "_" + sDialogName + ".csv" : "SlingshotDataDictionary_" + sView + "_" + globals.nav_program_name + ".csv");
	var sFile = "";
	var sHeader = 'Program Name, Dialog Name, Tab Name, Form Name, Field Name, Datasource\n';
	var sFileData = "";
	var sTabName = "";
	var sProgramName = "";

	/** @type {JSFoundset<db:/avanti/tmp_data_dictionary>} */
	var fsDataDictionary = scopes.avDB.getFS("tmp_data_dictionary", ["session_id"], [uSessionId]);
	
    if (!fsDataDictionary) {
        application.output("EXPORT_DATA_DICTIONARY_TO_CSV - Error: Could not get foundset for tmp_data_dictionary", LOGGINGLEVEL.ERROR);
        return;
    }
    
	if (fsDataDictionary && fsDataDictionary.getSize() > 0) {
		fsDataDictionary.sort("dd_sequence asc");

		for (var index = 1; index <= fsDataDictionary.getSize(); index++) {
			var rDataDictionary = fsDataDictionary.getRecord(index);
			
			// Clean Labels
			rDataDictionary.dd_element_label = scopes.avText.replaceAll(rDataDictionary.dd_element_label, "%" , "");
			rDataDictionary.dd_element_label = rDataDictionary.dd_element_label.replace(/(<([^>]+)>)/gi, "");
			
			if (rDataDictionary.dd_element_label.indexOf("globals.$space") > -1) {
				rDataDictionary.dd_element_label = "";
			}
			
			//Exclude local variable data sources.
			if (rDataDictionary.dd_element_datasource 
					&& rDataDictionary.dd_element_datasource.startsWith("_", 0) 
					&& !rDataDictionary.dd_element_datasource.startsWith("_v", 0)) {
				continue;
			}

			if (rDataDictionary.dd_element_type == "Program") {
				sProgramName = rDataDictionary.dd_element_label;
			} else if (rDataDictionary.dd_element_type == "Tab") {
				sTabName = rDataDictionary.dd_element_label;
				continue;
			}
			else if (rDataDictionary.dd_element_type == "Dialog") {
				sDialogName = rDataDictionary.dd_element_label;
				continue;
			}
			else if (rDataDictionary.dd_element_type == "Form") {
				// Program
				if (sProgramName) {
					sFileData += '"' + sProgramName + '",';
					sProgramName = "";
				}
				else {
					sFileData += '"",';
				}

				sFileData += '"",'; // Dialog
				
				// Tab
				if (sTabName) {
					sFileData += '"' + sTabName + '",';
					sTabName = "";
				}
				else {
					sFileData += '"",';
				}
				
				sFileData += '"' + rDataDictionary.dd_element_label + '",';
				sFileData += '"",';
				sFileData += '"' + rDataDictionary.dd_element_datasource + '",';
			} else {
				sFileData += '"",'; //Program
				
				// Dialog
				if (sDialogName) {
					sFileData += '"' + sDialogName + '",';
					sDialogName = "";
				}
				else {
					sFileData += '"",';
				}
				
				sFileData += '"",'; //Tab
				sFileData += '"",'; //Form name
				sFileData += '"' + rDataDictionary.dd_element_label + '",'; //Field Name
				sFileData += '"' + rDataDictionary.dd_element_datasource + '",'; // Datasource
			}
			
			// End of line
			sFileData += "\n";
		}

		sFile = sHeader + sFileData;
		var success = plugins.file.writeTXTFile(sFileName, sFile, 'UTF-8', 'text/csv');
        
        if (!success) {
            application.output("Failed to write data dictionary file: " + sFileName, LOGGINGLEVEL.ERROR);
        }
	}
}

/**
 * TODO generated, please specify type and doc for the params
 * @param {String} text
 *
 * @properties={typeid:24,uuid:"87C00607-152B-462A-813B-1C448D363090"}
 */
function writeToClipboard(text) {
	if (text) {
		plugins.WebClientUtils.executeClientSideJS("navigator.clipboard.writeText('" + text + "').then( () => {},  () => {},);");
	}

}

/**
 * This capitalizes the first letter of all keys in the JSON object
 * 
 * @public 
 * 
 * @param oJSON
 * @param {String} [sUpperOrLower] - u/U or l/L
 * 
 * @return
 * @properties={typeid:24,uuid:"4035D243-3C0B-4FC0-8064-560035B3CF7B"}
 */
function changeCaseFirstLetterJSONKeys(oJSON, sUpperOrLower) {
	var bUpper = (sUpperOrLower == "U" || sUpperOrLower == "u");
	var bLower = (sUpperOrLower == "L" || sUpperOrLower == "l");
	
    // Check if the given parameter is an object
    if (typeof oJSON !== 'object' || oJSON === null || (!bUpper && !bLower)) {
        return oJSON; // Return the original value if it's not an object
    }

    // Initialize an empty object or array to store the transformed keys/values
    var oNewJSON = Array.isArray(oJSON) ? [] : {};

    // Iterate through each key in the object
    for (var key in oJSON) {
    	// dont do anything with a function - we just want the data
        if (typeof oJSON[key] == 'function' || oJSON[key] === null) {
        	continue;
        }
    	
    	var newKey;
        
        // Uppercase the first letter of the current key
		if (bUpper) {
	        newKey = key.charAt(0).toUpperCase() + key.slice(1);
		}
        // Lowercase the first letter of the current key
        else if (bLower) {
            newKey = key.charAt(0).toLowerCase() + key.slice(1);
        }

        if (typeof oJSON[key] == 'object') {
    		// Recursively apply the function to nested objects or arrays
			if (oJSON[key].length > 0 || hasChildren(oJSON[key])) {
                oNewJSON[newKey] = changeCaseFirstLetterJSONKeys(oJSON[key], sUpperOrLower);
			}
        	else if (oJSON[key]) {
    			oNewJSON[newKey] = oJSON[key];
        	}
        }
        // eg. typeof 'boolean', 'string', 'bigint', 'number'
        else {
			oNewJSON[newKey] = oJSON[key];
        }
    }

    return oNewJSON;
    
    function hasChildren(o) {
        for (var key2 in o) {
            if (o[key2] && typeof o[key2] != 'function') {
            	return true;
            }
        }    	
        
        return false;
    }
}

/**
 * @public 
 * 
 * @param {String} str
 * 
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"218FC4DE-6006-4CB5-A60F-FFC2C8BD3D36"}
 */
function isJSON(str) {
	if (str) {
		try {
			JSON.parse(str);
		    return true;
		}
		catch (e) {
			return false;
		}
	}
	else {
		return false;
	}
}

/**
 * @public 
 * 
 * @param {plugins.http.Response} oHTTPResponse
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"3D8F9E6C-E5D0-49A0-9225-04405DCEDE81"}
 */
function getHTTPResponseBodyText(oHTTPResponse) {
	var sText = null;
	
	try {
		if (oHTTPResponse) {
			var oResponseHeaders = oHTTPResponse.getResponseHeaders();
			
			if (oResponseHeaders["content-encoding"] && oResponseHeaders["content-encoding"][0] == "gzip") {
				//*****Calling getMediaData() after getResponseBody() will return NULL, we can only call 1 or the other in one single request
				var aResponse = oHTTPResponse.getMediaData();
				/** @type{Packages.java.io.ByteArrayInputStream} */
				var bais = new Packages.java.io.ByteArrayInputStream(aResponse);
				/** @type{Packages.java.util.zip.GZIPInputStream} */
				var bodyStream = new Packages.java.util.zip.GZIPInputStream(bais);
				/** @type{Packages.java.io.InputStreamReader} */
				var inputStreamReader = new Packages.java.io.InputStreamReader(bodyStream);
				var reader = new Packages.java.io.BufferedReader(inputStreamReader);
				var line;
				
				while ( (line = reader.readLine()) != null) {
					sText += line;
				}
			
				reader.close();
				bais.close();
			}
			else {
				sText = oHTTPResponse.getResponseBody();
			}
		}
	}
	catch (ex) {
		if (reader !== null) {
			reader.close();
		}
		if (bais !== null) {
			bais.close();
		}
	}
	
	return sText;
}

/**
 * Sets a user preference value
 *
 * <AUTHOR> Dotzlaw
 * @since 2024-11-12
 * 
 * @param {String} prefType Unique identifier for the preference type
 * @param {String} prefValue Value to store
 * @return {Boolean} Success status
 * @public
 *
 * @properties={typeid:24,uuid:"AB3110CA-EF84-4701-93C0-4F6D9A60AE91"}
 */
function setUserPref(prefType, prefValue) {
	try {
        var q = databaseManager.createSelect('db:/avanti/sys_employee_preference');
        q.where.add(q.columns.empl_id.eq(globals.avBase_employeeUUID));
        q.where.add(q.columns.sys_empl_pref_type.eq(prefType));
        q.where.add(q.columns.org_id.eq(globals.org_id));
        
        var fs = databaseManager.getFoundSet(q);
        /** @type {JSRecord<db:/avanti/sys_employee_preference>} */
        var record;
        
        if (fs.getSize() == 1) {
            record = fs.getRecord(1);
        } else {
            record = fs.getRecord(fs.newRecord(false, true));
            record.empl_id = globals.avBase_employeeUUID;
            record.sys_empl_pref_type = prefType;
            record.org_id = globals.org_id;
        }
        
        record.sys_empl_pref_grid = prefValue;
        return databaseManager.saveData(record);
    } catch (e) {
        application.output('Error in setUserPref: ' + e.toString(), LOGGINGLEVEL.ERROR);
        return false;
    }
}

/**
 * Gets a user preference value
 *
 * <AUTHOR> Dotzlaw
 * @since 2024-11-12
 * 
 * @param {String} prefType Unique identifier for the preference type
 * @return {String} Stored preference value or null if not found
 * @public
 *
 * @properties={typeid:24,uuid:"C0F92D7D-9C6B-4C93-98DF-C448F234684B"}
 */
function getUserPref(prefType) {
	try {
        var q = databaseManager.createSelect('db:/avanti/sys_employee_preference');
        q.result.add(q.columns.sys_empl_pref_grid);
        q.where.add(q.columns.empl_id.eq(globals.avBase_employeeUUID));
        q.where.add(q.columns.sys_empl_pref_type.eq(prefType));
        q.where.add(q.columns.org_id.eq(globals.org_id));
        
        var ds = databaseManager.getDataSetByQuery(q, 1);
        return ds.getMaxRowIndex() > 0 ? ds.getValue(1, 1) : null;
    } catch (e) {
        application.output('Error in getUserPref: ' + e.toString(), LOGGINGLEVEL.ERROR);
        return null;
    }
}

/**
 * Updates the nav_programs and nav_menu_items tables with material icons
 * and logs any icons that don't have matching material icons
 *
 * @properties={typeid:24,uuid:"NEW-UUID-MIGRATION-FUNCTION"}
 */
function migrateIconsToDB() {
    // Get the icon mapping from vIconReplacement
    var vIconReplacement = {};
    vIconReplacement['home'] = 'home';
    vIconReplacement['Customers'] = 'groups';
    vIconReplacement['Estimating_24x24'] = 'calculate';
    vIconReplacement['Project_Plan_24x24'] = 'view_timeline';
    vIconReplacement['SalesOrders'] = 'box_add';
    vIconReplacement['Production'] = 'account_tree';
    vIconReplacement['Inventory'] = 'inventory_2';
    vIconReplacement['Purchasing'] = 'receipt_long';
    vIconReplacement['scheduling'] = 'overview';
    vIconReplacement['Shipping'] = 'local_shipping';
    vIconReplacement['Billing'] = 'request_quote';
    vIconReplacement['Accounting_24x24'] = 'list_alt';
    vIconReplacement['CRM'] = 'diversity_3';
    vIconReplacement['JobManager2'] = 'book_2';
    vIconReplacement['Reporting'] = 'analytics';
    vIconReplacement['SystemSetup'] = 'settings';
    vIconReplacement['Customers16'] = 'groups';
    vIconReplacement['Contact16'] = 'id_card';
    vIconReplacement['Estimating_16x16'] = 'calculate';
    vIconReplacement['SalesOrders16'] = 'box_add';
    vIconReplacement['Inventory16'] = 'inventory_2';
    vIconReplacement['Scheduling16'] = 'overview';
    vIconReplacement['Production16'] = 'account_tree';
    vIconReplacement['Mailing16'] = 'mail';
    vIconReplacement['Shipping16'] = 'local_shipping';
    vIconReplacement['Billing16'] = 'request_quote';
    vIconReplacement['Accounting16'] = 'list_alt';
    vIconReplacement['CRM16'] = 'diversity_3';
    vIconReplacement['Reporting16'] = 'analytics';
    vIconReplacement['Employees16'] = 'manage_accounts';
    vIconReplacement['Equipment16'] = 'manufacturing';
    vIconReplacement['Organization16'] = 'domain';
    vIconReplacement['Lock_16x16'] = 'lock';
    vIconReplacement['Integration_16x16'] = 'all_inclusive';
    vIconReplacement['Folder-Closed16'] = 'folder';
    vIconReplacement['TriggersAndAlerts_16x16'] = 'notifications';
    vIconReplacement['Tools_24x24'] = 'rule_settings';
    vIconReplacement['Paperclip_16x16'] = 'attach_file';

    // Arrays to track unmapped icons
    var unmappedProgramIcons = [];
    var unmappedMenuIcons = [];
    
    // Update nav_programs table
    var programs = databaseManager.getFoundSet('svy_framework', 'nav_program');
    programs.loadAllRecords();

    var updateCount = 0;

    for (var i = 1; i <= programs.getSize(); i++) {
        var program = programs.getRecord(i);
        var oldIcon = program.program_image;

        if (oldIcon) {
            // Remove file extension if present
            var cleanIcon = oldIcon.replace(/\.(png|jpg|jpeg|gif)$/ig, '');
            
            // Check if there's a replacement
            if (vIconReplacement[cleanIcon]) {
                program.program_image = vIconReplacement[cleanIcon];
                databaseManager.saveData(program);
                updateCount++;
            } else if (cleanIcon && cleanIcon !== 'arrow_right') {
                // Only add non-empty and non-default icons to the unmapped list
                // Track program name with the icon for better context
                unmappedProgramIcons.push({
                    program: program.program_name,
                    icon: oldIcon
                });
            }
        }
    }

    // Update nav_menu_items table for consistency
    var menuItems = databaseManager.getFoundSet('svy_framework', 'nav_menu_items');
    menuItems.loadAllRecords();

    for (var j = 1; j <= menuItems.getSize(); j++) {
        var menuItem = menuItems.getRecord(j);
        var oldMenuIcon = menuItem.menuitem_image;

        if (oldMenuIcon) {
            // Remove file extension if present
            var cleanMenuIcon = oldMenuIcon.replace(/\.(png|jpg|jpeg|gif)$/ig, '');
            
            // Check if there's a replacement
            if (vIconReplacement[cleanMenuIcon]) {
                menuItem.menuitem_image = vIconReplacement[cleanMenuIcon];
                databaseManager.saveData(menuItem);
                updateCount++;
            } else if (cleanMenuIcon && cleanMenuIcon !== 'folder' && cleanMenuIcon !== 'manufacturing') {
                // Only add non-empty and non-default icons to the unmapped list
                // Track menu item description for better context
                unmappedMenuIcons.push({
                    menuItem: menuItem.display_description,
                    icon: oldMenuIcon
                });
            }
        }
    }

    // Log any unmapped icons
    if (unmappedProgramIcons.length > 0) {
        application.output("===== UNMAPPED PROGRAM ICONS =====", LOGGINGLEVEL.WARNING);
        for (var p = 0; p < unmappedProgramIcons.length; p++) {
            application.output("Program: " + unmappedProgramIcons[p].program + 
                              ", Icon: " + unmappedProgramIcons[p].icon, LOGGINGLEVEL.WARNING);
        }
    }
    
    if (unmappedMenuIcons.length > 0) {
        application.output("===== UNMAPPED MENU ITEM ICONS =====", LOGGINGLEVEL.WARNING);
        for (var m = 0; m < unmappedMenuIcons.length; m++) {
            application.output("Menu Item: " + unmappedMenuIcons[m].menuItem + 
                              ", Icon: " + unmappedMenuIcons[m].icon, LOGGINGLEVEL.WARNING);
        }
    }

    var resultMessage = "Updated " + updateCount + " icons in database.";
    if (unmappedProgramIcons.length > 0 || unmappedMenuIcons.length > 0) {
        resultMessage += " Found " + (unmappedProgramIcons.length + unmappedMenuIcons.length) + 
                         " unmapped icons. See application log for details.";
    }
    
    return resultMessage;
}
