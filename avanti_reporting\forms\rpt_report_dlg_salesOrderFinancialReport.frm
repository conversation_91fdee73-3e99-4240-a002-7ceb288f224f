borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"127,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"127",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromOrderDate",
visible:true
},
name:"_fromOrderDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"05091162-3260-4D47-8F8E-78A94A30E2E7"
},
{
height:200,
partType:5,
typeid:19,
uuid:"059DB71A-630C-464C-A917-F61671DE3902"
},
{
cssPosition:"179,362,-1,-1,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"362",
top:"179",
width:"48"
},
enabled:true,
labelFor:"_toCustomerCode",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toCustomerCode_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"165DDB67-07DD-48A2-906F-7031BDE03265"
},
{
cssPosition:"127,-1,-1,478,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"478",
right:"-1",
top:"127",
width:"250"
},
dataProviderID:"_dateTo",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dTo",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"2396A658-E21D-4F9D-A073-1CAB0A61F296"
},
{
cssPosition:"153,-1,-1,155,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"153",
width:"250"
},
dataProviderID:"_fromSalesOrder",
enabled:true,
onDataChangeMethodID:"C67C77F8-6C10-4999-BA2E-DCC3DCDC1F76",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_fromSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2E518BFE-7568-4131-89E5-CF71E1B1B705"
},
{
cssPosition:"205,362,-1,-1,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"362",
top:"205",
width:"48"
},
enabled:true,
labelFor:"_toSalesRep",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toSalesRep_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4B6A2E66-A036-4729-A599-48BCC6075157"
},
{
cssPosition:"153,362,-1,-1,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"362",
top:"153",
width:"48"
},
enabled:true,
labelFor:"_toSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"75391502-508A-4A4A-AF9D-AE8DE6BAB95B"
},
{
cssPosition:"11,-1,-1,155,250,112",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"112",
left:"155",
right:"-1",
top:"11",
width:"250"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
onDataChangeMethodID:"BEE3B1B4-9E18-4B35-83DF-1E612F4FA111",
selectSize:5,
styleClass:"checkbox_column",
tabSeq:0,
valuelistID:"AEF3596A-1539-4A54-B2A9-9FFE614E2DFA",
visible:true
},
name:"fromDiv",
styleClass:"checkbox_column",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"9CD2C751-D5BB-4301-BECA-D00AC01EDFA3"
},
{
cssPosition:"154,-1,-1,478,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"478",
right:"-1",
top:"154",
width:"250"
},
dataProviderID:"_toSalesOrder",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"8DD85D6B-407A-4510-BF8E-F866B05E9E81",
visible:true
},
name:"_toSalesOrder",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"A898E13C-6872-4F50-B0E3-C5725A3FC691"
},
{
cssPosition:"11,-1,-1,5,140,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"11",
width:"140"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B35A246D-2611-424E-BDF2-FCD91437FC7A"
},
{
cssPosition:"8,-1,-1,478,250,115",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"115",
left:"478",
right:"-1",
top:"8",
width:"250"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
multiselect:"vl_Plants_rpt_dlg_base_on_div",
selectSize:5,
styleClass:"select_bts",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"select_bts",
typeName:"bootstrapcomponents-select",
typeid:47,
uuid:"BA1CD8EB-22AD-432B-9CF2-0EE8776926A3"
},
{
cssPosition:"11,362,-1,-1,48,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"362",
top:"11",
width:"48"
},
enabled:true,
formIndex:30,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BF9EFA13-9702-43B0-B993-B6C18F1BD8DA"
},
{
cssPosition:"205,-1,-1,155,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"205",
width:"250"
},
dataProviderID:"_fromSalesRepCode",
editable:true,
enabled:true,
onDataChangeMethodID:"4E44C29B-9C9E-43C8-9DEB-0A55EFDFE4C5",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"627651CC-8BFA-40FD-9DCD-CD71E26DB231",
visible:true
},
name:"_fromSalesRep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D17BAFDD-4C8E-4152-981E-7DF521FDAACE"
},
{
cssPosition:"205,-1,-1,478,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"478",
right:"-1",
top:"205",
width:"250"
},
dataProviderID:"_toSalesRepCode",
editable:true,
enabled:true,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"627651CC-8BFA-40FD-9DCD-CD71E26DB231",
visible:true
},
name:"_toSalesRep",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D1BD48A2-A157-440D-917F-72E32A75F958"
},
{
cssPosition:"179,-1,-1,478,250,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"478",
right:"-1",
top:"179",
width:"250"
},
dataProviderID:"_toCustomerCode",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"5AF60AE4-7FF2-4A55-8ED2-B6C7F1F444C6",
visible:true
},
name:"_toCustomerCode",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"EE3EFA14-DCEC-44A5-9763-9FBBEF3D829B"
},
{
cssPosition:"205,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"205",
width:"140"
},
enabled:true,
labelFor:"_fromSalesRep",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesRep",
visible:true
},
name:"_fromSalesRep_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F34D8E83-1806-4B40-89D3-CA0D34533DE6"
},
{
cssPosition:"179,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"179",
width:"140"
},
enabled:true,
labelFor:"_fromCustomerCode",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromCustomer",
visible:true
},
name:"_customer_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F7FF2DB2-9D94-4743-A983-B8EB40D10DFE"
},
{
cssPosition:"127,-1,-1,155,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"127",
width:"250"
},
dataProviderID:"_dateFrom",
enabled:true,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"dFrom",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"F8AC6AC0-AF39-4AD9-BA4D-A42A4AC16F8E"
},
{
cssPosition:"179,-1,-1,155,250,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"179",
width:"250"
},
dataProviderID:"_fromCustomerCode",
editable:true,
enabled:true,
onDataChangeMethodID:"6B60518F-DF0B-4F77-8E69-395C723E461F",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"5AF60AE4-7FF2-4A55-8ED2-B6C7F1F444C6",
visible:true
},
name:"_fromCustomerCode",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"FA2E0368-227F-43D3-8DFC-CA58D5F6DED0"
},
{
cssPosition:"152,-1,-1,5,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"152",
width:"140"
},
enabled:true,
labelFor:"_fromSalesOrder",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromSalesOrder",
visible:true
},
name:"_fromSalesOrder_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FD8ED418-ACD7-4644-ACAB-9DD477658716"
},
{
cssPosition:"127,362,-1,-1,48,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"362",
top:"127",
width:"48"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toOrderDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FF181FCD-4A3F-413B-882B-85C9BC50E766"
}
],
name:"rpt_report_dlg_salesOrderFinancialReport",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:33,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"B8312F4A-F1E3-4FEF-9F23-901E729A56F4"