// import of the custom servoy theme properties that will import the hidden servoy theme, this imported file is for customizing the default servoy theme properties 
@import 'custom_servoy_theme_properties.less';
@import 'icons.css';

// Add your custom less/css to this file you can use the same less properties that are specified in the above properties.less

/* Default variables */
@default-font-size: 11px;
@default-font-family: "Verdana", sans-serif;
@default-focus-color: @color-gray-dark;

/* Additional theme variables for components */
@primary-color: @main-color;       // Use main-color from theme as primary
@secondary-color-btn: @secondary-color;  // Secondary button color

/* Color palette */
@color-black: #000000;
@color-blue-web: #0000FF;
@color-blue: #94bae1;
@color-blue-light: #81F7F3;
@color-blue-medium: #4682B4;
@color-brown: #8B4513;
@color-gray: #999999;
@color-gray-dark: #6B6A65;
@color-gray-darker: #A4A4A4;
@color-gray-dim: #eaecef;
@color-gray-light: #cccccc;
@color-gray-lighter: #f0f0f0;
@color-gray-lightest: #f8f8f8;
@color-green: #F5F6BE;
@color-green-bright: #2EFE2E;
@color-green-dark: #228B22;
@color-navy: #000099;
@color-navy-light: #94BAE1;
@color-orange: #FE642E;
@color-pink: #F5A9F2;
@color-purple: #DF01D7;
@color-red: #FF0000;
@color-violet: #DF01D7;
@color-white: #ffffff;
@color-yellow: #2EFE2E;
@color-yellow-light: #ffffcc;
@color-yellow-medium: #F2F5A9;

/* Scrollbar colors */
@scrollbar-track-color: #d9d9df;
@scrollbar-track-border-color: #b8c6ff;
@scrollbar-thumb-color: #babdb6;

/* Form elements */
@input-border-color: #ced4da;
@input-border-radius: 4px;
@input-padding: 0 5px;
@input-min-height: 22px;

/* Brand colors standardization */
@brand-danger-highlighted: @color-red;

/* Override Bootstrap's default font size and family */
body {
  font-size: @default-font-size;
  font-family: @default-font-family;
}

/* Framework */
.svy-fr-status-bar {
	.bg-main;
	
	.svy-label,
	.bts-label {
		color: inherit;
		.h6;
		.font-weight-bold;
	}
}

.svy-fr-menu-header,
.svy-fr-menu-footer {
	background-color: @secondary-color;
	color: @secondary-color-inverse;
	
	.svy-label,
	.bts-label {
		color: @secondary-color-inverse;
		
		&.icon {
			color: @main-color;
		}
	}
}

.svy-fr-buttonbar {
	background-color: @secondary-color-light;
	color: @secondary-color-inverse;
	
	.svy-label,
	.bts-label {
		color: @secondary-color-inverse;
		
		&.icon {
			color: @main-color;
		}
	}
}

/* Fields */
.field {
	.h6;
	border: 1px solid @input-border-color !important;
	border-radius: @input-border-radius !important;
}

.input-sm {
	.h6;
}

.field-clickable {
  	cursor: pointer !important;
}

/* Tabpanel*/
.svy-form.border {
	border: none !important;
	padding: 0 !important;
	margin: 0 !important;
}

.svy-tablesPanel {
	overflow: hidden !important;
}

div[ng-reflect-name="sa_task_paper_rolls"] {
	overflow: visible !important;
}

/* Combobox and Typeahead*/
.svy-combobox,
.svy-typeahead {
	border: 1px solid @input-border-color !important;
	border-radius: @input-border-radius !important;
}

.svy-combobox.input-sm,
.svy-combobox .svy-combobox-container-dropdown,
.svy-combobox .svy-dropdown-text,
.svy-typeahead.input-sm,
.svy-typeahead .svy-typeahead-container-dropdown,
.svy-typeahead .svy-dropdown-text {
	min-height: @input-min-height !important;
}

.svy-combobox .svy-dropdown-text,
.svy-typeahead .svy-dropdown-text {
	display: flex !important;
	align-items: center !important;
}

.svy_field {
	color: @color-black;
}

label.svy-check-radio input[type="checkbox"],
label.svy-check-radio input[type="radio"] {
	vertical-align: baseline;
	margin: 0 5px 0 0;
}

.svy-radiogroup-horizontal {
	grid-auto-flow: column;
	padding: 0;
	align-items: center;
	
	label.svy-check-radio {
		display: flex;
		
		input[type="radio"],
		input[type="checkbox"] {
		  margin-right: 0; // Space between input and label text, the label has 5px padding already
		}
  	}
}

.labor-card, .material-card, .shift-card {
    width: 100px;
    border: 1px solid @color-gray;
    font-size: 12px;

    .normal, .bold {  // Nested inside the cards
        margin: 1px 0;
        height: 16px;
        line-height: 16px;
    }

    &-header {
        height: 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    &-unposted-posted {
        height: 32px;
        line-height: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2px 0;
    }

    &-total {
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.text-left {
  	text-align: left !important;

	&.material-symbols-outlined {
		font-size: 22px;
	    align-content: left !important;
	    cursor: pointer !important;
	}
}

.text-right {
  	text-align: right !important;

	&.material-symbols-outlined {
		font-size: 22px;
	    align-content: right !important;
	    cursor: pointer !important;
	padding-right: 2px
	}
}

.text-center {
	text-align: center !important;
	
	&.material-symbols-outlined {
		font-size: 22px;
	    align-content: center !important;
	    cursor: pointer !important;
	    
	}
}

.material-symbols-outlined {
	font-size: 22px;
    align-content: center !important;
    cursor: pointer !important;
	
	&.text-center {
		.text-center;
	}
	&.small {
		font-size: 16px;
		
		&.close {
	      color: @color-red !important; 
	      font-size: 12px;
	    }
	    &.arrow_drop_down {
	      color: @color-white !important; 
	      font-size: 12px;
	    }
	    &.arrow_drop_up {
	      color: @color-white !important; 
	      font-size: 12px;
	    }
	}
	
	&.text-center-vertical {
	    display: flex !important;
	    align-items: center !important;
	}
	
    &[disabled], &[readonly], &.disabled {
    	cursor: not-allowed !important;
    	opacity: 0.5;
   	}

	&.first_page::before {
	  content: "first_page";
	}
	
	&.last_page::before {
	  content: "last_page"; 
	}
	
	&.next_page::before {
	  content: "chevron_forward";
	}
	
	&.prev_page::before { 
	  content: "chevron_backward";
	}
	
}

/* Base styles for all bootstrap components */
/* Warning colors */
@color-warning: #edc87e;

&.over_warning {
		font: 16px "Verdana"; 
		font-weight: bold;
		color: @color-warning;
  	}
 	.bg-row-edited {
    background-color: @color-green !important;
  }
 
.bg-row-even {
  background-color: @field-bg !important;
}
 
.bg-row-odd {
  background-color: @color-gray-lighter !important;
}
 
/* Table selection color */
@table-selection-color: #3D80DF;

.bg-row-selected {
  background-color: @table-selection-color !important;
  color: @field-bg !important;
}

.text-large {
	font-size: 16px;
}
.text-medim {
	font-size: 12px;
}
.text-small {
	font-size: 8px;
}

.textBkgnd_White {
	background-color: @field-bg !important;
}
.text-red-bold-bg-white {
    color: @color-red;
    background-color: @field-bg;
    font-weight: bold;
}  
.text-blue {
    color: @color-blue !important;
}  
.text-blue-web {
    color: @color-blue-web;
}  
.text-orange {
    color: @color-orange !important;
}
.text-red {
    color: @color-red !important;
}
.text-blue-bold-bg-white {
    color: @color-blue;
    background-color: @field-bg;
    font-weight: bold;
}  
.text-white-bg-white {
   color: @text-color-inverse;
   background-color: @field-bg;
}
.text-red-bg-white {
   color: @color-red !important;
   background-color: @field-bg;
   font-weight: normal;
}
.text-black-bg-white {
   color: @color-black;
   background-color: @field-bg;
   font-weight: normal;
}
.text-black-bg-red {
   color: @color-black;
   background-color: @color-red;
   font-weight: normal;
}
.text-black-bg-green {
   color: @color-black;
   background-color: @color-green;
   font-weight: normal;
}
.text-black-bg-yellow {
   color: @text-color;
   background-color: @color-yellow;
   font-weight: normal;
}
.text-green-dark-bg-white { 
    color: @color-green-dark !important;
    background-color: @color-white !important;
}

.textBkgnd_White {
	background-color: @color-white !important;
}  

.textBkgnd_DimGray {
	background-color: @color-gray-dim !important;
}

.textBkgnd_LightGray {
	background-color: @color-gray-light !important;
}

.textBkgnd_MedGray {
  	background-color: @color-gray-darker !important;
}

.textBkgnd_MedDarkGray {
  	background-color: @color-gray-dark !important;
}

.textBkgnd_DarkGray {
	background-color: @color-gray-light !important;
}

.textBkgnd_Yellow {
	background-color: @color-green !important;
}

.textBkgnd_MedYellow {
    background-color: @color-yellow-medium !important;
}

.textBkgnd_LightYellow {
    background-color: @color-yellow-light !important;
}

.textBkgnd_Blue {
    background-color: @color-blue !important;
}
.textBkgnd_Blue_Dark {
    background-color: @color-blue-web !important;
}

.textBkgnd_Black {  
	background-color: @color-black !important;
	color: @color-white !important;
}

.textBkgnd_Red {
	background-color: @color-red !important;
}

.textBkgnd_Orange {
	background-color: @color-orange !important;
}

.textBkgnd_Green {
	background-color: @color-green-bright !important;
}

.textBkgnd_Violet {
	background-color: @color-violet !important;
}

.textBkgnd_LightBlue {
	background-color: @color-blue-light !important;
}

.textBkgnd_LightNavy {
	background-color: @color-navy-light !important;
}

.textBkgnd_Pink {
	background-color: @color-pink !important;
}

.textBkgnd_White-Override {
	background-color: @color-white !important;
}  

.group {
  &_heading {
    font-weight: bold;
    margin: 0px 0px 2px 0px;
    padding: 0px 4px 0px 4px;
    background-color: fade(@main-color, 15%) !important;
    //background-color: @secondary-color-light
  }
}

.background-group {
  border: 1px solid @color-gray-light;
  background-color: @color-gray-lighter;

  &_label {
    font-style: italic;
    color: @color-black;
    margin: 0px 0px 2px 0px;
    padding: 0px 4px 0px 4px;
    background-color: #F4F2F2;
  }

  &_heading {
    font-style: italic;
    font-weight: bold;
    color: @color-black;
    margin: 0px 0px 2px 0px;
    padding: 0px 4px 0px 4px;
    background-color: @color-gray-lighter;
  }
}

.background-group-white {
  border: 1px solid @color-white;
  background-color: @color-white;

  &_label {
    font-style: italic;
    color: @color-black;
    margin: 0px 0px 2px 0px;
    padding: 0px 4px 0px 4px;
    background-color: @color-white;
  }

  &_heading {
    font-style: italic;
    font-weight: bold;
    color: @color-black;
    margin: 0px 0px 2px 0px;
    padding: 0px 4px 0px 4px;
    background-color: @color-white;
  }
}

.bold
{
	font-weight: bold;
}
.bold_center
{
	font-weight: bold;
	text-align: center;
}
.italic
{
	font-style: italic;
}
.bold_black
{
	font-weight: bold;
	color: @color-black;
}

.large_bold_black_italic
{
	font-size: 16pt;
	font-weight: bold;
	font-style: italic;
	color: @color-black;
}

.large_bold_gray_italic
{
	font-size: 16pt;
	font-weight: bold;
	font-style: italic;
	color: @color-gray;
}

.bold_right
{
	font-weight: bold;
	color: @color-black;
	text-align: right;
}

.gray
{
	color: @color-gray;
}

.gray_italic
{
	color: @color-gray;
	font-style: italic;
}

.add_item_row
{
	color: @color-gray;
	font-style: italic;
	margin: 0px 4px 0px 4px;
}

.gray_bold
{
	font-weight: bold;
	color: @color-gray;
}

.gray_right
{
	text-align: right;
	color: @color-gray;
}

.hyperlink
{
	color: @color-navy;
	border-style: solid;
	border-width: 0px 0px 1px 0px;
	border-color: @color-navy;
	margin: 4px 0px 0px 4px; 
}

.text-danger-highlighted {
	color: @brand-danger-highlighted !important;
}	

.nav-tabs-container {	
	.nav-tabs {
		padding-top: 2px;
		border-bottom: 1px solid @color-gray-light;
	
		> li > a {
			padding: 3px 15px 3px 15px;
			border: 1px solid @color-gray-light;
			background: @color-gray-lightest;
	
			.bts-tabpanel-close-icon.fa-times.fas {
				margin-left: 12px !important;
				margin-right: -8px;
				color: @color-gray;
				&:hover {
                    color: @color-gray-light;
                }
			}
	
			&:hover {
				background: @color-white;				
			}
		}
	
		> li.active > a {
			background: @color-white;
			border-bottom-color: transparent;
		}
	}
}

&[readonly], &[disabled], &.not_editable {
	opacity: 1 !important;
	color: @text-color !important;
	cursor: default !important;  
}

.bootstrap-base {
	font-family: @default-font-family !important;
	font-size: @default-font-size !important;
	.h6;

}

/* Utility classes */
.line-height-normal {
	line-height: normal !important;
}
.white-space-initial {
	white-space: normal !important;
}
.flex {
	display: flex !important;
}
.gap-4px {
	gap: 4px;
}
.gap-12px {
	gap: 12px;
}
.margin-top-6px {
	margin-top: 6px;
}
.align-items-center {
	align-items: center;
}
.justify-center {
	justify-content: center;
}

.custom_content:before {
	display: flex;
	justify-content: center;
	font-size: 30px;
	line-height: 25px;
}


.px-4 {
	padding-left: 4px !important;
	padding-right: 4px !important;
}
.text-left {
	text-align: left !important;
}
    
.form-control-base {
	&:extend(.bootstrap-base);	
	border: 1px solid @input-border-color !important;
	border-radius: @input-border-radius !important;
	min-height: @input-min-height !important;
	padding: @input-padding;
/* 	display: flex; */
	align-items: center;
	background-color: @field-bg;
}

.textbox_bts {
	&:extend(.form-control-base);
 	&:focus {
	 	border: 1px solid @color-gray-dark !important;
	}
	&.small {
		font-size: 8px;
		min-height: 15px !important;
		max-height: 15px !important;
	}
	
}

.textarea_bts {
	&:extend(.form-control-base);
	min-height: @input-min-height !important;
	padding: 5px;
	align-items: flex-start;
	&:focus {
	 	border: 1px solid @color-gray-dark !important;
	}
}

.calendar_bts {  
	&:extend(.form-control-base);
}

.combobox_bts {	
	&:extend(.form-control-base);
	border: 1px solid @input-border-color !important;
	border-radius: @input-border-radius !important;
	min-height: @input-min-height !important;
	padding: 0 !important;
/*	display: flex !important;*/
	align-items: center !important;
/* 	background-color: @color-white !important; */

	.dropdown-toggle {
		width: 100% !important;
		height: 100% !important;
		min-height: @input-min-height !important;
		padding: 0 5px !important;
		display: flex !important;
		align-items: center !important;
		justify-content: space-between !important;
		background-color: transparent !important;
		border: none !important;
	  
		&:after {
			margin-left: 8px !important;
		}
		&:focus {
		 	border: 1px solid @color-gray-dark !important;
		}
	} 
}

.typeahead_bts {
	&:extend(.form-control-base);
  
	.bts-typeahead {
		display: flex !important;
		align-items: center !important;
		width: 100% !important;
	}	
	&:focus {
	 	border: 1px solid @color-gray-dark !important;
	}
}

.select_bts {
	&:extend(.form-control-base);
  
	select {
		border: none !important;
		background-color: transparent !important;
		width: 100% !important;
		height: @input-min-height !important;
		padding: 0 5px !important;
		option {
      		padding: 5px !important;
    	}
  	}
}

.multiselect_bts {
  &:extend(.form-control-base);
  
  min-height: 66px !important;
  
  select {
    border: none !important;
    background-color: transparent !important;
    width: 100% !important;
    min-height: 66px !important;
    padding: 5px !important;
    option {
      padding: 5px !important;
    }
  }
}

.label_bts {
	&:extend(.bootstrap-base);
	display: flex !important;
	align-items: center !important;
	min-height: @input-min-height !important;
	.h6;
	
	&.text-right {
		justify-content: flex-end !important
	}
	&.text-center {
		justify-content: center !important
	}
	&.text-left {
		justify-content: flex-start !important
	}
	&.rotate {
		writing-mode: vertical-lr;
    	text-orientation: mixed;
    	transform: rotate(180deg);
	}
	
	&.line {
	  min-height: 1px !important;
	  height: 1px !important;
	  padding: 0 !important;
	  
		&.black {
			background-color: @color-black !important;
		}
	  
		&.gray {
			background-color: @color-gray-light !important;
		}
	}
	&.small{
		font-size: 8px;
		min-height: 15px !important;
		max-height: 15px !important;
	}
	
	&[disabled], &.not_editable {
	   opacity: .65 !important;
	   color: @text-color !important;
	   cursor: default !important;  
	}
	
	&.button {
	   cursor: pointer !important;
	   transition: all 0.2s ease !important;
	   
	   &:hover {
	     background-color: rgba(0, 0, 0, 0.05) !important;
	     text-decoration: none !important;
	   }
	   
	   &:active {
	     background-color: rgba(0, 0, 0, 0.1) !important;
	   }
	}
    
	&.top_bg {
	  background: linear-gradient(to bottom, fade(@main-color, 90%), @main-color) !important;
	}
	
	&.bookmark_border {
	  background: linear-gradient(to bottom, fade(@main-color, 90%), @main-color) !important;
/* 	  border-style: solid !important;
	  border-width: 0px 0px 1px 0px !important;
	  border-color: #698111 !important;
	  background-color: @color-gray-dark !important; */
	
	}
	
	&.bookmark {
	  color: 8pt Verdana !important;
	  min-height: 15px !important;
	  max-height: 15px !important;
	  padding-top: 0 !important;
	  padding-bottom: 0 !important;
	  line-height: 15px !important;
	}
	
	&.bookmark_program {
	  color: @color-white !important;
	  font: bold 9pt Verdana !important;
	  min-height: 15px !important;
	  max-height: 15px !important;
	  padding-top: 0 !important;
	  padding-bottom: 0 !important;
	  line-height: 15px !important;
	}	
}

.button_bts {
	&:extend(.bootstrap-base);
	min-height: @input-min-height !important;
	padding: 0 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: @input-border-radius !important;
	color: @color-white !important;
	.h6;
	
	&:not(:disabled) {
	  cursor: pointer;
	}
	
	&[disabled] {
	  opacity: 0.65;
	  cursor: not-allowed;
	}
}

.choicegroup_bts {
	&:extend(.bootstrap-base);
	
	display: flex;
	align-items: center;
	gap: 10px;

	.bts-radiogroup {
	  display: flex;
	  align-items: center;
	  gap: 10px;
	}
	
	&.vertical {
	  flex-direction: column;
	  align-items: flex-start;
	  gap: 5px;
	  .bts-radiogroup {
	    flex-direction: column;
	    align-items: flex-start;
	  }
	}
	
	& > * {
		display: flex !important;
	}
	
}

.checkbox_bts {
	&:extend(.bootstrap-base);
	display: flex !important;
	min-height: @input-min-height !important;
	
	input[type="checkbox"] {
		margin: 0 4px 0 0 !important;
		vertical-align: middle !important;
	}

	label {
		margin: 0 !important;
		vertical-align: middle !important;
		display: inline-flex !important;
		align-items: center !important;
	}
}

.checkbox_column{
	width : auto !important;
}

.imagemedia_bts {
	&:extend(.bootstrap-base);
	
	border-radius: @input-border-radius !important;
	overflow: hidden;
  
	img {
		max-width: 100%;
		height: auto;
	}
}

.datalabel_bts {
	&:extend(.form-control-base);
	
	border: 1px solid @input-border-color !important;
	border-radius: @input-border-radius !important;
	min-height: @input-min-height !important;
	padding: 2px 5px;
	display: flex;
	align-items: center;
	color: @color-black !important;
	
	.bts-label {
		display: flex !important;
		align-items: center !important;
		min-height: @input-min-height !important;
		color: @color-black !important;
  	}	
}

.dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 10px 38px -10px rgba(22,23,24,0.35), 0 10px 20px -15px rgba(22,23,24,0.2);
  padding: 4px;
  font-size: inherit;
  font-family: inherit;
  min-width: max-content;

  li {
    > a {
      color: @text-color !important;
      padding: 6px 10px;
      border-radius: 4px;
      
      &:hover,
      &:focus {
        background-color: @secondary-color;
        color: @text-color !important;
      }
    }
    	&.active > a {
      &,
      &:hover,
      &:focus {
        background-color: @main-color;
        color: @main-color-inverse !important;
      }
    }
  }
  > li:not(:last-child) {
    margin-bottom: 1px;
  }

  border: 1px solid fadeout(@text-color, 90%);
}
.dropdown-divider {
  height: 1px;
  margin: 4px 0;
  background-color: fadeout(@text-color, 80%);
}

/* Icons */
.icon {
	font-size: 1.5rem;
	line-height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;
    
    &[disabled] {
	    cursor: not-allowed !important;
    }
}
.icon-bar-sep {
	border-color: @main-color;
	border-width: 0 0 0 1px;
	border-style: solid;
}

.icon-sm { 
  font-size: 16px;
}

/* Buttons */
.btn {
  font-size: 11px;
  border-radius: @input-border-radius;
  border: none;
  cursor: pointer;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  text-align: center;
  text-decoration: none;
  display: inline-block;
}

.btn-default {
  background-color: @main-color !important;
  color: @color-white !important;
  border: 1px solid @color-gray-light;
}

.btn-dialog {
  background-color: @main-color;
  color: @color-white;
  text-transform: uppercase;
  font-size: 11px;
}

.btn:hover {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0,0,0,0.1);
}

.btn:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.dashboard-widget {
	background-color: @color-gray-lightest;
	border: 1px solid @color-gray-dim;
	border-radius: 6px;
	padding: 15px;
	margin-bottom: 15px;
	box-shadow: 0 2px 6px rgba(0,0,0,0.08);
	
	.widget-header.svy-label,
	.widget-header.bts-label {
		background-color: fade(@main-color, 15%) !important;
	} 
	.widget-header {
		font-weight: 500;
		color: @text-color;
		margin-bottom: 10px;
		padding-bottom: 8px;
		border-bottom: 1px solid @color-gray-dim;
	}
	.widget-label.svy-label,
	.widget-label.bts-label {
		background-color: fade(@secondary-color, 15%) !important;
	} 
	.widget-label {
	   background-color: @color-gray-dim;
	   padding: 4px 8px;
	   border-radius: 3px;
	   font-weight: 500;
	}
}
.widget-header {
  &_bkgnd-gray {
    border: 1px solid @color-gray-light;
    background-color: @color-gray-light;
  }

  &_bkgnd-blue {
    border: 1px solid @color-blue-light;
    background-color: @color-blue-light;
  }

  &_text-white {
    font: 9px "Helvetica";
    font-weight: bold;
    color: @color-white;
  }
}

.svy-splitpane.no-border .split-panes.vertical > .split-pane2 {
	border-top: none;
}

/*Labels*/
.label-divider {
    display: block;
    height: 1px;
	position: absolute;
    padding: 0;
    background: linear-gradient(90deg, 
        rgba(0, 0, 0, 0.05) 0%,
        rgba(0, 0, 0, 0.15) 50%,
        rgba(0, 0, 0, 0.05) 100%
    );
    
    // Override default label properties
    border: none;
    text-indent: -9999px; // Hide any text content
    overflow: hidden;
}

// Light variant for dark backgrounds
.label-divider-light {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.05) 100%
    );
}

// Size variations
.label-divider-sm {
    margin: 10px 0;
}

.label-divider-lg {
    margin: 20px 0;
}

.text-9px {
	font-size: 9px !important;
}

// Dashed variation
.label-divider-dashed {
    background: none;
    border-top: 1px dashed rgba(0, 0, 0, 0.15);
}

/* DBTreeView */
.angular-tree-component {
	padding-left: 10px;
	font-size: @default-font-size;
	
	.node-content-wrapper {
		text-wrap: nowrap;
	}
	
	/* Remove the default icon and use FontAwesome font icon */
	.toggle-children-wrapper .toggle-children {
		background-image: none;
		top: 3px;
		font-family: var(--fa-style-family, "Font Awesome 6 Free");
		font-weight: 400;
	}

	/* equivalent to use fa-regular fa-plus-square */
	.toggle-children-wrapper.toggle-children-wrapper-collapsed .toggle-children::before {
		content: "\f0fe";
	}
	.toggle-children-wrapper.toggle-children-wrapper-expanded .toggle-children {
		transform: none;
		
		/* equivalent to use fa-regular fa-minus-square */
		&::before {
			content: "\f146";
		}
	}
}

/* AG Grid enhancements */
.ag-table.ag-theme-alpine {
    margin: 0 5px;
    
    .ag-root-wrapper {
        border-color: darken(@secondary-color-light, 10%);
        border-radius: 5px 5px 0 0;
    }
    
    .ag-root {
        border: none;
    }
    
    .ag-header {
        background-color: @secondary-color-light;
        text-transform: none;
        
        .ag-header-cell {
            --ag-cell-horizontal-padding: 2px;
            border-color: darken(@secondary-color-light, 10%);
            border-color: transparent;
            font-size: @default-font-size;
            padding-left: 4px !important;
            padding-right: 4px !important;
            .ag-header-cell-label {
                background-color: inherit;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                height: auto !important;
            }
            
            &.material-symbols-outlined,
            .material-symbols-outlined {
                font-size: 16px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                height: 100% !important;
                width: 100% !important;
                padding: 0 !important;
                font-variation-settings: 'FILL' 1 !important;
            }
            
            &.text-center {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
        }
        
        .ag-header-cell-label .ag-header-cell-text {
            white-space: pre-wrap !important;
            flex: 1;
        }
        
        .ag-header-cell-resize::after {
            background-color: darken(@secondary-color-light, 10%);
        }
    }
    
    .ag-cell {
        --ag-cell-horizontal-padding: 5px;
        align-content: center;
        font-size: @default-font-size;        	
        user-select: text;
		&.checkbox_bts {
			align-items: center !important;
            justify-content: center !important;
		}

        &.material-symbols-outlined {
            font-size: 16px;
            text-align: center;
            align-content: center !important;
            cursor: pointer;
           
            &.text-center {
				.text-center;
			}
			&.small {
				font-size: 16px;
			} 
        }
        & material-symbols-outlined {
            font-size: 16px;
        }
    }
}

/* Sidenav */
.svy-sidenav.no-border {
	.svy-navitem-selected.sn-level-1 {
		border-width: 0;
	}
}
.svy-sidenav-menu {
	line-height: 34px;
}
.svy-sidenav-item {
	padding: 0 10px;
	display: flex;
	align-items: center;
	margin: 0; /* Ensure no extra margin */
	
	.svy-sidenav-item-icon {
		margin-right: 10px;
	}
	&[class*="sn-level-"]:not(.sn-level-1) .svy-sidenav-item-icon {
		/* icon size is small in sub-menu items*/
		font-size: 20px;
	}
	.svy-sidenav-collapse-icon {
		line-height: 1;
		font-size: 16px;
	}
	.svy-sidenav-item-text {
		white-space: nowrap;
	}
}

/*Dialog*/
@warning-color: #FF6F00; // Darker orange for warning
@error-color: #C62828; // Darker red for error

.mat-mdc-dialog-container {
  font-family: @default-font-family !important;
  
  &.type-error {
    // Dialog action button styles
    .mat-mdc-dialog-actions .mdc-button.mat-primary {
      --mdc-protected-button-container-color: @error-color !important;
      background-color: @error-color !important;
      color: @color-white !important;
    }
  }

  .mdc-dialog__surface {
    overflow: hidden !important;
  }

  .mdc-dialog__title {
    display: flex !important;
    align-items: center !important;
    color: @color-white !important;
    text-align: left !important;
    padding: 12px 24px !important;
    margin: 0 !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    text-transform: capitalize !important;

    &::before {
      font-family: 'Material Symbols Outlined' !important;
      font-size: 32px !important;
      margin-right: 40px !important; // Increased spacing to prevent overlap
      font-style: normal !important;
      font-weight: normal !important;
      font-variant: normal !important;
      text-transform: none !important;
      line-height: 1 !important;
      -webkit-font-smoothing: antialiased !important;
      flex-shrink: 0 !important;
    }

    &.type-input, &.type-info, &.type-question {
      background-color: @main-color !important;
    }

    &.type-input::before { content: 'input' !important; }
    &.type-info::before { content: 'info' !important; }
    &.type-question::before { content: 'help' !important; }

    &.type-warning {
      background-color: @warning-color !important;
      &::before { content: 'warning' !important; }
    }

    &.type-error {
      background-color: @error-color !important;
      &::before { content: 'cancel' !important; }
    }
  }

  .mdc-dialog__content {
    padding: 24px 24px 32px !important;

    > p, > label {
      margin-top: 0 !important;
      margin-bottom: 16px !important;
    }
  }

  .mat-mdc-form-field {
    width: 100% !important;
    margin-top: 8px !important;
  }

  .mdc-dialog__actions {
    padding: 8px 24px 24px !important;
    justify-content: flex-end !important;
  }

  // Button styles
  .mat-mdc-dialog-actions .mdc-button {
    &.mat-primary {
      background-color: @main-color !important;
      color: @color-white !important;
    }
  }
}

.mat-mdc-dialog-container {
  &.type-input, &.type-info, &.type-question {
    .mat-mdc-dialog-actions .mdc-button.mat-primary {
      background-color: @main-color !important;
    }
  }

  &.type-warning {
    .mat-mdc-dialog-actions .mdc-button.mat-primary {
      background-color: @warning-color !important;
    }
  }

  &.type-error {
    .mat-mdc-dialog-actions .mdc-button.mat-primary {
      background-color: @error-color !important;
    }
  }
}

.svy-dialog {
  border-radius: 6px;

  .window-header {
    background: @main-color !important;
    color: @main-color-inverse !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
    padding: 12px 24px !important;
    display: flex !important;
    align-items: center !important;
    text-transform: capitalize !important;
    position: relative !important; // Added for absolute positioning of close button
    
    // Custom icon
    &::before {
      font-family: 'Material Symbols Outlined' !important;
      content: 'find_in_page' !important;
      font-size: 24px !important;
      margin-right: 16px !important;
      font-style: normal !important;
      font-weight: normal !important;
      font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24 !important;
      line-height: 1 !important;
      -webkit-font-smoothing: antialiased !important;
      flex-shrink: 0 !important;
      display: inline-block !important;
    }
    
    .window-title {
      color: @main-color-inverse !important;
      font-size: 18px !important;
      font-weight: 500 !important;
      text-transform: capitalize !important;
      flex: 1 !important;
    }

    // Close button styling
    button.svy-dialog-close {
      position: absolute !important;
      right: 24px !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      background: none !important;
      border: none !important;
      color: @main-color-inverse !important;
      font-size: 20px !important;
      padding: 4px !important;
      cursor: pointer !important;
      opacity: 0.8 !important;
      
      &:hover {
        opacity: 1 !important;
      }
    }
  }

  .window-body {
    background: @color-white !important;
    padding: 24px 24px 32px !important;
  }

  .window-footer {
    background: @color-white !important;
    border-bottom-left-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
    padding: 8px 24px 24px !important;
    //padding: 0px !important;
    display: flex !important;
    justify-content: flex-end !important;

    button {
      margin-left: 8px !important;
      text-transform: uppercase !important;
      font-weight: 500 !important;

      &.primary {
        background-color: @main-color !important;
        color: @main-color-inverse !important;
      }
    }
  }
}

.svy-toolbar-filter{
	.btn-group{
		height: 30px;
	}
}

/* Scrollbars - using the variables defined earlier */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  padding: 0 5px;
}

::-webkit-scrollbar-track {
  border-radius: 0px;
  background: @scrollbar-track-color;
  border: 1px solid @scrollbar-track-border-color;
  border-width: 0 1px;
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: @scrollbar-thumb-color;  
}

/* For Firefox (scrollbar-width and scrollbar-color) */
{
  scrollbar-width: thin;
  scrollbar-color: @scrollbar-thumb-color @scrollbar-track-color;
}

/* Old Avanti styles - already commented out, keeping for reference */

/* Additional widget header styles - refactored to use variables */
.widget-header {
  &_bkgnd-gray {
    border: 1px solid @color-gray-light;
    background-color: @color-gray-light;
  }

  &_bkgnd-blue {
    border: 1px solid @color-blue-light;
    background-color: @color-blue-light;
  }

  &_text-white {
    font: 9px "Helvetica";
    font-weight: bold;
    color: @color-white;
  }
}

/*JSChart*/
// Chart-specific colors
@border-color: @color-gray-lighter;       // Border color
@background-color: @color-white;          // Background color 

// Chart.js specific variables
@chart-tooltip-bg: fade(@text-color, 70%);
@chart-tooltip-color: @background-color;
@chart-tooltip-border-radius: 3px;
@chart-tooltip-padding: 6px;
@chart-tooltip-font-size: 12px;

@chart-legend-margin: 8px;
@chart-legend-font-size: 12px;
@chart-legend-item-size: 12px;
@chart-legend-item-spacing: 4px;

@chart-small-height: 300px;
@chart-medium-height: 400px;
@chart-large-height: 500px;

@chart-pie-max-width: 400px;
@chart-container-min-height: 300px;

// Default chart colors using variables from our color palette
@chart-colors: @primary-color, @secondary-color-btn, @color-red, @color-yellow-medium, @color-purple,
               @color-green-dark, @color-orange, @color-blue-medium, @color-gray, @color-blue;

// Chart.js specific styles for Servoy implementation
.chart-container {
    position: relative;
    width: 100%;
    background-color: @background-color;
    
    // Ensure proper chart sizing
    &.responsive-chart {
        height: 100%;
        min-height: @chart-container-min-height;
        
        canvas {
            // Chart.js recommends these settings for responsive charts
            max-width: 100% !important;
            height: auto !important;
        }
    }
    
    // Fixed size charts (when responsive: false)
    &.fixed-chart {
        canvas {
            // Preserve exact dimensions
            width: 100% !important;
            height: 100% !important;
        }
    }
}

// Chart.js tooltip customization
.chartjs-tooltip {
    opacity: 0;
    position: absolute;
    background: @chart-tooltip-bg;
    color: @chart-tooltip-color;
    border-radius: @chart-tooltip-border-radius;
    padding: @chart-tooltip-padding;
    font-size: @chart-tooltip-font-size;
    pointer-events: none;
    transform: translate(-50%, 0);
    transition: all .1s ease;
    z-index: 999;
    border: 1px solid @border-color;
}

// Chart.js legend styling
.chart-legend {
    margin-top: @chart-legend-margin;
    font-size: @chart-legend-font-size;
    color: @text-color;
    
    ul {
        padding: 0;
        margin: 0;
        list-style: none;
        
        li {
            display: inline-flex;
            align-items: center;
            margin-right: 10px;
            
            span {
                display: inline-block;
                width: @chart-legend-item-size;
                height: @chart-legend-item-size;
                margin-right: @chart-legend-item-spacing;
                border-radius: 2px;
            }
        }
    }
}

// Print optimization for Chart.js
@media print {
    .chart-container {
        // Chart.js print recommendations
        break-inside: avoid;
        height: auto !important;
        
        canvas {
            height: auto !important;
        }
    }
}

// Size-specific chart containers
.chart-size {
    &-small {
        max-height: @chart-small-height;
    }
    
    &-medium {
        max-height: @chart-medium-height;
    }
    
    &-large {
        max-height: @chart-large-height;
    }
}

// Chart type specific adjustments
.pie-chart-container,
.doughnut-chart-container {
    max-width: @chart-pie-max-width;
    margin: 0 auto;
    aspect-ratio: 1;
}

.line-chart-container,
.bar-chart-container {
    aspect-ratio: 16/9;
}

// Dark mode support (using your theme variables)
.dark-mode {
    .chart-container {
        background-color: darken(@background-color, 80%);
    }
    
    .chartjs-tooltip {
        background: fade(@background-color, 90%);
        color: @text-color;
        border-color: @border-color;
    }
    
    .chart-legend {
        color: lighten(@text-color, 80%);
    }
}

// Focus on accessibility
@high-contrast {
    .chart-container {
        canvas {
            border: 1px solid @text-color;
        }
    }
    
    .chartjs-tooltip {
        background: @text-color;
        color: @background-color;
        border: 2px solid @background-color;
    }
}

/*Bootstrap*/
.bts-tabpanel {
    overflow: hidden !important;
    
    .tab-content {
        overflow: hidden !important;
    }
}

.scrollable-tabpanel {
   	overflow: hidden !important;
    height: 100%; 

    .tab-content {
        overflow-y: auto !important;
        overflow-x: hidden; 
        max-height: 100%;
    }
}

.svy-wrapper > * > .bts-tabpanel > .tab-content > .tab-pane {
	min-height: auto !important;
}

/* 
Custom Components - commented out but refactored to use variables
ul.shadcn-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 100%;
    z-index: 1000;
    margin: 0;
    padding: 0;
    list-style: none;
}

.shadcn-dropdown-menu .dropdown-menu {
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    background-color: @color-white;
    border: 1px solid fadeout(@color-black, 85%);
    border-radius: 4px;
    box-shadow: 0 6px 12px fadeout(@color-black, 82.5%);
}

.shadcn-dropdown-menu .dropdown-item {
    display: flex;
    align-items: center;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: @text-color;
    white-space: nowrap;
    cursor: pointer;
}

.shadcn-dropdown-menu .dropdown-item:hover {
    background-color: @color-gray-lightest;
}

.shadcn-dropdown-menu .dropdown-divider {
    height: 1px;
    margin: 9px 0;
    background-color: @color-gray-lighter;
} */
