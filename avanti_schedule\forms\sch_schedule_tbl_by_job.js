/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"1DD9D9DC-D655-454C-9E14-817088CDC50B",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"037E0E9D-F288-41A5-8D4D-1F1966B27301"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} [event] the event that triggered the action
 *
 * @properties={typeid:24,uuid:"592947AD-EBBB-40B1-B0AD-5395AE49717B"}
 */
function onShow(firstShow, event)
{	

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	var dNow = new Date();
    
	var dTimeDiff = scopes.avDate.getDiffInSeconds(_dLastOnShow, dNow);
	
    if(_dLastOnShow && dTimeDiff < 3){    	
        return;    	
    }    
    
	
	_super.onShowForm(firstShow, event);
	
	if(show_press_ready_jobs_is_checked){	
		var array = getPressReadyJobs();
	} 
	
	globals.avBase_quickSearchCache.currentForm = 'sch_schedule_tbl_by_job'
		
	// GD - 2013-06-14: support for foundset being passed in by widget
	if (_loadFoundset){
       if(array && array.length>0) {
    	   filterRTSFoundsetWithPressReady(_loadFoundset,array);  
       }
       else {
    	   filterRTSFoundset(_loadFoundset);
       }

	   controller.loadRecords(_loadFoundset);
	   _loadFoundset = null;
	}
	else{
		if(array&& array.length>0){
			filterRTSFoundsetWithPressReady(foundset,array); 
			filterRTSFoundsetWithPressReady(forms.sch_schedule_tbl.foundset,array); 
        }
        else {
	   	    filterRTSFoundset(foundset);
	   	    filterRTSFoundset(forms.sch_schedule_tbl.foundset);
        }
	}
	
	// sl-2447 - keep user sort
	foundset.sort(_sort_col, false)
	globals.avBase_setValueListData();
	
	initQuickSearch();

    elements.grid.getColumn(elements.grid.getColumnIndex("dueDate")).format = globals.avBase_dateFormat;
	if (globals.avBase_getSystemPreference_String(scopes.avUtils.SYS_PREF.DueAtCustomerFormatInSalesOrders) == "DATETIME") {
		elements.grid.getColumn(elements.grid.getColumnIndex("promiseDate")).format = globals.avBase_dateTimeFormat;
	} 
	else {
		elements.grid.getColumn(elements.grid.getColumnIndex("promiseDate")).format = globals.avBase_dateFormat;
	}
	
	// need this set to edit in order to be able to select chkbox
	globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	
	if(firstShow){
		buildSchedDirVL()
	}
	if(!_scheduleDirection){
		_scheduleDirection = 'F'
	}
		
    _dLastOnShow = new Date();
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"67E49451-DE96-4DED-B380-DBD5146ACFBE"}
 */
var btnAddToSchedule_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.releaseThisJob');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C92423CD-4C78-49A7-863A-5F746DE3D565"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0") {
		onAction_dtl(event);
	}
	if (col.id == "btnAddToSchedule") {
		onAction_btnAddToSchedule(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"F71AB2A4-AE49-449C-A519-D87A54A874E1"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	onAction_dtl(event)
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"73F9073C-1206-4191-BA74-05FDE9369CCF"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "chkSelectJob") {
		chkSelectJob_onDataChange(oldValue, newValue, event);
	}
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E6A436DE-866E-4958-9D9A-5B6DEC3871AA"}
 */
var HC_LABEL = "Scheduling";

/**
 * @type {JSRecord<db:/avanti/sys_batch_job>}
 * 
 * @properties={typeid:35,uuid:"64707B58-DFC6-45F0-8E17-8339BFDE5CB4",variableType:-4}
 */
var _rBatchJob = null;

/**
 * @type {Boolean}
 * 
 * @properties={typeid:35,uuid:"D9F537F5-3EBD-4FF1-8F67-39EA14448BBE",variableType:-4}
 */
var bRunningHC = false;

/**
 * @type {java.util.HashSet}
 * 
 * @properties={typeid:35,uuid:"4DBC6099-D9B6-4810-8F6A-1EED23EF6E07",variableType:-4}
 */
var hsHeadlessClients = new java.util.HashSet();

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"81C3C19B-985B-4920-BA46-B0EF5C485E3D",variableType:4}
 */
var _nNumUnsuccessful = 0;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"664B521B-5751-4648-8DDC-BEFC14B83715",variableType:4}
 */
var _nNumSuccessful = 0;

/**
 * @type {UUID}
 *
 * @properties={typeid:35,uuid:"A743CFC9-B2C0-498C-A227-078F1FBBD839",variableType:-4}
 */
var _selectedSchID = null;

/**
 * @type {UUID}
 * @properties={typeid:35,uuid:"94A5099C-8D58-4B76-A960-027CE8C91EB5",variableType:-4}
 */
var _drillDownSchID = null;

/**
 * @type {JSFoundSet<db:/avanti/sch_schedule>}
 * @properties={typeid:35,uuid:"61191361-5F2B-4561-B107-3B12183A24BA",variableType:-4}
 */
var _loadFoundset = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"395AB0E8-E8B6-466B-8156-9DCC62EF30A0"}
 */
var _scheduleDirection = '';

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"95465A4C-759A-4D1A-92CE-00DCF6D1548F",variableType:4}
 */
var _selectAll = 0;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"2F4F7082-42D9-4AB6-96F1-4B68401A3B1F"}
 */
var _sort_col = 'sch_schedule_to_prod_job.job_number desc';

/**
 * @properties={typeid:35,uuid:"1E195E76-866F-4662-8669-BDC69E23AACD",variableType:-4}
 */
var show_press_ready_jobs_is_checked = false;

/**
 * @type {Date}
 * 
 * @properties={typeid:35,uuid:"4470EA41-8E86-499B-9D76-A20BA4796140",variableType:93}
 */
var _dLastOnShow = null;

/**
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"********-A9AB-4AB8-BB49-497203F08FB7"}
 */
function initQuickSearch(){
	globals.avBase_quickSearchCache.currentForm = null
	globals.svy_nav_form_name = 'sch_schedule_tbl_by_job'
//	globals.avBase_quickSearchCache[globals.svy_nav_form_name].searchObject = null	
	globals.avBase_quickSearchCache[globals.svy_nav_form_name] = null 
}

/**
 * @properties={typeid:24,uuid:"07C82C37-A476-45AB-8332-E8FEBBBF8BFA"}
 */
function buildSchedDirVL(){
	var aDisplay = new Array();
	var aReturn = new Array();
	
	aDisplay.push(i18n.getI18NMessage('i18n:avanti.lbl.scheduleForwards'));
	aDisplay.push(i18n.getI18NMessage('i18n:avanti.lbl.scheduleBackwards'));
	
	aReturn.push('F');
	aReturn.push('B');

	application.setValueListItems("avScheduleDirection", aDisplay, aReturn);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * 
 * @global {Boolean} globals.gAddingToSchedule - ref: avanti_calulation/globals.js
 *
 * @private
 *
 * @properties={typeid:24,uuid:"48E9396D-9364-4021-B97D-817E868D20FE"}
 */
function onAction_btnAddToSchedule(event) {

    globals.gAddingToSchedule = true;

    if (!_scheduleDirection) {
        globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('avanti.lbl.missingschedDir'));
    }
    else if (_to_sys_organization.org_sch_locked == 1) {
        scopes.avScheduling.showScheduleBoardLockedMessage();
    }
    else {
        var rSchedule = foundset.getSelectedRecord();
        var bSuccess = handleAddToScheduleEvent(rSchedule, _scheduleDirection, false, null);

        if (bSuccess) {
            databaseManager.saveData();
            foundset.loadAllRecords(); // refresh screen to get rid of schedule job
            scopes.avScheduling.confirmScheduleRelease(rSchedule);
        }
    }

    globals.gAddingToSchedule = false;

    return;
}

/**
 * @param {JSFoundSet<db:/avanti/sch_schedule>} fsSchedule
 *
 * @return
 * @properties={typeid:24,uuid:"04FCB476-4F73-41C5-808C-C29988F20A71"}
 */
function getNumRecordsInBatch(fsSchedule) {
	var nNumRecordsInBatch = 0;
	
    fsSchedule.sort('selected desc');
	
    for (var i = 1; i <= fsSchedule.getSize(); i++) {
        var rSchedule = fsSchedule.getRecord(i);

        if (rSchedule.selected) {
        	nNumRecordsInBatch++;
        }
        else {
        	break;
        }
    }
    
    return nNumRecordsInBatch;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {JSFoundSet<db:/avanti/sch_schedule>} [fsAutoSchedule] - fs passed from xml import auto schedule
 *
 * @private
 *
 * @properties={typeid:24,uuid:"171F0061-D74D-42B2-856B-D0F76A54D735"}
 */
function btnAddSelected_onAction(event, fsAutoSchedule) {
	scopes.avUtils.stopWatchStart("btnAddSelected_onAction");
	scopes.avScheduling.bDoingAutoSchedule = (fsAutoSchedule != null);

    var fsSchedule = fsAutoSchedule ? fsAutoSchedule : foundset;
	
    globals.gBulkReleaseToSchedule = true;
    scopes.avScheduling.bCommittingSchedule = true;

    if (!_scheduleDirection) {
        if (!fsAutoSchedule && !bRunningHC) {
            globals.DIALOGS.showWarningDialog('', i18n.getI18NMessage('avanti.lbl.missingschedDir'));
        }
    }
    else if (_to_sys_organization.org_sch_locked == 1) {
        if (!fsAutoSchedule && !bRunningHC) {
            scopes.avScheduling.showScheduleBoardLockedMessage();        
        }
    }    
    else {
        /*** @type {JSRecord<db:/avanti/sch_schedule>} */
        var rSchedule;
        /**@type {Array<UUID>} */
        var aOrdersProcessed = [];
        /**@type {Array<JSRecord<db:/avanti/sch_schedule>>} */
        var aOrderSchedules = [];
        /**@type {Array<UUID>} */
        var aOrderScheduleIDs = [];

        _nNumUnsuccessful = 0;
        _nNumSuccessful = 0;
        
        databaseManager.saveData(fsSchedule);

		if (!bRunningHC && !scopes.avScheduling.bDoingAutoSchedule) {
			var nMaxRecords = globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.RTSNumRecordsToUseHeadlessClient);
			var nNumRecordsInBatch = getNumRecordsInBatch(fsSchedule);

			if (scopes.avUtils.canClientUseProgressBar() && nMaxRecords && nNumRecordsInBatch >= nMaxRecords && createHeadlessClient(fsSchedule)) {
				return;
			}
		}
		else if (bRunningHC) {
			fsSchedule = scopes.avDB.getFSFromSQL("SELECT sch_id FROM sch_schedule WHERE sch_hc_rec_by_emp_id = ?", "sch_schedule", [globals.avBase_employeeUUID]);
			forms.sch_schedule_dtl.nHCCurrentStep = 0;
			forms.sch_schedule_dtl.nHCMaxSteps = databaseManager.getFoundSetCount(fsSchedule);
	        fsSchedule.sort('selected desc');
			scopes.avDB.RunSQL("UPDATE sch_schedule SET sch_hc_rec_by_emp_id = null WHERE sch_hc_rec_by_emp_id = ?", null, [globals.avBase_employeeUUID]);
		}
        
        for (var i = 1; i <= fsSchedule.getSize(); i++) {
            rSchedule = fsSchedule.getRecord(i);

            if (rSchedule.selected || fsAutoSchedule) {
                var uHeaderID = rSchedule.sch_schedule_to_prod_job.prod_job_to_sa_order_revision_detail.ordrevh_id;
                
                rSchedule.selected = null;

                // this is a new order - process the last one
                if (aOrdersProcessed.indexOf(uHeaderID) == -1) {
                    if (aOrderSchedules.length > 0 && aOrdersProcessed.length > 0) {
                        processOrder();
                    }

                    aOrdersProcessed.push(uHeaderID);
                }

                aOrderSchedules.push(rSchedule);
                aOrderScheduleIDs.push(rSchedule.sch_id);
                
            	if (bRunningHC) {
                    forms.sch_schedule_dtl.incrementHCPctComplete();
            	}
            }
            else {
            	break;
            }
        }
        
        // have to do one last time to process schedules for last order
        if (aOrderSchedules.length > 0 && aOrdersProcessed.length > 0) {
            processOrder();
        }

        if (!fsAutoSchedule) {
            if (_nNumUnsuccessful > 0) {
                globals.DIALOGS.showInfoDialog('', i18n.getI18NMessage('avanti.dialog.someJobCouldntBeSched2'), i18n.getI18NMessage('avanti.dialog.ok'));
            }
            else if (_nNumSuccessful > 0) {
                globals.DIALOGS.showInfoDialog('', i18n.getI18NMessage('avanti.dialog.JobsScheduled2'), i18n.getI18NMessage('avanti.dialog.ok'));
            }
        }

        // refresh screen to get rid of scheduled jobs
        if (_nNumSuccessful > 0) {
            databaseManager.saveData();
            scopes.avScheduling.notifyActiveSchedulers();
            
            if (!fsAutoSchedule) {
                fsSchedule.loadAllRecords();
            }
        }
    }

    globals.gBulkReleaseToSchedule = false;
    scopes.avScheduling.bCommittingSchedule = false;
    scopes.avScheduling.bDoingAutoSchedule = false;
	scopes.avUtils.devOutputTime("btnAddSelected_onAction");

    function processOrder() {
    	try {
            var uLastHeaderID = aOrdersProcessed[aOrdersProcessed.length - 1];
            var aOrderGroupJobsWorkPoolIDs = scopes.avScheduling.getOrderGroupJobsWorkPools(uLastHeaderID, aOrderScheduleIDs);
            /**@type {JSRecord<db:/avanti/sa_order_revision_header>} */
            var rRevHeader = scopes.avDB.getRec("sa_order_revision_header", ['ordrevh_id'], [uLastHeaderID]);
			var bLockAquired = false;

            //  we need to use locking to prevent concurrent xml orders from fighting over scheduling logic
			if (scopes.avScheduling.waitForScheduleLock(rRevHeader.ordh_id, 60)) {
				bLockAquired = true;
			}
            
    		if (bLockAquired) {
    	        if (aOrderGroupJobsWorkPoolIDs && aOrderGroupJobsWorkPoolIDs.length > 0) {
    	            scheduleOrderGroupBy(aOrderSchedules, aOrderScheduleIDs, aOrderGroupJobsWorkPoolIDs, rRevHeader.ordrevh_expected_date);
    	        }
    	        else {
    	            scheduleOrder(aOrderSchedules);
    	        }
    	        
    			if (bLockAquired) {
    	            scopes.avScheduling.releaseScheduleLock(rRevHeader.ordh_id);
    			}
    		}

            aOrderSchedules = [];
            // sl-17857 - this array needs to be cleared after processing each order or the milestones for the previous orders will be changed 
            aOrderScheduleIDs = [];
    	}
    	catch (ex) {
			scopes.avUtils.devLogError("processOrder()", ex);
    		
			if (bLockAquired) {
	            scopes.avScheduling.releaseScheduleLock(rRevHeader.ordh_id);
			}
    	}
    }
}

/**
 * @param {Array<JSRecord<db:/avanti/sch_schedule>>} aOrderSchedules
 * @param {Boolean} [bGroupJobs]
 * @param {Boolean} [bValidateLoadBalanced]
 *
 * @return
 * @properties={typeid:24,uuid:"258BBA0F-2856-430F-8B37-A9D7A059ADDF"}
 */
function scheduleOrder(aOrderSchedules, bGroupJobs, bValidateLoadBalanced) {
    var bSuccess = false;
    var bHadAFail = false;
    var bDontFailOnValidateMilestones = bGroupJobs || bValidateLoadBalanced;

    if (bGroupJobs) {
        scopes.avScheduling._aWorkPoolSchedTimes = [];
    }

    for (var i = 0; i < aOrderSchedules.length; i++) {
        var rSchedule = aOrderSchedules[i];
        
        if (_scheduleDirection == 'F') {
            bSuccess = scheduleMilestones(rSchedule, _scheduleDirection, application.getTimeStamp(), true, bDontFailOnValidateMilestones);
        }
        else if (!globals.jobCannotPossiblyCompleteByDueDate(rSchedule, false, 'B')) {
            bSuccess = scheduleMilestones(rSchedule, _scheduleDirection, null, true, bDontFailOnValidateMilestones);
        }

        if (bSuccess) {
            _nNumSuccessful++;

            if (!bGroupJobs && !bValidateLoadBalanced) {
                commitSchedule(rSchedule, true);
            }
        }
        else {
            _nNumUnsuccessful++;
            bHadAFail = true;
            
            // if bGroupJobs || bValidateLoadBalanced then if any line items fail the whole order fails - have to exit
            if (bGroupJobs || bValidateLoadBalanced) {
                break;
            }
        }
    }

    if (bGroupJobs) {
        scopes.avScheduling._aWorkPoolSchedTimes = [];
    }

    return !bHadAFail;
}

/**
 * @param {Array<JSRecord<db:/avanti/sch_schedule>>} aOrderSchedules
 * @param {Array<UUID>} aOrderScheduleIDs
 * @param {Array<UUID>} aOrderGroupJobsWorkPoolIDs
 * @param {Date} dExpectedShipDate
 *
 * @properties={typeid:24,uuid:"D4D0C24D-9E8C-4E22-94BE-7966993E498D"}
 */
function scheduleOrderGroupBy(aOrderSchedules, aOrderScheduleIDs, aOrderGroupJobsWorkPoolIDs, dExpectedShipDate) {
    if (aOrderSchedules.length > 0 && aOrderScheduleIDs.length > 0 && aOrderGroupJobsWorkPoolIDs.length > 0) {
        try {
            scopes.avScheduling._bGroupJobs = true;
            var sOrderScheduleIDs = scopes.avText.arrayToString(aOrderScheduleIDs, ",", "'");
            var sOrderGroupJobsWorkPoolIDs = scopes.avText.arrayToString(aOrderGroupJobsWorkPoolIDs, ",", "'");
            var sSQL = "select ms_id \
                        from sch_milestone ms \
                        inner join eq_equipment eq on eq.opcat_id = ms.opcat_id and eq.lag_parent IS NULL \
                        inner join sch_workpool_machine wpm on wpm.wrkpoolmach_mach_uuid = eq.equip_id \
                        where ms.org_id = ? and ms.sch_id in (" + sOrderScheduleIDs + ") \
                        and wpm.wrkpool_id in (" + sOrderGroupJobsWorkPoolIDs + ")";
            var aArgs = [globals.org_id];
            /**@type {JSFoundSet<db:/avanti/sch_milestone>} */
            var fsMS = scopes.avDB.getFSFromSQL(sSQL, "sch_milestone", aArgs);
            var i, j;
            var aAllWorkPoolMachines = [];
            var aFstContiguousWorkPoolMachinePerm = null;
            var aWorkPoolMachinesProcessed = [];
            var bCommitted = false;
            var bDoLateOrderCheck = true;
            var bReverted = false;
            var bHaveAPoolThatUsesLoadBalancing = false;
            /**@type {Array<Array<String>>} */
            var aNoLatePerms = [];
            /**@type {Array<Array<String>>} */
            var aContiguousPerms = [];
            var uWorkPoolID;
            var rMS;
            
            if (!dExpectedShipDate) {
                bDoLateOrderCheck = false;
            }
            else if (globals.orderCannotPossiblyCompleteByDueDate(aOrderSchedules, dExpectedShipDate)) {
                bDoLateOrderCheck = false;
            }

            // loop thru pools
            for (i = 0; i < aOrderGroupJobsWorkPoolIDs.length; i++) {
                var sWorkPoolID = aOrderGroupJobsWorkPoolIDs[i].toString();
                /**@type {JSRecord<db:/avanti/sch_workpool>} */
                var rWorkPool = scopes.avDB.getRec("sch_workpool", ['wrkpool_id'], [sWorkPoolID]); 
                /**@type {JSFoundSet<db:/avanti/sch_workpool_machine>} */
                var fsWorkPoolMachines = rWorkPool.sch_workpool_to_sch_workpool_machine;
                var aWorkPoolMachines = [];
                
                if (!bHaveAPoolThatUsesLoadBalancing) {
                    bHaveAPoolThatUsesLoadBalancing = rWorkPool.wrkpool_flg_load_bal == 1;
                }
                
                fsWorkPoolMachines.sort('sequence_nr asc');

                // loop thru machines
                for (j = 1; j <= fsWorkPoolMachines.getSize(); j++) {
                    var rWorkPoolMachine = fsWorkPoolMachines.getRecord(j);
                    var rEquip = rWorkPoolMachine.sch_workpool_machine_to_eq_equipment.getRecord(1);
                    var sDeptID = rWorkPoolMachine.sch_workpool_machine_to_eq_equipment.dept_id;
                    var sOpCatID = rWorkPoolMachine.sch_workpool_machine_to_eq_equipment.opcat_id;
                    var sWorkPoolMachine = sWorkPoolID + '|' + sDeptID + '|' + sOpCatID;

                    // there shouldnt be more than 1 equip with the same opcat - but make sure
                    if (rEquip.equip_active && aWorkPoolMachinesProcessed.indexOf(sWorkPoolMachine) == -1) {
                        aWorkPoolMachines.push(sWorkPoolMachine);
                    }
                }

                // add this array of wp machine keys to All array
                aAllWorkPoolMachines.push(aWorkPoolMachines);
            }

            // get all permutations of work pool machines
            /**@type {Array<Array<String>>} */
            var aAllWorkPoolMachinePerms = scopes.avUtils.getAllPermutationsOfArrays(aAllWorkPoolMachines);

            // loop thru permutations and assign machines to milestones then try to schedule
            for (i = 0; i < aAllWorkPoolMachinePerms.length; i++) {
                /**@type {Array<String>} */
                var aWorkPoolMachinePerm = aAllWorkPoolMachinePerms[i];
                copyWorkPoolMachines(true);

                if (scheduleOrder(aOrderSchedules, true)) {
                    // if we got here then we were able to sched all group job pool contiguously - save fst
                    if (!aFstContiguousWorkPoolMachinePerm) {
                        aFstContiguousWorkPoolMachinePerm = aWorkPoolMachinePerm;
                    }

                    // if we got here we can schedule all relevant ms contiguously with this permutation
                    if (bDoLateOrderCheck && isOrderLate()) {
                        if (bHaveAPoolThatUsesLoadBalancing) {
                            aContiguousPerms.push(aWorkPoolMachinePerm);
                        }
                    }
                    else {
                        if (bHaveAPoolThatUsesLoadBalancing) {
                            aNoLatePerms.push(aWorkPoolMachinePerm);
                        }
                        else {
                            commitAllSchedules();
                            break;
                        }
                    }
                }
                
                // if we didnt commit we need to clear the capacity recs
                scopes.avScheduling.clearOrderCapacity(aOrderSchedules, true, true);

                _nNumSuccessful = 0;
                _nNumUnsuccessful = 0;
            }

            scopes.avScheduling._bGroupJobs = false;
            
            // if using Load Balancing then we dont commit as soon as we find a machine that works. we have to go thru all machines 
            if (bHaveAPoolThatUsesLoadBalancing) {
                // if only 1 of aNoLatePerms or aContiguousPerms use that - doesnt matter if it can load balance or not
                if (aNoLatePerms.length == 1) {
                    aFstContiguousWorkPoolMachinePerm = aNoLatePerms[0];
                }
                else {
                    scopes.avScheduling._bValidateLoadBalanced = true;
                    
                    if (aNoLatePerms.length > 1) {
                        for (i = 0; i < aNoLatePerms.length; i++) {
                            aWorkPoolMachinePerm = aNoLatePerms[i];
                            copyWorkPoolMachines(false);

                            if (scheduleOrder(aOrderSchedules, false, true)) {
                                commitAllSchedules();
                                break;
                            }
                            else{
                                // if we didnt commit we need to clear the capacity recs
                                scopes.avScheduling.clearOrderCapacity(aOrderSchedules, true, true);
                                _nNumSuccessful = 0;
                                _nNumUnsuccessful = 0;
                            }
                        }
                    }
                    
                    if (!bCommitted && aContiguousPerms.length > 1) {
                        for (i = 0; i < aContiguousPerms.length; i++) {
                            aWorkPoolMachinePerm = aContiguousPerms[i];
                            copyWorkPoolMachines(false);

                            if (scheduleOrder(aOrderSchedules, false, true)) {
                                commitAllSchedules();
                                break;
                            }
                            else{
                                // if we didnt commit we need to clear the capacity recs
                                scopes.avScheduling.clearOrderCapacity(aOrderSchedules, true, true);
                                _nNumSuccessful = 0;
                                _nNumUnsuccessful = 0;
                            }
                        }
                    }
                    
                    scopes.avScheduling._bValidateLoadBalanced = false;
                    scopes.avScheduling._uValidateLoadBalancedEquipIDOverride = null;
                }
            }
            
            if (!bCommitted) {
                // if we didnt commit we need to clear the capacity recs
                scopes.avScheduling.clearOrderCapacity(aOrderSchedules, true, true);
                
                // use FstContiguousWorkPoolMachinePerm if one exists
                if (aFstContiguousWorkPoolMachinePerm) {
                    aWorkPoolMachinePerm = aFstContiguousWorkPoolMachinePerm;
                    copyWorkPoolMachines(false);
                    scheduleOrder(aOrderSchedules);
                }
                // cant do contiguous - revert to original ms equip
                else {
                    revertMilestones();
                    scheduleOrder(aOrderSchedules);
                    bReverted = true;
                }
            }

            if (!bReverted) {
                updateMilestoneOpNames();
            }
        }
        catch (ex) {
            application.output('Error in scheduleOrderGroupBy: ' + ex.message + '. Call stack: ' + ex.stack, LOGGINGLEVEL.ERROR);
        }
        finally {
            scopes.avScheduling._bGroupJobs = false;
            scopes.avScheduling._bValidateLoadBalanced = false;
        }
    }

    function copyWorkPoolMachines(bBackupMS) {
        for (j = 1; j <= fsMS.getSize(); j++) {
            rMS = fsMS.getRecord(j);
            uWorkPoolID = rMS.sch_milestone_to_eq_equipment$dept_opcat.eq_equipment_to_sch_workpool_machine.wrkpool_id;

            getWorkPoolMachine(bBackupMS && i == 0);
        }
    }
    
    function getWorkPoolMachine(bBackupMilestone) {
        for (var p = 0; p < aWorkPoolMachinePerm.length; p++) {
        	var sUUIDs;
        	
			if (Array.isArray(aWorkPoolMachinePerm[p])) {
				sUUIDs = aWorkPoolMachinePerm[p][0];
			}
			else {
				sUUIDs = aWorkPoolMachinePerm[p];
			}
        	
            var aPoolAndMachine = sUUIDs.split('|');
            var sPool = aPoolAndMachine[0];
            var sDept = aPoolAndMachine[1];
            var sOpCat = aPoolAndMachine[2];

            if (sPool == uWorkPoolID) {
                if (bBackupMilestone) {
                    rMS.dept_id_bak = rMS.dept_id;
                    rMS.opcat_id_bak = rMS.opcat_id;
                }

                rMS.dept_id = sDept;
                rMS.opcat_id = sOpCat;
                rMS.group_jobs = 1;

                break;
            }
        }
    }

    function isOrderLate() {
        var rLastSchedule = aOrderSchedules[aOrderSchedules.length - 1];

        rLastSchedule.sch_schedule_to_sch_milestone.sort('sequence_nr desc');

        var rLastMilestone = rLastSchedule.sch_schedule_to_sch_milestone.getRecord(1);

        if (dExpectedShipDate && rLastMilestone.ms_date_due > dExpectedShipDate) {
            return true;
        }
        else {
            return false;
        }
    }

    function revertMilestones() {
        for (j = 1; j <= fsMS.getSize(); j++) {
            rMS = fsMS.getRecord(j);

            rMS.group_jobs = 0;

            if (rMS.dept_id_bak) {
                rMS.dept_id = rMS.dept_id_bak;
                rMS.dept_id_bak = null;
            }
            if (rMS.opcat_id_bak) {
                rMS.opcat_id = rMS.opcat_id_bak;
                rMS.opcat_id_bak = null;
            }
        }
    }

    function updateMilestoneOpNames() {
        for (j = 1; j <= fsMS.getSize(); j++) {
            rMS = fsMS.getRecord(j);

            if (rMS.opcat_id != rMS.opcat_id_bak) {
                rMS.ms_opcat_modified = 1;
                rMS.ms_opcat_modified_by = "scheduleOrderGroupBy";

                var sNewOpCatID = rMS.opcat_id.toString();
                var fsMSG = rMS.sch_milestone_to_sch_milestone_group;
                var sCCSQL = "SELECT cl.cc_id \
                              FROM sa_task_cost_link cl \
                              INNER JOIN sys_cost_centre cc ON cc.cc_id = cl.cc_id \
                              WHERE cc.opcat_id = ? AND cl.taskoper_id = ? AND cl.org_id = ?";

                for (var msg = fsMSG.getSize(); msg >= 1; msg--) {
                    var rMSG = fsMSG.getRecord(msg);
                    var sTaskOperID = rMSG.sch_milestone_group_to_sa_task_cost_link.taskoper_id.toString();

                    // find the cc for the new opcat and taskoperation (eg. Run)
                    rMSG.cc_id = scopes.avDB.SQLQuery(sCCSQL, null, [sNewOpCatID, sTaskOperID, globals.org_id]);
                    
                    // the new opcat doesnt have a matching cost link - delete ms group
                    if (!rMSG.cc_id) {
                        fsMSG.deleteRecord(rMSG);
                    }
                }
                
                rMS.ms_oper_name = scopes.avScheduling.getNewMilestoneOperName(rMS);
            }

            rMS.dept_id_bak = null;
            rMS.opcat_id_bak = null;
        }
    }
    
    function commitAllSchedules() {
        // commit schedules
        for (var k = 0; k < aOrderSchedules.length; k++) {
            var rSchedule = aOrderSchedules[k];

            // dont commit schedule if milestone validation failed
            if (!rSchedule.sch_validate_milestones_failed) {
                commitSchedule(rSchedule, true);

                if (application.isInDeveloper()) {
                    application.output('ADDED JOB TO SCHEDULE: ' + rSchedule.sch_schedule_to_prod_job.job_number);
                }
            }
        }

        bCommitted = true;
    }
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8035B82F-4515-4383-A17D-1A674A22CCA7"}
 */
function chkSelectAll_onAction(event) {
	var tiNumRecs = databaseManager.getFoundSetCount(foundset);

	for(var i=1;i<=tiNumRecs;i++){
		foundset.getRecord(i).selected = _selectAll;
	}
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"55E48F25-5BB3-400B-8613-DDD50E1A16C4"}
 */
function chkSelectJob_onDataChange(oldValue, newValue, event) {
	if(_selectAll && !newValue){
		_selectAll=newValue;
	}
	return true;
}

/** *
 * @param _event
 * @param _form
 *
 * @return
 * @properties={typeid:24,uuid:"8E8A3799-A6AF-4504-AAFA-11B4C528AE49"}
 */
function onRecordSelection(_event, _form) {
	_selectedSchID = sch_id ;
	forms.sch_schedule_tbl.foundset.selectRecord(foundset.sch_id);
	return _super.onRecordSelection(_event, _form);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B36A7347-C9FF-4F11-9432-8CDA5DE2D210"}
 * @AllowToRunInFind
 */
function onAction_dtl(event) {
	_drillDownSchID = sch_id // need to store in var as sch_id will be cleared out when i enter find mode
	
	if(forms.sch_schedule_tbl.foundset.find()){
		forms.sch_schedule_tbl.foundset.sch_id = _drillDownSchID;
		forms.sch_schedule_dtl._formBeingCalledFrom = controller.getName();
		if(forms.sch_schedule_tbl.foundset.search()){
			if(forms.sch_schedule_tbl.foundset.selectRecord(_drillDownSchID)){
				globals.svy_nav_toggleView(event);
			}
		}
	}
}

/**
 * Perform sort.
 *
 * @param {String} dataProviderID element data provider
 * @param {Boolean} asc sort ascending [true] or descending [false]
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"E9DF8C94-2FF7-4871-B492-5D5A3388392B"}
 */
function onSort(dataProviderID, asc, event) {
	_sort_col = dataProviderID + (asc ? ' asc' : ' desc'); 
	foundset.sort(_sort_col, false);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"4EE49F2D-D677-4F56-91E8-B967378A0244"}
 */
function onCheckShowPressReadyCheckBox(event) {
	if (show_press_ready_jobs_is_checked) {
		 var filterArr = new Array();
		filterArr = getPressReadyJobs();
		foundset.addFoundSetFilterParam("sch_id","sql:in",filterArr,'press_ready_status_id');
		foundset.loadAllRecords();
	}else {
		 foundset.removeFoundSetFilterParam('press_ready_status_id');
		 //forms.sch_schedule.foundset.removeFoundSetFilterParam("press_ready_status_id");
		 foundset.loadAllRecords();
	}
}

/**
 * @param {JSFoundSet<db:/avanti/sch_schedule>} fsSchedule
 *
 * @return
 * @properties={typeid:24,uuid:"34E7B01F-5437-42B1-BCE3-4DA645601344"}
 */
function createHeadlessClient (fsSchedule) {
	/** @type {{ 
	 *  username:String,
	 *  password:String
	 *  }} */
    var user_info = globals.avBase_getServoyUser('jdf_process_jobs_user');
	
    /** @type {{ 
	 *  sOrgId:String,
	 *  uEmpID:UUID,
	 *  sScheduleDirection:String
     *  }} */
    var oParams = {};
    var aParams = new Array();
    
    oParams.sOrgId = globals.org_id;
    oParams.uEmpID = globals.avBase_employeeUUID;
    oParams.sScheduleDirection = _scheduleDirection;

    var hcWebToPrint = plugins.headlessclient.createClient("avanti_schedule", user_info.username, user_info.password, null);
        
    if (hcWebToPrint != null && hcWebToPrint.isValid()) {
		scopes.avDB.updateFS(fsSchedule, ["sch_hc_rec_by_emp_id"], [globals.avBase_employeeUUID]);
    	
    	var sClientId = hcWebToPrint.getClientID();
    	hsHeadlessClients.add(sClientId);
        aParams.push(oParams);
        hcWebToPrint.queueMethod(null, "scheduleUsingHC", aParams, headlessClientCallBack);

        forms.sch_schedule_dtl.bRunningHC = true;
        
		globals.nav.mode = "browse";
		globals.nav.fromTree = 1;
		globals.svy_nav_bookmarkGoto(null, "Schedule", null, 0);

		// i couldnt get the progress bar to work on tbl view, so moved it to dtl view. we switch to dtl view to show pb then come back when done
		forms.sch_schedule_dtl.showShield(HC_LABEL, globals.avBase_employeeUUID);
        
        return true;
    }
	// couldnt create headless client 
    else {
		if (scopes.avText.showYesNoQuestion("headlessClientCouldntBeCreated", null, null, null, [scopes.avText.getLblMsg("jobOperations")]) == scopes.avText.yes) {
			return false;
		}
		else {
			return true;
		}
    }
}

/**
 * @param {{ 
 *  sOrgId:String,
 *  uEmpID:UUID,
 *  sScheduleDirection:String
 *  }} oParams
 *
 * @properties={typeid:24,uuid:"459DEEED-3BBC-4A31-8093-E7B906D3F1F9"}
 */
function runScheduleHC(oParams) {
	scopes.avUtils.stopWatchStart("runScheduleHC");	
	bRunningHC = true;
	
	// copy params back to form
	forms.sch_schedule_dtl.uShieldBatchID = oParams.uEmpID;
    _scheduleDirection = oParams.sScheduleDirection;
	
    _rBatchJob = scopes.avUtils.getBatchJob(forms.sch_schedule_dtl.uShieldBatchID, HC_LABEL);

	if (!_rBatchJob) {
		_rBatchJob = scopes.avDB.newRecord("sys_batch_job");
	}

	_rBatchJob.sysbatch_start_time = application.getTimeStamp();
	_rBatchJob.sysbatch_type = HC_LABEL;
	_rBatchJob.sysbatch_fk_id = oParams.uEmpID;
	
	databaseManager.saveData(_rBatchJob);    	

	btnAddSelected_onAction(null);

	bRunningHC = false;
}

/**
 * @param jsEvent
 * @param {plugins.headlessclient.JSClient} jsClient
 *
 * @properties={typeid:24,uuid:"03751D7F-BA59-4D1F-98B1-0AA6FB839113"}
 */
function headlessClientCallBack(jsEvent, jsClient) {
	hsHeadlessClients.remove(jsClient.getClientID());
    
    if (jsClient && jsClient.isValid()) {
        jsClient.shutdown(true);
    }
    
    
	forms.sch_schedule_dtl.hideShield();

	// bring it back to tbl view
	if (globals.nav.getCurrentFormName() == "sch_schedule_dtl") {
		globals.nav.mode = "browse";
		globals.nav.fromTree = 1;
		globals.svy_nav_bookmarkGoto(null, "Schedule", null, 2);
	}
	
    _selectAll = null;
}

/**
 * @param {String} sText
 *
 * @properties={typeid:24,uuid:"********-0EB9-4AC8-9862-8932BFF0D1A6"}
 */
function devOutput(sText) {
	if (application.isInDeveloper()) {
		application.output(sText);
	}
}
