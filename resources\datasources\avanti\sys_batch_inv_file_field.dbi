columns:[
{
allowNull:false,
autoEnterSubType:3,
autoEnterType:2,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:37,
length:36,
name:"biff_id"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"abif_id"
},
{
allowNull:false,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
name:"bif_id"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,20,0]]",
creationOrderIndex:-1,
dataType:12,
length:20,
name:"biff_bi_data_table_field_name"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_bi_data_table_field_num"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,100,0]]",
creationOrderIndex:-1,
dataType:12,
length:100,
name:"biff_col_header"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_length"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_order_num"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_position"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,4,0]]",
creationOrderIndex:-1,
dataType:12,
length:4,
name:"biff_sort_dir"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_sort_num"
},
{
allowNull:true,
creationOrderIndex:-1,
dataType:4,
name:"biff_use_udv"
},
{
allowNull:true,
compatibleColumnTypes:"[[-9,50,0],[-9,250,0]]",
creationOrderIndex:-1,
dataType:12,
length:250,
name:"biff_user_defined_value"
},
{
allowNull:false,
autoEnterType:2,
compatibleColumnTypes:"[[-9,36,0]]",
creationOrderIndex:-1,
dataType:1,
flags:36,
length:36,
name:"org_id"
},
{
allowNull:true,
autoEnterType:2,
creationOrderIndex:-1,
dataType:4,
name:"sequence_nr"
}
],
name:"sys_batch_inv_file_field",
tableType:0