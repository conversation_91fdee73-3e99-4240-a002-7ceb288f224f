borderType:"EmptyBorder,0,0,0,0",
customProperties:"useCssPosition:true",
extendsID:"70D7B8B0-256B-4D6F-A5F8-413B516FE14A",
items:[
{
cssPosition:"326,-1,-1,169,190,20",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"169",
right:"-1",
top:"326",
width:"190"
},
dataProviderID:"includeSelectedCustomerParts",
enabled:true,
formIndex:7,
onDataChangeMethodID:"FBAED6AC-0AAE-46AF-AB2F-B8D34DF4F681",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.IncludeSelectedCustomerPartNo",
visible:true
},
name:"chkSelectedCustomersParts",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"0A3EFF89-6558-4C44-A066-630A3DF67939"
},
{
cssPosition:"240,-1,-1,423,25,22",
formIndex:34,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"423",
right:"-1",
top:"240",
width:"25"
},
enabled:true,
formIndex:34,
labelFor:"_toItem",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toItemGroup_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"12047A03-6FC4-4F12-97BE-C12DFCD8955B"
},
{
cssPosition:"186,-1,-1,390,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'itemclass_code'\",\
\"'Item Classes'\",\
\"'afterFromItemClassLookup'\"\
],\
parameters:[]\
}\
}",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"390",
right:"-1",
top:"186",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItemClass_From",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"14C52D29-46CC-4A7F-BD45-182F7E5A5BEA"
},
{
cssPosition:"353,-1,-1,453,220,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"453",
right:"-1",
top:"353",
width:"220"
},
dataProviderID:"toCustomerParts",
editable:true,
enabled:true,
formIndex:4,
onDataChangeMethodID:"85E9459F-3B3A-4CEA-B26F-5AD8D8150047",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"A1E73F98-2F03-49A1-AE87-12A821D5FB5D",
visible:true
},
name:"toCustomerParts",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"156971C5-CB7E-432D-AB1C-5790CD5C5879"
},
{
cssPosition:"296,-1,-1,426,25,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"426",
right:"-1",
top:"296",
width:"25"
},
enabled:true,
formIndex:1,
labelFor:"toParentCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"toParentCustomer_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"19B44BF4-6626-49A0-8994-716779F5378C"
},
{
cssPosition:"10,-1,-1,5,237,22",
formIndex:26,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"10",
width:"237"
},
enabled:true,
formIndex:26,
labelFor:"fromDiv",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.division",
visible:true
},
name:"fromDiv_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"288D38E4-79B9-4FE0-8402-476489B8502E"
},
{
cssPosition:"129,-1,-1,451,218,20",
formIndex:25,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"451",
right:"-1",
top:"129",
width:"218"
},
dataProviderID:"_toDate",
enabled:true,
formIndex:25,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"_toDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"29A27DE0-7724-4364-8B12-************"
},
{
cssPosition:"268,-1,-1,4,160,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"4",
right:"-1",
top:"268",
width:"160"
},
dataProviderID:"includeAllParentCustomers",
enabled:true,
formIndex:7,
onDataChangeMethodID:"96758648-8F46-454F-A775-A299AEDD62CE",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeAllParentCustomers",
visible:true
},
name:"chkAllParentCustomers",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"2D5306A0-72F4-4571-BA99-7BD49BA2912C"
},
{
cssPosition:"296,-1,-1,453,220,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"453",
right:"-1",
top:"296",
width:"220"
},
dataProviderID:"toParentCustomer",
editable:true,
enabled:true,
formIndex:4,
onDataChangeMethodID:"D65E76A3-DB8A-470D-B75E-1296C8822E76",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"959B6E10-C498-4ED8-B88F-674E2EE7F302",
visible:true
},
name:"toParentCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"2DF425DB-215D-4B10-9FCE-D8D58F8FA087"
},
{
cssPosition:"353,-1,-1,676,24,22",
customProperties:"",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"676",
right:"-1",
top:"353",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"C798514C-6B25-41BB-9B7D-2ECA4044D00A",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupToCustomerParts",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3221F641-9B6D-4969-8166-BACB7CF677D1"
},
{
cssPosition:"269,-1,-1,169,190,20",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"169",
right:"-1",
top:"269",
width:"190"
},
dataProviderID:"includeSelectedParentCustomers",
enabled:true,
formIndex:7,
onDataChangeMethodID:"743A3292-6F66-4080-B039-9CA0C5623DFE",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeSelectedParentCustomers",
visible:true
},
name:"chkSelectedParentCustomers",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"323C3594-7676-44B1-A8B8-0BAE33E04B02"
},
{
cssPosition:"186,-1,-1,451,220,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"451",
right:"-1",
top:"186",
width:"220"
},
dataProviderID:"_toItemClass",
editable:true,
enabled:true,
formIndex:7,
onDataChangeMethodID:"408E7DA9-5F0E-41A0-9E6A-A937EB8FDEF3",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"*************-4A76-93AD-160FE64635C0",
visible:true
},
name:"_toItemClass",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"3F4AFFEB-E034-4C66-93FA-58075E7C46B0"
},
{
cssPosition:"390,-1,-1,3,162,22",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"3",
right:"-1",
top:"390",
width:"162"
},
dataProviderID:"_includeAllItems",
enabled:true,
formIndex:11,
onDataChangeMethodID:"1E04A7A3-8C6D-49CC-90A2-F0C1E9D41556",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeAllItems",
visible:true
},
name:"_includeAllItems",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"4C2C9DF5-5A29-4D54-B578-92A9DF02694C"
},
{
cssPosition:"240,-1,-1,450,220,22",
formIndex:36,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"450",
right:"-1",
top:"240",
width:"220"
},
dataProviderID:"_toItemGroup",
editable:true,
enabled:true,
formIndex:36,
onDataChangeMethodID:"98F360B0-22A5-4BA7-84DB-9A6477FA90DD",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"959B6E10-C498-4ED8-B88F-674E2EE7F302",
visible:true
},
name:"_toItemGroup",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"4C43C357-7B84-4A6A-86B4-1936EA07C4C8"
},
{
cssPosition:"352,-1,-1,170,220,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"170",
right:"-1",
top:"352",
width:"220"
},
dataProviderID:"fromCustomerParts",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"51578DA7-9D4E-4678-ABE1-6B232555F0A9",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"A1E73F98-2F03-49A1-AE87-12A821D5FB5D",
visible:true
},
name:"fromCustomerParts",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"514E2EFE-D245-47B2-B0EF-24F50854323C"
},
{
cssPosition:"325,-1,-1,4,160,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"4",
right:"-1",
top:"325",
width:"160"
},
dataProviderID:"includeAllCustomerParts",
enabled:true,
formIndex:7,
onDataChangeMethodID:"7771FBCB-C9D9-43F3-8444-D9079A17E344",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lblincludeAllCustomerPartNo",
visible:true
},
name:"chkAllCustomersParts",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"51957E72-FE8C-42C4-AE12-01F371BF1B23"
},
{
cssPosition:"240,-1,-1,4,140,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"4",
right:"-1",
top:"240",
width:"140"
},
enabled:true,
formIndex:21,
labelFor:"_fromItemGroup",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromItemGroup",
visible:true
},
name:"_fromItemGroup_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"535544E8-CB2A-40FE-B176-99CE8F5D0CA4"
},
{
cssPosition:"10,310,-1,-1,244,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"310",
top:"10",
width:"244"
},
enabled:true,
formIndex:30,
labelFor:"fromPlant",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.plant_id",
visible:true
},
name:"fromPlant_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5961A049-41AB-45DB-A968-22870C372B0F"
},
{
cssPosition:"296,-1,-1,676,24,22",
customProperties:"",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"676",
right:"-1",
top:"296",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"B95FD5BD-C396-4A0F-8A23-8C3658D3804B",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupToParentCustomer",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"5ABE9F1D-50FB-49E8-9B5C-7CED782F74E3"
},
{
cssPosition:"33,-1,-1,5,237,82",
formIndex:27,
json:{
cssPosition:{
bottom:"-1",
height:"82",
left:"5",
right:"-1",
top:"33",
width:"237"
},
dataProviderID:"fromDiv",
enabled:true,
formIndex:27,
multiselect:"vl_DivisionshasWarehouses",
onDataChangeMethodID:"F5E211D7-33BF-4EFB-BE82-A257F8DFF89C",
selectSize:5,
styleClass:"select_bts",
tabSeq:0,
valuelistID:"B251AD75-FA26-4769-92A5-E0C773818A1C",
visible:true
},
name:"fromDiv",
styleClass:"select_bts",
typeName:"bootstrapcomponents-select",
typeid:47,
uuid:"5F5ADBC2-258C-466A-B0AD-A5FEBB8694CD"
},
{
cssPosition:"240,-1,-1,673,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'ingroup_desc'\",\
\"'Item Groups'\",\
\"'afterToItemGroupLookup'\",\
\"'lookupFilter_ItemGroupTo'\"\
],\
parameters:[]\
}\
}",
formIndex:55,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"673",
right:"-1",
top:"240",
width:"24"
},
enabled:true,
formIndex:55,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItemGroup_To",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"66BF8B88-BCC9-4FF0-B9F4-DE526673C7ED"
},
{
cssPosition:"240,-1,-1,169,220,22",
formIndex:24,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"169",
right:"-1",
top:"240",
width:"220"
},
dataProviderID:"_fromItemGroup",
editable:true,
enabled:true,
formIndex:24,
onDataChangeMethodID:"D39C34B5-E27E-4583-8770-574B0F52FABA",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"959B6E10-C498-4ED8-B88F-674E2EE7F302",
visible:true
},
name:"_fromItemGroup",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"6A36E9D5-749E-42C2-9A32-96959112F3AB"
},
{
extendsID:"9AE99E28-06F0-48DC-86E6-0E64EF901112",
height:486,
typeid:19,
uuid:"6AE08503-213D-479B-A60B-DCB917D3F65D"
},
{
cssPosition:"418,-1,-1,425,25,22",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"425",
right:"-1",
top:"418",
width:"25"
},
enabled:true,
formIndex:12,
labelFor:"_toItem",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toItem_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"730D0113-EF09-48AB-8BE6-2D3D42600188"
},
{
cssPosition:"419,-1,-1,165,220,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"165",
right:"-1",
top:"419",
width:"220"
},
dataProviderID:"_fromItem",
editable:true,
enabled:true,
formIndex:9,
onDataChangeMethodID:"539AEE91-991E-43AA-91DD-************",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"C0A3072F-A093-4E66-BE1E-5A84570EADC3",
visible:true
},
name:"_fromItem",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"73617175-EF42-4466-BBF5-3644ECC22419"
},
{
cssPosition:"326,-1,-1,453,178,20",
formIndex:86,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"453",
right:"-1",
top:"326",
width:"178"
},
enabled:true,
formIndex:86,
labelFor:"chkAllCustomersParts",
onActionMethodID:"07B5797E-681F-4C27-8FAB-7749D16A75CC",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"_selectedCustomerPartsMessage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"73FBA83D-C2D7-40CC-B437-5A4D43E0090A"
},
{
cssPosition:"129,-1,-1,422,26,20",
formIndex:24,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"422",
right:"-1",
top:"129",
width:"26"
},
enabled:true,
formIndex:24,
labelFor:"_toDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"76226F20-8CEB-4326-878B-33269D1751F8"
},
{
cssPosition:"159,-1,-1,170,202,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"170",
right:"-1",
top:"159",
width:"202"
},
dataProviderID:"includeSelectedItemClasses",
enabled:true,
formIndex:7,
onDataChangeMethodID:"9DEA9929-C3F8-4AE5-94F0-61739C0B77D1",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeSelectedItemClasses",
visible:true
},
name:"chkSelectedItemClasses",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"774BDABF-D2CD-47FD-BDB7-7F13C323E2F4"
},
{
cssPosition:"213,-1,-1,450,178,20",
formIndex:90,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"450",
right:"-1",
top:"213",
width:"178"
},
enabled:true,
formIndex:90,
labelFor:"_chkSelectedItemGroups",
onActionMethodID:"AF9B8C27-DF37-4D90-B590-021B743A65A2",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"_selectedItemGroupMessage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"78E9C8B0-66AA-4CFD-80C6-96EF22F93EC0"
},
{
height:200,
partType:5,
typeid:19,
uuid:"7941E064-AB7E-4403-A0CD-E00E68428A2E"
},
{
cssPosition:"10,32,-1,-1,244,22",
formIndex:30,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"-1",
right:"32",
top:"10",
width:"244"
},
enabled:true,
formIndex:30,
labelFor:"fromWhse",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.warehouse",
visible:true
},
name:"fromWhse_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"7BC86A34-8F3A-49F0-871C-A8177EF514A4"
},
{
cssPosition:"33,-1,-1,276,244,82",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"82",
left:"276",
right:"-1",
top:"33",
width:"244"
},
dataProviderID:"fromPlant",
enabled:true,
formIndex:31,
multiselect:"vl_Plants_rpt_dlg_base_on_div",
onDataChangeMethodID:"631D7037-A85E-4E35-A0AF-CB6A4DEEA166",
selectSize:5,
styleClass:"select_bts",
tabSeq:0,
valuelistID:"3D91C9F2-B82A-48E3-BEFD-8F23513043D0",
visible:true
},
name:"fromPlant",
styleClass:"select_bts",
typeName:"bootstrapcomponents-select",
typeid:47,
uuid:"7D1F2B7A-2E8E-48DF-BEE1-4C2D52AB43BC"
},
{
cssPosition:"186,-1,-1,170,220,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"170",
right:"-1",
top:"186",
width:"220"
},
dataProviderID:"_fromItemClass",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"4C426F54-11B0-401E-B61B-92C64826E318",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"*************-4A76-93AD-160FE64635C0",
visible:true
},
name:"_fromItemClass",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"7D3750C6-FCF9-40EA-A59E-2F827CBC4EA1"
},
{
cssPosition:"186,-1,-1,674,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'itemclass_code'\",\
\"'Item Classes'\",\
\"'afterToItemClassLookup'\",\
\"'lookupFilter_ItemClassTo'\"\
],\
parameters:[]\
}\
}",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"674",
right:"-1",
top:"186",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItemClass_To",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"80D246C1-6C98-4431-A595-60065CB2B1B3"
},
{
cssPosition:"389,-1,-1,169,220,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"169",
right:"-1",
top:"389",
width:"220"
},
dataProviderID:"includeSelectedItems",
enabled:true,
formIndex:7,
onDataChangeMethodID:"ADCCFC27-D619-47E8-BE9A-9F2F3A66DBAC",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeSelectedItems",
visible:true
},
name:"chkSelectedItems",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"851D78FB-B12A-4F0A-8D92-FB057C9267A3"
},
{
cssPosition:"296,-1,-1,392,24,22",
customProperties:"",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"392",
right:"-1",
top:"296",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"538A385F-0706-4FE0-B102-F3D7DA3FCC4B",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupFromParentCustomer",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"853222DD-EB93-4E2C-BC21-FC9B0D3766A2"
},
{
cssPosition:"186,-1,-1,422,25,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"422",
right:"-1",
top:"186",
width:"25"
},
enabled:true,
formIndex:6,
labelFor:"_toItemClass",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"_toItemClass_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"87E35D00-7E8E-47A6-9ECB-03E513580956"
},
{
cssPosition:"129,-1,-1,5,140,20",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"5",
right:"-1",
top:"129",
width:"140"
},
enabled:true,
formIndex:22,
labelFor:"_fromDate",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromDate",
visible:true
},
name:"_fromDate_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8CB33EBF-CA12-4C84-BFED-919B34FE4870"
},
{
cssPosition:"353,-1,-1,426,25,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"426",
right:"-1",
top:"353",
width:"25"
},
enabled:true,
formIndex:1,
labelFor:"toCustomerParts",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.to",
visible:true
},
name:"toCustomerParts_lbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8D26561F-D0D1-474E-84AE-72F3DB8CB84F"
},
{
cssPosition:"353,-1,-1,392,24,22",
customProperties:"",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"392",
right:"-1",
top:"353",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"3D69BB90-6F88-4518-B94D-D3F6BD284231",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupFromCustomerParts",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8F30A056-B900-4677-BCED-9AAEE5F4CF9B"
},
{
cssPosition:"186,-1,-1,5,140,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"186",
width:"140"
},
enabled:true,
formIndex:4,
labelFor:"_fromItemClass",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromItemClassCode",
visible:true
},
name:"_fromItemClass_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"92827710-2AA2-4647-A327-F03EA8439A11"
},
{
cssPosition:"159,-1,-1,5,365,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"159",
width:"365"
},
dataProviderID:"_includeAllItemClass",
enabled:true,
formIndex:3,
onDataChangeMethodID:"837C52F1-31E7-4970-919C-F2CF9C10FAD3",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeAllItemClasses",
visible:true
},
name:"_includeAllItemClass",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"99923C51-87D6-4D3B-922B-ABE425D42718"
},
{
cssPosition:"129,-1,-1,170,218,20",
formIndex:23,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"170",
right:"-1",
top:"129",
width:"218"
},
dataProviderID:"_fromDate",
enabled:true,
formIndex:23,
selectOnEnter:false,
styleClass:"calendar_bts",
tabSeq:0,
visible:true
},
name:"_fromDate",
styleClass:"calendar_bts",
typeName:"bootstrapcomponents-calendar",
typeid:47,
uuid:"9C8B4A34-B6AC-448B-BAB4-EE3F6CAD6653"
},
{
cssPosition:"269,-1,-1,453,178,20",
formIndex:86,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"453",
right:"-1",
top:"269",
width:"178"
},
enabled:true,
formIndex:86,
labelFor:"chkAllParentCustomers",
onActionMethodID:"18A9AED5-B880-483E-8525-AC9D4D69B532",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"_selectedParentCustomerMessage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9FD1D61E-07B1-47EA-BA5A-************"
},
{
cssPosition:"352,-1,-1,4,140,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"4",
right:"-1",
top:"352",
width:"140"
},
enabled:true,
formIndex:4,
labelFor:"fromCustomerParts",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromCustomerPartNo",
visible:true
},
name:"fromCustomerPartslbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A00D48D4-1A77-4212-A333-72C191D40BE9"
},
{
cssPosition:"325,-1,-1,638,116,22",
formIndex:23,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"638",
right:"-1",
top:"325",
width:"116"
},
dataProviderID:"_showInReportCustomerParts",
enabled:true,
formIndex:23,
onDataChangeMethodID:null,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.showInReport",
visible:true
},
name:"_showInReportCustomerParts",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"B30DF252-DACF-4537-977F-FC4F29F965B9"
},
{
cssPosition:"159,-1,-1,451,238,22",
formIndex:86,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"451",
right:"-1",
top:"159",
width:"238"
},
enabled:true,
formIndex:86,
labelFor:"chkSelectedItemClasses",
onActionMethodID:"82B811EE-60AE-421A-8907-E32BDD86BFB6",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"_selectedItemClassMessage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C11EF610-2C2F-42D6-A28D-094AF48992BC"
},
{
cssPosition:"33,-1,-1,554,244,82",
formIndex:31,
json:{
cssPosition:{
bottom:"-1",
height:"82",
left:"554",
right:"-1",
top:"33",
width:"244"
},
dataProviderID:"fromWhse",
enabled:true,
formIndex:31,
multiselect:"vl_Warehouses_rpt_dlg_base_on_plant",
selectSize:5,
styleClass:"select_bts",
tabSeq:0,
valuelistID:"21F0B4F9-98A0-4298-878C-7382346B9C9C",
visible:true
},
name:"fromWhse",
styleClass:"select_bts",
typeName:"bootstrapcomponents-select",
typeid:47,
uuid:"C5E8EC00-9D15-410E-A0FF-AAE4B469E75E"
},
{
cssPosition:"417,-1,-1,454,220,22",
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"454",
right:"-1",
top:"417",
width:"220"
},
dataProviderID:"_toItem",
editable:true,
enabled:true,
formIndex:13,
onDataChangeMethodID:"3D48B091-E60E-4E25-99CD-1E0049B41AEB",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"C0A3072F-A093-4E66-BE1E-5A84570EADC3",
visible:true
},
name:"_toItem",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"CD51127A-37BF-4D80-B065-CB884BE9823E"
},
{
cssPosition:"417,-1,-1,5,140,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"417",
width:"140"
},
enabled:true,
formIndex:8,
labelFor:"_fromItem",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromItemCode",
visible:true
},
name:"_fromItem_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CFD77C7D-80FD-4220-B077-179AF6973AE1"
},
{
cssPosition:"417,-1,-1,680,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'item_code'\",\
\"'Items'\",\
\"'afterToItemLookup'\",\
\"'lookupFilter_ItemTo'\"\
],\
parameters:[]\
}\
}",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"680",
right:"-1",
top:"417",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItem_To",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"CFE47571-3CA8-43B5-9A74-742A1CAEEDDD"
},
{
cssPosition:"213,-1,-1,169,190,20",
formIndex:80,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"169",
right:"-1",
top:"213",
width:"190"
},
dataProviderID:"_includeSelectedItemGroups",
enabled:true,
formIndex:80,
onDataChangeMethodID:"DDFC9DEC-B832-406A-97D8-763C349F589D",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeSelectedItemGroups",
visible:true
},
name:"_chkSelectedItemGroups",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"D4304DE6-566F-4FF9-9ED0-450340FCEFC9"
},
{
cssPosition:"213,-1,-1,4,160,22",
formIndex:28,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"4",
right:"-1",
top:"213",
width:"160"
},
dataProviderID:"_includeAllItemGroups",
enabled:true,
formIndex:28,
onDataChangeMethodID:"887C66F3-23C6-4819-A5B5-E8F9DFEB5B47",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.includeAllItemGroups",
visible:true
},
name:"_includeAllItemGroups",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"D650F681-5B2B-4CD0-B5A9-368B2E2DE2A3"
},
{
cssPosition:"298,-1,-1,168,220,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"168",
right:"-1",
top:"298",
width:"220"
},
dataProviderID:"fromParentCustomer",
editable:true,
enabled:true,
formIndex:5,
onDataChangeMethodID:"687713C9-3891-41A7-B600-B486E62C10F3",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"2F40DAA5-8D76-4209-B107-FF935FE02A3D",
visible:true
},
name:"fromParentCustomer",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"D9B83C35-E143-4F12-AF5A-D2BD33C75A3F"
},
{
cssPosition:"240,-1,-1,389,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'ingroup_desc'\",\
\"'Item Groups'\",\
\"'afterFromItemGroupLookup'\",\
\"'lookupFilter_ItemGroupFrom'\"\
],\
parameters:[]\
}\
}",
formIndex:48,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"389",
right:"-1",
top:"240",
width:"24"
},
enabled:true,
formIndex:48,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItemGroup_From",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"E0454DE5-25D0-4F66-B3ED-57D2D2A243F2"
},
{
cssPosition:"295,-1,-1,5,140,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"5",
right:"-1",
top:"295",
width:"140"
},
enabled:true,
formIndex:4,
labelFor:"fromParentCustomer",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.fromParentCustomer",
visible:true
},
name:"fromParentCustomerlbl",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"EDEF4A42-607A-409D-B2A0-9053D947B626"
},
{
cssPosition:"268,-1,-1,638,116,22",
formIndex:23,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"638",
right:"-1",
top:"268",
width:"116"
},
dataProviderID:"_showInReportParentCustomer",
enabled:true,
formIndex:23,
onDataChangeMethodID:null,
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.showInReport",
visible:true
},
name:"_showInReportParentCustomer",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"F4FCB595-D93F-40DA-B27A-7E86ECC596BE"
},
{
cssPosition:"386,-1,-1,453,220,22",
formIndex:85,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"453",
right:"-1",
top:"386",
width:"220"
},
enabled:true,
formIndex:85,
labelFor:"chkSelectedItems",
onActionMethodID:"F3AD2B41-977E-466E-82A1-4907E43D4BF5",
styleClass:"label_bts",
tabSeq:0,
visible:true
},
name:"_selectedItemMessage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FDBDDD23-51AB-48E4-8138-BF4AD08E9EC7"
},
{
cssPosition:"418,-1,-1,393,24,22",
customProperties:"methods:{\
onActionMethodID:{\
arguments:[\
null,\
\"'item_code'\",\
\"'Items'\",\
\"'afterFromItemLookup'\",\
\"'lookupFilter_ItemFrom'\"\
],\
parameters:[]\
}\
}",
formIndex:14,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"393",
right:"-1",
top:"418",
width:"24"
},
enabled:true,
formIndex:14,
onActionMethodID:"E106D87F-1106-4FB1-A67D-ED2DBE853664",
styleClass:"label_bts",
tabSeq:0,
text:"%%globals.icon_lookup%%",
toolTipText:"i18n:avanti.tooltip.lookup",
visible:true
},
name:"btnLookupItem_From",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"FF798D41-91EC-47A7-A123-6639D3517B94"
}
],
name:"rpt_report_dlg_InventoryUsageReport",
navigatorID:"-2",
paperPrintScale:100,
scrollbars:10,
showInMenu:false,
size:"830,200",
styleName:null,
typeid:3,
uuid:"E7A7E801-3380-4C91-9DDD-6C27F2C476A8"