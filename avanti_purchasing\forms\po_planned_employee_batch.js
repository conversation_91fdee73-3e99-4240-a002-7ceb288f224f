/**
 * @properties={typeid:24,uuid:"D2D68F89-B63C-4F22-BFAA-5FA28BCCC8A6"}
 */
function onChange_tabEmpl(previousIndex, event) {
    var dividerLocation = elements.tabEmpl.getDividerLocation();
    scopes.avUtils.setUserPref("po_planned_employee_batch_tabEmpl_divider", dividerLocation.toString());
}
/**
 * @return
 * @properties={typeid:24,uuid:"CC169B80-538C-4025-8167-5F045D5F406D"}
 * @override
 */
function onLoad(event) {
    elements.tabEmpl.setLeftForm(forms.po_planned_employee_batch_tbl,'_to_sys_employee$foundset');
    elements.tabEmpl.setRightForm(forms.po_planned_employee_batch_dtl,'sys_employee_to_po_planned');
    return _super.onLoad(event);
}
/** *

    // Restore divider location for tabEmpl
    var sPrefType = "po_planned_employee_batch_tabEmpl_divider";
    var savedLocation = scopes.avUtils.getUserPref(sPrefType);
    if (savedLocation) {
        elements.tabEmpl.setDividerLocation(parseInt(savedLocation));
    }

 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"D3E4CDBE-26BB-462D-8361-6DBD0C0E2442"}
 */
function onShowForm(_firstShow, _event)
{
	elements.tabEmpl.setDividerLocation(252);
	elements.tabEmpl.setDividerSize(2);
	 _super.onShowForm(_firstShow, _event)
}
