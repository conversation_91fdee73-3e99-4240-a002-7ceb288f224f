/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"6A162E7D-CE29-4B49-962F-70B50EC972A3"}
 */
var multi_selecter = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"0FEEE2D8-3CB7-4AEE-90AB-E1DCDDFB5764"}
 */
var udf_field_value_display = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"11CA678B-78F2-4B12-A862-48DD0AC58258"}
 */
var udf_field_value_input = '';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"93EAE41B-9B7A-4857-987F-A43670112B9A"}
 */
var udf_field_value_sequence = '';

/**
 * Close the modal dialog 
 * 
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"C79E3DB9-7FC2-44DA-8F19-B3B8BE8D8810"}
 */
function onActionAddOK(event) {
	convertMultiSelect(udf_field_value_input);
	
	globals.DIALOGS.closeForm(event);
}

/**
 * Close the modal dialog 
 * 
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"59B7586B-123E-4D04-8F0D-917644EB057B"}
 */
function onActionCancel(event) {
	globals.DIALOGS.closeForm(event);
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5A4CF7DD-7B6C-4E8C-A8AB-EE28DD56EF93"}
 */
function onShow(firstShow, event) {
// 	elements.udf_field_value_input.requestFocus();
	controller.readOnly = false;
	globals.svy_nav_setFieldsColor(controller.getName(), 'edit');
}

/**
 * Conver the input string from the value list to two lists: one of the display values and the other of the UUIDs
 * @param {String} udf_field_value_param
 *
 * @properties={typeid:24,uuid:"E6BD9A82-72F2-408A-B06F-147102245581"}
 * @AllowToRunInFind
 */
function convertMultiSelect(udf_field_value_param) {
	udf_field_value_input = '';
	udf_field_value_display = '';
	udf_field_value_sequence = '';
	
	if (udf_field_value_param) {
		if (udf_field_value_param.lastIndexOf('\n') == udf_field_value_param.length -1) {
			udf_field_value_param = udf_field_value_param.substring(0,udf_field_value_param.length-1);
		}
			
		var udf_field_value_array =  udf_field_value_param.split('\n');
		udf_field_value_array = udf_field_value_array.sort(globals.udfSorter);
		
		for each (var field_value in udf_field_value_array) {
			/** @type{String} */
			var field_value_string = field_value;
			udf_field_value_input +=  field_value_string.substring(0, 36) + ', ';
			udf_field_value_display +=  field_value_string.substring(37, field_value_string.lastIndexOf(' ')) + ', ';
			udf_field_value_sequence +=  field_value_string.substring(field_value_string.lastIndexOf(' ')+1) + ', ';
		}
			
		if (udf_field_value_input.length > 0) {
			udf_field_value_input = udf_field_value_input.substring(0, udf_field_value_input.length - 2);
			udf_field_value_display = udf_field_value_display.substring(0, udf_field_value_display.length -2);
			udf_field_value_sequence = udf_field_value_sequence.substring(0, udf_field_value_sequence.length - 2);
		}
	}
	
	// Set the UUIDs, Display Answer and Sequence Numbers for multi-select options.
	
	/*** @type {JSFoundSet<db:/avanti/sys_udf_values>} */
	var fsUDFValues = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_udf_values');
	
	if(fsUDFValues.find()){
		fsUDFValues.sys_udf_type_id = forms.sys_udf_tree_answers.foundset.getSelectedRecord().sys_udf_type_id;
		fsUDFValues.unique_id = forms.sys_udf_tree_answers.unique_uuid;
		//Update an existing record that already exists from user input dialog.
		if(fsUDFValues.search()> 0) {
			fsUDFValues.udf_answer_multi_select = udf_field_value_input;
			fsUDFValues.udf_answer = udf_field_value_display;
			fsUDFValues.udf_answer_multi_select_seq = udf_field_value_sequence;
			fsUDFValues.sys_udf_values_to_sys_udf_tmp_values.udf_answer = udf_field_value_display;
			databaseManager.saveData(fsUDFValues);
			scopes.avUDF.handleChangedUDFValue(fsUDFValues.sys_udf_type_id,fsUDFValues.unique_id,'MULTI_SELECT');
		} // Create a new record and get answer from user input dialog.
		else {
			var newUDFRec = fsUDFValues.getRecord(fsUDFValues.newRecord());
			newUDFRec.sys_udf_type_id =  forms.sys_udf_tree_answers.foundset.getSelectedRecord().sys_udf_type_id;
			newUDFRec.unique_id = forms.sys_udf_tree_answers.unique_uuid;
			newUDFRec.udf_answer_multi_select = udf_field_value_input;
			newUDFRec.udf_answer = udf_field_value_display;
			newUDFRec.udf_answer_multi_select_seq = udf_field_value_sequence;;
			newUDFRec.sys_udf_values_to_sys_udf_tmp_values.udf_answer = udf_field_value_display;
			databaseManager.saveData(newUDFRec);
			scopes.avUDF.handleChangedUDFValue(newUDFRec.sys_udf_type_id,newUDFRec.unique_id,'MULTI_SELECT');
		}
	}
	databaseManager.refreshRecordFromDatabase(forms['sys_udf_tree_answers'].foundset, -1); // refresh displayed answer
	return;	
}

/**
 * @param {JSEvent} event
 *
 * @properties={typeid:24,uuid:"D3B58EA9-06D3-435C-BDE6-909A2F5D7E0F"}
 */
function onAction(event) {
//application.output(udf_field_value_input);
application.output(multi_selecter)

}

/**
 * Handle changed data, return false if the value should not be accepted.
 * JSEvent.data will contain extra information about dataproviderid, its scope and the scope id (record datasource or form/global variable scope)
 *
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"43644ACF-A137-4118-84E0-E564D74C3B99"}
 */
function onDataChangeMultiselecter(oldValue, newValue, event) {
	// TODO Auto-generated method stub
	return true
}
