/**
 * @type {Date}
 * @properties={typeid:35,uuid:"146BB302-E167-4A36-B87A-BDBF60A9289C",variableType:93}
 */
var fromDate = null;

/**
 * @type {Date}
 * @properties={typeid:35,uuid:"E5C1DA9C-1C2B-403A-9808-EB1A6B0BD224",variableType:93}
 */
var toDate = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"117FAEE4-9897-4426-AE95-30FDFCB772E7"}
 */
var fromDiv = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"8BC73056-C0E8-40EE-B496-DF099EACD161"}
 */
var toDiv = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D8282753-2357-4C33-A664-3314AA8E17A9"}
 */
var fromPlant = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"67922A3C-F02C-4BFD-960D-DBEA33341B50"}
 */
var toPlant = null;

/**
 * Standard Method for getting the Parameters from this filter form
 *
 * @returns {{aParamNames: Array, aParamValues: Array, whereClause: String}} Returns an object containing the param names you need and values
 *
 *
 * @properties={typeid:24,uuid:"F7380678-C9EB-4B51-BEBD-2A372BE4E0C0"}
 */
function getFilterParams() {
	var oParams = new Object();

	var sWhere = " WHERE org_id = '" + globals.org_id + "'";

	var toDateParam = null;
	var fromDateParam = null;

	if (!toDate){
		toDate = new Date();
	}

	if (toDate) {
		toDateParam = plugins.DateUtils.dateFormat(toDate, 'yyyy-MM-dd');
	}

	if (fromDate) {
		fromDateParam = plugins.DateUtils.dateFormat(fromDate, 'yyyy-MM-dd');
	}

	oParams.whereClause = sWhere;
	oParams.aParamNames = ["pFromDate", "pToDate","pFromDivision", "pToDivision","pFromPlant", "pToPlant"];

	oParams.aParamValues = [fromDateParam, toDateParam, fromDiv, toDiv, fromPlant, toPlant];
	return oParams;
}

/** *
 * @param firstShow
 * @param event
 *
 * @properties={typeid:24,uuid:"FCEF18A6-737E-4932-B218-657D6A53AC72"}
 */
function onShow(firstShow, event) {
	_super.onShow(firstShow, event);

	globals.avUtilities_setFormEditMode(controller.getName(), "edit");

	setDefaultDivisionFilter();
	
	toDate = application.getServerTimeStamp();
	fromDate = globals.avUtilities_dateGetFirstOfMonth(toDate);
	elements.fromDate.format = globals.avBase_dateFormat;
	elements.toDate.format = globals.avBase_dateFormat;

}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"43C87822-45D9-4628-8FB1-0E5BB6948B7E"}
 */
function onDataChangeFromDate(oldValue, newValue, event) {
	if (newValue > toDate) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
		return false;
	}
	return true;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"1F637D61-EB26-4321-AFCB-6A20033714E5"}
 */
function onDataChangeToDate(oldValue, newValue, event) {
	if (newValue < fromDate) {
		globals.DIALOGS.showWarningDialog('i18n:avanti.dialog.notification', 'i18n:avanti.dialog.InvalidRange', i18n.getI18NMessage('avanti.dialog.ok'));
		return false;
	}
	return true;
}

/**
 * Set default division
 * 
 * <AUTHOR> Dol
 * @since Jul 19, 2016
 *
 *
 * @properties={typeid:24,uuid:"11439011-A15A-473D-A2ED-8C1B30CB4B19"}
 */
function setDefaultDivisionFilter () {
	
	elements.fromDiv.enabled = true;
	elements.toDiv.enabled = true;
	
	if (utils.hasRecords(_to_sys_division$org)){
		var rDivision = _to_sys_division$org.getRecord(1);
		fromDiv = rDivision.div_code;
		toDiv = fromDiv;
		
		if (_to_sys_division$org.getSize() == 1){
			elements.fromDiv.enabled = false;
			elements.toDiv.enabled = false;
		}
		
		if (utils.hasRecords(rDivision.sys_division_to_sys_plant$firstplant)){
			fromPlant = rDivision.sys_division_to_sys_plant$firstplant.plant_code;
			toPlant = fromPlant;
		}
	}
}
