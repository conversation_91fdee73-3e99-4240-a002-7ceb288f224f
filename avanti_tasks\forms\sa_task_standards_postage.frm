customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sa_task_standard",
extendsID:"67CFC51B-FCB5-406A-8E36-162B08C9F1AA",
items:[
{
height:325,
partType:5,
typeid:19,
uuid:"01E28131-DDFF-41BD-9DEF-6497B977CFA2"
},
{
cssPosition:"10,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"10",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.functionalArea",
visible:true
},
name:"component_8510129C",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0E9BF143-93EB-4A8A-8CA7-1C8EEE4E72FB"
},
{
cssPosition:"64,-1,-1,709,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"709",
right:"-1",
top:"64",
width:"116"
},
dataProviderID:"taskstd_tax_additional_charges_postage",
enabled:true,
inputType:"radio",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
toolTipText:"i18n:avanti.tooltip.taxAdditionalCharges",
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"taskstd_tax_additional_charges_postage",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"16319016-D71B-49EF-BCDB-77C82FFB8433"
},
{
cssPosition:"37,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"37",
width:"75"
},
dataProviderID:"taskstd_mrkup",
editable:true,
enabled:true,
format:"0.00%",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
toolTipText:"i18n:avanti.lbl.percent_tooltip",
visible:true
},
name:"fldMrkUp",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"18F682E1-424C-4B70-B459-3953FBC7B393"
},
{
cssPosition:"10,-1,-1,510,194,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"510",
right:"-1",
top:"10",
width:"194"
},
dataProviderID:"use_for_mailing_items",
enabled:true,
onDataChangeMethodID:"FD490AE6-61F4-480B-A676-FABD7E40AF1A",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.useForMailingItems",
toolTipText:"i18n:avanti.lbl.useForMailingItems_tooltip",
visible:true
},
name:"fldUseForMaillingItems",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"4469DD27-7E82-4E6C-9481-70A64AE10827"
},
{
cssPosition:"64,-1,-1,388,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"388",
right:"-1",
top:"64",
width:"116"
},
dataProviderID:"taskstd_cost_per_piece",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"3AE88BEF-70DA-4616-8E87-6C58DA3FE18B",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_973959BE",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"52BCD8C4-1F03-4392-A770-28B2D99F76D4"
},
{
cssPosition:"64,-1,-1,155,75,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"64",
width:"75"
},
dataProviderID:"taskstd_postage_markup",
editable:true,
enabled:true,
format:"0.00%",
selectOnEnter:false,
styleClass:"textbox_bts text-center",
tabSeq:0,
visible:true
},
name:"fldPostageMarkup",
styleClass:"textbox_bts text-center",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"697A6B4A-5C7D-4AF0-A6F5-7A4675873024"
},
{
cssPosition:"64,-1,-1,243,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"243",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.costPerPiecePricing",
visible:true
},
name:"component_D67C2E65",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9D41FF4C-CD67-431D-9E39-DAE8B10F990E"
},
{
cssPosition:"91,-1,-1,10,305,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"91",
width:"305"
},
dataProviderID:"auto_create_postage_task",
enabled:true,
onDataChangeMethodID:"0F76EDA3-9374-4207-878D-7B2579249880",
styleClass:"checkbox_bts",
tabSeq:0,
text:"i18n:avanti.lbl.autoCreatePostageStd",
visible:true
},
name:"fldAutoCreate",
styleClass:"checkbox_bts",
typeName:"bootstrapcomponents-checkbox",
typeid:47,
uuid:"A762CFFA-6079-4067-9C5C-F91DA17F3B6F"
},
{
cssPosition:"37,-1,-1,510,194,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"510",
right:"-1",
top:"37",
width:"194"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.include_postage_markup_in_sales",
visible:true
},
name:"component_4972F9E4",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BABA2368-4889-40EF-B57A-F744693EF815"
},
{
cssPosition:"64,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"64",
width:"140"
},
enabled:true,
labelFor:"fldPostageMarkup",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.postage_markup%",
visible:true
},
name:"taskstd_postage_markup_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"BAD2F55B-FFB1-46DF-8597-B8844B41ABC5"
},
{
cssPosition:"37,-1,-1,243,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"243",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.customerPricing",
visible:true
},
name:"component_00199933",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"C37D9619-4EC9-4CE5-9DD2-ED074C0AD6A5"
},
{
cssPosition:"37,-1,-1,10,140,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"37",
width:"140"
},
enabled:true,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.postage_task_markup%",
visible:true
},
name:"lblMrkUp",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"DE5FB38C-D201-4DCA-A48E-E61C51D425D0"
},
{
cssPosition:"37,-1,-1,709,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"709",
right:"-1",
top:"37",
width:"116"
},
dataProviderID:"taskstd_flag_postage_in_sales",
enabled:true,
inputType:"radio",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_3D099C9F",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"E5D6FE61-070F-4969-B0FC-B81A37BB43D0"
},
{
cssPosition:"64,-1,-1,510,194,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"510",
right:"-1",
top:"64",
width:"194"
},
enabled:true,
labelFor:"taskstd_tax_additional_charges_postage",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.taxAdditionalCharges",
visible:true
},
name:"taskstd_tax_additional_charges_postage_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"F098FAA5-145C-483A-9DFD-DB2F385B7E56"
},
{
cssPosition:"37,-1,-1,388,116,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"388",
right:"-1",
top:"37",
width:"116"
},
dataProviderID:"taskstd_custpricing",
enabled:true,
inputType:"radio",
onDataChangeMethodID:"8A1143B3-056C-47EF-9DAC-CD94FEC115D4",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
tabSeq:0,
valuelistID:"55D51C53-5395-4422-9A22-FF3F25EA3ED2",
visible:true
},
name:"component_CD40BD7E",
styleClass:"svy-radiogroup-horizontal choicegroup_bts",
typeName:"bootstrapcomponents-choicegroup",
typeid:47,
uuid:"F2CA314B-14C4-4DA5-9C03-F5F3FC465D76"
},
{
cssPosition:"10,-1,-1,155,345,22",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"22",
left:"155",
right:"-1",
top:"10",
width:"345"
},
dataProviderID:"systaskfunc_id",
enabled:true,
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"0CFDFA91-30A8-45C3-8721-498AC8B09F74",
visible:true
},
name:"fldTaskFunctionalArea",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"F9FB8AE1-5798-4A2B-A175-ADB67DC5EDF5"
}
],
name:"sa_task_standards_postage",
onShowMethodID:"A5CF6DA1-6296-41C2-A0C1-4773DCE9E6A4",
paperPrintScale:100,
scrollbars:33,
size:"945,325",
styleName:null,
typeid:3,
uuid:"FF4FE913-E1BC-41FB-9EAD-6A946AE9E10B"