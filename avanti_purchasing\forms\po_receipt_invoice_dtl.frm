customProperties:"useCssPosition:true",
dataSource:"db:/avanti/po_receipt",
extendsID:"3DF9114A-BDD8-4EF3-AEDD-59E7874347A7",
items:[
{
cssPosition:"39,-1,-1,10,130,22",
formIndex:20,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"39",
width:"130"
},
enabled:true,
formIndex:20,
labelFor:"porecd_fob_cost_total",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.PurchaseAmount",
visible:true
},
name:"porecd_fob_cost_total_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"0A9590DF-C3BE-44B4-A80B-D24A80AB6C39"
},
{
cssPosition:"205,-1,-1,10,129,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"205",
width:"129"
},
enabled:true,
formIndex:3,
labelFor:"purchase_amount",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.invoiceAmount",
visible:true
},
name:"ap_invoice_amount_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"31BA0C11-C541-45E1-8618-67A4E267F9D5"
},
{
cssPosition:"294,-1,-1,209,202,20",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"209",
right:"-1",
top:"294",
width:"202"
},
enabled:true,
formIndex:5,
styleClass:"table label_bts",
tabSeq:-1,
visible:true
},
name:"component_688C9434",
styleClass:"table label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"321E5991-037B-4BA9-A66E-7E37633CE9ED"
},
{
cssPosition:"293,-1,-1,5,406,86",
json:{
cssPosition:{
bottom:"-1",
height:"86",
left:"5",
right:"-1",
top:"293",
width:"406"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_F0707206",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"34DF7471-2522-485A-BD5C-200A0C202796"
},
{
cssPosition:"259,-1,-1,290,115,22",
formIndex:8,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"290",
right:"-1",
top:"259",
width:"115"
},
dataProviderID:"freight_amount_left",
editable:false,
enabled:true,
formIndex:8,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"freight_amount_left",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"356345F6-55A0-4B40-A5C0-D6C66AFE0C87"
},
{
cssPosition:"324,-1,-1,133,70,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"133",
right:"-1",
top:"324",
width:"70"
},
dataProviderID:"matching_tolerance_dollar",
editable:false,
enabled:true,
formIndex:2,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"matching_tolerance_dollar",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"3B369FEB-91E2-4A32-945B-C8AB987881D8"
},
{
partType:2,
typeid:19,
uuid:"3CC8C41D-2039-4812-9F92-5AB059A4A3FC"
},
{
cssPosition:"147,-1,-1,10,130,22",
formIndex:18,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"147",
width:"130"
},
enabled:true,
formIndex:18,
labelFor:"porec_setup_amt",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.setupCost",
visible:true
},
name:"porec_setup_amt_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3DBC916E-93A7-460C-ABBB-2597C4A16C5B"
},
{
cssPosition:"120,-1,-1,10,130,22",
formIndex:18,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"120",
width:"130"
},
enabled:true,
formIndex:18,
labelFor:"porec_freight_amt",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FreightCost",
visible:true
},
name:"porec_freight_amt_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"42F60460-4169-4FD1-9563-215C84AA7AA0"
},
{
cssPosition:"232,-1,-1,290,115,22",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"290",
right:"-1",
top:"232",
width:"115"
},
dataProviderID:"freight_amount_matched",
editable:false,
enabled:true,
formIndex:6,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"freight_amount_matched",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"46305B6A-3230-4277-B9DE-9CF1E7B7178A"
},
{
cssPosition:"39,-1,-1,145,140,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"39",
width:"140"
},
dataProviderID:"po_receipt_to_po_receipt_detail.sum_fob_cost_total",
editable:false,
enabled:true,
formIndex:21,
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"porecd_fob_cost_total",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"4631ADAC-BF8C-474C-9D48-4DDA18A5FB0D"
},
{
cssPosition:"294,-1,-1,10,193,20",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"10",
right:"-1",
top:"294",
width:"193"
},
enabled:true,
formIndex:5,
styleClass:"label_bts text-center",
tabSeq:-1,
text:"i18n:avanti.lbl.matchingTolerance",
visible:true
},
name:"matching_tolerance_lbl",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"46F9AA12-085B-4CDB-AF6C-502C81B0802F"
},
{
cssPosition:"180,-1,-1,145,146,20",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"145",
right:"-1",
top:"180",
width:"146"
},
enabled:true,
formIndex:11,
styleClass:"table label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.PurchaseAmount",
visible:true
},
name:"component_36A38F53",
styleClass:"table label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"4D6F1B1D-9B2D-47D1-B277-78A8A141F996"
},
{
cssPosition:"324,-1,-1,80,21,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"80",
right:"-1",
top:"324",
width:"21"
},
enabled:true,
formIndex:4,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.%",
toolTipText:"%%matching_tolerance_percentage_dollar%%",
visible:true
},
name:"component_3C9396EB",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"53D7E70E-2503-40E9-80F5-A5BC4BE71075"
},
{
cssPosition:"93,-1,-1,10,130,22",
formIndex:16,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"93",
width:"130"
},
enabled:true,
formIndex:16,
labelFor:"shipmethod_id",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.shipmethod_id",
visible:true
},
name:"shipmethod_id_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"59B34555-3C64-49D2-ABDB-65CD099FAD71"
},
{
cssPosition:"205,-1,-1,146,140,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"205",
width:"140"
},
dataProviderID:"purchase_amount",
editable:false,
enabled:true,
formIndex:1,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"purchase_amount",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"64A5A159-E3FE-4D8F-826B-44EA2F32B71E"
},
{
cssPosition:"12,-1,-1,145,140,22",
formIndex:21,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"12",
width:"140"
},
dataProviderID:"po_receipt_to_po_purchase.po_purchase_to_ap_supplier.supplier_name",
editable:false,
enabled:true,
formIndex:21,
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"supplier_name",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"674AA7A1-D2A1-41A9-82DD-B309B65D9561"
},
{
cssPosition:"324,-1,-1,101,22,22",
formIndex:3,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"101",
right:"-1",
top:"324",
width:"22"
},
enabled:true,
formIndex:3,
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.Or",
visible:true
},
name:"component_2EB82072",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"67CD8E25-2FAD-4242-9CDD-A19A1B3121E8"
},
{
cssPosition:"259,-1,-1,11,129,22",
formIndex:5,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"259",
width:"129"
},
enabled:true,
formIndex:5,
labelFor:"purchase_amount_left",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.varianceMoney",
visible:true
},
name:"amount_left_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6840C760-E782-4A8A-8ABA-5C966EDE4D0E"
},
{
cssPosition:"7,-1,-1,5,406,168",
json:{
cssPosition:{
bottom:"-1",
height:"168",
left:"5",
right:"-1",
top:"7",
width:"406"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_72D86331",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"6AC89566-A6DA-4C68-9102-34D1CC3E19FC"
},
{
cssPosition:"66,-1,-1,10,130,22",
formIndex:11,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"66",
width:"130"
},
enabled:true,
formIndex:11,
labelFor:"curr_name",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.currency_Rate",
visible:true
},
name:"curr_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"718A6DAD-8AF2-4426-9B4A-DC4581397C41"
},
{
cssPosition:"350,-1,-1,208,199,20",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"208",
right:"-1",
top:"350",
width:"199"
},
enabled:true,
formIndex:22,
onActionMethodID:"50D8BE20-BEA3-4891-88FA-F02455F8DCAF",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.allocateFreightVariances",
visible:true
},
name:"btnFreightAllocations",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"79B26CE3-5410-4187-A684-7A25821808D3"
},
{
cssPosition:"120,-1,-1,145,140,22",
formIndex:19,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"120",
width:"140"
},
dataProviderID:"porec_freight_amt",
editable:false,
enabled:true,
formIndex:19,
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"porec_freight_amt",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"807CA799-1452-4FDB-B99B-9A58E43A6961"
},
{
cssPosition:"180,-1,-1,290,121,20",
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"290",
right:"-1",
top:"180",
width:"121"
},
enabled:true,
formIndex:12,
styleClass:"table label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.FreightCost",
visible:true
},
name:"component_9AB329AA",
styleClass:"table label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"84A4F0B6-6B72-4725-9686-46B7826E7C89"
},
{
cssPosition:"12,-1,-1,10,130,22",
formIndex:20,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"12",
width:"130"
},
enabled:true,
formIndex:20,
labelFor:"supplier_name",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.supplier",
visible:true
},
name:"supplier_name_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"94FBE5AD-EC08-4BAF-AE13-B139ED8A6A45"
},
{
cssPosition:"232,-1,-1,11,129,22",
formIndex:4,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"11",
right:"-1",
top:"232",
width:"129"
},
enabled:true,
formIndex:4,
labelFor:"purchase_amount_matched",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.amountMatched",
visible:true
},
name:"amount_matched_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9C459B4F-EFC3-4D57-9B38-D28BEEE04AE8"
},
{
cssPosition:"205,-1,-1,290,115,22",
formIndex:2,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"290",
right:"-1",
top:"205",
width:"115"
},
dataProviderID:"freight_amount",
editable:false,
enabled:true,
formIndex:2,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"freight_amount",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"A6820C23-9D96-4428-ACE7-DD16DB7D30AF"
},
{
cssPosition:"325,-1,-1,208,199,20",
formIndex:22,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"208",
right:"-1",
top:"325",
width:"199"
},
enabled:true,
formIndex:22,
onActionMethodID:"769E82CF-10E0-4A2E-A76D-D0A2A1E14BA8",
styleClass:"btn btn-default button_bts",
tabSeq:0,
text:"i18n:avanti.lbl.allocatePurchaseVariances",
visible:true
},
name:"btnPurchaseAllocations",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"A9B1AFB3-8E61-4755-8223-5FC7D0727F24"
},
{
height:385,
partType:5,
typeid:19,
uuid:"AF1F75DB-09E1-4BA3-B9DC-F60281517ED2"
},
{
cssPosition:"179,-1,-1,5,406,109",
json:{
cssPosition:{
bottom:"-1",
height:"109",
left:"5",
right:"-1",
top:"179",
width:"406"
},
enabled:true,
styleClass:"background-group label_bts",
tabSeq:-1,
visible:true
},
name:"component_C43F1A55",
styleClass:"background-group label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B6B74F2F-09C9-4213-A62B-0B6B84673D3B"
},
{
cssPosition:"66,-1,-1,290,115,22",
enabled:false,
formIndex:13,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"290",
right:"-1",
top:"66",
width:"115"
},
dataProviderID:"porec_exchange_rate",
editable:false,
enabled:false,
formIndex:13,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"porec_exchange_rate",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"B85505FB-D3F2-45E3-979D-BA70DB3AB077"
},
{
cssPosition:"147,-1,-1,145,140,22",
formIndex:19,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"147",
width:"140"
},
dataProviderID:"porec_setup_amt",
editable:false,
enabled:true,
formIndex:19,
selectOnEnter:false,
styleClass:"textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"porec_setup_amt",
styleClass:"textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"BADA132F-49CF-4B66-A54B-E55D2378CC2C"
},
{
cssPosition:"259,-1,-1,146,140,22",
formIndex:7,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"259",
width:"140"
},
dataProviderID:"purchase_amount_left",
editable:false,
enabled:true,
formIndex:7,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"purchase_amount_left",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C013E645-2458-4D2F-BAF2-4B08067CE7E0"
},
{
cssPosition:"324,-1,-1,10,70,22",
formIndex:1,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"10",
right:"-1",
top:"324",
width:"70"
},
dataProviderID:"matching_tolerance_percentage",
editable:false,
enabled:true,
formIndex:1,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
toolTipText:"%%matching_tolerance_percentage_dollar%%",
visible:true
},
name:"matching_tolerance_percent",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"C2D4F9B2-F0B3-4B37-ACA5-5C655CBED3CA"
},
{
cssPosition:"232,-1,-1,146,140,22",
formIndex:9,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"146",
right:"-1",
top:"232",
width:"140"
},
dataProviderID:"purchase_amount_matched",
editable:false,
enabled:true,
formIndex:9,
selectOnEnter:false,
styleClass:"not_editable textbox_bts text-right",
tabSeq:0,
visible:true
},
name:"purchase_amount_matched",
styleClass:"not_editable textbox_bts text-right",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"D0B480A7-DD01-488A-88C7-CDA169C883A4"
},
{
cssPosition:"294,-1,-1,208,197,20",
formIndex:6,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"208",
right:"-1",
top:"294",
width:"197"
},
enabled:true,
formIndex:6,
styleClass:"label_bts text-center",
tabSeq:-1,
text:"i18n:avanti.lbl.varianceMoney",
visible:true
},
name:"allocations_label",
styleClass:"label_bts text-center",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D1A92400-58CB-4807-887F-46ED150297BA"
},
{
cssPosition:"180,-1,-1,5,141,20",
formIndex:10,
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"5",
right:"-1",
top:"180",
width:"141"
},
enabled:true,
formIndex:10,
styleClass:"table label_bts",
tabSeq:-1,
visible:true
},
name:"component_FF37C2EB",
styleClass:"table label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D25EA8EC-CBEB-44C6-9B84-21A8A967D1E1"
},
{
cssPosition:"66,-1,-1,145,140,22",
enabled:false,
formIndex:12,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"66",
width:"140"
},
dataProviderID:"po_receipt_to_po_purchase.po_purchase_to_sys_currency$po_currency.curr_name",
editable:false,
enabled:false,
formIndex:12,
selectOnEnter:false,
styleClass:"not_editable textbox_bts",
tabSeq:0,
visible:true
},
name:"curr_name",
styleClass:"not_editable textbox_bts",
typeName:"bootstrapcomponents-textbox",
typeid:47,
uuid:"E9CF3899-E1F8-415A-82E0-1656001118D0"
},
{
cssPosition:"294,-1,-1,6,202,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"6",
right:"-1",
top:"294",
width:"202"
},
enabled:true,
styleClass:"table label_bts",
tabSeq:-1,
visible:true
},
name:"component_DA52C41E",
styleClass:"table label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"ECCA48CB-3DC7-4FA4-BF08-F31B5F8E7FEE"
},
{
cssPosition:"93,-1,-1,145,260,22",
formIndex:17,
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"145",
right:"-1",
top:"93",
width:"260"
},
dataProviderID:"shipmethod_id",
editable:false,
enabled:true,
formIndex:17,
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"432C4C95-6A1B-4E14-83AF-3C1933A66646",
visible:true
},
name:"shipmethod_id",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"EE657CF2-6885-40F0-9007-50DB8E2CAB0C"
}
],
name:"po_receipt_invoice_dtl",
scrollbars:33,
size:"417,434",
styleName:null,
typeid:3,
uuid:"1038577B-9DAA-4733-8395-16E962F85FB3"