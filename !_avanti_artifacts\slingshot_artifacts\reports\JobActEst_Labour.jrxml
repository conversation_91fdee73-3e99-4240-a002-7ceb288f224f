<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.12.2.final using JasperReports Library version 6.12.2-75c5e90a222ab406e416cbf590a5397028a52de3  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="JobActEst_Labour" pageWidth="752" pageHeight="572" orientation="Landscape" columnWidth="752" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="8056043f-0029-4ff0-820f-9112e209bd24">
	<property name="ireport.zoom" value="1.6528925619834718"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="CellBorders">
		<box>
			<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#CCCCCC"/>
			<leftPen lineWidth="0.5" lineColor="#CCCCCC"/>
			<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#CCCCCC"/>
			<rightPen lineWidth="0.5" lineColor="#CCCCCC"/>
		</box>
	</style>
	<parameter name="pSQL" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["SELECT rev.*, est.*, det.*, detQty.* FROM _v_est_rev_det_qty detQty INNER JOIN _v_est_rev_det det ON detQty.ordrevd_id = det.ordrevd_id INNER JOIN _v_est_rev rev ON det.ordrevh_id = rev.ordrevh_id INNER JOIN _v_est est ON rev.ordh_id = est.ordh_id"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDate" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["dd-MM-yyyy"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatDateSmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[( $P{formatDate}.equals( "dd MMM yyyy" ) ? "dd/MM/yyyy" : "MM/dd/yyyy" )]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrency" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["$#,##0.00"]]></defaultValueExpression>
	</parameter>
	<parameter name="formatCurrencySmall" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{formatCurrency}.substring(0,$P{formatCurrency}.length()-3).toString()]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\Documents\\slingshotdata\\jasper_reports\\Job Reports\\Actual vs Budget\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="p79" class="java.lang.Integer" isForPrompting="false">
		<parameterDescription><![CDATA[Use Division and Plant Filter?]]></parameterDescription>
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="pJobID" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D5377C9C-6BD9-4A3A-A24D-5C5AE8F3E1FD"]]></defaultValueExpression>
	</parameter>
	<parameter name="pPostedOnly" class="java.lang.Integer" isForPrompting="false">
		<defaultValueExpression><![CDATA[0]]></defaultValueExpression>
	</parameter>
	<parameter name="pPostedRange" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pPostedOnly}==1 ? "AND Q_job_cost_labour.jcl_is_posted=1"
                   : " "]]></defaultValueExpression>
	</parameter>
	<parameter name="pPlant_id" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["8F7518A9-7B72-43F4-B64D-2CBE2A5B6D30"]]></defaultValueExpression>
	</parameter>
	<parameter name="pSelectedPlants" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["'8F7518A9-7B72-43F4-B64D-2CBE2A5B6D30'"]]></defaultValueExpression>
	</parameter>
	<parameter name="pPlantIDCondition_act" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pPlant_id} != null ? "actual.job_plant_id = actual.cc_plant_id" : "actual.cc_plant_id IS NULL"]]></defaultValueExpression>
	</parameter>
	<parameter name="pPlantIDCondition_bud" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pPlant_id} != null ? "Q_JB.job_plant_id = Q_JB.cc_plant_id" : "Q_JB.cc_plant_id IS NULL"]]></defaultValueExpression>
	</parameter>
	<parameter name="pPostedCondition_act" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pPostedOnly}==1 ? " AND actual.jcl_is_posted = 1"
                   : " "]]></defaultValueExpression>
	</parameter>
	<parameter name="pPostedCondition_bud" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{pPostedOnly}==1 ? " WHERE jcl_is_posted = 1"
                   : " "]]></defaultValueExpression>
	</parameter>
	<parameter name="pOmitDefaultDivPlant_costLinks_act" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{p79}.equals(new Integer(1)) ? " AND actual.isvalid_for_divplant = 1 " : " "]]></defaultValueExpression>
	</parameter>
	<parameter name="pOmitDefaultDivPlant_costLinks_bud" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA[$P{p79}.equals(new Integer(1)) ? " AND Q_JB.isvalid_for_divplant = 1 " : " "]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[SELECT
	  org_id
	, job_number
	, job_id
	, job_description
	, section_nr
	, ordrevds_description
	, ISNULL(ordrevds_id_order,ordrevds_id) AS ordrevds_id
	, cc_id
	, dept_code
	, dept_desc
	, dept_nr
	, opcat_code
	, opcat_desc
	, opcat_nr
	, cc_op_code
	, cc_desc
	, SUM(ISNULL(total_budget_labour_cost,0)) AS total_budget_labour_cost
	, SUM(ISNULL(total_budget_labour_time,0)) AS total_budget_labour_time
	, ROUND(SUM(ISNULL(total_actual_labour_cost,0)),2) AS total_actual_labour_cost
	, SUM(ISNULL(total_actual_labour_time,0))  AS total_actual_labour_time
	, SUM(ISNULL(total_actual_qty_produced,0))  AS total_actual_qty_produced
	, MAX(ISNULL(max_actual_labour_rate,0)) AS max_actual_labour_rate
	, MAX(ISNULL(cc_total_rate,0)) AS cc_total_rate
	, job_plant_id
	, isvalid_for_divplant
	, ISNULL(ordrevds_id_order,ordrevds_id) AS ordrevds_id_for_material
 FROM (
SELECT
      Q_JB.org_id, Q_JB.job_number, Q_JB.job_id, Q_JB.job_description
	, Q_JB.section_nr, Q_JB.ordrevds_description, Q_JB.ordrevds_id, Q_JB.cc_id, Q_JB.dept_code
	, Q_JB.dept_desc, Q_JB.dept_nr, Q_JB.opcat_code, Q_JB.opcat_desc, Q_JB.opcat_nr, Q_JB.cc_op_code,Q_JB.cc_desc
	, ISNULL(total_budget_labour_cost,0) AS total_budget_labour_cost
	, ISNULL(total_budget_labour_time,0) AS total_budget_labour_time
	, ISNULL(tab_lab.total_actual_labour_cost,0) AS total_actual_labour_cost
	, ISNULL(tab_lab.total_actual_labour_time,0)  AS total_actual_labour_time
	, ISNULL(tab_lab.total_actual_qty_produced,0)  AS total_actual_qty_produced
	, ISNULL(tab_lab.max_actual_labour_rate,0) AS max_actual_labour_rate
	, Q_JB.cc_total_rate, Q_JB.job_plant_id, isvalid_for_divplant
	, Q_JB.ordrevds_id_order

FROM
	_vrpt_jobactbud_prod_job_bud_lab_est AS Q_JB
LEFT OUTER JOIN
	(
		SELECT
			  ordrevds_id
			, job_id
			, cc_id
			, SUM(ISNULL(total_actual_labour_cost,0)) AS total_actual_labour_cost
			, SUM(ISNULL(total_actual_labour_time,0)) AS total_actual_labour_time
			, SUM(ISNULL(total_actual_qty_produced,0))  AS total_actual_qty_produced
			, SUM(ISNULL(max_actual_labour_rate,0)) AS max_actual_labour_rate
		FROM
			_vrpt_jobactbud_prod_job_cost_labour
			$P!{pPostedCondition_bud}
		GROUP BY
			  ordrevds_id
			, job_id
			, cc_id
	)AS tab_lab
		ON ISNULL(Q_JB.ordrevds_id_order,Q_JB.ordrevds_id) = tab_lab.ordrevds_id
		AND Q_JB.job_id     = tab_lab.job_id
		AND Q_JB.cc_id      = tab_lab.cc_id

WHERE
	Q_JB.job_id = $P{pJobID} AND (Q_JB.cc_plant_id  IN  ($P!{pSelectedPlants}) OR Q_JB.cc_plant_id IS NULL)
 $P!{pOmitDefaultDivPlant_costLinks_bud}


EXCEPT
SELECT
              actual.org_id
	  , actual.job_number
	  , actual.job_id
	  , actual.job_description
	  , actual.section_nr
	  , actual.ordrevds_description
	  , actual.ordrevds_id
	  , actual.cc_id
	  , actual.dept_code
	  , actual.dept_desc
	  , actual.dept_nr
	  , actual.opcat_code
	  , actual.opcat_desc
	  , actual.opcat_nr
	  , actual.cc_op_code
	  , actual.cc_desc
	  , ISNULL(budgeted.total_budget_labour_cost,0) AS total_budget_labour_cost
	  , ISNULL(budgeted.total_budget_labour_time,0) AS total_budget_labour_time
	  , ISNULL(actual.total_actual_labour_cost,0) AS total_actual_labour_cost
	  , ISNULL(actual.total_actual_labour_time,0)  AS total_actual_labour_time
	  , ISNULL(actual.total_actual_qty_produced,0)  AS total_actual_qty_produced
	  , ISNULL(actual.max_actual_labour_rate,0) AS max_actual_labour_rate
	  , budgeted.cc_total_rate
	  , actual.job_plant_id
 	  , actual.isvalid_for_divplant
	  , budgeted.ordrevds_id_order AS ordrevds_id_order

FROM
	_vrpt_jobactbud_prod_job_cost_labour AS actual
	LEFT OUTER JOIN _vrpt_jobactbud_prod_job_bud_lab_est AS budgeted
		ON  actual.ordrevds_id = budgeted.ordrevds_id
		AND actual.cc_id       = budgeted.cc_id
		AND actual.job_id      = budgeted.job_id

WHERE
	actual.job_id = $P{pJobID} AND (actual.cc_plant_id  IN  ($P!{pSelectedPlants}) OR actual.cc_plant_id IS NULL)
$P!{pOmitDefaultDivPlant_costLinks_act}
$P!{pPostedCondition_act}

UNION ALL
SELECT
              actual.org_id
	  , actual.job_number
	  , actual.job_id
	  , actual.job_description
	  , actual.section_nr
	  , actual.ordrevds_description
	  , actual.ordrevds_id
	  , actual.cc_id
	  , actual.dept_code
	  , actual.dept_desc
	  , actual.dept_nr
	  , actual.opcat_code
	  , actual.opcat_desc
	  , actual.opcat_nr
	  , actual.cc_op_code
	  , actual.cc_desc
	  , ISNULL(budgeted.total_budget_labour_cost,0) AS total_budget_labour_cost
	  , ISNULL(budgeted.total_budget_labour_time,0) AS total_budget_labour_time
	  , ISNULL(actual.total_actual_labour_cost,0) AS total_actual_labour_cost
	  , ISNULL(actual.total_actual_labour_time,0)  AS total_actual_labour_time
	  , ISNULL(actual.total_actual_qty_produced,0)  AS total_actual_qty_produced
	  , ISNULL(actual.max_actual_labour_rate,0) AS max_actual_labour_rate
	  , budgeted.cc_total_rate
	  , actual.job_plant_id
 	  , actual.isvalid_for_divplant
	  , budgeted.ordrevds_id_order AS ordrevds_id_order
FROM
	_vrpt_jobactbud_prod_job_cost_labour AS actual
	LEFT OUTER JOIN _vrpt_jobactbud_prod_job_bud_lab_est AS budgeted
		ON  actual.ordrevds_id = budgeted.ordrevds_id
		AND actual.cc_id       = budgeted.cc_id
		AND actual.job_id      = budgeted.job_id

WHERE
	actual.job_id = $P{pJobID} AND (actual.cc_plant_id  IN  ($P!{pSelectedPlants}) OR actual.cc_plant_id IS NULL)
$P!{pOmitDefaultDivPlant_costLinks_act}
$P!{pPostedCondition_act}

)AS main
GROUP BY
	 org_id
	, job_number
	, job_id
	, job_description
	, section_nr
	, ordrevds_description
	, ISNULL(ordrevds_id_order,ordrevds_id)
	, cc_id
	, dept_code
	, dept_desc
	, dept_nr
	, opcat_code
	, opcat_desc
	, opcat_nr
	, cc_op_code
	, cc_desc
	, job_plant_id
	, isvalid_for_divplant
ORDER BY
    main.section_nr, main.dept_nr, main.opcat_nr, main.cc_op_code]]>
	</queryString>
	<field name="org_id" class="java.lang.String"/>
	<field name="job_number" class="java.lang.String"/>
	<field name="job_id" class="java.lang.String"/>
	<field name="job_description" class="java.lang.String"/>
	<field name="section_nr" class="java.lang.Integer"/>
	<field name="ordrevds_description" class="java.lang.String"/>
	<field name="ordrevds_id" class="java.lang.String"/>
	<field name="cc_id" class="java.lang.String"/>
	<field name="dept_code" class="java.lang.String"/>
	<field name="dept_desc" class="java.lang.String"/>
	<field name="dept_nr" class="java.lang.Integer"/>
	<field name="opcat_code" class="java.lang.String"/>
	<field name="opcat_desc" class="java.lang.String"/>
	<field name="opcat_nr" class="java.lang.Integer"/>
	<field name="cc_op_code" class="java.lang.String"/>
	<field name="cc_desc" class="java.lang.String"/>
	<field name="total_budget_labour_cost" class="java.lang.Double"/>
	<field name="total_budget_labour_time" class="java.lang.Double"/>
	<field name="total_actual_labour_cost" class="java.lang.Double"/>
	<field name="total_actual_labour_time" class="java.lang.Double"/>
	<field name="total_actual_qty_produced" class="java.lang.Double"/>
	<field name="max_actual_labour_rate" class="java.lang.Double"/>
	<field name="cc_total_rate" class="java.lang.Double"/>
	<field name="job_plant_id" class="java.lang.String"/>
	<field name="isvalid_for_divplant" class="java.lang.Integer"/>
	<field name="ordrevds_id_for_material" class="java.lang.String"/>
	<variable name="vTotalVarCost_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[new Double(  $F{total_actual_labour_cost}.doubleValue() - $F{total_budget_labour_cost}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarCost_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[new Double( $F{total_actual_labour_cost}.doubleValue() - $F{total_budget_labour_cost}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarCost_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[new Double(  $F{total_actual_labour_cost}.doubleValue() - $F{total_budget_labour_cost}.doubleValue())]]></variableExpression>
	</variable>
	<variable name="vTotalVarCost_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[new Double( $F{total_budget_labour_cost}.doubleValue() - $F{total_actual_labour_cost}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarTime_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[new Double( $F{total_actual_labour_time}.doubleValue() - $F{total_budget_labour_time}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarTime_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[new Double(  $F{total_actual_labour_time}.doubleValue() - $F{total_budget_labour_time}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarTime_Section" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[new Double( $F{total_budget_labour_time}.doubleValue() - $F{total_actual_labour_time}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalVarTime_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[new Double( $F{total_budget_labour_time}.doubleValue() - $F{total_actual_labour_time}.doubleValue() )]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetCost_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetCost_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetCost_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetCost_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetTime_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetTime_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetTime_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalBudgetTime_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total_budget_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalActualCost_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalActualCost_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalActualCost_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_cost}]]></variableExpression>
	</variable>
	<variable name="vTotalActualCost_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_cost}]]></variableExpression>
		<initialValueExpression><![CDATA[]]></initialValueExpression>
	</variable>
	<variable name="vTotalActualTime_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalActualTime_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalActualTime_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalActualTime_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_time}]]></variableExpression>
	</variable>
	<variable name="vTotalActualProduced_OpCagtegory" class="java.lang.Double" resetType="Group" resetGroup="OpCategory" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_qty_produced}]]></variableExpression>
	</variable>
	<variable name="vTotalActualProduced_Dept" class="java.lang.Double" resetType="Group" resetGroup="Department" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_qty_produced}]]></variableExpression>
	</variable>
	<variable name="vTotalActualProduced_Section" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_qty_produced}]]></variableExpression>
	</variable>
	<variable name="vTotalActualProduced_Job" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_qty_produced}]]></variableExpression>
	</variable>
	<variable name="vTotalActualCost_Labor" class="java.lang.Double" resetType="Group" resetGroup="Section" calculation="Sum">
		<variableExpression><![CDATA[$F{total_actual_labour_cost}]]></variableExpression>
	</variable>
	<group name="Section">
		<groupExpression><![CDATA[$F{section_nr}]]></groupExpression>
		<groupHeader>
			<band height="17">
				<rectangle>
					<reportElement x="446" y="0" width="157" height="16" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="13fd4579-c495-46b1-8f57-5e75f224b5fd"/>
				</rectangle>
				<textField>
					<reportElement x="0" y="1" width="752" height="14" uuid="d74c7f33-f49e-4d7e-94b9-e21181364b07"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5"/>
						<leftPen lineWidth="0.5"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["SECTION (" + $F{section_nr} + "): " + $F{ordrevds_description}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="31">
				<rectangle>
					<reportElement x="447" y="0" width="155" height="15" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="7feaee6b-f07e-4528-8a75-62ee85f5e13b"/>
				</rectangle>
				<subreport>
					<reportElement x="0" y="15" width="752" height="16" uuid="d8b8e5b6-ae5c-4e8a-87c8-71ff530aff14"/>
					<subreportParameter name="pSelectedPlants">
						<subreportParameterExpression><![CDATA[$P{pSelectedPlants}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDate">
						<subreportParameterExpression><![CDATA[$P{formatDate}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pJob_plant_id">
						<subreportParameterExpression><![CDATA[$F{job_plant_id}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pSectionID">
						<subreportParameterExpression><![CDATA[$F{ordrevds_id_for_material}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pJobID">
						<subreportParameterExpression><![CDATA[$F{job_id}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDateSmall">
						<subreportParameterExpression><![CDATA[$P{formatDateSmall}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrency">
						<subreportParameterExpression><![CDATA[$P{formatCurrency}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pPostedOnly">
						<subreportParameterExpression><![CDATA[$P{pPostedOnly}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrencySmall">
						<subreportParameterExpression><![CDATA[$P{formatCurrencySmall}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<returnValue subreportVariable="vTotalActualCost_Mat" toVariable="vTotalActualCost_Section" calculation="Sum"/>
					<returnValue subreportVariable="vTotalActualCost_Mat" toVariable="vTotalActualCost_Job" calculation="Sum"/>
					<returnValue subreportVariable="vTotalBudCost_Mat" toVariable="vTotalBudgetCost_Section" calculation="Sum"/>
					<returnValue subreportVariable="vTotalBudCost_Mat" toVariable="vTotalBudgetCost_Job" calculation="Sum"/>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "JobActEst_Materials.jasper"]]></subreportExpression>
				</subreport>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="292" y="0" width="50" height="14" uuid="b5a5d38d-3fc5-4485-9700-08c185e03f1f"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualTime_Section}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="697" y="0" width="55" height="14" uuid="22cacea7-b3e8-4cdb-a437-1c284c9d0019"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalVarCost_Section}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="242" y="0" width="50" height="14" uuid="00ef57d2-f503-4a8f-9e7d-1b297b9b12ce"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualProduced_Section}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="602" y="0" width="45" height="14" uuid="3f303415-f918-4f2f-b4a5-0d63226a093f"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="#,##0.00 %" isBlankWhenNull="true">
					<reportElement x="647" y="0" width="50" height="14" forecolor="#000000" uuid="cc445507-fcd6-4663-9d7b-4f00d6c09de2"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[(($V{vTotalActualCost_Section} == null || $V{vTotalActualCost_Section}.equals(new Double(0))) &&
   ($V{vTotalBudgetCost_Section} == null ||  $V{vTotalBudgetCost_Section}.equals(new Double(0))) ? new Double(0) :
 ($V{vTotalActualCost_Section} == null || $V{vTotalActualCost_Section}.equals(new Double(0)) ? new Double(-1) :
 new java.lang.Double(
     (
        $V{vTotalActualCost_Section}.doubleValue()
        -
        $V{vTotalBudgetCost_Section}.doubleValue()
     )
     /
    $V{vTotalActualCost_Section}.doubleValue()
 )))]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00" isBlankWhenNull="true">
					<reportElement x="447" y="0" width="50" height="14" uuid="f5cd8aba-bce3-4bde-bd85-7d3315c8b585"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetTime_Section}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="392" y="0" width="55" height="14" uuid="33737e88-6a6c-4a94-8967-e9aa89465fa2"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualCost_Section}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="342" y="0" width="50" height="14" uuid="06641583-673a-4254-a8ab-d571d95687b9"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="547" y="0" width="55" height="14" uuid="163243ca-e3b8-4e2a-abb6-0251ecd4b990"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetCost_Section}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="497" y="0" width="50" height="14" uuid="ce4be66c-526b-472d-8862-3c6b4f1647bc"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField>
					<reportElement x="0" y="0" width="242" height="14" uuid="cd969896-f310-4686-8e40-3a631cc07e63"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Total - Labor"]]></textFieldExpression>
				</textField>
			</band>
			<band height="17">
				<subreport>
					<reportElement x="0" y="1" width="752" height="16" isRemoveLineWhenBlank="true" uuid="a65ef15b-0b70-453f-a099-0ec2bdc95ee9"/>
					<subreportParameter name="SUBREPORT_DIR">
						<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pJob_plant_id">
						<subreportParameterExpression><![CDATA[$F{job_plant_id}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDate">
						<subreportParameterExpression><![CDATA[$P{formatDate}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="pSectionID">
						<subreportParameterExpression><![CDATA[$F{ordrevds_id}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatDateSmall">
						<subreportParameterExpression><![CDATA[$P{formatDateSmall}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrency">
						<subreportParameterExpression><![CDATA[$P{formatCurrency}]]></subreportParameterExpression>
					</subreportParameter>
					<subreportParameter name="formatCurrencySmall">
						<subreportParameterExpression><![CDATA[$P{formatCurrencySmall}]]></subreportParameterExpression>
					</subreportParameter>
					<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
					<returnValue subreportVariable="vTotalActualCost_Pur" toVariable="vTotalActualCost_Section" calculation="Sum"/>
					<returnValue subreportVariable="vTotalActualCost_Pur" toVariable="vTotalActualCost_Job" calculation="Sum"/>
					<returnValue subreportVariable="vTotalBudCost_Pur" toVariable="vTotalBudgetCost_Section" calculation="Sum"/>
					<returnValue subreportVariable="vTotalBudCost_Pur" toVariable="vTotalBudgetCost_Job" calculation="Sum"/>
					<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "JobActEst_Purchases.jasper"]]></subreportExpression>
				</subreport>
			</band>
			<band height="19">
				<rectangle>
					<reportElement x="447" y="2" width="155" height="17" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="3993deb1-b562-437d-80cf-c08333ced554"/>
				</rectangle>
				<textField>
					<reportElement x="0" y="2" width="242" height="14" uuid="521b7217-75ea-4903-b84c-ed8703dfdcdf"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["TOTAL - SECTION (" + $F{section_nr} + "): " + $F{ordrevds_description}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="697" y="2" width="55" height="14" uuid="02dec6dd-88f4-4076-ad88-0bca19c53d63"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[new Double($V{vTotalActualCost_Section} - $V{vTotalBudgetCost_Section} )]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="392" y="2" width="55" height="14" uuid="45be34de-5f67-403b-a887-785edf9c1676"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualCost_Section}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="602" y="2" width="50" height="14" uuid="eb02fa1a-12e2-45f1-bb7d-b2dfe0816f99"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="##0.00##" isBlankWhenNull="true">
					<reportElement x="292" y="2" width="50" height="14" uuid="4f7bd9a6-4fa1-49a0-bab1-1284c3704441"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="#,##0.00 %" isBlankWhenNull="true">
					<reportElement x="652" y="2" width="45" height="14" uuid="0330ee84-d01d-45aa-ab78-6f853dbe084f"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[(($V{vTotalActualCost_Section} == null || $V{vTotalActualCost_Section}.equals(new Double(0))) &&
   ($V{vTotalBudgetCost_Section} == null ||  $V{vTotalBudgetCost_Section}.equals(new Double(0))) ? new Double(0) :
 ($V{vTotalActualCost_Section} == null || $V{vTotalActualCost_Section}.equals(new Double(0)) ? new Double(-1) :
 new java.lang.Double(
     (
        $V{vTotalActualCost_Section}.doubleValue()
        -
        $V{vTotalBudgetCost_Section}.doubleValue()
     )
     /
     $V{vTotalActualCost_Section}.doubleValue()
 )))]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="342" y="2" width="50" height="14" uuid="68b1c631-098b-4ecf-a4ae-128d90943411"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="242" y="2" width="50" height="14" uuid="6b14f7ef-7be0-4031-b5e6-9d131166ae4e"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="447" y="2" width="50" height="14" uuid="2bfca163-1da6-4e28-974c-949a82e04efb"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="497" y="2" width="50" height="14" uuid="aac89466-04b0-44fa-bbfb-ba6b475cf78a"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="547" y="2" width="55" height="14" uuid="2e5e56d9-398a-4208-9e4c-30fdc86f77fb"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetCost_Section}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<line>
					<reportElement x="0" y="17" width="752" height="1" forecolor="#000000" uuid="7ce54c6e-81d3-49d1-9f37-640be925aff8"/>
				</line>
				<line>
					<reportElement x="0" y="1" width="752" height="1" forecolor="#000000" uuid="191f0429-d3a5-4f93-af2a-cf467949225e"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="Department" minHeightToStartNewPage="60" keepTogether="true">
		<groupExpression><![CDATA[$F{job_id} + $F{section_nr} + $F{dept_code}]]></groupExpression>
		<groupHeader>
			<band height="17">
				<textField isBlankWhenNull="true">
					<reportElement x="5" y="2" width="547" height="14" uuid="ae98dff7-ce4a-412b-896f-2d29a40b6c27"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{dept_desc} == null || $F{dept_desc} == "" ? "** Department - Missing Cost Link **" : $F{dept_desc})]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement x="446" y="0" width="157" height="17" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="15f9ae48-1d5a-4a76-a035-fd0d83249e32"/>
				</rectangle>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<rectangle>
					<reportElement x="447" y="0" width="155" height="14" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="eab8e7f2-f3d2-45f7-ac01-ef26e2350a42"/>
				</rectangle>
				<textField>
					<reportElement x="697" y="0" width="55" height="14" uuid="39970586-5def-4f17-b799-e8a8df3f02b5"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalVarCost_Dept}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField>
					<reportElement style="CellBorders" x="5" y="0" width="237" height="14" uuid="e4e712ef-022d-44b3-8db3-0e9116713924"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="7" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Total - " + ($F{dept_desc} == null || $F{dept_desc} == "" ? "** Department - Missing Cost Link **" : $F{dept_desc})]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement x="392" y="0" width="55" height="14" uuid="4a8cede6-b90d-4c82-9ff5-57b8c38c3f2f"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualCost_Dept}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="602" y="0" width="45" height="14" uuid="a74e8d08-5e6a-4ba6-a809-273652c5bdc9"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalVarTime_Dept}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00##" isBlankWhenNull="true">
					<reportElement x="292" y="0" width="50" height="14" uuid="ad379fe1-e24c-447d-b26a-ebfa9fbb2f46"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualTime_Dept}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00 %" isBlankWhenNull="true">
					<reportElement x="647" y="0" width="50" height="14" forecolor="#000000" uuid="5fbe962d-f2c0-49e0-9d7d-c0386b8d840c"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[(($V{vTotalActualCost_Dept} == null || $V{vTotalActualCost_Dept}.equals(new Double(0))) &&
  ($V{vTotalBudgetCost_Dept} == null ||  $V{vTotalBudgetCost_Dept}.equals(new Double(0))) ? new Double(0) :
 ($V{vTotalActualCost_Dept} == null || $V{vTotalActualCost_Dept}.equals(new Double(0)) ? new Double(-1) :
 new java.lang.Double(
      (
       $V{vTotalActualCost_Dept}.doubleValue()
        -
        $V{vTotalBudgetCost_Dept}.doubleValue()
     )
     /
     $V{vTotalActualCost_Dept}.doubleValue()
 )))]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="342" y="0" width="50" height="14" uuid="8090ae29-50ca-427b-9f57-a1ff25a72da0"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="242" y="0" width="50" height="14" uuid="4dfe7e9a-206e-42b8-ac1f-baaa1fd0c885"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualProduced_Dept}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00##" isBlankWhenNull="true">
					<reportElement x="447" y="0" width="50" height="14" uuid="d6bf32ef-f12e-4c0a-abe4-4cde15cd13c2"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetTime_Dept}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="497" y="0" width="50" height="14" uuid="e019f02b-f166-4ffb-85f2-d8f1b1ba79e8"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="547" y="0" width="55" height="14" uuid="c4ce1247-d279-4292-9b34-07b16aa5a1d5"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetCost_Dept}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="OpCategory" minHeightToStartNewPage="45" keepTogether="true">
		<groupExpression><![CDATA[$F{job_id} + $F{section_nr} + $F{dept_code} + $F{opcat_code}]]></groupExpression>
		<groupHeader>
			<band height="18">
				<textField isBlankWhenNull="true">
					<reportElement x="10" y="2" width="542" height="14" uuid="c87dc941-eb3a-407d-9ea0-a658e3b632bb"/>
					<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
						<pen lineWidth="0.0"/>
						<topPen lineWidth="0.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="8" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[($F{opcat_desc} == null || $F{opcat_desc} == "" ? "Operation Category - Missing Cost Link" : $F{opcat_desc})]]></textFieldExpression>
				</textField>
				<rectangle>
					<reportElement x="446" y="0" width="157" height="18" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="791401ad-88c2-490b-9b92-1fb06511920a"/>
				</rectangle>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<printWhenExpression><![CDATA[new Boolean( $V{OpCategory_COUNT} > 1 )]]></printWhenExpression>
				<rectangle>
					<reportElement x="447" y="0" width="155" height="14" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="448a2b04-eb6c-4a7f-8bb4-b8e06bffd1aa"/>
				</rectangle>
				<textField>
					<reportElement x="697" y="0" width="55" height="14" uuid="b9c236b0-2569-48cf-8166-2f918e2e26f5"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalVarCost_OpCagtegory}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField>
					<reportElement x="10" y="0" width="232" height="14" uuid="c36be6f0-0424-42d7-bb9e-e4c5e54f4e29"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Verdana" size="7" isBold="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA["Total - " + ($F{opcat_desc} == null || $F{opcat_desc} == "" ? "Operation Category - Missing Cost Link" : $F{opcat_desc})]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0" isBlankWhenNull="true">
					<reportElement x="242" y="0" width="50" height="14" uuid="ecd72b60-cdfa-46e5-9728-f14cd5f7c5e8"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualProduced_OpCagtegory}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="602" y="0" width="45" height="14" uuid="6af7a2fa-5613-42a7-b1cb-5c00fd9fa4f7"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalVarTime_OpCagtegory}]]></textFieldExpression>
				</textField>
				<textField pattern="##0.00##" isBlankWhenNull="true">
					<reportElement x="447" y="0" width="50" height="14" uuid="05dfdd88-1e8d-40ea-bf46-3ffa1bc09768"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetTime_OpCagtegory}]]></textFieldExpression>
				</textField>
				<textField textAdjust="StretchHeight" pattern="" isBlankWhenNull="true">
					<reportElement x="392" y="0" width="55" height="14" uuid="47907b92-1935-499a-a31e-a2360c8c36c5"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualCost_OpCagtegory}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="##0.00##" isBlankWhenNull="true">
					<reportElement x="292" y="0" width="50" height="14" uuid="b3453ed9-fda7-40e7-988d-e2d02399aafc"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalActualTime_OpCagtegory}]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="547" y="0" width="55" height="14" uuid="42ff2622-ad6b-48fb-8bcc-3220022014ff"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{vTotalBudgetCost_OpCagtegory}]]></textFieldExpression>
					<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="497" y="0" width="50" height="14" uuid="4473e0cc-827b-4878-89b8-7cdcb9e1b903"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
						<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="##0.00" isBlankWhenNull="true">
					<reportElement x="342" y="0" width="50" height="14" uuid="194bab2d-98aa-4106-9250-bdfe364a6435"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
				</textField>
				<textField pattern="#,##0.00 %" isBlankWhenNull="true">
					<reportElement x="647" y="0" width="50" height="14" forecolor="#000000" uuid="4fa659bb-7598-4c79-a0ac-f625c6b507d5"/>
					<box>
						<pen lineWidth="0.25" lineColor="#CCCCCC"/>
						<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
						<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font fontName="Verdana" size="7"/>
						<paragraph leftIndent="2" rightIndent="2"/>
					</textElement>
					<textFieldExpression><![CDATA[(($V{vTotalActualCost_OpCagtegory} == null || $V{vTotalActualCost_OpCagtegory}.equals(new Double(0))) &&
 ( $V{vTotalBudgetCost_OpCagtegory} == null || $V{vTotalBudgetCost_OpCagtegory}.equals(new Double(0))) ? new Double(0) :
 ($V{vTotalActualCost_OpCagtegory} == null || $V{vTotalActualCost_OpCagtegory}.equals(new Double(0)) ? new Double(-1) :
 new java.lang.Double(
     (
       $V{vTotalActualCost_OpCagtegory}.doubleValue()
        -
        $V{vTotalBudgetCost_OpCagtegory}.doubleValue()
     )
     /
     $V{vTotalActualCost_OpCagtegory}.doubleValue()
 )))]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<rectangle>
				<reportElement x="447" y="0" width="155" height="24" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="121325d9-c02a-4f59-a19a-da142669f0d6"/>
			</rectangle>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="447" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="cbd9a42c-c2f2-401f-a6ef-e06e1e65d188"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Time/Mat]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="392" y="12" width="55" height="12" backcolor="#F0F0F0" uuid="de0bd867-fe28-4183-8231-31c8cbc86e77"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Cost]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="497" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="0af57c4a-0383-443a-a0f0-86c744d6f0b7"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Rate]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="242" y="0" width="205" height="12" backcolor="#F0F0F0" uuid="559b5531-ff1f-459a-83b6-35baf0caadc5"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Work Performed to Date (Actual Cost)]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="242" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="f5459c02-9364-423d-90be-cef663f52adb"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Prod.]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="292" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="2bfcfeb1-dd8c-4869-94d7-2803e412596e"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Time/Mat]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="447" y="0" width="155" height="12" backcolor="#F0F0F0" uuid="e1b5f4b2-b624-46f3-8257-f574d177e6d9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Estimate]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="547" y="12" width="55" height="12" backcolor="#F0F0F0" uuid="822ab4ec-6fbd-41ef-a677-b294b03a20e5"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Cost]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="602" y="0" width="150" height="12" backcolor="#F0F0F0" uuid="a07baaef-2851-407a-aa44-a349de1399f1"/>
				<box leftPadding="1"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Variance]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="697" y="12" width="55" height="12" backcolor="#F0F0F0" uuid="bcf5df10-f991-428d-b6cb-9bb94c3d651b"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Cost]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="342" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="d9efc656-3b5e-4130-877c-a70cb73e34f3"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Rate]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="602" y="12" width="50" height="12" backcolor="#F0F0F0" uuid="48b5d30b-7e63-46aa-977a-b0c96c1997b7"/>
				<box leftPadding="1"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[Time/Mat]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="652" y="12" width="45" height="12" backcolor="#F0F0F0" uuid="c41a8d56-d389-46c4-9a42-5fe18931c69a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="false" isItalic="false"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<text><![CDATA[%]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="0" y="12" width="242" height="12" backcolor="#F0F0F0" uuid="d516dc79-d69e-48da-b3e6-995e025e785a"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[Cost Center]]></text>
			</staticText>
			<staticText>
				<reportElement style="CellBorders" mode="Transparent" x="0" y="0" width="242" height="12" backcolor="#F0F0F0" uuid="1a16f38f-ad10-47a1-9c40-453dc899a258"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="8" isBold="true" isItalic="false"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<rectangle>
				<reportElement x="447" y="0" width="155" height="14" forecolor="#FFFFFF" backcolor="#CCCCCC" uuid="68be4327-c63b-49d9-8a56-99f38c722925"/>
			</rectangle>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="85" y="0" width="157" height="14" uuid="413ea158-ccf1-4068-8589-530f1243649d"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cc_desc}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="15" y="0" width="70" height="14" uuid="30cf17ba-13e9-4a90-9c49-8be7dc5b75f6"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cc_op_code}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="697" y="0" width="55" height="14" uuid="e91a09d1-e87e-4554-a569-4f78eadd0e2c"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new Double(  $F{total_actual_labour_cost}.doubleValue() - $F{total_budget_labour_cost}.doubleValue() )]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField isBlankWhenNull="false">
				<reportElement x="547" y="0" width="55" height="14" uuid="e38cb162-b2d9-4084-9d6c-8ab00e6cceeb"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_budget_labour_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="##0.00##" isBlankWhenNull="false">
				<reportElement x="447" y="0" width="50" height="14" uuid="13916422-73ba-4fb1-bd8b-0abca6579a51"/>
				<box>
					<pen lineWidth="0.25" lineColor="#FFFFFF"/>
					<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_budget_labour_time}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="392" y="0" width="55" height="14" uuid="782fb3a0-2ada-4e98-8cf8-5800a5c43309"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_actual_labour_cost}]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="##0.00##" isBlankWhenNull="false">
				<reportElement x="292" y="0" width="50" height="14" uuid="4a7998fd-b27a-4ceb-912a-9fdc11e204ce"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total_actual_labour_time}]]></textFieldExpression>
			</textField>
			<textField pattern="##0.00" isBlankWhenNull="false">
				<reportElement x="602" y="0" width="45" height="14" forecolor="#000000" uuid="2dfdfeb3-acf5-4105-ae2d-8ebe990a5cec"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new Double( $F{total_actual_labour_time}.doubleValue() - $F{total_budget_labour_time}.doubleValue() )]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00 %" isBlankWhenNull="false">
				<reportElement x="647" y="0" width="50" height="14" forecolor="#000000" uuid="618ad334-99c5-4450-b31b-26029dff3851"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[(($F{total_actual_labour_cost} == null || $F{total_actual_labour_cost}.equals(new Double(0))) &&
  ($F{total_budget_labour_cost} == null || $F{total_budget_labour_cost}.equals(new Double(0))) ? new Double(0) :
 ($F{total_actual_labour_cost} == null || $F{total_actual_labour_cost}.equals(new Double(0)) ? new Double(-1) :
 new java.lang.Double(
     (
        $F{total_actual_labour_cost}.doubleValue()
        -
       $F{total_budget_labour_cost}.doubleValue()
     )
     /
     $F{total_actual_labour_cost}.doubleValue()
 )))]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="497" y="0" width="50" height="14" uuid="47be2d0d-a5f9-4504-b9f7-197646de9e2a"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<leftPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="0.25" lineColor="#FFFFFF"/>
					<rightPen lineWidth="0.25" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{total_budget_labour_time} == null || $F{total_budget_labour_time}.equals(new Double(0))
    ? new Double(0)
 :
$F{cc_total_rate}
)]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="342" y="0" width="50" height="14" uuid="13a5ad69-b3e6-46d7-b926-668f6a148af7"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[new Double($F{max_actual_labour_rate})]]></textFieldExpression>
				<patternExpression><![CDATA[$P{formatCurrency}]]></patternExpression>
			</textField>
			<textField pattern="#,##0" isBlankWhenNull="true">
				<reportElement x="242" y="0" width="50" height="14" uuid="ba45d8d7-53ed-493f-89d9-9a8ac76fe8c4"/>
				<box>
					<pen lineWidth="0.25" lineColor="#CCCCCC"/>
					<topPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<leftPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<bottomPen lineWidth="0.25" lineColor="#CCCCCC"/>
					<rightPen lineWidth="0.25" lineColor="#CCCCCC"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Verdana" size="7"/>
					<paragraph leftIndent="2" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{total_actual_qty_produced} == null || $F{total_actual_qty_produced} == 0 ? null : $F{total_actual_qty_produced})]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
