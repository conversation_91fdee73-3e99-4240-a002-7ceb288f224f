/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"212B1159-F8B9-4674-A17E-C96689101AA8",variableType:4}
 */
var _iPrefValue = null;

/**
 * @type {JSRecord<db:/avanti/app_preference>}
 *
 * @properties={typeid:35,uuid:"EA4CC66F-10F0-466E-89E8-FC82E375A77A",variableType:-4}
 */
var _rPreference;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"704BC522-C846-40EF-98AD-CAA9D3F1ED1D"}
 */
var _prefValue = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"F706E5A2-11D9-4EC3-9196-2747F0EEF599"}
 */
var _sPrefValue = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"3ED93382-623C-447F-9DE2-0BA8F072F6C4",variableType:8}
 */
var _nPrefValue = null;

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"8AB1FB16-4015-4390-AA00-9E15C84B2F15"}
 */
function onAction_close(event)
{
	globals.DIALOGS.closeForm(event);

}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CA66973D-01E9-4E1F-AFEC-422FCBC7CF6C"}
 */
function onAction_edit(event)
{
	elements.btn_close.enabled=false;
	elements.btn_save.enabled=true;
	elements.btn_cancel.enabled=true;
	
	globals.nav.mode = 'edit';
	globals.avUtilities_setFormEditMode('sys_preference_dtl_edit')

	if (elements._nPrefValue.visible == true)
	{
 		elements._nPrefValue.requestFocus();
	}
	else if (elements._iPrefValue.visible == true)
	{
 		elements._iPrefValue.requestFocus();
	}
	else
	{
 		elements._sPrefValue.requestFocus();
	}

}

/** *
 * @param _firstShow
 * @param _event
 *
 * @properties={typeid:24,uuid:"F76D8EEE-1E63-4C73-9580-A37882A06628"}
 * @AllowToRunInFind
 */
function onShowForm(_firstShow, _event)
{
	_super.onShowForm(_firstShow, _event)

	if (foundset.find() || foundset.find())
	{
		foundset.pref_id = _rPreference.pref_id
		foundset.search();
	}

	globals.avUtilities_setFormEditMode('sys_preference_dtl_edit','browse');
	
	elements.btn_close.enabled=true;
	elements.btn_save.enabled=false;
	elements.btn_cancel.enabled=false;
	
	refreshUI();
	
   if (_rPreference.pref_valuelist == 'vlAccountingIntegrationsSystemPreference') {
        setAccountingIntegrationValusList();
   }

}

/**
 * setFormat
 *
 * @return {String}
 *
 * @properties={typeid:24,uuid:"5AFD1AE3-2B79-4C02-92AF-FE3EAA651DEB"}
 */
function setFormat()
{
	var _useFormat = '';

	try
	{
		if (utils.stringPatternCount(_rPreference.pref_format_string, 'global'))
		{
			_useFormat = eval(_rPreference.pref_format_string);
		}
		else
		{
			_useFormat = _rPreference.pref_format_string;
		}

	}
	catch (ex)
	{
		_useFormat = _rPreference.pref_format_string;
	}

	return _useFormat;

}

/**
 * refreshUI
 *
 * @properties={typeid:24,uuid:"********-FB9E-4EEC-925F-38F4453AD487"}
 */
function refreshUI()
{

	elements._sPrefValue.visible = false;
	elements._nPrefValue.visible = false;
	elements._iPrefValue.visible = false;

	switch (_rPreference.pref_data_type)
	{
		case 'N':

			elements._nPrefValue.visible = true;				
			_nPrefValue = utils.stringToNumber(_prefValue, globals.sDecimal_Separator);
			elements._nPrefValue.format = setFormat();			
			setValueList();

			break;

		case 'I':

			elements._iPrefValue.visible = true;

			// null was being converted to zero, it shouldnt do this. we need to distinguish between the 2
			if (_prefValue == null) {
				_iPrefValue = null;
			}
			else {
				_iPrefValue = utils.stringToNumber(_prefValue);
			}
			
			elements._iPrefValue.format = setFormat();
			setValueList();
			break;

		default:

			elements._sPrefValue.visible = true;
			_sPrefValue = _prefValue;
			setValueList();

	}

}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"2942AA25-9A35-4B3C-AFE5-E866B9CB3E33"}
 * @AllowToRunInFind
 */
function onAction_save(event)
{

	if (_nPrefValue != null || _iPrefValue != null || _sPrefValue != null)
	{
		switch (_rPreference.pref_data_type)
		{
			case 'N':
	
				_prefValue = _nPrefValue.toString();
				break;
	
			case 'I':
				_prefValue = _iPrefValue.toString();
				break;
	
			default:
	
				_prefValue = _sPrefValue;
	
		}
	}
	else
	{
		_prefValue = null;
	}

	foundset.syspref_value = _prefValue
	databaseManager.saveData(foundset);

	// GD - 2012-08-29: Need to make sure the standard ink type chosen here, is also a digital toner in ink types
	if (utils.hasRecords(sys_preference_to_app_preference)) {
		
		if (sys_preference_to_app_preference.pref_number == 16) // Standard Ink type
		{
			/*** @type {JSFoundSet<db:/avanti/in_ink_type>} */
			var fsInkType = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'in_ink_type');
	
			if (fsInkType.find() || fsInkType.find())
			{
				fsInkType.inktype_id = syspref_value;
				fsInkType.org_id = globals.org_id;
	
				fsInkType.search();
	
				if (fsInkType.getSize() > 0 && fsInkType.inktype_id == syspref_value)
				{
					fsInkType.inktype_is_toner = 1;
	
					databaseManager.saveData(fsInkType);
				}
			}
		} else if (sys_preference_to_app_preference.pref_number == 144) {
			/*** @type {JSFoundSet<db:/avanti/app_preference>} */
			var fsAppPreference = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'app_preference');
			
			var bPreferencesAnswered = false;
			if(fsAppPreference.find()) {
				fsAppPreference.pref_number = 143;
				if(fsAppPreference.search() > 0) {
					if(utils.hasRecords(fsAppPreference.app_preference_to_sys_preference) && fsAppPreference.app_preference_to_sys_preference.syspref_value) {
						bPreferencesAnswered = true;
					} 
				} 
			}
			
			if(bPreferencesAnswered && fsAppPreference.find()) {
				fsAppPreference.pref_number = 146;
				if(fsAppPreference.search() > 0) {
					if(utils.hasRecords(fsAppPreference.app_preference_to_sys_preference) && fsAppPreference.app_preference_to_sys_preference.syspref_value) {
						bPreferencesAnswered = true;
					} else {
						bPreferencesAnswered = false;
					}
				} else {
					bPreferencesAnswered = false;
				}
			}
			
			if(!bPreferencesAnswered) {
				foundset.syspref_value = '0';
				databaseManager.saveData(foundset);
			}
		}
	}
	_super.dc_save(event, event.getFormName());

	onAction_close(event);
	
	if (utils.hasRecords(sys_preference_to_app_preference)) {
		 if (sys_preference_to_app_preference.pref_number == 144) {
			 if(!bPreferencesAnswered) {
			 	globals.DIALOGS.setDialogHeight(200);
				globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),i18n.getI18NMessage('avanti.dialog.posSetup_error'), i18n.getI18NMessage('avanti.dialog.okay'));
				globals.DIALOGS.setDialogHeight(150);
			 }
		 }
	}
	globals.updateDimensionUnit(_prefValue);
}

/**
 * setValueList
 *
 * @properties={typeid:24,uuid:"D476FE62-7C6C-4FD3-9570-1845B78C3A60"}
 */
function setValueList()
{

	if (_rPreference.pref_valuelist_type != null && _rPreference.pref_valuelist.length > 0)
	{
		var _dsDataset = getValueList()

		if (elements._nPrefValue.visible == true)
		{
			application.setValueListItems('avPref_optionValuesNumber', _dsDataset)
		}
		else if (elements._iPrefValue.visible == true)
		{
			application.setValueListItems('avPref_optionValuesInteger', _dsDataset)
		}
		else
		{
			application.setValueListItems('avPref_optionValuesText', _dsDataset)
		}
	}

}

/**
 * getValueList
 *
 * @return
 * @properties={typeid:24,uuid:"E645C87B-6C91-4F0E-9DAE-FFBBD370B2DA"}
 */
function getValueList()
{
	var _valueListType = _rPreference.pref_valuelist_type;
	var _valueList = _rPreference.pref_valuelist;

	/*** @type {JSDataSet} */
	var _dsData = databaseManager.createEmptyDataSet(0, ['DisplayValue', 'RealValue'])

	var aCols = new Array();

	if (_valueList != null && _valueList.length > 0)
	{
		if (_valueListType == 'M')
		{
			//Parse the manual value List
			var aList = new Array();
			aList = _rPreference.pref_valuelist.split(",")

			//Loop Each Item
			//TODO: Add Support for i18n?
			for (var i = 0; i < aList.length; i++)
			{
				var aItem = aList[i].split('|')
				aCols = new Array();

				if (aItem.length == 1)
				{
					aCols.push(aItem[0], aItem[0])
				}
				else if (aItem.length == 2)
				{
					aCols.push(aItem[0], aItem[1])
				}
				else
				{
					aCols.push(_rPreference.app_preference_to_sys_preference.syspref_value)
				}

				_dsData.addRow(aCols)
			}

			return _dsData;
		}
		else if (_valueListType == 'S')
		{

			var _ds = application.getValueListItems(_valueList)

			return _ds
		}
		else
		{

			switch (_rPreference.pref_data_type)
			{
				case 'N':

					aCols.push(_nPrefValue, _nPrefValue)

					break;

				default:

					aCols.push(_sPrefValue, _sPrefValue)

			}

			_dsData.addRow(aCols)

			return _dsData;

		}
	}
	else
	{

		switch (_rPreference.pref_data_type)
		{
			case 'N':

				aCols.push(_nPrefValue, _nPrefValue)

				break;

			default:

				aCols.push(_sPrefValue, _sPrefValue)
		}

		_dsData.addRow(aCols)

		return _dsData;

	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"FC25055E-C1DA-45EB-8C20-604BDDE22EF5"}
 */
function onAction_cancel(event)
{
	_super.dc_cancel(event, controller.getName());
	
	onAction_close(event);
}

/**
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"F08758E8-0EDD-4913-843C-6E43DADEF3D8"}
 */
function onDataChange_intValue(oldValue, newValue, event) {
	
    if (sys_preference_to_app_preference.pref_number == scopes.avUtils.SYS_PREF.AutoPopTillNum 
            && newValue == 1) {
        scopes.avText.showWarning('setupTills');
    }
    else if (sys_preference_to_app_preference.pref_number == scopes.avUtils.SYS_PREF.UseSamePressForAllForms) {
            
        if (newValue == 1) {
            
            syspref_modified_date = new Date();
        }
        else {
            
            syspref_modified_date = null;
        }
    }
    
	if (sys_preference_to_app_preference.pref_number == scopes.avUtils.SYS_PREF.OverrideTheRegisterNumberingValues) {
	    onDataChange_registerNumberFormatting(oldValue, newValue, event);
	}

    return true;
}

/**
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @properties={typeid:24,uuid:"1EF6EB92-93E6-4CD0-AC9E-857189DE8666"}
 */
function onDataChange_registerNumberFormatting(oldValue, newValue, event) {
	if (sys_preference_to_app_preference.pref_number == scopes.avUtils.SYS_PREF.OverrideTheRegisterNumberingValues) {
		var aWebClients = plugins.UserManager.getWebClients();

		/***@type {JSRecord<db:/avanti/sys_record_lock>} */
		var rRecordLock = scopes.avDB.getRec('sys_record_lock', ['recordlock_program_name', 'recordlock_user_id', 'recordlock_record_pk'], ['onDataChange_registerNumberFormatting', globals.user_id, '5CE4F953-89D4-443B-981D-623D5B03061D']);
		var recordlock_id = null;

		if (aWebClients.length == 1) {

			if (!newValue || newValue < oldValue) {
				newValue = oldValue;
				globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
					i18n.getI18NMessage('avanti.dialog.registerNumLength'), i18n.getI18NMessage('avanti.dialog.ok'));
				onAction_cancel(event);
			} else if (newValue > oldValue) {
				var result = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'), i18n.getI18NMessage('avanti.dialog.registerNumFormatting'), i18n.getI18NMessage('avanti.dialog.ok'), i18n.getI18NMessage('avanti.dialog.cancel'));

				if (result == i18n.getI18NMessage('avanti.dialog.ok')) {
					try {
						if (!rRecordLock) {
							recordlock_id = scopes.avDB.insert('sys_record_lock', ['recordlock_program_name', 'recordlock_user_id', 'recordlock_record_pk'], ['onDataChange_registerNumberFormatting', globals.user_id, '5CE4F953-89D4-443B-981D-623D5B03061D'], 'recordlock_id');
						} else {
							recordlock_id = rRecordLock.recordlock_id;
						}

						/***@type {{sql:String, args:Array, server:String, maxRows:Number, table:String}}*/
						var oSQL = { };

						oSQL.sql = "UPDATE in_transaction_register SET itreg_number = RIGHT('000000000000000' + CAST(itreg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						oSQL.table = "in_transaction_register";
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'in_transaction_register');
						}

						oSQL.sql = "UPDATE po_purchase_register SET popreg_number = RIGHT('000000000000000' + CAST(popreg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'po_purchase_register');
						}

						oSQL.sql = "UPDATE po_receipt_register SET porreg_number = RIGHT('000000000000000' + CAST(porreg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'po_receipt_register')
						}

						oSQL.sql = "UPDATE prod_receipt_register SET prodrec_reg_number = RIGHT('000000000000000' + CAST(prodrec_reg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'prod_receipt_register');
						}

						oSQL.sql = "UPDATE prod_job_cost_register SET jc_reg_number = RIGHT('000000000000000' + CAST(jc_reg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'prod_job_cost_register');
						}

						oSQL.sql = "UPDATE prod_job_cost_adj_reg SET jcareg_number = RIGHT('000000000000000' + CAST(jcareg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'prod_job_cost_adj_reg');
						}

						oSQL.sql = "UPDATE sa_invoice_register SET invreg_number = RIGHT('000000000000000' + CAST(invreg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_invoice_register');
						}
						oSQL.sql = "UPDATE sa_cash_receipt_register SET sacr_reg_number = RIGHT('000000000000000' + CAST(sacr_reg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						oSQL.table = "sa_cash_receipt_register";
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'sa_cash_receipt_register');
						}

						oSQL.sql = "UPDATE ap_invoice_register SET apinv_reg_number = RIGHT('000000000000000' + CAST(apinv_reg_number AS VARCHAR(16)), ?) WHERE (org_id = ?)";
						oSQL.args = [newValue, globals.org_id];
						if (globals.avUtilities_sqlRaw(oSQL)) {
							plugins.rawSQL.flushAllClientsCache(globals.avBase_dbase_avanti, 'ap_invoice_register');
						}

						scopes.avDB.deleteRecs('sys_record_lock', ['recordlock_id'], [recordlock_id]);
						onAction_save(event);
					} catch (e) {
						scopes.avDB.deleteRecs('sys_record_lock', ['recordlock_id'], [recordlock_id]);
					}
				} else {
					newValue = oldValue;
					onAction_cancel(event);
				}
			}

		} else {
			newValue = oldValue;
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.logoutOtherUsers'), i18n.getI18NMessage('avanti.dialog.ok'));
			onAction_cancel(event);

		}
	}
	return true;
}

/**
 * Handle changed data, return false if the value should not be accepted. In NGClient you can return also a (i18n) string, instead of false, which will be shown as a tooltip.
 *
 * @param {String} oldValue old value
 * @param {String} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @return {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5FA78ED0-58CD-4C2F-AF50-FD64FAC0F643"}
 */
function onDataChange_prefValue(oldValue, newValue, event) {
	
    if (sys_preference_to_app_preference.pref_number == scopes.avUtils.SYS_PREF.AccountingIntegration) {
        globals.avBase_AccountingIntegrationType = newValue;
        
        if (newValue == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoQuickBooksOnline 
                || newValue == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoMicrosoftDynamics365
                || newValue == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoNetSuiteCloudAccounting
                || newValue == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoSage200) {
            createIntegrationUser();
            createSystemWorkTypes(newValue);
        }
    }
	
	return true;
}

/**
 * Create Integration User
 *
 * @properties={typeid:24,uuid:"572BFC08-10E3-43C9-B95E-BD63E4AE311D"}
 */
function createIntegrationUser() {
    
    var sEmplCode = "INTUSER";
    var rIntegrationUser = scopes.avDB.getRec("sys_employee",["empl_code", "empl_hidden"],[sEmplCode, 1]);
    
    if (rIntegrationUser) {
        return;
    }
	
	/** @type {JSFoundSet<db:/svy_framework/sec_user>} */
	var fsUser = databaseManager.getFoundSet(globals.avBase_dbase_framework, 'sec_user');
	
	/** @type {JSFoundSet<db:/avanti/sys_employee>} */
	var fsEmployee = databaseManager.getFoundSet(globals.avBase_dbase_avanti, 'sys_employee');
	
	fsUser = scopes.avDB.getFS('sec_user',['user_name', 'owner_id'],['integration', globals.owner_id], null, globals.avBase_dbase_framework);
	
	if (fsUser && fsUser.getSize() == 0) {
		var rUser = fsUser.getRecord(fsUser.newRecord(false));
		rUser.owner_id = globals.owner_id;
		rUser.user_name = "api_integration";
		rUser.user_active = 1;
		rUser.name_first_names = "Integration";
		rUser.name_last = "User";
		rUser.name_compound = "Integration User";
		rUser.bypass_ldap = 1;
		
		var rEmployee = fsEmployee.getRecord(fsEmployee.newRecord(false));
		rEmployee.user_id = rUser.user_id;
		rUser.empl_id = rEmployee.empl_id;
		
		rEmployee.empl_code = sEmplCode;
		rEmployee.empl_hidden = 1;
		rEmployee.empl_first_name = rUser.name_first_names;
		rEmployee.empl_last_name = rUser.name_last;
		rEmployee.empl_full_name = rUser.name_compound;
		rEmployee.empl_active = 1;
		
		/***@type {{aOrgID:Array, aOrgName:Array}}*/
		var oOrgs =  forms["_data_migrator_import"]._getOrgs(globals.owner_id);
		
		for (var i = 0; i < oOrgs.aOrgID.length; i++ ) {
			var rUserOrg = rUser.sec_user_to_sec_user_org.getRecord(rUser.sec_user_to_sec_user_org.newRecord(false));
			rUserOrg.organization_id = oOrgs.aOrgID[i];
		}
		
		databaseManager.saveData();
	}
}

/**
 * Create system work types for postage, freight, customer deposits and postage deposits
 *
 *@param {String} sIntegrationType
 * @properties={typeid:24,uuid:"4FCE4BE9-FC0E-40B5-ACB2-B4796C51E31D"}
 */
function createSystemWorkTypes(sIntegrationType) {
    scopes.globals.createSystemWorkType("POSTAGE","Postage");
    scopes.globals.createSystemWorkType("FREIGHT","Freight");
    
    if (sIntegrationType == scopes.avUtils.ACCOUNTING_INTEGRATION_TYPE.WorkatoQuickBooksOnline) {
      scopes.globals.createSystemWorkType("DEPOSIT_C","Customer Deposit");
      scopes.globals.createSystemWorkType("APPLY_CDA","Apply Customer Deposit");
      scopes.globals.createSystemWorkType("DEPOSIT_P","Postage Deposit");
      scopes.globals.createSystemWorkType("APPLY_PEA","Apply Postage Deposit");
    }
}

/**
 * Set the accounting integration value list
 *
 * @properties={typeid:24,uuid:"2F83BFCE-8C19-49D4-8B55-BB03AFE79847"}
 */
function setAccountingIntegrationValusList() {
    var aDisplayValues = new Array();
    var aRealValues = new Array();
    
    aRealValues.push("None");
    aDisplayValues.push("None");
    
    aRealValues.push("CSV");
    aDisplayValues.push("CSV File");
    
    aRealValues.push("Q");
    aDisplayValues.push("QuickBooks IIF");
    
    aRealValues.push("QWC");
    aDisplayValues.push("QuickBooks Web Connector");
    
    aRealValues.push("QBO");
    aDisplayValues.push("QuickBooks Online");
    
    aRealValues.push("GP");
    aDisplayValues.push("Microsoft Dynamics GP");
    
    aRealValues.push("S");
    aDisplayValues.push("Sage 50 Accounting IMP");
    
    aRealValues.push("SC");
    aDisplayValues.push("Sage 50 Accounting CSV");
    
    aRealValues.push("M");
    aDisplayValues.push("Sage 100 (MAS 90)");
    
    aRealValues.push("S2");
    aDisplayValues.push("Sage 200");
    
    aRealValues.push("P");
    aDisplayValues.push("Sage 300 (AccPacc)");
    
    aRealValues.push("K");
    aDisplayValues.push("Kramer");
    
    if (globals["isLicenseGroupActive"]('Additional - Integrated Accounting')) {
        aRealValues.push("WQBO");
        aDisplayValues.push("Integrated Accounting - QuickBooks Online");
        
        aRealValues.push("WD365");
        aDisplayValues.push("Integrated Accounting - Microsoft Dynamics 365");
        
        aRealValues.push("WNET");
        aDisplayValues.push("Integrated Accounting - NetSuite Cloud Accounting");
        
        aRealValues.push("WS200");
        aDisplayValues.push("Integrated Accounting - Sage 200");
    }
    
    application.setValueListItems("vlAccountingIntegrationsSystemPreference",aDisplayValues,aRealValues);
    
}