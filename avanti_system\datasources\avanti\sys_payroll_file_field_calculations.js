/**

 * @return {MEDIA}
 *
 * @properties={typeid:36,uuid:"B7AF0EC6-F41D-447B-B560-7F0A510A84AD"}
 */
 function clc_EnablefldFieldPayroll()
 {
 	if(prff_use_database_field_flag == 1){
 		return true;
 	}else if(prff_use_database_field_flag == 0){
 		return false;
 	}else{
 		return false;
 	}
 }
 
 /**
 * @return {MEDIA}
 *
 * @properties={typeid:36,uuid:"19F17D38-A242-4D35-9AF3-C7A59282632A"}
 */
function clc_EnableFldUserDefinedValuePayroll(){
	 if(prff_use_database_field_flag == 1){
	 	return false;
	 }else if(prff_use_database_field_flag == 0){
	 	return true;
	 }else{
	 	return false;
	 }
 }
