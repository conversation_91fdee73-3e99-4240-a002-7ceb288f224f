/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"*************-49CD-BAC7-CBC90B581EAB",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"400FD43D-6184-43DF-B990-C7416B7EA521"}
 */
function onReady() {
    _gridReady = 1;
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 * @param {Boolean} [bRefreshLight] we found missing task or task qty records and are forcing a ui refresh SL-11030 & SL-9896
 * @param {Boolean} [bBypass_avUtilities_formOnShow_logic] 
 *
 * @properties={typeid:24,uuid:"5D6A91BA-42E0-45DD-B7F9-BCD9963E6E6D"}
 * @AllowToRunInFind
 */
function onShow (firstShow, event, bRefreshLight, bBypass_avUtilities_formOnShow_logic) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	// sl-27603 - have to call _super.onShowForm for saving/reading sec_user_table_properties to work 
	_super.onShowForm(firstShow, event);
	
    scopes.avSales.bSectionTableFirstShow = false;
    
	scopes.avVL.load_vl_ordRevLineItems(globals.avSales_selectedRevisionHeaderID);
	
	// GD - May 6, 2015: SL-5027: If the TVE calculator is being used, then hide it from the section table so it is only accessible from the detail line (ColorTree spec)
	if (globals.avBase_getSystemPreference_Number(109) == 1){
		
		elements.grid.getColumn(elements.grid.getColumnIndex("calculate_icon")).visible = false;
		
	} else {
		
		elements.grid.getColumn(elements.grid.getColumnIndex("calculate_icon")).visible = true;
		
	}
	
	if(!elements.fldLineItem.enabled || !elements.fldQtySelector.enabled){
		scopes.avSection.setSectionTblQtyFields();
	}
	
	if (globals.nav_program_name == "Task_Work_Types") {
		elements.fldLineItem.visible = false;
	}
	else {
		elements.fldLineItem.visible = true;
	}
	
	var nColIndexFinishW = elements.grid.getColumnIndex('fldFinishSizeW');
    var nColIndexFinishL = elements.grid.getColumnIndex('fldFinishSizeL');
	var nColIndexBleedW = elements.grid.getColumnIndex('fldBleedWSize');
    var nColIndexBleedL = elements.grid.getColumnIndex('fldBleedLSize');
    
	if (useAlternateEnvelopeDimensionNaming()) {
	    if (nColIndexFinishW < nColIndexFinishL) {
	    	elements.grid.moveColumn('fldFinishSizeW', nColIndexFinishL);
	    	elements.grid.moveColumn('fldFinishSizeL', nColIndexFinishW);
	    }
	    if (nColIndexBleedW < nColIndexBleedL) {
	    	elements.grid.moveColumn('fldBleedWSize', nColIndexBleedL);
	    	elements.grid.moveColumn('fldBleedLSize', nColIndexBleedW);
	    }
	}
	else {		    
	    if (nColIndexFinishW > nColIndexFinishL) {
	    	elements.grid.moveColumn('fldFinishSizeW', nColIndexFinishL);
	    	elements.grid.moveColumn('fldFinishSizeL', nColIndexFinishW);
	    }
	    if (nColIndexBleedW > nColIndexBleedL) {
	    	elements.grid.moveColumn('fldBleedWSize', nColIndexBleedL);
	    	elements.grid.moveColumn('fldBleedLSize', nColIndexBleedW);
	    }
	}
	
	if ( globals.nav_program_name != "Estimate_Entry" && globals.nav_program_name != "CRM_Opportunity" ) {
		elements.fldQtySelector.enabled = false;
		elements.fldQtySelector.visible = false;
		elements.lblQtySelector.visible = false;

		//Calculator
		if ( globals.avSecurity_showCostSalesOrder == false && globals.avSecurity_showSellPrice == false ) elements.grid.getColumn(elements.grid.getColumnIndex("calculate_icon")).visible = false;

	} else {
		elements.fldQtySelector.enabled = false;
		elements.fldQtySelector.visible = true;
		elements.lblQtySelector.visible = true;
	}

	if(!bBypass_avUtilities_formOnShow_logic){
		if ( globals.avUtilities_formOnShow(controller.getName()) ) {
			setPagingVisible();
			return;
		}
	}


	if (!bRefreshLight) refreshUI();

	/// sl-3478 - something happened and foundset got set to zero even tho relation has recs - reload
	if(foundset.getSize() == 0 && 
			utils.hasRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted) && 
			forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.getSize() > 0 && 
			globals.nav_program_name != "Task_Work_Types") {
		controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);
	}
	// sl-7935 - when scrolling between estimates, with the sections tab visible, when you move from an estimate with sections to one without - its showing the sections for the one with
	else if(foundset.getSize() > 0 && 
			!utils.hasRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted) && 
			globals.nav_program_name != "Task_Work_Types") {
		controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);
	}
	else if (globals.nav_program_name == "CRM_Order_Manager_View") {
		controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);
	}
	
    if (!bRefreshLight) validateRevDVariables();  // sl-6901 - task info was disappearing after going to other propgram then coming back - could duplicate this one
	
	// sl-4902 - when adding a template line item then going to section tab - it selects the fst record - but looking at the section dialog - the model is missing unless we goto other section then back - so just 
	// call onRecordSelection() here to execute the needed code
	if (!bRefreshLight) {
		
		if(foundset && foundset.getSize() > 0 && foundset.getSelectedIndex() > 0){
			
			// GD - Apr 7, 2016: SL-8092: If we are in browse mode and show this form, then we have to initialize the oEstOrd object (updates the ui)
			if (scopes.avUtils.isNavModeReadOnly()) {
				
				scopes.avSales.oEstOrd_initialize (null, null, sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1));
			}
			
			onRecordSelection(null, null, true)
		}
		
		if(!scopes.avUtils.isNavModeReadOnly()){
			elements.grid.requestFocus(elements.grid.getColumnIndex("fldDescription"));
		}
	}
	else {
		setPagingVisible();
	}
	
	// GD - Jan 29, 2021: SL-20079 - Need to refresh the UI so that the add task row is unlocked for postage on released orders
    forms.sa_order_revision_detail_task_tbl.refreshUI();
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"944A1AE9-DEE5-4D19-8FF5-3FE0E0C622B3"}
 */
var btnCopy_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.copySection');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D7742D7D-E21A-4AE3-BB44-0D8673BA3AF2"}
 */
var btnDelete_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.DeleteSection');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"4DC2EE64-0E71-432B-9653-94A0220363A9"}
 */
var calculate_icon_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.SectionCalculator');

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"FDDE0A0B-DE33-4FEF-BC99-874C4A574856"}
 */
var fldPress_tooltip = '%%sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.sa_order_revds_press_to_sa_task.task_description%%';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"D2F54099-50CF-4521-8F89-0EBF79C08ADC"}
 */
var info_icon_tooltip = i18n.getI18NMessage('i18n:avanti.lbl.sectionDetails');

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"32D98641-AD3C-4AB9-BEB9-CF9BF54E3D5E"}
 * @AllowToRunInFind
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "info_icon" && col.styleClass.search(' disabled') == -1) {
		// If the button is disabled, do not call the method
		btnInfo(event);
	}
	
	if (col.id == "parent_icon") {
		btnToggleParent(event);
	}
	if (col.id == "fldPaging") {
		onAction_paging(event);
	}
	if (col.id == "calculate_icon") {
		btnEstimateCalculator(event);
	}
	if (col.id == "sort_icon") {
		btnSort(event);
	}
	if (col.id == "btnCopy" && col.styleClass.search(' disabled') == -1) {
		onAction_copySection(event);
	}
	if (col.id == "btnDelete" && col.styleClass.search(' disabled') == -1) {
		btnDeleteRow(event);
	}
}

/**
 * Called when the columns data is changed. *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param oldValue
 * @param newValue
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"27F66D68-EC72-4D42-ABA0-BD947241ECED"}
 */
function onColumnDataChange(foundsetindex, columnindex, oldValue, newValue, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		return;
	}
	
	/** @type {JSRecord} */
	var record = elements[event.getElementName()].myFoundset.foundset.getRecord(foundsetindex);
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "fldGripWork") {
		onDataChange_gripWork(oldValue, newValue, event);
	}
	if (col.id == "fldGroup") {
		onDataChange_quantity(oldValue, newValue, event);
	}
	if (col.id == "fldQuantity") {
		onDataChange_quantity(oldValue, newValue, event);
	}
	if (col.id == "fldFlatWork") {
		onDataChange_flatwork(oldValue, newValue, event);
	}
	if (col.id == "fldPages") {
		onDataChange_numPages(oldValue, newValue, event);
	}
	if (col.id == "fldDifficulty") {
		onDataChange_difficulty(oldValue, newValue, event);
	}
	if (col.id == "fldColors") {
		onDataChange_colors(oldValue, newValue, event);
	}
	if (col.id == "fldColourBar") {
		onDataChange_colorBar(oldValue, newValue, event);
	}
	if (col.id == "fldFinishSizeW") {
		onDataChange_trimW(oldValue, newValue, event);
	}
	if (col.id == "fldFinishSizeHc") {
		onDataChange_trimL(oldValue, newValue, event);
	}
	if (col.id == "fldFinishSizeL") {
		onDataChange_trimL(oldValue, newValue, event);
	}
	if (col.id == "fldFinishSizeWc") {
		onDataChange_trimW(oldValue, newValue, event);
	}
	if (col.id == "fldBleedHSizec") {
		onDataChange_bleed(oldValue, newValue, event);
	}
	if (col.id == "fldBleedWSize") {
		onDataChange_bleed(oldValue, newValue, event);
	}
	if (col.id == "fldBleedLSize") {
		onDataChange_bleed(oldValue, newValue, event);
	}
	if (col.id == "fldBleedWSizec") {
		onDataChange_bleed(oldValue, newValue, event);
	}
	if (col.id == "fldGrain") {
		onDataChange_grain(oldValue, newValue, event);
	}
}

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"E5F5AF75-3755-4449-84BC-8AD31530AC54"}
 */
var _selectedSectionUUID = null;

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"5582D24D-CCFD-4A0C-8DCA-8AA1EF96A6FD"}
 */
var vSelectedId = null;

/**
 * @type {Date}
 *
 * @properties={typeid:35,uuid:"97CB7C9C-0058-48D3-BA96-F91969CC0427",variableType:93}
 */
var vNextOnRecordSelection = null;

/**
 * @public 
 * 
 * @return {Boolean}
 * 
 * @properties={typeid:24,uuid:"59E5CB1B-20A7-490C-AB4D-45F4B557C074"}
 */
function useAlternateEnvelopeDimensionNaming() {
	var bUseAlternateEnvelopeDimensionNaming = false;
	
	if (globals.avBase_getSystemPreference_Number(scopes.avUtils.SYS_PREF.EnvelopeNaming) === 1) {
		var bLineItemsContainGrandFormatSection = false;
		var rLineItem = forms.sa_order_revision_detail_tbl.foundset.getSelectedRecord();
		
		if (rLineItem) {
			bLineItemsContainGrandFormatSection = scopes.avDetail.doesLineItemContainGrandFormatSection(rLineItem);
		}

		// SL-21302 - dont use envelop naming if this is a grand format section
		if (!bLineItemsContainGrandFormatSection) {
			bUseAlternateEnvelopeDimensionNaming = true;
		}
	}
	
	return bUseAlternateEnvelopeDimensionNaming;
}

/**
 * @public 
 * 
 * sets paging field visible based on isThereADigitalSection()
 * 
 * @properties={typeid:24,uuid:"2E3CA994-844C-4E5A-8A17-76A6D889AEE5"}
 */
function setPagingVisible(){
	elements.grid.getColumn(elements.grid.getColumnIndex("fldPaging")).visible = isThereADigitalSection();
}

/**
 * @return {Boolean}
 * @properties={typeid:24,uuid:"D92A7990-CB5D-420D-B328-060FDC4AC892"}
 */
function isThereADigitalSection(){
	for(var i=1; i<=foundset.getSize(); i++){
		var rSect = foundset.getRecord(i) ;
		
		if(scopes.avSection.usesDigitalPress(rSect, true)){
			return true;
		}
	}
	
	return false;
}

/**
 * Refreshes the UI
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-07-14
 *
 * @param {Boolean} [bSkipSelect] - optional to skip the record select
 *
 * @AllowToRunInFind
 *
 * @properties={typeid:24,uuid:"A23E2202-9EC8-45DD-A66B-8B2CCEE60FCE"}
 */
function refreshUI (bSkipSelect) {
	
	if ( !bSkipSelect ) bSkipSelect = false;

	// GD - 2013-01-09: Refresh the paper section details
	refreshPaperDetails();
	
   var bAllowGanging = false,
       bLineGang = false,
       bShowOtherButtons = true;
   
    if (globals.avSales_orderType == 'EST') {
        bAllowGanging = scopes.globals.avSecurity_checkForUserRight('Estimate_Entry', 'estimateGanging', globals.avBase_employeeUserID);
    }
    else {
        bAllowGanging = scopes.globals.avSecurity_checkForUserRight('Order_Entry', 'orderGanging', globals.avBase_employeeUserID);      
    }
    var rDetail = (foundset 
            && foundset.getSize() > 0 
            && utils.hasRecords(foundset.sa_order_revision_detail_section_to_sa_order_revision_detail)) ? foundset.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1) : null;
    
    if (rDetail && rDetail.ordrevd_is_gang == 1) {
        bAllowGanging =  false; // Hide buttons not allowed while ganging
        bLineGang = true;
    }
    
    var bForceEnableBtns = false;
    if (globals.nav_program_name == "Task_Work_Types") {
    	bShowOtherButtons = false;
    	bAllowGanging = false;
    	bForceEnableBtns = true;
    }
    
    if (scopes.avUtils.isNavModeReadOnly() 
            || !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName())) {
        
        bAllowGanging	= false;
        bShowOtherButtons = false;
    }
    
    elements.btnAddGang.visible = bAllowGanging;
    elements.lblGang.visible = bAllowGanging;
    if (bLineGang) {
        bShowOtherButtons = false;
    }
    
    elements.btnAddParent.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons) || bForceEnableBtns;
    elements.lblParent.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons) || bForceEnableBtns;
    elements.btnAddTemplate.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons);
    elements.lblTemplate.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons);
    elements.btnAddVersion.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons) || bForceEnableBtns;
    elements.lblVersion.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons) || bForceEnableBtns;
    elements.btnDeleteAll.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons);
    elements.lblDeleteAll.visible = (bAllowGanging && bShowOtherButtons) || (!bAllowGanging && bShowOtherButtons);
    
	var bSelected = false;
	if ( !bSkipSelect && _selectedSectionUUID && foundset && foundset.getSelectedRecord() && foundset.getSelectedRecord().ordrevds_id != _selectedSectionUUID ) {
		if ( foundset.selectRecord(_selectedSectionUUID) ) {
			bSelected = true;
		}
	}
	if ( !bSkipSelect && !bSelected && ( !_selectedSectionUUID || ( foundset && foundset.getSelectedRecord() && foundset.getSelectedRecord().ordrevds_id != _selectedSectionUUID ) ) ) {
		_selectedSectionUUID = null;
		foundset.setSelectedIndex(1);
	}

	globals.avSales_selectedRevisionSectionID = foundset.ordrevds_id;

	// GD - Nov 14, 2014: Perf Tune - trying to avoid setting forms into edit mode again when recalc
	// GD - 2014-02-28: Should only need this now to refresh
	forms._sa_order_est_base.getOrderEditStatus(bSkipSelect, null, controller.getName());

	// Refresh the parent children sections
	globals.avUtilities_tableTreeView_refresh(controller.getName(), "ordrevd_id", globals.avSales_selectedRevisionDetailID);
	
	setPagingVisible();
	
	scopes.avSection.setSectionTblQtyFields();
}

/**
 * Refreshes the paper details
 *
 * <AUTHOR> Dotzlaw
 * @since 2013-01-09
 *
 *
 *
 * @properties={typeid:24,uuid:"D08AD203-DEA0-4CE6-80B7-0C22F2A79038"}
 */
function refreshPaperDetails () {
	var rSection;
	if ( foundset && foundset.getSize() > 0 ) {
		for ( var i = 1; i <= foundset.getSize(); i++ ) {
			rSection = foundset.getRecord(i);
			_super.setPaperDetails(rSection);
		}
	}
}

/**
 * Adds a Parent Section
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-07-14
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"60BA4F45-3D4F-41D8-AEC2-662F263B7019"}
 */
function btnAddParent (event) {
	
	if ( scopes.avUtils.isNavModeReadOnly() || (!scopes.avUtils.isNavModeReadOnly() && !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()))) {
		return;
	}
	
	var iSeqNr = 0,
		rSection = null,
		/** @type {{type:Number,webNr:Number,webGroup:String}} */ 
		oObj = {},
		i = 0,
		bFound = false,
		rRec = null,
		/**@type {Array<JSRecord<db:/avanti/sa_order_revision_detail_section>>} **/
		aSections = [],
		iMax = 0;

    if (event != null) {
        scopes.avSection.showAddSectionDialog();
    }
	oObj = scopes.avSection.oSectionType;
	scopes.avSection.oSectionType = null;
	
	if (!oObj || !oObj.type || oObj.type === 0) return;

	iSeqNr = globals.avUtilities_getSequenceNr(foundset);
	
	if (oObj.type === 99 ){ // assembly
		// sl-3067 - allow more than 1 assembly - but it must have a print section to refer to
		bFound=false
		for ( i = foundset.getSize(); i >= 1; i-- ) {
			rRec = foundset.getRecord(i);
			if (rRec.ordrevds_is_assembly) {
				break; 
			}
			if ( !rRec.ordrevds_is_assembly && !rRec.ordrevds_is_generic ) {
				bFound = true;
				break;
			}
		}
		if ( bFound ){
			rSection = forms.sa_order.addParentSection(event.getFormName(), null, true, iSeqNr);
			databaseManager.saveData(rSection);
		}
		else{
			scopes.avText.showWarning('cantAddAssembly')
			return
		}
	} 
	else if (oObj.type === 100){
		rSection = forms.sa_order.addParentSection(event.getFormName(), null, null, iSeqNr, null, true);
		databaseManager.saveData(rSection);
	} 
	else {
		
		rSection = forms.sa_order.addParentSection(event.getFormName(), null, null, iSeqNr);
		aSections.push(rSection);
		databaseManager.saveData(rSection);
	}
	
	if (oObj.type === scopes.avTask.TASKTYPEID.WebPress){
		
		// Offset Web
		rSection.ordrevds_is_offsetweb = 1;
		rSection.sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdsqty_group = oObj.webGroup + ":1";
		rSection.ordrevds_over_paper_item_id = null;
		rSection.ordrevds_nr_webs = oObj.webNr;
		databaseManager.saveData(rSection);
		
		if (oObj.webNr > 1){
			
			for ( i = 1; i <= oObj.webNr - 1; i++ ) {
				iSeqNr += 1;
				rSection = forms.sa_order.addParentSection(event.getFormName(), null, null, iSeqNr);
				rSection.ordrevds_is_offsetweb = 1;
				rSection.ordrevds_nr_webs = oObj.webNr;
				rSection.sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdsqty_group = oObj.webGroup + ":" + (i + 1);
				rSection.ordrevds_over_paper_item_id = null;
				databaseManager.saveData(rSection);
				aSections.push(rSection);
			}
		}
		
	} else if (oObj.type === scopes.avTask.TASKTYPEID.FlexoPress){
		
		// Flexo
		rSection.ordrevds_is_flexo = 1;
		rSection.ordrevds_over_paper_item_id = null;
		databaseManager.saveData(rSection);
		
	} else if (oObj.type === scopes.avTask.TASKTYPEID.DigitalRollPress){
		
		// Digital roll
		rSection.ordrevds_is_digitalweb = 1;
		rSection.ordrevds_over_paper_item_id = null;
		databaseManager.saveData(rSection);
		
	} else if (oObj.type === scopes.avTask.TASKTYPEID.GrandFormatPress) {
		
		// Grand Format
		rSection.ordrevds_is_grandformat = 1;
		databaseManager.saveData(rSection);
		
	} else if (oObj.type === scopes.avTask.TASKTYPEID.DiePress) {
		
		rSection.ordrevds_is_die_press = 1;
		databaseManager.saveData(rSection);
		
	} else if (oObj.type === scopes.avTask.TASKTYPEID.StampingPress) {
		
		rSection.ordrevds_is_stamping_press = 1;
		databaseManager.saveData(rSection);
	} 

	// GD - 2012-08-01: Needed to add this because the records are not always appearing
	forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.loadAllRecords();
	controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);

	scopes.avSales.oEstOrd.refreshSections(rSection.ordrevd_id);
	
    if (globals.avSales_orderType == scopes.avUtils.DOCUMENT_TYPE.ProjectPlan 
            && forms.sa_order_project_plan_dtl.foundset.sa_order_to_sa_order_plant_rev.getSize() > 0) {
        scopes.avSection.createSectionProducingPlantRecords(rSection, null);
    }
	
	refreshUI();

	// Create color records
	iMax = aSections.length;
	for ( i = 0; i < iMax; i++ ) {
		forms._sa_order_est_base.onDataChange_colors(null, foundset.ordrevds_colours, event, aSections[i]);
	}
	
	foundset.selectRecord(rSection.ordrevds_id);

	// Make sure the task add item row and value lists are refreshed for the section (like when Generic)
	forms.sa_order_revision_detail_task_tbl.resetAddItemRowLabels();
    if (event) {
    	elements.grid.requestFocus(elements.grid.getColumnIndex("fldDescription"));
    }
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @return
 * @properties={typeid:24,uuid:"BF826E4B-7BC5-4600-AA31-5BDBB8119444"}
 */
function getNextSeqNum(rSect){
	if(utils.hasRecords(rSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted)){
		rSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.sort('sequence_nr asc');
		var mx = rSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.getSize();
		return rSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.getRecord(mx).sequence_nr+1;
	}
	else{
		return rSect.sequence_nr+1;
	}
}

/**
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} rSect
 *
 * @properties={typeid:24,uuid:"4A2BAD07-1957-4ADB-847D-4E6DF7576F10"}
 */
function pushSectionsAfterDown(rSect){
	var fs = rSect.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted;
	fs.sort('sequence_nr asc');
	var mx = fs.getSize();
	
	for(var i=1;i<=mx;i++){
		var r = fs.getRecord(i);
		
		if(r.sequence_nr >= rSect.sequence_nr && r.ordrevds_id != rSect.ordrevds_id){
			r.sequence_nr++;
		}
	}
	databaseManager.saveData();
}

/**
 * Adds a Child Section
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-07-14
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"3C9B950C-E633-432C-893B-C6BCDCDB016D"}
 */
function btnAddVersion(event) {
    if (scopes.avUtils.isNavModeReadOnly() || ( !scopes.avUtils.isNavModeReadOnly() && !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()) )) {
        return;
    }

    var rOrgSection = foundset.getSelectedRecord();

    if (scopes.avSection.getVersionNum(rOrgSection) > 1) {
        scopes.avText.showWarning('noVersionOfVersion');
        return;
    }

    if (rOrgSection 
            && !rOrgSection.ordrevds_is_gang 
            && rOrgSection.ordrevds_form_num) {
        // can only make a version of a form

        var rVersionSib = scopes.avSection.copySection(rOrgSection, null, true);
        rVersionSib.ordrevds_id_orig = null; //need to clear this for change orders

        rVersionSib.sequence_nr = getNextVersionSeqNum(rOrgSection.ordrevd_id, rOrgSection.ordrevds_form_num);
        rVersionSib.ordrevds_form_num = rOrgSection.ordrevds_form_num;
        rVersionSib.ordrevds_base_version_id = rOrgSection.ordrevds_id;

        if (rOrgSection.ordrevds_version_num) {
            rVersionSib.ordrevds_version_num = getNextVersionNum(rOrgSection.ordrevd_id, rOrgSection.ordrevds_form_num);
        }
        else {
            rOrgSection.ordrevds_version_num = 1;
            rVersionSib.ordrevds_version_num = 2;
        }

        if (utils.hasRecords(rVersionSib.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid)) {
            var rPress = rVersionSib.sa_order_revision_detail_section_to_sa_order_revds_press$is_selected_avsales_selectedrevisiondetailqtyuuid.getRecord(1);
            rPress.mpress_version_plates = rPress.mpress_plates;
        }

        var fsKids = rOrgSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.duplicateFoundSet();
        var numKids = fsKids.getSize();
        if (numKids > 0) {
            fsKids.sort('sequence_nr asc');

            for (var i = 1; i <= numKids; i++) {
                var rOldKid = fsKids.getRecord(i);
                var rNewKid = scopes.avSection.copySection(rOldKid, null, true);
                rNewKid.parent_id = rVersionSib.ordrevds_id;
                rNewKid.ordrevds_id_orig = null;
                rNewKid.sequence_nr = rVersionSib.sequence_nr + i;
            }

            pushSectionsAfterDown(rNewKid);
        }
        else {
            pushSectionsAfterDown(rVersionSib);
        }

        scopes.avSales.oEstOrd.refreshSections(rOrgSection.ordrevd_id);

        forms.sa_order_revision_detail_section.btnCalculate(null, rVersionSib);

        // GD - 2012-08-01: Needed to add this because the records are not always appearing
        controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);

        refreshUI();
        application.updateUI();

        // Select the newly added record
        if (rVersionSib) {
            foundset.selectRecord(rVersionSib.ordrevds_id);
        }

        elements.grid.requestFocus(elements.grid.getColumnIndex("fldDescription"));

        databaseManager.saveData()
        resetFormNums(rOrgSection.ordrevd_id);
    }
}

/**
 * @param {UUID} ordrevdID
 * @param {Number} formNum
 *
 * @return
 * @properties={typeid:24,uuid:"85CE178E-900D-44EF-94B4-D78E1937549F"}
 */
function getNextVersionNum(ordrevdID, formNum){
	return scopes.avDB.getMax('sa_order_revision_detail_section', ['ordrevd_id', 'ordrevds_form_num'], [ordrevdID, formNum] , 'ordrevds_version_num')+1;
}

/**
 * @param {UUID} ordrevdID
 * @param {Number} formNum
 *
 * @return
 * @properties={typeid:24,uuid:"E915E238-A0E4-4FD0-B96B-92F744AC649C"}
 */
function getNextVersionSeqNum(ordrevdID, formNum){
	return scopes.avDB.getMax('sa_order_revision_detail_section', ['ordrevd_id', 'ordrevds_form_num'], [ordrevdID, formNum] , 'sequence_nr')+1;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"9F856D69-E40A-4944-AB4D-339E521BCE80"}
 */
function btnSort (event) {
	ordrevds_is_changed = 1;
	
	// have to expand all sections with children or the shuffle doesnt update the sequence nums correctly
	scopes.avSection.expandAllSectionParents(sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1));
	
	globals.avUtilities_shuffle(event);
	
	// have to refresh sections array order in oEstOrd or resetFormNums doesnt work correctly 
	scopes.avSales.oEstOrd.refreshSections(foundset.ordrevd_id);
	
	resetFormNums(foundset.ordrevd_id);
	scopes.avSales.oEstOrd.refreshSections(foundset.ordrevd_id);
	refreshUI();
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3FFC04E5-2AAF-42E4-A66B-5CDD66A612F7"}
 */
function btnDeleteAllRows (event) {
	if ( !scopes.avUtils.isNavModeReadOnly() ) {
		if ( !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()) ) {
			return;
		}

		databaseManager.setAutoSave(true);

		if ( foundset.getSize() > 0 ) {
			var rSection = foundset.getRecord(1);
			var rDetail = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.getSelectedRecord();
			var rHeader = rDetail.sa_order_revision_detail_to_sa_order_revision_header.getSelectedRecord();

			if ( rHeader.sa_order_revision_header_to_sa_order.ordh_document_type == "ORD" && rHeader.ordrevh_revision > 0 ) {
				// This is a Change Order, set the record is_deleted flag and do not delete it from the database

				var sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
					i18n.getI18NMessage("avanti.dialog.deleteMsg"),
					i18n.getI18NMessage("avanti.dialog.ok"),
					i18n.getI18NMessage("avanti.dialog.cancel"));

				if ( sAns != i18n.getI18NMessage("avanti.dialog.ok") ) return;

				for ( var i = 1; i <= rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section.getSize(); i++ ) {
					rSection = rDetail.sa_order_revision_detail_to_sa_order_revision_detail_section.getRecord(i);
					rSection.ordrevds_is_deleted = 1;
				}

			} else {
				
				if ( !globals.avUtilities_deleteAll(event) ) return;
				
			}
			
			scopes.avSales.oEstOrd.refreshSections(rDetail.ordrevd_id);

			// Clear the detail line item
			globals.avCalcs_detail_clearTotals(rDetail);

			// Now calculate the order total
			globals.avCalcs_detail_getLineItemTotal(rHeader);

		}

		refreshUI();

		databaseManager.setAutoSave(false);
	}

}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {Boolean} [bDeleteSilently]
 *
 * @properties={typeid:24,uuid:"AADEBD80-8D44-47E9-84C4-A9A16BC8DB6A"}
 */
function btnDeleteRow (event,bDeleteSilently) {
	if ( !scopes.avUtils.isNavModeReadOnly() ) {
		if ( !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()) ) {
			return;
		}

		var rSection = foundset.getSelectedRecord(),
			rDetail = null,
			rHeader,
			i = 0,
			sMsg = "";
		
		// GD - Aug 21, 2015: Check for gang section with children; prevent user from deleting gang when children are attached
		// Also prevent the deletion of any child in the gang in this layout (needs to be done in the Gang Layout tab of the section dialog)
		if ((rSection.ordrevds_line_type === scopes.avSection.SECTION_TYPE.Gang 
			&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted))
			|| rSection.ordrevds_line_type === scopes.avSection.SECTION_TYPE.GangChild
			|| rSection.ordrevds_id_gang) {
			
				
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.disassembleGang_title"),
				i18n.getI18NMessage("avanti.dialog.disassembleGang_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"));	
				
			return;
			
		}
		
        if (rSection.ordrevds_version_num == 1 
                && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail_section$version_sibs)) {

            scopes.avText.showWarning('cantDeleteBaseVersion');
            return;

        }
        
        
        if (utils.hasRecords(rSection.sa_order_revision_detail_section_to_prod_job_cost$material)
        		|| utils.hasRecords(rSection.sa_order_revision_detail_section_to_prod_job_cost$purchases)
				|| (utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail)
					&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_prod_job)
					&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_prod_job.prod_job_to_prod_job_cost$labour))) {
        			
        			scopes.avText.showWarning('cannotdeletesection');
                    return;
        		}
		
		databaseManager.setAutoSave(true);

		
		// GD - 2014-02-23: SL-2017 - fix server error
		if ( rSection && utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail) 
				&& utils.hasRecords(rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.sa_order_revision_detail_to_sa_order_revision_header) ) {
			rDetail = rSection.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
			rHeader = rDetail.sa_order_revision_detail_to_sa_order_revision_header.getSelectedRecord();
		} else {
			return;
		}

		var bAssembly = false;
		var bDeleted = false;
		var sAns;

		// if this is a version row being deleted, and it is the last one, we need to reset the parent (not a version parent anymore)
		if ( ordrevds_is_version == 1 && parent_id ) {
			var rParent = sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent.getRecord(1);
			var nChildSize = sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children.getSize();

			if ( rHeader && rHeader.sa_order_revision_header_to_sa_order.ordh_document_type == "ORD" && rHeader.ordrevh_revision > 0 ) {
				// This is a Change Order, set the record is_deleted flag and do not delete it from the database

				sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
					i18n.getI18NMessage("avanti.dialog.deleteMsg"),
					i18n.getI18NMessage("avanti.dialog.ok"),
					i18n.getI18NMessage("avanti.dialog.cancel"));

				if ( sAns != i18n.getI18NMessage("avanti.dialog.ok") ) return;

				rSection.ordrevds_is_deleted = 1;
				databaseManager.saveData(rSection);
				bDeleted = true;
			} else {
				if ( !globals.avUtilities_delete(event) ) return;
				bDeleted = true;
			}

			if ( bDeleted ) {
				
				if ( nChildSize == 1 ) {
					rParent.ordrevds_is_version = null;
					rParent.is_parent = null;
					rParent.parent_icon = null;
					rParent.ordrevds_line_type = "S";
					rParent.ordrevds_quantity = rParent.sa_order_revision_detail_section_to_sa_order_revision_detail.ordrevd_qty_ordered;
					databaseManager.saveData(rParent);
				}
				
				scopes.avSales.oEstOrd.refreshSections(rDetail.ordrevd_id);

				// Recalculate the parent
				forms.sa_order_revision_detail_section.calculateParentTotals(rParent.ordrevds_id);
			}

		} else if (ordrevds_is_offsetweb === 1 && (is_parent || parent_id)){ // sl-4751 - added '(is_parent || parent_id)'
			
			// If this is an offset web, then all the sections need to be deleted
			
			// Warn the user
			sMsg = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteOffsetWebTemplate_title"),
				i18n.getI18NMessage("avanti.dialog.deleteOffsetWebTemplate_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"),
				i18n.getI18NMessage("avanti.dialog.cancel"));
			
			if (sMsg != i18n.getI18NMessage("avanti.dialog.ok")) return;
			
			// GD - Jan 22, 2016: Below approach is slow, and not working reliably, trying this instead
			if ( rHeader && rHeader.sa_order_revision_header_to_sa_order.ordh_document_type == "ORD" && rHeader.ordrevh_revision > 0 && ordrevds_id_orig) {
				// This is a Change Order, set the record is_deleted flag and do not delete it from the database
				
				for ( i = 1; i <= sa_order_revision_detail_section_to_sa_order_revision_detail_section$children.getSize(); i++ ) {
					
					sa_order_revision_detail_section_to_sa_order_revision_detail_section$children.ordrevds_is_deleted = 1;
				}
				ordrevds_is_deleted = 1;
				
			} else {
				if (utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revision_detail_section$children)) {
					
					sa_order_revision_detail_section_to_sa_order_revision_detail_section$children.deleteAllRecords();
				}
				foundset.deleteRecord(foundset.getSelectedIndex());
			}
			
			scopes.avSales.oEstOrd.refreshSections(rDetail.ordrevd_id);
			
		}else {
			if ( ordrevds_line_type == "A" ) bAssembly = true;

			if ( rHeader && rHeader.sa_order_revision_header_to_sa_order.ordh_document_type == "ORD" && rHeader.ordrevh_revision > 0 && ordrevds_id_orig) {
				// This is a Change Order, set the record is_deleted flag and do not delete it from the database

				sAns = globals.DIALOGS.showWarningDialog(i18n.getI18NMessage("avanti.dialog.deleteTitle"),
					i18n.getI18NMessage("avanti.dialog.deleteMsg"),
					i18n.getI18NMessage("avanti.dialog.ok"),
					i18n.getI18NMessage("avanti.dialog.cancel"));

				if ( sAns != i18n.getI18NMessage("avanti.dialog.ok") ) {
					return;
				}
				
				rSection.ordrevds_is_deleted = 1;
				databaseManager.saveData(rSection);
				bDeleted = true;
			} else {
				databaseManager.saveData(foundset);
				
				if (bDeleteSilently || scopes.avText.showYesNoQuestion('deleteMsg') == scopes.avText.yes) {
					// sl-11929 - if deleting this section have to clear any Ps that use it for prod_section
					for(var p=sa_order_revision_detail_section_to_sa_order_revds_page_set$prod_section.getSize(); p>=1; p--){
						var rPS = sa_order_revision_detail_section_to_sa_order_revds_page_set$prod_section.getRecord(p);
						rPS.ordrevdsps_prod_section = null;
					}
					
					scopes.avUtils.devLog("SL-23984", "User confirmed to delete section from the sales order #: " + rHeader.sa_order_revision_header_to_sa_order.ordh_document_num + "  - Section Description: " + rSection.ordrevds_description  + " - Section UUID: " + rSection.ordrevds_id , true);
					foundset.deleteRecord();
				} 
				else{
					return;
				}
				
				bDeleted = true;
			}
			
			scopes.avSales.oEstOrd.refreshSections(rDetail.ordrevd_id);
		}

		// Recalculate the order total
		if ( bDeleted ) {

			if ( bAssembly || foundset.getSize() == 0) {
				
				// If the deletion was an assembly task, then all the sections need to be recomputed
				scopes.avSales.reCalculateLineItem(rDetail);
				
			} else {
				
				// Just re-total the est/ord
				globals.avCalcs_detail_setTotals(rSection,true);
				globals.avCalcs_detail_getLineItemTotal(rHeader, true, null, rDetail.ordrevd_id.toString());
			}

            // GD - Jan 11, 2019: We always need to compute commissions on the detail line
			globals.avCalcs_revision_calculate(rHeader, false, true, false, rDetail.ordrevd_id.toString());

			// GD - Jan 9, 2015: Load the related records
			forms._sa_order_est_base.loadRelatedRecords(rDetail);
			scopes.avSales.totalOrderPostage(rHeader);
		}

		refreshUI();

		databaseManager.setAutoSave(false);
	}
}

/**
 * Handle record selected.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param _form
 * @param {Boolean} [bSkipCheck] - skips the check to prevent firing of onRecordSelection
 * @param {Boolean} [bSkipRefreshTaskTbl] - skips the task table refresh
 *
 * @properties={typeid:24,uuid:"69954905-1AAC-4037-8BAE-DCEF7BD206A0"}
 * @AllowToRunInFind
 */
function onRecordSelection (event, _form, bSkipCheck, bSkipRefreshTaskTbl) {
	// this record was just deleted in avCalcs_section_setDivisions - dont process onRecordSelection or we get an error
	if (ordrevds_description == "This record was deleted in avCalcs_section_setDivisions") {
		return;
	}
	
    if ( ordrevds_line_type == "N") {
        
        forms.sa_order_revision_detail_task_tbl.elements.lblGangParentShield.visible = true;
        
    } else {
        
        forms.sa_order_revision_detail_task_tbl.elements.lblGangParentShield.visible = false;
        
    }
    
    var rSec = foundset.getSelectedRecord(),
		bDisabled = false;
	if ( ( ordrevds_line_type == scopes.avSection.SECTION_TYPE.Assembly && !utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revds_plant_qty) ) 
	    || ordrevds_line_type == scopes.avSection.SECTION_TYPE.VersionChild ) {
	    bDisabled  = true;
	}
	else if (ordrevds_is_generic == 1){
		
		if (scopes.avSection.getGenericSection_supportedTaskTypesFound(rSec).length > 0) {
			
			bDisabled = false;
		} 
		else {
			bDisabled = true;;
		}
	}

	// GD - Apr 7, 2016: SL-8092: If we are in browse mode and show this form, then we have to initialize the oEstOrd object (updates the ui)
	if (globals.nav.mode == "browse" 
		&& scopes.avSales.validationManagerRunning != 1
		&& scopes.avDetail.apiCall != 1
		&& foundset.getSize() > 0) {
			
		var rDetail = sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
		
		if (utils.hasRecords(rDetail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber)) {
			scopes.avSales.oEstOrd_initialize (null, null, rDetail.sa_order_revision_detail_to_sa_order_revision_header.sa_order_revision_header_to_sa_order.sa_order_to_sa_order_revision_header$avsales_selectedrevisionheadernumber.getRecord(1));
			scopes.avSales.oEstOrd.refreshSelectedQtyIdx(true, rDetail.ordrevd_id.toString());
		}
	} 
	
	// GD - Apr 3, 2017: SL-10944: Cavanaugh Press reporting that the wrong tasks are showing up for a given section
	// These are loaded in the split panel using the relation "_to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted"
	// Adding some code to attempt a fix for the problem
	globals.avSales_selectedRevisionSectionID = ordrevds_id;
	var bGeneric = foundset.ordrevds_is_generic == 1;
	scopes.avVL.setVL_salesTasks(bGeneric)
		
	if (utils.hasRecords(_to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted) 
			&& _to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted.ordrevdstask_id == undefined) {
		
		_to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted.loadAllRecords();
		
		scopes.avSales.oEstOrd.refreshTasks(ordrevds_id);
		
		application.output("sa_order_revision_detail_section_tbl.onRecordSelection(): ERROR: Task records were missing and needed to be reloaded", LOGGINGLEVEL.ERROR);
	}
	
	// GD - Jun 6, 2017: SL-7231 - For gang sections, sometimes the task qty record (total price) for the press record is not showing up
	// This is normally loaded using the relation "sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.ordrevdstqty_t_orig"
	if (ordrevds_id) scopes.avTask.checkForMissingTaskQtyRecords (ordrevds_id.toString());

	
	//SA reducing the calls of the onRecordSelection for speed
	if (!bSkipCheck && vSelectedId && (vSelectedId == ordrevds_id) && (vNextOnRecordSelection > new Date())) {
		return;// method is already called for this record
	} 
	
	// hp - sl-9962 - changed this from 30 secs to 2. 30 secs is far too big a time - its reasonable to suppose a user might
	// recalc then recalc again within 30 secs. 2 secs should be sufficient to weed out the repeat calls to this func
//	vNextOnRecordSelection = new Date().setSeconds(new Date().getSeconds() + 2);
	vSelectedId = ordrevds_id
	
	globals.avSales_selectedRevisionSectionParentID = ordrevds_id;
	globals.avSales_paperGradeUUID = papergrade_id;
	
	if (!scopes.avDetail.apiCall) {

		elements.grid.setReadOnly(false, ["fldFlatWork"]);

		if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Section && ordrevds_uses_divisions == 1) // a parent section with child divisions
		{
			// Unlock the fields
			unlockFields(true);
			elements.grid.setReadOnly(false, ["fldQuantity"]);

		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.SectionChild) // child division of a parent
		{
			globals.avSales_selectedRevisionSectionParentID = sa_order_revision_detail_section_to_sa_order_revision_detail_section$parent.ordrevds_id;

			// Lock the fields
			unlockFields(false);

			// if its a version child we need to be able to edit qty
			if (ordrevds_version_num) {

				elements.grid.setReadOnly(false, ["fldQuantity"]);
			} else {
				elements.grid.setReadOnly(true, ["fldQuantity"]);
			}
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Version) // Parent of some version sections
		{
			// Unlock the fields
			unlockFields(true);

			// Lock the qty field
			elements.grid.setReadOnly(true, ["fldQuantity"]);
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Gang) {
			// Lock the fields
			unlockFields(false);

			elements.grid.setReadOnly(true, ["fldQuantity"]);
			elements.grid.setReadOnly(false, ["fldColourBar", "fldGripWork"]);
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.GangChild || (ordrevds_id_gang && ordrevds_line_type == scopes.avSection.SECTION_TYPE.Section)) {
			// Lock the fields
			unlockFields(false);

			elements.grid.setReadOnly(true, ["fldQuantity", "fldColourBar", "fldGripWork"]);
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.VersionChild) // Child version
		{
			// Lock the fields
			unlockFields(false);

			// UnLock the qty field
			elements.grid.setReadOnly(false, ["fldQuantity"]);
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Generic) {
			// Lock the fields
			unlockFields(false);

			elements.grid.setReadOnly(false, ["fldQuantity", "fldPages", "fldFinishSizeW", "fldFinishSizeL"]);
		} else if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Assembly) {
			// Lock the fields
			unlockFields(false);

			elements.grid.setReadOnly(false, ["fldQuantity"]);
			elements.grid.setReadOnly(true, ["fldFlatWork"]);
		} else // a section that has no children but has a press model
		{
			// Unlock the fields
			unlockFields(true);

			elements.grid.setReadOnly(false, ["fldQuantity"]);
		}

		if (ordrevds_is_flexo && ordrevds_web_number > 0 && parent_id) {

			elements.grid.getColumn(elements.grid.getColumnIndex("btnCopy")).styleClass = "material-symbols-outlined content_copy disabled";
		} else {

			elements.grid.getColumn(elements.grid.getColumnIndex("btnCopy")).styleClass = "material-symbols-outlined content_copy";
		}
	}

	// Load the same record onto the parent form
	forms.sa_order_dtl_header_sections.controller.loadRecords(foundset);

	// GD - 2014-03-05: 1460 - No tasks can be added; all from worktemplate
	// Enable/Disable tasks depending on the type of section
	if ( ( ordrevds_line_type == scopes.avSection.SECTION_TYPE.Section && ordrevds_uses_divisions == 1 ) || ordrevds_line_type == scopes.avSection.SECTION_TYPE.VersionChild  ) {
		forms.sa_order_revision_detail_task_tbl.controller.enabled = false;
		
	} 
	else {
		forms.sa_order_revision_detail_task_tbl.controller.enabled = true;
	}
	
	setSigFieldVisibility()
	
	// GD - Oct 5, 2015: skip this from firing from avCalcs_section_setDivisions
	if (!bSkipRefreshTaskTbl && ordrevds_id) {
	    
    	refreshTaskTbl(ordrevds_id.toString());
	}


	if ( event ) {
		_super.onRecordSelection(event, _form);
	} 
	
	if (!scopes.avDetail.apiCall) {
		scopes.avSection.setSectionTblQtyFields();
	}
    
    return;
}

/**
 * @properties={typeid:24,uuid:"28B8999C-35ED-4262-80AE-B5ADC3B9E8A5"}
 */
function setSigFieldVisibility(){
	forms.sa_order_revision_detail_task_tbl.elements._sig_id.visible = false
	if(ordrevds_signatures_ok && (globals.nav.mode == 'edit' || globals.nav.mode == 'add')){
		if(utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revds_signature)){
			forms.sa_order_revision_detail_task_tbl.elements._sig_id.visible = true
		}
	}
}

/**
 * Refreshes the tasks table
 * @param {String} [sSectionUUID]
 * <AUTHOR> Dotzlaw
 * @since 2013-11-27
 *
 *
 * @properties={typeid:24,uuid:"29981878-6A05-41C6-9CCD-2FD4DCB5E4D9"}
 */
function refreshTaskTbl (sSectionUUID) {
	// GD - 2013-11-27: SL-1769
	globals.avSales_selectedRevisionSectionID = (sSectionUUID) ? sSectionUUID : ordrevds_id;
	
	// Make sure we have the right records loaded
	if (utils.hasRecords(_to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted)) {
	    _to_sa_order_revds_task$avsales_selectedrevisionsectionid$is_deleted.loadAllRecords();
	}
	
	// Set some VLs for this section
	forms.sa_order.setVL_salesSectionTasks();
	forms.sa_order.setVL_salesSectionMaterials();

	// Make sure the tasks are sorted
	forms.sa_order_revision_detail_task_tbl.foundset.sort(scopes.avTask.getTaskSortString());
}

/**
 * Lock/Unlock the fields
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-07-14
 *
 * @param {Boolean} bLock - Whether or not to lock the fields
 *
 *
 * @properties={typeid:24,uuid:"B29AA812-2A2A-4979-A6B7-26A179B286E0"}
 */
function unlockFields (bLock) {
	if ( !bLock ) bLock = false;

	var aCols = ["fldBleedLSize", "fldBleedWSize", "fldColors", "fldFinishSizeL", "fldFinishSizeW", "fldPages", "fldDifficulty", "fldGroup", "fldColourBar", "fldGrain", "fldGripWork"];
	
	if (bLock) {
		elements.grid.setReadOnly(false,aCols);
	}
	else {
		elements.grid.setReadOnly(true,aCols);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"B2CC2A85-0362-4EDD-BFD2-C64396386405"}
 * @AllowToRunInFind
 */
function btnInfo (event) {
	
	var fs = foundset;
	
	// Check for parent (no press computations for a parent), none for assembly, and none for a child version
    if ( ( ordrevds_line_type == scopes.avSection.SECTION_TYPE.Assembly && !utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revds_plant_qty) ) 
            || ordrevds_line_type == scopes.avSection.SECTION_TYPE.VersionChild ) {
        return;
    }
	
	if (ordrevds_is_generic == 1){
		
		if (scopes.avSection.getGenericSection_supportedTaskTypesFound(fs.getSelectedRecord()).length > 0) {
			
			forms.sa_order_revision_detail_section_dlg.controller.loadRecords(fs);
			forms.sa_order_revision_detail_section_dlg_tab_generic_info.controller.loadRecords(fs);
			
		} else {
            // Generic section with tasks that do not support the info dialog		    
			return;
		}
	} else {

		// Normal section
		forms.sa_order_revision_detail_section_dlg.controller.loadRecords(fs);
		forms.sa_order_revision_detail_section_dlg_tab_info.controller.loadRecords(fs);
	
	}
	globals.DIALOGS.showFormInModalDialog(forms.sa_order_revision_detail_section_dlg, -1, -1, 900, 750, i18n.getI18NMessage("avanti.lbl.sectionDetails"), true, false, "dlgSectionDetails", true);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"165ECAA6-70C7-46DB-B81A-F9BF9E96504A"}
 */
function btnToggleParent (event) {
	globals.avUtilities_tableTreeView_toggleParent(event, "ordrevd_id", globals.avSales_selectedRevisionDetailID);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"38E86384-55D2-4AE6-A121-56C588DEEF7E"}
 */
function btnAdd_assemblySection (event) {
	if ( !scopes.avUtils.isNavModeReadOnly() ) {
		if ( !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()) ) {
			return;
		}

		// sl-3067 - allow more than 1 assembly - but it must have a print section to refer to
		var bFound=false
		for (var i = foundset.getSize(); i >= 1; i-- ) {
			var rRec = foundset.getRecord(i);
			if (rRec.ordrevds_is_assembly) {
				break; 
			}
			if ( !rRec.ordrevds_is_assembly && !rRec.ordrevds_is_generic ) {
				bFound = true;
				break;
			}
		}
		if ( !bFound ){
			scopes.avText.showWarning('cantAddAssembly')
			return
		}

		var iSeqNr = globals.avUtilities_getSequenceNr(foundset);

		var rAssembly = forms.sa_order.addParentSection(event.getFormName(), null, true, iSeqNr);
		databaseManager.saveData(rAssembly);

		// GD - 2012-08-01: Needed to add this because the records are not always appearing
		controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);

		refreshUI();

		// Select the newly added record
		foundset.selectRecord(rAssembly.ordrevds_id);

		unlockFields(false);

		elements.grid.requestFocus(elements.grid.getColumnIndex("fldDescription"));
	}
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"0654B5A2-EA57-42C0-9FD9-FF96AEE52B69"}
 */
function onDataChange_detailQty (oldValue, newValue, event) {

	globals.avSales_selectedRevisionDetailQtyUUID = newValue;
	
	scopes.avSales.oEstOrd.refreshSelectedQtyIdx(true, ordrevd_id.toString());

	forms._sa_order_est_base.setVL_salesSectionMaterials();

	//	sa_order_revision_detail_section_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid.loadAllRecords();
	//	databaseManager.refreshRecordFromDatabase(sa_order_revision_detail_section_to_sa_order_revds_task.sa_order_revds_task_to_sa_order_revds_task_qty$avsales_selectedrevisiondetailqtyuuid,-1);

	return true
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"245F71CB-9917-4837-9B73-DC6CFBBCBBF9"}
 */
function btnEstimateCalculator (event) {
	
	if (ordrevds_line_type == scopes.avSection.SECTION_TYPE.GangChild
	        || (ordrevds_line_type == scopes.avSection.SECTION_TYPE.Gang && !utils.hasRecords(foundset.sa_order_revision_detail_section_to_sa_order_revds_gang))) {
		
		return null;
	}

	// Should not be showing the calculator for a parent section when their are children
	// Exception may be ganging later, when we will do the reverse (show the calculator only for the parent, and not the children)
	if (is_parent == null || ordrevds_line_type == scopes.avSection.SECTION_TYPE.Gang) {

		forms.sa_order_revision_detail_section_estimate_calculator_dlg.controller.loadRecords(sa_order_revision_detail_section_to_sa_order_revds_qty$avsales_selectedrevisiondetailqtyuuid);

		return globals.DIALOGS.showFormInModalDialog(forms.sa_order_revision_detail_section_estimate_calculator_dlg, -1, -1, 785, 570, i18n.getI18NMessage("avanti.lbl.estimateCalculator"), true, false, "dlgEstimateCalculator", true);

	}
	else {
	    return null;
	}

}

/**
 * Handle hide window.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"E7E8FBC8-97A0-4756-BA2B-17A4C5C583C4"}
 */
function onHide (event) {
    
	_selectedSectionUUID = foundset.ordrevds_id;
	
	globals.avUtilities_formOnHide(controller.getName());

	return _super.onHide(event)
}

/**
 * Callback method when form is (re)loaded.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @return
 * @properties={typeid:24,uuid:"1B029507-56BE-4511-BBDC-F1CD4A5D8ED8"}
 */
function onLoad (event) {
	return _super.onLoad(event);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"C996DA27-E284-43BA-9463-43D61F6ADB8B"}
 */
function onAction_btnAddTemplate(event) {
	if ( scopes.avUtils.isNavModeReadOnly() || (!scopes.avUtils.isNavModeReadOnly() && !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()))) {
		return;
	}
	if(!scopes.avUtils.isNavModeReadOnly() ){
		globals.DIALOGS.showFormInModalDialog(forms.sa_order_revision_detail_section_add_wt, -1, -1, 825, 125, i18n.getI18NMessage("avanti.lbl.workTemplate"), true, false, "dlgAddSectionWT", true);
	}
}

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"4AE0D6D6-FD6B-4D27-A932-6B60BCE5215C"}
 */
function onDataChange_gripWork(oldValue, newValue, event) {
	var rSection = foundset.getSelectedRecord();
	rSection.ordrevds_grip_work = newValue;
	databaseManager.saveData(rSection);
	
	// If this is a version, then update the children
	if (ordrevds_is_version == 1 && is_parent == 1)	setVersion(rSection);
	
	globals.avSales_jdfFoldUUID = null;
	
	// GD - 2014-01-27: Need to flag the section as having been changed
	forms._sa_order_est_base.setSectionIsChangedFlag(rSection);

	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 * @param {JSRecord<db:/avanti/sa_order_revision_detail_section>} [rOrgSect]
 * @param {Boolean} [bNoNumOfCopiesMsg]
 * @param {Boolean} [bLineGang]
 * 
 * @return {JSRecord<db:/avanti/sa_order_revision_detail_section>} New Section (last copy)
 *
 * @public
 *
 * @properties={typeid:24,uuid:"AEB6F22F-**************-2D6EBBDFFE8E"}
 */
function onAction_copySection(event, rOrgSect, bNoNumOfCopiesMsg, bLineGang) {
    
	
	if ( scopes.avUtils.isNavModeReadOnly() || (!scopes.avUtils.isNavModeReadOnly() && !forms._sa_order_est_base.getOrderEditStatus(true, true, controller.getName()))) {
		return null;
	}
	if(foundset) {
		
		if (!rOrgSect) {
		    rOrgSect = foundset.getSelectedRecord();
		}
		
		var rDetail = rOrgSect.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1);
		
        if (rOrgSect.ordrevds_is_gang == 1 || rOrgSect.ordrevds_id_gang) {
            globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.lineGang_title"),
                i18n.getI18NMessage("avanti.dialog.lineGang_duplicateSection_msg"),
                i18n.getI18NMessage("avanti.dialog.ok"));
            return null;
        }
        
		// GD - Jun 8, 2017: Do not support copy section for gang or gang children
		if (rOrgSect.ordrevds_is_gang === 1) {
			
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.gangCopySection_title"),
				i18n.getI18NMessage("avanti.dialog.gangCopySection_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"));
			return null;
		}
		if (rOrgSect.ordrevds_id_gang) {
			
			globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.gangCopyChildSection_title"),
				i18n.getI18NMessage("avanti.dialog.gangCopyChildSection_msg"),
				i18n.getI18NMessage("avanti.dialog.ok"));
			return null;
		}
	
	}
	
	if(rOrgSect) {
		
		var nCopies = 1;
		
		if (!bNoNumOfCopiesMsg) {
		    
    		var sInput = globals.DIALOGS.showInputDialog(i18n.getI18NMessage("avanti.lbl.howManyCopies_title"),
    			i18n.getI18NMessage("avanti.lbl.howManyCopies_msg"),
    			"1");
    		if ( !sInput || sInput.length == 0 ) {
    			return null;
    		}
    		nCopies = parseInt(sInput);
    		if (nCopies > 100) {
    			nCopies = 100;
    		}
		}
		
		scopes.avSection.bCopySection = true;
		
		// dont allow copy of non-version child
		if(rOrgSect.parent_id && !rOrgSect.ordrevds_is_version){
			return null;
		}
		
		for ( var k = 1; k <= nCopies; k++ ) {
			
			var rNewSect = scopes.avSection.copySection(rOrgSect, null, null, null, bLineGang);
			
			// GD - Dec 23, 2015: Always reset these so the user has to hook them up again properly
			rNewSect.ordrevds_use_prev_sec = null;
			rNewSect.ordrevds_id_prev_section = null;
	//		var rNewSect = scopes.avSection.copySectionToComboOrder(rOrgSect, rOrgSect.sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1));
			
			// sl-5611 - have to manually copy children - dont rely on them being auto created thru web creation or modelling - doesnt carry overrides forward
			var fsKids = rOrgSect.sa_order_revision_detail_section_to_sa_order_revision_detail_section$children$is_deleted.duplicateFoundSet();
			var numKids = fsKids.getSize();
			if(numKids > 0){
				fsKids.sort('sequence_nr asc');
				
				for(var i=1;i<=numKids;i++){
					var rOldKid = fsKids.getRecord(i);
					var rNewKid = scopes.avSection.copySection(rOldKid);
					rNewKid.parent_id = rNewSect.ordrevds_id;
				}
			}
		}
			
		// Needed to add this because the records are not always appearing
		forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted.loadAllRecords();
		controller.loadRecords(forms.sa_order_revision_detail_tbl.sa_order_revision_detail_to_sa_order_revision_detail_section$is_deleted);

		scopes.avSales.oEstOrd.refreshSections(rNewSect.ordrevd_id);

		refreshUI();

		// sl-8328 - press overs key uses section form num - so we have to make sure its set for new section bef calling reCalculateLineItem() or press overs wont be copeid to new sect
		forms.sa_order_revision_detail_section.resetFormNums(rDetail.ordrevd_id);	
		
		// GD - Feb 4, 2016: Use this calc to recompute all the copies at one time
		scopes.avSales.reCalculateLineItem(rDetail, true, true, true);
		scopes.avSales.totalOrderPostage(rDetail.sa_order_revision_detail_to_sa_order_revision_header.getRecord(1));
		
		foundset.selectRecord(rNewSect.ordrevds_id);
		elements.grid.requestFocus(elements.grid.getColumnIndex("fldDescription"));
		
		scopes.avSection.bCopySection = false;
		
	}
	return rNewSect;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5786243B-970A-4505-BF78-39CA67C924E7"}
 */
function btnAddGang (event) {
	if ( scopes.avUtils.isNavModeReadOnly() || (!scopes.avUtils.isNavModeReadOnly() && !forms._sa_order_est_base.getOrderEditStatus(null, true, controller.getName()))) {
		return;
	}
	
	var rDetail = forms.sa_order_revision_detail_tbl.foundset.getSelectedRecord();
	
	if (rDetail && rDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty && rDetail.sa_order_revision_detail_to_sa_order_revision_detail_qty.getSize() > 1) {
		scopes.avText.showWarning('LineItemHasMultipleQtys');
	}
	else {
		var iSeqNr = scopes.avSection.getGangSeqNr(event.getFormName());
	    var rSection = forms.sa_order.addParentSection(event.getFormName(), null, null, iSeqNr, null, false, true);
		rSection.ordrevds_description = "Gang";
		databaseManager.saveData(rSection);
		
		scopes.avSales.oEstOrd.refreshSections(rSection.ordrevd_id);
		
		refreshUI();
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"56A29D4B-4926-479A-A12E-58B4012D35C2"}
 */
function btnCalculateUsingHeadlessClient (event) {
	
	var rDetail = sa_order_revision_detail_section_to_sa_order_revision_detail.getRecord(1),
		rOrdH = rDetail.sa_order_revision_detail_to_sa_order_revision_header.getRecord(1);
	
	// Probably need to use a global method
	scopes.globals["avUtilities_headlessClient_queueMethod"]("forms.sa_order_revision_detail_section_tbl.btnCalculateAll", [null, false, true, true, rDetail, false, false,	false, false], null, null);
		
	scopes.globals["avUtilities_headlessClient_queueMethod"]("globals.avCalcs_revision_calculate", [rOrdH, true, true, false, rDetail.ordrevd_id.toString()], null, null);
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"81A3D41F-D0BF-46F6-B44C-7E8D09ADF484"}
 */
function onAction_paging(event) {
	if(scopes.avSection.usesDigitalPress(foundset.getSelectedRecord(), true) && foundset.getSelectedRecord() && !parent_id){
		globals.DIALOGS.showFormInModalDialog(forms.sa_order_revds_page_sets, -1, -1, 1200, 750, i18n.getI18NMessage("avanti.lbl.DefinePaging"), true, false, "dlgDefinePaging",true);
		loadPageSetVL();
		loadSectionSubstratesVL();
	}
}

/**
 * @public 
 * 
 * @param {String} [sCurrentPageSet]
 * @param {Boolean} [bAll]
 * 
 * @return {String}
 *
 * @properties={typeid:24,uuid:"71DD67B3-258F-4E71-B469-1620AD647489"}
 */
function loadPageSetVL(sCurrentPageSet, bAll){
	var aReturn = [];
	var aDisplay = [];
	var aReturnAll = [];
	var aDisplayAll = [];
	var sFstPageSet = '';
	var bCurrentPageSetInVL = false;
	
	if(utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revds_page_set)){
		aReturnAll.push('All');
		aDisplayAll.push(scopes.avText.getLblMsg('all'));
		
		if(bAll){
			sFstPageSet = 'All';
		}
		
		sa_order_revision_detail_section_to_sa_order_revds_page_set.sort('sequence_nr asc');
		
		for(var i=1; i<=sa_order_revision_detail_section_to_sa_order_revds_page_set.getSize(); i++){
			var rPS = sa_order_revision_detail_section_to_sa_order_revds_page_set.getRecord(i);
			
			if(rPS.ordrevdsps_base_section){
				aDisplay.push(rPS.ordrevdsps_descr);
				aDisplayAll.push(rPS.ordrevdsps_descr);
			}
			else{
				aDisplay.push(rPS.ordrevdsps_descr + ' (' + rPS.ordrevdsps_page_nums + ')');
				aDisplayAll.push(rPS.ordrevdsps_descr + ' (' + rPS.ordrevdsps_page_nums + ')');
			}
			aReturn.push(rPS.ordrevdsps_id);
			aReturnAll.push(rPS.ordrevdsps_id);
			
			if(i == 1 && sFstPageSet == ''){
				sFstPageSet = rPS.ordrevdsps_id;
			}
			
			if(sCurrentPageSet && rPS.ordrevdsps_id == sCurrentPageSet){
				bCurrentPageSetInVL = true;
			}
		}
	}

	application.setValueListItems('vl_sectionPageSets', aDisplay, aReturn);
	application.setValueListItems('vl_sectionPageSetsPlusAll', aDisplayAll, aReturnAll);
	
	if(bCurrentPageSetInVL){
		return sCurrentPageSet;
	}
	else{
		return sFstPageSet;
	}
}

/**
 * @public 
 * 
 * @properties={typeid:24,uuid:"DF8D2AA9-5FB5-405A-9287-41C4DE21C3DC"}
 */
function loadAssemblyPageSetVL(){
	var aReturn = [];
	var aDisplay = [];

	if(ordrevds_is_assembly){
		var aSects = scopes.avSection.getAssembledSections(foundset.getSelectedRecord());

		for(var i=0; i< aSects.length; i++){
			var rSect = aSects[i];
			
			if(utils.hasRecords(rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set)){
				rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set.sort('sequence_nr asc');
				var nTotExceptionNumPages = 0;

				for(var j=1; j<=rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set.getSize(); j++){
					var rPS = rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set.getRecord(j);
					nTotExceptionNumPages += rPS.ordrevdsps_num_pages;
				}

				if(nTotExceptionNumPages < rSect.ordrevds_pages){
					var sBaseSection = scopes.avDB.getVal('sa_order_revds_page_set', ['ordrevds_id', 'ordrevdsps_base_section'], 
						[rSect.ordrevds_id, 1], 'ordrevdsps_id');
					aDisplay.push(rSect.ordrevds_description + ': ' + scopes.avText.getLblMsg('BaseSection'));
					aReturn.push(sBaseSection);
				}
				
				for(j=1; j<=rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set.getSize(); j++){
					rPS = rSect.sa_order_revision_detail_section_to_sa_order_revds_page_set.getRecord(j);
					
					if(!rPS.ordrevdsps_base_section){
						aDisplay.push(rSect.ordrevds_description + ': ' + rPS.ordrevdsps_descr + ' (' + rPS.ordrevdsps_page_nums + ')');
						aReturn.push(rPS.ordrevdsps_id);
					}
				}
			}
		}
	}
	
	application.setValueListItems('vl_sectionPageSets', aDisplay, aReturn);
}

/**
 * @properties={typeid:24,uuid:"DBA2DFBD-806F-4D2A-B582-6D304C850FA4"}
 */
function loadSectionSubstratesVL(){
	var aReturn = [];
	var aDisplay = [];
	
	if(utils.hasRecords(sa_order_revision_detail_section_to_sa_order_revds_substrate)){
		sa_order_revision_detail_section_to_sa_order_revds_substrate.sort('sa_order_revds_substrate_to_in_item.item_code asc');
		
		for(var i=1; i<=sa_order_revision_detail_section_to_sa_order_revds_substrate.getSize(); i++){
			var rSub = sa_order_revision_detail_section_to_sa_order_revds_substrate.getRecord(i);
			
			if(utils.hasRecords(rSub.sa_order_revds_substrate_to_in_item)){
				aDisplay.push(rSub.sa_order_revds_substrate_to_in_item.item_code + ': ' + rSub.sa_order_revds_substrate_to_in_item.item_desc1);
				aReturn.push(rSub.ordrevdssub_id);
			}
		}
	}

	application.setValueListItems('vl_sectionSubstrates', aDisplay, aReturn);
}

/**
 * @public 
 * @properties={typeid:24,uuid:"75D0C754-C7C3-4067-A638-C8BF6C2DD6E1"}
 */
function loadAssemblySectionSubstratesVL(){
	var aReturn = [];
	var aDisplay = [];

	if(ordrevds_is_assembly){
		var aSects = scopes.avSection.getAssembledSections(foundset.getSelectedRecord());

		for(var i=0; i< aSects.length; i++){
			var rSect = aSects[i];
			
			if(utils.hasRecords(rSect.sa_order_revision_detail_section_to_sa_order_revds_substrate)){
				rSect.sa_order_revision_detail_section_to_sa_order_revds_substrate.sort('sa_order_revds_substrate_to_in_item.item_code asc');
				
				for(var j=1; j<=rSect.sa_order_revision_detail_section_to_sa_order_revds_substrate.getSize(); j++){
					var rSub = rSect.sa_order_revision_detail_section_to_sa_order_revds_substrate.getRecord(j);
					
					if(utils.hasRecords(rSub.sa_order_revds_substrate_to_in_item)){
						aDisplay.push(rSect.ordrevds_description + ': ' + rSub.sa_order_revds_substrate_to_in_item.item_code + ': ' + rSub.sa_order_revds_substrate_to_in_item.item_desc1);
						aReturn.push(rSub.ordrevdssub_id);
					}
				}
			}
		}
	}
	
	application.setValueListItems('vl_sectionSubstrates', aDisplay, aReturn);
}

/**
 * Perform the element double-click action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 *
 * @properties={typeid:24,uuid:"2060785E-596B-4B95-8C6C-0B79CF16A569"}
 */
function onDoubleClick_qty(event) {
	if(_oCalcStopWatch && _oCalcStopWatch.total() && (_to_sec_user$user_id.user_name == 'sysadmin' || _to_sec_user$user_id.user_name == 'superuser')){
		scopes.avText.showInfo('Calculate Time: ' + _oCalcStopWatch.total() + ' seconds', true);
	}
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"9228B7D3-6144-4FAE-A7CD-6D255015717E"}
 */
function onAction_buttonTest (event) {

	if(!_oCalcStopWatch){
		_oCalcStopWatch = new scopes.avUtils.StopWatch();
	}
	_oCalcStopWatch.reset();
	_oCalcStopWatch.start();
	_oCalcStopWatch.stop()
	application.output('******* TIME: ' + _oCalcStopWatch.total());
	
	return;
}

/**
 * Handle changed data.
 *
 * @param oldValue old value
 * @param newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @public
 *
 * @properties={typeid:24,uuid:"AE63A86B-B27A-4F84-95F0-31559CC056AE"}
 */
function onDataChange_selectedLineItem(oldValue, newValue, event) {
    
	forms.sa_order_revision_detail_tbl.foundset.selectRecord(globals.avSales_selectedRevisionDetailID);
	forms.sa_order_revision_detail_tbl.onRecordSelection(event, null);
	onShow(null, event, null, true);
	forms._sa_order_est_base.setVL_salesDetailQuantities();
	
	return true
}
